package robot

import (
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

func (r *Router) pushActivityRecordCreate(ctx *abugo.AbuHttpContent) {

	errCode := 0
	type RequestData struct {
		model.XRobotPushActivityRecord
		RequestTime int64 `json:"request_time"`
	}
	var req RequestData
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errCode) {
		return
	}
	req.RecordTime = time.Unix(req.RequestTime, 0)
	req.ClickCnt = 1
	dao := server.DaoxHashGame().XRobotPushActivityRecord
	query, _ := dao.WithContext(nil).Select(dao.ALL).
		Where(dao.RecordTime.Eq(req.RecordTime)).
		Where(dao.UserID.Eq(req.UserID)).
		Where(dao.Name.Eq(req.Name)).
		Where(dao.PushMsgID.Eq(req.PushMsgID)).First()

	if query != nil && query.UserID != 0 {
		req.ClickCnt = query.ClickCnt + 1
		err = server.DaoxHashGame().XRobotPushActivityRecord.WithContext(nil).Save(&req.XRobotPushActivityRecord)
		if err != nil {
			ctx.RespErr(err, &errCode)
			return
		}
		ctx.RespOK()
	} else {
		err = server.DaoxHashGame().XRobotPushActivityRecord.WithContext(nil).Create(&req.XRobotPushActivityRecord)
		if err != nil {
			ctx.RespErr(err, &errCode)
			return
		}
		ctx.RespOK()
	}

}

func (r *Router) pushActivityConfigRecordList(ctx *abugo.AbuHttpContent) {
	errCode := 0
	type RequestData struct {
		Page     int   `json:"page"`
		PageSize int   `json:"page_size"`
		ID       int64 `json:"id"`
	}
	reqData := &RequestData{}
	type Result struct {
		model.XRobotPushMsgConfig
	}
	var results []*Result
	dao := server.DaoxHashGame().XRobotPushMsgConfig
	query := dao.WithContext(nil).Select(dao.ALL)
	if reqData.ID != 0 {
		query.Where(dao.ID.Eq(reqData.ID))
	}
	err := query.WithContext(nil).
		Select(dao.ALL).
		Order(dao.ID.Desc()).Scan(&results)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}
	ctx.RespOK()
}
