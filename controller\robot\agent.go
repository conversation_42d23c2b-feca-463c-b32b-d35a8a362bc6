package robot

import (
	"github.com/spf13/cast"
	"strings"
	"xserver/server"
)

func GetAgentURLByBotUserRegister(userID int64) string {
	sql := "select accountType, regURL  from x_user where UserID = ?"
	pResult, err := server.Db().Query(sql, []interface{}{userID})
	if err != nil {
		return ""
	}
	shardURL := ""
	num := 0
	gameURL := ""
	if pResult != nil {
		num = cast.ToInt((*pResult)[0]["accountType"])
	}
	if num == 5 {
		gameURL = cast.ToString((*pResult)[0]["regURL"])
		gameURL = "https://" + gameURL
		sql = "SELECT * FROM  x_robot_config WHERE game_url=? and robot_type=1 AND is_enable=1 ORDER BY id DESC LIMIT 1 "
		pResult, err = server.Db().Query(sql, []interface{}{gameURL})
		if err != nil || *pResult == nil {
			return ""
		}

		name := strings.ReplaceAll(cast.ToString((*pResult)[0]["name"]), "@", "")
		if name != "" {
			shardURL = "https://t.me/share/url?url=t.me/" + name + "?start="
		}
		// https://t.me/share/url?url=t.me/bot_20250526_bot?start=787437
	}
	return shardURL
}
