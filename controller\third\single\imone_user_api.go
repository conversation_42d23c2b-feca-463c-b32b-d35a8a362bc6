package single

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/beego/beego/logs"
)

// RegisterPlayerRequest 注册玩家请求结构
type RegisterPlayerRequest struct {
	MerchantCode string `json:"MerchantCode"` // 营运商唯一代码
	PlayerId     string `json:"PlayerId"`     // 玩家账号
	Currency     string `json:"Currency"`     // 货币代码
	Password     string `json:"Password"`     // 密码
	Custom05     string `json:"Custom05"`     // 自定义字段（仅适用于PlayTech）
}

// RegisterPlayerResponse 注册玩家响应结构
type RegisterPlayerResponse struct {
	Code     string `json:"Code"`
	Message  string `json:"Message"`
	Currency string `json:"Currency"`
}

// CheckExistsRequest 检测玩家是否存在请求结构
type CheckExistsRequest struct {
	MerchantCode string `json:"MerchantCode"` // 营运商唯一代码
	PlayerId     string `json:"PlayerId"`     // 玩家账号
}

// CheckExistsResponse 检测玩家是否存在响应结构
type CheckExistsResponse struct {
	Code     string `json:"Code"`
	Message  string `json:"Message"`
	Currency string `json:"Currency"`
}

// RegisterPlayer 创建新玩家 - API: Player/Register
func (l *IMOneSingleService) RegisterPlayer(userId int, currency string) (*RegisterPlayerResponse, error) {
	playerId := l.getPlayerIdFromUserId(userId)
	password := l.getUserPassword(userId)

	request := RegisterPlayerRequest{
		MerchantCode: l.merchantCode,
		PlayerId:     playerId,
		Currency:     currency,
		Password:     password,
		Custom05:     "", // 仅适用于PlayTech，我们留空
	}

	if l.Debug {
		logs.Info("IMOne RegisterPlayer 请求: userId=%d, playerId=%s, currency=%s", userId, playerId, currency)
	}

	// 发送请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化注册请求失败: %v", err)
	}

	response, err := l.sendRequestWithResponse("Player/Register", jsonData)
	if err != nil {
		return nil, err
	}

	var result RegisterPlayerResponse
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("解析注册响应失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne RegisterPlayer 响应: userId=%d, code=%s, message=%s", userId, result.Code, result.Message)
	}

	return &result, nil
}

// CheckPlayerExists 检测玩家是否存在 - API: Player/CheckExists
func (l *IMOneSingleService) CheckPlayerExists(userId int) (*CheckExistsResponse, error) {
	playerId := l.getPlayerIdFromUserId(userId)

	request := CheckExistsRequest{
		MerchantCode: l.merchantCode,
		PlayerId:     playerId,
	}

	if l.Debug {
		logs.Info("IMOne CheckPlayerExists 请求: userId=%d, playerId=%s", userId, playerId)
	}

	// 发送请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化检测请求失败: %v", err)
	}

	response, err := l.sendRequestWithResponse("Player/CheckExists", jsonData)
	if err != nil {
		return nil, err
	}

	var result CheckExistsResponse
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("解析检测响应失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne CheckPlayerExists 响应: userId=%d, code=%s, message=%s", userId, result.Code, result.Message)
	}

	return &result, nil
}

// UserExists 检查用户是否存在的便利方法
func (l *IMOneSingleService) UserExists(userId int) (bool, error) {
	response, err := l.CheckPlayerExists(userId)
	if err != nil {
		return false, err
	}

	// 根据IMOne API文档：
	// Code = "0" 且 Message = "Successful." 表示玩家存在
	// Code = "503" 且 Message = "Player already exists." 表示玩家存在
	// Code = "504" 且 Message = "Player does not exist." 表示玩家不存在
	if response.Code == IMOne_Code_Success || response.Code == IMOne_Code_Player_Already_Exists {
		return true, nil
	}

	if response.Code == IMOne_Code_Player_Not_Exists {
		return false, nil
	}

	// 其他错误码
	return false, fmt.Errorf("检测用户存在失败: %s - %s", response.Code, response.Message)
}

// GetOrCreatePlayer 获取或创建玩家的便利方法，参考OFA的GetOrCreateUserGameURL逻辑
func (l *IMOneSingleService) GetOrCreatePlayer(userId int, currency string) error {
	// 步骤1: 检查用户是否存在
	userExists, err := l.UserExists(userId)
	if err != nil {
		return fmt.Errorf("检查用户是否存在失败: %v", err)
	}

	// 步骤2: 如果用户不存在，则创建用户
	if !userExists {
		if l.Debug {
			logs.Info("IMOne 用户 %d 不存在，将创建新用户", userId)
		}

		response, err := l.RegisterPlayer(userId, currency)
		if err != nil {
			// 如果创建用户时返回"Player already exists"错误，则表示用户已存在
			// 这可能是因为在检查用户是否存在和创建用户之间，用户被其他进程创建了
			if strings.Contains(err.Error(), "Player already exists") ||
				(response != nil && response.Code == IMOne_Code_Player_Already_Exists) {
				if l.Debug {
					logs.Info("IMOne 用户 %d 已存在", userId)
				}
				return nil
			}
			return fmt.Errorf("创建用户失败: %v", err)
		}

		// 检查创建是否成功
		if response.Code != IMOne_Code_Success && response.Code != IMOne_Code_Player_Already_Exists {
			return fmt.Errorf("创建用户失败: %s - %s", response.Code, response.Message)
		}

		if l.Debug {
			logs.Info("IMOne 成功创建用户 %d", userId)
		}
	} else if l.Debug {
		logs.Info("IMOne 用户 %d 已存在", userId)
	}

	return nil
}

// sendRequestWithResponse 发送请求并返回原始响应数据，用于解析特定的响应结构
func (l *IMOneSingleService) sendRequestWithResponse(endpoint string, jsonData []byte) ([]byte, error) {
	if l.Debug {
		logs.Info("IMOne API请求: %s, 数据: %s", endpoint, string(jsonData))
	}

	// 构造完整URL
	url := fmt.Sprintf("%s/%s", l.apiUrl, endpoint)

	// 发送HTTP POST请求
	response, err := l.sendHTTPRequest(url, jsonData)
	if err != nil {
		return nil, err
	}

	if l.Debug {
		logs.Info("IMOne API响应: %s", string(response))
	}

	return response, nil
}
