package base

// ThirdOrderModel 第三方游戏订单模型
// 扩展支持混合投注系统
type ThirdOrderModel struct {
	Id             int64   `json:"Id" gorm:"column:Id"`
	SellerId       int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId      int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId   int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId         int     `json:"UserId" gorm:"column:UserId"`
	Brand          string  `json:"Brand" gorm:"column:Brand"`
	ThirdId        string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId         string  `json:"GameId" gorm:"column:GameId"`
	GameName       string  `json:"GameName" gorm:"column:GameName"`
	BetAmount      float64 `json:"BetAmount" gorm:"column:BetAmount"`           // 总下注金额
	BonusBetAmount float64 `json:"BonusBetAmount" gorm:"column:BonusBetAmount"` // Bonus币下注金额
	BetType        int     `json:"BetType" gorm:"column:BetType"`               // 下注类型：0-真金，1-Bonus币，2-混合
	WinAmount      float64 `json:"WinAmount" gorm:"column:WinAmount"`           // 总赢得金额
	RealWinAmount  float64 `json:"RealWinAmount" gorm:"column:RealWinAmount"`   // 真金赢得金额
	BonusWinAmount float64 `json:"BonusWinAmount" gorm:"column:BonusWinAmount"` // Bonus币赢得金额
	ValidBet       float64 `json:"ValidBet" gorm:"column:ValidBet"`             // 有效投注额
	ThirdTime      string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency       string  `json:"Currency" gorm:"column:Currency"`
	RawData        string  `json:"RawData" gorm:"column:RawData"`
	State          int     `json:"State" gorm:"column:State"`
	Fee            float64 `json:"Fee" gorm:"column:Fee"`
	DataState      int     `json:"DataState" gorm:"column:DataState"`
	CreateTime     string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup        string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId           string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId     int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent   int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx         string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst        string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType     int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

// GameInfo 游戏信息
type GameInfo struct {
	GameId   string
	GameName string
	Currency string
}

// 投注类型常量
const (
	BetTypeReal  = 0 // 真金投注
	BetTypeBonus = 1 // Bonus币投注
	BetTypeMixed = 2 // 混合投注
)

// 余额类型常量
const (
	AmountTypeReal  = 1 // 真金
	AmountTypeBonus = 2 // Bonus币
)
