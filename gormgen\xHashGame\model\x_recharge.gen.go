// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRecharge = "x_recharge"

// XRecharge mapped from table <x_recharge>
type XRecharge struct {
	ID           int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID       int32     `gorm:"column:UserId;comment:玩家" json:"UserId"`                                           // 玩家
	SellerID     int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                      // 运营商
	ChannelID    int32     `gorm:"column:ChannelId;comment:渠道商" json:"ChannelId"`                                    // 渠道商
	OrderID      string    `gorm:"column:OrderId;comment:hbc订单id" json:"OrderId"`                                    // hbc订单id
	OrderType    int32     `gorm:"column:OrderType;not null;default:21;comment:充值订单分类 21充值 22上庄充值" json:"OrderType"` // 充值订单分类 21充值 22上庄充值
	Symbol       string    `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                           // 币种
	Amount       float64   `gorm:"column:Amount;comment:金额" json:"Amount"`                                           // 金额
	Net          string    `gorm:"column:Net" json:"Net"`
	FromAddress  string    `gorm:"column:FromAddress;comment:打款地址" json:"FromAddress"`                              // 打款地址
	ToAddress    string    `gorm:"column:ToAddress;comment:收款地址" json:"ToAddress"`                                  // 收款地址
	TxID         string    `gorm:"column:TxId;comment:交易哈希" json:"TxId"`                                            // 交易哈希
	RealAmount   float64   `gorm:"column:RealAmount;comment:到账金额" json:"RealAmount"`                                // 到账金额
	TransferRate float64   `gorm:"column:TransferRate;comment:汇率" json:"TransferRate"`                              // 汇率
	CreateTime   time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:到账时间" json:"CreateTime"`      // 到账时间
	Memo         string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                              // 备注
	TopAgentID   int32     `gorm:"column:TopAgentId;comment:顶级id" json:"TopAgentId"`                                // 顶级id
	State        int32     `gorm:"column:State;default:1;comment:状态 1未找到玩家 2小于最低充值额  3待支付 4已过期 5充值成功" json:"State"` // 状态 1未找到玩家 2小于最低充值额  3待支付 4已过期 5充值成功
	NetFee       float64   `gorm:"column:NetFee;default:0.000000" json:"NetFee"`
	CSGroup      string    `gorm:"column:CSGroup" json:"CSGroup"`
	CSID         string    `gorm:"column:CSId" json:"CSId"`
	AdJustState  int32     `gorm:"column:AdJustState;default:1" json:"AdJustState"`
	SpecialAgent int32     `gorm:"column:SpecialAgent" json:"SpecialAgent"`
	PayType      int32     `gorm:"column:PayType;default:1;comment:支付类型 1 链上充值 2.pix" json:"PayType"` // 支付类型 1 链上充值 2.pix
	PayID        int32     `gorm:"column:PayId" json:"PayId"`
	PayData      string    `gorm:"column:PayData" json:"PayData"`
	PayTime      time.Time `gorm:"column:PayTime;comment:支付回调时间" json:"PayTime"` // 支付回调时间
	IsFirst      int32     `gorm:"column:IsFirst;default:2" json:"IsFirst"`
	ThirdID      string    `gorm:"column:ThirdId" json:"ThirdId"`
	DataState    int32     `gorm:"column:DataState;default:1" json:"DataState"`
	IsTgCounted  int32     `gorm:"column:IsTgCounted;comment:是否tg统计(0未统计 1已统计)" json:"IsTgCounted"`       // 是否tg统计(0未统计 1已统计)
	MaidianState int32     `gorm:"column:MaidianState;not null;comment:1-已經上報 0-未上報" json:"MaidianState"` // 1-已經上報 0-未上報
}

// TableName XRecharge's table name
func (*XRecharge) TableName() string {
	return TableNameXRecharge
}
