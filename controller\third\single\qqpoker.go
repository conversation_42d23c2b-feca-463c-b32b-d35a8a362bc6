package single

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/active"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"

	daogormclause "gorm.io/gorm/clause"
)

const (
	cacheKeyQQPoker = "cacheKeyQQPoker:"
)

// QQPoker API 错误代码
const (
	// 成功
	QQPoker_CodeSuccess = 200

	// 业务错误 (401xx)
	QQPoker_CodeInsufficientBalance = 40101 // 余额不足
	QQPoker_CodeInvalidCurrency     = 40103 // 币种不匹配

	// 参数错误 (402xx)
	QQPoker_CodeInvalidGameCode       = 40202 // 无效的游戏代码
	QQPoker_CodeInvalidCurrencyFormat = 40203 // 无效的币种
	QQPoker_CodeInvalidPlayerId       = 40204 // 无效的玩家ID
	QQPoker_CodeInvalidPlatform       = 40205 // 无效的平台

	// 状态错误 (403xx)
	QQPoker_CodeVendorDisabled   = 40301 // 供应商已禁用
	QQPoker_CodeGameDisabled     = 40302 // 游戏已禁用
	QQPoker_CodePlayerDisabled   = 40303 // 玩家已禁用
	QQPoker_CodePlatformDisabled = 40304 // 平台已禁用

	// 系统错误 (500xx)
	QQPoker_CodeUnknownError = 50001 // 未知错误
)

// QQPokerService QQPoker服务
type QQPokerService struct {
	apiUrl                string
	currency              string
	merchantId            string // 商户号
	appIdKey              string // APPID KEY
	secretIv              string // Secret (IV)
	gameCode              string // 游戏代码
	brandName             string
	RefreshUserAmountFunc func(int) error
}

func NewQQPokerService(temp map[string]string, fc func(int) error) *QQPokerService {
	return &QQPokerService{
		apiUrl:                temp["url"],
		merchantId:            temp["merchantId"],
		appIdKey:              temp["appIdKey"],
		secretIv:              temp["secretIv"],
		currency:              temp["currency"],
		brandName:             "qqpoker",
		RefreshUserAmountFunc: fc,
	}
}

// 加密请求参数
func (s *QQPokerService) encryptRequest(params map[string]interface{}) (string, error) {
	// 1. 排序参数
	var keys []string
	for k := range params {
		if k != "sign" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 2. 构建待签名字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%v", k, params[k]))
	}
	signStr := strings.Join(parts, "&")

	// 3. MD5加密
	h := md5.New()
	h.Write([]byte(signStr))
	//sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	// 4. AES-256-CBC加密
	block, err := aes.NewCipher([]byte(s.appIdKey))
	if err != nil {
		return "", err
	}

	// 使用PKCS7填充
	blockSize := block.BlockSize()
	padding := blockSize - len(signStr)%blockSize
	padtext := make([]byte, len(signStr)+padding)
	copy(padtext, signStr)
	for i := len(signStr); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	// 加密
	encrypted := make([]byte, len(padtext))
	iv := []byte(s.secretIv)
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(encrypted, padtext)

	// Base64编码
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// 构建响应
func (s *QQPokerService) buildResponse(code int, message string, data interface{}) map[string]interface{} {
	// 标准错误码映射
	errorMessages := map[int]string{
		QQPoker_CodeInsufficientBalance:   "insufficient balance",
		QQPoker_CodeInvalidCurrency:       "invalid currency",
		QQPoker_CodeInvalidGameCode:       "invalid game code",
		QQPoker_CodeInvalidCurrencyFormat: "invalid currency",
		QQPoker_CodeInvalidPlayerId:       "invalid player id",
		QQPoker_CodeInvalidPlatform:       "invalid platform",
		QQPoker_CodeVendorDisabled:        "vendor disabled",
		QQPoker_CodeGameDisabled:          "game disabled",
		QQPoker_CodePlayerDisabled:        "player disabled",
		QQPoker_CodePlatformDisabled:      "platform disabled",
		QQPoker_CodeUnknownError:          "unknown error",
	}

	// 如果没有提供message，使用标准错误信息
	if message == "" {
		if msg, exists := errorMessages[code]; exists {
			message = msg
		}
	}

	response := map[string]interface{}{
		"status_code": code,
		"message":     message,
	}

	if data != nil {
		response["data"] = data
	}

	return response
}

// CheckZeroAmountChanges  判断用户是否存在账变金额小于1且Reason不在特定范围内的记录 账变小于1 代表用户输光一次
func (b *QQPokerService) CheckZeroAmountChanges(userId int) (bool, error) {
	var count int64

	// 定义排除的 账变类型Reason数组
	excludedReasons := []int{12, 13, 14, 15, 105, 107, 126, 127, 128, 129, 130, 131, 2512, 2513, 3045, 3046, 3903, 3904}

	// 查询账变金额小于1且Reason不在特定范围内的记录
	query := server.Db().GormDao().Table("x_amount_change_log").
		Where("UserId = ? AND ABS(AfterAmount) < 1", userId)

	// 添加NOT IN条件
	query = query.Where("Reason NOT IN (?)", excludedReasons)

	err := query.Count(&count).Error

	if err != nil {
		logs.Error(b.brandName+"  查询小额账变记录失败 userId=", userId, " err=", err.Error())
		return false, err
	}

	return count > 0, nil
}

// CheckAdminDepositChanges  判断管理员是否手动增资、玩家是否领取了返佣 领取了 返佣可以进德州
//func (b *QQPokerService) CheckAdminDepositChanges(userId int) (bool, error) {
//	var count int64
//
//	// 定义包含的 账变类型Reason数组
//	includeReasons := []int{4, 5} //管理员增值、返佣
//	query := server.Db().GormDao().Table("x_amount_change_log").
//		Where("UserId = ?", userId)
//	query = query.Where("Reason IN (?)", includeReasons)
//
//	err := query.Count(&count).Error
//
//	if err != nil {
//		logs.Error(b.brandName+"  查询小额账变记录失败 userId=", userId, " err=", err.Error())
//		return false, err
//	}
//
//	return count > 0, nil
//}

// CheckAdminDepositOrCommission 判断玩家是否曾经人工充值或领取佣金
func (c *QQPokerService) CheckAdminDepositOrCommission(userId int) (bool, error) {
	db := server.Db().GormDao()

	// 检查人工充值记录
	var manualCount int64
	err := db.Table("x_user_recharge_withard").
		Where("UserId = ? AND ManRechargeCount > 0", userId).
		Count(&manualCount).Error

	if err != nil {
		logs.Error("查询用户人工充值记录失败 userId=", userId, " err=", err.Error())
		return false, err
	}

	// 如果有人工充值记录，直接返回true
	if manualCount > 0 {
		return true, nil
	}

	// 检查佣金记录
	var commissionCount int64
	err = db.Table("x_user_reward_commission").
		Where("UserId = ? AND GetCommissionCount > 0", userId).
		Count(&commissionCount).Error

	if err != nil {
		logs.Error("查询用户佣金记录失败 userId=", userId, " err=", err.Error())
		return false, err
	}

	return commissionCount > 0, nil
}

// CheckExperienceBonus 检查用户是否领取了体验金，以及充值是否达到体验金的20倍
// 返回值: (是否可以进入德州, 错误信息)
func (c *QQPokerService) CheckExperienceBonus(userId int) (bool, string, error) {
	db := server.Db().GormDao()

	// 1. 获取TRX汇率
	var trxRate float64 = 0.2752 // 默认值
	var config struct {
		ConfigValue string `gorm:"column:ConfigValue"`
	}

	err := db.Table("x_config").
		Select("ConfigValue").
		Where("ConfigName = ? AND IsShow = ?", "TrxPrice", 1).
		First(&config).Error

	if err != nil {
		logs.Error("查询用户trx汇率失败 userId=", userId, " err=", err.Error())
		return false, "系统错误，请稍后再试", err
	}
	if config.ConfigValue == "" {
		logs.Error("查询用户trx汇率失败 x_config的TrxPrice配置为空 userId=", userId, " config.ConfigValue=", config.ConfigValue)
		return false, "系统错误，请稍后再试", err
	}

	if err == nil && config.ConfigValue != "" {
		if rate, err := strconv.ParseFloat(config.ConfigValue, 64); err == nil {
			logs.Info("体验金的20倍，成功查询到trx汇率 userId=", userId, " trxRate=", trxRate)
			trxRate = rate
		}
	}

	// 2. 获取用户领取的体验金总额
	type TiYanJingSum struct {
		TotalAmount float64 `gorm:"column:TotalAmount"`
	}

	var tiYanJingSum TiYanJingSum
	err = db.Table("x_tiyanjing").
		Select("SUM(CASE WHEN Symbol = 'trx' THEN Amount * ? ELSE Amount END) as TotalAmount", trxRate).
		Where("UserId = ? AND Symbol IN ('trx', 'usdt') AND State IN(5,7)", userId).
		Scan(&tiYanJingSum).Error
	if err != nil {
		logs.Error("查询用户体验金总额失败 userId=", userId, " err=", err.Error())
		return false, "系统错误，请稍后再试", err
	}

	// 如果没有领取体验金，可以直接进入
	if tiYanJingSum.TotalAmount <= 0 {
		logs.Info("用户没有领取体验金，可以直接进入德州扑克 userId=", userId, " trxRate=", trxRate)
		return true, "", nil
	}

	// 3. 获取用户充值总额
	type RechargeSum struct {
		TotalRecharge float64 `gorm:"column:TotalRecharge"`
	}

	var rechargeSum RechargeSum
	err = db.Table("x_recharge").
		Select("SUM(RealAmount) as TotalRecharge").
		Where("UserId = ? AND State = 5", userId).
		Scan(&rechargeSum).Error

	if err != nil {
		logs.Error("查询用户充值总额失败 userId=", userId, " err=", err.Error())
		return false, "系统错误，请稍后再试", err
	}

	// 4. 判断充值总额是否达到体验金的20倍
	requiredRecharge := tiYanJingSum.TotalAmount * 20
	if rechargeSum.TotalRecharge < requiredRecharge {
		needRecharge := requiredRecharge - rechargeSum.TotalRecharge
		//对不起，您已参与活动，流水不达标不能进入德州，还需要打##20.00
		//对不起，您已领取体验金，流水不达标不能进入德州，还需要充值##100
		//对不起，您已领取体验金，需要充值%.2f才能进入德州
		msg := fmt.Sprintf("对不起，您已领取体验金，需要充值体验金的20倍，才能进入德州，还需要充值{n}##%.2f", needRecharge)
		return false, msg, nil
	}

	return true, "", nil
}

// CheckBonusWithdrawRequirements 检查用户是否领取了彩金，以及提现流水是否达标
// 返回值: (是否可以进入德州, 错误信息)
func (c *QQPokerService) CheckBonusWithdrawRequirements(userId int) (bool, string, error) {
	db := server.Db().GormDao()

	// 1. 检查提现流水是否达标
	type UserLiuSui struct {
		TotalLiuSui  float64 `gorm:"column:TotalLiuSui"`
		CaijinLiuSui float64 `gorm:"column:CaijinLiuSui"`
	}

	var userLiuSui UserLiuSui
	err := db.Table("x_user").
		Select("TotalLiuSui, CaijinLiuSui").
		Where("UserId = ?", userId).
		First(&userLiuSui).Error

	if err != nil {
		logs.Error("查询用户流水信息失败 userId=", userId, " err=", err.Error())
		return false, "系统错误，请稍后再试", err
	}

	needLiuSui := 0.00
	pass := true
	msgstr := ""
	// 如果提现流水未达标
	if userLiuSui.TotalLiuSui < userLiuSui.CaijinLiuSui {
		pass = false
		needLiuSui = userLiuSui.CaijinLiuSui - userLiuSui.TotalLiuSui
		msgstr = "对不起，您的流水不达标不能进入德州，还需要打"
		logs.Error("提现流水不达标 userId=", userId, " needLiuSui=", needLiuSui, " totalLiuSui=", userLiuSui.TotalLiuSui, " withdrawLiuSui=", userLiuSui.CaijinLiuSui)
	}

	// 定义需要检查的活动类型列表
	activityTypes := []int32{
		utils.FirstDepositGift,      // 首充活动
		utils.MultipleDepositGift,   // 复充活动
		utils.MagicFirstDepositGift, // magic首充活动
		utils.X9FirstDepositGift,    // X9首充活动
		utils.X9SecondDepositGift,   // X9第二次充值活动
		utils.X9ThirdDepositGift,    // X9第三次充值活动
		utils.X9FourthDepositGift,   // X9第四次充值活动
		utils.X9FifthDepositGift,    // X9第五次充值活动
	}

	// 活动名称映射，用于日志记录
	activityNames := map[int32]string{
		utils.FirstDepositGift:      "首充活动",
		utils.MultipleDepositGift:   "复充活动",
		utils.MagicFirstDepositGift: "magic首充活动",
		utils.X9FirstDepositGift:    "X9首充活动",
		utils.X9SecondDepositGift:   "X9第二次充值活动",
		utils.X9ThirdDepositGift:    "X9第三次充值活动",
		utils.X9FourthDepositGift:   "X9第四次充值活动",
		utils.X9FifthDepositGift:    "X9第五次充值活动",
	}

	// 循环检查所有活动类型
	for _, activityType := range activityTypes {
		isPass, _, amount := active.CheckActiveWithdrawable(int32(userId), activityType)
		if !isPass {
			pass = false
			amountFloat, _ := amount.Float64()
			if amountFloat > needLiuSui {
				needLiuSui = amountFloat
			}
			msgstr = "对不起，您已参与活动，流水不达标不能进入德州，还需要打"

			// 获取活动名称，如果找不到则使用活动ID
			activityName, exists := activityNames[activityType]
			if !exists {
				activityName = fmt.Sprintf("活动ID_%d", activityType)
			}

			logs.Error("%s流水不达标 userId=%d activityType=%d amount=%v isPass=%v",
				activityName, userId, activityType, amount, isPass)
		}
	}
	// 检查用户是否满足兑换码活动流水要求
	//isPass, _, giftAmount := active.IsWidthdrawableActive(int(userId), utils.RedeemCodeGift)
	//if !isPass {
	//	pass = false
	//	amount, _ := giftAmount.Float64()
	//	if amount > needLiuSui {
	//		needLiuSui = amount
	//	}
	//	logs.Error("兑换码活动流水不达标 userId=", userId, " amount=", giftAmount, " isPass=", isPass)
	//}

	// 判断是否通过
	if !pass {
		//if needLiuSui > 0 {
		//对不起，您已参与活动，流水不达标不能进入德州，还需要打##20.00
		//对不起，您已参与活动，还需要打%.2f流水才能进入德州
		msg := fmt.Sprintf(msgstr+"{n}##%.2f", needLiuSui)
		logs.Error("流水不达标 userId=", userId, " needLiuSui=", needLiuSui, " msg=", msg)
		return false, msg, nil
		//}
	}

	return true, "", nil
}

// Login QQ德州登录
func (s *QQPokerService) Login(ctx *abugo.AbuHttpContent) {
	type loginRequest struct {
		GameId     string `json:"GameId" validate:"required"` // 游戏code
		LangCode   string `json:"LangCode"`                   // 语言 en
		HomeUrl    string `json:"HomeUrl"`                    // 返回商户地址
		UserImgUrl string `json:"UserImgUrl"`                 // 头像路径
		DeviceType string `json:"DeviceType"`                 // 设备类型 mobile
	}

	errcode := 0
	reqdata := loginRequest{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("qqpoker 解析数据失败 ", " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyQQPoker, token.UserId); err != nil {
		logs.Error("qqpoker 登录失败 userId=", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(token.UserId, s.brandName, reqdata.GameId); err != nil {
		logs.Error("qqpoker 登录游戏 权限检查错误 userId=", token.UserId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("qqpoker 登录游戏 权限被拒绝 userId=", token.UserId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 检查1：体验金充值倍数限制
	canEnter, errMsg, err := s.CheckExperienceBonus(token.UserId)
	if err != nil {
		logs.Error("qqpoker 登录失败 检查体验金充值倍数失败 userId=", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if !canEnter {
		logs.Error("qqpoker 登录失败 体验金充值倍数不足 userId=", token.UserId, " msg=", errMsg)
		ctx.RespErrString(true, &errcode, errMsg)
		return
	}

	// 检查2：彩金流水限制
	canEnter, errMsg, err = s.CheckBonusWithdrawRequirements(token.UserId)
	if err != nil {
		logs.Error("qqpoker 登录失败 检查彩金流水限制失败 userId=", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if !canEnter {
		logs.Error("qqpoker 登录失败 彩金流水限制不满足 userId=", token.UserId, " msg=", errMsg)
		ctx.RespErrString(true, &errcode, errMsg)
		return
	}

	// 获取用户信息
	//user, err := server.DaoxHashGame().XUser.WithContext(context.TODO()).
	//	Where(server.DaoxHashGame().XUser.UserID.Eq(int32(token.UserId))).
	//	First()
	//if err != nil {
	//	logs.Error("获取用户信息失败:", err)
	//	ctx.RespErrString(true, &errcode, err.Error())
	//}

	// 获取客户端IP
	ip := ctx.GetIp()

	// 设置默认语言
	if reqdata.LangCode == "" {
		reqdata.LangCode = "zh-CN"
	}

	type LoginParam struct {
		Account    string `json:"account"`
		IP         string `json:"ip,omitempty"`
		Country    string `json:"country,omitempty"`
		KindID     int    `json:"kindId"`
		Avatar     string `json:"avatar,omitempty"`
		JumpType   int    `json:"jumpType,omitempty"`
		BackURL    string `json:"BackUrl,omitempty"`
		Currency   string `json:"currency,omitempty"`
		GameLang   string `json:"gameLang,omitempty"`
		TableID    string `json:"tableId,omitempty"`
		TableMapID string `json:"tableMapId,omitempty"`
		GameMapID  string `json:"gameMapId,omitempty"`
	}

	kindID_ := reqdata.GameId
	kindID, _ := strconv.Atoi(kindID_)

	reqParams := &LoginParam{
		Account:  "" + fmt.Sprintf("%d", token.UserId),
		IP:       ip,
		KindID:   kindID,
		Currency: s.currency,
		GameLang: reqdata.LangCode,
		Avatar:   reqdata.UserImgUrl,
	}

	// 发送登录请求
	resp, err := s.makeRequest("/v1/seamless/login", reqParams)
	if err != nil {
		logs.Error("qqpoker 登录请求失败 ", " err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 解析响应
	if resp["status_code"] != nil && resp["status_code"].(float64) != 200 {
		logs.Error("qqpoker 登录失败 ", " err=", resp["message"])
		ctx.RespErrString(true, &errcode, resp["message"].(string))
		return
	}

	// 返回游戏URL
	if resp["data"] != nil {
		data := resp["data"].(map[string]interface{})
		if data["url"] != "" {
			ctx.Put("url", data["url"])
		}
	}
	//sessionId
	base.UserId2token(cacheKeyQQPoker, token.UserId)
	ctx.RespOK()
}

// Balance 查询余额
func (s *QQPokerService) Balance(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("qqpoker 获取原始数据失败 ", " err=", err)
		ctx.RespErr(err, nil)
		return
	}
	//logs.Info("QQPoker Balance原始请求数据:", string(bodyBytes))

	// 2. 解析请求
	var req struct {
		Account string `json:"account"`
		Sign    string `json:"sign"`
		Time    int64  `json:"time"`
	}
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Error("QQPoker Balance 解析请求数据失败 ", " err=", err)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidPlayerId, "invalid request data", nil))
		return
	}

	// 3. 验证签名
	reqData := map[string]interface{}{
		"account": req.Account,
		"sign":    req.Sign,
		"time":    req.Time,
	}
	if !s.verifySign(reqData, req.Sign) {
		logs.Error("QQPoker Balance 签名验证失败")
		ctx.RespJson(s.buildResponse(QQPoker_CodeVendorDisabled, "invalid sign", nil))
		return
	}

	userId_ := req.Account
	userId, _ := strconv.Atoi(userId_)
	_, balance, err := base.GetUserBalance(userId, cacheKeyQQPoker)
	if err != nil {
		ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to get balance", nil))
		logs.Error("QQPoker Balance 查询余额失败,发生错误,userId=", userId, "err=", err.Error())
		return
	}

	// 4. 根据全局配置“TexasCarryInDays”限制可带入金额为近N天累计充值金额
	// 配置路径：系统管理-系统设置-修改配置；固定全局（SellerId=0, ChannelId=0）
	carryDays := int(server.GetConfigInt(1, 0, "TexasCarryInDays"))
	logs.Info("QQPoker Balance TexasCarryInDays=", carryDays)
	allowedAmount := balance
	if carryDays > 0 {
		// 计算起止时间：自然日，起点为今天00:00往前 (carryDays-1) 天
		now := time.Now()
		startOfToday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		startTime := startOfToday.AddDate(0, 0, -(carryDays - 1))
		startStr := startTime.Format("2006-01-02 15:04:05")
		endStr := now.Format("2006-01-02 15:04:05")

		var sum struct{ A float64 }
		errSum := server.Db().GormDao().Table("x_recharge").
			Select("COALESCE(SUM(RealAmount),0) as A").
			Where("UserId = ? AND State = 5 AND CreateTime BETWEEN ? AND ?", userId, startStr, endStr).
			Scan(&sum).Error
		if errSum != nil {
			logs.Error("QQPoker Balance 统计近N天充值失败 userId=", userId, " days=", carryDays, " err=", errSum.Error())
		} else {
			// 允许带入 = min(钱包余额, 近N天累计充值)
			if sum.A < allowedAmount {
				allowedAmount = sum.A
			}
		}
	}

	// 5. 返回成功响应（保留两位小数）
	balance2 := float64(int(allowedAmount*100)) / 100
	resp := map[string]interface{}{
		"account":  req.Account,
		"currency": s.currency,
		"balance":  balance2,
	}
	ctx.RespJson(s.buildResponse(QQPoker_CodeSuccess, "success", resp))
	logs.Info("QQPoker Balance 查询余额成功,userId=", userId, "balance=", balance2)
}

// Debit 扣除金额（下注）
func (s *QQPokerService) Debit(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("qqpoker 获取原始数据失败 ", " err=", err)
		ctx.RespErr(err, nil)
		return
	}
	logs.Info("QQPoker Debit原始请求数据:", string(bodyBytes))

	// 2. 解析请求
	var req struct {
		Account     string  `json:"account"`
		EtransGroup string  `json:"etransgroup"`
		EtransId    string  `json:"etransid"`
		Currency    string  `json:"currency"`
		Money       float64 `json:"money"`
		OpCode      string  `json:"opcode"`
		Sign        string  `json:"sign"`
		Time        int64   `json:"time"`
	}
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Error("QQPoker Debit 解析请求数据失败 ", " err=", err)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidPlayerId, "invalid request data", nil))
		return
	}

	// 3. 验证签名
	reqData := map[string]interface{}{
		"account":     req.Account,
		"etransgroup": req.EtransGroup,
		"etransid":    req.EtransId,
		"currency":    req.Currency,
		"money":       req.Money,
		"opcode":      req.OpCode,
		"sign":        req.Sign,
		"time":        req.Time,
	}
	if !s.verifySign(reqData, req.Sign) {
		logs.Error("QQPoker Debit 签名验证失败")
		ctx.RespJson(s.buildResponse(QQPoker_CodeVendorDisabled, "invalid sign", nil))
		return
	}

	// 4. 验证币种
	if req.Currency != s.currency {
		logs.Error("QQPoker Debit 币种不匹配 ", req.Currency)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidCurrency, "invalid currency", nil))
		return
	}

	//判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, req.EtransId, ctx.Gin().Request.URL.String())
	if err != nil {
		logs.Error("QQPoker Debit 检测是否重复请求 发生错误 transactionId=", req.EtransId, " err=", err)
		ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "system error", nil))
		return
	}
	if duplicate {
		logs.Error("QQPoker Debit 检测到重复请求 transactionId=", req.EtransId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "system error", nil))
		}
		return
	}

	userId_ := req.Account
	userId, _ := strconv.Atoi(userId_)
	amount := math.Abs(req.Money) //三方返回的的Amount是负数，需要取绝对值变正数，以扣款
	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGame(userId, utils.GameTypeTexas, s.brandName); err != nil {
		logs.Error("QQPoker Debit 权限检查错误 userId=", userId, " gameId=qqpoker err=", err.Error())
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidPlayerId, "invalid player", nil))
		return
	} else if !allowed {
		logs.Error("QQPoker Debit 权限被拒绝 userId=", userId, " gameId=qqpoker hint=", hint)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidPlayerId, "invalid player", nil))
		return
	}

	// 检查用户余额是否被冻结
	//errcode := 0
	//if frozen, message := common.CheckUserFrozen(userId); err != nil {
	//	logs.Error("gfg_single 下注 系统错误 userId=", userId, " err=", err.Error())
	//	ctx.RespErrString(true, &errcode, err.Error())
	//	return
	//} else if frozen {
	//	logs.Error("gfg_single 下注失败 玩家账号余额被冻结 userId=", userId, " message=", message)
	//	ctx.RespErrString(true, &errcode, message)
	//	return
	//}

	// 开启事务处理
	var response interface{}
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 6.1 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
			Select("UserId,SellerId,ChannelId,Amount,Token").
			Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("QQPoker Debit 下注失败 会员不存在,thirdId=", req.EtransId, "userId=", userId)
			}
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to get balance", nil))
			logs.Error("QQPoker Debit 下注失败 查询用余额,发生错误,thirdId=", req.EtransId, "userId=", userId, "err=", e.Error())
			return e
		}

		balance := userBalance.Amount
		// 6.2 检查余额
		if balance < 0 || balance < amount {
			ctx.RespJson(s.buildResponse(QQPoker_CodeInsufficientBalance, "insufficient balance", nil))
			logs.Error("QQPoker Debit 下注失败 用户余额不足,thirdId=", req.EtransId, "userId=", userId, "betAmount=", req.Money, "balance=", balance)
			return errors.New("insufficient balance")
		}

		// 6.4 更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, amount).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount-?", amount),
			})
		if resultTmp.Error != nil {
			logs.Error("QQPoker Debit 更新用户余额失败 userId=", userId, " thirdId=", req.EtransId, " e=", resultTmp.Error)
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to update balance", nil))
			return resultTmp.Error
		}
		if resultTmp.RowsAffected <= 0 {
			d := errors.New("更新条数0")
			logs.Error("QQPoker Debit 更新用户余额失败，更新条数0 userId=", userId, " thirdId=", req.EtransId)
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to update balance", nil))
			return d
		}

		// 6.6 创建账变记录
		afterBalance := balance - amount
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       0 - amount,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceCReasonQQPokerBet,
			Memo:         fmt.Sprintf(" bet,etransId:%s", req.EtransId),
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("QQPoker Debit下注 创建账变失败 userId=", userId, " thirdId=", req.EtransId, " e=", e.Error())
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to create amount log", nil))
			return e
		}

		// 6.7 返回成功响应
		resp := map[string]interface{}{
			"account":  req.Account,
			"money":    req.Money,
			"currency": req.Currency,
			"balance":  afterBalance,
		}
		response = s.buildResponse(QQPoker_CodeSuccess, "success", resp)
		ctx.RespJson(response)
		logs.Info("QQPoker Debit下注成功 thirdId=", req.EtransId)
		return nil
	})

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if err != nil {
			respCode = 1
		}
		base.AddRequestDB(req.EtransId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	// 发送余额变动通知
	if err == nil && s.RefreshUserAmountFunc != nil {
		go func() {
			if err := s.RefreshUserAmountFunc(userId); err != nil {
				logs.Error("QQPoker Debit 刷新用户余额失败 userId=", userId, " err=", err)
			}
		}()
	}
}

// Credit 增加金额（派奖）
func (s *QQPokerService) Credit(ctx *abugo.AbuHttpContent) {
	// 1. 获取原始请求数据
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("qqpoker 获取原始数据失败 ", " err=", err)
		ctx.RespErr(err, nil)
		return
	}
	logs.Info("QQPoker Credit原始请求数据:", string(bodyBytes))

	// 2. 解析请求
	var req struct {
		Account     string  `json:"account"`
		EtransGroup string  `json:"etransgroup"`
		EtransId    string  `json:"etransid"`
		Currency    string  `json:"currency"`
		Money       float64 `json:"money"`
		OpCode      string  `json:"opcode"`
		Sign        string  `json:"sign"`
		Time        int64   `json:"time"`
	}
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		logs.Error("QQPoker Credit 解析请求数据失败 ", " err=", err)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidPlayerId, "invalid request data", nil))
		return
	}

	// 3. 验证签名
	reqData := map[string]interface{}{
		"account":     req.Account,
		"etransgroup": req.EtransGroup,
		"etransid":    req.EtransId,
		"currency":    req.Currency,
		"money":       req.Money,
		"opcode":      req.OpCode,
		"sign":        req.Sign,
		"time":        req.Time,
	}
	if !s.verifySign(reqData, req.Sign) {
		logs.Error("QQPoker Credit 签名验证失败")
		ctx.RespJson(s.buildResponse(QQPoker_CodeVendorDisabled, "invalid sign", nil))
		return
	}

	// 4. 验证币种
	if req.Currency != s.currency {
		logs.Error("QQPoker Credit 币种不匹配 ", req.Currency)
		ctx.RespJson(s.buildResponse(QQPoker_CodeInvalidCurrency, "invalid currency", nil))
		return
	}

	//判断是否是重复请求数据
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, req.EtransId, ctx.Gin().Request.URL.String())
	if err != nil {
		logs.Error("QQPoker Credit 检测是否重复请求 发生错误 transactionId=", req.EtransId, " err=", err)
		ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "system error", nil))
		return
	}
	if duplicate {
		logs.Error("QQPoker Credit 检测到重复请求 transactionId=", req.EtransId)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "system error", nil))
		}
		return
	}

	userId_ := req.Account
	userId, _ := strconv.Atoi(userId_)

	// 开启事务处理
	var response interface{}
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 6.1 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
			Select("UserId,SellerId,ChannelId,Amount,Token").
			Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("QQPoker Credit 派奖失败 会员不存在,thirdId=", req.EtransId, "userId=", userId)
			}
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to get balance", nil))
			logs.Error("QQPoker Credit 派奖失败 查询用余额,发生错误,thirdId=", req.EtransId, "userId=", userId, "err=", e.Error())
			return e
		}

		balance := userBalance.Amount

		// 6.3 更新用户余额
		resultTmp := tx.Table("x_user").Where("UserId=?", userId).
			Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount+?", req.Money),
			})
		if resultTmp.Error != nil {
			logs.Error("QQPoker Credit 更新用户余额失败 userId=", userId, " thirdId=", req.EtransId, " e=", resultTmp.Error)
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to update balance", nil))
			return resultTmp.Error
		}
		if resultTmp.RowsAffected <= 0 {
			d := errors.New("更新条数0")
			logs.Error("QQPoker Credit 更新用户余额失败，更新条数0 userId=", userId, " thirdId=", req.EtransId)
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to update balance", nil))
			return d
		}

		// 6.5 创建账变记录
		afterBalance := balance + req.Money
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       req.Money,
			AfterAmount:  afterBalance,
			Reason:       utils.BalanceCReasonQQPokerWin,
			Memo:         fmt.Sprintf(" settle,etransId:%s", req.EtransId),
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("QQPoker Credit 创建账变失败 userId=", userId, " thirdId=", req.EtransId, " e=", e.Error())
			ctx.RespJson(s.buildResponse(QQPoker_CodeUnknownError, "failed to create amount log", nil))
			return e
		}

		// 6.6 返回成功响应
		resp := map[string]interface{}{
			"account":  req.Account,
			"money":    req.Money,
			"currency": req.Currency,
			"balance":  afterBalance,
		}
		response = s.buildResponse(QQPoker_CodeSuccess, "success", resp)
		ctx.RespJson(response)
		logs.Info("QQPoker Credit 派奖成功 thirdId=", req.EtransId)
		return nil
	})

	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if err != nil {
			respCode = 1
		}
		base.AddRequestDB(req.EtransId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	// 发送余额变动通知
	if err == nil && s.RefreshUserAmountFunc != nil {
		go func() {
			if err := s.RefreshUserAmountFunc(userId); err != nil {
				logs.Error("QQPoker Credit 刷新用户余额失败 userId=", userId, " err=", err)
			}
		}()
	}
}

// verifySign 验证签名
func (s *QQPokerService) verifySign(data map[string]interface{}, sign string) bool {
	//logs.Info("QQPoker verifySign 开始验证签名, 原始数据:", data, "待验证签名:", sign)

	// 1. 过滤掉值为null的键值对
	filtered := make(map[string]interface{})
	for k, v := range data {
		if v != nil {
			filtered[k] = v
		}
	}
	//logs.Info("QQPoker verifySign 过滤null后的数据:", filtered)

	// 2. 移除sign字段
	delete(filtered, "sign")
	//logs.Info("QQPoker verifySign 移除sign后的数据:", filtered)

	// 3. 按键名字母顺序排序
	keys := make([]string, 0, len(filtered))
	for k := range filtered {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	//logs.Info("QQPoker verifySign 排序后的keys:", keys)

	// 4. 生成查询字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%v", k, filtered[k]))
	}
	signStr := strings.Join(parts, "&")
	//logs.Info("QQPoker verifySign 生成的签名字符串:", signStr)

	// 5. 计算MD5签名并转换为大写
	h := md5.New()
	h.Write([]byte(signStr))
	calculatedSign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	//logs.Info("QQPoker verifySign 计算的签名:", calculatedSign, "原始签名:", sign)

	// 6. 比较签名 (不区分大小写)
	result := strings.EqualFold(calculatedSign, sign)
	//logs.Info("QQPoker verifySign 签名验证结果:", result)
	return result
}

// generateSign 生成签名
func (s *QQPokerService) generateSign(params map[string]interface{}) string {
	// 1. 过滤掉值为null的键值对
	filtered := make(map[string]interface{})
	for k, v := range params {
		if v != nil {
			filtered[k] = v
		}
	}

	// 2. 移除sign字段
	delete(filtered, "sign")

	// 3. 按键名字母顺序排序
	keys := make([]string, 0, len(filtered))
	for k := range filtered {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 4. 生成查询字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%v", k, filtered[k]))
	}
	signStr := strings.Join(parts, "&")

	// 5. 计算MD5签名并转换为大写
	h := md5.New()
	h.Write([]byte(signStr))
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}

// encrypt encrypts data using AES-256-CBC with PKCS7 padding
func (s *QQPokerService) encrypt(data []byte) (string, error) {
	block, err := aes.NewCipher([]byte(s.appIdKey))
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Add PKCS7 padding
	blockSize := block.BlockSize()
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	data = append(data, padText...)

	encrypted := make([]byte, len(data))
	mode := cipher.NewCBCEncrypter(block, []byte(s.secretIv))
	mode.CryptBlocks(encrypted, data)

	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// makeRequest 发送HTTP请求
func (s *QQPokerService) makeRequest(method string, payload interface{}) (map[string]interface{}, error) {
	// 1. 将参数转换为JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("marshal params failed: %v", err)
	}

	// 2. 加密JSON数据
	encryptedData, err := s.encrypt(jsonData)
	if err != nil {
		return nil, fmt.Errorf("encrypt data failed: %v", err)
	}

	// 3. 构建请求体
	requestBody := map[string]interface{}{
		"m": s.merchantId,
		"b": encryptedData,
		"t": time.Now().Unix(),
	}

	// 4. 将请求体转换为JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %v", err)
	}

	//logs.Info("QQPoker请求URL:", s.apiUrl+method)
	//logs.Info("QQPoker请求报文:", string(jsonData))

	// 发送POST请求
	resp, err := http.Post(s.apiUrl+method, "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		logs.Error("QQPoker 请求失败: err=", err.Error())
		return nil, err
	}

	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("QQPoker 读取响应失败: err=", err.Error())
		return nil, err
	}

	//logs.Info("QQPoker响应报文: ", string(body))

	// 解析响应JSON
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("QQPoker 解析响应失败: err=", err.Error())
		return nil, err
	}

	return result, nil
}

func getUserBalance(userId int) (float64, error) {
	var userBalance thirdGameModel.UserBalance
	err := server.Db().GormDao().Table("x_user").
		Select("UserId,SellerId,ChannelId,Amount,Token").
		Where("UserId = ?", userId).
		First(&userBalance).Error
	if err != nil {
		return 0, err
	}
	return userBalance.Amount, nil
}

// GetHotEvents 获取德州扑克热门赛事
func (s *QQPokerService) GetHotEvents(ctx *abugo.AbuHttpContent) {
	// 定义比赛结构体
	type Match struct {
		ID     int    `json:"id"`  // 比赛ID
		MID    int    `json:"mId"` // matchMapId
		Name   string `json:"n"`   // 牌桌名称
		Icon   string `json:"i"`   // Icon
		CID    int    `json:"cId"` // clubId
		GID    int    `json:"gId"` // gameId
		BT     int    `json:"bT"`  // betType下注类型
		CC     []int  `json:"cC"`  // chairCount 座位数
		IC     int    `json:"iC"`  // initChips 初始筹码
		PT     int    `json:"pT"`  // poolType 奖励货币id
		GP     int    `json:"gP"`  // guaranteedPool 保底奖励
		OM     int    `json:"oM"`  // openMode 开启模式
		TS     []int  `json:"ts"`  // times 时间信息(单位：秒)
		Status []int  `json:"s"`   // 比赛状态(数组)
		BL     int    `json:"bL"`  // blindLevel 当前盲注级别
		PC     []int  `json:"pC"`  // playerCount 报名人数等
	}

	// 定义响应结构体
	type ResponseData struct {
		StatusCode int    `json:"status_code"` // 状态码
		Message    string `json:"message"`     // 消息
		Data       struct {
			GameID     int `json:"gameId"`     // 游戏ID
			PageIndex  int `json:"pageIndex"`  // 页码
			PageSize   int `json:"pageSize"`   // 每页数量
			TotalCount int `json:"totalCount"` // 总数量
			Matches    []struct {
				Match      Match `json:"match"` // 比赛信息
				TP         int   `json:"tP"`    // totalPrize 总奖池
				Status     int   `json:"s"`     // 状态
				Time       []int `json:"t"`     // 时间信息
				BlindLevel int   `json:"bL"`    // 当前盲注级别
				PC         []int `json:"pC"`    // playerCount
				LA         []int `json:"lA"`    // lAttach
			} `json:"matches"`
		} `json:"data"`
	}

	// 获取请求参数
	type requestParams struct {
		LangCode string `json:"LangCode"` // 语言，默认en(英文)
		GameID   string `json:"GameId"`   // 游戏ID
	}

	// 解析请求参数
	reqData := requestParams{
		LangCode: "en", // 默认英文
	}

	// 尝试从请求中获取参数
	err := ctx.RequestData(&reqData)
	if err != nil {
		logs.Error("[ERROR][qqpoker] GetHotEvents 解析请求参数失败: ", err.Error())
	}

	// 缓存key
	rKey := fmt.Sprintf("%s:%s:qqpoker:hotevents:%d", server.Project(), server.Module(), reqData.GameID)
	rValueInterface := server.Redis().Get(rKey)
	rValue := abugo.GetStringFromInterface(rValueInterface)
	if rValue != "" {
		// 如果缓存存在，直接返回缓存数据
		var rspdata ResponseData
		err := json.Unmarshal([]byte(rValue), &rspdata)
		if err == nil {
			// 限制返回给前端的数据为 20 条
			matches := rspdata.Data.Matches
			if len(matches) > 10 {
				matches = matches[:10]
			}

			// 只返回 matches 数据给前端
			ctx.RespOK(matches)
			return
		}
		// 缓存解析失败，继续请求API
		logs.Error("[ERROR][qqpoker] GetHotEvents 缓存解析失败: ", err.Error())
	}

	// 构建请求参 数
	payload := map[string]interface{}{
		"page_index": 0,  // 默认页码为0
		"page_size":  15, // 默认每页显示50条
		"lang":       reqData.LangCode,
		"game_id":    0,
		"status":     1,
	}

	errcode := 0
	// 发送请求
	result, err := s.makeRequest("/v1/seamless/matches", payload)
	if err != nil {
		logs.Error("[ERROR][qqpoker] GetHotEvents 请求失败: ", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	// 将结果转换为JSON字符串
	responseBytes, err := json.Marshal(result)
	if err != nil {
		logs.Error("[ERROR][qqpoker] GetHotEvents 响应序列化失败: ", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	// 解析响应数据
	var rspdata ResponseData
	err = json.Unmarshal(responseBytes, &rspdata)
	if err != nil {
		logs.Error("[ERROR][qqpoker] GetHotEvents 响应解析失败: ", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	// 检查响应状态
	if rspdata.StatusCode != 200 {
		logs.Error("[ERROR][qqpoker] GetHotEvents API返回错误: ", rspdata.Message)
		ctx.RespErrString(true, &errcode, "获取热门赛事失败: "+rspdata.Message)
		return
	}

	// 设置缓存，有效期30秒
	defer func() {
		if e := server.Redis().SetStringEx(rKey, 30, string(responseBytes)); e != nil {
			logs.Error("[ERROR][qqpoker] GetHotEvents Redis SetStringEx err=", e.Error())
		}
	}()

	// 限制返回给前端的数据为 10 条
	matches := rspdata.Data.Matches
	if len(matches) > 10 {
		matches = matches[:10]
	}

	// 只返回 matches 数据给前端
	ctx.RespOK(matches)
	return
}
