// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXOrder = "x_order"

// XOrder mapped from table <x_order>
type XOrder struct {
	ID          int32   `gorm:"column:Id;primaryKey" json:"Id"`
	SellerID    int32   `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                      // 运营商
	UserID      int32   `gorm:"column:UserId;comment:玩家id" json:"UserId"`                                         // 玩家id
	GameID      int32   `gorm:"column:GameId;comment:游戏id" json:"GameId"`                                         // 游戏id
	RoomLevel   int32   `gorm:"column:RoomLevel;comment:房间等级" json:"RoomLevel"`                                   // 房间等级
	ToAddress   string  `gorm:"column:ToAddress;comment:游戏地址" json:"ToAddress"`                                   // 游戏地址
	FromAddress string  `gorm:"column:FromAddress;comment:玩家地址" json:"FromAddress"`                               // 玩家地址
	TxID        string  `gorm:"column:TxId;comment:下注哈希" json:"TxId"`                                             // 下注哈希
	ChainType   int32   `gorm:"column:ChainType;not null;default:1;comment:网链分类 1trc 2erc 3bsc" json:"ChainType"` // 网链分类 1trc 2erc 3bsc
	BlockNum    int32   `gorm:"column:BlockNum;comment:下注块编号" json:"BlockNum"`                                    // 下注块编号
	BlockHash   string  `gorm:"column:BlockHash;comment:下注块哈希" json:"BlockHash"`                                  // 下注块哈希
	Symbol      string  `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                           // 币种
	Amount      float64 `gorm:"column:Amount;comment:下注金额" json:"Amount"`                                         // 下注金额
	BonusAmount float64 `gorm:"column:BonusAmount;default:0.000000;comment:Bonus下注金额" json:"BonusAmount"`         // Bonus下注金额
	/*
		订单状态
		1.未找到房间信息
		2.未找到运营商
	*/
	State                 int32     `gorm:"column:State;comment:订单状态 \n1.未找到房间信息 \n2.未找到运营商" json:"State"`
	Memo                  string    `gorm:"column:Memo;comment:备忘录" json:"Memo"`                                                   // 备忘录
	CreateTime            time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:订单创建时间" json:"CreateTime"`          // 订单创建时间
	RewardOrder           string    `gorm:"column:RewardOrder;comment:返奖订单号" json:"RewardOrder"`                                   // 返奖订单号
	RewardAmount          float64   `gorm:"column:RewardAmount;comment:返奖励金额" json:"RewardAmount"`                                 // 返奖励金额
	BonusRewardAmount     float64   `gorm:"column:BonusRewardAmount;default:0.000000;comment:Bonus返奖励金额" json:"BonusRewardAmount"` // Bonus返奖励金额
	RewardTxID            string    `gorm:"column:RewardTxId;comment:返奖哈希" json:"RewardTxId"`                                      // 返奖哈希
	WinAmount             float64   `gorm:"column:WinAmount;comment:中奖金额" json:"WinAmount"`                                        // 中奖金额
	BetArea               string    `gorm:"column:BetArea;comment:下注区域" json:"BetArea"`                                            // 下注区域
	OpenArea              string    `gorm:"column:OpenArea;comment:开奖结果" json:"OpenArea"`                                          // 开奖结果
	RewardType            int32     `gorm:"column:RewardType;comment:赔率类型" json:"RewardType"`                                      // 赔率类型
	RewardRate            float64   `gorm:"column:RewardRate;comment:赔率" json:"RewardRate"`                                        // 赔率
	DataState             int32     `gorm:"column:DataState;default:1;comment:数据状态" json:"DataState"`                              // 数据状态
	AuditAccount          string    `gorm:"column:AuditAccount;comment:审核人" json:"AuditAccount"`                                   // 审核人
	AuditTime             time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                        // 审核时间
	GasFee                float64   `gorm:"column:GasFee;default:0.************;comment:gas费" json:"GasFee"`                       // gas费
	IsWin                 int32     `gorm:"column:IsWin;comment:是否中奖 1中奖 2不中奖" json:"IsWin"`                                       // 是否中奖 1中奖 2不中奖
	Fee                   float64   `gorm:"column:Fee;default:0.000000;comment:手续费" json:"Fee"`                                    // 手续费
	IsHe                  int32     `gorm:"column:IsHe;default:2;comment:是否和局" json:"IsHe"`                                        // 是否和局
	Fined                 int32     `gorm:"column:Fined;default:2;comment:是否已扣除业绩 1已扣除 2 未扣除" json:"Fined"`                        // 是否已扣除业绩 1已扣除 2 未扣除
	LiuSui                float64   `gorm:"column:LiuSui;default:0.000000;comment:流水" json:"LiuSui"`                               // 流水
	FirstLiuSui           float64   `gorm:"column:FirstLiuSui;default:0.000000;comment:扣减前有效投注" json:"FirstLiuSui"`                // 扣减前有效投注
	ValidBetAmount        float64   `gorm:"column:ValidBetAmount;default:0.000000;comment:有效投注" json:"ValidBetAmount"`             // 有效投注
	TopAgentID            int32     `gorm:"column:TopAgentId;comment:顶级id" json:"TopAgentId"`                                      // 顶级id
	FineAccount           string    `gorm:"column:FineAccount;comment:扣除账号" json:"FineAccount"`                                    // 扣除账号
	FineTime              time.Time `gorm:"column:FineTime;comment:扣除时间" json:"FineTime"`                                          // 扣除时间
	FineMemo              string    `gorm:"column:FineMemo;comment:扣除备注" json:"FineMemo"`                                          // 扣除备注
	HbcState              int32     `gorm:"column:HbcState;comment:hbc状态" json:"HbcState"`                                         // hbc状态
	ChannelID             int32     `gorm:"column:ChannelId;default:1;comment:渠道id" json:"ChannelId"`                              // 渠道id
	BetChannelID          int32     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"`                                // 下注渠道Id
	BlockTime             int64     `gorm:"column:BlockTime;comment:区块时间" json:"BlockTime"`                                        // 区块时间
	Period                string    `gorm:"column:Period;comment:彩票期号" json:"Period"`                                              // 彩票期号
	ReportState           int32     `gorm:"column:ReportState;comment:tg机器人播报状态" json:"ReportState"`                               // tg机器人播报状态
	BlockMaker            string    `gorm:"column:BlockMaker;comment:出块者" json:"BlockMaker"`                                       // 出块者
	NextBlockHash         string    `gorm:"column:NextBlockHash;comment:下一个区块区块哈希值" json:"NextBlockHash"`                          // 下一个区块区块哈希值
	RewardTime            time.Time `gorm:"column:RewardTime;comment:返奖时间" json:"RewardTime"`                                      // 返奖时间
	ExceedLimit           int32     `gorm:"column:ExceedLimit;default:2;comment:是否超限制  1超过,2未超" json:"ExceedLimit"`                // 是否超限制  1超过,2未超
	UserAmount            float64   `gorm:"column:UserAmount;default:0.000000;comment:玩家该笔下注后余额" json:"UserAmount"`                // 玩家该笔下注后余额
	UserBonusAmount       float64   `gorm:"column:UserBonusAmount;default:0.000000;comment:玩家该笔下注后Bonus余额" json:"UserBonusAmount"` // 玩家该笔下注后Bonus余额
	IsTest                int32     `gorm:"column:IsTest;default:2;comment:是否是测试账号的注单" json:"IsTest"`                              // 是否是测试账号的注单
	IsPanda               int32     `gorm:"column:IsPanda;default:2;comment:是否是量化用户" json:"IsPanda"`                               // 是否是量化用户
	HeFeeRate             float64   `gorm:"column:HeFeeRate" json:"HeFeeRate"`
	IsFanBei              int32     `gorm:"column:IsFanBei;default:2;comment:是否翻倍 ”翻倍”在牛牛玩法中是指”牛牛”、”牛九”二种中奖情形；在庄闲玩法是指”和”的中奖情形" json:"IsFanBei"` // 是否翻倍 ”翻倍”在牛牛玩法中是指”牛牛”、”牛九”二种中奖情形；在庄闲玩法是指”和”的中奖情形
	CSGroup               string    `gorm:"column:CSGroup" json:"CSGroup"`
	CSID                  string    `gorm:"column:CSId" json:"CSId"`
	SpecialAgent          int32     `gorm:"column:SpecialAgent" json:"SpecialAgent"`
	IsCommission          int32     `gorm:"column:IsCommission;comment:是否计算佣金 1计算 2不计算" json:"IsCommission"`          // 是否计算佣金 1计算 2不计算
	UtType                int32     `gorm:"column:UtType;comment:是否ut下注 1-是" json:"UtType"`                           // 是否ut下注 1-是
	IP                    string    `gorm:"column:Ip;comment:登录ip" json:"Ip"`                                         // 登录ip
	Lang                  string    `gorm:"column:Lang;comment:登录语言" json:"Lang"`                                     // 登录语言
	BlackUserType         int32     `gorm:"column:BlackUserType;comment:用户类型 1用户 2代理 3渠道 4运营商" json:"BlackUserType"`  // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID           int32     `gorm:"column:BlackUserId;comment:用户Id" json:"BlackUserId"`                       // 用户Id
	RiskEarlyWarningFirst int32     `gorm:"column:RiskEarlyWarningFirst;comment:风控预警首次" json:"RiskEarlyWarningFirst"` // 风控预警首次
	RandomHash            string    `gorm:"column:RandomHash;comment:随机区块哈希" json:"RandomHash"`                       // 随机区块哈希
	RandomBlock           string    `gorm:"column:RandomBlock;comment:随机区块" json:"RandomBlock"`                       // 随机区块
	Random                int32     `gorm:"column:Random;comment:随机数" json:"Random"`                                  // 随机数
	Md5Hash               string    `gorm:"column:Md5Hash;comment:md5hash" json:"Md5Hash"`                            // md5hash
}

// TableName XOrder's table name
func (*XOrder) TableName() string {
	return TableNameXOrder
}
