package base

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"sort"
	"strconv"
	"strings"
	"unicode"
	"xserver/abugo"
	"xserver/controller/common"
	"xserver/controller/userbrand"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	daogormclause "gorm.io/gorm/clause"

	daogorm "gorm.io/gorm"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"github.com/imroc/req"
	jsoniter "github.com/json-iterator/go"
)

var MyJson = jsoniter.Config{
	UseNumber: true,
}.Froze()

func UserId2token(cacheKey string, userId int) string {
	token := uuid.NewString()
	if err := server.Redis().SetStringEx(cacheKey+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("AstarSwSrvice userId2token set redis error:%s", err)
	}
	return token
}

func Token2UserId(cacheKey, token string) int {
	redisdata := server.Redis().Get(cacheKey + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

func IsLoginByUserId(cacheKey string, userId int) (error, int) {

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest, State").Where(where).GetOne()
	if data == nil {

		return errors.New("进入失败"), 0
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {

		return errors.New("当前账号暂时无法进入该游戏，请联系客服"), 0
	}
	// 添加对用户状态的检查
	state := abugo.GetInt64FromInterface((*data)["State"])
	if state != 1 {
		return errors.New("当前账号暂时无法进入该游戏，请联系客服"), 0
	}
	rediskey := fmt.Sprintf("%v:third_login_%v", cacheKey, userId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		return errors.New("操作频繁,请稍后再试"), 1000001
	}
	defer server.Redis().Del(rediskey)
	return nil, 0
}

func DoPostJson(url, querydata string) (map[string]interface{}, error) {
	reqdata := map[string]interface{}{}
	json.Unmarshal([]byte(querydata), &reqdata)
	header := req.Header{
		"Content-Type": "application/json",
	}
	reqbytes, err := json.Marshal(&reqdata)
	resp, err := req.Post(url, header, string(reqbytes))
	if err != nil {
		logs.Error("http_post request error:", url, err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("http_post body error:", url, err)
		return nil, err
	}
	logs.Debug("http_post:", url, "|", string(reqbytes), "|", string(body))
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		return nil, err
	}
	return jdata, nil
}

// IsGameAvailable 验证游戏是否存在且可用，如果可用则返回游戏信息
func IsGameAvailable(brandName string, gameId string) (*thirdGameModel.GameList, error, int) {
	// 查询游戏信息
	gameList := thirdGameModel.GameList{}
	err := server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", brandName, gameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			return nil, errors.New("游戏code不存在!"), 1000002
		}
		logs.Error("验证游戏 查询游戏错误 GameId=", gameId, " Brand=", brandName, " err=", err.Error())
		return nil, errors.New("系统错误,请稍后再试"), 1000003
	}

	// 验证游戏状态
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("验证游戏 游戏不可用 GameId=", gameId, " Brand=", brandName, " gameList=", gameList)
		return nil, errors.New("游戏未开放"), 1000004
	}

	// 可以添加更多的验证逻辑，例如游戏维护时间、区域限制等

	return &gameList, nil, 0
}

func GetUserBalance(userId int, cacheKey string) (udata *map[string]interface{}, balance float64, err error) {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	udata, err = server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		return nil, 0, err
	}
	if udata == nil {
		logs.Error(cacheKey+" getUser failed, uid: %d", userId)
		return nil, 0, errors.New("user not found")
	}
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	return udata, balance, nil
}

// 返回用户和用户余额信息
func GetUserById(userId int) (udata *thirdGameModel.UserBalance, balance float64, err error) {
	userBalance := thirdGameModel.UserBalance{}
	r := server.Db().GormDao().Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
	if r != nil {
		if errors.Is(r, daogorm.ErrRecordNotFound) {
			logs.Error("查询用户失败，用户不存在 userId=", userId)
		} else {
			logs.Error("查询用户失败 userId=", userId, " e=", r.Error())
		}
		return nil, 0, r
	}
	balance = userBalance.Amount
	return &userBalance, balance, nil
}

func OrderIsExist(thirdId, brand, table string) (*map[string]interface{}, bool) {
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", brand, nil)
	betTran, _ := server.Db().Table(table).Where(where).GetOne()
	if betTran != nil {
		dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
		if dataState >= 1 {
			return betTran, true
		}
	}
	return betTran, false
}

func GetOrder(thirdId, brand, table string) *map[string]interface{} {
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", brand, nil)
	betTran, _ := server.Db().Table(table).Where(where).GetOne()
	if betTran == nil {
		return nil
	} else {
		return betTran
	}
}

// GetOrderForUpdate 查询订单是否存在并返回订单结构体和状态
// 返回值:
// order: 如果订单存在，返回订单结构体；如果不存在，返回nil
// exists: 表示订单是否存在
// err: 如果是查询错误（非记录不存在），返回错误；否则返回nil
func GetOrderForUpdate(tx *daogorm.DB, thirdId, brand string, tableName string) (order *thirdGameModel.ThirdOrder, exists bool, err error) {
	orderData := thirdGameModel.ThirdOrder{}
	// 查询订单是否存在
	queryErr := tx.Table(tableName).Where("ThirdId = ? AND Brand = ?", thirdId, brand).
		Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&orderData).Error
	// 如果没有错误，说明订单存在
	if queryErr == nil && orderData.Id > 0 {
		return &orderData, true, nil
	}
	// 如果是记录不存在的错误，返回nil, false, nil
	if errors.Is(queryErr, daogorm.ErrRecordNotFound) {
		return nil, false, nil
	}
	// 其他错误，如数据库查询错误
	return nil, false, queryErr
}

// GetValidBet 所有的有效流水取不大于下注金额的输赢绝对值
func GetValidBet(betAmt, winAmount float64) float64 {
	if winAmount > 0 {
		subAmount := winAmount - betAmt
		if subAmount > betAmt {
			return betAmt
		} else {
			return math.Abs(subAmount)
		}
	} else {
		return betAmt
	}
}

func DeductGoldByUserId(userId int, gold float64) error {
	_, err := server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", gold, userId, gold)

	return err
}

// sec  以什么为链接符号 例如 "&”
// eq   key=value例如 "=”
// orderBy desc  asc
func SortMap(in map[string]any, sec, eq, orderBy string) string {
	var s string
	x := []string{}
	for i := range in {
		x = append(x, i)
	}
	if orderBy == "desc" {
		sort.Sort(sort.Reverse(sort.StringSlice(x)))
	} else {
		sort.Strings(x)
	}
	for _, v := range x {
		if in[v] == "" || in[v] == nil {
			continue
		}
		s += fmt.Sprintf("%s%s%s%s", v, eq, fmt.Sprint(in[v]), sec)
	}

	return strings.TrimRight(s, "&")
}

func MD5(str string) string {
	data := []byte(str)
	has := md5.Sum(data)
	return fmt.Sprintf("%x", has)
}

func MD5Up(str string) string {
	data := []byte(str)
	has := md5.Sum(data)
	return fmt.Sprintf("%X", has)
}

func StrToFloat64(s string) float64 {
	floatValue, _ := strconv.ParseFloat(s, 64)
	return floatValue
}

func ToTitleFirst(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// AddSportResettleLog 增加重新结算记录
/**
 * 体育会出现多次重新结算记录结算历史
 */
func AddSportResettleLog(tx *daogorm.DB, order thirdGameModel.ThirdSportOrder, thirdTime string, resultCtx string, changeAmount float64, changeType int, state int, thirdRefId string, resettleTime string, cancelTime string, memo string) error {
	sportResettleLog := thirdGameModel.ThirdSportResettleLog{
		UserId:     order.UserId,
		Brand:      order.Brand,
		ThirdId:    order.ThirdId,
		GameId:     order.GameId,
		GameName:   order.GameName,
		BetAmount:  order.BetAmount,
		WinAmount:  order.WinAmount,
		Amount:     changeAmount, // 金额
		ChangeType: changeType,   // 0增加资金 1减少资金
		ThirdTime:  thirdTime,
		Currency:   order.Currency,
		RawData:    resultCtx,
		State:      state,
		BetCtxType: order.BetCtxType,
		ThirdRefId: thirdRefId,
		CreateTime: thirdTime, //创建日期
		Memo:       memo,
	}

	if resettleTime == "" {
		sportResettleLog.ResettleTime = nil
	} else {
		sportResettleLog.ResettleTime = &resettleTime
	}
	if cancelTime == "" {
		sportResettleLog.CancelTime = nil
	} else {
		sportResettleLog.CancelTime = &cancelTime
	}

	e := tx.Table("x_third_sport_resettle_log").Create(&sportResettleLog).Error
	if e != nil {
		return e
	}
	return nil
}

// BeforeEnterGame 在进入游戏前调用此方法
// BeforeEnterGame 检查用户是否被禁止进入该游戏厂商，并记录用户当前游戏
// 返回值: (allowed bool, hint string, err error)
func BeforeEnterGame(userId int, gameType int, brand string) (bool, string, error) {
	// 检查用户余额是否被冻结
	frozen, message := common.CheckUserFrozen(userId)
	if frozen {
		return false, message, nil
	}

	// 检查用户是否被禁止进入该游戏厂商
	blocked, message := CheckUserGameBrandBlocked(userId, gameType, brand)
	if blocked {
		return false, message, nil
	}

	// 记录用户当前游戏到Redis，缓存5分钟
	err := userbrand.SetUserCurrentGame(userId, gameType, brand)
	if err != nil {
		logs.Error("记录用户当前游戏失败: userId=%d, gameType=%d, brand=%s, error=%v", userId, gameType, brand, err)
		return false, "系统错误", err
	}

	return true, "", nil
}

// BeforeEnterGameId 根据brand和gameId检查用户是否被禁止进入该游戏厂商
// 返回值: (allowed bool, hint string, err error)
func BeforeEnterGameId(userId int, brand string, gameId string) (bool, string, error) {
	if gameId == "" {
		logs.Error("游戏ID不能为空", userId)
		return false, "游戏ID不能为空", nil
	}

	if brand == "" {
		logs.Error("游戏厂商不能为空", userId)
		return false, "游戏厂商不能为空", nil
	}

	// 检查用户余额是否被冻结
	frozen, message := common.CheckUserFrozen(userId)
	if frozen {
		return false, message, nil
	}

	// 查询游戏信息
	var gameInfo struct {
		Brand     string `gorm:"column:Brand"`
		GameType  int    `gorm:"column:GameType"`
		State     int    `gorm:"column:State"`
		OpenState int    `gorm:"column:OpenState"`
		Name      string `gorm:"column:Name"`
	}

	db := server.Db().GormDao()

	// 对于特殊的Brand前缀，使用LIKE查询
	var err error
	if strings.HasPrefix(brand, "OPS_") || strings.HasPrefix(brand, "HS8_") {
		err = db.Table("x_game_list").
			Select("Brand, GameType, State, OpenState, Name").
			Where("Brand LIKE ? AND GameId = ?", brand+"%", gameId).
			First(&gameInfo).Error
	} else {
		err = db.Table("x_game_list").
			Select("Brand, GameType, State, OpenState, Name").
			Where("Brand = ? AND GameId = ?", brand, gameId).
			First(&gameInfo).Error
	}

	if err != nil {
		if err.Error() == "record not found" {
			logs.Warning("游戏ID未找到 gameId=%s", gameId)
			return false, "游戏不存在", nil
		}
		logs.Error("查询游戏信息失败 gameId=%s err=%v", gameId, err)
		return false, "系统错误", err
	}

	// 检查游戏是否可用
	if gameInfo.State != 1 {
		logs.Info("游戏已禁用 userId=%d gameId=%s brand=%s", userId, gameId, gameInfo.Brand)
		return false, "游戏已禁用", nil
	}

	if gameInfo.OpenState != 1 {
		logs.Info("游戏未开放 userId=%d gameId=%s brand=%s", userId, gameId, gameInfo.Brand)
		return false, "游戏未开放", nil
	}

	// 检查用户是否被禁止进入该游戏厂商
	blocked, message := CheckUserGameBrandBlocked(userId, gameInfo.GameType, gameInfo.Brand)
	if blocked {
		logs.Info("用户被禁止进入游戏厂商 userId=%d gameId=%s brand=%s gameType=%d message=%s",
			userId, gameId, gameInfo.Brand, gameInfo.GameType, message)
		return false, message, nil
	}

	// 记录用户当前游戏到Redis，缓存5分钟
	err = userbrand.SetUserCurrentGame(userId, gameInfo.GameType, gameInfo.Brand)
	if err != nil {
		logs.Error("记录用户当前游戏失败: userId=%d, gameId=%s, gameType=%d, brand=%s, error=%v",
			userId, gameId, gameInfo.GameType, gameInfo.Brand, err)
		return false, "系统错误", err
	}

	logs.Info("用户进入游戏检查通过 userId=%d gameId=%s brand=%s gameType=%d gameName=%s",
		userId, gameId, gameInfo.Brand, gameInfo.GameType, gameInfo.Name)
	return true, "", nil
}

// CheckUserGameBrandBlocked 检查用户是否被禁止进入游戏厂商
func CheckUserGameBrandBlocked(userId int, gameType int, brand string) (bool, string) {
	// 使用 userbrand 包中的统一逻辑获取该游戏类型下的禁用品牌
	blockedBrands, err := userbrand.GetBlockedBrandsByGameType(userId, gameType)
	if err != nil {
		logs.Error("获取用户禁止游戏厂商失败 userId=", userId, " gameType=", gameType, " err=", err.Error())
		return false, ""
	}

	// 检查当前游戏品牌是否在该游戏类型的禁止列表中
	for _, blockedBrand := range blockedBrands {
		if blockedBrand == brand {
			return true, "暂时不能进入，请联系客服"
		}
	}

	return false, ""
}

// AfterExitGame 在退出游戏时调用此方法
func AfterExitGame(userId int) {
	//清除用户当前游戏
	err := userbrand.ClearUserCurrentGame(userId)
	if err != nil {
		logs.Error("清除用户当前游戏失败: userId=%d, error=%v", userId, err)
	}
}

// GetUserChannelId 获取用户的渠道ID，优先使用令牌中的信息，令牌无效时使用用户自身的渠道ID
func GetUserChannelId(ctx *abugo.AbuHttpContent, user *thirdGameModel.UserBalance) int {
	// 检查 user 是否为 nil
	if user == nil {
		logs.Error("GetUserChannelId: 用户对象为空,使用默认渠道 1")
		return 1 // 返回默认渠道ID
	}

	// 检查 user.Token 是否为空
	if user.Token == "" {
		logs.Info("GetUserChannelId: 用户Token为空，使用用户自身的渠道ID，用户ID:", user.UserId)
		return user.ChannelId
	}

	// 从 Redis 获取令牌并检查
	token := server.GetTokenFromRedis(user.Token)
	if token == nil {
		logs.Info("GetUserChannelId: 无法从Redis获取令牌，使用用户自身的渠道ID，用户ID:", user.UserId, "，Token:", user.Token)
		return user.ChannelId
	}

	// 令牌存在，安全地获取渠道ID
	channelId, _ := server.GetChannel(ctx, token.Host)
	return channelId
}

// GetUserChannelIdFromMap 从用户数据map中获取渠道ID，优先使用令牌中的信息
// 参数:
//   - ctx: HTTP上下文
//   - udata: 用户数据map
//   - userId: 用户ID (用于日志记录)
//
// 返回: 渠道ID (int 类型)
func GetUserChannelIdFromMap(ctx *abugo.AbuHttpContent, udata *map[string]interface{}, userId int) int {
	// 获取用户渠道ID (默认值)，使用abugo的通用转换函数
	var defaultChannelId int = 1 // 默认渠道ID
	if channelIdRaw, ok := (*udata)["ChannelId"]; ok {
		// 使用abugo的通用转换函数，支持多种数字类型
		channelIdInt64 := abugo.GetInt64FromInterface(channelIdRaw)
		if channelIdInt64 > 0 {
			defaultChannelId = int(channelIdInt64)
			logs.Info("成功获取用户自身渠道ID，用户ID:", userId, "，渠道ID:", defaultChannelId)
		} else {
			logs.Warning("用户渠道ID无效，使用默认值1，用户ID:", userId, "，原值:", channelIdRaw)
		}
	}

	// 检查用户Token是否存在
	if userToken, ok := (*udata)["Token"]; ok && userToken != "" {
		// 从Redis获取令牌
		token := server.GetTokenFromRedis(abugo.GetStringFromInterface(userToken))
		if token != nil {
			// 令牌存在，获取渠道ID
			newChannelId, _ := server.GetChannel(ctx, token.Host)
			logs.Info("成功从令牌获取渠道ID，用户ID:", userId, "，渠道ID:", newChannelId)
			return newChannelId
		} else {
			logs.Info("无法从Redis获取令牌，使用用户自身的渠道ID，用户ID:", userId, "，渠道ID:", defaultChannelId)
		}
	} else {
		logs.Info("用户Token为空，使用用户自身的渠道ID，用户ID:", userId, "，渠道ID:", defaultChannelId)
	}

	return defaultChannelId
}

// ParseUserIdFromPlayerName 解析 PlayerName 或 UserId 字符串，兼容 "币种_用户ID" 与纯数字
func ParseUserIdFromPlayerName(value string) int {
	if value == "" {
		return 0
	}
	if strings.Contains(value, "_") {
		parts := strings.Split(value, "_")
		if len(parts) >= 2 {
			if parsedUserId, err := strconv.Atoi(parts[len(parts)-1]); err == nil {
				return parsedUserId
			}
		}
	}
	if parsedUserId, err := strconv.Atoi(value); err == nil {
		return parsedUserId
	}
	return 0
}
