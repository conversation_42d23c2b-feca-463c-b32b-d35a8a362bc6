// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdSport(db *gorm.DB, opts ...gen.DOOption) xThirdSport {
	_xThirdSport := xThirdSport{}

	_xThirdSport.xThirdSportDo.UseDB(db, opts...)
	_xThirdSport.xThirdSportDo.UseModel(&model.XThirdSport{})

	tableName := _xThirdSport.xThirdSportDo.TableName()
	_xThirdSport.ALL = field.NewAsterisk(tableName)
	_xThirdSport.ID = field.NewInt64(tableName, "Id")
	_xThirdSport.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdSport.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdSport.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdSport.UserID = field.NewInt32(tableName, "UserId")
	_xThirdSport.Brand = field.NewString(tableName, "Brand")
	_xThirdSport.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdSport.GameID = field.NewString(tableName, "GameId")
	_xThirdSport.GameName = field.NewString(tableName, "GameName")
	_xThirdSport.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdSport.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdSport.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdSport.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdSport.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdSport.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdSport.Currency = field.NewString(tableName, "Currency")
	_xThirdSport.RawData = field.NewString(tableName, "RawData")
	_xThirdSport.State = field.NewInt32(tableName, "State")
	_xThirdSport.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdSport.DataState = field.NewInt32(tableName, "DataState")
	_xThirdSport.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdSport.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdSport.CSID = field.NewString(tableName, "CSId")
	_xThirdSport.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdSport.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdSport.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdSport.GameRst = field.NewString(tableName, "GameRst")
	_xThirdSport.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdSport.IP = field.NewString(tableName, "Ip")
	_xThirdSport.Lang = field.NewString(tableName, "Lang")
	_xThirdSport.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdSport.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdSport.ResettleState = field.NewInt32(tableName, "ResettleState")
	_xThirdSport.ResettleTime = field.NewTime(tableName, "ResettleTime")
	_xThirdSport.ResettleNumber = field.NewInt32(tableName, "ResettleNumber")
	_xThirdSport.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdSport.SettleTime = field.NewTime(tableName, "SettleTime")
	_xThirdSport.CancelTime = field.NewTime(tableName, "CancelTime")
	_xThirdSport.BetTime = field.NewTime(tableName, "BetTime")
	_xThirdSport.BetLocalTime = field.NewTime(tableName, "BetLocalTime")
	_xThirdSport.BetType = field.NewInt32(tableName, "BetType")
	_xThirdSport.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdSport.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdSport.fillFieldMap()

	return _xThirdSport
}

type xThirdSport struct {
	xThirdSportDo xThirdSportDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	ResettleState  field.Int32   // 是否重新结算0-否 1-是 -1撤销结算
	ResettleTime   field.Time    // 体育涉及重新结算重新结算日期
	ResettleNumber field.Int32   // 体育重新结算次数
	ThirdRefID     field.String  // 体育三方流水号(用来处理重复请求问题)
	SettleTime     field.Time    // 订单结算时间-三方系统时间
	CancelTime     field.Time    // 订单取消时间
	BetTime        field.Time    // 投注时间-三方系统时间
	BetLocalTime   field.Time    // 投注时间-本地服务器时间-北京时间
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdSport) Table(newTableName string) *xThirdSport {
	x.xThirdSportDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdSport) As(alias string) *xThirdSport {
	x.xThirdSportDo.DO = *(x.xThirdSportDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdSport) updateTableName(table string) *xThirdSport {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.ResettleState = field.NewInt32(table, "ResettleState")
	x.ResettleTime = field.NewTime(table, "ResettleTime")
	x.ResettleNumber = field.NewInt32(table, "ResettleNumber")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.SettleTime = field.NewTime(table, "SettleTime")
	x.CancelTime = field.NewTime(table, "CancelTime")
	x.BetTime = field.NewTime(table, "BetTime")
	x.BetLocalTime = field.NewTime(table, "BetLocalTime")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdSport) WithContext(ctx context.Context) *xThirdSportDo {
	return x.xThirdSportDo.WithContext(ctx)
}

func (x xThirdSport) TableName() string { return x.xThirdSportDo.TableName() }

func (x xThirdSport) Alias() string { return x.xThirdSportDo.Alias() }

func (x xThirdSport) Columns(cols ...field.Expr) gen.Columns { return x.xThirdSportDo.Columns(cols...) }

func (x *xThirdSport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdSport) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 43)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["ResettleState"] = x.ResettleState
	x.fieldMap["ResettleTime"] = x.ResettleTime
	x.fieldMap["ResettleNumber"] = x.ResettleNumber
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["SettleTime"] = x.SettleTime
	x.fieldMap["CancelTime"] = x.CancelTime
	x.fieldMap["BetTime"] = x.BetTime
	x.fieldMap["BetLocalTime"] = x.BetLocalTime
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdSport) clone(db *gorm.DB) xThirdSport {
	x.xThirdSportDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdSport) replaceDB(db *gorm.DB) xThirdSport {
	x.xThirdSportDo.ReplaceDB(db)
	return x
}

type xThirdSportDo struct{ gen.DO }

func (x xThirdSportDo) Debug() *xThirdSportDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdSportDo) WithContext(ctx context.Context) *xThirdSportDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdSportDo) ReadDB() *xThirdSportDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdSportDo) WriteDB() *xThirdSportDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdSportDo) Session(config *gorm.Session) *xThirdSportDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdSportDo) Clauses(conds ...clause.Expression) *xThirdSportDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdSportDo) Returning(value interface{}, columns ...string) *xThirdSportDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdSportDo) Not(conds ...gen.Condition) *xThirdSportDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdSportDo) Or(conds ...gen.Condition) *xThirdSportDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdSportDo) Select(conds ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdSportDo) Where(conds ...gen.Condition) *xThirdSportDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdSportDo) Order(conds ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdSportDo) Distinct(cols ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdSportDo) Omit(cols ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdSportDo) Join(table schema.Tabler, on ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdSportDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdSportDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdSportDo) Group(cols ...field.Expr) *xThirdSportDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdSportDo) Having(conds ...gen.Condition) *xThirdSportDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdSportDo) Limit(limit int) *xThirdSportDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdSportDo) Offset(offset int) *xThirdSportDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdSportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdSportDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdSportDo) Unscoped() *xThirdSportDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdSportDo) Create(values ...*model.XThirdSport) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdSportDo) CreateInBatches(values []*model.XThirdSport, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdSportDo) Save(values ...*model.XThirdSport) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdSportDo) First() (*model.XThirdSport, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdSport), nil
	}
}

func (x xThirdSportDo) Take() (*model.XThirdSport, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdSport), nil
	}
}

func (x xThirdSportDo) Last() (*model.XThirdSport, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdSport), nil
	}
}

func (x xThirdSportDo) Find() ([]*model.XThirdSport, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdSport), err
}

func (x xThirdSportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdSport, err error) {
	buf := make([]*model.XThirdSport, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdSportDo) FindInBatches(result *[]*model.XThirdSport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdSportDo) Attrs(attrs ...field.AssignExpr) *xThirdSportDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdSportDo) Assign(attrs ...field.AssignExpr) *xThirdSportDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdSportDo) Joins(fields ...field.RelationField) *xThirdSportDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdSportDo) Preload(fields ...field.RelationField) *xThirdSportDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdSportDo) FirstOrInit() (*model.XThirdSport, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdSport), nil
	}
}

func (x xThirdSportDo) FirstOrCreate() (*model.XThirdSport, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdSport), nil
	}
}

func (x xThirdSportDo) FindByPage(offset int, limit int) (result []*model.XThirdSport, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdSportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdSportDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdSportDo) Delete(models ...*model.XThirdSport) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdSportDo) withDO(do gen.Dao) *xThirdSportDo {
	x.DO = *do.(*gen.DO)
	return x
}
