// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXThirdQuwei = "x_third_quwei"

// XThirdQuwei mapped from table <x_third_quwei>
type XThirdQuwei struct {
	ID             int64     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:自增Id" json:"Id"`  // 自增Id
	SellerID       int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                     // 运营商
	ChannelID      int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                    // 渠道
	BetChannelID   int32     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"`          // 下注渠道Id
	UserID         int32     `gorm:"column:UserId;comment:玩家id" json:"UserId"`                        // 玩家id
	Brand          string    `gorm:"column:Brand;comment:第三方品牌" json:"Brand"`                         // 第三方品牌
	ThirdID        string    `gorm:"column:ThirdId;comment:第三方订单号" json:"ThirdId"`                    // 第三方订单号
	ThirdRefID     string    `gorm:"column:ThirdRefId;comment:三方备用注单号" json:"ThirdRefId"`             // 三方备用注单号
	GameID         string    `gorm:"column:GameId;comment:游戏ID" json:"GameId"`                        // 游戏ID
	GameName       string    `gorm:"column:GameName;comment:游戏名称" json:"GameName"`                    // 游戏名称
	BetAmount      float64   `gorm:"column:BetAmount;default:0.000000;comment:下注金额" json:"BetAmount"` // 下注金额
	WinAmount      float64   `gorm:"column:WinAmount;default:0.000000;comment:派奖金额" json:"WinAmount"` // 派奖金额
	ValidBet       float64   `gorm:"column:ValidBet;default:0.000000;comment:有效投注" json:"ValidBet"`   // 有效投注
	FirstLiuSui    float64   `gorm:"column:FirstLiuSui;comment:扣减前有效投注" json:"FirstLiuSui"`           // 扣减前有效投注
	ValidBetAmount float64   `gorm:"column:ValidBetAmount;comment:有效投注" json:"ValidBetAmount"`        // 有效投注
	ThirdTime      time.Time `gorm:"column:ThirdTime;comment:第三方游戏时间" json:"ThirdTime"`               // 第三方游戏时间
	Currency       string    `gorm:"column:Currency;comment:币种" json:"Currency"`                      // 币种
	RawData        string    `gorm:"column:RawData;comment:第三方原始数据" json:"RawData"`                   // 第三方原始数据
	State          int32     `gorm:"column:State;default:1" json:"State"`
	Fee            float64   `gorm:"column:Fee;default:0.000000" json:"Fee"`
	DataState      int32     `gorm:"column:DataState;default:1;comment:数据状态" json:"DataState"`                   // 数据状态
	CreateTime     time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:同步时间" json:"CreateTime"` // 同步时间
	CSGroup        string    `gorm:"column:CSGroup" json:"CSGroup"`
	CSID           string    `gorm:"column:CSId" json:"CSId"`
	TopAgentID     int32     `gorm:"column:TopAgentId" json:"TopAgentId"`
	SpecialAgent   int32     `gorm:"column:SpecialAgent" json:"SpecialAgent"`
	BetCtx         string    `gorm:"column:BetCtx;comment:下注内容" json:"BetCtx"`                                      // 下注内容
	GameRst        string    `gorm:"column:GameRst;comment:开奖结果" json:"GameRst"`                                    // 开奖结果
	BetCtxType     int32     `gorm:"column:BetCtxType;default:1;comment:1-默认类型 2-链接类型 3-json" json:"BetCtxType"`    // 1-默认类型 2-链接类型 3-json
	IP             string    `gorm:"column:Ip;comment:登录ip" json:"Ip"`                                              // 登录ip
	Lang           string    `gorm:"column:Lang;comment:登录语言" json:"Lang"`                                          // 登录语言
	BlackUserType  int32     `gorm:"column:BlackUserType;comment:用户类型 1用户 2代理 3渠道 4运营商" json:"BlackUserType"`       // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    int32     `gorm:"column:BlackUserId;comment:用户Id" json:"BlackUserId"`                            // 用户Id
	RefundAmount   float64   `gorm:"column:RefundAmount;default:0.000000;comment:反水金额updown需求" json:"RefundAmount"` // 反水金额updown需求
	BetType        int32     `gorm:"column:BetType;comment:下注类型：0=纯真金 1=混合下注 2=彩金" json:"BetType"`                  // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount float64   `gorm:"column:BonusBetAmount;default:0.00;comment:Bonus币下注金额" json:"BonusBetAmount"`   // Bonus币下注金额
	BonusWinAmount float64   `gorm:"column:BonusWinAmount;default:0.00;comment:Bonus币派彩金额" json:"BonusWinAmount"`   // Bonus币派彩金额
}

// TableName XThirdQuwei's table name
func (*XThirdQuwei) TableName() string {
	return TableNameXThirdQuwei
}
