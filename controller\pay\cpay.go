package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

// cpay 是cpay支付服务的实例
var Cpay = new(cpay)

type cpay struct {
	Base
}

// token缓存
var cpayToken string
var cpayTokenExpire int64
var cpayTokenLock sync.Mutex

// Init 初始化cpay支付，注册回调路由
func (c *cpay) Init() {
	server.Http().PostNoAuth("/api/cpay/recharge_callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/cpay/withdraw_callback", c.withdrawCallback)
}

// 获取token（带缓存）
func (c *cpay) getToken(cfg map[string]interface{}) (string, error) {
	cpayTokenLock.Lock()
	defer cpayTokenLock.Unlock()
	if cpayToken != "" && time.Now().Unix() < cpayTokenExpire {
		return cpayToken, nil
	}
	url, ok := cfg["token_url"].(string)
	if !ok || url == "" {
		return "", errors.New("token_url配置错误")
	}
	clientID, ok := cfg["client_id"].(string)
	if !ok || clientID == "" {
		return "", errors.New("client_id配置错误")
	}
	clientSecret, ok := cfg["client_secret"].(string)
	if !ok || clientSecret == "" {
		return "", errors.New("client_secret配置错误")
	}
	body := map[string]string{
		"client_id":     clientID,
		"client_secret": clientSecret,
	}
	bytes, _ := json.Marshal(body)
	resp, err := req.Post(url, req.Header{"Content-Type": "application/json"}, bytes)
	if err != nil {
		return "", err
	}
	defer resp.Response().Body.Close()
	respBody, _ := io.ReadAll(resp.Response().Body)
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", err
	}
	token, ok := result["access_token"].(string)
	if !ok || token == "" {
		return "", errors.New("token获取失败")
	}
	cpayToken = token
	cpayTokenExpire = time.Now().Unix() + 3600
	return token, nil
}

// Recharge 充值接口
func (c *cpay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethodData.String("Symbol")); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)
	token, err := c.getToken(cfg)
	if err != nil {
		ctx.RespErr(errors.New("cpay token获取失败"), &errcode)
		return
	}
	userToken := server.GetToken(ctx)
	user, err := c.getUser(userToken.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	// 汇率
	rate, err := c.getRechargeRate(req.Symbol, user.SellerID)
	if err != nil {
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}
	c.createRechargeOrder(ctx, cfg, rate, user, specialAgent, userToken, req, payMethodData, token)
}

// createRechargeOrder 创建充值订单并请求cpay
func (c *cpay) createRechargeOrder(ctx *abugo.AbuHttpContent, cfg map[string]interface{}, rate float64, user *model.XUser, specialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap, accessToken string) {
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		errcode := 0
		var amount float64
		currentRate := rate
		if user.SellerID == 26 {
			currentRate = 0
			amount = reqdata.Amount
		} else {
			amount = reqdata.Amount / rate
		}
		payType := paymethod.Int("PayType")
		if paymethod.String("Brand") == "cpay" && paymethod.String("Name") == "cpay" {
			payType = 26
		}
		OrderId, err := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":     user.SellerID,
			"ChannelId":    user.ChannelID,
			"UserId":       user.UserID,
			"Symbol":       reqdata.Symbol,
			"PayId":        reqdata.MethodId,
			"PayType":      payType,
			"Amount":       reqdata.Amount,
			"RealAmount":   amount,
			"TransferRate": currentRate,
			"State":        3,
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": specialAgent,
			"TopAgentId":   user.TopAgentID,
			"OrderType":    reqdata.OrderType,
		})
		if err != nil {
			ctx.RespErr(errors.New("创建订单失败"), &errcode)
			return err
		}

		// 构建billingInfo
		var billingInfo map[string]interface{} = nil
		if reqdata.Address != "" && reqdata.City != "" && reqdata.State != "" && reqdata.PostalCode != "" && reqdata.Country != "" {
			billingInfo = map[string]interface{}{
				"address":    reqdata.Address,
				"city":       reqdata.City,
				"state":      reqdata.State,
				"postalCode": reqdata.PostalCode,
				"country":    reqdata.Country,
			}
		}

		if !utils.VerifyMAIl(reqdata.Email) {
			ctx.RespErr(errors.New("邮箱格式不正确"), &errcode)
			return errors.New("邮箱格式不正确")
		}
		createUrl, ok := cfg["create_url"].(string)
		if !ok || createUrl == "" {
			ctx.RespErr(errors.New("create_url配置错误"), &errcode)
			return errors.New("create_url配置错误")
		}
		returnUrl, _ := cfg["return_url"].(string)
		cancelUrl, _ := cfg["cancel_url"].(string)
		notifyUrl, _ := cfg["notify_url"].(string)
		param := map[string]interface{}{
			"amount":              reqdata.Amount,
			"currency":            strings.ToUpper(reqdata.Symbol),
			"description":         "cPay",
			"customerEmail":       reqdata.Email,
			"customerName":        reqdata.RealName,
			"customerId":          fmt.Sprint(user.UserID),
			"returnUrl":           returnUrl,
			"cancelUrl":           cancelUrl,
			"notificationHookUrl": notifyUrl,
			"paymentPageTheme":    "dark", // 添加支付页面主题
			"metadata": map[string]interface{}{
				"user":    fmt.Sprint(user.UserID),
				"orderId": fmt.Sprint(OrderId),
			},
		}
		// providerCode
		if providerCode, hasProviderCode := cfg["provider_code"].(string); hasProviderCode && providerCode != "" {
			param["providerCode"] = providerCode
		}

		if billingInfo != nil {
			param["billingInfo"] = billingInfo
		}
		bytes, _ := json.Marshal(param)
		logs.Info("cpay下单请求参数: %s", string(bytes))
		headers := req.Header{
			"Content-Type":  "application/json",
			"Authorization": "Bearer " + accessToken,
		}
		resp, err := req.Post(createUrl, headers, bytes)
		if err != nil {
			logs.Error("cpay下单请求失败: %v", err)
			ctx.RespErr(errors.New("cpay下单失败"), &errcode)
			return err
		}
		defer resp.Response().Body.Close()
		respBody, _ := io.ReadAll(resp.Response().Body)
		logs.Info("cpay下单返回内容: %s", string(respBody))

		// 记录响应状态码
		logs.Info("cpay下单响应状态码: %d", resp.Response().StatusCode)

		var result map[string]interface{}
		if err := json.Unmarshal(respBody, &result); err != nil {
			logs.Error("cpay响应解析失败: %v, body: %s", err, string(respBody))
			ctx.RespErr(errors.New("cpay响应解析失败"), &errcode)
			return err
		}

		// 检查HTTP状态码
		if resp.Response().StatusCode != 200 {
			logs.Error("cpay下单HTTP状态码异常: %d, body: %s", resp.Response().StatusCode, string(respBody))
			ctx.RespErr(fmt.Errorf("cpay下单失败，状态码: %d", resp.Response().StatusCode), &errcode)
			return fmt.Errorf("cpay下单失败，状态码: %d", resp.Response().StatusCode)
		}

		if status, ok := result["status"].(string); ok && status == "ERROR" {
			msg := "cpay下单失败"
			if m, ok := result["message"].(string); ok {
				msg = m
			}
			traceId := ""
			if t, ok := result["traceId"].(string); ok {
				traceId = t
			}
			logs.Error("cpay下单业务失败: %s, traceId: %s", msg, traceId)
			ctx.RespErr(fmt.Errorf("%s (traceId: %s)", msg, traceId), &errcode)
			return fmt.Errorf("%s (traceId: %s)", msg, traceId)
		}
		transactionId, _ := result["transactionId"]
		// 优先使用重定向链接，iframe链接可能有跨域问题
		payUrl, _ := result["paymentUrlRedirect"].(string)
		if payUrl == "" {
			payUrl, _ = result["paymentUrlIframe"].(string)
		}
		if payUrl == "" {
			// 尝试其他可能的字段名
			payUrl, _ = result["paymentUrl"].(string)
		}

		logs.Info("cpay支付链接获取: transactionId=%v, paymentUrlRedirect=%v, paymentUrlIframe=%v, finalPayUrl=%v",
			transactionId, result["paymentUrlRedirect"], result["paymentUrlIframe"], payUrl)

		if payUrl == "" {
			ctx.RespErr(errors.New("cpay支付链接获取失败"), &errcode)
			return errors.New("cpay支付链接获取失败")
		}
		payData := map[string]interface{}{
			"transactionId":  transactionId,
			"payUrl":         payUrl,
			"Brand":          paymethod.String("Brand"),
			"RequiredAmount": reqdata.Amount,
		}
		payDataJson, _ := json.Marshal(payData)
		tx.Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
			"PayData": string(payDataJson),
			"ThirdId": transactionId,
		})
		ctx.RespOK(xgo.H{
			"payurl":  payUrl,
			"orderId": OrderId,
		})
		return nil
	})
}

// rechargeCallback 处理cpay充值回调
func (c *cpay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 读取回调数据
	body, _ := io.ReadAll(ctx.Gin().Request.Body)

	// 输出原始回调数据
	logs.Info("cpay回调原始数据: %s", string(body))

	// 解析JSON
	var notify map[string]interface{}
	if err := json.Unmarshal(body, &notify); err != nil {
		logs.Error("cpay回调JSON解析失败: %v", err)
		ctx.Gin().String(500, "fail")
		return
	}

	// 输出解析后的回调数据
	notifyJson, _ := json.Marshal(notify)
	logs.Info("cpay回调解析后数据: %s", string(notifyJson))

	// 1. 状态判断: 只在 COMPLETED 状态时处理充值成功逻辑
	status, ok := notify["status"].(string)
	if !ok {
		logs.Error("cpay回调无status字段")
		ctx.Gin().String(500, "fail")
		return
	}

	// 如果不是COMPLETED状态，记录日志并返回成功（避免重复回调）
	if status != "COMPLETED" {
		logs.Info("cpay回调状态非COMPLETED，跳过处理: status=%s", status)
		ctx.Gin().String(200, "success")
		return
	}

	// 2. 获取关键字段
	transactionId, ok := notify["transactionId"].(string)
	if !ok || transactionId == "" {
		logs.Error("cpay回调无transactionId")
		ctx.Gin().String(500, "fail")
		return
	}

	amount, ok := notify["amount"].(float64)
	if !ok {
		logs.Error("cpay回调无amount字段或格式错误")
		ctx.Gin().String(500, "fail")
		return
	}

	// 3. 获取metadata中的用户ID
	metadata, ok := notify["metadata"].(map[string]interface{})
	if !ok {
		logs.Error("cpay回调无metadata字段或格式错误")
		ctx.Gin().String(500, "fail")
		return
	}

	userIDStr, ok := metadata["user"].(string)
	if !ok || userIDStr == "" {
		logs.Error("cpay回调metadata中无user字段")
		ctx.Gin().String(500, "fail")
		return
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		logs.Error("cpay回调metadata中user字段格式错误: %v", err)
		ctx.Gin().String(500, "fail")
		return
	}

	logs.Info("cpay回调处理 - transactionId: %s, amount: %f, userID: %d", transactionId, amount, userID)

	// 4. 重复处理防护: 通过交易ID查询订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", transactionId, "")
	where.Add("and", "PayType", "=", 26, "") // cpay的PayType是26
	orderData, err := server.Db().Table("x_recharge").Where(where).GetOne()
	if err != nil {
		logs.Error("cpay回调查询订单失败: %v", err)
		ctx.Gin().String(500, "fail")
		return
	}

	if orderData == nil {
		logs.Error("cpay回调订单不存在: transactionId=%s", transactionId)
		ctx.Gin().String(500, "fail")
		return
	}

	// 检查订单状态，避免重复处理
	if xgo.ToInt((*orderData)["State"]) == 5 {
		logs.Info("cpay回调订单已处理成功: transactionId=%s", transactionId)
		ctx.Gin().String(200, "success")
		return
	}

	// 5. 金额验证: 验证回调金额与订单金额是否一致
	orderAmount := xgo.ToFloat((*orderData)["Amount"])
	if math.Abs(orderAmount-amount) > 0.01 {
		logs.Error("cpay回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, amount)
		ctx.Gin().String(500, "fail")
		return
	}

	// 6. 验证用户ID是否匹配
	orderUserID := int(xgo.ToInt((*orderData)["UserId"]))
	if orderUserID != userID {
		logs.Error("cpay回调用户ID不匹配: 订单用户ID=%d, 回调用户ID=%d", orderUserID, userID)
		ctx.Gin().String(500, "fail")
		return
	}

	// 7. 业务逻辑: 处理充值成功
	orderID := int(xgo.ToInt((*orderData)["Id"]))
	c.rechargeCallbackHandel(userID, orderID, 5)

	logs.Info("cpay回调处理完成，充值成功: transactionId=%s, orderID=%d, userID=%d", transactionId, orderID, userID)
	ctx.Gin().String(200, "success")
}

// withdrawCallback 处理cpay提现回调
func (c *cpay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	var notify map[string]interface{}
	if err := json.Unmarshal(body, &notify); err != nil {
		logs.Error("cpay提现回调解析失败", err)
		ctx.Gin().String(500, "fail")
		return
	}

	// 兼容 withdrawal 嵌套结构和扁平结构
	var withdrawal map[string]interface{}
	if w, ok := notify["withdrawal"].(map[string]interface{}); ok {
		withdrawal = w
	} else {
		withdrawal = notify
	}

	transactionId, ok := withdrawal["transactionId"].(string)
	if !ok || transactionId == "" {
		logs.Error("cpay提现回调无transactionId")
		ctx.Gin().String(500, "fail")
		return
	}

	status, ok := withdrawal["status"].(string)
	if !ok {
		logs.Error("cpay提现回调无status")
		ctx.Gin().String(500, "fail")
		return
	}

	// 查询提现订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", transactionId, "")
	where.Add("and", "PayType", "=", 26, "") // cpay的PayType是26
	withdrawData, err := server.Db().Table("x_withdraw").Where(where).GetOne()
	if err != nil || withdrawData == nil {
		logs.Error("cpay提现回调订单不存在", transactionId)
		ctx.Gin().String(500, "fail")
		return
	}

	// 金额验证: 验证回调金额与订单金额是否一致
	if amount, ok := withdrawal["amount"].(float64); ok {
		orderAmount := xgo.ToFloat((*withdrawData)["RealAmount"])
		if math.Abs(orderAmount-amount) > 0.01 {
			logs.Error("cpay提现回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, amount)
			ctx.Gin().String(500, "fail")
			return
		}
		logs.Info("cpay提现回调金额校验通过: 订单金额=%f, 回调金额=%f", orderAmount, amount)
	}

	// 处理不同状态（严格按Cpay官方状态枚举）
	switch status {
	case "PAYOUT_COMPLETED":
		// 提现成功
		c.withdrawCallbackHandel(int(xgo.ToInt((*withdrawData)["Id"])), 6)
		logs.Info("cpay提现成功: transactionId=%v, orderId=%v", transactionId, (*withdrawData)["Id"])
	case "PAYOUT_FAILED", "PAYOUT_DECLINED", "PAYOUT_CANCELLED":
		// 提现失败
		c.withdrawCallbackHandel(int(xgo.ToInt((*withdrawData)["Id"])), 7)
		logs.Info("cpay提现失败: transactionId=%v, orderId=%v, 状态=%v", transactionId, (*withdrawData)["Id"], status)
	case "WITHDRAWAL_INITIATED", "AWAITING_PAYOUT_PROVIDER_SELECTION", "AWAITING_PAYOUT_DETAILS", "AWAITING_PAYOUT_CONFIRMATION", "PAYOUT_PROCESSING":
		// 处理中
		c.withdrawCallbackHandel(int(xgo.ToInt((*withdrawData)["Id"])), 5)
		logs.Info("cpay提现处理中: transactionId=%v, orderId=%v, 状态=%v", transactionId, (*withdrawData)["Id"], status)
	default:
		logs.Info("cpay提现未知状态: transactionId=%v, orderId=%v, 状态=%v", transactionId, (*withdrawData)["Id"], status)
	}

	ctx.Gin().String(200, "success")
}
