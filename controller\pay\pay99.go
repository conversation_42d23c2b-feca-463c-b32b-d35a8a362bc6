package paycontroller

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/dgrijalva/jwt-go"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

// pay99实例
var Pay99 = new(pay99)

type pay99 struct {
	Base
}

// Init 初始化pay99支付，注册回调路由
func (c *pay99) Init() {
	server.Http().PostNoAuth("/api/pay/pay99/recharge_callback", c.RechargeCallback)
	server.Http().PostNoAuth("/api/pay/pay99/withdraw_callback", c.WithdrawCallback)
}

// JWT生成
func (c *pay99) generateJWT(cfg map[string]interface{}) (string, error) {
	sub := ""
	if v, ok := cfg["merchant"].(string); ok && v != "" {
		sub = v
	} else if v, ok := cfg["id"].(string); ok && v != "" {
		sub = v
	}
	if sub == "" {
		logs.Error("pay99 商号配置错误: merchant和id都为空")
		return "", errors.New("商号配置错误")
	}
	logs.Debug("pay99 JWT Subject: %v", sub)
	secretBase64 := ""
	if v, ok := cfg["secret"].(string); ok && v != "" {
		secretBase64 = v
	} else if v, ok := cfg["key"].(string); ok && v != "" {
		secretBase64 = v
	}
	if secretBase64 == "" {
		logs.Error("pay99 密钥配置错误: secret和key都为空")
		return "", errors.New("密钥配置错误")
	}
	logs.Debug("pay99 Secret Base64长度: %v", len(secretBase64))
	secret, err := base64.StdEncoding.DecodeString(secretBase64)
	if err != nil {
		return "", errors.New("密钥Base64解码失败")
	}
	now := time.Now().Unix()
	claims := jwt.StandardClaims{
		Subject:   sub,
		IssuedAt:  now,
		ExpiresAt: now + 60,
	}
	logs.Debug("pay99 JWT claims: Subject=%v, IssuedAt=%v, ExpiresAt=%v", sub, now, now+60)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString(secret)
	if err != nil {
		logs.Error("pay99 JWT签名失败: %v", err)
		return "", err
	}
	logs.Debug("pay99 JWT生成完成, 长度: %v", len(signedToken))
	return signedToken, nil
}

// 充值接口
func (c *pay99) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0
	logs.Info("pay99 进入Recharge, MethodId=%v, Symbol=%v, Amount=%v", req.MethodId, req.Symbol, req.Amount)
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		logs.Error("pay99 未找到支付方式, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	logs.Info("pay99 获取支付方式成功, MethodId=%v", req.MethodId)
	if err := c.ValidateSymbol(req.Symbol, payMethodData.String("Symbol")); err != nil {
		logs.Error("pay99 币种校验失败, Symbol=%v", req.Symbol)
		ctx.RespErr(err, &errcode)
		return
	}
	logs.Info("pay99 币种校验通过")
	var cfg map[string]interface{}
	err = json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)
	if err != nil {
		logs.Error("pay99 ExtraConfig解析失败, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("ExtraConfig解析失败"), &errcode)
		return
	}
	logs.Info("pay99 ExtraConfig解析成功")
	// 商户号脱敏处理
	merchantStr := ""
	if merchant, ok := cfg["merchant"].(string); ok && len(merchant) > 6 {
		merchantStr = merchant[:3] + "***" + merchant[len(merchant)-3:]
	} else {
		merchantStr = "***"
	}
	logs.Debug("pay99 配置内容: url=%v, merchant=%v, depositProcedureId=%v", cfg["url"], merchantStr, cfg["depositProcedureId"])
	jwtToken, err := c.generateJWT(cfg)
	if err != nil {
		logs.Error("pay99 JWT生成失败, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("JWT生成失败"), &errcode)
		return
	}
	logs.Info("pay99 JWT生成成功")
	// JWT token脱敏处理，只显示前6位和后6位
	maskedToken := ""
	if len(jwtToken) > 12 {
		maskedToken = jwtToken[:6] + "***" + jwtToken[len(jwtToken)-6:]
	} else {
		maskedToken = "***"
	}
	logs.Debug("pay99 JWT Token: %v", maskedToken)
	userToken := server.GetToken(ctx)
	user, err := c.getUser(userToken.UserId)
	if err != nil {
		logs.Error("pay99 未找到用户, UserId=%v", userToken.UserId)
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	logs.Info("pay99 获取用户成功, UserId=%v", userToken.UserId)
	rate, err := c.getRechargeRate(req.Symbol, user.SellerID)
	if err != nil {
		logs.Error("pay99 获取汇率失败, Symbol=%v", req.Symbol)
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}
	logs.Info("pay99 获取汇率成功")
	c.createRechargeOrder(ctx, cfg, rate, user, specialAgent, userToken, req, payMethodData, jwtToken)
	logs.Info("pay99 createRechargeOrder已调用")
}

func (c *pay99) createRechargeOrder(ctx *abugo.AbuHttpContent, cfg map[string]interface{}, rate float64, user *model.XUser, specialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap, jwtToken string) {
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		errcode := 0
		amount := reqdata.Amount / rate

		// 构建 PayData
		payData := map[string]interface{}{
			"PayId":            reqdata.MethodId,
			"Brand":            paymethod.String("Brand"),
			"Name":             paymethod.String("Name"),
			"RealName":         reqdata.RealName,
			"IsRechargeActive": reqdata.IsRechargeActive,
			"Account":          user.Account,
		}
		payDataBytes, _ := json.Marshal(payData)

		OrderId, err := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":     user.SellerID,
			"ChannelId":    user.ChannelID,
			"UserId":       user.UserID,
			"Symbol":       reqdata.Symbol,
			"PayId":        reqdata.MethodId,
			"PayType":      27, // 硬编码pay99的PayType=27
			"Amount":       reqdata.Amount,
			"RealAmount":   amount,
			"TransferRate": rate,
			"State":        3,
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": specialAgent,
			"TopAgentId":   user.TopAgentID,
			"OrderType":    reqdata.OrderType,
			"PayData":      string(payDataBytes),
		})
		if err != nil {
			ctx.RespErr(errors.New("创建订单失败"), &errcode)
			return err
		}

		url, ok := cfg["url"].(string)
		if !ok || url == "" {
			ctx.RespErr(errors.New("url配置错误"), &errcode)
			return errors.New("url配置错误")
		}

		depositProcedureId, _ := cfg["depositProcedureId"].(float64) // 配置中获取充值流程ID
		param := map[string]interface{}{
			"depositProcedureId": int(depositProcedureId),
			"username":           user.Account,
			"payer":              reqdata.RealName,
			"amount":             int(reqdata.Amount),
			"sn":                 fmt.Sprintf("%v", OrderId),
		}
		bytes, _ := json.Marshal(param)
		logs.Debug("pay99 recharge param: %v", string(bytes))
		logs.Debug("pay99 request URL: %v", url+"/core.finance.tenant.v1/credits")
		// JWT token脱敏处理
		maskedRequestToken := ""
		if len(jwtToken) > 12 {
			maskedRequestToken = jwtToken[:6] + "***" + jwtToken[len(jwtToken)-6:]
		} else {
			maskedRequestToken = "***"
		}
		logs.Debug("pay99 JWT token for request: %v", maskedRequestToken)
		headers := req.Header{
			"Authorization": "Bearer " + jwtToken,
			"Content-Type":  "application/json; charset=utf-8",
		}
		resp, err := req.Post(url+"/core.finance.tenant.v1/credits", headers, bytes)
		if err != nil {
			logs.Error("pay99 HTTP请求失败: %v, URL: %v", err, url+"/core.finance.tenant.v1/credits")
			ctx.RespErr(errors.New("请求pay99失败"), &errcode)
			return err // 事务回滚
		}
		defer resp.Response().Body.Close()
		body, _ := io.ReadAll(resp.Response().Body)
		logs.Debug("pay99 recharge resp:", string(body))
		logs.Debug("pay99 HTTP状态码: %v", resp.Response().StatusCode)
		var result map[string]interface{}
		if err := json.Unmarshal(body, &result); err != nil {
			logs.Error("pay99响应解析失败: %v, 原始响应: %v", err, string(body))
			ctx.RespErr(errors.New("pay99响应解析失败"), &errcode)
			return err // 事务回滚
		}
		if resp.Response().StatusCode != 201 {
			logs.Error("pay99充值失败: 状态码=%v, 响应=%v", resp.Response().StatusCode, string(body))
			ctx.RespErr(errors.New(fmt.Sprintf("pay99充值失败: %v", result["message"])), &errcode)
			return errors.New(fmt.Sprintf("pay99充值失败: %v", result["message"])) // 事务回滚
		}

		// API成功后，更新订单数据
		// 新增：保存三方返回的id到x_recharge.ThirdId
		thirdId, ok := result["id"].(float64)
		if ok {
			tx.Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
				"ThirdId": int(thirdId),
			})
		}

		// 提取支付URL - 统一返回格式
		payUrl := ""
		if url, ok := result["payUrl"].(string); ok {
			payUrl = url
		} else if url, ok := result["payment_url"].(string); ok {
			payUrl = url
		} else if url, ok := result["redirect_url"].(string); ok {
			payUrl = url
		}

		// 统一返回格式，与cpay/pay24保持一致
		ctx.RespOK(xgo.H{
			"payurl":  payUrl,
			"orderId": OrderId,
		})
		return nil // 事务提交
	})
}

// 提现接口
func (c *pay99) Withdraw(ctx *abugo.AbuHttpContent, req *CreateOrderReq) {
	errcode := 0
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethodData.String("Symbol")); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)
	jwtToken, err := c.generateJWT(cfg)
	if err != nil {
		ctx.RespErr(errors.New("JWT生成失败"), &errcode)
		return
	}
	userToken := server.GetToken(ctx)
	user, err := c.getUser(userToken.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	rate, err := c.getWithdrawRate(req.Symbol, user.SellerID)
	if err != nil {
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}
	c.createWithdrawOrder(ctx, cfg, rate, user, userToken, req, payMethodData, jwtToken)
}

func (c *pay99) createWithdrawOrder(ctx *abugo.AbuHttpContent, cfg map[string]interface{}, rate float64, user *model.XUser, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap, jwtToken string) {
	errcode := 0

	// 参数校验
	if reqdata.RealName == "" {
		ctx.RespErr(errors.New("真实姓名不能为空"), &errcode)
		return
	}

	if reqdata.BankName == "" {
		ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
		return
	}

	if reqdata.BankNo == "" {
		ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
		return
	}

	amount := reqdata.Amount / rate

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":      reqdata.MethodId,
		"Brand":      paymethod.String("Brand"),
		"Name":       paymethod.String("Name"),
		"RealName":   reqdata.RealName,
		"BankName":   reqdata.BankName,
		"BankNo":     reqdata.BankNo,
		"BranchName": reqdata.BranchName,
		"City":       reqdata.City,
		"State":      reqdata.State,
		"Account":    user.Account,
	}
	payDataBytes, _ := json.Marshal(payData)

	OrderId, err := server.XDb().Table("x_withdraw").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      27, // 硬编码pay99的PayType=27
		"Amount":       reqdata.Amount,
		"RealAmount":   amount,
		"TransferRate": rate,
		"State":        3,
		"CSGroup":      user.CSGroup,
		"CSId":         user.CSID,
		"TopAgentId":   user.TopAgentID,
		"OrderType":    reqdata.OrderType,
		"PayData":      string(payDataBytes),
	})
	if err != nil {
		ctx.RespErr(errors.New("创建提现订单失败"), &errcode)
		return
	}

	// 更新 PayData，添加 sn 信息
	payData["sn"] = fmt.Sprintf("%v", OrderId)
	payDataBytes, _ = json.Marshal(payData)
	server.XDb().Table("x_withdraw").Where("Id = ?", OrderId).Update(xgo.H{
		"PayData": string(payDataBytes),
	})
	url, ok := cfg["url"].(string)
	if !ok || url == "" {
		ctx.RespErr(errors.New("url配置错误"), &errcode)
		return
	}
	// 根据提现类型构建参数
	var param map[string]interface{}
	if reqdata.BankName == "支付宝" {
		// 支付宝提现 - 使用固定值
		param = map[string]interface{}{
			"username": user.Account,
			"payer":    user.RealName,
			"amount":   int(reqdata.Amount),
			"bankName": "支付宝",  // 固定值
			"number":   "1234", // 固定值
			"branch":   "分行",   // 固定值
			"city":     "北京",   // 固定值
			"province": "北京",   // 固定值
			"sn":       fmt.Sprintf("%v", OrderId),
			"alipay": map[string]interface{}{
				"name":   user.RealName,
				"number": reqdata.BankNo, // 支付宝账号
			},
		}
	} else {
		// 银行卡提现
		param = map[string]interface{}{
			"username": user.Account,
			"payer":    user.RealName,
			"amount":   int(reqdata.Amount),
			"bankName": reqdata.BankName,
			"number":   reqdata.BankNo,
			"branch":   reqdata.BranchName,
			"city":     reqdata.City,
			"province": reqdata.State,
			"sn":       fmt.Sprintf("%v", OrderId),
		}

	}
	bytes, _ := json.Marshal(param)
	headers := req.Header{
		"Authorization": "Bearer " + jwtToken,
		"Content-Type":  "application/json; charset=utf-8",
	}
	resp, err := req.Post(url+"/core.finance.tenant.v1/debits", headers, bytes)
	if err != nil {
		ctx.RespErr(errors.New("请求pay99失败"), &errcode)
		return
	}
	defer resp.Response().Body.Close()
	body, _ := io.ReadAll(resp.Response().Body)
	logs.Debug("pay99 withdraw resp:", string(body))
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		ctx.RespErr(errors.New("pay99响应解析失败"), &errcode)
		return
	}
	if resp.Response().StatusCode != 201 {
		ctx.RespErr(errors.New(fmt.Sprintf("pay99提现失败: %v", result["message"])), &errcode)
		return
	}

	// 新增：保存三方返回的id到x_withdraw.ThirdId
	thirdId, ok := result["id"].(float64)
	if ok {
		server.XDb().Table("x_withdraw").Where("Id = ?", OrderId).Update(xgo.H{
			"ThirdId": int(thirdId),
		})
		logs.Info("pay99提现保存ThirdId成功: 订单ID=%v, ThirdId=%v", OrderId, int(thirdId))
	}

	// 统一返回格式，与其他支付方式保持一致
	ctx.RespOK(xgo.H{
		"orderId": OrderId,
		"status":  "success",
		"message": "提现订单创建成功",
	})
}

// pay99充值回调接口
func (c *pay99) RechargeCallback(ctx *abugo.AbuHttpContent) {
	// 1. 获取JWT
	auth := ctx.Gin().GetHeader("Authorization")
	tokenStr := ""
	if len(auth) > 7 && auth[:7] == "Bearer " {
		tokenStr = auth[7:]
	}
	if tokenStr == "" {
		ctx.RespJsonWithCode(401, "missing jwt", nil)
		return
	}

	// 2. 解析JWT拿到sn
	parser := &jwt.Parser{}
	claims := jwt.MapClaims{}
	_, _, err := parser.ParseUnverified(tokenStr, claims)
	if err != nil {
		ctx.RespJsonWithCode(400, "invalid jwt format", nil)
		return
	}
	sn, ok := claims["sn"].(string)
	if !ok || sn == "" {
		ctx.RespJsonWithCode(400, "jwt missing sn", nil)
		return
	}

	// 3. 查订单，获取支付方式配置
	var order *model.XRecharge
	order, err = c.getRechargeOrder(int(xgo.ToInt(sn)))
	if err != nil || order == nil {
		ctx.RespJsonWithCode(404, "order not found", nil)
		return
	}
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil || payMethod == nil {
		ctx.RespJsonWithCode(500, "pay config not found", nil)
		return
	}
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 4. 获取密钥
	secretBase64 := ""
	if v, ok := cfg["secret"].(string); ok && v != "" {
		secretBase64 = v
	} else if v, ok := cfg["key"].(string); ok && v != "" {
		secretBase64 = v
	}
	if secretBase64 == "" {
		ctx.RespJsonWithCode(500, "pay config missing secret", nil)
		return
	}
	secret, err := base64.StdEncoding.DecodeString(secretBase64)
	if err != nil {
		ctx.RespJsonWithCode(500, "pay config secret decode error", nil)
		return
	}

	// 5. 校验JWT
	jwtToken, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method")
		}
		return secret, nil
	})
	if err != nil || !jwtToken.Valid {
		ctx.RespJsonWithCode(401, "jwt verify fail", nil)
		return
	}
	payload, ok := jwtToken.Claims.(jwt.MapClaims)
	if !ok {
		ctx.RespJsonWithCode(400, "jwt claims error", nil)
		return
	}

	// 6. 提取参数
	id, _ := payload["id"].(float64)
	dealAmount, _ := payload["dealAmount"].(float64)
	status, _ := payload["status"].(float64)
	sn = payload["sn"].(string)
	logs.Info("pay99回调jwt: id=%v, dealAmount=%v, status=%v, sn=%v", id, dealAmount, status, sn)

	// 7. 金额验证: 验证回调金额与订单金额是否一致
	if dealAmount > 0 {
		orderAmount := order.Amount
		if math.Abs(orderAmount-dealAmount) > 0.01 {
			logs.Error("pay99充值回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, dealAmount)
			ctx.RespJsonWithCode(500, "amount mismatch", nil)
			return
		}
		logs.Info("pay99充值回调金额校验通过: 订单金额=%f, 回调金额=%f", orderAmount, dealAmount)
	}

	// 8. status映射
	result := 3 // 默认待支付/需重试
	switch int(status) {
	case 1, 6:
		result = 5 // 成功
	case 2, 3, 4, 7:
		result = 7 // 失败
	case 5:
		result = 3 // 需重试/待支付
	}

	// 9. 业务处理
	c.rechargeCallbackHandel(int(order.UserID), int(xgo.ToInt(sn)), result)
	ctx.RespJsonWithCode(200, "ok", nil)
}

// pay99提现回调接口
func (c *pay99) WithdrawCallback(ctx *abugo.AbuHttpContent) {
	// 1. 获取JWT
	auth := ctx.Gin().GetHeader("Authorization")
	tokenStr := ""
	if len(auth) > 7 && auth[:7] == "Bearer " {
		tokenStr = auth[7:]
	}
	if tokenStr == "" {
		ctx.RespJsonWithCode(401, "missing jwt", nil)
		return
	}

	// 2. 先ParseUnverified拿sn
	parser := &jwt.Parser{}
	claims := jwt.MapClaims{}
	_, _, err := parser.ParseUnverified(tokenStr, claims)
	if err != nil {
		ctx.RespJsonWithCode(400, "invalid jwt format", nil)
		return
	}
	sn, ok := claims["sn"].(string)
	if !ok || sn == "" {
		ctx.RespJsonWithCode(400, "jwt missing sn", nil)
		return
	}

	// 3. 查订单，获取支付方式配置
	var order *model.XWithdraw
	order, err = c.getWithdrawOrder(int(xgo.ToInt(sn)))
	if err != nil || order == nil {
		ctx.RespJsonWithCode(404, "order not found", nil)
		return
	}
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil || payMethod == nil {
		ctx.RespJsonWithCode(500, "pay config not found", nil)
		return
	}
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 4. 获取密钥
	secretBase64 := ""
	if v, ok := cfg["secret"].(string); ok && v != "" {
		secretBase64 = v
	} else if v, ok := cfg["key"].(string); ok && v != "" {
		secretBase64 = v
	}
	if secretBase64 == "" {
		ctx.RespJsonWithCode(500, "pay config missing secret", nil)
		return
	}
	secret, err := base64.StdEncoding.DecodeString(secretBase64)
	if err != nil {
		ctx.RespJsonWithCode(500, "pay config secret decode error", nil)
		return
	}

	// 5. 校验JWT
	jwtToken, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method")
		}
		return secret, nil
	})
	if err != nil || !jwtToken.Valid {
		ctx.RespJsonWithCode(401, "jwt verify fail", nil)
		return
	}
	payload, ok := jwtToken.Claims.(jwt.MapClaims)
	if !ok {
		ctx.RespJsonWithCode(400, "jwt claims error", nil)
		return
	}

	// 6. 提取参数
	id, _ := payload["id"].(float64)
	dealAmount, _ := payload["dealAmount"].(float64)
	status, _ := payload["status"].(float64)
	sn = payload["sn"].(string)
	logs.Info("pay99提现回调jwt: id=%v, dealAmount=%v, status=%v, sn=%v", id, dealAmount, status, sn)

	// 7. 金额验证: 验证回调金额与订单金额是否一致
	if dealAmount > 0 {
		orderAmount := order.RealAmount
		if math.Abs(orderAmount-dealAmount) > 0.01 {
			logs.Error("pay99提现回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, dealAmount)
			ctx.RespJsonWithCode(500, "amount mismatch", nil)
			return
		}
		logs.Info("pay99提现回调金额校验通过: 订单金额=%f, 回调金额=%f", orderAmount, dealAmount)
	}

	// 8. status映射 (根据pay99官方文档: status=3成功, status=4拒绝)
	result := 3
	switch int(status) {
	case 3:
		result = 6 // 成功 (提现成功)
	case 4:
		result = 7 // 失败 (拒绝)
	default:
		result = 3 // 其他状态需重试/待支付
	}

	// 9. 业务处理
	c.withdrawCallbackHandel(int(xgo.ToInt(sn)), result)
	ctx.RespJsonWithCode(200, "ok", nil)
}
