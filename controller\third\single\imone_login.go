package single

import (
	"errors"
	"fmt"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"encoding/json"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
)

// Login /api/v2/imone/user-login
func (l *IMOneSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
		//ProductWallet int                    `json:"ProductWallet"`                // 产品钱包
		IsMobile bool `json:"IsMobile"` // 是否手机端
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("IMOne_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	loginLang := reqdata.LangCode
	if loginLang == "" {
		loginLang = "ZH-CN" // 默认简体中文
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("IMOne_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyIMOne, userId); err != nil {
		logs.Error("IMOne_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, reqdata.GameId); err != nil {
		logs.Error("IMOne 登录游戏 权限检查错误 userId=", userId, " gameCode=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("IMOne 登录游戏 权限被拒绝 userId=", userId, " gameCode=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 查询游戏信息
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("IMOne_single 登录游戏 查询游戏错误 userId=", userId, " GameCode=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("IMOne_single 登录游戏 游戏不可用 userId=", userId, " GameCode=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	// 生成登录token
	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("IMOne_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 获取客户端IP
	clientIP := ctx.Gin().ClientIP()

	// IM体育
	//gameCode := "IMSB"
	//productWallet := 301

	// 电竞
	//gameCode := "ESportsbull"
	//productWallet := 401

	productWallet := 301
	if reqdata.GameId == "IMSB" {
		productWallet = 301
	} else if reqdata.GameId == "ESportsbull" {
		productWallet = 401
	}

	//// 设置产品钱包，如果请求中没有指定则使用默认值
	//productWallet := reqdata.ProductWallet
	//if productWallet == 0 {
	//	productWallet = 101 // 默认产品钱包
	//}

	// 获取或创建玩家并启动游戏
	gameURL, err := l.GetOrCreatePlayerAndLaunchGame(userId, reqdata.GameId, loginLang, clientIP, productWallet, reqdata.IsMobile, nil)
	if err != nil {
		logs.Error("IMOne 获取或创建用户并获取游戏URL错误: userId=%d, gameCode=%s, err=%v", userId, reqdata.GameId, err)

		// 根据错误类型返回不同的错误信息
		if gameURL != nil {
			switch gameURL.Code {
			case IMOne_Code_Player_Not_Exists:
				ctx.RespErrString(true, &errcode, "玩家不存在,请稍后再试")
				return
			case IMOne_Code_Invalid_Game_Code:
				ctx.RespErrString(true, &errcode, "游戏代码无效")
				return
			case IMOne_Code_Game_Not_Active:
				ctx.RespErrString(true, &errcode, "游戏暂时不可用")
				return
			case IMOne_Code_Game_Not_Activated:
				ctx.RespErrString(true, &errcode, "游戏未对该营运商开放")
				return
			case IMOne_Code_Player_Inactive:
				ctx.RespErrString(true, &errcode, "玩家账号已停用")
				return
			case IMOne_Code_Game_Not_Supported_Platform:
				ctx.RespErrString(true, &errcode, "此游戏目前不支援此平台")
				return
			default:
				ctx.RespErrString(true, &errcode, "启动游戏失败: "+gameURL.Message)
				return
			}
		}

		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	// 检查游戏URL是否获取成功
	if gameURL.Code != IMOne_Code_Success {
		logs.Error("IMOne 启动游戏失败: userId=%d, gameCode=%s, code=%s, message=%s", userId, reqdata.GameId, gameURL.Code, gameURL.Message)
		ctx.RespErrString(true, &errcode, "启动游戏失败: "+gameURL.Message)
		return
	}

	if gameURL.GameUrl == "" {
		logs.Error("IMOne 获取游戏URL为空: userId=%d, gameCode=%s", userId, reqdata.GameId)
		ctx.RespErrString(true, &errcode, "获取游戏URL失败,请稍后再试")
		return
	}

	logs.Info("IMOne 成功获取游戏URL: userId=%d, gameCode=%s, url=%s", userId, reqdata.GameId, gameURL.GameUrl, " sessionToken=", gameURL.SessionToken)

	// 解析游戏URL中的token参数，建立与我们sessionToken的映射
	l.createGameTokenMapping(gameURL.GameUrl, gameURL.SessionToken, userId, reqdata.GameId)

	ctx.RespOK(gameURL.GameUrl)
	return
}

// 生成IMOne token
func (l *IMOneSingleService) getTokenByUser(userId int, account string) (token string) {
	type IMOneTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyIMOne + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyIMOne + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("IMOne_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("IMOne_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "IMOne_" + uuid.NewString()
	tokendata := IMOneTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyIMOne + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("IMOne_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("IMOne_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *IMOneSingleService) getUserByToken(token string) (userId int, account string, err error) {
	type IMOneTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	rKeyToken := cacheKeyIMOne + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendataStr := string(v.([]byte))
		var tokendata IMOneTokenData
		if err = json.Unmarshal([]byte(tokendataStr), &tokendata); err != nil {
			logs.Error("IMOne_single getUserByToken json.Unmarshal err=", err.Error())
			return
		}
		userId = tokendata.UserId
		account = tokendata.Account
		return
	}
	err = errors.New("token不存在或已过期")
	return
}
