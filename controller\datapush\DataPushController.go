package datapush

import (
	"encoding/json"
	"fmt"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// DataPushController ThinkingData数据推送控制器
type DataPushController struct{}

// Init 初始化路由
func (c *DataPushController) Init() {
	// 注册充值成功事件上报接口
	server.Http().PostNoAuth("/api/datapush/send_deposit_success_event", c.SendDepositSuccessEventAPI)

	// 注册提款成功事件上报接口
	server.Http().PostNoAuth("/api/datapush/send_withdraw_success_event", c.SendWithdrawSuccessEventAPI)

	// 注册提款失败事件上报接口
	server.Http().PostNoAuth("/api/datapush/send_withdraw_fail_event", c.SendWithdrawFailEventAPI)

	// 派奖事件上报接口
	server.Http().PostNoAuth("/api/datapush/send_reward_give_event", c.SendRewardGiveEventAPI)
}

// SendDepositSuccessEventAPI 发送充值成功事件到ThinkingData的HTTP接口
// @description 提供给Node.js等外部系统调用的充值成功事件上报接口
// @param ctx HTTP请求上下文
// 请求参数:
//   - order_id: 充值订单ID (必填) - 数字ID
//
// 返回结果:
//   - success: 是否成功
//   - message: 处理结果消息
func (c *DataPushController) SendDepositSuccessEventAPI(ctx *abugo.AbuHttpContent) {
	defer recover()
	logs.Info("SendDepositSuccessEventAPI 调用充值成功事件上报接口")

	// 定义请求数据结构
	type RequestData struct {
		Id int32 `validate:"required" json:"id"` // 充值订单ID，必填
	}

	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询充值订单信息 - 按ID查询
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(ctx.Gin())

	order, err := rechargeDb.Where(rechargeTb.ID.Eq(reqdata.Id)).First()
	if err != nil {
		logs.Error("SendDepositSuccessEventAPI 查询充值订单失败: orderID=%d, err=%v", reqdata.Id, err)
		ctx.RespErrString(true, &errcode, "充值订单不存在")
		return
	}

	// 调用充值成功事件发送函数（自动处理支付方式）
	SendDepositSuccessEventAuto(order)

	// 确定支付方式显示名称
	var payMethodName string
	if order.PayID > 0 {
		payMethodName = "法币支付"
	} else {
		payMethodName = "虚拟币支付"
	}

	// 构造返回结果
	result := map[string]interface{}{
		"success": true,
		"message": "充值成功事件上报完成",
		"data": map[string]interface{}{
			"order_id":    order.OrderID,
			"user_id":     order.UserID,
			"amount":      order.Amount,
			"real_amount": order.RealAmount,
			"pay_method":  payMethodName,
			"symbol":      order.Symbol,
		},
	}

	logs.Info("SendDepositSuccessEventAPI 充值成功事件上报完成: orderID=%s, userID=%d, amount=%.2f",
		order.OrderID, order.UserID, order.Amount)

	// 返回结果
	ctx.RespOK(result)
}

// SendWithdrawSuccessEventAPI 发送提款成功事件到ThinkingData的HTTP接口
// @description 提供给外部系统调用的提款成功事件上报接口
// @param ctx HTTP请求上下文
// 请求参数:
//   - id: 提款订单ID (必填) - 数字ID
//
// 返回结果:
//   - success: 是否成功
//   - message: 处理结果消息
func (c *DataPushController) SendWithdrawSuccessEventAPI(ctx *abugo.AbuHttpContent) {
	defer recover()
	logs.Info("SendWithdrawSuccessEventAPI 调用提款成功事件上报接口")

	// 定义请求数据结构
	type RequestData struct {
		Id int32 `validate:"required" json:"id"` // 提款订单ID，必填
	}

	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询提款订单信息 - 按ID查询
	withdrawTb := server.DaoxHashGame().XWithdraw
	withdrawDb := server.DaoxHashGame().XWithdraw.WithContext(ctx.Gin())

	order, err := withdrawDb.Where(withdrawTb.ID.Eq(reqdata.Id)).First()
	if err != nil {
		logs.Error("SendWithdrawSuccessEventAPI 查询提款订单失败: orderID=%d, err=%v", reqdata.Id, err)
		ctx.RespErrString(true, &errcode, "提款订单不存在")
		return
	}

	// 调用提款成功事件发送函数
	SendWithdrawSuccessEvent(order, "提款")

	// 确定提款方式显示名称
	var withdrawMethodName string
	if order.PayID > 0 {
		withdrawMethodName = "法币提款"
	} else {
		withdrawMethodName = "虚拟币提款"
	}

	// 构造返回结果
	result := map[string]interface{}{
		"success": true,
		"message": "提款成功事件上报完成",
		"data": map[string]interface{}{
			"order_id":        order.HbcOrder,
			"user_id":         order.UserID,
			"amount":          order.Amount,
			"received_amount": order.RealAmount,
			"withdraw_method": withdrawMethodName,
			"symbol":          order.Symbol,
		},
	}

	logs.Info("SendWithdrawSuccessEventAPI 提款成功事件上报完成: orderID=%d, userID=%d, amount=%.2f",
		order.ID, order.UserID, order.Amount)

	// 返回结果
	ctx.RespOK(result)
}

// SendWithdrawFailEventAPI 发送提款失败事件到ThinkingData的HTTP接口
// @description 提供给Node.js等外部系统调用的提款失败事件上报接口
// @param ctx HTTP请求上下文
// 请求参数:
//   - id: 提款订单ID (必填) - 数字ID
//   - fail_reason: 失败原因 (可选) - 失败原因描述
//
// 返回结果:
//   - success: 是否成功
//   - message: 处理结果消息
func (c *DataPushController) SendWithdrawFailEventAPI(ctx *abugo.AbuHttpContent) {
	defer recover()
	logs.Info("SendWithdrawFailEventAPI 调用提款失败事件上报接口")

	// 定义请求数据结构
	type RequestData struct {
		Id         int32  `validate:"required" json:"id"` // 提款订单ID，必填
		FailReason string `json:"fail_reason,omitempty"`  // 失败原因，可选
	}

	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 查询提款订单信息 - 按ID查询
	withdrawTb := server.DaoxHashGame().XWithdraw
	withdrawDb := server.DaoxHashGame().XWithdraw.WithContext(ctx.Gin())

	order, err := withdrawDb.Where(withdrawTb.ID.Eq(reqdata.Id)).First()
	if err != nil {
		logs.Error("SendWithdrawFailEventAPI 查询提款订单失败: orderID=%d, err=%v", reqdata.Id, err)
		ctx.RespErrString(true, &errcode, "提款订单不存在")
		return
	}

	// 查询用户信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(ctx.Gin())
	user, err := userDb.Where(userTb.UserID.Eq(order.UserID)).First()
	if err != nil {
		logs.Error("SendWithdrawFailEventAPI 查询用户信息失败: userID=%d, err=%v", order.UserID, err)
		ctx.RespErrString(true, &errcode, "用户信息不存在")
		return
	}

	// 设置默认失败原因
	failReason := reqdata.FailReason
	if failReason == "" {
		failReason = "提款失败"
	}

	// 调用提款失败事件发送函数
	SendWithdrawFailEvent(user, order, "提款", failReason)

	// 确定提款方式显示名称
	var withdrawMethodName string
	if order.PayID > 0 {
		withdrawMethodName = "法币提款"
	} else {
		withdrawMethodName = "虚拟币提款"
	}

	// 构造返回结果
	result := map[string]interface{}{
		"success": true,
		"message": "提款失败事件上报完成",
		"data": map[string]interface{}{
			"order_id":        order.ID,
			"user_id":         order.UserID,
			"amount":          order.Amount,
			"withdraw_method": withdrawMethodName,
			"symbol":          order.Symbol,
			"fail_reason":     failReason,
		},
	}

	logs.Info("SendWithdrawFailEventAPI 提款失败事件上报完成: orderID=%d, userID=%d, amount=%.2f, reason=%s",
		order.ID, order.UserID, order.Amount, failReason)

	// 返回结果
	ctx.RespOK(result)
}

// SendRewardGiveEventAPI 发送派奖事件到ThinkingData的HTTP接口
// @description 通过用户ID和订单ID查询订单信息和用户信息，并上报派奖事件
// @param ctx HTTP请求上下文
// 请求参数:
//   - user_id: 用户ID (必填) - int32
//   - order_id: 订单ID (必填) - int32
//
// 返回结果:
//   - success: 是否成功
//   - message: 处理结果消息
func (c *DataPushController) SendRewardGiveEventAPI(ctx *abugo.AbuHttpContent) {
	// 打印接收到的请求参数
	rawBody, _ := ctx.Gin().GetRawData()

	// 请求数据结构 - 匹配实际请求参数
	type RequestData struct {
		OrderId      int64   `json:"orderId"`      // 订单ID
		UserId       int32   `json:"userId"`       // 用户ID
		UserAddress  string  `json:"userAddress"`  // 用户地址
		GameId       int32   `json:"gameId"`       // 游戏ID
		BetArea      string  `json:"betArea"`      // 下注区域
		BetAmount    float64 `json:"betAmount"`    // 投注金额
		RewardAmount float64 `json:"rewardAmount"` // 返奖金额
		OpenResult   string  `json:"openResult"`   // 开奖结果
		IsWin        int32   `json:"isWin"`        // 是否中奖
		Symbol       string  `json:"symbol"`       // 币种
		SellerId     int32   `json:"sellerId"`     // 运营商ID
		ChannelId    int32   `json:"channelId"`    // 渠道ID
		Timestamp    int64   `json:"timestamp"`    // 时间戳
		ClientIP     string  `json:"clientIP"`     // 客户端IP (可选)
		ZoneOffset   int     `json:"zoneOffset"`   // 时区偏移量 (可选，默认+8)
	}

	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	if err := json.Unmarshal(rawBody, &reqdata); err != nil {
		logs.Error("SendRewardGiveEventAPI 请求数据解析失败: err=%v", err)
		ctx.RespErrString(true, &errcode, "请求数据格式错误: "+err.Error())
		return
	}

	// 验证参数
	if reqdata.UserId <= 0 {
		ctx.RespErrString(true, &errcode, "用户ID无效")
		return
	}
	if reqdata.OrderId <= 0 {
		ctx.RespErrString(true, &errcode, "订单ID无效")
		return
	}

	// 查询用户信息
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())
	user, err := userDb.Where(userDao.UserID.Eq(reqdata.UserId)).First()
	if err != nil {
		logs.Error("SendRewardGiveEventAPI 获取用户信息失败: userId=%d, err=%v", reqdata.UserId, err)
		ctx.RespErrString(true, &errcode, "用户不存在: "+err.Error())
		return
	}

	// 智能选择客户端IP
	var clientIP string
	if reqdata.ClientIP != "" && reqdata.ClientIP != "127.0.0.1" {
		// 优先使用请求中的客户端IP
		clientIP = reqdata.ClientIP
	} else if user.LoginIP != "" && user.LoginIP != "127.0.0.1" {
		// 其次使用用户登录IP
		clientIP = user.LoginIP
	} else {
		// 最后使用请求IP
		clientIP = ctx.GetIp()
	}

	// 根据实际数据构建参数
	gameId := fmt.Sprint(reqdata.GameId)
	gameName := c.getGameNameByGameId(int(reqdata.GameId))
	gameType := "哈希游戏"
	gameMethod := reqdata.BetArea
	winLossAmount := reqdata.RewardAmount - reqdata.BetAmount // 盈亏 = 返奖 - 投注

	// 设置时区偏移量，默认为+8（中国时区）
	zoneOffset := reqdata.ZoneOffset
	if zoneOffset == 0 {
		zoneOffset = 8 // 默认使用+8时区
	}

	// 上报game_result事件到ThinkingData
	logs.Info("SendRewardGiveEventAPI 开始调用SendGameResultEvent")
	err = SendGameResultEvent(
		user,              // user
		gameId,            // gameID
		gameName,          // gameName
		gameType,          // gameType
		gameMethod,        // gameMethod
		reqdata.BetAmount, // betAmount
		winLossAmount,     // winLossAmount
		clientIP,          // clientIP
		zoneOffset,        // zoneOffset (可选参数)
	)
	if err != nil {
		logs.Error("SendRewardGiveEventAPI 事件上报失败: userId=%d, orderId=%d, err=%v",
			reqdata.UserId, reqdata.OrderId, err)
		ctx.RespErrString(true, &errcode, "事件上报失败: "+err.Error())
		return
	}

	// 返回成功响应
	ctx.RespOK(map[string]interface{}{
		"message": "派奖事件上报成功",
		"data": map[string]interface{}{
			"user_id":  reqdata.UserId,
			"order_id": reqdata.OrderId,
		},
	})
}

// getGameNameByGameId 根据游戏ID获取游戏名称
func (c *DataPushController) getGameNameByGameId(gameId int) string {
	// 根据游戏ID的规律推断游戏类型
	switch {
	case gameId%100 == 1 || gameId == 1 || gameId == 101 || gameId == 131 || gameId == 201 || gameId == 301 || gameId == 331:
		return "哈希大小"
	case gameId%100 == 2 || gameId == 2 || gameId == 102 || gameId == 132 || gameId == 202 || gameId == 302 || gameId == 332:
		return "哈希单双"
	case gameId%100 == 3 || gameId == 3 || gameId == 103 || gameId == 133 || gameId == 203:
		return "哈希幸运"
	case gameId%100 == 4 || gameId == 4 || gameId == 104 || gameId == 134 || gameId == 204:
		return "哈希庄闲"
	case gameId%100 == 5 || gameId == 5 || gameId == 105 || gameId == 135 || gameId == 205:
		return "哈希牛牛"
	case gameId%100 == 6 || gameId == 106 || gameId == 116 || gameId == 126 || gameId == 136 || gameId == 206:
		return "哈希轮盘"
	default:
		return "哈希游戏"
	}
}
