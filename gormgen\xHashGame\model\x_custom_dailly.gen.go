// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXCustomDailly = "x_custom_dailly"

// XCustomDailly mapped from table <x_custom_dailly>
type XCustomDailly struct {
	ID                int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	SellerID          int32     `gorm:"column:SellerId;not null;comment:运营商" json:"SellerId"`                                          // 运营商
	RecordDate        time.Time `gorm:"column:RecordDate;not null;comment:记录日期" json:"RecordDate"`                                     // 记录日期
	Symbol            string    `gorm:"column:Symbol;not null;comment:币种" json:"Symbol"`                                               // 币种
	UserID            int32     `gorm:"column:UserId;not null;comment:玩家id" json:"UserId"`                                             // 玩家id
	Address           string    `gorm:"column:Address;not null;comment:投注地址" json:"Address"`                                           // 投注地址
	GameID            int32     `gorm:"column:GameId;not null;comment:游戏id" json:"GameId"`                                             // 游戏id
	RoomLevel         int32     `gorm:"column:RoomLevel;not null;comment:房间等级" json:"RoomLevel"`                                       // 房间等级
	ToAddress         string    `gorm:"column:ToAddress;not null;comment:官方地址" json:"ToAddress"`                                       // 官方地址
	BetAmount         float64   `gorm:"column:BetAmount;not null;default:0.000000;comment:下注金额" json:"BetAmount"`                      // 下注金额
	BonusBetAmount    float64   `gorm:"column:BonusBetAmount;not null;default:0.000000;comment:Bonus下注金额" json:"BonusBetAmount"`       // Bonus下注金额
	RewardAmount      float64   `gorm:"column:RewardAmount;not null;default:0.000000;comment:返奖金额" json:"RewardAmount"`                // 返奖金额
	BonusRewardAmount float64   `gorm:"column:BonusRewardAmount;not null;default:0.000000;comment:Bonus返奖金额" json:"BonusRewardAmount"` // Bonus返奖金额
	Fee               float64   `gorm:"column:Fee;not null;default:0.000000;comment:平台手续费" json:"Fee"`                                 // 平台手续费
	GasFee            float64   `gorm:"column:GasFee;not null;default:0.000000000000;comment:gas费" json:"GasFee"`                      // gas费
	NewGuys           int32     `gorm:"column:NewGuys;not null;default:2;comment:是否是第一次来平台完, 1是,2不是" json:"NewGuys"`                   // 是否是第一次来平台完, 1是,2不是
	IsGameAddress     int32     `gorm:"column:IsGameAddress;default:2;comment:是否是游戏地址 1 是 ,2 不是" json:"IsGameAddress"`                 // 是否是游戏地址 1 是 ,2 不是
	BetCount          int32     `gorm:"column:BetCount;not null;comment:投注次数" json:"BetCount"`                                         // 投注次数
	WinCount          int32     `gorm:"column:WinCount;not null;comment:中奖次数" json:"WinCount"`                                         // 中奖次数
	ChannelID         int32     `gorm:"column:ChannelId;default:1;comment:渠道" json:"ChannelId"`                                        // 渠道
	BetChannelID      int32     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"`                                        // 下注渠道Id
	IsTest            int32     `gorm:"column:IsTest;default:2;comment:是否是测试玩家" json:"IsTest"`                                         // 是否是测试玩家
	LiuSui            float64   `gorm:"column:LiuSui;default:0.000000;comment:有效投注" json:"LiuSui"`                                     // 有效投注
	FirstLiuSui       float64   `gorm:"column:FirstLiuSui;default:0.000000;comment:扣减前有效投注" json:"FirstLiuSui"`                        // 扣减前有效投注
	ValidBetAmount    float64   `gorm:"column:ValidBetAmount;default:0.000000;comment:有效投注" json:"ValidBetAmount"`                     // 有效投注
	TopAgentID        int32     `gorm:"column:TopAgentId;comment:顶级代理" json:"TopAgentId"`                                              // 顶级代理
	SpecialAgent      int32     `gorm:"column:SpecialAgent;comment:是否独立代理" json:"SpecialAgent"`                                        // 是否独立代理
	STopAgentID       int32     `gorm:"column:STopAgentId" json:"STopAgentId"`
	CSGroup           string    `gorm:"column:CSGroup" json:"CSGroup"`
	Lang              string    `gorm:"column:Lang;comment:登录语言" json:"Lang"` // 登录语言
}

// TableName XCustomDailly's table name
func (*XCustomDailly) TableName() string {
	return TableNameXCustomDailly
}
