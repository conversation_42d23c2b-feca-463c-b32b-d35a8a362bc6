package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

type PPConfig struct {
	url                   string
	name                  string
	secureLogin           string
	key                   string //Secret Key
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func NewPPLogic(params map[string]string, fc func(int) error) *PPConfig {

	return &PPConfig{
		url:                   params["url"],
		name:                  params["name"],
		secureLogin:           params["secureLogin"],
		key:                   params["key"],
		currency:              params["currency"],
		brandName:             "pp",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyPP = "cacheKeyPP:"

func (l *PPConfig) createHash(querydata map[string]any) string {
	str := base.SortMap(querydata, "&", "=", "asc") + l.key
	md5s := base.MD5(str)
	logs.Info("pp签名前字符", str, "|pp签名后md5", md5s)
	return md5s
}

func (l *PPConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId string `validate:"required"`
		Lang   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error(l.brandName, "Login 参数解析失败", "err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyPP, token.UserId); err != nil {
		logs.Error(l.brandName, "Login IsLoginByUserId 失败", "userId=", token.UserId, "err=", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, l.brandName, reqdata.GameId)
	if err != nil {
		logs.Error(l.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	//sessionId
	sessionId := base.UserId2token(cacheKeyPP, token.UserId)

	querydata := map[string]any{
		"secureLogin":      l.secureLogin,
		"symbol":           reqdata.GameId,
		"language":         reqdata.Lang,
		"token":            sessionId,
		"externalPlayerId": l.currency + "_" + fmt.Sprint(token.UserId),
		"currency":         l.currency,
	}

	querydata["hash"] = l.createHash(querydata)

	url := fmt.Sprintf("%s/IntegrationService/v3/http/CasinoGameAPI/game/url/", l.url)
	reqBytes, _ := json.Marshal(querydata)

	httpclient := httpc.DoRequest{
		UrlPath:    url,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("PP API request failed:", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试") // "Ошибка входа, пожалуйста, попробуйте позже"
		return
	}
	if errors, ok := data["error"]; ok {
		if errors != "0" {
			logs.Error("pp_http_post body error:", data)
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
	}
	if data != nil {
		ctx.Put("url", data["gameURL"])
	}
	ctx.RespOK()

}

// 验证token
func (l *PPConfig) CheckToken(querydata map[string]any) bool {
	hash := querydata["hash"]
	delete(querydata, "hash")
	myHash := l.createHash(querydata)
	logs.Info("pp三方给出的签名:", hash, "|我方签名", myHash)
	return myHash == hash
}

// 0 Success.    成功
// 1 余额不足。该错误应在投注请求的响应中返回。
// 2 未找到玩家或已注销。如果无法找到玩家或在娱乐场运营商处登出，则应在Pragmatic Play 发送的任何请求的响应中返回。
// 3 不允许投注。当玩家不被允许玩特定游戏时，无论如何都应该返回。例如，因为特殊奖金。
// 4 由于令牌无效、未找到或过期，玩家身份验证失败。
// 5 无效的哈希码。如果哈希码验证失败，则应在Pragmatic  Play 发送的任何请求的响应中返回。
// 6 玩家被冻结。如果玩家帐户被禁止或冻结，娱乐场运营商将在响应任何请求时返回此错误。
// 7 请求参数错误，请检查post参数。
// 8 游戏未找到或已禁用。如果由于某种原因游戏无法进行，则应在投注请求时返回此错误。即使游戏被禁用，包含获胜金额的投注结果请求也应按预期处理。
// 50 已达到投注限额。该准则与受监管的市场相关。
// 100 内部服务器错误(需要重试)。如果娱乐场运营商的系统存在内部问题并且目前无法处理请求并且运营商逻辑需要重试请求，则娱乐场运营商将返回此错误代码。请求将遵循协调流程
// 120 内部服务器错误(不需要重试)。如果娱乐场运营商的系统存在内部问题并且无法处理请求，并且运营商逻辑不需要重试请求，则娱乐场运营商将返回此错误代码。请求不会遵循对帐流程
// 130 EndRound 处理时出现内部服务器错误。如果娱乐场运营商的系统存在内部问题并且无法处理EndRound 请求，并且运营商逻辑需要重试该请求，则娱乐场运营商将返回此错误代码。此错误代码仅适用于Endround 方法，不适用于其他方法
// 210 现实检查警告
// 310 玩家的赌注超出了他的赌注限制。如果玩家的限制已更改，并且投注超出新的限制级别，则应返回。游戏客户端会显示正确的错误信息，并要求玩家重新打开游戏。游戏重新开放后，将应用新的投注限额。该错误与发送玩家下注限额以响应身份验证请求的运营商相关。

// Auth
func (l *PPConfig) Auth(ctx *abugo.AbuHttpContent) {
	type resp struct {
		UserId      string  `json:"userId"`
		Currency    string  `json:"currency"`
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}
	req := gin.H{
		"hash":       ctx.Gin().PostForm("hash"),
		"token":      ctx.Gin().PostForm("token"),
		"providerId": ctx.Gin().PostForm("providerId"),
		"gameId":     ctx.Gin().PostForm("gameId"),
		"ipAddress":  ctx.Gin().PostForm("ipAddress"),
	}
	logs.Info("pp三方AuthAuth req:", req)
	//验证签名
	if !l.CheckToken(req) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	//获取用户id
	token := fmt.Sprint(req["token"])
	logs.Error("AuthAuthAuthAuth", ctx.Gin().Request.URL, req)
	userId := base.Token2UserId(cacheKeyPP, token)
	if userId == -1 {
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	//获取玩家余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyPP)

	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "Auth 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	authResp.Error = 0
	authResp.Description = "成功"
	authResp.Currency = l.currency
	authResp.UserId = l.currency + "_" + fmt.Sprint(userId)
	authResp.Cash = balance2
	authResp.Bonus = 0
	ctx.RespJson(authResp)
	return
}

// Balance
func (l *PPConfig) Balance(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Currency    string  `json:"currency"`
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}
	req := gin.H{
		"hash":       ctx.Gin().PostForm("hash"),
		"providerId": ctx.Gin().PostForm("providerId"),
		"userId":     ctx.Gin().PostForm("userId"),
		"token":      ctx.Gin().PostForm("token"),
	}
	logs.Error("pp三方Balance req:", req)
	//验证签名
	if !l.CheckToken(req) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}

	//获取玩家余额
	userId := base.ParseUserIdFromPlayerName(fmt.Sprint(req["userId"]))
	_, balance, err := base.GetUserBalance(userId, cacheKeyPP)

	balance2 := float64(int(balance*100)) / 100

	if err != nil {
		logs.Error(l.brandName, "Balance 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		authResp.Currency = l.currency
		ctx.RespJson(authResp)
		return
	}
	authResp.Error = 0
	authResp.Description = "成功"
	authResp.Currency = l.currency
	authResp.Cash = balance2
	authResp.Bonus = 0
	ctx.RespJson(authResp)
	return
}

type betReq struct {
	Hash                string `form:"hash" json:"hash"`
	ProviderId          string `form:"providerId" json:"providerId"`
	UserId              string `form:"userId" json:"userId"`
	PlayerName          string `form:"playerName" json:"playerName"`
	GameId              string `form:"gameId" json:"gameId"`
	RoundId             *int64 `form:"roundId" json:"roundId"`
	Amount              string `form:"amount" json:"amount"`
	Reference           string `form:"reference" json:"reference"`
	Timestamp           int64  `form:"timestamp" json:"timestamp"`
	RoundDetails        string `form:"roundDetails" json:"roundDetails"`
	BonusCode           string `form:"bonusCode" json:"bonusCode"`
	Platform            string `form:"platform" json:"platform"`
	Language            string `form:"language" json:"language"`
	JackpotContribution string `form:"jackpotContribution" json:"jackpotContribution"`
	JackpotId           *int64 `form:"jackpotId" json:"jackpotId"`
	JackpotDetails      string `form:"jackpotDetails" json:"jackpotDetails"`
	Token               string `form:"token" json:"token"`
	IpAddress           string `form:"ipAddress" json:"ipAddress"`
}

func (l *PPConfig) Bet(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		UsedPromo     float64 `json:"usedPromo"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := betReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		logs.Error(l.brandName, "Bet 参数绑定失败", "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info("pp三方Bet req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if err != nil {
		logs.Error("pp三方Bet json.Unmarshal err:", err)

		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Description = "请求参数错误，请检查post参数。"
		authResp.Error = 7
		ctx.RespJson(authResp)
		return
	}
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "Bet 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}
	//三方来源的数据整理
	var (
		betAmount = base.StrToFloat64(req.Amount)
		thirdId   = fmt.Sprint(*req.RoundId)
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	if betAmount > balance2 {
		authResp.Error = 1
		authResp.Description = "余额不足"
		authResp.Currency = l.currency
		authResp.TransactionId = myTransactionId
		authResp.Cash = balance2
		ctx.RespJson(authResp)
		return
	}

	//查询订单是否存在
	betTran, isExist := base.OrderIsExist(thirdId, l.brandName, "x_third_dianzhi_pre_order")
	if isExist {
		authResp.Error = 3
		authResp.Description = "不允许投注"
		authResp.Currency = l.currency
		authResp.TransactionId = myTransactionId
		authResp.Cash = balance2
		ctx.RespJson(authResp)
		logs.Error(l.brandName, "Bet 注单已经存在", "userId=", userId, "thirdId=", thirdId)
		return
	}

	// 获取用户渠道ID
	ChannelId := base.GetUserChannelIdFromMap(ctx, udata, userId)
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gamecode, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("获取游戏列表失败 GameId=", gamecode, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
		}
	}

	//开启事务
	var afterBalance float64
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		if betAmount > 0 {
			ressql, err2 := tx.Tx.Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmount, userId, betAmount)
			row, err3 := ressql.RowsAffected()
			if err2 != nil || err3 != nil || row < 1 {
				logs.Error(l.brandName, "下注 修改x_user失败了:  id = ", thirdId, err)
				return errors.New("修改x_user失败了")
			}
		}
		afterBalance = balance2 - betAmount
		if betTran != nil {
			betId := abugo.GetInt64FromInterface((*betTran)["Id"])
			_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmount, betId)
			if err != nil {
				logs.Error(l.brandName, "下注 修改x_third_dianzhi_pre_order修改失败了:  id = ", thirdId, err)
				return err
			}
		} else {
			//获取三方游戏名称
			order := xgo.H{
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
				"BetChannelId": ChannelId,
				"UserId":       userId,
				"Brand":        l.brandName,
				"ThirdId":      thirdId,
				"GameId":       gamecode,
				"GameName":     gameName,
				"BetAmount":    betAmount,
				"WinAmount":    0,
				"ValidBet":     0,
				"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
				"Currency":     l.currency,
				"RawData":      string(reqBytes),
				"DataState":    -1,
			}
			_, err = tx.Table("x_third_dianzhi_pre_order").Insert(order)
			if err != nil {
				logs.Error(l.brandName, "下注 修改x_third_dianzhi_pre_order新增失败了:  id = ", thirdId, err)
				return err
			}
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance,
			"Amount":       0 - betAmount,
			"AfterAmount":  balance - betAmount,
			"Reason":       utils.BalanceCReasonPPBet,
			"Memo":         l.brandName + " bet,thirdId:" + thirdId,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Error(l.brandName, "下注 新增x_amount_change_log失败了:  id = ", thirdId, err)
			return err
		}

		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP Bet事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 100
		authResp.Description = "服务器错误"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		afterBalance = float64(int(afterBalance*100)) / 100
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = afterBalance
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)

		// 推送下注事件通知
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency, l.brandName, thirdId, 1)
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][PPConfig] Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][PPConfig] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

type resultReq struct {
	Hash              string `form:"hash" json:"hash"`
	UserId            string `form:"userId" json:"userId"`
	PlayerName        string `form:"playerName" json:"playerName"`
	GameId            string `form:"gameId" json:"gameId"`
	RoundId           *int64 `form:"roundId" json:"roundId"`
	Amount            string `form:"amount" json:"amount"`
	Reference         string `form:"reference" json:"reference"`
	ProviderId        string `form:"providerId" json:"providerId"`
	Timestamp         *int64 `form:"timestamp" json:"timestamp"`
	RoundDetails      string `form:"roundDetails" json:"roundDetails"`
	BonusCode         string `form:"bonusCode" json:"bonusCode"`
	Platform          string `form:"platform" json:"platform"`
	Token             string `form:"token" json:"token"`
	PromoWinAmount    string `form:"promoWinAmount" json:"promoWinAmount"`
	PromoWinReference string `form:"promoWinReference" json:"promoWinReference"`
	PromoCampaignID   string `form:"promoCampaignID" json:"promoCampaignID"`
	PromoCampaignType string `form:"promoCampaignType" json:"promoCampaignType"`
}

// Result
func (l *PPConfig) Result(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := resultReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		logs.Error(l.brandName, "Result 参数绑定失败", "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error(l.brandName, "三方Result req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if err != nil {
		logs.Error(l.brandName, "三方Result json.Unmarshal err:", err)

		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Description = "请求参数错误，请检查post参数。"
		authResp.Error = 7
		ctx.RespJson(authResp)
		return
	}
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "Result 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = fmt.Sprint(*req.RoundId)
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	//判断订单是否处理过  DataState -2取消订单  -1下注订单  1结算后的订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", l.brandName, nil)
	where.Add("and", "DataState", "=", -1, nil)
	betTran, err := server.Db().Table("x_third_dianzhi_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		if err != nil {
			logs.Error(l.brandName, "Result 查询预订单失败或不存在", "thirdId=", thirdId, "err=", err)
		}
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 120
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}
	logs.Info(l.brandName, " 订单数据：", *betTran)

	// 所有电子的有效流水取不大于下注金额的输赢绝对值
	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	validBet := math.Abs(amount - betAmount)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}
	logs.Info("PP Result 记录有效流水", l.brandName, "thirdId=", thirdId, " amount=", amount, " betAmount=", betAmount, " validBet=", validBet)

	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//将下注订单移动至结算订单表
		//修改成已经结算了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set
		WinAmount = ?,
		ValidBet = ?,
		RawData = ?,
		GameId = ?,
		ThirdTime = ?,
		DataState = 1
		where Id = ?`,
			amount,
			validBet,
			string(reqBytes),
			gamecode,
			thirdTime,
			betId,
		)
		if err != nil {
			logs.Error(l.brandName, " 结算 修改成已经结算了 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, err)
			return err
		}
		//移动至统计表
		tmp := (*betTran)
		delete(tmp, "Id")
		delete(tmp, "CreateTime")
		tmp["DataState"] = 1
		tmp["WinAmount"] = amount
		tmp["ValidBet"] = validBet
		tmp["RawData"] = string(reqBytes)
		tmp["GameId"] = gamecode
		tmp["ThirdTime"] = thirdTime
		_, err = tx.Table("x_third_dianzhi").Insert(tmp)
		if err != nil {
			logs.Error("PP 结算 移动至统计表 x_third_dianzhi 失败了:  id = ", thirdId, err)
			return err
		}
		//处理结算
		if amount > 0 {
			//win
			_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			if err != nil {
				logs.Error(l.brandName, "结算 处理结算 x_user 失败了:  id = ", thirdId, err)
				return err
			}
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance,
				"Amount":       amount,
				"AfterAmount":  balance + amount,
				"Reason":       utils.BalanceCReasonPPSettle,
				"Memo":         l.brandName + " settle,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Error(l.brandName, "结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
				return err
			}
		}
		logs.Info("PP 结算 Result 結算成功 成功", l.brandName, "thirdId=", thirdId, " amount=", amount, " betAmount=", betAmount, " validBet=", validBet)
		balance2 = balance2 + amount
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP 结算事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 100
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		balance2 = float64(int(balance2*100)) / 100
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)

		// 推送奖励事件通知
		if l.thirdGamePush != nil {
			//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, betAmount, winAmount, l.currency)
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
		}

		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				logs.Error("pp Result 异步获取开奖结果 首次失败", "userId=", userId, "brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId, "err=", err)
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != "" {
				if e := server.Db().Gorm().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp Result 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Result 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Result 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type bonusWinReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	PlayerName   string `form:"playerName" json:"playerName"`
	Amount       string `form:"amount" json:"amount"`
	Reference    string `form:"reference" json:"reference"`
	ProviderId   string `form:"providerId" json:"providerId"`
	Timestamp    *int64 `form:"timestamp" json:"timestamp"`
	BonusCode    string `form:"bonusCode" json:"bonusCode"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	GameId       string `form:"gameId" json:"gameId"`
	Token        string `form:"token" json:"token"`
	RequestId    string `form:"requestId" json:"requestId"`
	RemainAmount string `form:"remainAmount" json:"remainAmount"`
}

// BonusWin
func (l *PPConfig) BonusWin(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := bonusWinReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		logs.Error(l.brandName, "三方BonusWin err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//关于 3.9 bonusWin 处理
	//我们将在每一个免费旋转（amount=0.00和bonuscode参数）向您发送"zero" 投注呼叫。此外，如果免费旋转有赢额，我们会向您发送结果请求 （result request). 免费旋转结束了以后，我们会呼叫 bonusWin (使用与投注呼叫相同bonusCode参数）
	//贵方有两种方法关于result/bonuswin处理：
	//1.您可以使用玩家的余额回复中间的result呼叫（在免费旋转周期内不奖励玩家的赢额), 然后给玩家bonusWin呼叫中的总赢额。
	//2.您可以每次根据收到的结果请求中的金额奖励玩家，忽略bonusWin呼叫中的金额（您可以将它使用作校验和）
	//*根据我们的经验，我们建议第二个方法

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方BonusWin req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "BonusWin 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = req.Reference
		gamecode  = req.GameId
		thirdTime = time.Now()
	)
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gamecode, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("获取游戏列表失败 GameId=", gamecode, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
		}
	}

	//开启事务
	var finalBalance float64
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//将免费游戏直接结算订单表
		order := xgo.H{
			"SellerId":  (*udata)["SellerId"],
			"ChannelId": (*udata)["ChannelId"],
			"UserId":    userId,
			"Brand":     l.brandName,
			"ThirdId":   thirdId,
			"GameId":    gamecode,
			"GameName":  gameName,
			"BetAmount": 0,
			"WinAmount": amount,
			"ValidBet":  0,
			"ThirdTime": thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			"Currency":  l.currency,
			"RawData":   string(reqBytes),
			"DataState": 1,
		}
		_, err = tx.Table("x_third_dianzhi").Insert(order)
		if err != nil {
			logs.Error("PP 结算 免费游戏 统计表 x_third_dianzhi 失败了:  id = ", thirdId, err)
			return err
		}
		//处理结算
		if amount > 0 {
			//win
			_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			if err != nil {
				logs.Error(l.brandName, "结算 免费游戏 处理结算 x_user 失败了:  id = ", thirdId, err)
				return err
			}
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance,
				"Amount":       amount,
				"AfterAmount":  balance + amount,
				"Reason":       utils.BalanceCReasonPPBonusWin,
				"Memo":         l.brandName + " bonusWin,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Error(l.brandName, "结算 免费游戏 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
				return err
			}
		}
		logs.Debug("pp sw :", "結算 免费游戏 成功")
		finalBalance = balance2 + amount
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP BonusWin事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 100
		authResp.Description = "事务回滚异常"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		finalBalance = float64(int(finalBalance*100)) / 100
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = finalBalance
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] BonusWin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] BonusWin 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type jackpotWinWinReq struct {
	Hash           string `form:"hash" json:"hash"`
	ProviderId     string `form:"providerId" json:"providerId"`
	Timestamp      *int64 `form:"timestamp" json:"timestamp"`
	UserId         string `form:"userId" json:"userId"`
	PlayerName     string `form:"playerName" json:"playerName"`
	GameId         string `form:"gameId" json:"gameId"`
	RoundId        *int64 `form:"roundId" json:"roundId"`
	JackpotId      *int64 `form:"jackpotId" json:"jackpotId"`
	JackpotDetails string `form:"jackpotDetails" json:"jackpotDetails"`
	Amount         string `form:"amount" json:"amount"`
	Reference      string `form:"reference" json:"reference"`
	Platform       string `form:"platform" json:"platform"`
	Token          string `form:"token" json:"token"`
}

// JackpotWin
func (l *PPConfig) JackpotWin(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := jackpotWinWinReq{}
	err := ctx.Gin().ShouldBind(&req)
	//唯一请求uuid
	myTransactionId := abugo.GetUuid()

	if err != nil {
		logs.Error(l.brandName, "三方JackpotWin err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方JackpotWin req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "JackpotWin 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = req.Reference
		gamecode  = req.GameId
		thirdTime = time.Now()
	)
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gamecode, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("获取游戏列表失败 GameId=", gamecode, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
		}
	}
	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//将JackpotWin游戏直接结算订单表
		order := xgo.H{
			"SellerId":  (*udata)["SellerId"],
			"ChannelId": (*udata)["ChannelId"],
			"UserId":    userId,
			"Brand":     l.brandName,
			"ThirdId":   thirdId,
			"GameId":    gamecode,
			"GameName":  gameName,
			"BetAmount": 0,
			"WinAmount": amount,
			"ValidBet":  0,
			"ThirdTime": thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			"Currency":  l.currency,
			"RawData":   string(reqBytes),
			"DataState": 1,
		}
		_, err = tx.Table("x_third_dianzhi").Insert(order)
		if err != nil {
			logs.Error("PP 结算 JackpotWin 统计表 x_third_dianzhi 失败了:  id = ", thirdId, err)
			return err
		}
		//处理结算
		if amount > 0 {
			//win
			_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			if err != nil {
				logs.Error(l.brandName, "结算 JackpotWin 处理结算 x_user 失败了:  id = ", thirdId, err)
				return err
			}
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance,
				"Amount":       amount,
				"AfterAmount":  balance + amount,
				"Reason":       utils.BalanceCReasonPPJackpotWin,
				"Memo":         l.brandName + " bonusWin,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Error(l.brandName, "结算 JackpotWin 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
				return err
			}
		}
		logs.Debug("pp sw :", " JackpotWin 結算成功", " thirdId=", thirdId)
		balance2 = balance2 + amount
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP JackpotWin事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 100
		authResp.Description = "内部服务器错误"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		balance2 = float64(int(balance2*100)) / 100
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)

		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				logs.Error("pp JackpotWin 异步获取开奖结果 首次失败", "userId=", userId, "brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId, "err=", err)
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != "" {
				if e := server.Db().Gorm().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp JackpotWin 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] JackpotWin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] JackpotWin 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type endRoundReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	PlayerName   string `form:"playerName" json:"playerName"`
	GameId       string `form:"gameId" json:"gameId"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	ProviderId   string `form:"providerId" json:"providerId"`
	BonusCode    string `form:"bonusCode" json:"bonusCode"`
	Platform     string `form:"platform" json:"platform"`
	Token        string `form:"token" json:"token"`
	RoundDetails string `form:"roundDetails" json:"roundDetails"`
}

// EndRound
func (l *PPConfig) EndRound(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Cash        float64 `json:"cash"`
		Bonus       float64 `json:"bonus"`
		Error       int     `json:"error"`
		Description string  `json:"description"`
	}
	authResp := resp{}

	req := endRoundReq{}
	err := ctx.Gin().ShouldBind(&req)
	if err != nil {
		logs.Error(l.brandName, "三方EndRound 请检查post参数 reqMap:", req, err)
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方EndRound req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	_, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "EndRound 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		thirdId   = req.RoundId
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", l.brandName, nil)
	where.Add("and", "DataState", "=", -1, nil)
	betTran, err := server.Db().Table("x_third_dianzhi_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		if err != nil {
			logs.Error(l.brandName, "EndRound 查询预订单失败", "thirdId=", thirdId, "err=", err)
		}
		//订单不存在  忽略它
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return
	}
	logs.Info(l.brandName, " 订单数据：", *betTran)

	// 所有电子的有效流水取不大于下注金额的输赢绝对值
	winAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"])
	betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	//gameName := abugo.GetStringFromInterface((*betTran)["GameName"])
	validBet := math.Abs(winAmount - betAmount)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}

	logs.Info("PP EndRound 记录有效流水", l.brandName, "thirdId=", *thirdId, " winAmount=", winAmount, " betAmount=", betAmount, " validBet=", validBet)

	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//将下注订单移动至结算订单表
		//修改成已经结算了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set
		ValidBet = ?,
		RawData = ?,
		GameId = ?,
		ThirdTime = ?,
		DataState = 1
		where Id = ?`,
			validBet,
			string(reqBytes),
			gamecode,
			thirdTime,
			betId,
		)
		if err != nil {
			logs.Error(l.brandName, " EndRound 修改成已经结算了 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, err)
			return err
		}
		//移动至统计表
		tmp := (*betTran)
		delete(tmp, "Id")
		delete(tmp, "CreateTime")
		tmp["ValidBet"] = validBet
		tmp["DataState"] = 1
		tmp["RawData"] = string(reqBytes)
		tmp["GameId"] = gamecode
		tmp["ThirdTime"] = thirdTime
		_, err = tx.Table("x_third_dianzhi").Insert(tmp)
		if err != nil {
			logs.Error("PP EndRound 移动至统计表 x_third_dianzhi 失败了:  id = ", thirdId, err)
			return err
		}
		logs.Info("pp EndRound 結算成功", " thirdId=", thirdId)
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP EndRound事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.Cash = balance2
		authResp.Error = 100
		authResp.Description = "事务回滚异常"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		balance2 = float64(int(balance2*100)) / 100
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)

		go func() {
			// time.Sleep(100 * time.Millisecond)
			url, err := l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
			if err != nil {
				logs.Error("pp EndRound 异步获取开奖结果 首次失败", "userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId, "err=", err)
				time.Sleep(100 * time.Millisecond)
				url, err = l.getRoundDetail(req.UserId, req.GameId, *req.RoundId, "zh")
				if err != nil {
					logs.Error("pp EndRound 异步获取开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}
			if url != "" && thirdId != nil && *thirdId != 0 {
				if e := server.Db().Gorm().Table("x_third_dianzhi").Where("ThirdId=? and UserId=? and Brand=?", strconv.FormatInt(*thirdId, 10), userId, l.brandName).Updates(map[string]interface{}{
					"BetCtx":     url,
					"GameRst":    url,
					"BetCtxType": 2,
				}).Error; e != nil {
					logs.Error("pp EndRound 异步设置开奖结果 错误 userId=", userId, "table=x_third_dianzhi brand=", l.brandName, "gameCode=", gamecode, " thirdId=", thirdId)
				}
			}

		}()
	}
}

type refundReq struct {
	Hash         string `form:"hash" json:"hash"`
	UserId       string `form:"userId" json:"userId"`
	PlayerName   string `form:"playerName" json:"playerName"`
	Reference    string `form:"reference" json:"reference"`
	ProviderId   string `form:"providerId" json:"providerId"`
	Platform     string `form:"platform" json:"platform"`
	Amount       string `form:"amount" json:"amount"`
	GameId       string `form:"gameId" json:"gameId"`
	RoundId      *int64 `form:"roundId" json:"roundId"`
	Timestamp    *int64 `form:"timestamp" json:"timestamp"`
	RoundDetails *int64 `form:"roundDetails" json:"roundDetails"`
	BonusCode    string `form:"bonusCode" json:"bonusCode"`
	Token        string `form:"token" json:"token"`
}

// Refund
func (l *PPConfig) Refund(ctx *abugo.AbuHttpContent) {
	type resp struct {
		TransactionId string `json:"transactionId"`
		Error         int    `json:"error"`
		Description   string `json:"description"`
	}
	authResp := resp{}

	req := refundReq{}
	err := ctx.Gin().ShouldBind(&req)

	myTransactionId := abugo.GetUuid()
	if err != nil {
		logs.Error(l.brandName, "三方Refund err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Error(l.brandName, "三方Refund req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}

	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "Refund 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		thirdId = abugo.GetStringFromInterface(req.RoundId)
	)
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", l.brandName, nil)
	betTran, err := server.Db().Table("x_third_dianzhi_pre_order").Where(where).GetOne()
	if betTran == nil || err != nil {
		if err != nil {
			logs.Error(l.brandName, "Refund 查询预订单失败", "thirdId=", thirdId, "err=", err)
		}
		//订单不存在  todo 后面发现日志有空指针存在
		logs.Debug("pp 订单不存在:  id = ", thirdId)
		authResp.TransactionId = myTransactionId
		authResp.Error = 120
		authResp.Description = "订单不存在"
		ctx.RespJson(authResp)
		return
	}
	dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
	if dataState == -2 {
		//已经取消了
		logs.Warning("pp 订单已经取消了:  id = ", thirdId)
		authResp.TransactionId = myTransactionId
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return
	}
	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//修改成取消了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set
		RawData = ?,
		DataState = -2
		where Id = ?`,
			string(reqBytes),
			betId,
		)
		if err != nil {
			logs.Error("pp 订单取消 修改x_third_dianzhi_pre_order状态失败了:  id = ", thirdId, err)
			return err
		}

		_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", abugo.GetFloat64FromInterface((*betTran)["BetAmount"]), userId)
		if err != nil {
			logs.Error("pp 订单取消 操作金币上分失败了:  id = ", thirdId, err)
			return err
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance2,
			"Amount":       (*betTran)["BetAmount"],
			"AfterAmount":  balance2 + abugo.GetFloat64FromInterface((*betTran)["BetAmount"]),
			"Reason":       utils.BalanceCReasonPPCancel,
			"Memo":         "pp cancel,thirdId:" + thirdId,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Error("pp 订单取消 操作金币日志失败了:  id = ", thirdId, err)
			return err
		}
		logs.Debug("pp 订单取消成功了:  id = ", thirdId, err)
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP Refund事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Error = 100
		authResp.Description = "事务回滚异常"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		authResp.TransactionId = myTransactionId
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Refund 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// PromoWin  暂时未做
func (l *PPConfig) PromoWin(ctx *abugo.AbuHttpContent) {

}

type adjustmentReq struct {
	Hash           string `form:"hash" json:"hash"`
	UserId         string `form:"userId" json:"userId"`
	PlayerName     string `form:"playerName" json:"playerName"`
	GameId         string `form:"gameId" json:"gameId"`
	Token          string `form:"token" json:"token"`
	RoundId        *int64 `form:"roundId" json:"roundId"`
	Amount         string `form:"amount" json:"amount"`
	Reference      string `form:"reference" json:"reference"`
	ProviderId     string `form:"providerId" json:"providerId"`
	ValidBetAmount string `form:"validBetAmount" json:"validBetAmount"`
	Timestamp      *int64 `form:"timestamp" json:"timestamp"`
}

// Adjustment
func (l *PPConfig) Adjustment(ctx *abugo.AbuHttpContent) {
	//适用于真人游戏
	type resp struct {
		TransactionId string  `json:"transactionId"`
		Currency      string  `json:"currency"`
		Cash          float64 `json:"cash"`
		Bonus         float64 `json:"bonus"`
		Error         int     `json:"error"`
		Description   string  `json:"description"`
	}
	authResp := resp{}

	req := adjustmentReq{}
	err := ctx.Gin().ShouldBind(&req)

	myTransactionId := abugo.GetUuid()
	if err != nil {
		logs.Error(l.brandName, "三方Adjustment err req:", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 7
		authResp.Description = "请求参数错误，请检查post参数。"
		ctx.RespJson(authResp)
		return
	}

	//验证签名
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方Adjustment req:", string(reqBytes))
	reqMaps := map[string]any{}
	err = base.MyJson.Unmarshal(reqBytes, &reqMaps)
	if !l.CheckToken(reqMaps) {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 5
		authResp.Description = "无效的哈希码"
		ctx.RespJson(authResp)
		return
	}
	//查询玩家信息
	userId := base.ParseUserIdFromPlayerName(req.UserId)
	udata, balance, err := base.GetUserBalance(userId, cacheKeyPP)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error(l.brandName, "Adjustment 获取玩家余额失败", "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Error = 2
		authResp.Description = "未找到玩家或已注销"
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		amount    = base.StrToFloat64(req.Amount)
		thirdId   = fmt.Sprint(req.RoundId)
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	if amount == 0 {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
		return
	}
	if -amount > balance2 {
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2
		authResp.Error = 1
		authResp.Description = "余额不足"
		ctx.RespJson(authResp)
		return
	}
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gamecode, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("获取游戏列表失败 GameId=", gamecode, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
		}
	}
	//开启事务
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		//将余额调整直接结算订单表
		order := xgo.H{
			"SellerId":  (*udata)["SellerId"],
			"ChannelId": (*udata)["ChannelId"],
			"UserId":    userId,
			"Brand":     l.brandName,
			"ThirdId":   thirdId,
			"GameId":    gamecode,
			"GameName":  gameName,
			"ValidBet":  0,
			"ThirdTime": thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
			"Currency":  l.currency,
			"RawData":   string(reqBytes),
			"DataState": 1,
		}
		if amount > 0 {
			order["WinAmount"] = amount
		} else {
			order["BetAmount"] = -amount
		}
		_, err = tx.Table("x_third_dianzhi").Insert(order)
		if err != nil {
			logs.Error("PP 结算 Adjustment 统计表 x_third_dianzhi 失败了:  id = ", thirdId, err)
			return err
		}
		//处理余额调整
		_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
		if err != nil {
			logs.Error(l.brandName, "结算 处理余额调整 处理结算 x_user 失败了:  id = ", thirdId, err)
			return err
		}
		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance,
			"Amount":       amount,
			"AfterAmount":  balance + amount,
			"Reason":       utils.BalanceCReasonPPAdjustment,
			"Memo":         l.brandName + " bonusWin,thirdId:" + thirdId,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Error(l.brandName, "结算 处理余额调整 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
			return err
		}
		logs.Debug("pp sw :", "处理余额调整成功")
		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PP Refund事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		authResp.TransactionId = myTransactionId
		authResp.Error = 100
		authResp.Description = "事务回滚异常"
		ctx.RespJson(authResp)
		return
	}

	if err == nil {
		// 事务成功，准备响应数据
		authResp.TransactionId = myTransactionId
		authResp.Currency = l.currency
		authResp.Cash = balance2 + amount
		authResp.Error = 0
		authResp.Description = "成功"
		ctx.RespJson(authResp)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PPConfig] Adjustment 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PPConfig] Adjustment 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// RoundDetails
func (l *PPConfig) RoundDetails(ctx *abugo.AbuHttpContent) {
	type resp struct {
		Error       int    `json:"error"`
		Description string `json:"description"`
	}
	authResp := resp{}
	authResp.Error = 0
	authResp.Description = "成功"
	ctx.RespJson(authResp)
	return
}

// 获取开奖结果 OpenHistoryExtended
func (l *PPConfig) getRoundDetail(reqUser, reqGameId string, reqRound int64, reqLang string) (detailUrl string, err error) {
	querydata := map[string]any{
		"secureLogin": l.secureLogin,
		"playerId":    reqUser,
		"gameId":      reqGameId,
		"roundId":     reqRound,
		"language":    reqLang,
	}
	querydata["hash"] = l.createHash(querydata)

	url := fmt.Sprintf("%s/IntegrationService/v3/http/HistoryAPI/OpenHistoryExtended/", l.url)
	reqBytes, _ := json.Marshal(querydata)
	logs.Info("pp  请求三方开奖结果 参数:", querydata)

	httpclient := httpc.DoRequest{
		UrlPath:    url,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error("pp  请求三方开奖结果 请求错误:", err.Error())
		return
	}
	logs.Info("pp  请求三方开奖结果 响应:", data)
	if errors, ok := data["error"]; ok {
		if errors.(string) != "0" {
			logs.Error("pp  请求三方开奖结果 请求错误:", errors, data["description"])
			err = fmt.Errorf("请求失败%s", data["description"])
			return
		}
	}
	detailUrl = data["url"].(string)
	return
}
