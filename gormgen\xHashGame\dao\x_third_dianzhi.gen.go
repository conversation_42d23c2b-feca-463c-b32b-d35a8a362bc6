// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdDianzhi(db *gorm.DB, opts ...gen.DOOption) xThirdDianzhi {
	_xThirdDianzhi := xThirdDianzhi{}

	_xThirdDianzhi.xThirdDianzhiDo.UseDB(db, opts...)
	_xThirdDianzhi.xThirdDianzhiDo.UseModel(&model.XThirdDianzhi{})

	tableName := _xThirdDianzhi.xThirdDianzhiDo.TableName()
	_xThirdDianzhi.ALL = field.NewAsterisk(tableName)
	_xThirdDianzhi.ID = field.NewInt64(tableName, "Id")
	_xThirdDianzhi.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdDianzhi.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdDianzhi.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdDianzhi.UserID = field.NewInt32(tableName, "UserId")
	_xThirdDianzhi.Brand = field.NewString(tableName, "Brand")
	_xThirdDianzhi.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdDianzhi.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdDianzhi.GameID = field.NewString(tableName, "GameId")
	_xThirdDianzhi.GameName = field.NewString(tableName, "GameName")
	_xThirdDianzhi.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdDianzhi.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdDianzhi.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdDianzhi.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdDianzhi.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdDianzhi.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdDianzhi.Currency = field.NewString(tableName, "Currency")
	_xThirdDianzhi.RawData = field.NewString(tableName, "RawData")
	_xThirdDianzhi.State = field.NewInt32(tableName, "State")
	_xThirdDianzhi.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdDianzhi.DataState = field.NewInt32(tableName, "DataState")
	_xThirdDianzhi.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdDianzhi.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdDianzhi.CSID = field.NewString(tableName, "CSId")
	_xThirdDianzhi.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdDianzhi.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdDianzhi.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdDianzhi.GameRst = field.NewString(tableName, "GameRst")
	_xThirdDianzhi.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdDianzhi.IP = field.NewString(tableName, "Ip")
	_xThirdDianzhi.Lang = field.NewString(tableName, "Lang")
	_xThirdDianzhi.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdDianzhi.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdDianzhi.BetType = field.NewInt32(tableName, "BetType")
	_xThirdDianzhi.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdDianzhi.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdDianzhi.fillFieldMap()

	return _xThirdDianzhi
}

type xThirdDianzhi struct {
	xThirdDianzhiDo xThirdDianzhiDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	ThirdRefID     field.String  // 三方备用注单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32   // 状态
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdDianzhi) Table(newTableName string) *xThirdDianzhi {
	x.xThirdDianzhiDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdDianzhi) As(alias string) *xThirdDianzhi {
	x.xThirdDianzhiDo.DO = *(x.xThirdDianzhiDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdDianzhi) updateTableName(table string) *xThirdDianzhi {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdDianzhi) WithContext(ctx context.Context) *xThirdDianzhiDo {
	return x.xThirdDianzhiDo.WithContext(ctx)
}

func (x xThirdDianzhi) TableName() string { return x.xThirdDianzhiDo.TableName() }

func (x xThirdDianzhi) Alias() string { return x.xThirdDianzhiDo.Alias() }

func (x xThirdDianzhi) Columns(cols ...field.Expr) gen.Columns {
	return x.xThirdDianzhiDo.Columns(cols...)
}

func (x *xThirdDianzhi) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdDianzhi) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdDianzhi) clone(db *gorm.DB) xThirdDianzhi {
	x.xThirdDianzhiDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdDianzhi) replaceDB(db *gorm.DB) xThirdDianzhi {
	x.xThirdDianzhiDo.ReplaceDB(db)
	return x
}

type xThirdDianzhiDo struct{ gen.DO }

func (x xThirdDianzhiDo) Debug() *xThirdDianzhiDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdDianzhiDo) WithContext(ctx context.Context) *xThirdDianzhiDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdDianzhiDo) ReadDB() *xThirdDianzhiDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdDianzhiDo) WriteDB() *xThirdDianzhiDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdDianzhiDo) Session(config *gorm.Session) *xThirdDianzhiDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdDianzhiDo) Clauses(conds ...clause.Expression) *xThirdDianzhiDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdDianzhiDo) Returning(value interface{}, columns ...string) *xThirdDianzhiDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdDianzhiDo) Not(conds ...gen.Condition) *xThirdDianzhiDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdDianzhiDo) Or(conds ...gen.Condition) *xThirdDianzhiDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdDianzhiDo) Select(conds ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdDianzhiDo) Where(conds ...gen.Condition) *xThirdDianzhiDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdDianzhiDo) Order(conds ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdDianzhiDo) Distinct(cols ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdDianzhiDo) Omit(cols ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdDianzhiDo) Join(table schema.Tabler, on ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdDianzhiDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdDianzhiDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdDianzhiDo) Group(cols ...field.Expr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdDianzhiDo) Having(conds ...gen.Condition) *xThirdDianzhiDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdDianzhiDo) Limit(limit int) *xThirdDianzhiDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdDianzhiDo) Offset(offset int) *xThirdDianzhiDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdDianzhiDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdDianzhiDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdDianzhiDo) Unscoped() *xThirdDianzhiDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdDianzhiDo) Create(values ...*model.XThirdDianzhi) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdDianzhiDo) CreateInBatches(values []*model.XThirdDianzhi, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdDianzhiDo) Save(values ...*model.XThirdDianzhi) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdDianzhiDo) First() (*model.XThirdDianzhi, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdDianzhi), nil
	}
}

func (x xThirdDianzhiDo) Take() (*model.XThirdDianzhi, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdDianzhi), nil
	}
}

func (x xThirdDianzhiDo) Last() (*model.XThirdDianzhi, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdDianzhi), nil
	}
}

func (x xThirdDianzhiDo) Find() ([]*model.XThirdDianzhi, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdDianzhi), err
}

func (x xThirdDianzhiDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdDianzhi, err error) {
	buf := make([]*model.XThirdDianzhi, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdDianzhiDo) FindInBatches(result *[]*model.XThirdDianzhi, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdDianzhiDo) Attrs(attrs ...field.AssignExpr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdDianzhiDo) Assign(attrs ...field.AssignExpr) *xThirdDianzhiDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdDianzhiDo) Joins(fields ...field.RelationField) *xThirdDianzhiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdDianzhiDo) Preload(fields ...field.RelationField) *xThirdDianzhiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdDianzhiDo) FirstOrInit() (*model.XThirdDianzhi, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdDianzhi), nil
	}
}

func (x xThirdDianzhiDo) FirstOrCreate() (*model.XThirdDianzhi, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdDianzhi), nil
	}
}

func (x xThirdDianzhiDo) FindByPage(offset int, limit int) (result []*model.XThirdDianzhi, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdDianzhiDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdDianzhiDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdDianzhiDo) Delete(models ...*model.XThirdDianzhi) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdDianzhiDo) withDO(do gen.Dao) *xThirdDianzhiDo {
	x.DO = *do.(*gen.DO)
	return x
}
