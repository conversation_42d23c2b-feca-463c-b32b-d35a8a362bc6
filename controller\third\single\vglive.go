package single

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	"github.com/robfig/cron/v3"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

const cacheKeyVG = "cacheKeyVG:"

var (
	vgTableMutex sync.Mutex // 确保线程安全
)

func NewVGSingleService(params VGSingleConfig, fc func(int) error) *VGSingleService {
	brand := "vglive"
	var games []VGGame
	err := server.Db().GormDao().Table("x_game_list").Select("GameId", "Name").Where("Brand = ?", brand).Scan(&games).Error
	if err != nil {
		logs.Error("VGLive_single 获取 game id 失败 err=", err.Error())
	}
	logs.Info("VGLive_single 获取 game id 成功, games=", games)
	c := cron.New()
	vg := VGSingleService{params, brand, fc, c, nil, games}
	c.AddFunc("@every 5m", vg.getTableID)
	c.Start()
	vg.getTableID()
	return &vg
}

func (l *VGSingleService) getTableID() {
	vgReqData := map[string]string{
		"agent": l.Agent,
		"sign":  utils.Md5V(l.Agent + l.ApiKey),
	}
	reqBytes, _ := json.Marshal(vgReqData)
	payload := bytes.NewReader(reqBytes)
	logs.Info("VGLive_single 桌号列表 开始 url=", l.ApiDomain, " reqdata=", string(reqBytes))
	url := fmt.Sprintf("%s/vg/table/list", l.ApiDomain)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("VGLive_single 桌号列表 请求错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("VGLive_single 桌号列表 读取响应错误 err=", err.Error())
		return
	}
	respData := VGTableListResponse{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		if len(respBytes) > 500 {
			respBytes = respBytes[:500]
		}
		logs.Error("VGLive_single 桌号列表 解析响应消息体错误 err=", err.Error(), "respBytes=", string(respBytes))
		return
	}
	logs.Info("VGLive_single 桌号列表 请求成功 respBytes=", string(respBytes))
	if respData.Code != 1000 {
		logs.Error("VGLive_single 桌号列表 失败 respData.Code=", respData.Code)
		return
	}
	vgTableMutex.Lock()
	defer vgTableMutex.Unlock()
	l.TableID = make(map[string]VGTable, len(respData.Data))
	for _, table := range respData.Data {
		l.TableID[table.TableName] = table
	}

	var ids []string
	err = server.Db().GormDao().Table("x_game_list").Select("GameId").Where("OpenState = 1 and Brand=?", l.BrandName).Find(&ids).Error
	if err != nil {
		logs.Error("VGLive_single 数据库桌号列表 失败 error=", err)
		return
	}
	for _, id := range ids {
		State := 2
		for _, table := range respData.Data {
			if id == table.TableId {
				State = 1
				break
			}
		}
		err := server.Db().GormDao().Table("x_game_list").Select("GameId").Where("GameId=? and Brand=?", id, l.BrandName).Update("State", State).Error
		if err != nil {
			logs.Error("VGLive_single 更新数据库桌号列表 失败 error=", err, " GameId=", id, " State=", State)
			return
		}
	}
}

func (l *VGSingleService) getTableByName(name string) (VGTable, bool) {
	vgTableMutex.Lock()
	defer vgTableMutex.Unlock()
	if l.TableID == nil {
		return VGTable{}, false
	}
	table, ok := l.TableID[name]
	if !ok {
		return VGTable{}, false
	}
	return table, true
}

func (l *VGSingleService) username(userId int) string {
	return fmt.Sprintf("%d%s", userId, l.Suffix)
}

// 注册用户
func (l *VGSingleService) signUp(userId int) bool {
	vgReqData := VGSignUpRequest{
		Agent:     l.Agent,
		BetLimit:  "J1",
		LoginName: l.username(userId),
	}
	vgReqData.Sign(l.ApiKey)

	reqBytes, _ := json.Marshal(vgReqData)
	payload := bytes.NewReader(reqBytes)
	logs.Info("VGLive_single 用户注册 开始 userId=", userId, " url=", l.ApiDomain, " reqdata=", string(reqBytes))
	url := fmt.Sprintf("%s/vg/sign-up", l.ApiDomain)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("VGLive_single 用户注册 请求错误 userId=", userId, " err=", err.Error())
		return false
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("VGLive_single 用户注册 读取响应错误 userId=", userId, " err=", err.Error())
		return false
	}
	logs.Info("VGLive_single 用户注册 请求成功 userId=", userId, " respBytes=", string(respBytes))
	respData := VGSignUpResponse{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		if len(respBytes) > 500 {
			respBytes = respBytes[:500]
		}
		logs.Error("VGLive_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error(), "respBytes=", string(respBytes))
		return false
	}
	return respData.Code == 1000
}

// 登入游戏
func (l *VGSingleService) Login(ctx *abugo.AbuHttpContent) {
	errorTimes := 0
	errCode := 0
	reqData := VGLoginRequestData{}
	err := ctx.RequestData(&reqData)
	if err != nil {
		logs.Error("VGLive_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errCode, "参数错误,请稍后再试1")
		return
	}
	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("VGLive_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errCode, "登录过期,请重新登录2")
		return
	}
	userId := token.UserId
	if err, errCode = base.IsLoginByUserId(cacheKeyVG, userId); err != nil {
		logs.Error("VGLive_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errCode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.BrandName, reqData.GameId); err != nil {
		logs.Error("VGLive_single 登录游戏 权限检查错误 userId=", userId, " gameId=", reqData.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errCode, err.Error())
		return
	} else if !allowed {
		logs.Error("VGLive_single 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqData.GameId, " hint=", hint)
		ctx.RespErrString(true, &errCode, hint)
		return
	}
	for {
		errorTimes++
		if errorTimes > 2 {
			logs.Error("VGLive_single 登录游戏 失败次数过多")
			ctx.RespErrString(true, &errCode, "失败次数过多,请稍后再试")
			return
		}

		vgReqData := VGSignInRequest{
			Agent:     l.Agent,
			Language:  reqData.Language,
			LoginName: l.username(userId),
			ReturnUrl: reqData.RedirectUrl,
			Token:     l.getTokenByUser(userId, token.Account),
			TableId:   reqData.GameId,
		}
		vgReqData.Sign(l.ApiKey)

		reqBytes, _ := json.Marshal(vgReqData)
		payload := bytes.NewReader(reqBytes)
		logs.Info("VGLive_single 登录游戏 开始 userId=", userId, " url=", l.ApiDomain, " reqdata=", string(reqBytes))
		url := fmt.Sprintf("%s/vg/sign-in", l.ApiDomain)
		client := &http.Client{}
		req, _ := http.NewRequest(http.MethodPost, url, payload)
		req.Header.Add("Content-Type", "application/json")
		resp, err := client.Do(req)
		if err != nil {
			logs.Error("VGLive_single 登录游戏 请求错误 userId=", userId, " err=", err.Error())
			ctx.RespErrString(true, &errCode, "网络错误,请稍后再试4")
			return
		}
		defer resp.Body.Close()
		respBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			ctx.RespErrString(true, &errCode, "系统错误,请稍后再试5")
			return
		}
		logs.Info("VGLive_single 登录游戏 请求成功 userId=", userId, " respBytes=", string(respBytes))

		respData := VGSignInResponse{}
		err = json.Unmarshal(respBytes, &respData)
		if err != nil {
			if len(respBytes) > 500 {
				respBytes = respBytes[:500]
			}
			logs.Error("VGLive_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error(), "respBytes=", string(respBytes))
			ctx.RespErrString(true, &errCode, "系统错误,请稍后再试6")
			return
		}
		switch respData.Code {
		case 1000:
			// 成功
			ctx.RespOK(respData.Data.URL)
			return
		case 5009:
			// 玩家账号不存在
			if l.signUp(userId) {
				continue
			}
		}
		logs.Error("VGLive_single 登录游戏 登录失败 userId=", userId, " 错误码=", respData.Code, " Message=", respData.Message)
		// ctx.RespErrString(true, &errCode, "系统错误,请稍后再试7")
	}
}

// 余额查询
func (l *VGSingleService) Balance(ctx *abugo.AbuHttpContent) {
	respData := VGBetResponse{Code: 0, Balance: 0}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("VGLive_single 余额查询 读取请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Other_Error
		ctx.RespJson(respData)
		return
	}
	logs.Info("VGLive_single 余额查询 请求消息体 reqData=", string(bodyBytes))

	reqData := VGBalanceRequest{}
	err = json.Unmarshal(bodyBytes, &reqData)
	if err != nil {
		logs.Error("VGLive_single 余额查询 解析请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}

	// 检查签名
	if !reqData.IsValid(l.ApiKey) {
		logs.Error("VGLive_single 余额查询 请求参数不合法 reqData=", string(bodyBytes))
		respData.Code = VG_Code_Fail_Signature_Error
		ctx.RespJson(respData)
		return
	}

	// 检查代理名称
	if reqData.Agent != l.Agent {
		logs.Error("VGLive_single 余额查询 商户名称不正确, agent=", reqData.Agent)
		respData.Code = VG_Code_Fail_Agent_Error
		ctx.RespJson(respData)
		return
	}

	// 检查 token
	userId, _ := l.getUserByToken(reqData.Token)
	loginName := l.username(userId)
	if loginName != reqData.LoginName {
		logs.Error("VGLive_single 余额查询 用户比对失败")
		respData.Code = VG_Code_Fail_User_Not_Exist
		ctx.RespJson(respData)
		return
	}
	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("VGLive_single 余额查询 获取用户余额失败 err=", err.Error())
		respData.Code = VG_Code_Fail_User_Not_Exist
		ctx.RespJson(respData)
		return
	}
	respData.Balance = math.Floor(userBalance.Amount*100) / 100 // 保留两位小数
	respData.Code = VG_Code_OK
	logs.Info("VGLive_single 余额查询 成功 userId=", userId, " balance=", respData.Balance)
	ctx.RespJson(respData)
}

// 下注
func (l *VGSingleService) Bet(ctx *abugo.AbuHttpContent) {
	errCode := 0
	respData := VGBetResponse{Code: 0, Balance: 0}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("VGLive_single 下注 读取请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Other_Error
		ctx.RespJson(respData)
		return
	}
	logs.Info("VGLive_single 下注 请求消息体 reqData=", string(bodyBytes))

	reqData := VGBetRequest{}
	err = json.Unmarshal(bodyBytes, &reqData)
	if err != nil {
		logs.Error("VGLive_single 下注 解析请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}

	// 检查签名
	if !reqData.IsValid(l.ApiKey) {
		logs.Error("VGLive_single 下注 请求参数不合法")
		respData.Code = VG_Code_Fail_Signature_Error
		ctx.RespJson(respData)
		return
	}

	// 检查代理名称
	if reqData.Agent != l.Agent {
		logs.Error("VGLive_single 下注 商户名称不正确, agent=", reqData.Agent)
		respData.Code = VG_Code_Fail_Agent_Error
		ctx.RespJson(respData)
		return
	}

	// 检查桌号
	tableIdFound := false
	for _, game := range l.Games {
		if game.GameID == reqData.Detail.TableIdx {
			tableIdFound = true
			break
		}
	}
	if !tableIdFound {
		logs.Error("VGLive_single 下注 桌号不正确, tableIdx=", reqData.Detail.TableIdx)
		errCode = VG_Code_Fail_Table_Error
		ctx.RespErrString(true, &errCode, "桌号不正确")
		return
	}

	// 检查 token
	userId, _ := l.getUserByToken(reqData.Token)
	loginName := l.username(userId)
	if loginName != reqData.LoginName {
		logs.Error("VGLive_single 下注 用户比对失败")
		errCode = VG_Code_Fail_User_Not_Exist
		ctx.RespErrString(true, &errCode, "用户不存在或者已登出")
		return
	}

	// 检查下注量
	betAmount, err := reqData.Amount.Float64()
	if err != nil || betAmount < 0 {
		logs.Error("VGLive_single 下注 数量错误, err=", err.Error(), " loginname=", reqData.LoginName, " roundid=", reqData.RoundID, " transid=", reqData.TransID, " amount=", reqData.Amount)
		errCode = VG_Code_Fail_Signature_Error
		ctx.RespErrString(true, &errCode, "下注数量错误")
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId           //用户ID
	cacheSignKey.Brand = l.BrandName       //三方厂商名称
	cacheSignKey.ThirdId = reqData.TransID //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqData, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error(l.BrandName, " 检测到重复请求 thirdId=", reqData.TransID, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqData, respData, nil); e != nil {
			logs.Error(l.BrandName, " 设置缓存出错 thirdId=", reqData.TransID, " err=", e.Error())
		}
	}()
	gameName := ""

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("VGLive_single 下注 获取用户余额失败 transid=", reqData.TransID, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respData.Code = VG_Code_Fail_User_Not_Exist
			} else {
				respData.Code = VG_Code_Fail_System_Error
			}
			return e
		}
		// 检查游戏权限
		gameId := reqData.Detail.TableIdx
		if allowed, hint, err := base.BeforeEnterGameId(int(userBalance.UserId), l.BrandName, gameId); err != nil {
			logs.Error("VGLive_single 下注 权限检查错误 userId=", userBalance.UserId, " gameId=", gameId, " err=", err.Error())
			respData.Code = VG_Code_Fail_User_Not_Exist
			return err
		} else if !allowed {
			logs.Error("VGLive_single 下注 权限被拒绝 userId=", userBalance.UserId, " gameId=", gameId, " hint=", hint)
			respData.Code = VG_Code_Fail_User_Not_Exist
			return errors.New("权限被拒绝")
		}

		if betAmount > userBalance.Amount {
			e = errors.New("余额不足")
			respData.Code = WE_Code_Fail_Not_Enough_Balance
			return e
		}
		//获取投注渠道
		channelId := base.GetUserChannelId(ctx, &userBalance)
		respData.Balance = userBalance.Amount - betAmount
		thirdId := reqData.TransID
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		gameName = l.GetGameNameById(reqData.Detail.TableIdx)
		// 创建注单
		order := thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: channelId,
			UserId:       userBalance.UserId,
			Brand:        l.BrandName,
			ThirdId:      thirdId,
			GameId:       reqData.Detail.TableIdx,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.Currency,
			RawData:      string(bodyBytes),
			State:        1,
			Fee:          0,
			DataState:    -1, //未开奖
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_third_live_pre_order").Create(&order).Error
		if e != nil {
			// 订单已存在不能跳过 否则 三方会认为下注成功，暂时修改为只允许下注一笔
			logs.Error("VGLive_single 下注确认 创建订单失败  thirdId=", thirdId, " order=", order, " error=", e)
			respData.Code = VG_Code_Fail_Create_Pre_Bet
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, betAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("VGLive_single 下注确认 扣款失败  userId=", userId, " thirdId=", thirdId, " betAmount=", betAmount, " err=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -betAmount,
			AfterAmount:  userBalance.Amount - betAmount,
			Reason:       utils.BalanceCReasonVGBet,
			Memo:         l.BrandName + " bet,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("VGLive_single 下注确认 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
			respData.Code = VG_Code_Fail_System_Error
			return e
		}

		respData.Code = VG_Code_OK
		respData.Balance = math.Floor(amountLog.AfterAmount*100) / 100
		return nil
	})
	if err != nil {
		logs.Error("DVGLive_single 下注确认 事务处理失败 err=", err)
		ctx.RespJson(respData)
		return
	}
	if l.ThirdGamePush != nil {
		// 获取用户对应的配置
		// userConfig := l.getRouteConfig(userId)
		// logs.Info("gfg单一钱包 Bet 下注成功 推送下注事件", gameName, " betAmount=", betAmount)
		l.ThirdGamePush.PushBetEvent(userId, gameName, l.BrandName, betAmount, l.Currency, l.BrandName, reqData.TransID, 5)
	}
	// 发送余额变动通知
	go func(notifyUserId int64) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
			if tmpErr != nil {
				logs.Error("[ERROR][VGLive_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][VGLive_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(int64(userId))

	ctx.RespJson(respData)
	logs.Info("VGLive_single 下注确认 响应成功 respData=", respData)
}

// 派彩
func (l *VGSingleService) Win(ctx *abugo.AbuHttpContent) {
	respData := VGWinResponse{Code: 0, Balance: 0}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("VGLive_single 派彩 读取请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Other_Error
		ctx.RespJson(respData)
		return
	}
	logs.Info("VGLive_single 派彩 请求消息体 reqData=", string(bodyBytes))

	reqData := VGWinBetRequest{}
	err = json.Unmarshal(bodyBytes, &reqData)
	if err != nil {
		logs.Error("VGLive_single 派彩 解析请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}

	// 检查签名
	if !reqData.IsValid(l.ApiKey) {
		logs.Error("VGLive_single 派彩 请求参数不合法")
		respData.Code = VG_Code_Fail_Signature_Error
		ctx.RespJson(respData)
		return
	}

	// 检查代理名称
	if reqData.Agent != l.Agent {
		logs.Error("VGLive_single 派彩 商户名称不正确, agent=", reqData.Agent)
		respData.Code = VG_Code_Fail_Agent_Error
		ctx.RespJson(respData)
		return
	}
	loginName := reqData.LoginName[:len(reqData.LoginName)-len(l.Suffix)]
	uid, err := strconv.Atoi(loginName)
	if err != nil {
		logs.Error("VGLive_single 派彩 LoginName 不合法 loginname=", loginName, " error=", err.Error())
		respData.Code = VG_Code_Fail_User_Account_Error
		ctx.RespJson(respData)
		return
	}
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = uid
	cacheSignKey.Brand = l.BrandName       //三方厂商名称
	cacheSignKey.ThirdId = reqData.TransID //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqData, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error("VGLive_single 派彩 检测到重复请求 thirdId=", reqData.TransID, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqData, respData, nil); e != nil {
			logs.Error("VGLive_single 派彩 设置缓存出错 thirdId=", reqData.TransID, " err=", e.Error())
		}
	}()

	winAmount, err := reqData.Amount.Float64()
	if err != nil {
		logs.Error("VG 派彩 派彩金额解析失败 thirdId=", reqData.TransID, " reqData.amount=", reqData.Amount, " err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}
	userId := 0
	thirdId := ""

	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e := tx.Table("x_third_live_pre_order").Where("ThirdId=? and Brand=?", reqData.TransID, l.BrandName).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
				logs.Error("VGLive_single 派彩 订单不存在 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
				respData.Code = VG_Code_Fail_Record_Not_Found
				return e
			}
			logs.Error("VGLive_single 派彩 查询订单失败 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		// 检查玩家ID
		if l.username(order.UserId) != reqData.LoginName {
			logs.Error("VGLive_single 派彩 玩家比对失败 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_User_Account_Error
			return errors.New("玩家比对失败")
		}
		userId = order.UserId
		// 检查订单状态
		if order.DataState == -2 {
			// 已取消
			logs.Error("VGLive_single 派彩 订单已取消 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_Bet_Cancelled
			return errors.New("订单已取消")
		} else if order.DataState == 1 {
			// 已结算
			logs.Error("VGLive_single 派彩 订单已结算 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_Bet_Settled
			return errors.New("订单已结算")
		} else if order.DataState != -1 {
			logs.Error("VGLive_single 派彩 订单已结算 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_Bet_Settled
			return errors.New("订单已结算")
		}
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", order.UserId).First(&userBalance).Error
		if e != nil {
			logs.Error("VGLive_single 派彩 获取用户余额失败 thirdId=", reqData.TransID, " userId=", order.UserId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respData.Code = VG_Code_Fail_User_Not_Exist
			} else {
				respData.Code = VG_Code_Fail_System_Error
			}
			return e
		}
		betCtx := reqData.GameRecord2Str(strconv.Itoa(order.UserId), "已派奖")
		thirdId = order.ThirdId
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

		// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
		validBet := math.Abs(winAmount - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}
		table, _ := l.getTableByName(reqData.Detail.TableIdx)
		// 更新注单状态
		e = tx.Table("x_third_live_pre_order").Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState": 1,
			"ThirdTime": thirdTime,
			"ValidBet":  validBet,
			"WinAmount": reqData.Amount,
			// "Currency":   reqData.Detail.Currency,
			"GameId":     table.TableId,
			"BetCtx":     betCtx,
			"BetCtxType": 3,
			"RawData":    string(bodyBytes),
		}).Error
		if e != nil {
			logs.Error("VGLive_single 派彩 更新订单状态失败 thirdId=", thirdId, " error=", e.Error())
			respData.Code = VG_Code_Fail_Update_Bet
			return e
		}
		order.DataState = 1
		order.ThirdTime = thirdTime
		order.ValidBet = validBet
		order.WinAmount = winAmount
		order.BetCtx = betCtx
		order.GameRst = betCtx
		order.BetCtxType = 3
		order.RawData = string(bodyBytes)
		order.Id = 0
		// order.Currency = reqData.Detail.Currency

		e = tx.Table("x_third_live").Create(&order).Error
		if e != nil {
			logs.Error("VGLive_single 派彩 创建正式表订单失败 thirdId=", thirdId, " order=", order, " error=", e.Error())
			respData.Code = VG_Code_Fail_Create_Bet
			return e
		}

		if winAmount != 0 { //金额不=0才需要更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId = ?", order.UserId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("VGLive_single 派彩 加扣款失败 userId=", order.UserId, " thirdId=", thirdId, " winAmount=", winAmount, " err=", e.Error())
				respData.Code = VG_Code_Fail_Update_Balance
				return e
			}
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       winAmount,
			AfterAmount:  userBalance.Amount + winAmount,
			Reason:       utils.BalanceCReasonVGWin,
			Memo:         l.BrandName + " settle,thirdId:" + order.ThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("VGLive_single 派彩 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
			respData.Code = VG_Code_Fail_Update_Balance
			return e
		}

		respData.Code = VG_Code_OK
		respData.Balance = math.Floor(amountLog.AfterAmount*100) / 100
		return nil
	})

	if err != nil {
		logs.Error("VGLive_single 派彩 事务处理失败 err=", err)
		ctx.RespJson(respData)
		return
	}

	// 推送派奖事件通知
	if winAmount > 0 && l.ThirdGamePush != nil {
		//l.thirdGamePush.PushRewardEvent(userId, betTran.GameName, l.brandName, requests.ValidBet, winAmount, l.currency)
		//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		l.ThirdGamePush.PushRewardEvent(5, l.BrandName, thirdId)
	}

	// 发送余额变动通知
	go func(notifyUserId int64) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
			if tmpErr != nil {
				logs.Error("[ERROR][VGLive_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][VGLive_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(int64(userId))

	ctx.RespJson(respData)
	logs.Info("VGLive_single 派彩 响应成功 respdata=", respData)
}

// 取消投注
func (l *VGSingleService) Cancel(ctx *abugo.AbuHttpContent) {
	respData := VGWinResponse{Code: 0, Balance: 0}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("VGLive_single 取消投注 读取请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Other_Error
		ctx.RespJson(respData)
		return
	}
	logs.Info("VGLive_single 取消投注 请求消息体 reqData=", string(bodyBytes))

	reqData := VGCancelBetRequest{}
	err = json.Unmarshal(bodyBytes, &reqData)
	if err != nil {
		logs.Error("VGLive_single 取消投注 解析请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}

	// 检查签名
	if !reqData.IsValid(l.ApiKey) {
		logs.Error("VGLive_single 取消投注 请求参数不合法")
		respData.Code = VG_Code_Fail_Signature_Error
		ctx.RespJson(respData)
		return
	}

	// 检查代理名称
	if reqData.Agent != l.Agent {
		logs.Error("VGLive_single 取消投注 商户名称不正确, agent=", reqData.Agent)
		respData.Code = VG_Code_Fail_Agent_Error
		ctx.RespJson(respData)
		return
	}
	loginName := reqData.LoginName[:len(reqData.LoginName)-len(l.Suffix)]
	uid, err := strconv.Atoi(loginName)
	if err != nil {
		logs.Error("VGLive_single 取消投注 LoginName 不合法 loginname=", loginName, " error=", err.Error())
		respData.Code = VG_Code_Fail_User_Account_Error
		ctx.RespJson(respData)
		return
	}
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = uid
	cacheSignKey.Brand = l.BrandName       //三方厂商名称
	cacheSignKey.ThirdId = reqData.TransID //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqData, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error("VGLive_single 取消投注 检测到重复请求 thirdId=", reqData.TransID, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqData, respData, nil); e != nil {
			logs.Error("VGLive_single 取消投注 设置缓存出错 thirdId=", reqData.TransID, " err=", e.Error())
		}
	}()

	var userId = 0
	// 开始取消投注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e := tx.Table("x_third_live_pre_order").Where("ThirdId=? and Brand=?", reqData.TransID, l.BrandName).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
				logs.Error("VGLive_single 取消投注 订单不存在 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
				respData.Code = VG_Code_Fail_Record_Not_Found
				return e
			}
			logs.Error("VGLive_single 取消投注 查询订单失败 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		// 检查玩家ID
		if l.username(order.UserId) != reqData.LoginName {
			logs.Error("VGLive_single 取消投注 玩家比对失败 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_User_Account_Error
			return errors.New("玩家比对失败")
		}
		userId = order.UserId
		// 检查订单状态
		if order.DataState == -2 {
			// 已取消
			logs.Error("VGLive_single 取消投注 订单已取消 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_Bet_Cancelled
			return errors.New("订单已取消")
		}

		if order.DataState == 0 {
			betAmount, err := reqData.Amount.Float64()
			if err != nil {
				logs.Error("VGLive_single 取消投注 投注金额解析失败 thirdId=", reqData.TransID, " reqData.amount=", reqData.Amount)
				respData.Code = VG_Code_Fail_Illegal_Parameter
				return err
			}
			if order.BetAmount != betAmount {
				logs.Error("VGLive_single 取消投注 投注金额不同 thirdId=", reqData.TransID, " reqData.amount=", reqData.Amount, " order.amount=", order.BetAmount)
				respData.Code = VG_Code_Fail_Illegal_Amount
				return err
			}
		} else {
			amountDiff, err := reqData.Amount.Float64()
			if err != nil {
				logs.Error("VGLive_single 取消投注 派彩金额解析失败 thirdId=", reqData.TransID, " reqData.amount=", reqData.Amount)
				respData.Code = VG_Code_Fail_Illegal_Parameter
				return err
			}
			if order.WinAmount-order.BetAmount != amountDiff {
				logs.Error("VGLive_single 取消投注 派彩金额不同 thirdId=", reqData.TransID, " reqData.Amount=", reqData.Amount, " order.winAmount=", order.WinAmount-order.BetAmount)
				respData.Code = VG_Code_Fail_Illegal_Amount
				return err
			}
		}

		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", order.UserId).First(&userBalance).Error
		if e != nil {
			logs.Error("WE_single 取消投注 获取用户余额失败 thirdId=", reqData.TransID, " userId=", order.UserId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respData.Code = VG_Code_Fail_User_Not_Exist
			} else {
				respData.Code = VG_Code_Fail_System_Error
			}
			return e
		}
		betCtx := reqData.GameRecord2Str(strconv.Itoa(order.UserId), order.DataState == 1, "已取消投注")
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		table, _ := l.getTableByName(reqData.Detail.TableIdx)
		// 更新注单状态
		e = tx.Table("x_third_live_pre_order").Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":      -2, // -2-已取消 -1-未开奖 1-已结算
			"BetAmount":      0,
			"WinAmount":      0,
			"ValidBet":       0,
			"ValidBetAmount": 0,
			"ThirdTime":      thirdTime,
			"RawData":        string(bodyBytes),
			"BetCtx":         betCtx,
			"GameId":         table.TableId,
			"BetCtxType":     3,
		}).Error
		if e != nil {
			logs.Error("VGLive_single 取消投注 更新订单状态失败 thirdId=", order.ThirdId, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		if order.DataState == 1 {
			e = tx.Table("x_third_live").Where("ThirdId=? and Brand=?", reqData.TransID, l.BrandName).Updates(map[string]interface{}{
				"DataState":      -2, // -2-已取消 -1-未开奖 1-已结算
				"BetAmount":      0,
				"WinAmount":      0,
				"ValidBet":       0,
				"ValidBetAmount": 0,
				"ThirdTime":      thirdTime,
				"RawData":        string(bodyBytes),
				"BetCtx":         betCtx,
				"GameRst":        betCtx,
				"GameId":         table.TableId,
				"BetCtxType":     3,
			}).Error

			if e != nil {
				logs.Error("VGLive_single 取消投注 更新订单状态失败 thirdId=", order.ThirdId, " error=", e.Error())
				respData.Code = VG_Code_Fail_System_Error
			}
		}
		resultTmp := tx.Table("x_user").Where("UserId = ?", order.UserId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", order.BetAmount-order.WinAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && order.BetAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("VGLive_single 取消投注 加款失败 thirdId=", order.ThirdId, " userId=", order.UserId, " RefundMoney=", order.BetAmount, " err=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       order.BetAmount - order.WinAmount,
			AfterAmount:  userBalance.Amount + order.BetAmount - order.WinAmount,
			Reason:       utils.BalanceCReasonVGCancel,
			Memo:         l.BrandName + " cancel,thirdId:" + order.ThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("VGLive_single 取消投注 创建账变记录失败 thirdId=", order.ThirdId, " amountLog=", amountLog, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		respData.Code = VG_Code_OK
		respData.Balance = math.Floor(amountLog.AfterAmount*100) / 100
		return nil
	})

	if err != nil {
		logs.Error("VGLive_single 取消投注 事务处理失败 err=", err)
		ctx.RespJson(respData)
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int64) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
			if tmpErr != nil {
				logs.Error("[ERROR][VGLive_single] 取消投注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][VGLive_single] 取消投注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(int64(userId))

	ctx.RespJson(respData)
	logs.Info("VGLive_single 取消投注 响应成功 respdata=", respData)
}

// 重新派彩
func (l *VGSingleService) ReSettle(ctx *abugo.AbuHttpContent) {
	var respData = VGResponse{Code: 0}
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("VGLive_single 重新派彩 读取请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}
	logs.Info("VGLive_single 重新派彩 请求消息体 reqData=", string(bodyBytes))

	reqData := VGReSettleRequest{}
	err = json.Unmarshal(bodyBytes, &reqData)
	if err != nil {
		logs.Error("VGLive_single 重新派彩 解析请求消息体错误 err=", err.Error())
		respData.Code = VG_Code_Fail_Illegal_Parameter
		ctx.RespJson(respData)
		return
	}

	// 检查签名
	if !reqData.IsValid(l.ApiKey) {
		logs.Error("VGLive_single 重新派彩 请求参数不合法")
		respData.Code = VG_Code_Fail_Signature_Error
		ctx.RespJson(respData)
		return
	}

	// 检查代理名称
	if reqData.Agent != l.Agent {
		logs.Error("VGLive_single 重新派彩 商户名称不正确, agent=", reqData.Agent)
		respData.Code = VG_Code_Fail_Agent_Error
		ctx.RespJson(respData)
		return
	}
	loginName := reqData.LoginName[:len(reqData.LoginName)-len(l.Suffix)]
	uid, err := strconv.Atoi(loginName)
	if err != nil {
		logs.Error("VGLive_single 重新派彩 loginName 不合法 loginname=", loginName, " error=", err.Error())
		respData.Code = VG_Code_Fail_User_Account_Error
		ctx.RespJson(respData)
		return
	}
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = uid
	cacheSignKey.Brand = l.BrandName       //三方厂商名称
	cacheSignKey.ThirdId = reqData.TransID //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqData, nil)
	if duplicateResult != nil && err == nil {
		ctx.RespJson(duplicateResult)
		logs.Error("VGLive_single 重新派彩 检测到重复请求 thirdId=", reqData.TransID, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqData, respData, nil); e != nil {
			logs.Error("VGLive_single 重新派彩 设置缓存出错 thirdId=", reqData.TransID, " err=", e.Error())
		}
	}()
	var userId = 0
	// 开始取消投注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e := tx.Table("x_third_live_pre_order").Where("ThirdId=? and Brand=?", reqData.TransID, l.BrandName).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
				logs.Error("VGLive_single 重新派彩 订单不存在 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
				respData.Code = VG_Code_Fail_Record_Not_Found
				return e
			}
			logs.Error("VGLive_single 重新派彩 查询订单失败 thirdId=", reqData.TransID, " order=", order, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		// 检查玩家ID
		if l.username(order.UserId) != reqData.LoginName {
			logs.Error("VGLive_single 重新派彩 玩家比对失败 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_User_Account_Error
			return errors.New("玩家比对失败")
		}
		userId = order.UserId
		// 检查订单状态
		if order.DataState != 1 {
			// 未结算
			logs.Error("VGLive_single 重新派彩 订单未结算 thirdId=", reqData.TransID, " reqData.LoginName=", reqData.LoginName, " order.UserId=", order.UserId)
			respData.Code = VG_Code_Fail_Not_Settled
			return errors.New("订单未结算")
		}
		// 检查订单
		winAmount, err := reqData.Amount.Float64()
		if err != nil {
			logs.Error("VGLive_single 重新派彩 最后派彩金额解析失败 thirdId=", reqData.TransID, " error=", err.Error())
			respData.Code = VG_Code_Fail_Illegal_Parameter
			return errors.New("最后派彩金额错误")
		}
		winAmountDiff := winAmount - order.WinAmount
		if winAmountDiff == 0 {
			logs.Info("VGLive_single 重新派彩 最后派彩金额与原金额相同 thirdId=", reqData.TransID, " winAmount=", winAmount, " order.WinAmount=", order.WinAmount)
			// respData.Code = VG_Code_Fail_Illegal_Parameter
			// return errors.New("最后派彩金额错误")
		}
		gameRst := reqData.GameRecord2Str(strconv.Itoa(order.UserId), "重新派彩")
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		// table, _ := l.getTableByName(reqData.Detail.TableIdx)
		// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
		validBet := math.Abs(winAmount - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}

		// 更新注单状态
		e = tx.Table("x_third_live_pre_order").Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState": 1, // -2-已取消 -1-未开奖 1-已结算
			// "BetAmount":      0,
			"WinAmount":      winAmount,
			"ValidBet":       validBet,
			"ValidBetAmount": validBet,
			"ThirdTime":      thirdTime,
			"GameRst":        gameRst,
			// "GameId":         table.TableId,
			"BetCtxType": 3,
		}).Error
		if e != nil {
			logs.Error("VGLive_single 重新派彩 更新订单状态失败 thirdId=", order.ThirdId, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}
		e = tx.Table("x_third_live").Where("ThirdId=? and Brand=?", reqData.TransID, l.BrandName).Updates(map[string]interface{}{
			"DataState": 1, // -2-已取消 -1-未开奖 1-已结算
			// "BetAmount":      0,
			"WinAmount":      winAmount,
			"ValidBet":       validBet,
			"ValidBetAmount": validBet,
			"ThirdTime":      thirdTime,
			"GameRst":        gameRst,
			// "GameId":         table.TableId,
			"BetCtxType": 3,
		}).Error
		if e != nil {
			logs.Error("VGLive_single 重新派彩 更新订单状态失败 thirdId=", order.ThirdId, " error=", e.Error())
			respData.Code = VG_Code_Fail_System_Error
			return e
		}

		if winAmountDiff != 0 {
			// 获取用户余额
			userBalance := thirdGameModel.UserBalance{}
			e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", order.UserId).First(&userBalance).Error
			if e != nil {
				logs.Error("WE_single 重新派彩 获取用户余额失败 thirdId=", reqData.TransID, " userId=", order.UserId, " err=", e.Error())
				if e == daogorm.ErrRecordNotFound {
					respData.Code = VG_Code_Fail_User_Not_Exist
				} else {
					respData.Code = VG_Code_Fail_System_Error
				}
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", order.UserId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", order.WinAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && order.WinAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("VGLive_single 重新派彩 扣款失败 thirdId=", order.ThirdId, " userId=", order.UserId, " RefundMoney=", order.WinAmount, " err=", e.Error())
				respData.Code = VG_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       order.WinAmount,
				AfterAmount:  userBalance.Amount - order.WinAmount,
				Reason:       utils.BalanceCReasonVGSettleCancel,
				Memo:         l.BrandName + " cancel,thirdId:" + order.ThirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("VGLive_single 重新派彩 创建账变记录失败 thirdId=", order.ThirdId, " amountLog=", amountLog, " error=", e.Error())
				respData.Code = VG_Code_Fail_System_Error
				return e
			}
			userBalance.Amount = amountLog.AfterAmount
			resultTmp = tx.Table("x_user").Where("UserId = ?", order.UserId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("VGLive_single 重新派彩 加款失败 thirdId=", order.ThirdId, " userId=", order.UserId, " winAmount=", winAmount, " err=", e.Error())
				respData.Code = VG_Code_Fail_System_Error
				return e
			}
			// 创建账变记录
			amountLog = thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount + winAmount,
				Reason:       utils.BalanceCReasonVGReSettle,
				Memo:         l.BrandName + " settle,thirdId:" + order.ThirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("VGLive_single 重新派彩 创建账变记录失败 thirdId=", order.ThirdId, " amountLog=", amountLog, " error=", e.Error())
				respData.Code = VG_Code_Fail_System_Error
				return e
			}

		}

		respData.Code = VG_Code_OK
		return nil
	})
	if err != nil {
		logs.Error("VGLive_single 重新派彩 事务处理失败 err=", err)
		ctx.RespJson(respData)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][VGLive_single] 重新派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][VGLive_single] 重新派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	ctx.RespJson(respData)
	logs.Info("VGLive_single 重新派彩 响应成功 respdata=", respData)
}

// 生成 vg token
func (l *VGSingleService) getTokenByUser(userId int, account string) (token string) {
	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyVG + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyVG + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("VGLive_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("VGLive_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "VG_" + uuid.NewString()
	tokenData := VGTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokenDataByte, _ := json.Marshal(tokenData)
	tokenDataStr := string(tokenDataByte)
	rKeyToken = cacheKeyVG + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("VGLive_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokenDataStr, 86401); err != nil {
		logs.Error("VGLive_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *VGSingleService) getUserByToken(token string) (userId int, account string) {
	if token == "" {
		return
	}
	rKeyToken := cacheKeyVG + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokenData := VGTokenData{}
		err := json.Unmarshal(v.([]byte), &tokenData)
		if err != nil {
			logs.Error("VGLive_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyVG + "uid:" + fmt.Sprintf("%d", tokenData.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("VGLive_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("VGLive_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokenData.UserId
		account = tokenData.Account
	} else {
		logs.Error("VGLive_single getUserIdBy token=", token, " 不存在")
	}
	return
}

func (*VGSingleService) VGGameResult2Str(data VGBetRespDetail) (res string) {
	dataMap := map[string]any{
		"投注号":  data.BetID,
		"游戏局号": data.RoundID,
		"玩家名称": data.Username,
		"桌别":   data.Casino,
		"投注金额": data.Bet,
		"派彩金额": data.Win,
		"币别":   data.Currency,
		"桌号":   data.TableIdx,
		"状态":   data.Status,
		"玩家IP": data.IP,
		"投注类型": data.BetType,
		"派彩结果": data.BetResult,
		"开牌结果": data.Detail,
	}
	jsonString, _ := json.Marshal(dataMap)
	return string(jsonString)
}

func (l *VGSingleService) GetGameNameById(id string) (res string) {
	for _, game := range l.Games {
		if game.GameID == id {
			return game.GameName
		}
	}
	return ""
}
