package paycontroller

import (
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"io"
	"math"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

var PraxisPay = new(praxisPay)

type praxisPay struct {
	Base
}

// PraxisConfig Praxis支付配置
// @description Praxis支付配置结构体
type PraxisConfig struct {
	MerchantId     string `json:"merchant_id"`     // 商户ID
	ApplicationKey string `json:"application_key"` // 应用密钥
	MerchantSecret string `json:"merchant_secret"` // 商户密钥
	ApiUrl         string `json:"api_url"`         // API地址
	CbUrl          string `json:"cb_url"`          // 回调地址
	ReturnUrl      string `json:"return_url"`      // 返回地址
}

// PraxisResponse Praxis API响应
// @description Praxis API响应结构体
type PraxisResponse struct {
	Status      int    `json:"status"`       // 状态码
	Description string `json:"description"`  // 状态描述
	RedirectURL string `json:"redirect_url"` // 重定向URL
	Customer    struct {
		CustomerToken     string `json:"customer_token"`     // 客户令牌
		Country           string `json:"country"`            // 国家代码
		FirstName         string `json:"first_name"`         // 客户名
		LastName          string `json:"last_name"`          // 客户姓
		AvsAlert          int    `json:"avs_alert"`          // 地址验证系统警报
		VerificationAlert int    `json:"verification_alert"` // 验证警报
	} `json:"customer"`
	Session struct {
		AuthToken         string      `json:"auth_token"`         // 授权令牌
		Intent            string      `json:"intent"`             // 交易意图
		SessionStatus     string      `json:"session_status"`     // 会话状态
		OrderID           string      `json:"order_id"`           // 订单ID
		Currency          string      `json:"currency"`           // 货币类型
		Amount            int         `json:"amount"`             // 订单金额
		ConversionRate    interface{} `json:"conversion_rate"`    // 转换汇率
		ProcessedCurrency string      `json:"processed_currency"` // 处理货币
		ProcessedAmount   int         `json:"processed_amount"`   // 处理金额
		PaymentMethod     interface{} `json:"payment_method"`     // 支付方式
		Gateway           string      `json:"gateway"`            // 支付网关
		Cid               string      `json:"cid"`                // 客户ID
		Variable1         string      `json:"variable1"`          // 自定义变量1
		Variable2         string      `json:"variable2"`          // 自定义变量2
		Variable3         string      `json:"variable3"`          // 自定义变量3
	} `json:"session"`
	Version   string `json:"version"`   // API版本
	Timestamp int    `json:"timestamp"` // 时间戳
}

type PraxisData struct {
	TransactionId string  `json:"transaction_id"` // 交易ID
	PaymentUrl    string  `json:"payment_url"`    // 支付地址
	Amount        float64 `json:"amount"`         // 订单金额
	Currency      string  `json:"currency"`       // 货币类型
	Status        string  `json:"status"`         // 订单状态
}

type PraxisCallback struct {
	MerchantID     string `json:"merchant_id"`
	ApplicationKey string `json:"application_key"`
	Customer       struct {
		CustomerToken     string `json:"customer_token"`
		Country           string `json:"country"`
		FirstName         string `json:"first_name"`
		LastName          string `json:"last_name"`
		AvsAlert          int    `json:"avs_alert"`
		VerificationAlert int    `json:"verification_alert"`
	} `json:"customer"`
	Session struct {
		AuthToken         string `json:"auth_token"`
		Intent            string `json:"intent"`
		SessionStatus     string `json:"session_status"`
		OrderID           string `json:"order_id"`
		Currency          string `json:"currency"`
		Amount            int    `json:"amount"`
		ConversionRate    string `json:"conversion_rate"`
		ProcessedCurrency string `json:"processed_currency"`
		ProcessedAmount   int    `json:"processed_amount"`
		PaymentMethod     string `json:"payment_method"`
		Gateway           string `json:"gateway"`
		Cid               string `json:"cid"`
		Variable1         string `json:"variable1"`
		Variable2         string `json:"variable2"`
		Variable3         string `json:"variable3"`
	} `json:"session"`
	Transaction struct {
		TransactionType   string `json:"transaction_type"`
		TransactionStatus string `json:"transaction_status"`
		Tid               int    `json:"tid"`
		TransactionID     string `json:"transaction_id"`
		Currency          string `json:"currency"`
		Amount            int    `json:"amount"`
		ConversionRate    string `json:"conversion_rate"`
		ProcessedCurrency string `json:"processed_currency"`
		ProcessedAmount   int    `json:"processed_amount"`
		Fee               int    `json:"fee"`
		FeeIncluded       int    `json:"fee_included"`
		FeeType           string `json:"fee_type"`
		PaymentMethod     string `json:"payment_method"`
		PaymentProcessor  string `json:"payment_processor"`
		Gateway           string `json:"gateway"`
		Card              struct {
			CardToken         interface{} `json:"card_token"`
			CardType          string      `json:"card_type"`
			CardNumber        string      `json:"card_number"`
			CardExp           string      `json:"card_exp"`
			CardIssuerName    interface{} `json:"card_issuer_name"`
			CardIssuerCountry interface{} `json:"card_issuer_country"`
		} `json:"card"`
		Wallet              interface{} `json:"wallet"`
		IsAsync             interface{} `json:"is_async"`
		IsCascade           int         `json:"is_cascade"`
		CascadeLevel        interface{} `json:"cascade_level"`
		ReferenceID         interface{} `json:"reference_id"`
		WithdrawalRequestID int         `json:"withdrawal_request_id"`
		CreatedBy           string      `json:"created_by"`
		EditedBy            interface{} `json:"edited_by"`
		StatusCode          string      `json:"status_code"`
		StatusDetails       string      `json:"status_details"`
	} `json:"transaction"`
	Version   string `json:"version"`
	Timestamp int    `json:"timestamp"`
}

// Init 初始化
// @description 初始化Praxis支付控制器
func (c *praxisPay) Init() {
	server.Http().PostNoAuth("/api/praxispay/recharge/callback", c.RechargeCallback)
	server.Http().PostNoAuth("/api/praxispay/withdraw/callback", c.WithdrawCallback)
}

// Recharge 创建充值订单
// @description 创建Praxis支付充值订单并返回支付地址
// @param ctx *abugo.AbuHttpContent - HTTP上下文，包含请求和响应信息
// @param req *CreateOrderReq - 创建订单请求参数，包含支付方式、金额等信息
// @param specialAgent int - 特殊代理ID，用于标识特殊的代理关系
// @flow 处理流程:
//  1. 获取支付方式：根据请求的支付方式ID获取对应的支付配置
//  2. 解析支付配置：解析Praxis支付的配置信息
//  3. 获取用户信息：获取发起充值的用户详细信息
//  4. 获取汇率信息：获取货币转换汇率
//  5. 创建订单记录：在数据库中创建充值订单记录
//  6. 获取用户IP所属国家：用于支付请求的地区信息
//  7. 准备API参数：构建Praxis支付接口需要的请求参数
//  8. 生成签名：根据参数生成安全签名
//  9. 发送支付请求：调用Praxis支付接口
//
// 10. 处理响应结果：解析接口响应并更新订单信息
// 11. 返回支付地址：将支付URL返回给客户端
// @return 返回支付URL或错误信息
func (c *praxisPay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	var errcode int

	// 1. 获取支付方式配置
	// 根据支付方式ID获取对应的支付配置信息
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		logs.Error("praxispay: 支付方式不存在:", err)
		ctx.RespErr(errors.New("支付方式不存在"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 2. 解析配置
	// 将支付方式的额外配置解析为Praxis支付配置结构
	cfg := PraxisConfig{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("praxispay: 无效的支付配置:", err)
		ctx.RespErr(errors.New("无效的支付配置"), &errcode)
		return
	}

	// 3. 获取用户信息
	// 根据token获取用户详细信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		logs.Error("praxispay: 用户不存在:", err)
		ctx.RespErr(errors.New("用户不存在"), &errcode)
		return
	}

	// 4. 获取汇率
	// 获取指定货币的转换汇率
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	// 5. 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 创建订单
	// 开启数据库事务
	tx := server.DaoxHashGame().Begin()
	// 构建充值订单记录
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,                                              // 商户ID
		ChannelID:    user.ChannelID,                                             // 渠道ID
		UserID:       user.UserID,                                                // 用户ID
		Symbol:       req.Symbol,                                                 // 货币符号
		PayID:        int32(req.MethodId),                                        // 支付方式ID
		PayType:      22,                                                         // Praxis支付的类型标识
		Amount:       req.Amount,                                                 // 充值金额
		RealAmount:   amount,                                                     // 实际金额（考虑汇率）
		TransferRate: rate,                                                       // 转换汇率
		State:        3,                                                          // 初始状态：等待支付
		CSGroup:      user.CSGroup,                                               // 客服组
		CSID:         user.CSID,                                                  // 客服ID
		SpecialAgent: int32(specialAgent),                                        // 特殊代理标识
		TopAgentID:   user.TopAgentID,                                            // 顶级代理ID
		OrderID:      fmt.Sprintf("PX%d%d", time.Now().UnixNano(), token.UserId), // 生成唯一的订单ID
		PayData:      string(payDataBytes),                                       // 添加 PayData 字段
	}

	// 创建订单记录
	err = tx.XRecharge.WithContext(ctx.Gin()).
		Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress).
		Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("praxispay: 创建订单失败:", err)
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 5.1 根据IP获取国家
	// 获取用户IP对应的国家代码
	country := utils.GetIsoCountryByIp(ctx.GetIp())

	// 6. 构建回调URL
	// 拼接完整的回调通知地址
	var urlBuilder strings.Builder
	urlBuilder.WriteString(cfg.CbUrl)
	urlBuilder.WriteString("/api/praxispay/recharge/callback")
	callbackUrl := urlBuilder.String()

	// 7. 准备Praxis API请求参数
	// 构建支付接口需要的所有参数
	params := xgo.H{
		"merchant_id":      cfg.MerchantId,                                 // 商户ID
		"application_key":  cfg.ApplicationKey,                             // 应用密钥
		"intent":           "payment",                                      // 交易类型：支付
		"currency":         strings.ToUpper(req.Symbol),                    // 货币类型（大写）
		"amount":           req.Amount * 100,                               // 金额（转换为分）
		"cid":              strconv.FormatInt(int64(user.UserID), 10),      // 客户ID
		"locale":           c.GetLanguage(country),                         // 语言设置
		"customer_token":   "",                                             // 客户令牌（可选）
		"return_url":       cfg.ReturnUrl,                                  // 支付完成返回URL
		"notification_url": callbackUrl,                                    // 支付通知回调URL
		"validation_url":   "",                                             // 验证URL（可选）
		"order_id":         strconv.FormatInt(int64(rechargeOrder.ID), 10), // 订单ID
		"variable1":        "",                                             // 自定义变量1
		"variable2":        "",                                             // 自定义变量2
		"variable3":        "",                                             // 自定义变量3
		"version":          "1.3",                                          // API版本
		"timestamp":        time.Now().Unix(),                              // 当前时间戳
		"session_type":     "cashier",                                      // 会话类型：收银台
		"expires":          "",                                             // 过期时间（可选）
		"customer_data": map[string]interface{}{ // 客户信息
			"country":    country,                                  // 国家
			"first_name": user.RealName,                            // 名
			"last_name":  user.RealName,                            // 姓
			"dob":        user.Birthday.Format("01/02/2006"),       // 生日
			"email":      user.Email,                               // 邮箱
			"phone":      user.PhoneNum,                            // 电话
			"city":       user.Address,                             // 城市
			"zip":        "123456",                                 // 邮编
			"state":      strconv.FormatInt(int64(user.State), 10), // 状态
			"address":    user.Address,                             // 地址
			"profile":    1,                                        // 配置文件
		},
	}

	// 8. 生成签名
	// 使用商户密钥对请求参数生成签名
	signStr := c.GenerateSignature(0, params, cfg.MerchantSecret)

	// 9. 发送请求
	// 将参数序列化为JSON并发送HTTP请求
	jsonData, _ := json.Marshal(params)
	resp, err := c.post(cfg.ApiUrl+"/cashier/cashier", map[string]string{
		"Content-Type":      "application/json",
		"Gt-Authentication": signStr,
	}, jsonData)

	if err != nil {
		tx.Rollback()
		logs.Error("praxispay: 发送支付请求失败:", err)
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		return
	}

	// 10. 解析响应
	// 解析支付接口的响应数据
	var response PraxisResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		tx.Rollback()
		logs.Error("praxispay: 解析响应失败:", err)
		ctx.RespErr(errors.New("解析响应失败"), &errcode)
		return
	}
	logs.Info("praxispay: 支付响应:", response)

	// 检查响应状态
	if response.Status != 0 {
		tx.Rollback()
		logs.Error("praxispay: 支付请求失败:", response.Description)
		ctx.RespErr(errors.New(response.Description), &errcode)
		return
	}

	// 11. 更新订单信息
	// 保存支付平台返回的授权令牌
	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
		Updates(map[string]interface{}{
			"ThirdID": response.Session.AuthToken, // 第三方订单号
		})
	if err != nil {
		tx.Rollback()
		logs.Error("praxispay: 更新订单失败:", err)
		ctx.RespErr(errors.New("更新订单失败"), &errcode)
		return
	}

	// 12. 提交事务
	tx.Commit()

	// 13. 返回支付URL
	// 返回支付平台的收银台URL给客户端
	ctx.RespOK(xgo.H{
		"payurl": response.RedirectURL, // 支付跳转地址
	})
}

// RechargeCallback 处理 Praxis 支付回调
// @description 处理支付平台的回调请求，验证订单状态并更新系统订单信息
// @param ctx *abugo.AbuHttpContent - HTTP上下文
// @flow 处理流程:
//  1. 解析回调数据：解析支付平台返回的JSON数据
//  2. 查找订单：根据回调中的订单ID查找对应的充值订单
//  3. 验证订单状态：检查订单是否已处理完成
//  4. 验证支付配置：获取并验证支付方式配置
//  5. 验证订单金额：确保回调金额与订单金额匹配
//  6. 验证订单号：确保回调的订单号与系统记录匹配
//  7. 处理订单状态：根据回调状态更新订单并处理相关业务逻辑
//
// @return 返回处理结果的JSON响应
func (c *praxisPay) RechargeCallback(ctx *abugo.AbuHttpContent) {
	// 1. 解析回调数据
	// 定义回调数据结构，包含支付平台返回的所有必要信息
	var callbackData struct {
		Status      int    `json:"status"`       // 回调状态码：0-成功，其他-失败
		Description string `json:"description"`  // 状态描述信息
		RedirectURL string `json:"redirect_url"` // 支付完成后的跳转URL
		Customer    struct {
			CustomerToken     string `json:"customer_token"`     // 客户令牌
			Country           string `json:"country"`            // 客户所在国家
			FirstName         string `json:"first_name"`         // 客户名
			LastName          string `json:"last_name"`          // 客户姓
			AvsAlert          int    `json:"avs_alert"`          // 地址验证警报
			VerificationAlert int    `json:"verification_alert"` // 验证警报
		} `json:"customer"`
		Session struct {
			AuthToken         string      `json:"auth_token"`         // 授权令牌，用于验证订单
			Intent            string      `json:"intent"`             // 交易意图
			SessionStatus     string      `json:"session_status"`     // 会话状态
			OrderID           string      `json:"order_id"`           // 系统订单ID
			Currency          string      `json:"currency"`           // 交易货币类型
			Amount            int         `json:"amount"`             // 交易金额(单位:分)
			ConversionRate    int         `json:"conversion_rate"`    // 货币转换率
			ProcessedCurrency string      `json:"processed_currency"` // 实际处理的货币类型
			ProcessedAmount   int         `json:"processed_amount"`   // 实际处理的金额
			PaymentMethod     string      `json:"payment_method"`     // 支付方式
			Gateway           interface{} `json:"gateway"`            // 支付网关信息
			Cid               string      `json:"cid"`                // 客户ID
			Variable1         string      `json:"variable1"`          // 自定义变量1
			Variable2         string      `json:"variable2"`          // 自定义变量2
			Variable3         string      `json:"variable3"`          // 自定义变量3
		} `json:"session"`
		Version   string `json:"version"`   // API版本号
		Timestamp int    `json:"timestamp"` // 时间戳
	}

	// 解析请求体中的JSON数据
	if err := ctx.Gin().BindJSON(&callbackData); err != nil {
		logs.Error("praxispay: 无效的请求数据:", err)
		ctx.Gin().JSON(400, gin.H{"error": "无效的请求数据"})
		return
	}

	// 记录回调数据日志
	callbackJson, _ := json.Marshal(callbackData)
	logs.Info("praxispay: 回调数据:", string(callbackJson))

	// 2. 查找订单
	// 将回调中的订单ID转换为整数
	orderId, _ := strconv.Atoi(callbackData.Session.OrderID)
	// 查询数据库中的订单记录
	order, err := server.DaoxHashGame().XRecharge.WithContext(ctx.Gin()).
		Where(server.DaoxHashGame().XRecharge.ID.Eq(int32(orderId))).
		First()

	if err != nil {
		logs.Error("praxispay: 订单不存在:", err)
		ctx.Gin().JSON(400, gin.H{"error": "订单不存在"})
		return
	}

	// 3. 验证订单状态
	// 检查订单是否已经处理完成，避免重复处理
	if order.State == 5 {
		ctx.Gin().JSON(200, gin.H{"status": "success"})
		return
	}

	// 4. 获取支付方式配置
	// 获取支付方式相关配置信息
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("praxispay: 支付方式不存在:", err)
		ctx.Gin().JSON(400, gin.H{"error": "支付方式不存在"})
		return
	}

	// 解析支付配置
	cfg := PraxisConfig{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("praxispay: 无效的支付配置:", err)
		ctx.Gin().JSON(400, gin.H{"error": "无效的支付配置"})
		return
	}

	// 4.1 验证订单金额
	// 将回调金额从分转换为元并验证与订单金额是否匹配
	callbackAmount := float64(callbackData.Session.Amount) / 100
	if math.Abs(order.Amount-callbackAmount) > 0.01 {
		logs.Error("praxispay: 订单金额不匹配, 订单金额:", order.Amount, "回调金额:", callbackAmount)
		ctx.Gin().JSON(400, gin.H{"error": "订单金额不匹配"})
		return
	}

	// 4.2 验证订单号
	// 验证回调中的授权令牌与订单记录中的第三方订单号是否匹配
	orderID, _ := strconv.Atoi(callbackData.Session.OrderID)
	if order.ID != int32(orderID) {
		logs.Error("praxispay: 订单号不匹配, 订单号:", order.ID, "回调订单号:", callbackData.Session.OrderID)
		ctx.Gin().JSON(400, gin.H{"error": "订单号不匹配"})
		return
	}

	// 5. 处理订单状态
	if callbackData.Status == 0 {
		// 更新订单状态为成功并记录支付时间
		_, err = server.DaoxHashGame().XRecharge.WithContext(ctx.Gin()).
			Where(server.DaoxHashGame().XRecharge.ID.Eq(order.ID)).
			Updates(map[string]interface{}{
				"State":   5, // 5表示支付成功
				"PayTime": time.Now(),
			})

		if err != nil {
			logs.Error("praxispay: 更新订单状态失败:", err)
			ctx.Gin().JSON(500, gin.H{"error": "内部服务器错误"})
			return
		}

		// 处理订单完成后的业务逻辑
		c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	}

	// 返回成功响应
	ctx.Gin().JSON(200, gin.H{"status": "success"})
}

// WithdrawCallback 处理Praxis代付回调
// @description 处理Praxis代付回调请求，验证订单状态并更新系统订单信息
// @param ctx *abugo.AbuHttpContent - HTTP上下文
// @flow 处理流程:
//  1. 解析回调数据：解析支付平台返回的JSON数据
//  2. 查找订单：根据回调中的订单ID查找对应的提现订单
//  3. 验证订单状态：检查订单是否已处理完成
//  4. 验证支付配置：获取并验证支付方式配置
//  5. 验证订单金额：确保回调金额与订单金额匹配
//  6. 验证订单号：确保回调的订单号与系统记录匹配
//  7. 处理订单状态：根据回调状态更新订单并处理相关业务逻辑
//
// @return 返回处理结果
func (c *praxisPay) WithdrawCallback(ctx *abugo.AbuHttpContent) {
	// 1. 读取并解析回调数据
	body, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("praxispay: 读取回调请求体失败:", err)
		ctx.Gin().String(400, "读取请求失败")
		return
	}
	logs.Info("praxispay: 代付回调数据:", string(body))

	// 解析回调数据
	var callbackData PraxisCallback
	if err := json.Unmarshal(body, &callbackData); err != nil {
		logs.Error("praxispay: 解析回调数据失败:", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 2. 获取订单详情
	// 检查订单ID是否为空
	if callbackData.Session.OrderID == "" {
		logs.Error("praxispay: 回调数据中订单ID为空")
		ctx.Gin().String(400, "无效的订单ID")
		return
	}

	// 解析订单ID
	orderId, err := strconv.Atoi(callbackData.Session.OrderID)
	if err != nil {
		logs.Error("praxispay: 解析订单ID失败, OrderID:", callbackData.Session.OrderID, "错误:", err)
		ctx.Gin().String(400, "无效的订单ID")
		return
	}

	// 查询提现订单
	order, err := server.DaoxHashGame().XWithdraw.WithContext(ctx.Gin()).
		Where(server.DaoxHashGame().XWithdraw.ID.Eq(int32(orderId))).
		First()
	if err != nil {
		logs.Error("praxispay: 未找到提现订单:", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 3. 检查订单是否已处理
	if order.State == 6 {
		logs.Info("praxispay: 订单已处理, 订单ID:", orderId)
		ctx.Gin().String(200, "success")
		return
	}

	// 4. 验证支付方式并获取配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("praxispay: 无效的支付方式:", err)
		ctx.Gin().String(400, "无效的支付方式")
		return
	}

	// 解析支付配置
	var cfg PraxisConfig
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("praxispay: 无效的配置:", err)
		ctx.Gin().String(400, "无效的配置")
		return
	}

	// 5. 验证订单金额
	// 注意：回调金额单位为分，需要转换为元进行比较
	callbackAmount := float64(callbackData.Transaction.Amount) / 100 // 使用Transaction中的金额而不是Session中的金额
	if math.Abs(order.Amount-callbackAmount) > 0.01 {                // 允许0.01的误差
		logs.Error("praxispay: 金额不匹配, 订单金额:", order.Amount, "回调金额:", callbackAmount,
			"原始回调金额(分):", callbackData.Transaction.Amount)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 6. 根据交易状态处理订单
	var newState int32
	updateFields := map[string]interface{}{
		"State": newState, // 新状态
	}

	switch callbackData.Transaction.TransactionStatus {
	case "approved":
		newState = 6 // 提现成功
		updateFields["ThirdID"] = callbackData.Transaction.WithdrawalRequestID
	case "failed", "error", "cancelled":
		newState = 7 // 提现失败
		updateFields["ThirdID"] = callbackData.Transaction.WithdrawalRequestID
	case "requested":
		newState = 5
		updateFields["SendTime"] = time.Now()                                // 发送时间                                                                         // 处理中
		updateFields["ThirdID"] = strconv.Itoa(callbackData.Transaction.Tid) // 更新三方订单号
	default:
		logs.Error("praxispay: 未知的交易状态:", callbackData.Transaction.TransactionStatus)
		ctx.Gin().String(400, "未知的交易状态")
		return
	}

	updateFields["State"] = newState

	// 7. 开启事务处理订单状态更新
	tx := server.DaoxHashGame().Begin()

	// 更新订单状态
	_, err = tx.XWithdraw.WithContext(ctx.Gin()).
		Where(tx.XWithdraw.ID.Eq(order.ID)).
		Updates(updateFields)

	if err != nil {
		tx.Rollback()
		logs.Error("praxispay: 更新订单状态失败:", err)
		ctx.Gin().String(500, "更新订单失败")
		return
	}

	// 8. 处理订单完成后的业务逻辑
	if newState == 6 || newState == 7 {
		c.withdrawCallbackHandel(int(order.UserID), int(order.ID))
	}

	// 9. 提交事务
	tx.Commit()

	// 10. 返回成功响应
	ctx.Gin().JSON(200, gin.H{
		"status":      0,
		"description": "withdraw success",
		"version":     "1.3",
		"timestamp":   time.Now().Unix(),
	})
}

// GetRequestSignatureList 获取签名字段列表
// @description 根据不同的场景获取需要签名的字段列表
// @param code int - 场景代码：0-充值订单，1-充值回调，2-提现回调
// @return []string - 需要签名的字段列表
func (s *praxisPay) GetRequestSignatureList(code int) []string {
	var temp []string
	switch code {
	case 0: // 充值订单签名字段
		temp = []string{
			"merchant_id",
			"application_key",
			"timestamp",
			"intent",
			"cid",
			"order_id",
		}
	case 1: // 充值回调签名字段
		temp = []string{
			"status",
			"timestamp",
			"redirect_url",
			"customer.customer_token",
		}
	}
	return temp
}

// GetConcatenatedString 拼接签名字符串
// @description 根据签名字段列表，按顺序拼接数据中的对应值
// @param code int - 场景代码：0-充值订单，1-充值回调，2-提现回调
// @param data map[string]interface{} - 需要签名的数据
// @return string - 拼接后的字符串
func (s *praxisPay) GetConcatenatedString(code int, data map[string]interface{}) string {
	result := ""
	for _, key := range s.GetRequestSignatureList(code) {
		if val, exists := data[key]; exists && val != nil {
			result += fmt.Sprintf("%v", val)
		}
	}
	return result
}

// GenerateSignature 生成签名
// @description 根据数据和商户密钥生成SHA-384签名
// @param code int - 场景代码：0-充值订单，1-充值回调，2-提现回调
// @param data map[string]interface{} - 需要签名的数据
// @param merchantSecret string - 商户密钥
// @return string - SHA-384签名结果
func (s *praxisPay) GenerateSignature(code int, data map[string]interface{}, merchantSecret string) string {
	// 1. 按顺序拼接字符串
	concatenatedString := s.GetConcatenatedString(code, data)
	// 2. 加入商户密钥
	concatenatedString += merchantSecret
	// 3. 生成 SHA-384 签名
	hash := sha512.New384()
	hash.Write([]byte(concatenatedString))
	signature := hex.EncodeToString(hash.Sum(nil))
	return signature
}
