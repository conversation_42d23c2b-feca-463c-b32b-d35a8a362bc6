package userbrand

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/beego/beego/logs"
	"gorm.io/gorm"
)

const UserProtectionConfigName = "user_protection_setting"

// 比较操作符枚举
type CompareOperator string

const (
	CompareEqual        CompareOperator = "="
	CompareLess         CompareOperator = "<"
	CompareLessEqual    CompareOperator = "<="
	CompareGreater      CompareOperator = ">"
	CompareGreaterEqual CompareOperator = ">="
)

// 保护对象类型枚举
type ProtectObjectType string

const (
	ProtectOnlyTiYanJing        ProtectObjectType = "only_tiyanjing"         // 仅领体验金
	ProtectOnlyFirstRecharge    ProtectObjectType = "only_first_recharge"    // 仅首充
	ProtectTiYanJingAndRecharge ProtectObjectType = "tiyanjing_and_recharge" // 领体验金且充值
	ProtectRechargeLessThan10U  ProtectObjectType = "recharge_less_10u"      // 充值小于10U
)

type LeaveConditions struct {
	RechargeCountEnabled   bool            `json:"rechargeCountEnabled"`   // 是否启用充值次数
	RechargeCount          int             `json:"rechargeCount"`          // 充值次数
	RechargeCountOperator  CompareOperator `json:"rechargeCountOperator"`  // 充值次数比较操作符
	RechargeAmountEnabled  bool            `json:"rechargeAmountEnabled"`  // 是否启用充值金额
	RechargeAmount         float64         `json:"rechargeAmount"`         // 充值金额
	RechargeAmountOperator CompareOperator `json:"rechargeAmountOperator"` // 充值金额比较操作符
	WithdrawCountEnabled   bool            `json:"withdrawCountEnabled"`   // 是否启用提现次数
	WithdrawCount          int             `json:"withdrawCount"`          // 提现次数
	WithdrawCountOperator  CompareOperator `json:"withdrawCountOperator"`  // 提现次数比较操作符
	ProfitAmountEnabled    bool            `json:"profitAmountEnabled"`    // 是否启用盈利金额
	ProfitAmount           float64         `json:"profitAmount"`           // 盈利金额
	ProfitAmountOperator   CompareOperator `json:"profitAmountOperator"`   // 盈利金额比较操作符
}

// 用户保护配置
type UserProtectionSetting struct {
	Enabled            bool                `json:"enabled"`            // 用户保护总开关：true=启用，false=禁用
	ProtectObjects     []ProtectObjectType `json:"protectObjects"`     // 保护玩家对象，可多选
	GameBrands         []string            `json:"gameBrands"`         // 保护生效游戏品牌，可多选(GFG、OMG、HeiBao)
	InactiveGameHint   string              `json:"inactiveGameHint"`   // 未生效游戏点击提示
	InactiveGameHintEn string              `json:"inactiveGameHintEn"` // 未生效游戏英文提示
	LeaveConditions    LeaveConditions     `json:"leaveConditions"`    // 脱离保护条件
	WithdrawLimit      bool                `json:"withdrawLimit"`      // 是否启用提现限制
	WithdrawPercent    int                 `json:"withdrawPercent"`    // 单次提现金额≤体验金百分比

	// 新增字段
	CaijinEnabled bool   `json:"caijinEnabled"` // 体验金用户保护开关（兼容性保留）
	GameBrand     string `json:"gameBrand"`     // 保护生效游戏品牌（兼容性保留）
}

// UserProtectionStatus 用户保护状态
type UserProtectionStatus struct {
	IsProtected        bool    `json:"isProtected"`        // 是否是保护用户
	ProtectionReason   string  `json:"protectionReason"`   // 保护原因
	TiYanJingTotal     float64 `json:"tiYanJingTotal"`     // 体验金总额
	CanLeave           bool    `json:"canLeave"`           // 是否可以脱离保护
	CanLeaveMsg        string  `json:"canLeaveMsg"`        // 脱离保护原因
	AllowedGameBrand   string  `json:"allowedGameBrand"`   // 允许的游戏品牌
	InactiveGameHint   string  `json:"inactiveGameHint"`   // 未生效游戏点击提示（中文）
	InactiveGameHintEn string  `json:"inactiveGameHintEn"` // 未生效游戏点击提示（英文）
}

// WithdrawLimitInfo 提现限制信息
type WithdrawLimitInfo struct {
	IsProtected        bool    `json:"isProtected"`        // 是否是保护用户
	IsWithdrawLimited  bool    `json:"isWithdrawLimited"`  // 是否有提现限制
	MaxWithdrawAmount  float64 `json:"maxWithdrawAmount"`  // 最大提现金额
	CurrentBalance     float64 `json:"currentBalance"`     // 当前总余额
	TiYanJingTotal     float64 `json:"tiYanJingTotal"`     // 体验金总额
	WithdrawPercent    int     `json:"withdrawPercent"`    // 提现百分比
	MaxByBalance       float64 `json:"maxByBalance"`       // 按余额百分比计算的最大提现金额
	MaxByTiYanJing     float64 `json:"maxByTiYanJing"`     // 按体验金计算的最大提现金额
	CanLeaveProtection bool    `json:"canLeaveProtection"` // 是否可以脱离保护
	Reason             string  `json:"reason"`             // 限制原因或说明
}

// UserProtectionService 用户保护服务
type UserProtectionService struct{}

// GetUserTiYanJingTotal 获取用户领取的体验金总额（转换为USDT计算）
func (s *UserProtectionService) GetUserTiYanJingTotal(db *gorm.DB, userId int) (float64, error) {
	// 1. 获取TRX汇率
	var trxRate float64 = 0.2752 // 默认值
	var config struct {
		ConfigValue string `gorm:"column:ConfigValue"`
	}

	err := db.Table("x_config").
		Select("ConfigValue").
		Where("ConfigName = ? AND IsShow = ?", "TrxPrice", 1).
		First(&config).Error

	if err != nil {
		logs.Error("查询用户trx汇率失败 userId=", userId, " err=", err.Error())
		return 0, err
	}
	if config.ConfigValue == "" {
		logs.Error("查询用户trx汇率失败 x_config的TrxPrice配置为空 userId=", userId, " config.ConfigValue=", config.ConfigValue)
		return 0, fmt.Errorf("TrxPrice配置为空")
	}

	if err == nil && config.ConfigValue != "" {
		if rate, err := strconv.ParseFloat(config.ConfigValue, 64); err == nil {
			logs.Info("体验金查询，成功查询到trx汇率 userId=", userId, " trxRate=", rate)
			trxRate = rate
		}
	}

	// 2. 获取用户领取的体验金总额
	type TiYanJingSum struct {
		TotalAmount float64 `gorm:"column:TotalAmount"`
	}

	var tiYanJingSum TiYanJingSum
	err = db.Table("x_tiyanjing").
		Select("SUM(CASE WHEN Symbol = 'trx' THEN Amount * ? ELSE Amount END) as TotalAmount", trxRate).
		Where("UserId = ? AND Symbol IN ('trx', 'usdt') AND State IN(5,7)", userId).
		Scan(&tiYanJingSum).Error
	if err != nil {
		logs.Error("查询用户体验金总额失败 userId=", userId, " err=", err.Error())
		return 0, err
	}

	logs.Info("查询用户体验金总额成功 userId=", userId, " totalAmount=", tiYanJingSum.TotalAmount, " trxRate=", trxRate)
	return tiYanJingSum.TotalAmount, nil
}

// GetUserProtectionSetting 获取用户保护配置
func (s *UserProtectionService) GetUserProtectionSetting(db *gorm.DB) (*UserProtectionSetting, error) {
	var config struct {
		ConfigValue string `gorm:"column:ConfigValue"`
	}
	sellerId := 0
	err := db.Table("x_config").
		Select("ConfigValue").
		Where("ConfigName = ? AND SellerId = ?", UserProtectionConfigName, sellerId).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 配置不存在时返回默认空配置
			logs.Info("用户保护配置不存在，返回默认配置")
			return &UserProtectionSetting{
				Enabled:            false,
				ProtectObjects:     []ProtectObjectType{ProtectOnlyTiYanJing}, // 默认只能选择仅领体验金
				GameBrands:         []string{"gfg"},                           // 默认选择GFG
				InactiveGameHint:   "当前账号暂时无法进入该游戏，请联系客服。",
				InactiveGameHintEn: "This account can't access the game. Please contact support",
				LeaveConditions: LeaveConditions{
					RechargeCountEnabled:   false,
					RechargeCount:          0,
					RechargeCountOperator:  CompareGreaterEqual,
					RechargeAmountEnabled:  false,
					RechargeAmount:         0.0,
					RechargeAmountOperator: CompareGreaterEqual,
					WithdrawCountEnabled:   false,
					WithdrawCount:          0,
					WithdrawCountOperator:  CompareGreaterEqual,
					ProfitAmountEnabled:    false,
					ProfitAmount:           0.0,
					ProfitAmountOperator:   CompareGreaterEqual,
				},
				WithdrawLimit:   false,
				WithdrawPercent: 0,
				// 兼容性字段
				CaijinEnabled: true,
				GameBrand:     "gfg",
			}, nil
		}
		logs.Error("查询用户保护配置失败:", err)
		return nil, err
	}

	var setting UserProtectionSetting
	if err := json.Unmarshal([]byte(config.ConfigValue), &setting); err != nil {
		logs.Error("解析用户保护配置失败:", err)
		return nil, err
	}

	return &setting, nil
}

// SaveUserProtectionSetting 保存用户保护配置
func (s *UserProtectionService) SaveUserProtectionSetting(db *gorm.DB, setting *UserProtectionSetting) error {
	setting.InactiveGameHint = "当前账号暂时无法进入该游戏，请联系客服。"
	setting.InactiveGameHintEn = "This account can't access the game. Please contact support"
	val, err := json.Marshal(setting)
	if err != nil {
		logs.Error("序列化用户保护配置失败:", err)
		return err
	}
	sellerId := 0
	// 先检查配置是否存在
	var count int64
	err = db.Table("x_config").
		Where("ConfigName = ? AND SellerId = ?", UserProtectionConfigName, sellerId).
		Count(&count).Error
	if err != nil {
		logs.Error("检查用户保护配置是否存在失败:", err)
		return err
	}

	if count > 0 {
		// 配置存在，执行更新
		err = db.Table("x_config").
			Where("ConfigName = ? AND SellerId = ?", UserProtectionConfigName, sellerId).
			Update("ConfigValue", string(val)).Error
		if err != nil {
			logs.Error("更新用户保护配置失败:", err)
		} else {
			logs.Info("用户保护配置更新成功")
		}
	} else {
		// 配置不存在，执行新增
		err = db.Table("x_config").
			Create(map[string]interface{}{
				"ConfigName":  UserProtectionConfigName,
				"ConfigValue": string(val),
				"SellerId":    sellerId,
			}).Error
		if err != nil {
			logs.Error("新增用户保护配置失败:", err)
		} else {
			logs.Info("用户保护配置新增成功")
		}
	}

	return err
}

// 比较操作符判断函数
func compareValues(operator CompareOperator, actual, expected float64) bool {
	switch operator {
	case CompareEqual:
		return actual == expected
	case CompareLess:
		return actual < expected
	case CompareLessEqual:
		return actual <= expected
	case CompareGreater:
		return actual > expected
	case CompareGreaterEqual:
		return actual >= expected
	default:
		return actual >= expected // 默认使用 >=
	}
}

// ShouldLeaveUserProtection 判断玩家是否应该脱离用户保护
func (s *UserProtectionService) ShouldLeaveUserProtection(db *gorm.DB, userId int, setting *UserProtectionSetting) (bool, string, error) {
	conds := setting.LeaveConditions

	// 检查充值次数条件
	if conds.RechargeCountEnabled {
		var cnt struct{ C int }
		err := db.Table("x_recharge").
			Select("COUNT(1) as C").
			Where("UserId = ? AND State = 5", userId).
			Scan(&cnt).Error
		if err != nil {
			logs.Error("查询充值次数失败 userId=", userId, " err=", err.Error())
			return false, "", err
		}
		if compareValues(conds.RechargeCountOperator, float64(cnt.C), float64(conds.RechargeCount)) {
			message := fmt.Sprintf("用户满足充值次数脱离条件 userId=%d, 充值次数=%d, 脱离保护条件充值次数%s%d", userId, cnt.C, string(conds.RechargeCountOperator), conds.RechargeCount)
			logs.Info(message)
			return true, message, nil
		}
	}

	// 检查充值金额条件
	if conds.RechargeAmountEnabled {
		var amt struct{ A float64 }
		err := db.Table("x_recharge").
			Select("SUM(RealAmount) as A").
			Where("UserId = ? AND State = 5", userId).
			Scan(&amt).Error
		if err != nil {
			logs.Error("查询充值金额失败 userId=", userId, " err=", err.Error())
			return false, "", err
		}
		if compareValues(conds.RechargeAmountOperator, amt.A, conds.RechargeAmount) {
			message := fmt.Sprintf("用户满足充值金额脱离条件 userId=%d, 充值金额=%.2f, 脱离保护条件充值金额%s%.2f", userId, amt.A, string(conds.RechargeAmountOperator), conds.RechargeAmount)
			logs.Info(message)
			return true, message, nil
		}
	}

	// 检查提现次数条件
	if conds.WithdrawCountEnabled {
		var cnt struct{ C int }
		err := db.Table("x_withdraw").
			Select("COUNT(1) as C").
			Where("UserId = ? AND State = 6", userId).
			Scan(&cnt).Error
		if err != nil {
			logs.Error("查询提现次数失败 userId=", userId, " err=", err.Error())
			return false, "", err
		}
		if compareValues(conds.WithdrawCountOperator, float64(cnt.C), float64(conds.WithdrawCount)) {
			message := fmt.Sprintf("用户满足提现次数脱离条件 userId=%d, 提现次数=%d, 脱离保护条件提现次数%s%d", userId, cnt.C, string(conds.WithdrawCountOperator), conds.WithdrawCount)
			logs.Info(message)
			return true, message, nil
		}
	}

	// 检查盈利金额条件
	if conds.ProfitAmountEnabled {
		var betData struct {
			BetAmount float64 `gorm:"column:BetAmount"`
			WinAmount float64 `gorm:"column:WinAmount"`
		}
		err := db.Table("x_custom_third").
			Select("SUM(BetAmount) as BetAmount, SUM(WinAmount) as WinAmount").
			Where("UserId = ?", userId).
			Scan(&betData).Error
		if err != nil {
			logs.Error("查询投注盈利数据失败 userId=", userId, " err=", err.Error())
			return false, "", err
		}
		// 计算盈利金额：math.Floor((BetAmount-WinAmount)*100) / 100
		profitAmount := math.Floor((betData.BetAmount-betData.WinAmount)*100) / 100
		if compareValues(conds.ProfitAmountOperator, profitAmount, conds.ProfitAmount) {
			message := fmt.Sprintf("用户满足盈利金额脱离条件 userId=%d, 投注金额=%.2f, 赢得金额=%.2f, 盈利金额=%.2f, 脱离保护条件盈利金额%s%.2f", userId, betData.BetAmount, betData.WinAmount, profitAmount, string(conds.ProfitAmountOperator), conds.ProfitAmount)
			logs.Info(message)
			return true, message, nil
		}
	}

	return false, "", nil
}

// IsUserProtectedObject 判断用户是否为保护对象
// 根据 ProtectObjects 配置判断用户是否符合保护条件
func (s *UserProtectionService) IsUserProtectedObject(db *gorm.DB, userId int, setting *UserProtectionSetting) (bool, string, error) {
	// 检查配置的保护对象类型
	for _, protectType := range setting.ProtectObjects {
		switch protectType {
		case ProtectOnlyTiYanJing:
			// 仅领体验金：检查是否领取了体验金
			tiYanJingTotal, err := s.GetUserTiYanJingTotal(db, userId)
			if err != nil {
				return false, "", err
			}
			if tiYanJingTotal > 0 {
				return true, "用户领取了体验金，受新手保护限制", nil
			}

		case ProtectOnlyFirstRecharge:
			// 仅首充：检查是否有充值记录且充值次数为1
			var cnt struct{ C int }
			err := db.Table("x_recharge").
				Select("COUNT(1) as C").
				Where("UserId = ? AND State = 5", userId).
				Scan(&cnt).Error
			if err != nil {
				return false, "", err
			}
			if cnt.C == 1 {
				return true, "用户为首充用户，受新手保护限制", nil
			}

		case ProtectTiYanJingAndRecharge:
			// 领体验金且充值：检查是否既领取了体验金又有充值记录
			tiYanJingTotal, err := s.GetUserTiYanJingTotal(db, userId)
			if err != nil {
				return false, "", err
			}
			if tiYanJingTotal > 0 {
				var cnt struct{ C int }
				err := db.Table("x_recharge").
					Select("COUNT(1) as C").
					Where("UserId = ? AND State = 5", userId).
					Scan(&cnt).Error
				if err != nil {
					return false, "", err
				}
				if cnt.C > 0 {
					return true, "用户领取了体验金且有充值记录，受新手保护限制", nil
				}
			}

		case ProtectRechargeLessThan10U:
			// 充值小于10U：检查充值总金额是否小于10
			var amt struct{ A float64 }
			err := db.Table("x_recharge").
				Select("SUM(RealAmount) as A").
				Where("UserId = ? AND State = 5", userId).
				Scan(&amt).Error
			if err != nil {
				return false, "", err
			}
			if amt.A > 0 && amt.A < 10 {
				return true, "用户充值金额小于10U，受新手保护限制", nil
			}
		}
	}

	return false, "", nil
}

// CheckWithdrawLimit 检查用户提现限制
// 参数：
//   - userId: 用户ID
//   - withdrawAmount: 提现金额
//
// 返回：
//   - allowed: 是否允许提现
//   - reason: 不允许提现的原因
//   - err: 系统错误
func (s *UserProtectionService) CheckWithdrawLimit(db *gorm.DB, userId int, withdrawAmount float64) (allowed bool, reason string, err error) {
	// 1. 获取保护配置
	setting, err := s.GetUserProtectionSetting(db)
	if err != nil {
		return false, "系统错误：获取配置失败", err
	}

	// 2. 保护功能未启用，直接允许
	if !setting.Enabled {
		return true, "", nil
	}

	// 3. 根据用户注册域名检查域名级别的用户保护开关
	var userRegHost struct {
		RegUrl      string `gorm:"column:regUrl"`
		UserProtect int32  `gorm:"column:UserProtect"`
	}
	err = db.Table("x_user").
		Select("x_user.regUrl, x_channel_host.UserProtect").
		Joins("LEFT JOIN x_channel_host ON x_channel_host.Host = x_user.regUrl").
		Where("x_user.UserId = ?", userId).
		Scan(&userRegHost).Error
	if err != nil {
		logs.Error("查询用户注册域名和保护开关失败 userId=", userId, " err=", err.Error())
		return false, "系统错误：查询用户信息失败", err
	}

	// 根据用户注册域名的用户保护开关决定提现限制
	logs.Info("检查用户注册域名的用户保护开关 userId=", userId, " regUrl=", userRegHost.RegUrl, " UserProtect=", userRegHost.UserProtect)
	if userRegHost.UserProtect == 2 || userRegHost.UserProtect == 0 {
		logs.Info("用户注册域名已关闭用户保护，提现不受限制 userId=", userId, " regUrl=", userRegHost.RegUrl)
		return true, "", nil
	}

	// 4. 查询用户余额信息
	var user struct {
		Amount      float64 `gorm:"column:Amount"`      // 当前USDT余额
		BonusAmount float64 `gorm:"column:BonusAmount"` // 当前Bonus余额
	}

	err = db.Table("x_user").
		Select("Amount, BonusAmount").
		Where("UserId = ?", userId).
		Scan(&user).Error
	if err != nil {
		return false, "系统错误：查询用户信息失败", err
	}

	// 5. 查询体验金总额（用于后续计算）
	tiYanJingTotal, err := s.GetUserTiYanJingTotal(db, userId)
	if err != nil {
		return false, "系统错误：查询体验金记录失败", err
	}

	// 根据 ProtectObjects 配置判断用户是否为保护对象
	isProtected, _, err := s.IsUserProtectedObject(db, userId, setting)
	if err != nil {
		return false, "系统错误：检查保护对象失败", err
	}

	// 非保护对象不受限制
	if !isProtected {
		return true, "", nil
	}

	// 6. 检查是否满足脱离保护条件
	leave, message, err := s.ShouldLeaveUserProtection(db, userId, setting)
	if err != nil {
		return false, "系统错误：检查脱离条件失败", err
	}
	if leave {
		logs.Info(message) // 记录脱离保护条件的详细信息
		return true, "", nil
	}

	// 7. 未启用提现限制，允许提现
	if !setting.WithdrawLimit {
		return true, "", nil
	}

	// 8. 计算当前总余额
	currentBalance := user.Amount + user.BonusAmount

	// 9. 计算允许的最大提现金额
	// 条件1：提现金额 <= 当前余额 * 百分比
	maxByBalance := currentBalance * float64(setting.WithdrawPercent) / 100.0

	// 条件2：提现金额 <= 领取体验金额度
	maxByTiYanJing := tiYanJingTotal

	// 取两个条件中的较小值
	maxWithdrawAmount := maxByBalance
	if maxByTiYanJing < maxByBalance {
		maxWithdrawAmount = maxByTiYanJing
	}

	// 10. 检查提现金额是否超限
	if withdrawAmount > maxWithdrawAmount {
		if maxWithdrawAmount <= 0 {
			return false, "当前不允许提现，请先进行充值或满足脱离保护条件", nil
		}
		return false, fmt.Sprintf("提现金额不能超过%.2f", maxWithdrawAmount), nil
	}

	return true, "", nil
}

// CheckGameEntry 检查用户游戏入口权限
// 参数：
//   - userId: 用户ID
//   - brand: 游戏品牌
//   - isEnglish: 是否返回英文提示（true=英文，false=中文）
//
// 返回：
//   - allowed: 是否允许进入
//   - hint: 不允许进入的提示信息
//   - err: 系统错误
func (s *UserProtectionService) CheckGameEntry(db *gorm.DB, userId int, brand string, isEnglish bool) (allowed bool, hint string, err error) {
	// 1. 获取保护配置
	setting, err := s.GetUserProtectionSetting(db)
	if err != nil {
		return false, "", err
	}

	// 2. 保护总开关检查 - 如果关闭，全部放行
	if !setting.Enabled {
		return true, "", nil
	}

	// 3. 根据用户注册域名检查域名级别的用户保护开关
	var userRegHost struct {
		RegUrl      string `gorm:"column:regUrl"`
		UserProtect int32  `gorm:"column:UserProtect"`
	}
	err = db.Table("x_user").
		Select("x_user.regUrl, x_channel_host.UserProtect").
		Joins("LEFT JOIN x_channel_host ON x_channel_host.Host = x_user.regUrl").
		Where("x_user.UserId = ?", userId).
		Scan(&userRegHost).Error
	if err != nil {
		logs.Error("查询用户注册域名和保护开关失败 userId=", userId, " err=", err.Error())
		return false, "系统错误：查询用户信息失败", err
	}

	// 根据用户注册域名的用户保护开关决定游戏入口权限
	logs.Info("检查用户注册域名的用户保护开关 userId=", userId, " regUrl=", userRegHost.RegUrl, " UserProtect=", userRegHost.UserProtect)
	if userRegHost.UserProtect == 2 || userRegHost.UserProtect == 0 {
		logs.Info("用户注册域名已关闭用户保护，游戏入口不受限制 userId=", userId, " regUrl=", userRegHost.RegUrl)
		return true, "", nil
	}

	// 4. 根据 ProtectObjects 配置判断用户是否为保护对象
	isProtected, _, err := s.IsUserProtectedObject(db, userId, setting)
	if err != nil {
		return false, "", err
	}

	// 非保护对象直接放行
	if !isProtected {
		return true, "", nil
	}

	// 5. 检查是否满足脱离保护条件
	leave, message, err := s.ShouldLeaveUserProtection(db, userId, setting)
	if err != nil {
		return false, "", err
	}
	if leave {
		logs.Info(message) // 记录脱离保护条件的详细信息
		return true, "", nil
	}

	// 6. 保护生效游戏品牌检查 - 支持多品牌检查
	// 优先使用新的GameBrands字段，如果为空则使用旧的GameBrand字段进行兼容
	gameBrands := setting.GameBrands
	if len(gameBrands) == 0 && setting.GameBrand != "" {
		gameBrands = []string{strings.ToUpper(setting.GameBrand)}
	}

	brandUpper := strings.ToUpper(brand)
	for _, gameBrand := range gameBrands {
		if brandUpper == strings.ToUpper(gameBrand) {
			return true, "", nil
		}
	}

	// 7. 拒绝进入非保护游戏，返回提示
	var hintMsg string
	if isEnglish {
		hintMsg = setting.InactiveGameHintEn
	} else {
		hintMsg = setting.InactiveGameHint
	}
	return false, hintMsg, nil
}

// CheckUserProtectionStatus 检查用户保护状态
func (s *UserProtectionService) CheckUserProtectionStatus(db *gorm.DB, userId int) (*UserProtectionStatus, error) {
	status := &UserProtectionStatus{
		IsProtected:        false,
		ProtectionReason:   "",
		TiYanJingTotal:     0,
		CanLeave:           false,
		CanLeaveMsg:        "",
		AllowedGameBrand:   "",
		InactiveGameHint:   "",
		InactiveGameHintEn: "",
	}

	// 1. 获取保护配置
	setting, err := s.GetUserProtectionSetting(db)
	if err != nil {
		return status, err
	}

	// 2. 保护总开关检查 - 如果关闭，返回非保护状态
	if !setting.Enabled {
		return status, nil
	}

	// 3. 根据用户注册域名检查域名级别的用户保护开关
	var userRegHost struct {
		RegUrl      string `gorm:"column:regUrl"`
		UserProtect int32  `gorm:"column:UserProtect"`
	}
	err = db.Table("x_user").
		Select("x_user.regUrl, x_channel_host.UserProtect").
		Joins("LEFT JOIN x_channel_host ON x_channel_host.Host = x_user.regUrl").
		Where("x_user.UserId = ?", userId).
		Scan(&userRegHost).Error
	if err != nil {
		logs.Error("查询用户注册域名和保护开关失败 userId=", userId, " err=", err.Error())
		return status, err
	}

	// 根据用户注册域名的用户保护开关决定是否启用保护
	logs.Info("检查用户注册域名的用户保护开关 userId=", userId, " regUrl=", userRegHost.RegUrl, " UserProtect=", userRegHost.UserProtect)
	if userRegHost.UserProtect == 2 || userRegHost.UserProtect == 0 {
		logs.Info("用户注册域名已关闭用户保护 userId=", userId, " regUrl=", userRegHost.RegUrl)
		return status, nil
	}

	// 4. 查询体验金总额（用于状态信息显示）
	tiYanJingTotal, err := s.GetUserTiYanJingTotal(db, userId)
	if err != nil {
		return status, err
	}
	status.TiYanJingTotal = tiYanJingTotal

	// 5. 根据 ProtectObjects 配置判断用户是否为保护对象
	isProtected, protectionReason, err := s.IsUserProtectedObject(db, userId, setting)
	if err != nil {
		return status, err
	}

	// 非保护对象
	if !isProtected {
		return status, nil
	}

	// 6. 该用户是保护对象，设置基本状态
	status.IsProtected = true
	status.ProtectionReason = protectionReason

	// 设置允许的游戏品牌（支持多品牌）
	if len(setting.GameBrands) > 0 {
		status.AllowedGameBrand = strings.Join(setting.GameBrands, ",")
	} else if setting.GameBrand != "" {
		// 兼容性处理
		status.AllowedGameBrand = setting.GameBrand
	}

	status.InactiveGameHint = setting.InactiveGameHint
	status.InactiveGameHintEn = setting.InactiveGameHintEn

	logs.Info("新手保护 查询数据 userId=", userId, " isProtected=", status.IsProtected, " reason=", status.ProtectionReason)

	// 7. 检查是否满足脱离保护条件
	canLeave, message, err := s.ShouldLeaveUserProtection(db, userId, setting)
	if err != nil {
		return status, err
	}

	status.CanLeave = canLeave
	if canLeave {
		logs.Info(message) // 记录脱离保护条件的详细信息
		status.IsProtected = false
		status.ProtectionReason = "用户已满足脱离保护条件"
		status.CanLeaveMsg = message
	}

	return status, nil
}

// GetWithdrawLimitInfo 获取用户提现限制信息
func (s *UserProtectionService) GetWithdrawLimitInfo(db *gorm.DB, userId int) (*WithdrawLimitInfo, error) {
	info := &WithdrawLimitInfo{
		IsProtected:        false,
		IsWithdrawLimited:  false,
		MaxWithdrawAmount:  -1, // -1 表示无限制
		CurrentBalance:     0,
		TiYanJingTotal:     0,
		WithdrawPercent:    0,
		MaxByBalance:       0,
		MaxByTiYanJing:     0,
		CanLeaveProtection: false,
		Reason:             "无限制",
	}

	// 1. 获取保护配置
	setting, err := s.GetUserProtectionSetting(db)
	if err != nil {
		return info, err
	}

	// 2. 保护功能未启用，直接返回无限制
	if !setting.Enabled {
		info.Reason = "用户保护功能未启用"
		return info, nil
	}

	// 3. 根据用户注册域名检查域名级别的用户保护开关
	var userRegHost struct {
		RegUrl      string `gorm:"column:regUrl"`
		UserProtect int32  `gorm:"column:UserProtect"`
	}
	err = db.Table("x_user").
		Select("x_user.regUrl, x_channel_host.UserProtect").
		Joins("LEFT JOIN x_channel_host ON x_channel_host.Host = x_user.regUrl").
		Where("x_user.UserId = ?", userId).
		Scan(&userRegHost).Error
	if err != nil {
		logs.Error("查询用户注册域名和保护开关失败 userId=", userId, " err=", err.Error())
		return info, err
	}

	// 根据用户注册域名的用户保护开关决定提现限制
	logs.Info("检查用户注册域名的用户保护开关 userId=", userId, " regUrl=", userRegHost.RegUrl, " UserProtect=", userRegHost.UserProtect)
	if userRegHost.UserProtect == 2 || userRegHost.UserProtect == 0 {
		logs.Info("用户注册域名已关闭用户保护，提现不受限制 userId=", userId, " regUrl=", userRegHost.RegUrl)
		info.Reason = "用户注册域名已关闭用户保护"
		return info, nil
	}

	// 4. 查询用户余额信息
	var user struct {
		Amount      float64 `gorm:"column:Amount"`      // 当前USDT余额
		BonusAmount float64 `gorm:"column:BonusAmount"` // 当前Bonus余额
	}

	err = db.Table("x_user").
		Select("Amount, BonusAmount").
		Where("UserId = ?", userId).
		Scan(&user).Error
	if err != nil {
		return info, err
	}

	// 保留两位小数
	info.CurrentBalance = math.Round((user.Amount+user.BonusAmount)*100) / 100

	// 5. 查询体验金总额（用于状态信息显示）
	tiYanJingTotal, err := s.GetUserTiYanJingTotal(db, userId)
	if err != nil {
		return info, err
	}

	// 保留两位小数
	info.TiYanJingTotal = math.Round(tiYanJingTotal*100) / 100

	// 6. 根据 ProtectObjects 配置判断用户是否为保护对象
	isProtected, _, err := s.IsUserProtectedObject(db, userId, setting)
	if err != nil {
		return info, err
	}

	// 非保护对象不受限制
	if !isProtected {
		info.Reason = "用户不是保护对象，不受保护限制"
		return info, nil
	}

	// 标记为保护用户
	info.IsProtected = true

	// 7. 检查是否满足脱离保护条件
	canLeave, message, err := s.ShouldLeaveUserProtection(db, userId, setting)
	if err != nil {
		return info, err
	}

	info.CanLeaveProtection = canLeave
	if canLeave {
		logs.Info(message) // 记录脱离保护条件的详细信息
		info.Reason = "用户已满足脱离保护条件，无提现限制"
		return info, nil
	}

	// 8. 检查是否启用提现限制
	if !setting.WithdrawLimit {
		info.Reason = "用户受保护但未启用提现限制"
		return info, nil
	}

	// 9. 计算提现限制
	info.IsWithdrawLimited = true
	info.WithdrawPercent = setting.WithdrawPercent

	// 条件1：提现金额 <= 当前余额 * 百分比（保留两位小数）
	info.MaxByBalance = math.Round((info.CurrentBalance*float64(setting.WithdrawPercent)/100.0)*100) / 100

	// 条件2：提现金额 <= 领取体验金额度（保留两位小数）
	info.MaxByTiYanJing = math.Round(info.TiYanJingTotal*100) / 100

	// 取两个条件中的较小值（保留两位小数）
	info.MaxWithdrawAmount = info.MaxByBalance
	if info.MaxByTiYanJing < info.MaxByBalance {
		info.MaxWithdrawAmount = info.MaxByTiYanJing
	}
	info.MaxWithdrawAmount = math.Round(info.MaxWithdrawAmount*100) / 100

	if info.MaxWithdrawAmount <= 0 {
		info.Reason = "当前不允许提现，请先进行充值或满足脱离保护条件"
	} else {
		info.Reason = fmt.Sprintf("受新手保护限制，最大提现金额为%.2f", info.MaxWithdrawAmount)
	}

	return info, nil
}

// 全局服务实例
var UserProtectionSvc = &UserProtectionService{}
