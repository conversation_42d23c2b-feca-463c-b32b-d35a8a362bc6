package paycontroller

import (
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// SafeGatePay 全局实例
var SafeGatePay = new(safeGatePay)

// safeGatePay SafeGatePay支付处理器
type safeGatePay struct {
	Base
}

// SafeGatePayConfig SafeGatePay支付方式配置
type SafeGatePayConfig struct {
	// 充值专用配置
	PayinOutletID  string `json:"payin_outlet_id"`  // 充值专用商户出口ID
	PayinSecretKey string `json:"payin_secret_key"` // 充值专用密钥

	// 提现专用配置
	PayoutOutletID  string `json:"payout_outlet_id"`  // 提现专用商户出口ID
	PayoutSecretKey string `json:"payout_secret_key"` // 提现专用密钥

	// 共用配置
	BaseURL  string `json:"base_url"`  // API基础地址
	CbURL    string `json:"cb_url"`    // 回调地址
	HashAlgo string `json:"hash_algo"` // 哈希算法：md5、sha256、sha512，默认md5
}

// SafeGatePayInitPaymentRequest SafeGatePay充值请求结构
type SafeGatePayInitPaymentRequest struct {
	SpOutletID         string `json:"sp_outlet_id"`             // 商户出口ID
	SpAmount           string `json:"sp_amount"`                // 支付金额
	SpDescription      string `json:"sp_description"`           // 支付描述
	SpPaymentSystem    string `json:"sp_payment_system"`        // 支付系统代码
	SpOrderID          string `json:"sp_order_id"`              // 商户订单ID
	SpUserPhone        string `json:"sp_user_phone"`            // 用户电话
	SpUserContactEmail string `json:"sp_user_contact_email"`    // 用户邮箱
	SpUserName         string `json:"sp_user_name"`             // 用户姓名
	SpCurrency         string `json:"sp_currency,omitempty"`    // 支付币种
	SpLifetime         string `json:"sp_lifetime,omitempty"`    // 订单生命周期（秒）
	SpResultURL        string `json:"sp_result_url"`            // 结果回调地址
	SpSuccessURL       string `json:"sp_success_url"`           // 成功跳转地址
	SpFailureURL       string `json:"sp_failure_url"`           // 失败跳转地址
	SpUserIP           string `json:"sp_user_ip,omitempty"`     // 用户IP
	SpRealURL          string `json:"sp_real_url,omitempty"`    // 真实回调地址
	SpFingerprint      string `json:"sp_fingerprint,omitempty"` // 浏览器指纹
	SpUserParams       string `json:"sp_user_params,omitempty"` // 用户参数
	SpSalt             string `json:"sp_salt"`                  // 随机数
	SpSig              string `json:"sp_sig"`                   // 签名
}

// SafeGatePayInitPaymentResponse SafeGatePay充值响应结构
type SafeGatePayInitPaymentResponse struct {
	SpStatus           string      `json:"sp_status"`            // 响应状态：ok-成功，error-失败
	SpPaymentID        interface{} `json:"sp_payment_id"`        // SafeGatePay支付ID（字符串或数字）
	SpRedirectURLType  string      `json:"sp_redirect_url_type"` // 重定向类型
	SpRedirectURL      string      `json:"sp_redirect_url"`      // 支付页面URL
	SpErrorCode        interface{} `json:"sp_error_code"`        // 错误代码（字符串或数字）
	SpErrorDescription string      `json:"sp_error_description"` // 错误描述
	SpSalt             interface{} `json:"sp_salt"`              // 随机数（字符串或数字）
	SpSig              string      `json:"sp_sig"`               // 响应签名
}

// SafeGatePayCallback SafeGatePay支付回调结构
type SafeGatePayCallback struct {
	SpResult           int         `json:"sp_result"`            // 支付结果：1-成功，0-失败
	SpOutletID         string      `json:"sp_outlet_id"`         // 商户出口ID
	SpOrderID          string      `json:"sp_order_id"`          // 商户订单ID
	SpAmount           string      `json:"sp_amount"`            // 实际支付金额
	SpPaymentID        int         `json:"sp_payment_id"`        // SafeGatePay支付ID
	SpInitAmount       string      `json:"sp_init_amount"`       // 初始支付金额
	SpNetAmount        string      `json:"sp_net_amount"`        // 净金额（减去手续费）
	SpPsAmount         string      `json:"sp_ps_amount"`         // 支付系统金额（兼容字段）
	SpPaymentSystem    string      `json:"sp_payment_system"`    // 使用的支付系统名称
	SpCurrency         string      `json:"sp_currency"`          // 支付币种
	SpErrorDescription string      `json:"sp_error_description"` // 错误描述（失败时）
	SpSalt             interface{} `json:"sp_salt"`              // 随机数（字符串或数字）
	SpSig              string      `json:"sp_sig"`               // 回调签名
}

// Init 初始化SafeGatePay支付
func (c *safeGatePay) Init() {
	server.Http().PostNoAuth("/api/safegatepay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/safegatepay/withdraw/callback", c.withdrawCallback)
}

// generateSignature 生成SafeGatePay签名（用于请求）
func (c *safeGatePay) generateSignature(methodName string, params map[string]interface{}, secretKey string, hashAlgo string) string {
	// 过滤掉sp_sig参数和空值
	filteredParams := make(map[string]interface{})
	for k, v := range params {
		if k != "sp_sig" && v != nil && v != "" {
			filteredParams[k] = v
		}
	}

	// 按键名排序
	keys := make([]string, 0, len(filteredParams))
	for k := range filteredParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建签名数组：方法名 + 参数值(按字母序) + 密钥
	var signatureArray []string
	signatureArray = append(signatureArray, methodName) // 第一个元素：方法名

	// 添加参数值（只要值，不要键名）
	for _, k := range keys {
		signatureArray = append(signatureArray, fmt.Sprintf("%v", filteredParams[k]))
	}

	signatureArray = append(signatureArray, secretKey) // 最后一个元素：密钥

	// 签名字符串格式：method_name;value1;value2;...;secret_key
	signatureString := strings.Join(signatureArray, ";")

	logs.Info("SafeGatePay充值签名字符串: %s", signatureString)

	// 根据指定的哈希算法生成签名
	var hash string
	switch strings.ToLower(hashAlgo) {
	case "sha256":
		h := sha256.Sum256([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	case "sha512":
		h := sha512.Sum512([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	default: // md5
		h := md5.Sum([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	}

	logs.Info("SafeGatePay充值生成签名: %s", hash)
	return hash
}

// generateCallbackSignature 生成回调签名验证（按照文档第2节Signature generation mechanism）
func (c *safeGatePay) generateCallbackSignature(methodName string, params map[string]interface{}, secretKey string, hashAlgo string) string {
	// 过滤掉sp_sig参数和空值
	filteredParams := make(map[string]interface{})
	for k, v := range params {
		if k != "sp_sig" && v != nil && v != "" {
			// 对于字符串类型，检查是否为空字符串或只包含空白字符
			if str, ok := v.(string); ok {
				if strings.TrimSpace(str) != "" {
					filteredParams[k] = v
				}
			} else {
				// 非字符串类型，直接保留
				filteredParams[k] = v
			}
		}
	}

	// 按键名排序
	keys := make([]string, 0, len(filteredParams))
	for k := range filteredParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 按照文档第2节格式：method_name;value1;value2;...;secret_key
	var signatureArray []string
	signatureArray = append(signatureArray, methodName) // 第一个元素：API方法名

	// 添加参数值（只要值，不要键名）
	for _, k := range keys {
		signatureArray = append(signatureArray, fmt.Sprintf("%v", filteredParams[k]))
	}

	// 从参数中获取支付系统类型
	paymentSystem := ""
	if paymentSystemVal, exists := filteredParams["sp_payment_system"]; exists {
		paymentSystem = fmt.Sprintf("%v", paymentSystemVal)
	}

	// 添加密钥到签名中
	signatureArray = append(signatureArray, secretKey) // 最后一个元素：密钥

	// 签名字符串格式：method_name;value1;value2;...;secret_key (或仅method_name;value1;value2;...)
	signatureString := strings.Join(signatureArray, ";")

	logs.Info("SafeGatePay回调签名字符串: %s", signatureString)
	logs.Info("支付系统类型: %s, 包含密钥", paymentSystem)

	// 根据指定的哈希算法生成签名
	var hash string
	switch strings.ToLower(hashAlgo) {
	case "sha256":
		h := sha256.Sum256([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	case "sha512":
		h := sha512.Sum512([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	default: // md5
		h := md5.Sum([]byte(signatureString))
		hash = hex.EncodeToString(h[:])
	}

	logs.Info("SafeGatePay回调生成签名: %s", hash)
	return hash
}

// getMethodNameFromCallbackURL 从回调URL获取方法名
func (c *safeGatePay) getMethodNameFromCallbackURL(callbackURL string) string {
	// 解析URL，获取最后一段作为方法名（按照SafeGatePay文档要求）
	parts := strings.Split(strings.TrimSuffix(callbackURL, "/"), "/")
	logs.Info("URL解析: %s -> parts: %v", callbackURL, parts)

	if len(parts) > 0 {
		methodName := parts[len(parts)-1]
		// 如果方法名为空，返回默认值
		if methodName != "" {
			logs.Info("从最后一段获取方法名: %s", methodName)
			return methodName
		}
	}
	logs.Info("使用默认方法名: init_payment")
	return "init_payment" // 默认值
}

// verifyCallback 验证回调签名
func (c *safeGatePay) verifyCallback(callback SafeGatePayCallback, secretKey string, hashAlgo string, callbackURL string) bool {
	// 处理 sp_salt 的类型转换
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	// 根据SafeGatePay实际回调参数构建签名参数
	// 只包含回调中实际存在的字段，按字母顺序排列
	allParams := map[string]interface{}{
		"sp_amount":            callback.SpAmount,
		"sp_currency":          callback.SpCurrency,
		"sp_error_description": callback.SpErrorDescription,
		"sp_init_amount":       callback.SpInitAmount,
		"sp_net_amount":        callback.SpNetAmount,
		"sp_order_id":          callback.SpOrderID,
		"sp_payment_id":        callback.SpPaymentID,
		"sp_payment_system":    callback.SpPaymentSystem,
		"sp_ps_amount":         callback.SpPsAmount,
		"sp_result":            strconv.Itoa(callback.SpResult),
		"sp_salt":              saltStr,
	}

	// 过滤掉空值字段 - 只保留非空、非null、非空字符串的字段
	params := make(map[string]interface{})
	for k, v := range allParams {
		// 检查值是否为空
		if v != nil && v != "" {
			// 对于字符串类型，检查是否为空字符串或只包含空白字符
			if str, ok := v.(string); ok {
				if strings.TrimSpace(str) != "" {
					params[k] = v
				}
			} else {
				// 非字符串类型，直接保留
				params[k] = v
			}
		}
	}

	// 打印签名验证的详细参数
	logs.Info("=== SafeGatePay签名验证参数详情 ===")
	logs.Info("回调URL: %s", callbackURL)
	logs.Info("哈希算法: %s", hashAlgo)
	logs.Info("密钥前4位: %s", secretKey[:4])
	logs.Info("参与签名的参数（过滤空值后）:")
	for k, v := range params {
		logs.Info("  %s: %v", k, v)
	}
	logs.Info("=== 签名验证参数详情结束 ===")

	// 从回调URL动态获取方法名
	methodName := c.getMethodNameFromCallbackURL(callbackURL)
	logs.Info("SafeGatePay回调方法名: %s, 回调URL: %s", methodName, callbackURL)
	expectedSig := c.generateCallbackSignature(methodName, params, secretKey, hashAlgo)
	logs.Info("SafeGatePay回调期望签名: %s, 实际签名: %s", expectedSig, callback.SpSig)
	return strings.ToLower(expectedSig) == strings.ToLower(callback.SpSig)
}

// generateResponseSignature 生成回调响应签名
func (c *safeGatePay) generateResponseSignature(salt, secretKey, hashAlgo string) string {
	params := map[string]interface{}{
		"sp_salt":   salt,
		"sp_status": "ok",
	}
	return c.generateSignature("payment", params, secretKey, hashAlgo)
}

// Recharge SafeGatePay充值
func (c *safeGatePay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 验证币种是否匹配
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 解析SafeGatePay配置
	cfg := SafeGatePayConfig{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		ctx.RespErr(errors.New("SafeGatePay配置解析失败"), &errcode)
		return
	}

	// 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	// 获取汇率
	var rate, amount float64
	if user.SellerID == 26 {
		rate = 0
		amount = float64(req.Amount)
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID)
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode)
			return
		}
		amount = float64(req.Amount) / rate
	}

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive,
	}
	payDataBytes, _ := json.Marshal(payData)

	// 开始数据库事务
	tx := server.DaoxHashGame().Begin()

	// 创建充值订单
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      21, // SafeGatePay 的唯一 ID
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3, // 待支付
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		OrderType:    int32(req.OrderType),
		PayData:      string(payDataBytes),
	}

	// 插入订单记录
	if err := tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder); err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 设置哈希算法默认值
	if cfg.HashAlgo == "" {
		cfg.HashAlgo = "md5"
	}

	// SafeGatePay支持的币种
	var currency string
	switch strings.ToUpper(req.Symbol) {
	case "RUB":
		currency = "RUB"
	default:
		currency = "RUB" // 默认为RUB
	}

	// 获取支付系统代码，优先使用数据库配置，如果为空则使用默认值
	paymentSystem := payMethod.PayType
	if paymentSystem == "" {
		paymentSystem = "P2P_CASCADE_C2C" // 默认俄罗斯卡对卡支付
	}

	// 使用充值专用配置
	payinOutletID := cfg.PayinOutletID
	payinSecretKey := cfg.PayinSecretKey

	// 构建SafeGatePay请求参数
	salt := fmt.Sprintf("%d", time.Now().UnixNano())
	requestParams := SafeGatePayInitPaymentRequest{
		SpOutletID:         payinOutletID,
		SpAmount:           fmt.Sprintf("%.2f", req.Amount),
		SpDescription:      fmt.Sprintf("Recharge Order %d", rechargeOrder.ID),
		SpPaymentSystem:    paymentSystem, // 从数据库读取支付系统代码
		SpOrderID:          fmt.Sprintf("%d", rechargeOrder.ID),
		SpUserPhone:        req.PhoneNum,
		SpUserContactEmail: req.Email,
		SpUserName:         req.RealName,
		SpCurrency:         currency,
		SpLifetime:         "3600", // 1小时有效期
		SpResultURL:        fmt.Sprintf("%s/api/safegatepay/recharge/callback", cfg.CbURL),
		SpSuccessURL:       cfg.CbURL,
		SpFailureURL:       cfg.CbURL,
		SpUserIP:           req.Ip,
		SpRealURL:          cfg.CbURL,
		SpFingerprint:      "",
		SpUserParams:       "",
		SpSalt:             salt,
	}

	// 生成请求签名
	params := map[string]interface{}{
		"sp_outlet_id":          payinOutletID,
		"sp_amount":             requestParams.SpAmount,
		"sp_description":        requestParams.SpDescription,
		"sp_payment_system":     requestParams.SpPaymentSystem,
		"sp_order_id":           requestParams.SpOrderID,
		"sp_user_phone":         requestParams.SpUserPhone,
		"sp_user_contact_email": requestParams.SpUserContactEmail,
		"sp_user_name":          requestParams.SpUserName,
		"sp_currency":           requestParams.SpCurrency,
		"sp_lifetime":           requestParams.SpLifetime,
		"sp_result_url":         requestParams.SpResultURL,
		"sp_success_url":        requestParams.SpSuccessURL,
		"sp_failure_url":        requestParams.SpFailureURL,
		"sp_user_ip":            requestParams.SpUserIP,
		"sp_real_url":           requestParams.SpRealURL,
		"sp_fingerprint":        requestParams.SpFingerprint,
		"sp_user_params":        requestParams.SpUserParams,
		"sp_salt":               requestParams.SpSalt,
	}

	requestParams.SpSig = c.generateSignature("init_payment", params, payinSecretKey, cfg.HashAlgo)

	// 发送到SafeGatePay API
	requestBody, _ := json.Marshal(requestParams)
	logs.Info("SafeGatePay充值请求: %s", string(requestBody))

	apiURL := cfg.BaseURL + "/init_payment"
	resp, err := c.post(apiURL, map[string]string{
		"Content-Type": "application/json",
	}, requestBody)
	if err != nil {
		logs.Error("SafeGatePay充值请求失败: %v", err)
		tx.Rollback()
		ctx.RespErr(errors.New("支付请求失败"), &errcode)
		return
	}

	// 解析响应
	var response SafeGatePayInitPaymentResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("SafeGatePay响应解析失败: %v", err)
		tx.Rollback()
		ctx.RespErr(errors.New("支付响应解析失败"), &errcode)
		return
	}

	logs.Info("SafeGatePay充值响应: %+v", response)

	// 检查响应状态
	if response.SpStatus != "ok" {
		logs.Error("SafeGatePay充值失败: %v - %s", response.SpErrorCode, response.SpErrorDescription)
		tx.Rollback()
		ctx.RespErr(errors.New(response.SpErrorDescription), &errcode)
		return
	}

	// 更新订单的第三方ID - 确保正确转换为字符串格式
	var paymentIDStr string
	switch v := response.SpPaymentID.(type) {
	case float64:
		// 如果是浮点数，转换为整数字符串，避免科学计数法
		paymentIDStr = fmt.Sprintf("%.0f", v)
	case int:
		paymentIDStr = strconv.Itoa(v)
	case int64:
		paymentIDStr = strconv.FormatInt(v, 10)
	case string:
		paymentIDStr = v
	default:
		// 其他类型，使用默认转换但确保是字符串
		paymentIDStr = fmt.Sprintf("%v", v)
	}

	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
		Update(tx.XRecharge.ThirdID, paymentIDStr)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("更新订单失败"), &errcode)
		return
	}

	// 提交事务
	tx.Commit()

	ctx.RespOK(map[string]interface{}{
		"payurl":  response.SpRedirectURL,
		"orderId": rechargeOrder.ID,
	})
}

// rechargeCallback 处理SafeGatePay充值回调
func (c *safeGatePay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 打印完整的回调请求信息
	logs.Info("========== SafeGatePay充值回调完整信息 ==========")

	// 1. 打印请求基本信息
	logs.Info("【请求基本信息】")
	logs.Info("请求方法: %s", ctx.Gin().Request.Method)
	logs.Info("请求URL: %s", ctx.Gin().Request.URL.String())
	logs.Info("请求路径: %s", ctx.Gin().Request.URL.Path)
	logs.Info("查询参数: %s", ctx.Gin().Request.URL.RawQuery)
	logs.Info("客户端IP: %s", ctx.Gin().ClientIP())
	logs.Info("User-Agent: %s", ctx.Gin().Request.UserAgent())

	// 2. 打印所有请求头
	logs.Info("【请求头信息】")
	for name, values := range ctx.Gin().Request.Header {
		for _, value := range values {
			logs.Info("  %s: %s", name, value)
		}
	}

	// 3. 先读取原始请求体并保存
	logs.Info("【原始请求体】")
	body, err := ctx.Gin().GetRawData()
	if err != nil {
		logs.Error("获取原始请求体失败: %v", err)
		ctx.Gin().String(400, "获取请求体失败")
		return
	}
	logs.Info("原始请求体内容: %s", string(body))
	logs.Info("请求体长度: %d bytes", len(body))

	// 4. 尝试解析为JSON并打印
	logs.Info("【JSON解析结果】")
	var rawData map[string]interface{}
	if err := json.Unmarshal(body, &rawData); err != nil {
		logs.Error("JSON解析失败: %v", err)
	} else {
		logs.Info("JSON解析成功，字段数量: %d", len(rawData))
		for k, v := range rawData {
			logs.Info("  %s: %v (类型: %T)", k, v, v)
		}
	}

	logs.Info("========== 回调完整信息结束 ==========")

	// 5. 重新设置请求体，以便后续BindJSON可以读取
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(body))

	var callback SafeGatePayCallback

	// 解析回调数据
	if err := ctx.Gin().BindJSON(&callback); err != nil {
		logs.Error("SafeGatePay充值回调数据解析失败: %v", err)
		ctx.Gin().String(400, "数据解析失败")
		return
	}

	// 6. 处理sp_payment_id的科学计数法问题
	if rawData != nil {
		if paymentIDRaw, exists := rawData["sp_payment_id"]; exists {
			switch v := paymentIDRaw.(type) {
			case float64:
				// 处理科学计数法，转换为整数
				callback.SpPaymentID = int(v)
				logs.Info("修复sp_payment_id: 从科学计数法 %v 转换为 %d", paymentIDRaw, callback.SpPaymentID)
			case string:
				if id, err := strconv.Atoi(v); err == nil {
					callback.SpPaymentID = id
					logs.Info("修复sp_payment_id: 从字符串 %s 转换为 %d", v, callback.SpPaymentID)
				}
			}
		}
	}

	// 打印所有回调参数
	logs.Info("=== SafeGatePay充值回调参数详情 ===")
	logs.Info("sp_result: %v", callback.SpResult)
	logs.Info("sp_order_id: %s", callback.SpOrderID)
	logs.Info("sp_amount: %s", callback.SpAmount)
	logs.Info("sp_payment_id: %d", callback.SpPaymentID)
	logs.Info("sp_init_amount: %s", callback.SpInitAmount)
	logs.Info("sp_net_amount: %s", callback.SpNetAmount)
	logs.Info("sp_ps_amount: %s", callback.SpPsAmount)
	logs.Info("sp_payment_system: %s", callback.SpPaymentSystem)
	logs.Info("sp_currency: %s", callback.SpCurrency)
	logs.Info("sp_error_description: %s", callback.SpErrorDescription)
	logs.Info("sp_salt: %v", callback.SpSalt)
	logs.Info("sp_sig: %s", callback.SpSig)
	logs.Info("=== 回调参数详情结束 ===")

	logs.Info("SafeGatePay充值回调数据: %+v", callback)

	// 解析订单ID
	orderId, err := strconv.Atoi(callback.SpOrderID)
	if err != nil {
		logs.Error("SafeGatePay回调订单ID解析失败: %v", err)
		ctx.Gin().String(400, "订单ID格式错误")
		return
	}

	// 获取订单信息
	rechargeOrder, err := server.DaoxHashGame().XRecharge.WithContext(ctx.Gin()).
		Where(server.DaoxHashGame().XRecharge.ID.Eq(int32(orderId))).First()
	if err != nil {
		logs.Error("SafeGatePay充值订单不存在: %d", orderId)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(int(rechargeOrder.PayID))
	if err != nil {
		logs.Error("SafeGatePay充值支付方式不存在: %d", rechargeOrder.PayID)
		ctx.Gin().String(400, "支付方式配置错误")
		return
	}

	// 解析SafeGatePay配置
	cfg := SafeGatePayConfig{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("SafeGatePay配置解析失败: %v", err)
		ctx.Gin().String(400, "配置解析失败")
		return
	}

	// 构建回调URL
	callbackURL := fmt.Sprintf("%s/api/safegatepay/recharge/callback", cfg.CbURL)

	// 调用调试函数打印详细信息
	c.debugCallbackInfo(callback, cfg.PayinSecretKey, cfg.HashAlgo, callbackURL)

	// 验证回调签名（充值使用充值密钥）
	if !c.verifyCallback(callback, cfg.PayinSecretKey, cfg.HashAlgo, callbackURL) {
		logs.Error("SafeGatePay充值回调签名验证失败: 订单ID=%d", orderId)
		ctx.Gin().String(400, "签名验证失败")
		return
	}

	// 解析回调金额
	callbackAmount, err := strconv.ParseFloat(callback.SpAmount, 64)
	if err != nil {
		logs.Error("SafeGatePay充值回调金额解析失败: %v", err)
		ctx.Gin().String(400, "金额格式错误")
		return
	}

	// 验证金额（允许小额误差）
	amountDiff := callbackAmount - rechargeOrder.Amount
	if amountDiff > 0.01 || amountDiff < -0.01 {
		logs.Warn("SafeGatePay充值回调金额差异: 订单=%.2f, 回调=%.2f", rechargeOrder.Amount, callbackAmount)
	}

	// 根据结果处理订单
	if callback.SpResult == 1 {
		// 支付成功
		logs.Info("SafeGatePay充值成功: 订单ID=%d, 用户ID=%d, 金额=%.2f", orderId, rechargeOrder.UserID, callbackAmount)
		c.rechargeCallbackHandel(int(rechargeOrder.UserID), orderId, 5)
	} else {
		// 支付失败
		logs.Info("SafeGatePay充值失败: 订单ID=%d, 用户ID=%d", orderId, rechargeOrder.UserID)
		c.rechargeCallbackHandel(int(rechargeOrder.UserID), orderId, 7)
	}

	// 处理 sp_salt 的类型转换
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	// 返回XML格式的成功响应
	ctx.Gin().Header("Content-Type", "application/xml; charset=utf-8")
	ctx.Gin().String(200, `<?xml version="1.0" encoding="utf-8"?>
<response>
<sp_salt>%s</sp_salt>
<sp_status>ok</sp_status>
<sp_sig>%s</sp_sig>
</response>`, saltStr, c.generateResponseSignature(saltStr, cfg.PayinSecretKey, cfg.HashAlgo))
}

// debugCallbackInfo 调试函数：打印完整的回调信息和签名验证过程
func (c *safeGatePay) debugCallbackInfo(callback SafeGatePayCallback, secretKey string, hashAlgo string, callbackURL string) {
	logs.Info("========== SafeGatePay回调调试信息 ==========")

	// 1. 打印原始回调数据
	logs.Info("【原始回调数据】")
	logs.Info("sp_result: %v (类型: %T)", callback.SpResult, callback.SpResult)
	logs.Info("sp_outlet_id: %s (类型: %T)", callback.SpOutletID, callback.SpOutletID)
	logs.Info("sp_order_id: %s (类型: %T)", callback.SpOrderID, callback.SpOrderID)
	logs.Info("sp_amount: %s (类型: %T)", callback.SpAmount, callback.SpAmount)
	logs.Info("sp_payment_id: %d (类型: %T)", callback.SpPaymentID, callback.SpPaymentID)
	logs.Info("sp_init_amount: %s (类型: %T)", callback.SpInitAmount, callback.SpInitAmount)
	logs.Info("sp_net_amount: %s (类型: %T)", callback.SpNetAmount, callback.SpNetAmount)
	logs.Info("sp_ps_amount: %s (类型: %T)", callback.SpPsAmount, callback.SpPsAmount)
	logs.Info("sp_payment_system: %s (类型: %T)", callback.SpPaymentSystem, callback.SpPaymentSystem)
	logs.Info("sp_currency: %s (类型: %T)", callback.SpCurrency, callback.SpCurrency)
	logs.Info("sp_error_description: %s (类型: %T)", callback.SpErrorDescription, callback.SpErrorDescription)
	logs.Info("sp_salt: %v (类型: %T)", callback.SpSalt, callback.SpSalt)
	logs.Info("sp_sig: %s (类型: %T)", callback.SpSig, callback.SpSig)

	// 2. 打印配置信息
	logs.Info("【配置信息】")
	logs.Info("回调URL: %s", callbackURL)
	logs.Info("哈希算法: %s", hashAlgo)
	logs.Info("密钥长度: %d", len(secretKey))
	logs.Info("密钥前4位: %s", secretKey[:4])

	// 3. 获取方法名
	methodName := c.getMethodNameFromCallbackURL(callbackURL)
	logs.Info("【方法名解析】")
	logs.Info("从URL解析的方法名: %s", methodName)

	// 4. 构建签名参数
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	params := map[string]interface{}{
		"sp_amount":         callback.SpAmount,
		"sp_currency":       callback.SpCurrency,
		"sp_init_amount":    callback.SpInitAmount,
		"sp_net_amount":     callback.SpNetAmount,
		"sp_outlet_id":      callback.SpOutletID,
		"sp_payment_id":     callback.SpPaymentID,
		"sp_payment_system": callback.SpPaymentSystem,
		"sp_ps_amount":      callback.SpPsAmount,
		"sp_result":         strconv.Itoa(callback.SpResult),
		"sp_salt":           saltStr,
	}

	logs.Info("【签名参数构建】")
	logs.Info("处理后的sp_salt: %s", saltStr)
	logs.Info("参与签名的参数:")
	for k, v := range params {
		logs.Info("  %s: %v (类型: %T)", k, v, v)
	}

	// 5. 生成签名
	generatedSig := c.generateCallbackSignature(methodName, params, secretKey, hashAlgo)

	logs.Info("【签名验证结果】")
	logs.Info("生成的签名: %s", generatedSig)
	logs.Info("接收的签名: %s", callback.SpSig)
	logs.Info("签名匹配: %v", strings.EqualFold(generatedSig, callback.SpSig))

	logs.Info("========== 调试信息结束 ==========")
}

// withdrawCallback 处理SafeGatePay提现回调
func (c *safeGatePay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 打印完整的回调请求信息
	logs.Info("========== SafeGatePay提现回调完整信息 ==========")

	// 1. 打印请求基本信息
	logs.Info("【请求基本信息】")
	logs.Info("请求方法: %s", ctx.Gin().Request.Method)
	logs.Info("请求URL: %s", ctx.Gin().Request.URL.String())
	logs.Info("请求路径: %s", ctx.Gin().Request.URL.Path)
	logs.Info("查询参数: %s", ctx.Gin().Request.URL.RawQuery)
	logs.Info("客户端IP: %s", ctx.Gin().ClientIP())
	logs.Info("User-Agent: %s", ctx.Gin().Request.UserAgent())

	// 2. 打印所有请求头
	logs.Info("【请求头信息】")
	for name, values := range ctx.Gin().Request.Header {
		for _, value := range values {
			logs.Info("  %s: %s", name, value)
		}
	}

	// 3. 先读取原始请求体并保存
	logs.Info("【原始请求体】")
	body, err := ctx.Gin().GetRawData()
	if err != nil {
		logs.Error("获取原始请求体失败: %v", err)
		ctx.Gin().String(400, "获取请求体失败")
		return
	}
	logs.Info("原始请求体内容: %s", string(body))
	logs.Info("请求体长度: %d bytes", len(body))

	// 4. 尝试解析为JSON并打印
	logs.Info("【JSON解析结果】")
	var rawData map[string]interface{}
	if err := json.Unmarshal(body, &rawData); err != nil {
		logs.Error("JSON解析失败: %v", err)
	} else {
		logs.Info("JSON解析成功，字段数量: %d", len(rawData))
		for k, v := range rawData {
			logs.Info("  %s: %v (类型: %T)", k, v, v)
		}
	}

	logs.Info("========== 回调完整信息结束 ==========")

	// 5. 重新设置请求体，以便后续BindJSON可以读取
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(body))

	var callback SafeGatePayCallback

	// 解析回调数据
	if err := ctx.Gin().BindJSON(&callback); err != nil {
		logs.Error("SafeGatePay提现回调数据解析失败: %v", err)
		ctx.Gin().String(400, "数据解析失败")
		return
	}

	// 6. 处理sp_payment_id的科学计数法问题
	if rawData != nil {
		if paymentIDRaw, exists := rawData["sp_payment_id"]; exists {
			switch v := paymentIDRaw.(type) {
			case float64:
				// 处理科学计数法，转换为整数
				callback.SpPaymentID = int(v)
				logs.Info("修复sp_payment_id: 从科学计数法 %v 转换为 %d", paymentIDRaw, callback.SpPaymentID)
			case string:
				if id, err := strconv.Atoi(v); err == nil {
					callback.SpPaymentID = id
					logs.Info("修复sp_payment_id: 从字符串 %s 转换为 %d", v, callback.SpPaymentID)
				}
			}
		}
	}

	// 打印所有回调参数
	logs.Info("=== SafeGatePay提现回调参数详情 ===")
	logs.Info("sp_result: %v", callback.SpResult)
	logs.Info("sp_order_id: %s", callback.SpOrderID)
	logs.Info("sp_amount: %s", callback.SpAmount)
	logs.Info("sp_payment_id: %d", callback.SpPaymentID)
	logs.Info("sp_init_amount: %s", callback.SpInitAmount)
	logs.Info("sp_net_amount: %s", callback.SpNetAmount)
	logs.Info("sp_ps_amount: %s", callback.SpPsAmount)
	logs.Info("sp_payment_system: %s", callback.SpPaymentSystem)
	logs.Info("sp_currency: %s", callback.SpCurrency)
	logs.Info("sp_error_description: %s", callback.SpErrorDescription)
	logs.Info("sp_salt: %v", callback.SpSalt)
	logs.Info("sp_sig: %s", callback.SpSig)
	logs.Info("=== 回调参数详情结束 ===")

	logs.Info("SafeGatePay提现回调数据: %+v", callback)

	// 解析订单ID
	orderId, err := strconv.Atoi(callback.SpOrderID)
	if err != nil {
		logs.Error("SafeGatePay提现回调订单ID解析失败: %v", err)
		ctx.Gin().String(400, "订单ID格式错误")
		return
	}

	// 获取提现订单信息
	withdrawOrder, err := server.DaoxHashGame().XWithdraw.WithContext(ctx.Gin()).
		Where(server.DaoxHashGame().XWithdraw.ID.Eq(int32(orderId))).First()
	if err != nil {
		logs.Error("SafeGatePay提现订单不存在: %d", orderId)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(int(withdrawOrder.PayID))
	if err != nil {
		logs.Error("SafeGatePay提现支付方式不存在: %d", withdrawOrder.PayID)
		ctx.Gin().String(400, "支付方式配置错误")
		return
	}

	// 解析SafeGatePay配置
	cfg := SafeGatePayConfig{}
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("SafeGatePay配置解析失败: %v", err)
		ctx.Gin().String(400, "配置解析失败")
		return
	}

	// 构建回调URL
	callbackURL := fmt.Sprintf("%s/api/safegatepay/withdraw/callback", cfg.CbURL)

	// 调用调试函数打印详细信息
	c.debugWithdrawCallbackInfo(callback, cfg.PayoutSecretKey, cfg.HashAlgo, callbackURL)

	// 验证回调签名（提现使用提现密钥）
	if !c.verifyWithdrawCallback(callback, cfg.PayoutSecretKey, cfg.HashAlgo, callbackURL) {
		logs.Error("SafeGatePay提现回调签名验证失败: 订单ID=%d, 开始回滚用户金额", orderId)

		// 签名验证失败，需要回滚用户金额
		// 调用失败回调处理函数，该函数会回滚金额并标记订单失败
		c.withdrawCallbackHandel(orderId, 7) // 7表示失败状态

		logs.Info("SafeGatePay提现回调签名验证失败处理完成: 订单ID=%d, 用户ID=%d, 已回滚金额=%.2f",
			orderId, withdrawOrder.UserID, withdrawOrder.RealAmount)

		ctx.Gin().String(400, "签名验证失败")
		return
	}

	// 解析回调金额
	callbackAmount, err := strconv.ParseFloat(callback.SpAmount, 64)
	if err != nil {
		logs.Error("SafeGatePay提现回调金额解析失败: 订单ID=%d, Error=%v, 开始回滚用户金额", orderId, err)

		// 金额解析失败，需要回滚用户金额
		c.withdrawCallbackHandel(orderId, 7) // 7表示失败状态

		logs.Info("SafeGatePay提现回调金额解析失败处理完成: 订单ID=%d, 用户ID=%d, 已回滚金额=%.2f",
			orderId, withdrawOrder.UserID, withdrawOrder.RealAmount)

		ctx.Gin().String(400, "金额格式错误")
		return
	}

	// 验证金额（允许小额误差）
	amountDiff := callbackAmount - withdrawOrder.RealAmount
	if amountDiff > 0.01 || amountDiff < -0.01 {
		logs.Warn("SafeGatePay提现回调金额差异: 订单=%.2f, 回调=%.2f", withdrawOrder.RealAmount, callbackAmount)
	}

	// 根据结果处理订单
	if callback.SpResult == 1 {
		// 提现成功
		logs.Info("SafeGatePay提现成功: 订单ID=%d, 用户ID=%d, 金额=%.2f", orderId, withdrawOrder.UserID, callbackAmount)
		c.withdrawCallbackHandel(orderId, 6) // 6表示成功状态
	} else {
		// 提现失败
		logs.Info("SafeGatePay提现失败: 订单ID=%d, 用户ID=%d", orderId, withdrawOrder.UserID)
		c.withdrawCallbackHandel(orderId, 7) // 7表示失败状态
	}

	// 处理 sp_salt 的类型转换
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	// 返回XML格式的成功响应
	ctx.Gin().Header("Content-Type", "application/xml; charset=utf-8")
	ctx.Gin().String(200, `<?xml version="1.0" encoding="utf-8"?>
<response>
<sp_salt>%s</sp_salt>
<sp_status>ok</sp_status>
<sp_sig>%s</sp_sig>
</response>`, saltStr, c.generateResponseSignature(saltStr, cfg.PayoutSecretKey, cfg.HashAlgo))
}

// verifyWithdrawCallback 验证提现回调签名
func (c *safeGatePay) verifyWithdrawCallback(callback SafeGatePayCallback, secretKey string, hashAlgo string, callbackURL string) bool {
	// 处理 sp_salt 的类型转换
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	// 根据SafeGatePay实际回调参数构建签名参数
	// 只包含回调中实际存在的字段，按字母顺序排列
	allParams := map[string]interface{}{
		"sp_amount":            callback.SpAmount,
		"sp_currency":          callback.SpCurrency,
		"sp_error_description": callback.SpErrorDescription,
		"sp_init_amount":       callback.SpInitAmount,
		"sp_net_amount":        callback.SpNetAmount,
		"sp_order_id":          callback.SpOrderID,
		"sp_payment_id":        callback.SpPaymentID,
		"sp_payment_system":    callback.SpPaymentSystem,
		"sp_ps_amount":         callback.SpPsAmount,
		"sp_result":            strconv.Itoa(callback.SpResult),
		"sp_salt":              saltStr,
	}

	// SafeGatePay签名包含所有参数（包括空值），但排除未发送的字段
	params := allParams

	// 打印签名验证的详细参数
	logs.Info("=== SafeGatePay提现回调签名验证参数详情 ===")
	logs.Info("回调URL: %s", callbackURL)
	logs.Info("哈希算法: %s", hashAlgo)
	logs.Info("密钥前4位: %s", secretKey[:4])
	logs.Info("参与签名的参数（包含空值）:")
	for k, v := range params {
		logs.Info("  %s: %v", k, v)
	}
	logs.Info("=== SafeGatePay提现回调签名验证参数详情结束 ===")

	// 从回调URL动态获取方法名
	methodName := c.getMethodNameFromWithdrawCallbackURL(callbackURL)
	logs.Info("SafeGatePay提现回调方法名: %s, 回调URL: %s", methodName, callbackURL)
	expectedSig := c.generateCallbackSignature(methodName, params, secretKey, hashAlgo)
	logs.Info("SafeGatePay提现回调期望签名: %s, 实际签名: %s", expectedSig, callback.SpSig)
	return strings.ToLower(expectedSig) == strings.ToLower(callback.SpSig)
}

// getMethodNameFromWithdrawCallbackURL 从提现回调URL获取方法名
func (c *safeGatePay) getMethodNameFromWithdrawCallbackURL(callbackURL string) string {
	// 解析URL，获取最后一段作为方法名（按照SafeGatePay文档要求）
	parts := strings.Split(strings.TrimSuffix(callbackURL, "/"), "/")
	logs.Info("提现URL解析: %s -> parts: %v", callbackURL, parts)

	if len(parts) > 0 {
		methodName := parts[len(parts)-1]
		// 如果方法名为空，返回默认值
		if methodName != "" {
			logs.Info("从最后一段获取提现方法名: %s", methodName)
			return methodName
		}
	}
	logs.Info("使用默认提现方法名: transfer_to_card_rus")
	return "transfer_to_card_rus" // 默认值
}

// debugWithdrawCallbackInfo 调试函数：打印完整的提现回调信息和签名验证过程
func (c *safeGatePay) debugWithdrawCallbackInfo(callback SafeGatePayCallback, secretKey string, hashAlgo string, callbackURL string) {
	logs.Info("========== SafeGatePay提现回调调试信息 ==========")

	// 1. 打印原始回调数据
	logs.Info("【原始提现回调数据】")
	logs.Info("sp_result: %v (类型: %T)", callback.SpResult, callback.SpResult)
	logs.Info("sp_outlet_id: %s (类型: %T)", callback.SpOutletID, callback.SpOutletID)
	logs.Info("sp_order_id: %s (类型: %T)", callback.SpOrderID, callback.SpOrderID)
	logs.Info("sp_amount: %s (类型: %T)", callback.SpAmount, callback.SpAmount)
	logs.Info("sp_payment_id: %d (类型: %T)", callback.SpPaymentID, callback.SpPaymentID)
	logs.Info("sp_init_amount: %s (类型: %T)", callback.SpInitAmount, callback.SpInitAmount)
	logs.Info("sp_net_amount: %s (类型: %T)", callback.SpNetAmount, callback.SpNetAmount)
	logs.Info("sp_ps_amount: %s (类型: %T)", callback.SpPsAmount, callback.SpPsAmount)
	logs.Info("sp_payment_system: %s (类型: %T)", callback.SpPaymentSystem, callback.SpPaymentSystem)
	logs.Info("sp_currency: %s (类型: %T)", callback.SpCurrency, callback.SpCurrency)
	logs.Info("sp_error_description: %s (类型: %T)", callback.SpErrorDescription, callback.SpErrorDescription)
	logs.Info("sp_salt: %v (类型: %T)", callback.SpSalt, callback.SpSalt)
	logs.Info("sp_sig: %s (类型: %T)", callback.SpSig, callback.SpSig)

	// 2. 打印配置信息
	logs.Info("【配置信息】")
	logs.Info("回调URL: %s", callbackURL)
	logs.Info("哈希算法: %s", hashAlgo)
	logs.Info("密钥长度: %d", len(secretKey))
	logs.Info("密钥前4位: %s", secretKey[:4])

	// 3. 获取方法名
	methodName := c.getMethodNameFromWithdrawCallbackURL(callbackURL)
	logs.Info("【方法名解析】")
	logs.Info("从URL解析的提现方法名: %s", methodName)

	// 4. 构建签名参数
	var saltStr string
	switch v := callback.SpSalt.(type) {
	case string:
		saltStr = v
	case float64:
		saltStr = fmt.Sprintf("%.0f", v)
	case int:
		saltStr = strconv.Itoa(v)
	case int64:
		saltStr = strconv.FormatInt(v, 10)
	default:
		saltStr = fmt.Sprintf("%v", v)
	}

	params := map[string]interface{}{
		"sp_amount":         callback.SpAmount,
		"sp_currency":       callback.SpCurrency,
		"sp_init_amount":    callback.SpInitAmount,
		"sp_net_amount":     callback.SpNetAmount,
		"sp_outlet_id":      callback.SpOutletID,
		"sp_payment_id":     callback.SpPaymentID,
		"sp_payment_system": callback.SpPaymentSystem,
		"sp_ps_amount":      callback.SpPsAmount,
		"sp_result":         strconv.Itoa(callback.SpResult),
		"sp_salt":           saltStr,
	}

	logs.Info("【签名参数构建】")
	logs.Info("处理后的sp_salt: %s", saltStr)
	logs.Info("参与签名的参数:")
	for k, v := range params {
		logs.Info("  %s: %v (类型: %T)", k, v, v)
	}

	// 5. 生成签名
	generatedSig := c.generateCallbackSignature(methodName, params, secretKey, hashAlgo)

	logs.Info("【签名验证结果】")
	logs.Info("生成的签名: %s", generatedSig)
	logs.Info("接收的签名: %s", callback.SpSig)
	logs.Info("签名匹配: %v", strings.EqualFold(generatedSig, callback.SpSig))

	logs.Info("========== 提现回调调试信息结束 ==========")
}
