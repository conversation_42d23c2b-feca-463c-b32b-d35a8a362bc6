package controller

import (
	"bytes"
	"crypto/aes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	daogorm "gorm.io/gorm"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/spf13/cast"
)

// Wali API请求结构体
type WaliTransferRequest struct {
	MerchantCode string `json:"merchantCode"`
	AgentName    string `json:"agentName"`
	UserName     string `json:"userName"`
	Amount       string `json:"amount"`
	OrderID      string `json:"orderId"`
	Currency     string `json:"currency"`
	Timestamp    string `json:"timestamp"`
	Sign         string `json:"sign"`
}

// Wali API查询订单请求结构体
type WaliQueryOrderRequest struct {
	MerchantCode string `json:"merchantCode"`
	OrderID      string `json:"orderId"`
	Timestamp    string `json:"timestamp"`
	Sign         string `json:"sign"`
}

// Wali API进入游戏请求结构体
type WaliEnterGameRequest struct {
	MerchantCode string `json:"merchantCode"`
	AgentName    string `json:"agentName"`
	UserName     string `json:"userName"`
	Currency     string `json:"currency"`
	Language     string `json:"language"`
	GameID       string `json:"gameId,omitempty"`
	GameType     string `json:"gameType,omitempty"`
	Timestamp    string `json:"timestamp"`
	Sign         string `json:"sign"`
}

// WaliTransferResponse 表示transferV3 API的响应
type WaliTransferResponse struct {
	OrderID string `json:"orderId"`
	Status  int    `json:"status"`
	Reason  string `json:"reason"`
	Balance string `json:"balance,omitempty"`
	Credit  string `json:"credit,omitempty"`
}

// WaliQueryOrderResponse 表示queryOrderV3 API的响应
type WaliQueryOrderResponse struct {
	OrderID string `json:"orderId"`
	Status  int    `json:"status"`
	Reason  string `json:"reason"`
	Credit  string `json:"credit,omitempty"`
	Balance string `json:"balance,omitempty"`
}

// WaliBalanceResponse 表示getBalance API的响应
type WaliBalanceResponse struct {
	Status       int    `json:"status"`
	Balance      string `json:"balance"`
	Transferable string `json:"transferable"`
}

// WaliAgentBalanceResponse 表示getAgentBalance API的响应
type WaliAgentBalanceResponse struct {
	Balance string `json:"balance"`
}

// WaliEnterGameResponse 表示enterGame API的响应
type WaliEnterGameResponse struct {
	GameURL    string `json:"gameUrl,omitempty"`
	GameReason string `json:"gameReason,omitempty"`
}

// Wali API响应基础结构体
type WaliBaseResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

type WaliClient struct {
	waliApiUrl      string
	waliApiKey      string
	waliCurrency    string
	waliAesKey      string
	waliSignKey     string
	waliAgentName   string
	DebugMode       bool
	thirdController *ThirdController
	brandName       string
	// 交易锁超时时间（秒）
	transactionLockTimeout int
}

// NewWaliClient 初始化Wali api 接口配置
func NewWaliClient(temp map[string]string, t *ThirdController) *WaliClient {
	return &WaliClient{

		waliApiUrl:      temp["api_url"],
		waliApiKey:      temp["api_key"],
		waliCurrency:    temp["currency"],
		waliAesKey:      temp["aes_key"],
		waliSignKey:     temp["sign_key"],
		waliAgentName:   temp["agent_name"],
		brandName:       "wali",
		DebugMode:       true,
		thirdController: t,
		// 设置交易锁超时时间为3秒
		transactionLockTimeout: 3,
	}
}

func (c *WaliClient) waliHttpPost(path string, params map[string]string) (*map[string]interface{}, error) {
	// 构建业务参数字符串
	var paramStr string
	for k, v := range params {
		if k != "sign" && v != "" {
			if paramStr != "" {
				paramStr += "&"
			}
			paramStr += k + "=" + v
		}
	}

	logs.Info("Wali API业务参数:", paramStr)

	// AES加密业务参数
	encryptedParams, err := c.waliAesEncrypt(paramStr)
	if err != nil {
		logs.Error("wali_http_post encrypt error:", err)
		return nil, fmt.Errorf("参数加密失败: %v", err)
	}

	// 生成签名
	t := fmt.Sprintf("%d", time.Now().Unix())
	k := c.waliSign(encryptedParams, t)

	// 对加密参数进行URL编码
	encryptedParamsEscaped := url.QueryEscape(encryptedParams)

	// 构建完整URL
	url := fmt.Sprintf("%s/%s?a=%s&t=%s&p=%s&k=%s",
		c.waliApiUrl,
		path,
		c.waliApiKey,
		t,
		encryptedParamsEscaped,
		k)

	logs.Info("Wali API请求URL:", url)

	// 发送GET请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logs.Error("wali_http_post create request error:", err)
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("wali_http_post request failed:", err)
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("wali_http_post read response error:", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	logs.Info("Wali API响应:", string(body))

	// 解析响应JSON
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("wali_http_post unmarshal error:", err)
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	code, ok := result["code"].(float64)
	if !ok {
		logs.Error("wali_http_post invalid response format")
		return nil, fmt.Errorf("响应格式无效")
	}

	if code != 0 {
		msg, _ := result["msg"].(string)
		logs.Error("wali_http_post API error, code:", code, "msg:", msg)
		return nil, fmt.Errorf("API错误: %v", msg)
	}

	return &result, nil
}

func (c *WaliClient) waliAesEncrypt(plaintext string) (string, error) {
	// 使用AES-128-ECB模式加密
	key := []byte(c.waliAesKey)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS#5/PKCS#7填充
	blockSize := block.BlockSize()
	plainBytes := []byte(plaintext)
	padding := blockSize - len(plainBytes)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	plainBytes = append(plainBytes, padText...)

	// 加密
	ciphertext := make([]byte, len(plainBytes))

	// 使用ECB模式加密
	for bs, be := 0, blockSize; bs < len(plainBytes); bs, be = bs+blockSize, be+blockSize {
		block.Encrypt(ciphertext[bs:be], plainBytes[bs:be])
	}

	// 返回Base64编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func (c *WaliClient) waliSign(p string, t string) string {
	// 构建签名字符串: p + t + SIGN_KEY
	signStr := p + t + c.waliSignKey

	// 计算MD5
	h := md5.New()
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// getTodayStartTimestamp 获取当天零点的时间戳（毫秒）
func (c *WaliClient) getTodayStartTimestamp() int64 {
	// 获取当前时间戳（毫秒）
	currentTimeMillis := time.Now().UnixNano() / 1000000
	return currentTimeMillis
}

// acquireTransactionLock 获取用户交易锁，确保同一用户在指定时间内只能进行一笔交易
// 返回值：是否成功获取锁，如果获取失败则返回错误
func (c *WaliClient) acquireTransactionLock(userId int) error {
	// 构造Redis锁的键名
	lockKey := fmt.Sprintf("%v:%v:wali_transaction_lock_%v", server.Project(), server.Module(), userId)

	// 尝试获取锁，设置过期时间为transactionLockTimeout秒
	lockResult := server.Redis().SetNxString(lockKey, "1", c.transactionLockTimeout)

	// 如果锁已存在，返回错误
	if lockResult != nil {
		logs.Warn("Wali交易锁获取失败，用户ID:", userId, "，请等待", c.transactionLockTimeout, "秒后再试")
		return fmt.Errorf("操作频繁，请等待%d秒后再试", c.transactionLockTimeout)
	}

	logs.Info("Wali交易锁获取成功，用户ID:", userId)
	return nil
}

// releaseTransactionLock 释放用户交易锁
func (c *WaliClient) releaseTransactionLock(userId int) {
	// 构造Redis锁的键名
	lockKey := fmt.Sprintf("%v:%v:wali_transaction_lock_%v", server.Project(), server.Module(), userId)

	// 删除锁
	server.Redis().Del(lockKey)
	logs.Info("Wali交易锁释放成功，用户ID:", userId)
}

// TransferDeposit 从平台转出，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *WaliClient) TransferDeposit(userId int, amount float64, orderId string) (int, error) {
	txStartTime := time.Now()
	txType := "转出(deposit)"
	//// 获取交易锁，确保同一用户在指定时间内只能进行一笔交易
	//if err := c.acquireTransactionLock(userId); err != nil {
	//	logs.Error(fmt.Sprintf("[Wali%s] 获取交易锁失败 - 用户ID: %d, 错误: %v", txType, userId, err))
	//	return TRANSFER_STATUS_NET_ERROR, err // 网络错误或其它错误
	//}
	//// 确保交易完成后释放锁
	//defer c.releaseTransactionLock(userId)

	// 参数验证
	if amount <= 0 {
		logs.Error(fmt.Sprintf("[Wali%s] 参数错误 - 用户ID: %d, 金额必须大于0", txType, userId))
		return TRANSFER_STATUS_FAILED, errors.New("金额必须大于0") // 失败
	}

	// 保存原始订单号，用于后续查询
	todayTimestamp := c.getTodayStartTimestamp()
	waliOrderId := fmt.Sprintf("%s_%d_%d_%s", c.waliAgentName, todayTimestamp, userId, orderId)
	logs.Info(fmt.Sprintf("[Wali%s] 开始处理 - 用户ID: %d, 原始订单号: %s, 查询订单号: %s",
		txType, userId, orderId, waliOrderId))

	// 构造请求参数
	params := map[string]string{
		"orderId": waliOrderId,
		"uid":     fmt.Sprintf("%d", userId),
		"credit":  fmt.Sprintf("%.2f", amount),
		"ccy":     c.waliCurrency,
	}

	// 发送上分请求
	logs.Info(fmt.Sprintf("[Wali%s] 发送请求 - 用户ID: %d, 参数: %v", txType, userId, params))
	result, err := c.waliHttpPost("transferV3", params)
	if err != nil {
		logs.Error(fmt.Sprintf("[Wali%s] 请求失败 - 用户ID: %d, 错误: %v", txType, userId, err))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("存款请求失败: %v", err) // 网络错误或其它错误
	}

	// 检查上分结果
	data := (*result)["data"].(map[string]interface{})

	// 将响应数据转换为结构体
	response := WaliTransferResponse{
		OrderID: cast.ToString(data["orderId"]),
		Status:  cast.ToInt(data["status"]),
		Reason:  cast.ToString(data["reason"]),
		Balance: cast.ToString(data["balance"]),
		Credit:  cast.ToString(data["credit"]),
	}

	// 记录完整的响应信息
	//logs.Info(fmt.Sprintf("[Wali%s] 收到响应 - 用户ID: %d, 订单号: %s, 状态: %d, 原因: %s, 余额: %s, 金额: %s",
	//	txType, userId, response.OrderID, response.Status, response.Reason, response.Balance, response.Credit))

	// 开启事务
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("wali 开启事务失败: err=", tx.Error.Error())
	}
	defer tx.Rollback()

	//保存原始订单号，用于后续查单
	memo := strconv.Itoa(response.Status) + response.Reason
	e := c.AddThirdAmountLog(tx, -amount, 129, userId, orderId, waliOrderId, memo)
	if e != nil {
		logs.Error("wali 插入三方账变记录失败: err=", e.Error())
	}

	// 提交事务
	if e = tx.Commit().Error; e != nil {
		logs.Error("wali 提交事务失败: err=", e.Error())
	}

	// 根据状态码处理
	switch response.Status {
	case 1: // 已成功，用户余额已更新

	// 继续执行成功逻辑
	case 2: // 已失败，放弃更新用户余额
		logs.Error(fmt.Sprintf("[Wali%s] 交易失败 - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_FAILED, fmt.Errorf("存款失败，状态: %d, 原因: %s", response.Status, response.Reason)
	case 0: // 处理中
		logs.Warning(fmt.Sprintf("[Wali%s] 交易处理中 - 用户ID: %d, 状态: %d", txType, userId, response.Status))
		return TRANSFER_STATUS_PROCESSING, fmt.Errorf("存款处理中，请稍后查询")
	case -2: // 不支持此操作
		logs.Error(fmt.Sprintf("[Wali%s] 不支持此操作 - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("不支持此操作，状态: %d, 原因: %s", response.Status, response.Reason)
	default: // 其他未知状态
		logs.Error(fmt.Sprintf("[Wali%s] 交易失败(未知状态) - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("存款失败，状态: %d, 原因: %s", response.Status, response.Reason)
	}

	// 计算交易耗时并记录成功信息
	txDuration := time.Since(txStartTime)
	logs.Info(fmt.Sprintf("[Wali%s] 交易成功 - 用户ID: %d, 订单号: %s, 金额: %.4f, 耗时: %v", txType, userId, waliOrderId, amount, txDuration))

	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

// TransferWithdraw 转入到平台，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *WaliClient) TransferWithdraw(userId int, amount float64, orderId string) (int, error) {
	txStartTime := time.Now()
	txType := "转入(withdraw)"

	//// 获取交易锁，确保同一用户在指定时间内只能进行一笔交易
	//if err := c.acquireTransactionLock(userId); err != nil {
	//	logs.Error(fmt.Sprintf("[Wali%s] 获取交易锁失败 - 用户ID: %d, 错误: %v", txType, userId, err))
	//	return TRANSFER_STATUS_NET_ERROR, err // 网络错误或其它错误
	//}
	//// 确保交易完成后释放锁
	//defer c.releaseTransactionLock(userId)

	// 参数验证
	if amount <= 0 {
		logs.Error(fmt.Sprintf("[Wali%s] 参数错误 - 用户ID: %d, 金额必须大于0", txType, userId))
		return TRANSFER_STATUS_FAILED, errors.New("金额必须大于0") // 失败
	}

	todayTimestamp := c.getTodayStartTimestamp()
	waliOrderId := fmt.Sprintf("%s_%d_%d_%s", c.waliAgentName, todayTimestamp, userId, orderId)
	logs.Info(fmt.Sprintf("[Wali%s] 开始处理 - 用户ID: %d, 原始订单号: %s, Wali平台订单号: %s", txType, userId, orderId, waliOrderId))

	// 构造请求参数
	params := map[string]string{
		"orderId": waliOrderId,
		"uid":     fmt.Sprintf("%d", userId),
		"credit":  fmt.Sprintf("-%.2f", amount), // 下分使用负数
		"ccy":     c.waliCurrency,
	}

	// 发送下分请求
	logs.Info(fmt.Sprintf("[Wali%s] 发送请求 - 用户ID: %d, 参数: %v", txType, userId, params))
	result, err := c.waliHttpPost("transferV3", params)
	if err != nil {
		logs.Error(fmt.Sprintf("[Wali%s] 请求失败 - 用户ID: %d, 错误: %v", txType, userId, err))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("取款请求失败: %v", err) // 网络错误或其它错误
	}

	// 检查下分结果
	data := (*result)["data"].(map[string]interface{})

	// 将响应数据转换为结构体
	response := WaliTransferResponse{
		OrderID: cast.ToString(data["orderId"]),
		Status:  cast.ToInt(data["status"]),
		Reason:  cast.ToString(data["reason"]),
		Balance: cast.ToString(data["balance"]),
		Credit:  cast.ToString(data["credit"]),
	}

	// 记录完整的响应信息
	//logs.Info(fmt.Sprintf("[Wali%s] 收到响应 - 用户ID: %d, 订单号: %s, 状态: %d, 原因: %s, 余额: %s, 金额: %s",
	//	txType, userId, response.OrderID, response.Status, response.Reason, response.Balance, response.Credit))

	// 开启事务
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("wali 开启事务失败: err=", tx.Error.Error())
	}
	defer tx.Rollback()

	//保存原始订单号，用于后续查单
	memo := strconv.Itoa(response.Status) + response.Reason
	e := c.AddThirdAmountLog(tx, amount, 128, userId, orderId, waliOrderId, memo)
	if e != nil {
		logs.Error("wali 插入三方账变记录失败: err=", e.Error())
	}

	// 提交事务
	if e = tx.Commit().Error; e != nil {
		logs.Error("wali 提交事务失败: err=", e.Error())
	}

	// 根据状态码处理
	switch response.Status {
	case 1: // 已成功，用户余额已更新

	// 继续执行成功逻辑
	case 2: // 已失败，放弃更新用户余额
		logs.Error(fmt.Sprintf("[Wali%s] 交易失败 - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_FAILED, fmt.Errorf("取款失败，状态: %d, 原因: %s", response.Status, response.Reason)
	case 0: // 处理中
		logs.Warning(fmt.Sprintf("[Wali%s] 交易处理中 - 用户ID: %d, 状态: %d", txType, userId, response.Status))
		return TRANSFER_STATUS_PROCESSING, fmt.Errorf("取款处理中，请稍后查询")
	case -2: // 不支持此操作
		logs.Error(fmt.Sprintf("[Wali%s] 不支持此操作 - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("不支持此操作，状态: %d, 原因: %s", response.Status, response.Reason)
	default: // 其他未知状态
		logs.Error(fmt.Sprintf("[Wali%s] 交易失败(未知状态) - 用户ID: %d, 状态: %d, 原因: %s", txType, userId, response.Status, response.Reason))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("取款失败，状态: %d, 原因: %s", response.Status, response.Reason)
	}

	// 计算交易耗时并记录成功信息
	txDuration := time.Since(txStartTime)
	logs.Info(fmt.Sprintf("[Wali%s] 交易成功 - 用户ID: %d, 订单号: %s, 金额: %.4f, 耗时: %v",
		txType, userId, waliOrderId, amount, txDuration))

	return TRANSFER_STATUS_SUCCESS, nil // 成功
}

/**
 * 记录三方账变和单号用于查询注单金额
 */
func (s *WaliClient) AddThirdAmountLog(tx *daogorm.DB, changeAmount float64, reason int, userId int, thirdId string, transactionId string, memo string) error {
	amountLog := thirdGameModel.ThirdAmountLog{
		UserId:        userId,
		Amount:        changeAmount,
		ThirdId:       thirdId,
		TransactionId: transactionId,
		Reason:        reason,
		Memo:          memo,
		Brand:         s.brandName,
		CreateTime:    utils.GetCurrentTime(),
	}
	e := tx.Table("x_third_amount_log").Create(&amountLog).Error
	if e != nil {
		return e
	}
	return nil
}

// QueryOrder 查询订单状态，返回状态码和错误
// 状态码：TRANSFER_STATUS_SUCCESS-成功，TRANSFER_STATUS_FAILED-失败，TRANSFER_STATUS_PROCESSING-处理中，TRANSFER_STATUS_NET_ERROR-网络错误或其它错误
func (c *WaliClient) QueryOrder(orderId string, userId int) (int, error) {
	txStartTime := time.Now()
	txType := "查询订单"
	logs.Info(fmt.Sprintf("[Wali%s] 开始处理 - 用户ID: %d, 订单号: %s", txType, userId, orderId))

	// 参数验证
	if orderId == "" {
		logs.Error(fmt.Sprintf("[Wali%s] 参数错误 - 用户ID: %d, 订单号不能为空", txType, userId))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("订单号不能为空") // 失败
	}

	originalOrderId := orderId
	// 查询账变记录，确定订单号
	thirdAmountLog := thirdGameModel.ThirdAmountLog{}
	e := server.Db().GormDao().Table("x_third_amount_log").
		Where("UserId = ? and ThirdId = ? and Brand = ? ", userId, originalOrderId, c.brandName).
		First(&thirdAmountLog).Error
	if e != nil {
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			logs.Error(" 账变记录不存在 userId=", userId, " thirdId=", originalOrderId, " error=", e.Error())
		}
		logs.Error(fmt.Sprintf("[Wali%s] 查询订单失败 - 用户ID: %d, 订单号: %s, 错误: %v", txType, userId, originalOrderId, e))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("查询订单失败可能单号不存在") // 订单不存在
	}

	// 尝试从订单号中提取Wali平台的订单号格式
	waliOrderId := thirdAmountLog.TransactionId

	// 构造请求参数
	params := map[string]string{
		"orderId": waliOrderId,
	}

	// 发送查询请求
	logs.Info(fmt.Sprintf("[Wali%s] 发送请求 - 用户ID: %d, 参数: %v", txType, userId, params))
	result, err := c.waliHttpPost("queryOrderV3", params)
	if err != nil {
		logs.Error(fmt.Sprintf("[Wali%s] 请求失败 - 用户ID: %d, 订单号: %s, 错误: %v", txType, userId, originalOrderId, err))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("查询订单失败: %v", err) // 订单不存在
	}

	// 检查订单状态
	data := (*result)["data"].(map[string]interface{})

	// 将响应数据转换为结构体
	response := WaliQueryOrderResponse{
		OrderID: cast.ToString(data["orderId"]),
		Status:  cast.ToInt(data["status"]),
		Reason:  cast.ToString(data["reason"]),
		Credit:  cast.ToString(data["credit"]),
		Balance: cast.ToString(data["balance"]),
	}

	// 记录完整的响应信息
	logs.Info(fmt.Sprintf("[Wali%s] 收到响应 - 用户ID: %d, 订单号: %s, 状态: %d, 原因: %s, 金额: %s, 余额: %s",
		txType, userId, response.OrderID, response.Status, response.Reason, response.Credit, response.Balance))

	// 计算查询耗时
	txDuration := time.Since(txStartTime)

	// 根据订单状态返回相应的状态码
	switch response.Status {
	case 1: // 已成功，用户余额已更新
		logs.Info(fmt.Sprintf("[Wali%s] 订单成功 - 用户ID: %d, 订单号: %s, 耗时: %v", txType, userId, originalOrderId, txDuration))
		return TRANSFER_STATUS_SUCCESS, nil // 成功

	case 2: // 已失败，放弃更新用户余额
		logs.Error(fmt.Sprintf("[Wali%s] 订单失败 - 用户ID: %d, 订单号: %s, 原因: %s, 耗时: %v",
			txType, userId, originalOrderId, response.Reason, txDuration))
		return TRANSFER_STATUS_FAILED, fmt.Errorf("订单失败: %s, 原因: %s", originalOrderId, response.Reason) // 失败

	case 0: // 处理中
		logs.Warning(fmt.Sprintf("[Wali%s] 订单处理中 - 用户ID: %d, 订单号: %s, 耗时: %v", txType, userId, originalOrderId, txDuration))
		return TRANSFER_STATUS_PROCESSING, fmt.Errorf("订单处理中: %s, 请稍后再查询", originalOrderId) // 处理中

	case -1: // 未找到此单（仅用于查询接口）
		logs.Error(fmt.Sprintf("[Wali%s] 订单不存在 - 用户ID: %d, 订单号: %s, 耗时: %v", txType, userId, originalOrderId, txDuration))
		return TRANSFER_STATUS_FAILED, fmt.Errorf("订单不存在: %s", originalOrderId) // 失败

	case -2: // 不支持此操作
		logs.Error(fmt.Sprintf("[Wali%s] 不支持此操作 - 用户ID: %d, 订单号: %s, 耗时: %v", txType, userId, originalOrderId, txDuration))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("不支持此操作，订单号: %s", originalOrderId) // 失败

	default: // 未知状态
		logs.Error(fmt.Sprintf("[Wali%s] 未知订单状态 - 用户ID: %d, 订单号: %s, 状态: %d, 耗时: %v",
			txType, userId, originalOrderId, response.Status, txDuration))
		return TRANSFER_STATUS_NET_ERROR, fmt.Errorf("未知订单状态: %d, 订单号: %s", response.Status, originalOrderId) // 网络错误或其它错误
	}
}

// GetLoginUrl 登录到平台
func (c *WaliClient) GetLoginUrl(ctx *abugo.AbuHttpContent) {
	// 获取用户ID
	logs.Info("wali开始登录")

	// 获取游戏类型和ID
	type RequestData struct {
		GameId   string `validate:"required"`
		LangCode string `validate:"required"`
		Ip       string `json:"ip"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("wali真人登录失败")
		ctx.RespErr(err, &errcode)
		return
	}

	// 获取用户token
	token := server.GetToken(ctx)

	//三方全局并发锁-
	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	//瓦力单独交易锁 确保3秒内只能有一笔登录请求
	if err := c.acquireTransactionLock(token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 验证用户
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	user, _ := server.Db().Table("x_user").Select("State").Where(where).GetOne()
	if user == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	state := abugo.GetInt64FromInterface((*user)["State"])
	if state != 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	// 检查游戏入口权限
	brand := "wali"
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, brand, reqdata.GameId)
	if err != nil {
		logs.Error(brand, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", "UP", " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 同步余额
	err = c.thirdController.sync_amountex(token.UserId, TRANSFER_PLATFORM_WALI)
	if err != nil {
		logs.Error("wali_login 同步余额失败", err)
	}

	// 构造请求参数
	params := map[string]string{
		"uid": fmt.Sprintf("%d", token.UserId),
		"ccy": c.waliCurrency,
	}

	// 如果提供了IP，则添加到请求中
	if reqdata.Ip != "" {
		params["ip"] = reqdata.Ip
	}

	// 如果提供了游戏类型和ID，则添加到请求中
	if reqdata.GameId != "" {
		params["game"] = reqdata.GameId
	}

	// 发送进入游戏请求
	result, err := c.waliHttpPost("enterGame", params)
	if err != nil {
		logs.Error("wali_login request failed:", err)
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	// 获取游戏URL
	data := (*result)["data"].(map[string]interface{})

	// 将响应数据转换为结构体
	response := WaliEnterGameResponse{
		GameURL:    cast.ToString(data["gameUrl"]),
		GameReason: cast.ToString(data["gameReason"]),
	}

	// 检查是否有失败原因
	if response.GameReason != "" {
		logs.Error("wali_login failed with reason:", response.GameReason)
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	if response.GameURL == "" {
		logs.Error("wali_login no game URL returned")
		ctx.RespErrString(true, &errcode, "进入游戏失败,请稍后再试")
		return
	}

	// 登录成功后执行转账
	err = c.thirdController.third_transfer_out(TRANSFER_PLATFORM_WALI, token.UserId, "")
	if err != nil {
		logs.Error("wali_login 同步余额失败", err)
		ctx.RespErrString(true, &errcode, "进入失败 "+err.Error())
		return
	}

	// 返回游戏URL
	ctx.Put("url", response.GameURL)
	ctx.RespOK()
}

// GetBalance 获取用户在平台上的余额
func (c *WaliClient) GetBalance(userId int) (float64, error) {
	logs.Info("wali_get_balance 参数 userId:", userId)

	// 构造请求参数
	params := map[string]string{
		"uid": fmt.Sprintf("%d", userId),
	}

	// 发送查询余额请求
	result, err := c.waliHttpPost("getBalance", params)
	if err != nil {
		logs.Error("wali_get_balance request failed:", err)
		return 0, fmt.Errorf("查询余额失败: %v", err)
	}

	// 获取余额信息
	data := (*result)["data"].(map[string]interface{})

	// 将响应数据转换为结构体
	response := WaliBalanceResponse{
		Status:       cast.ToInt(data["status"]),
		Balance:      cast.ToString(data["balance"]),
		Transferable: cast.ToString(data["transferable"]),
	}

	// 检查用户状态
	if response.Status == -2 {
		logs.Error("用户已被封禁:", userId)
		return 0, fmt.Errorf("用户已被封禁")
	} else if response.Status == -1 {
		logs.Error("用户未注册:", userId)
		return 0, nil // 用户未注册，返回0余额
	}

	// 返回可下分余额
	transferable := cast.ToFloat64(response.Transferable)
	return transferable, nil
}
