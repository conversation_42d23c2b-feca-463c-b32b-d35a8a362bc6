package sms

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"

	"github.com/beego/beego/logs"
	"github.com/spf13/viper"
)

// SmsConfig SMS接口配置
type SmsConfig struct {
	URL      string `json:"url" mapstructure:"url"`
	SpId     string `json:"sp_id" mapstructure:"sp_id"`
	Password string `json:"password" mapstructure:"password"`
	Enabled  bool   `json:"enabled" mapstructure:"enabled"`
	AesKey   string `json:"aes_key" mapstructure:"aes_key"`
}

// SmsService SMS服务结构体
type SmsService struct {
	Config SmsConfig
}

// 单条短信发送请求结构
type SingleSmsRequest struct {
	SpId      string `json:"sp_id"`
	Mobile    string `json:"mobile"`
	Content   string `json:"content"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
	Ext       string `json:"ext,omitempty"`
}

// 批量短信发送请求结构
type BatchSmsRequest struct {
	SpId      string `json:"sp_id"`
	Mobiles   string `json:"mobiles"`
	Content   string `json:"content"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
	Ext       string `json:"ext,omitempty"`
}

// 变量短信发送请求结构
type VariableSmsRequest struct {
	SpId      string `json:"sp_id"`
	Params    string `json:"params"`
	Content   string `json:"content"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
	Ext       string `json:"ext,omitempty"`
}

// 一对一批量发送请求结构
type BiUniqueSmsRequest struct {
	SpId      string `json:"sp_id"`
	Params    string `json:"params"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
	Ext       string `json:"ext,omitempty"`
}

// SMS响应结构
type SmsResponse struct {
	Code       int                    `json:"code"`
	Msg        string                 `json:"msg"`
	MsgId      interface{}            `json:"msg_id,omitempty"` // 改为interface{}以支持数字和字符串
	Data       interface{}            `json:"data,omitempty"`   // 改为interface{}以支持不同类型
	FailedData map[string]interface{} `json:"failed_data,omitempty"`
}

// 余额查询响应
type BalanceResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Balance string `json:"balance"`
	} `json:"data"`
}

// 状态报告响应
type ReportResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"` // 改为interface{}以支持不同类型
}

// 模板报备请求
type TemplateRequest struct {
	SpId      string `json:"sp_id"`
	Template  string `json:"template"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
}

// 模板报备响应
type TemplateResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		TempId int `json:"temp_id"`
	} `json:"data"`
}

// 签名报备请求
type SignRequest struct {
	SpId      string `json:"sp_id"`
	Sign      string `json:"sign"`
	Signature string `json:"signature,omitempty"`
	Password  string `json:"password,omitempty"`
}

// 签名报备响应
type SignResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		SignId int `json:"sign_id"`
	} `json:"data"`
}

// 全局SMS服务实例
var smsService *SmsService

// InitSmsService 初始化SMS服务
func InitSmsService() {
	// 强制重新读取配置
	smsService = &SmsService{}

	// 直接读取配置值
	url := viper.GetString("new_sms.url")
	spId := viper.GetString("new_sms.sp_id")
	password := viper.GetString("new_sms.password")
	enabled := viper.GetBool("new_sms.enabled")
	aesKey := viper.GetString("new_sms.aes_key")

	// 设置配置
	smsService.Config.URL = url
	smsService.Config.SpId = spId
	smsService.Config.Password = password
	smsService.Config.Enabled = enabled
	smsService.Config.AesKey = aesKey

	// 临时解决方案：如果配置为空，使用硬编码配置
	if smsService.Config.URL == "" {
		smsService.Config.URL = "http://www.onesnok.net:9511/api"
		smsService.Config.SpId = "470844"
		smsService.Config.Password = "5f030f3b86365c8a943ed26ceca09313"
		smsService.Config.Enabled = true
		smsService.Config.AesKey = ""
	}

	if !smsService.Config.Enabled {
		logs.Warn("SMS服务已禁用，请在配置文件中设置 new_sms.enabled = true")
	}
	if smsService.Config.URL == "" {
		logs.Error("SMS服务URL未配置，请检查 new_sms.url 配置")
	}
	if smsService.Config.SpId == "" {
		logs.Error("SMS服务SpId未配置，请检查 new_sms.sp_id 配置")
	}
}

// GetSmsService 获取SMS服务实例
func GetSmsService() *SmsService {
	if smsService == nil {
		InitSmsService()
	}
	return smsService
}

// IsEnabled 检查SMS接口是否启用
func (s *SmsService) IsEnabled() bool {
	return s.Config.Enabled
}

// SendSingleSms 发送单条短信
func (s *SmsService) SendSingleSms(mobile, content string) (*SmsResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	request := SingleSmsRequest{
		SpId:     s.Config.SpId,
		Mobile:   mobile,
		Content:  content,
		Password: s.Config.Password,
	}

	return s.sendRequest("/send-sms-single", request)
}

// SendBatchSms 发送批量短信
func (s *SmsService) SendBatchSms(mobiles []string, content string) (*SmsResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	request := BatchSmsRequest{
		SpId:     s.Config.SpId,
		Mobiles:  strings.Join(mobiles, ","),
		Content:  content,
		Password: s.Config.Password,
	}

	return s.sendRequest("/send-sms-batch", request)
}

// SendVariableSms 发送变量短信
func (s *SmsService) SendVariableSms(params []string, content string) (*SmsResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	request := VariableSmsRequest{
		SpId:     s.Config.SpId,
		Params:   strings.Join(params, ";"),
		Content:  content,
		Password: s.Config.Password,
	}

	return s.sendRequest("/send-variable", request)
}

// SendBiUniqueSms 发送一对一批量短信
func (s *SmsService) SendBiUniqueSms(params map[string]string) (*SmsResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	paramsJson, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("参数序列化失败: %v", err)
	}

	request := BiUniqueSmsRequest{
		SpId:     s.Config.SpId,
		Params:   string(paramsJson),
		Password: s.Config.Password,
	}

	return s.sendRequest("/send-biunique", request)
}

// GetBalance 获取余额
func (s *SmsService) GetBalance() (*BalanceResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	params := url.Values{}
	params.Add("sp_id", s.Config.SpId)
	params.Add("password", s.Config.Password)

	resp, err := http.Get(fmt.Sprintf("%s/balance?%s", s.Config.URL, params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result BalanceResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// GetReport 获取状态报告
func (s *SmsService) GetReport() (*ReportResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	params := url.Values{}
	params.Add("sp_id", s.Config.SpId)
	params.Add("password", s.Config.Password)

	resp, err := http.Get(fmt.Sprintf("%s/report?%s", s.Config.URL, params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result ReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// GetReply 获取上行回复
func (s *SmsService) GetReply() (*ReportResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	params := url.Values{}
	params.Add("sp_id", s.Config.SpId)
	params.Add("password", s.Config.Password)

	resp, err := http.Get(fmt.Sprintf("%s/get-reply?%s", s.Config.URL, params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result ReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// AddTemplate 模板报备
func (s *SmsService) AddTemplate(template string) (*TemplateResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	formData := url.Values{}
	formData.Add("sp_id", s.Config.SpId)
	formData.Add("template", template)
	formData.Add("password", s.Config.Password)

	resp, err := http.PostForm(s.Config.URL+"/add-template", formData)
	if err != nil {
		logs.Error("模板报备请求失败:", err)
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取模板报备响应失败:", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result TemplateResponse
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("解析模板报备响应失败:", err, "响应内容:", string(body))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// GetTemplateStatus 获取模板审核状态
func (s *SmsService) GetTemplateStatus(tempId int) (*ReportResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	params := url.Values{}
	params.Add("sp_id", s.Config.SpId)
	params.Add("password", s.Config.Password)
	params.Add("temp_id", fmt.Sprintf("%d", tempId))

	resp, err := http.Get(fmt.Sprintf("%s/get-template-status?%s", s.Config.URL, params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result ReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// AddSign 签名报备
func (s *SmsService) AddSign(sign string) (*SignResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	formData := url.Values{}
	formData.Add("sp_id", s.Config.SpId)
	formData.Add("sign", sign)
	formData.Add("password", s.Config.Password)

	resp, err := http.PostForm(s.Config.URL+"/add-sign", formData)
	if err != nil {
		logs.Error("签名报备请求失败:", err)
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取签名报备响应失败:", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result SignResponse
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("解析签名报备响应失败:", err, "响应内容:", string(body))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// GetSignStatus 获取签名审核状态
func (s *SmsService) GetSignStatus(signId int) (*ReportResponse, error) {
	if !s.IsEnabled() {
		return nil, fmt.Errorf("SMS接口未启用")
	}

	params := url.Values{}
	params.Add("sp_id", s.Config.SpId)
	params.Add("password", s.Config.Password)
	params.Add("sign_id", fmt.Sprintf("%d", signId))

	resp, err := http.Get(fmt.Sprintf("%s/get-sign-status?%s", s.Config.URL, params.Encode()))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result ReportResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// sendRequest 发送POST请求的通用方法
func (s *SmsService) sendRequest(endpoint string, data interface{}) (*SmsResponse, error) {
	// 将结构体转换为form数据
	formData := url.Values{}

	// 使用类型断言来处理不同的请求类型
	switch req := data.(type) {
	case SingleSmsRequest:
		formData.Add("sp_id", req.SpId)
		formData.Add("mobile", req.Mobile)
		formData.Add("content", req.Content)
		formData.Add("password", req.Password)
		if req.Ext != "" {
			formData.Add("ext", req.Ext)
		}
	case BatchSmsRequest:
		formData.Add("sp_id", req.SpId)
		formData.Add("mobiles", req.Mobiles)
		formData.Add("content", req.Content)
		formData.Add("password", req.Password)
		if req.Ext != "" {
			formData.Add("ext", req.Ext)
		}
	case VariableSmsRequest:
		formData.Add("sp_id", req.SpId)
		formData.Add("params", req.Params)
		formData.Add("content", req.Content)
		formData.Add("password", req.Password)
		if req.Ext != "" {
			formData.Add("ext", req.Ext)
		}
	case BiUniqueSmsRequest:
		formData.Add("sp_id", req.SpId)
		formData.Add("params", req.Params)
		formData.Add("password", req.Password)
		if req.Ext != "" {
			formData.Add("ext", req.Ext)
		}
	}

	// 发送POST请求
	resp, err := http.PostForm(s.Config.URL+endpoint, formData)
	if err != nil {
		logs.Error("发送SMS请求失败:", err)
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取SMS响应失败:", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var result SmsResponse
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("解析SMS响应失败:", err, "响应内容:", string(body))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 记录发送结果
	if result.Code != 0 {
		logs.Error("SMS发送失败: code=%d, msg=%s", result.Code, result.Msg)
	}

	return &result, nil
}

// generateSignature 生成签名
func (s *SmsService) generateSignature(params map[string]string) string {
	// 第一步：准备QueryString
	// 1. 将所有参数（除了signature）按照key排序
	var keys []string
	for k := range params {
		if k != "signature" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 2. 将排序好的参数key和值进行URLEncode，并进行字符替换
	var queryParts []string
	for _, k := range keys {
		v := params[k]
		// URLEncode
		encodedKey := url.QueryEscape(k)
		encodedValue := url.QueryEscape(v)

		// 字符替换：加号（+）替换成 %20、星号（*）替换成 %2A、%7E 替换回波浪号（~）
		encodedKey = strings.ReplaceAll(encodedKey, "+", "%20")
		encodedKey = strings.ReplaceAll(encodedKey, "*", "%2A")
		encodedKey = strings.ReplaceAll(encodedKey, "%7E", "~")

		encodedValue = strings.ReplaceAll(encodedValue, "+", "%20")
		encodedValue = strings.ReplaceAll(encodedValue, "*", "%2A")
		encodedValue = strings.ReplaceAll(encodedValue, "%7E", "~")

		queryParts = append(queryParts, encodedKey+"="+encodedValue)
	}

	// 3. 拼接QueryString
	queryString := strings.Join(queryParts, "&")

	// 4. 在QueryString前拼接请求方式
	finalQueryString := "POST&" + url.QueryEscape("/") + "&" + url.QueryEscape(queryString)

	// 第二步：生成signature
	// 1. 使用HmacSHA1算法进行编码，使用产品密码作为密钥
	h := hmac.New(sha1.New, []byte(s.Config.Password))
	h.Write([]byte(finalQueryString))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

// encryptAES AES加密
func (s *SmsService) encryptAES(value string) (string, error) {
	if s.Config.AesKey == "" {
		return value, nil // 如果没有配置AES密钥，直接返回原值
	}

	// AES-128-CBC加密
	key := []byte(s.Config.AesKey)
	if len(key) != 16 {
		return "", fmt.Errorf("AES密钥长度必须为16字节")
	}

	// 生成随机IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", fmt.Errorf("生成IV失败: %v", err)
	}

	// 创建AES加密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建AES加密器失败: %v", err)
	}

	// PKCS7填充
	plaintext := []byte(value)
	padding := aes.BlockSize - len(plaintext)%aes.BlockSize
	padtext := make([]byte, len(plaintext)+padding)
	copy(padtext, plaintext)
	for i := len(plaintext); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	// CBC模式加密
	mode := cipher.NewCBCEncrypter(block, iv)
	ciphertext := make([]byte, len(padtext))
	mode.CryptBlocks(ciphertext, padtext)

	// 将IV和加密结果进行base64编码
	ivBase64 := base64.StdEncoding.EncodeToString(iv)
	valueBase64 := base64.StdEncoding.EncodeToString(ciphertext)

	// 组装成JSON格式
	jsonData := map[string]string{
		"iv":    ivBase64,
		"value": valueBase64,
	}
	jsonBytes, err := json.Marshal(jsonData)
	if err != nil {
		return "", fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 再次base64编码
	result := base64.StdEncoding.EncodeToString(jsonBytes)
	return result, nil
}

// MD5Hash 生成MD5哈希
func (s *SmsService) MD5Hash(text string) string {
	h := md5.New()
	h.Write([]byte(text))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// GetMsgIdString 获取字符串格式的消息ID
func (r *SmsResponse) GetMsgIdString() string {
	if r.MsgId == nil {
		return ""
	}

	switch v := r.MsgId.(type) {
	case string:
		return v
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case float64:
		return fmt.Sprintf("%.0f", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}
