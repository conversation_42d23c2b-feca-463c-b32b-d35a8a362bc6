// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdQuwei(db *gorm.DB, opts ...gen.DOOption) xThirdQuwei {
	_xThirdQuwei := xThirdQuwei{}

	_xThirdQuwei.xThirdQuweiDo.UseDB(db, opts...)
	_xThirdQuwei.xThirdQuweiDo.UseModel(&model.XThirdQuwei{})

	tableName := _xThirdQuwei.xThirdQuweiDo.TableName()
	_xThirdQuwei.ALL = field.NewAsterisk(tableName)
	_xThirdQuwei.ID = field.NewInt64(tableName, "Id")
	_xThirdQuwei.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdQuwei.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdQuwei.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdQuwei.UserID = field.NewInt32(tableName, "UserId")
	_xThirdQuwei.Brand = field.NewString(tableName, "Brand")
	_xThirdQuwei.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdQuwei.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdQuwei.GameID = field.NewString(tableName, "GameId")
	_xThirdQuwei.GameName = field.NewString(tableName, "GameName")
	_xThirdQuwei.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdQuwei.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdQuwei.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdQuwei.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdQuwei.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdQuwei.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdQuwei.Currency = field.NewString(tableName, "Currency")
	_xThirdQuwei.RawData = field.NewString(tableName, "RawData")
	_xThirdQuwei.State = field.NewInt32(tableName, "State")
	_xThirdQuwei.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdQuwei.DataState = field.NewInt32(tableName, "DataState")
	_xThirdQuwei.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdQuwei.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdQuwei.CSID = field.NewString(tableName, "CSId")
	_xThirdQuwei.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdQuwei.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdQuwei.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdQuwei.GameRst = field.NewString(tableName, "GameRst")
	_xThirdQuwei.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdQuwei.IP = field.NewString(tableName, "Ip")
	_xThirdQuwei.Lang = field.NewString(tableName, "Lang")
	_xThirdQuwei.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdQuwei.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdQuwei.RefundAmount = field.NewFloat64(tableName, "RefundAmount")
	_xThirdQuwei.BetType = field.NewInt32(tableName, "BetType")
	_xThirdQuwei.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdQuwei.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdQuwei.fillFieldMap()

	return _xThirdQuwei
}

type xThirdQuwei struct {
	xThirdQuweiDo xThirdQuweiDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	ThirdRefID     field.String  // 三方备用注单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	RefundAmount   field.Float64 // 反水金额updown需求
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdQuwei) Table(newTableName string) *xThirdQuwei {
	x.xThirdQuweiDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdQuwei) As(alias string) *xThirdQuwei {
	x.xThirdQuweiDo.DO = *(x.xThirdQuweiDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdQuwei) updateTableName(table string) *xThirdQuwei {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.RefundAmount = field.NewFloat64(table, "RefundAmount")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdQuwei) WithContext(ctx context.Context) *xThirdQuweiDo {
	return x.xThirdQuweiDo.WithContext(ctx)
}

func (x xThirdQuwei) TableName() string { return x.xThirdQuweiDo.TableName() }

func (x xThirdQuwei) Alias() string { return x.xThirdQuweiDo.Alias() }

func (x xThirdQuwei) Columns(cols ...field.Expr) gen.Columns { return x.xThirdQuweiDo.Columns(cols...) }

func (x *xThirdQuwei) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdQuwei) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 37)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["RefundAmount"] = x.RefundAmount
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdQuwei) clone(db *gorm.DB) xThirdQuwei {
	x.xThirdQuweiDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdQuwei) replaceDB(db *gorm.DB) xThirdQuwei {
	x.xThirdQuweiDo.ReplaceDB(db)
	return x
}

type xThirdQuweiDo struct{ gen.DO }

func (x xThirdQuweiDo) Debug() *xThirdQuweiDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdQuweiDo) WithContext(ctx context.Context) *xThirdQuweiDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdQuweiDo) ReadDB() *xThirdQuweiDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdQuweiDo) WriteDB() *xThirdQuweiDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdQuweiDo) Session(config *gorm.Session) *xThirdQuweiDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdQuweiDo) Clauses(conds ...clause.Expression) *xThirdQuweiDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdQuweiDo) Returning(value interface{}, columns ...string) *xThirdQuweiDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdQuweiDo) Not(conds ...gen.Condition) *xThirdQuweiDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdQuweiDo) Or(conds ...gen.Condition) *xThirdQuweiDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdQuweiDo) Select(conds ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdQuweiDo) Where(conds ...gen.Condition) *xThirdQuweiDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdQuweiDo) Order(conds ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdQuweiDo) Distinct(cols ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdQuweiDo) Omit(cols ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdQuweiDo) Join(table schema.Tabler, on ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdQuweiDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdQuweiDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdQuweiDo) Group(cols ...field.Expr) *xThirdQuweiDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdQuweiDo) Having(conds ...gen.Condition) *xThirdQuweiDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdQuweiDo) Limit(limit int) *xThirdQuweiDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdQuweiDo) Offset(offset int) *xThirdQuweiDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdQuweiDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdQuweiDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdQuweiDo) Unscoped() *xThirdQuweiDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdQuweiDo) Create(values ...*model.XThirdQuwei) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdQuweiDo) CreateInBatches(values []*model.XThirdQuwei, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdQuweiDo) Save(values ...*model.XThirdQuwei) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdQuweiDo) First() (*model.XThirdQuwei, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQuwei), nil
	}
}

func (x xThirdQuweiDo) Take() (*model.XThirdQuwei, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQuwei), nil
	}
}

func (x xThirdQuweiDo) Last() (*model.XThirdQuwei, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQuwei), nil
	}
}

func (x xThirdQuweiDo) Find() ([]*model.XThirdQuwei, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdQuwei), err
}

func (x xThirdQuweiDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdQuwei, err error) {
	buf := make([]*model.XThirdQuwei, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdQuweiDo) FindInBatches(result *[]*model.XThirdQuwei, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdQuweiDo) Attrs(attrs ...field.AssignExpr) *xThirdQuweiDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdQuweiDo) Assign(attrs ...field.AssignExpr) *xThirdQuweiDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdQuweiDo) Joins(fields ...field.RelationField) *xThirdQuweiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdQuweiDo) Preload(fields ...field.RelationField) *xThirdQuweiDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdQuweiDo) FirstOrInit() (*model.XThirdQuwei, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQuwei), nil
	}
}

func (x xThirdQuweiDo) FirstOrCreate() (*model.XThirdQuwei, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdQuwei), nil
	}
}

func (x xThirdQuweiDo) FindByPage(offset int, limit int) (result []*model.XThirdQuwei, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdQuweiDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdQuweiDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdQuweiDo) Delete(models ...*model.XThirdQuwei) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdQuweiDo) withDO(do gen.Dao) *xThirdQuweiDo {
	x.DO = *do.(*gen.DO)
	return x
}
