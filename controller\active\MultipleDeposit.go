package active

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	xHashModel "xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// MultipleDepositGift 处理用户复充活动
func MultipleDepositGift(userId int32, activeId int, level int, option string) (int, error) {
	// 用户表相关
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 不再使用用户每日统计表

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 用户钱包表相关
	userWalletTb := server.DaoxHashGame().XUserWallet
	userWalletDb := server.DaoxHashGame().XUserWallet.WithContext(context.Background())

	defer recover() // 捕获可能的panic异常
	// 获取当前时间
	now := carbon.Parse(carbon.Now().String()).StdTime()

	// 获取用户信息和渠道信息
	// 获取用户信息
	user, err := userDb.Where(userTb.UserID.Eq(int32(userId))).
		First()
	if err != nil {
		logs.Error("FirstDepositGift 获取用户信息失败: %v", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 第一步：获取活动定义，验证活动有效性
	activeDefine, err := GetActiveDefine(user.SellerID, user.ChannelID, int32(activeId))
	if err != nil {
		logs.Error("MultipleDepositGift getADefineInfo err", err)
		return utils.ActiveINotDefine, errors.New("活动不存在")
	}

	// 检查活动状态是否开启
	if activeDefine.State != utils.ActiveStateOpen {
		return utils.ActiveINotDefine, errors.New("活动已关闭")
	}

	// 校验活动时间范围
	if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
		startTime := time.UnixMilli(activeDefine.EffectStartTime)
		endTime := time.UnixMilli(activeDefine.EffectEndTime)
		if now.Before(startTime) || now.After(endTime) {
			return utils.ActiveINotDefine, errors.New("不在活动时间内")
		}
	}

	// 第二步：解析活动配置，检查用户当天领取次数限制
	//
	// 新逻辑：检查用户是否已申请过该活动，需要检查的是当天时间内规定领取的次数
	// 如果活动配置了每天可以领一次，就是一次，如果是两次就要判断两次
	// 这与传统的"一次性活动"不同，复充活动允许每天多次领取
	//
	// 解析活动基础配置
	var baseData DepositBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
	if err != nil {
		return utils.ActiveIParamsIllegal, errors.New("活动配置有误")
	}

	// 检查用户当天是否已达到领取次数限制
	if baseData.MaxDailyIDAttempts > 0 {
		dailyAttemptCount, err := CheckUserDailyActivityAttempts(
			user.UserID, user.SellerID, user.ChannelID, int32(activeId), baseData.MaxDailyIDAttempts)
		if err != nil {
			logs.Error("MultipleDepositGift CheckUserDailyActivityAttempts err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		// 记录检查日志
		LogDailyActivityAttempts(user.UserID, user.SellerID, user.ChannelID,
			int32(activeId), dailyAttemptCount, baseData.MaxDailyIDAttempts, "pre-check")

		if dailyAttemptCount >= baseData.MaxDailyIDAttempts {
			logs.Warn("MultipleDepositGift 当天领取次数已达上限: 用户ID=%d, 今日已领取=%d次, 每日限制=%d次",
				user.UserID, dailyAttemptCount, baseData.MaxDailyIDAttempts)
			return utils.ActiveINocondition, fmt.Errorf("您今日已达到最大领取次数(%d次)，请明天再来", baseData.MaxDailyIDAttempts)
		}

		logs.Info("MultipleDepositGift 当天领取次数检查通过: 用户ID=%d, 今日已领取=%d次, 每日限制=%d次, 剩余=%d次",
			user.UserID, dailyAttemptCount, baseData.MaxDailyIDAttempts, baseData.MaxDailyIDAttempts-dailyAttemptCount)
	} else {
		logs.Info("MultipleDepositGift 未配置每日领取次数限制，跳过当天领取次数检查")
	}

	// 第三步：解析活动配置
	// 解析活动档位配置
	var configData []DepositConfig
	err = json.Unmarshal([]byte(activeDefine.Config), &configData)
	if err != nil {
		return utils.ActiveIParamsIllegal, errors.New("活动配置有误")
	}

	// baseData 已在第二步中解析，这里不需要重复解析

	// 根据level参数选择对应的档位配置
	var selectData DepositConfig
	logs.Info("MultipleDepositGift 开始选择档位: 传入level=%d, 可用档位数量=%d", level, len(configData))

	// 打印所有可用档位配置
	for i, config := range configData {
		logs.Info("MultipleDepositGift 档位%d配置: ID=%d, 最低充值=%s, 赠送比例=%s%%, 赠送上限=%s",
			i+1, config.ID, config.FirstChargeUstdLimit.StringFixed(2),
			config.GiveProportion.StringFixed(2), config.GiveLimit.StringFixed(2))
	}

	for _, v := range configData {
		if v.ID == int32(level) {
			selectData = v
			logs.Info("MultipleDepositGift 找到匹配档位: ID=%d, 最低充值=%s, 赠送比例=%s%%, 赠送上限=%s",
				selectData.ID, selectData.FirstChargeUstdLimit.StringFixed(2),
				selectData.GiveProportion.StringFixed(2), selectData.GiveLimit.StringFixed(2))
		}
	}
	if selectData.ID == 0 {
		// 未找到对应档位
		logs.Error("MultipleDepositGift 未找到对应档位: 传入level=%d", level)
		return utils.ActiveIParamsIllegal, errors.New("参数错误")
	}

	// 检查是否为测试账号
	if user.IsTest == 1 {
		return utils.ActiveIIsTest, errors.New("该玩家禁止领取")
	}

	// 检查邮箱绑定要求
	if baseData.IsBindEmail && user.Email == "" {
		return utils.ActiveINocondition, errors.New("请先绑定邮箱")
	}

	// 检查钱包地址有效性
	if baseData.IsValidWallet {
		// 查询用户钱包地址

		// 查询用户是否有已验证的钱包地址
		userWallet, err := userWalletDb.
			Where(userWalletTb.UserID.Eq(user.UserID)).
			Where(userWalletTb.SellerID.Eq(user.SellerID)).
			Where(userWalletTb.ChannelID.Eq(user.ChannelID)).
			Where(userWalletTb.State.Eq(1)). // 状态 1已验证 2未验证
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("MultipleDepositGift query wallet address err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if userWallet == nil || userWallet.Address == "" {
			return utils.ActiveINocondition, errors.New("请先绑定有效的钱包地址")
		}
	}

	// 计算注册时间和有效时间范围
	registerTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).StartOfDay().String()).StdTime()
	endFindTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay)).StartOfDay().String()).StdTime()

	// 检查是否活动期间内注册的账号
	if baseData.IsDuringReg {
		// 检查用户注册时间是否在活动期间内
		activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
		activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
		if user.RegisterTime.Before(activityStartTime) || user.RegisterTime.After(activityEndTime) {
			return utils.ActiveINocondition, errors.New("仅限活动期间内注册的账号参与")
		}
	}

	// 计算活动领取截止时间
	receiveTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay + baseData.ReceiveDay)).StartOfDay().String()).StdTime()
	if now.After(receiveTime) {
		// 已超过领取时间
		return utils.ActiveINocondition, errors.New("已过了领取时间")
	}

	// 获取用户IP
	userIPInfo, err := userDb.Select(userTb.LoginIP).
		Where(userTb.UserID.Eq(user.UserID)).
		Where(userTb.SellerID.Eq(user.SellerID)).
		Where(userTb.ChannelID.Eq(user.ChannelID)).
		First()
	if err != nil {
		logs.Error("MultipleDepositGift query user IP err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}
	userIP := userIPInfo.LoginIP

	// 检查IP限制
	if userIP != "" {
		// 检查IP是否在黑名单中
		if baseData.BlockedIPList != "" {
			blockedIPs := strings.Split(baseData.BlockedIPList, ",")
			for _, blockedIP := range blockedIPs {
				if blockedIP == userIP {
					return utils.ActiveINocondition, errors.New("您的IP被限制参与此活动")
				}
			}
		}

		// 检查同IP当天最大领取次数
		if baseData.MaxIPAttempts > 0 {
			var ipAttemptCount int64
			// 获取当天的开始和结束时间
			todayStart := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
			todayEnd := carbon.Parse(carbon.Now().EndOfDay().String()).StdTime()

			// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
			ipAttemptCount, err = activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
				Where(activeRewardAuditTb.UserIP.Eq(userIP)).
				Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
				Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
				Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 限制为当天
				Count()
			if err != nil {
				logs.Error("MultipleDepositGift query IP daily attempt count err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}

			if int(ipAttemptCount) >= baseData.MaxIPAttempts {
				return utils.ActiveINocondition, fmt.Errorf("您的IP当天已达到最大领取次数(%d次)", baseData.MaxIPAttempts)
			}

			// 记录日志，方便调试
			logs.Info("MultipleDepositGift IP当天验证: IP=%s, 活动ID=%d, IP当天已领取次数=%d, 最大限制=%d次",
				userIP, activeId, ipAttemptCount, baseData.MaxIPAttempts)
		}
	}

	// 获取用户设备ID，用于后续保存到活动审核表
	userDeviceInfo, err := userDb.Select(userTb.LoginDeviceID).
		Where(userTb.UserID.Eq(user.UserID)).
		Where(userTb.SellerID.Eq(user.SellerID)).
		Where(userTb.ChannelID.Eq(user.ChannelID)).
		First()
	if err != nil {
		logs.Error("MultipleDepositGift query device ID err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}
	deviceID := userDeviceInfo.LoginDeviceID

	// 检查同设备号当天是否可参与
	if !baseData.IsDeviceLimit && deviceID != "" {
		var deviceAttemptCount int64
		// 获取当天的开始和结束时间
		todayStart := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
		todayEnd := carbon.Parse(carbon.Now().EndOfDay().String()).StdTime()

		// 通过DeviceID字段查询当天的参与记录
		// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
		deviceAttemptCount, err = activeRewardAuditDb.
			Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
			Where(activeRewardAuditTb.DeviceID.Eq(deviceID)).
			Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))). // 检查当前复充活动
			Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
			Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
			Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 限制为当天
			Count()
		if err != nil {
			logs.Error("MultipleDepositGift query device daily attempt count err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if deviceAttemptCount > 0 {
			return utils.ActiveINocondition, errors.New("同一设备当天只能领取一次")
		}

		// 记录日志，方便调试
		logs.Info("MultipleDepositGift 设备ID当天验证: 设备ID=%s, 活动ID=%d, 设备当天已领取次数=%d",
			deviceID, activeId, deviceAttemptCount)
	}

	// 检查同ID当天最大领取次数
	if baseData.MaxIDAttempts > 0 {
		var idAttemptCount int64
		// 获取当天的开始和结束时间
		todayStart := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
		todayEnd := carbon.Parse(carbon.Now().EndOfDay().String()).StdTime()

		// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
		idAttemptCount, err = activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
			Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
			Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
			Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
			Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
			Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 限制为当天
			Count()
		if err != nil {
			logs.Error("MultipleDepositGift query ID daily attempt count err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if int(idAttemptCount) >= baseData.MaxIDAttempts {
			return utils.ActiveINocondition, fmt.Errorf("您当天已达到最大领取次数(%d次)", baseData.MaxIDAttempts)
		}

		// 记录日志，方便调试
		logs.Info("MultipleDepositGift 用户ID当天验证: 用户ID=%d, 活动ID=%d, 用户当天已领取次数=%d, 最大限制=%d次",
			user.UserID, activeId, idAttemptCount, baseData.MaxIDAttempts)
	}

	// 每日领取次数检查已在第二步中完成，这里不需要重复检查
	logs.Info("MultipleDepositGift 每日领取次数检查已在第二步完成，继续后续验证")

	// 第五步：查询用户流水数据
	// 复充活动的流水计算从当天领取活动的时间开始计算
	// 这与首充活动（从注册时间开始）和其他活动（从活动开始时间）不同
	logs.Info("MultipleDepositGift 开始计算复充活动流水: 用户ID=%d, 计算规则=从当天00:00:00开始到当前时间", user.UserID)

	// 获取投注限制参数
	minBetAmount := baseData.MinBetAmount
	maxBetAmount := baseData.MaxBetAmount

	// 如果配置了最小或最大投注限制，则排除超出限制的流水
	isExcludeBetLimit := !minBetAmount.IsZero() || !maxBetAmount.IsZero()

	// 如果配置了投注限制，则在日志中记录
	if isExcludeBetLimit {
		logs.Info("MultipleDepositGift 配置了投注限制: minBetAmount=%v, maxBetAmount=%v, 活动方式=%v",
			minBetAmount, maxBetAmount, baseData.ActivityMethod)
	}

	// 无论是前置模式还是后置模式，都需要计算用户总流水
	// 前置模式：用于记录用户当前流水，方便后续审核
	// 后置模式：用于检查用户流水是否达到要求
	// 注意：复充活动的流水计算从当天开始，而不是从注册时间开始
	total, err := CalculateMultipleDepositActivityFlow(user, minBetAmount, maxBetAmount, isExcludeBetLimit)
	if err != nil {
		logs.Error("MultipleDepositGift CalculateMultipleDepositActivityFlow err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 第六步：根据活动类型获取充值记录
	var recharge *xHashModel.XRecharge

	// 判断是否为X9系列活动
	isX9DepositActivity := int32(activeId) == utils.X9SecondDepositGift || int32(activeId) == utils.X9ThirdDepositGift ||
		int32(activeId) == utils.X9FourthDepositGift || int32(activeId) == utils.X9FifthDepositGift

	if isX9DepositActivity {
		// X9系列活动：基于历史充值次数验证
		var requiredRechargeCount int32
		var activityName string

		switch int32(activeId) {
		case utils.X9SecondDepositGift:
			requiredRechargeCount = 2
			activityName = "二存活动"
		case utils.X9ThirdDepositGift:
			requiredRechargeCount = 3
			activityName = "三存活动"
		case utils.X9FourthDepositGift:
			requiredRechargeCount = 4
			activityName = "四存活动"
		case utils.X9FifthDepositGift:
			requiredRechargeCount = 5
			activityName = "五存活动"
		}

		logs.Info("MultipleDepositGift %s充值次数要求: 活动ID=%d, 要求充值次数=%d",
			activityName, activeId, requiredRechargeCount)

		// 查询用户所有充值记录来验证充值次数
		allRecharges, err := rechargeDb.
			Where(rechargeTb.UserID.Eq(int32(user.UserID))).
			Where(rechargeTb.SellerID.Eq(user.SellerID)).
			Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
			Where(rechargeTb.State.Eq(5)). // 状态为成功
			Order(rechargeTb.CreateTime.Asc()).
			Find()
		if err != nil {
			logs.Error("MultipleDepositGift query all recharges err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if len(allRecharges) < int(requiredRechargeCount) {
			return utils.ActiveINocondition, fmt.Errorf("请完成第%d次充值，当前充值次数：%d", requiredRechargeCount, len(allRecharges))
		}

		// 获取第N次充值记录作为领取凭证
		targetRecharge := allRecharges[requiredRechargeCount-1]
		recharge = targetRecharge

		// 验证第N次充值后是否有投注记录
		hasBetAfterRecharge, err := CheckUserMultipleDepositAfterRecharge(
			int(user.UserID), int(user.SellerID), int(user.ChannelID),
			targetRecharge.CreateTime, time.Now())
		if err != nil {
			logs.Error("MultipleDepositGift CheckUserMultipleDepositAfterRecharge err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if hasBetAfterRecharge {
			return utils.ActiveINocondition, errors.New("对不起,因您在申请活动前已经投注,已不符合复充活动要求")
		}

		logs.Info("MultipleDepositGift %s充值验证通过: 用户ID=%d, 第%d次充值时间=%v, 充值金额=%f, 充值后无投注",
			activityName, user.UserID, requiredRechargeCount, targetRecharge.CreateTime, targetRecharge.RealAmount)
	} else {
		// 普通复充活动：获取当天最近的一笔充值记录作为领取凭证
		var hasBetAfterRecharge bool
		var err error
		recharge, hasBetAfterRecharge, err = GetTodayLatestRechargeForActivity(
			user.UserID, user.SellerID, user.ChannelID)
		if err != nil {
			if err.Error() == "今日无复充记录" {
				return utils.ActiveINocondition, errors.New("请先完成今日复充")
			}
			logs.Error("MultipleDepositGift GetTodayLatestRechargeForActivity err", err)
			return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
		}

		if hasBetAfterRecharge {
			return utils.ActiveINocondition, errors.New("对不起,因您在申请活动前已经投注,已不符合复充活动要求")
		}

		logs.Info("MultipleDepositGift 当天最新充值验证通过: 用户ID=%d, 充值时间=%v, 充值金额=%f, 充值后无投注",
			user.UserID, recharge.CreateTime, recharge.RealAmount)
	}

	// 所有复充活动都需要检查充值时间是否在活动期间内
	if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
		activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
		activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
		if recharge.CreateTime.Before(activityStartTime) || recharge.CreateTime.After(activityEndTime) {
			var activityName string
			if isX9DepositActivity {
				switch int32(activeId) {
				case utils.X9SecondDepositGift:
					activityName = "X9第二次充值"
				case utils.X9ThirdDepositGift:
					activityName = "X9第三次充值"
				case utils.X9FourthDepositGift:
					activityName = "X9第四次充值"
				case utils.X9FifthDepositGift:
					activityName = "X9第五次充值"
				default:
					activityName = "X9复充"
				}
			} else {
				activityName = "普通复充"
			}
			logs.Info("%s活动充值时间检查: 活动ID=%d, 充值时间=%v, 活动开始时间=%v, 活动结束时间=%v",
				activityName, activeId, recharge.CreateTime, activityStartTime, activityEndTime)
			return utils.ActiveINocondition, errors.New("活动仅限活动期间内的充值参与")
		}
	}

	// X9系列活动的充值次数验证已在上面完成，这里跳过重复检查
	if !isX9DepositActivity {
		// 普通复充活动：如果配置了充值次数要求，还需要验证充值次数
		rechargeCount := baseData.RechargeCount
		if rechargeCount > 0 {
			// 查询用户所有充值记录来验证充值次数
			allRecharges, err := rechargeDb.
				Where(rechargeTb.UserID.Eq(int32(user.UserID))).
				Where(rechargeTb.SellerID.Eq(user.SellerID)).
				Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
				Where(rechargeTb.State.Eq(5)). // 状态为成功
				Where(rechargeTb.CreateTime.Between(registerTime, endFindTime)).
				Order(rechargeTb.CreateTime).
				Find()
			if err != nil {
				logs.Error("MultipleDepositGift query all recharges err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}

			if len(allRecharges) < int(rechargeCount) {
				return utils.ActiveINocondition, fmt.Errorf("请完成第%d次充值，当前充值次数：%d", rechargeCount, len(allRecharges))
			}

			logs.Info("MultipleDepositGift 充值次数验证通过: 用户ID=%d, 要求次数=%d, 实际次数=%d",
				user.UserID, rechargeCount, len(allRecharges))
		}
	} else {
		logs.Info("MultipleDepositGift X9系列活动充值次数验证已完成，跳过重复检查")
	}

	// 复充活动允许每天多次领取，不需要检查历史参与记录
	// 每日领取次数限制已在第二步中检查完成
	logs.Info("MultipleDepositGift 复充活动允许每天多次领取，跳过历史参与检查")

	// 第七步：验证充值金额是否满足要求
	isValidAmount, amountErrorMsg := ValidateRechargeForActivity(recharge, selectData, "MultipleDepositGift")
	if !isValidAmount {
		return utils.ActiveINocondition, errors.New(amountErrorMsg)
	}

	rechargetDecimal := decimal.NewFromFloat(recharge.RealAmount)

	// 根据活动方式处理
	// 如果 ActivityMethod 不是 1（前置模式），则默认使用 2（后置模式）
	// 定义一个变量用于存储总流水要求，以便后续保存到MinLiuShui字段
	var totalRequiredLiushui decimal.Decimal

	if baseData.ActivityMethod == 1 {
		logs.Info("MultipleDepositGift 前置模式，不检查流水，但会记录当前流水，用于后续提现检查")

		// 计算流水要求，用于后续提现检查
		var requiredLiushui decimal.Decimal
		switch baseData.BetType {
		case 1: // 真金流水
			// 使用真金流水倍数计算
			requiredLiushui = rechargetDecimal.Mul(selectData.LiushuiMultiple)
		case 2: // 彩金流水
			// 计算赠送金额（彩金）- 优先使用GiveAmount，如果为0则使用比例计算
			var finalGiveAmount decimal.Decimal
			if selectData.GiveAmount > 0 {
				// 使用固定金额
				finalGiveAmount = decimal.NewFromFloat32(selectData.GiveAmount)
				logs.Info("MultipleDepositGift 前置模式使用固定赠送金额: %.2f", selectData.GiveAmount)
			} else {
				// 使用比例计算
				giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
				giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
				giveLimit := selectData.GiveLimit                                          // 奖励上限

				// 如果计算的奖励超过上限，则使用上限值
				finalGiveAmount = giveAmount
				if giveAmount.GreaterThan(giveLimit) {
					finalGiveAmount = giveLimit
				}
				logs.Info("MultipleDepositGift 前置模式使用比例计算: 充值金额=%v, 赠送比例=%v, 计算金额=%v, 上限=%v, 最终金额=%v",
					rechargetDecimal, selectData.GiveProportion, giveAmount, giveLimit, finalGiveAmount)
			}

			// 彩金流水 = 赠送金额 * 彩金流水倍数
			requiredLiushui = finalGiveAmount.Mul(selectData.BonusMultiple)
		case 3: // 彩金+真金流水
			// 计算赠送金额 - 优先使用GiveAmount，如果为0则使用比例计算
			var finalGiveAmount decimal.Decimal
			if selectData.GiveAmount > 0 {
				// 使用固定金额
				finalGiveAmount = decimal.NewFromFloat32(selectData.GiveAmount)
				logs.Info("MultipleDepositGift 前置模式使用固定赠送金额: %.2f", selectData.GiveAmount)
			} else {
				// 使用比例计算
				giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
				giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
				giveLimit := selectData.GiveLimit                                          // 奖励上限

				// 如果计算的奖励超过上限，则使用上限值
				finalGiveAmount = giveAmount
				if giveAmount.GreaterThan(giveLimit) {
					finalGiveAmount = giveLimit
				}
				logs.Info("MultipleDepositGift 前置模式使用比例计算: 充值金额=%v, 赠送比例=%v, 计算金额=%v, 上限=%v, 最终金额=%v",
					rechargetDecimal, selectData.GiveProportion, giveAmount, giveLimit, finalGiveAmount)
			}

			// 真金流水 = 充值金额 * 真金流水倍数
			realMoneyFlow := rechargetDecimal.Mul(selectData.LiushuiMultiple)
			// 彩金流水 = 赠送金额 * 彩金流水倍数
			bonusFlow := finalGiveAmount.Mul(selectData.BonusMultiple)
			// 总流水要求 = 真金流水 + 彩金流水
			requiredLiushui = realMoneyFlow.Add(bonusFlow)

			logs.Info("MultipleDepositGift 前置模式彩金+真金流水计算: 充值金额=%v, 真金流水倍数=%v, 真金流水=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v, 总流水要求=%v",
				rechargetDecimal, selectData.LiushuiMultiple, realMoneyFlow, finalGiveAmount, selectData.BonusMultiple, bonusFlow, requiredLiushui)
		}

		// 记录流水要求，方便调试
		logs.Info("MultipleDepositGift 前置模式流水要求: %s, 当前流水: %s", requiredLiushui.StringFixed(2), total.StringFixed(2))

		// 保存总流水要求
		totalRequiredLiushui = requiredLiushui
	} else {
		// 根据打码条件计算流水要求，先打流水再领奖励，检查流水是否已经达到要求
		var requiredLiushui decimal.Decimal
		switch baseData.BetType {
		case 1: // 真金流水
			// 使用真金流水倍数计算
			requiredLiushui = rechargetDecimal.Mul(selectData.LiushuiMultiple)
		case 2: // 彩金流水
			// 计算赠送金额（彩金）- 优先使用GiveAmount，如果为0则使用比例计算
			var finalGiveAmount decimal.Decimal
			if selectData.GiveAmount > 0 {
				// 使用固定金额
				finalGiveAmount = decimal.NewFromFloat32(selectData.GiveAmount)
				logs.Info("MultipleDepositGift 后置模式使用固定赠送金额: %.2f", selectData.GiveAmount)
			} else {
				// 使用比例计算
				giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
				giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
				giveLimit := selectData.GiveLimit                                          // 奖励上限

				// 如果计算的奖励超过上限，则使用上限值
				finalGiveAmount = giveAmount
				if giveAmount.GreaterThan(giveLimit) {
					finalGiveAmount = giveLimit
				}
				logs.Info("MultipleDepositGift 后置模式使用比例计算: 充值金额=%v, 赠送比例=%v, 计算金额=%v, 上限=%v, 最终金额=%v",
					rechargetDecimal, selectData.GiveProportion, giveAmount, giveLimit, finalGiveAmount)
			}

			// 彩金流水 = 赠送金额 * 彩金流水倍数
			requiredLiushui = finalGiveAmount.Mul(selectData.BonusMultiple)
		case 3: // 彩金+真金流水
			// 计算赠送金额 - 优先使用GiveAmount，如果为0则使用比例计算
			var finalGiveAmount decimal.Decimal
			if selectData.GiveAmount > 0 {
				// 使用固定金额
				finalGiveAmount = decimal.NewFromFloat32(selectData.GiveAmount)
				logs.Info("MultipleDepositGift 后置模式使用固定赠送金额: %.2f", selectData.GiveAmount)
			} else {
				// 使用比例计算
				giveProportion := selectData.GiveProportion.Div(decimal.NewFromInt32(100)) // 转换为小数比例
				giveAmount := rechargetDecimal.Mul(giveProportion)                         // 按比例计算奖励
				giveLimit := selectData.GiveLimit                                          // 奖励上限

				// 如果计算的奖励超过上限，则使用上限值
				finalGiveAmount = giveAmount
				if giveAmount.GreaterThan(giveLimit) {
					finalGiveAmount = giveLimit
				}
				logs.Info("MultipleDepositGift 后置模式使用比例计算: 充值金额=%v, 赠送比例=%v, 计算金额=%v, 上限=%v, 最终金额=%v",
					rechargetDecimal, selectData.GiveProportion, giveAmount, giveLimit, finalGiveAmount)
			}

			// 真金流水 = 充值金额 * 真金流水倍数
			realMoneyFlow := rechargetDecimal.Mul(selectData.LiushuiMultiple)
			// 彩金流水 = 赠送金额 * 彩金流水倍数
			bonusFlow := finalGiveAmount.Mul(selectData.BonusMultiple)
			// 总流水要求 = 真金流水 + 彩金流水
			requiredLiushui = realMoneyFlow.Add(bonusFlow)

			logs.Info("MultipleDepositGift 后置模式彩金+真金流水计算: 充值金额=%v, 真金流水倍数=%v, 真金流水=%v, 赠送金额=%v, 彩金流水倍数=%v, 彩金流水=%v, 总流水要求=%v",
				rechargetDecimal, selectData.LiushuiMultiple, realMoneyFlow, finalGiveAmount, selectData.BonusMultiple, bonusFlow, requiredLiushui)
		}

		// 保存总流水要求
		totalRequiredLiushui = requiredLiushui

		// 检查流水是否达到要求
		totalEqual := total.GreaterThanOrEqual(requiredLiushui)
		if !totalEqual {
			// 计算差额并保留小数点后2位
			diff := requiredLiushui.Sub(total)
			// 使用Round(2)进行四舍五入，然后使用StringFixed(2)格式化为2位小数
			roundedDiff := diff.Round(2)
			logs.Info("MultipleDepositGift 流水不足计算: 需要流水=%s, 当前流水=%s, 差额=%s, 四舍五入后=%s",
				requiredLiushui.StringFixed(2), total.StringFixed(2), diff.StringFixed(2), roundedDiff.StringFixed(2))
			return utils.ActiveINocondition, errors.New("流水不足，还需要" + roundedDiff.StringFixed(2))
		}
	}

	// 第八步：计算奖励金额 - 优先使用GiveAmount，如果为0则使用比例计算
	var realAmount float64
	var withdrawLiuSuiAdd float64
	var rewardDecimal decimal.Decimal

	if selectData.GiveAmount > 0 {
		// 使用固定金额
		rewardDecimal = decimal.NewFromFloat32(selectData.GiveAmount)
		logs.Info("MultipleDepositGift 最终奖励使用固定赠送金额: %.2f", selectData.GiveAmount)
	} else {
		// 使用比例计算
		giveProportion := selectData.GiveProportion
		percentage := decimal.NewFromInt32(100)
		giveProportion = giveProportion.Div(percentage)       // 转换为小数比例
		giveLimit := selectData.GiveLimit                     // 奖励上限
		amountDecimal := rechargetDecimal.Mul(giveProportion) // 按比例计算奖励
		equal := amountDecimal.GreaterThanOrEqual(giveLimit)

		// 初始计算奖励金额
		if equal {
			rewardDecimal = giveLimit
		} else {
			rewardDecimal = amountDecimal
		}
		logs.Info("MultipleDepositGift 最终奖励使用比例计算: 充值金额=%v, 赠送比例=%v, 计算金额=%v, 上限=%v, 最终金额=%v",
			rechargetDecimal, selectData.GiveProportion, amountDecimal, giveLimit, rewardDecimal)
	}

	// 检查总派发和每日派发限制
	if baseData.TotalRewardLimit.GreaterThan(decimal.Zero) || baseData.DailyRewardLimit.GreaterThan(decimal.Zero) {
		// 定义变量，用于存储每日和总派发的检查结果
		var dailyCheckPassed, totalCheckPassed bool

		// 默认设置为通过，如果不需要检查则直接通过
		dailyCheckPassed = true
		totalCheckPassed = true

		// 1. 先检查当日领取条件
		if baseData.DailyRewardLimit.GreaterThan(decimal.Zero) {
			today := time.Now().Format("2006-01-02")
			type DailyRewardResult struct {
				DailyAmount float64
			}
			var dailyRewardResult DailyRewardResult
			// 使用Gen的方式查询，通过自定义条件表达式处理DATE函数
			todayStart, _ := time.Parse("2006-01-02", today)
			todayEnd := todayStart.Add(24 * time.Hour)

			// 首先查询所有状态的记录，了解数据库中的实际情况
			type AllRecordsResultSQL struct {
				TotalAmount sql.NullFloat64
				Count       sql.NullInt64
			}
			var allRecordsResultSQL AllRecordsResultSQL
			err = activeRewardAuditDb.Select(
				activeRewardAuditTb.Amount.Sum().As("TotalAmount"),
				activeRewardAuditTb.ID.Count().As("Count"),
			).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
				Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
				Scan(&allRecordsResultSQL)

			if err != nil {
				logs.Error("MultipleDepositGift query all records err", err)
			} else {
				var totalAmount float64
				var count int64
				if allRecordsResultSQL.TotalAmount.Valid {
					totalAmount = allRecordsResultSQL.TotalAmount.Float64
				}
				if allRecordsResultSQL.Count.Valid {
					count = allRecordsResultSQL.Count.Int64
				}
				logs.Info("MultipleDepositGift 所有记录查询结果: 活动ID=%v, 总金额=%v, 记录数=%v",
					activeId, totalAmount, count)
			}

			// 查询各种状态的记录数量
			type StateCountResult struct {
				AuditState  int32
				Count       int64
				TotalAmount float64
			}
			var stateCountResults []StateCountResult
			err = activeRewardAuditDb.Select(
				activeRewardAuditTb.AuditState,
				activeRewardAuditTb.ID.Count().As("Count"),
				activeRewardAuditTb.Amount.Sum().As("TotalAmount"),
			).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
				Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
				Group(activeRewardAuditTb.AuditState).
				Scan(&stateCountResults)

			if err != nil {
				logs.Error("MultipleDepositGift query state count err", err)
			} else {
				for _, result := range stateCountResults {
					logs.Info("MultipleDepositGift 状态统计: 活动ID=%v, 状态=%v, 记录数=%v, 总金额=%v",
						activeId, result.AuditState, result.Count, result.TotalAmount)
				}
			}

			// 查询今日已审核通过的记录，使用AuditTime字段
			type SumResult struct {
				DailyAmount sql.NullFloat64
			}
			var auditTimeSumResult SumResult
			err = activeRewardAuditDb.Select(activeRewardAuditTb.Amount.Sum().As("DailyAmount")).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
				Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
				Where(activeRewardAuditTb.AuditState.In(3, 4)).
				Where(activeRewardAuditTb.AuditTime.Between(todayStart, todayEnd)).
				Scan(&auditTimeSumResult)

			// 记录更详细的日志，方便调试
			logs.Info("MultipleDepositGift 每日派发限制查询: 活动ID=%v, 日期范围=%v至%v",
				activeId, todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

			if err != nil {
				logs.Error("MultipleDepositGift query daily reward amount err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}

			// 处理NULL值
			if auditTimeSumResult.DailyAmount.Valid {
				dailyRewardResult.DailyAmount = auditTimeSumResult.DailyAmount.Float64
			} else {
				dailyRewardResult.DailyAmount = 0
			}

			logs.Info("MultipleDepositGift 今日已审核通过记录: 活动ID=%v, 总金额=%v",
				activeId, dailyRewardResult.DailyAmount)

			dailyRewardAmount := decimal.NewFromFloat(dailyRewardResult.DailyAmount)

			// 计算每日可领取奖励的额度
			remainingDailyAmount := baseData.DailyRewardLimit.Sub(dailyRewardAmount)

			// 记录日志，方便调试
			logs.Info("MultipleDepositGift 每日派发限制检查: 今日已派发=%s, 本次预计派发=%s, 每日限制=%s, 剩余可派发=%s",
				dailyRewardAmount.StringFixed(2), rewardDecimal.StringFixed(2),
				baseData.DailyRewardLimit.StringFixed(2), remainingDailyAmount.StringFixed(2))

			// 检查原始奖励是否超过每日限制
			if rewardDecimal.GreaterThan(baseData.DailyRewardLimit) {
				// 如果原始奖励超过每日限制，不发放奖励
				logs.Warn("MultipleDepositGift 原始奖励超过每日限制: 应奖励=%s, 每日限制=%s",
					rewardDecimal.StringFixed(2), baseData.DailyRewardLimit.StringFixed(2))
				dailyCheckPassed = false
			} else if rewardDecimal.GreaterThan(remainingDailyAmount) {
				// 如果奖励金额大于剩余可派发金额，则不发放奖励
				logs.Warn("MultipleDepositGift 今日活动奖金额度不足: 应奖励=%s, 剩余可派发=%s, 每日限制=%s",
					rewardDecimal.StringFixed(2), remainingDailyAmount.StringFixed(2), baseData.DailyRewardLimit.StringFixed(2))
				dailyCheckPassed = false
			}
		}

		// 2. 再检查总领取条件
		if baseData.TotalRewardLimit.GreaterThan(decimal.Zero) {
			type TotalRewardResult struct {
				TotalAmount float64
			}
			var totalRewardResult TotalRewardResult
			// 只包含审核通过和自动通过的记录
			err = activeRewardAuditDb.Select(activeRewardAuditTb.Amount.Sum().As("TotalAmount")).
				Where(activeRewardAuditTb.ActiveID.Eq(int32(activeId))).
				Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
				Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
				Where(activeRewardAuditTb.AuditState.In(3, 4)). // 只包含审核通过和自动通过的记录
				Scan(&totalRewardResult)
			if err != nil {
				logs.Error("MultipleDepositGift query total reward amount err", err)
				return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
			}
			totalRewardAmount := decimal.NewFromFloat(totalRewardResult.TotalAmount)

			// 计算总可领取奖励的额度
			remainingTotalAmount := baseData.TotalRewardLimit.Sub(totalRewardAmount)

			// 记录日志，方便调试
			logs.Info("MultipleDepositGift 总派发限制检查: 总礼金已派发=%s, 本次预计派发=%s, 总限制=%s, 剩余可派发=%s",
				totalRewardAmount.StringFixed(2), rewardDecimal.StringFixed(2),
				baseData.TotalRewardLimit.StringFixed(2), remainingTotalAmount.StringFixed(2))

			// 检查原始奖励是否超过总限制
			if rewardDecimal.GreaterThan(baseData.TotalRewardLimit) {
				// 如果原始奖励超过总限制，不发放奖励
				logs.Warn("MultipleDepositGift 原始奖励超过总限制: 应奖励=%s, 总限制=%s",
					rewardDecimal.StringFixed(2), baseData.TotalRewardLimit.StringFixed(2))
				totalCheckPassed = false
			} else if totalRewardAmount.Add(rewardDecimal).GreaterThan(baseData.TotalRewardLimit) {
				// 计算剩余可派发金额
				remainingAmount := baseData.TotalRewardLimit.Sub(totalRewardAmount)

				// 如果剩余金额小于原始奖励，则不发放奖励
				if remainingAmount.LessThan(rewardDecimal) {
					logs.Warn("MultipleDepositGift 活动奖金额度不足: 应奖励=%s, 剩余可派发=%s",
						rewardDecimal.StringFixed(2), remainingAmount.StringFixed(2))
					totalCheckPassed = false
				}
			}
		}

		// 3. 只有两个条件都满足才能发放奖励
		if !dailyCheckPassed {
			return utils.ActiveINocondition, errors.New("今日礼金额度不足")
		}

		if !totalCheckPassed {
			return utils.ActiveINocondition, errors.New("礼金额度已满")
		}
	}

	// 使用调整后的奖励金额和计算出的总流水要求
	// 使用计算出的总流水要求，而不是activeDefine.MinLiuShui
	withdrawLiuSuiAdd, _ = totalRequiredLiushui.Float64()
	realAmount, _ = rewardDecimal.Float64()

	// 第九步：准备保存活动数据
	configStr, _ := json.Marshal(configData)

	// 确保baseData中包含IsCalcActiveWager和RewardWalletType信息
	// 这样SaveActiveData函数可以通过解析baseData来获取这些信息
	baseConfigStr, _ := json.Marshal(baseData)
	totalLiushui, _ := total.Float64()
	firstRecharge, _ := rechargetDecimal.Float64()

	// 根据RewardWalletType设置奖励账户类型
	balanceCReason := utils.MultipleDepositGift
	if baseData.RewardWalletType == 1 {
		// 如果是彩金账户，使用彩金相关的BalanceCReason
		balanceCReason = utils.MultipleDepositGift // 这里可以替换为彩金账户对应的BalanceCReason
	}
	remark := ""

	// 为X9系列活动生成特殊的remark
	if isX9DepositActivity {
		var depositNumber string
		switch int32(activeId) {
		case utils.X9SecondDepositGift:
			depositNumber = "二"
		case utils.X9ThirdDepositGift:
			depositNumber = "三"
		case utils.X9FourthDepositGift:
			depositNumber = "四"
		case utils.X9FifthDepositGift:
			depositNumber = "五"
		}

		if option == "auto" {
			remark = fmt.Sprintf("X9复充第%s次(%s)", depositNumber, "自动")
		} else if option == "manual" {
			remark = fmt.Sprintf("X9复充第%s次(%s)", depositNumber, "手动")
		}
	} else {
		// 普通复充活动的remark
		if option == "auto" {
			remark = fmt.Sprintf("复充(%s)", "自动")
		} else if option == "manual" {
			remark = fmt.Sprintf("复充(%s)", "手动")
		} else {
			remark = "复充"
		}
	}
	// 构建活动数据保存信息
	saveActiveDataInfo := SaveActiveDataInfo{
		AuditType:         int(activeDefine.AuditType), // 审核类型
		SellerID:          user.SellerID,               // 运营商ID
		ChannelID:         user.ChannelID,              // 渠道ID
		ActiveId:          activeDefine.ActiveId,       // 活动ID
		Level:             level,                       // 活动等级
		RealAmount:        realAmount,                  // 实际奖励金额
		TotalLiushui:      totalLiushui,                // 总流水
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,           // 提现流水增加值
		ActiveName:        activeDefine.Title,          // 活动名称
		ActiveMemo:        remark,                      // 活动备注
		BalanceCReason:    balanceCReason,              // 余额变更原因
		ConfigStr:         configStr,                   // 配置字符串
		BastConfigStr:     baseConfigStr,               // 基础配置字符串
		FirstRecharge:     firstRecharge,               // 首充金额
		TotalRecharge:     0.0,                         // 总充值金额
		MinLiuShui:        totalRequiredLiushui,        // 总流水要求
		UserIP:            userIP,                      // 用户IP
		DeviceID:          deviceID,                    // 设备ID
	}

	// 第十步：保存活动数据并发放奖励
	err = SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("MultipleDepositGift saveActiveData err", err)
		return utils.ActiveIInternal, errors.New("系统错误,请稍后再试")
	}

	// 根据审核类型返回不同的提示信息
	if saveActiveDataInfo.AuditType == utils.ActiveAuditRenGong {
		logs.Info("MultipleDepositGift 人工审核模式: 用户ID=%d, 活动ID=%d, 申请已提交等待审核", user.UserID, activeId)
		// 第十一步：向 x_user_reduce 表写入数据 计算需要扣减的流水
	} else {
		logs.Info("MultipleDepositGift 自动审核模式: 用户ID=%d, 活动ID=%d, 奖励已自动发放", user.UserID, activeId)
		// 第十一步：向 x_user_reduce 表写入数据 计算需要扣减的流水
	}
	reduceLiuShui := totalRequiredLiushui
	currentTime := time.Now()

	if baseData.IsCalcActiveWager {
		userReduceTb := server.DaoxHashGame().XUserReduce
		userReduceDb := server.DaoxHashGame().XUserReduce.WithContext(context.Background())

		// 查询用户是否已有记录
		userReduce, err := userReduceDb.Where(userReduceTb.UserID.Eq(user.UserID)).First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("MultipleDepositGift query x_user_reduce err", err)
			// 不影响主流程，继续返回成功
		} else {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 用户不存在记录，创建新记录
				// 将 decimal.Decimal 类型转换为 float64 类型
				reduceLiuShuiFloat, _ := reduceLiuShui.Float64()
				newUserReduce := xHashModel.XUserReduce{
					UserID:            user.UserID,
					ReduceLiuShui:     reduceLiuShuiFloat,
					RealReduceLiuShui: 0, // 使用 0 代替 decimal.Zero
					CreateTime:        currentTime,
					UpdateTime:        currentTime,
				}

				err = userReduceDb.Create(&newUserReduce)
				if err != nil {
					logs.Error("MultipleDepositGift create x_user_reduce err", err)
					// 不影响主流程，继续返回成功
				}
			} else {
				// 用户已有记录，更新记录
				// 将 decimal.Decimal 类型转换为 float64 类型
				reduceLiuShuiFloat, _ := reduceLiuShui.Float64()
				// 直接相加 float64 类型的值
				updatedReduceLiuShui := userReduce.ReduceLiuShui + reduceLiuShuiFloat

				_, err = userReduceDb.Where(userReduceTb.UserID.Eq(user.UserID)).
					Updates(map[string]interface{}{
						"ReduceLiuShui": updatedReduceLiuShui,
						"UpdateTime":    currentTime,
					})

				if err != nil {
					logs.Error("MultipleDepositGift update x_user_reduce err", err)
					// 不影响主流程，继续返回成功
				}
			}
		}
	}

	// 记录成功领取的日志
	if baseData.MaxDailyIDAttempts > 0 {
		// 重新查询当前已领取次数（因为刚刚新增了一条记录）
		currentAttempts, err := CheckUserDailyActivityAttempts(
			user.UserID, user.SellerID, user.ChannelID, int32(activeId), baseData.MaxDailyIDAttempts)
		if err == nil {
			LogDailyActivityAttempts(user.UserID, user.SellerID, user.ChannelID,
				int32(activeId), currentAttempts, baseData.MaxDailyIDAttempts, "claim")
		}
	}

	// 计算实际奖励金额
	rechargeDecimal := decimal.NewFromFloat(recharge.RealAmount)
	actualReward := rechargeDecimal.Mul(selectData.GiveProportion).Div(decimal.NewFromInt(100))
	if actualReward.GreaterThan(selectData.GiveLimit) {
		actualReward = selectData.GiveLimit
	}

	// 处理成功，根据审核类型返回不同的成功提示
	if saveActiveDataInfo.AuditType == utils.ActiveAuditRenGong {
		logs.Info("MultipleDepositGift 申请提交成功(人工审核): 用户ID=%d, 活动ID=%d, 充值金额=%s, 奖励比例=%s%%, 奖励金额=%s",
			user.UserID, activeId, rechargeDecimal.StringFixed(2), selectData.GiveProportion.StringFixed(2), actualReward.StringFixed(2))
		return utils.ActiveISuccess, errors.New("申请已提交，请等待审核")
	} else {
		logs.Info("MultipleDepositGift 申请提交成功(自动审核): 用户ID=%d, 活动ID=%d, 充值金额=%s, 奖励比例=%s%%, 奖励金额=%s",
			user.UserID, activeId, rechargeDecimal.StringFixed(2), selectData.GiveProportion.StringFixed(2), actualReward.StringFixed(2))
		return utils.ActiveISuccess, nil
	}
}

// CheckUserMultipleDepositAfterRecharge 检查用户在复充成功后是否有投注记录
func CheckUserMultipleDepositAfterRecharge(userId, sellerId, channelId int, rechargeTime, endTime time.Time) (bool, error) {
	// 1. 检查哈希游戏投注记录 (x_order表)
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())

	orderCount, err := orderDb.Where(orderTb.UserID.Eq(int32(userId))).
		Where(orderTb.SellerID.Eq(int32(sellerId))).
		Where(orderTb.ChannelID.Eq(int32(channelId))).
		Where(orderTb.CreateTime.Gt(rechargeTime)).
		Where(orderTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_order err", err)
		return false, err
	}

	if orderCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现哈希游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, orderCount, rechargeTime)
		return true, nil
	}

	// 2. 检查电子游戏投注记录 (x_third_dianzhi表)
	dianzhiTb := server.DaoxHashGame().XThirdDianzhi
	dianzhiDb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())

	dianzhiCount, err := dianzhiDb.Where(dianzhiTb.UserID.Eq(int32(userId))).
		Where(dianzhiTb.SellerID.Eq(int32(sellerId))).
		Where(dianzhiTb.ChannelID.Eq(int32(channelId))).
		Where(dianzhiTb.CreateTime.Gt(rechargeTime)).
		Where(dianzhiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_dianzhi err", err)
		return false, err
	}

	if dianzhiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现电子游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, dianzhiCount, rechargeTime)
		return true, nil
	}

	// 3. 检查真人游戏投注记录 (x_third_live表)
	liveTb := server.DaoxHashGame().XThirdLive
	liveDb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())

	liveCount, err := liveDb.Where(liveTb.UserID.Eq(int32(userId))).
		Where(liveTb.SellerID.Eq(int32(sellerId))).
		Where(liveTb.ChannelID.Eq(int32(channelId))).
		Where(liveTb.CreateTime.Gt(rechargeTime)).
		Where(liveTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_live err", err)
		return false, err
	}

	if liveCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现真人游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, liveCount, rechargeTime)
		return true, nil
	}

	// 4. 检查彩票游戏投注记录 (x_third_lottery表)
	lotteryTb := server.DaoxHashGame().XThirdLottery
	lotteryDb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())

	lotteryCount, err := lotteryDb.Where(lotteryTb.UserID.Eq(int32(userId))).
		Where(lotteryTb.SellerID.Eq(int32(sellerId))).
		Where(lotteryTb.ChannelID.Eq(int32(channelId))).
		Where(lotteryTb.CreateTime.Gt(rechargeTime)).
		Where(lotteryTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_lottery err", err)
		return false, err
	}

	if lotteryCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现彩票游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, lotteryCount, rechargeTime)
		return true, nil
	}

	// 5. 检查棋牌游戏投注记录 (x_third_qipai表)
	qipaiTb := server.DaoxHashGame().XThirdQipai
	qipaiDb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())

	qipaiCount, err := qipaiDb.Where(qipaiTb.UserID.Eq(int32(userId))).
		Where(qipaiTb.SellerID.Eq(int32(sellerId))).
		Where(qipaiTb.ChannelID.Eq(int32(channelId))).
		Where(qipaiTb.CreateTime.Gt(rechargeTime)).
		Where(qipaiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_qipai err", err)
		return false, err
	}

	if qipaiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现棋牌游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, qipaiCount, rechargeTime)
		return true, nil
	}

	// 6. 检查趣味游戏投注记录 (x_third_quwei表)
	quweiTb := server.DaoxHashGame().XThirdQuwei
	quweiDb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())

	quweiCount, err := quweiDb.Where(quweiTb.UserID.Eq(int32(userId))).
		Where(quweiTb.SellerID.Eq(int32(sellerId))).
		Where(quweiTb.ChannelID.Eq(int32(channelId))).
		Where(quweiTb.CreateTime.Gt(rechargeTime)).
		Where(quweiTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_quwei err", err)
		return false, err
	}

	if quweiCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现趣味游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, quweiCount, rechargeTime)
		return true, nil
	}

	// 7. 检查体育游戏投注记录 (x_third_sport表)
	sportTb := server.DaoxHashGame().XThirdSport
	sportDb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())

	sportCount, err := sportDb.Where(sportTb.UserID.Eq(int32(userId))).
		Where(sportTb.SellerID.Eq(int32(sellerId))).
		Where(sportTb.ChannelID.Eq(int32(channelId))).
		Where(sportTb.CreateTime.Gt(rechargeTime)).
		Where(sportTb.CreateTime.Lte(endTime)).
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositAfterRecharge query x_third_sport err", err)
		return false, err
	}

	if sportCount > 0 {
		logs.Info("CheckUserMultipleDepositAfterRecharge 发现体育游戏投注记录: 用户ID=%d, 投注次数=%d, 充值时间=%v",
			userId, sportCount, rechargeTime)
		return true, nil
	}

	// 如果所有游戏类型都没有投注记录,返回false
	logs.Info("CheckUserMultipleDepositAfterRecharge 未发现任何投注记录: 用户ID=%d, 充值时间=%v", userId, rechargeTime)
	return false, nil
}

// CheckUserMultipleDepositCount 检查用户是否完成复充活动要求的充值次数
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//
// 返回:
//   - bool: 是否完成指定次数充值
//   - int: 实际充值次数
//   - error: 错误信息
func CheckUserMultipleDepositCount(sellerId, channelId, activeId, userId, rechargeCount int32) (bool, int, error) {
	// 记录详细的参数信息，用于调试
	logs.Info("CheckUserMultipleDepositCount 函数调用参数: sellerId=%d, channelId=%d, activeId=%d, userId=%d, rechargeCount=%d",
		sellerId, channelId, activeId, userId, rechargeCount)

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 查询用户充值次数
	count, err := rechargeDb.Where(rechargeTb.UserID.Eq(userId)).
		Where(rechargeTb.SellerID.Eq(sellerId)).
		Where(rechargeTb.ChannelID.Eq(channelId)).
		Where(rechargeTb.State.Eq(5)). // 状态为成功
		Count()

	if err != nil {
		logs.Error("CheckUserMultipleDepositCount query error:", err)
		return false, 0, err
	}

	actualCount := int(count)
	hasEnoughDeposits := actualCount >= int(rechargeCount)

	logs.Info("CheckUserMultipleDepositCount 充值次数检查: 用户ID=%d, 活动ID=%d, 要求次数=%d, 实际次数=%d, 满足要求=%v",
		userId, activeId, rechargeCount, actualCount, hasEnoughDeposits)

	return hasEnoughDeposits, actualCount, nil
}

// CheckUserMultipleDepositActive 检查用户当天是否参与过复充活动
//
// 此函数检查用户在当天是否已经参加过复充活动
//
// 参数:
//   - userId: 用户ID
//
// 返回:
//   - bool: 当天是否已参加过复充活动
func CheckUserMultipleDepositActive(userId int32) bool {
	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 获取当天的开始和结束时间
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)
	todayEnd := todayStart.Add(24 * time.Hour)

	// 查询用户当天是否有复充活动记录
	count, err := activeRewardAuditDb.Where(activeRewardAuditTb.UserID.Eq(userId)).
		Where(activeRewardAuditTb.ActiveID.In(utils.MultipleDepositGift)).   // 复充活动ID列表
		Where(activeRewardAuditTb.AuditState.Eq(4)).                         // 检查自动通过状态的记录
		Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 当天时间范围
		Count()

	if err != nil {
		// 查询出错，记录日志并返回 false
		logs.Error("CheckUserMultipleDepositActive query error:", err)
		return false
	}

	logs.Info("CheckUserMultipleDepositActive 当天记录查询: 用户ID=%d, 当天复充活动记录数=%d", userId, count)

	// 返回当天是否有复充活动参与记录
	return count > 0
}

// GetTodayLatestRechargeForActivity 获取当天最近的一笔充值记录作为活动领取凭证
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//
// 返回:
//   - *xHashModel.XRecharge: 充值记录
//   - bool: 是否在充值后有投注记录
//   - error: 错误信息
func GetTodayLatestRechargeForActivity(userId, sellerId, channelId int32) (*xHashModel.XRecharge, bool, error) {
	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 获取当天的时间范围
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	logs.Info("GetTodayLatestRechargeForActivity 时间计算: 当前时间=%v, 时区=%s, 今日开始=%v, 今日结束=%v",
		now, now.Location().String(), todayStart, todayEnd)

	// 查询当天最近的一笔非首充记录（复充活动只能使用非首充记录）
	logs.Info("GetTodayLatestRechargeForActivity 开始查询: 用户ID=%d, 运营商ID=%d, 渠道ID=%d, 时间范围=%s至%s",
		userId, sellerId, channelId, todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

	todayRecharges, err := rechargeDb.
		Where(rechargeTb.UserID.Eq(userId)).
		Where(rechargeTb.SellerID.Eq(sellerId)).
		Where(rechargeTb.ChannelID.Eq(channelId)).
		Where(rechargeTb.State.Eq(5)).                              // 状态为成功
		Where(rechargeTb.IsFirst.Eq(2)).                            // 非首充（IsFirst=2表示不是第一次充值）
		Where(rechargeTb.CreateTime.Between(todayStart, todayEnd)). // 当日充值
		Order(rechargeTb.CreateTime.Desc()).                        // 按时间倒序，获取最新的充值记录
		Find()

	if err != nil {
		logs.Error("GetTodayLatestRechargeForActivity query today recharge err", err)
		return nil, false, err
	}

	logs.Info("GetTodayLatestRechargeForActivity 查询结果: 用户ID=%d, 当天充值记录数=%d", userId, len(todayRecharges))
	for i, recharge := range todayRecharges {
		logs.Info("GetTodayLatestRechargeForActivity 充值记录[%d]: 时间=%v, 金额=%f, 是否首充=%d, 状态=%d",
			i, recharge.CreateTime, recharge.RealAmount, recharge.IsFirst, recharge.State)
	}

	if len(todayRecharges) == 0 {
		logs.Error("GetTodayLatestRechargeForActivity 当天无非首充记录: 用户ID=%d", userId)
		return nil, false, errors.New("今日无复充记录")
	}

	// 使用当天最新的充值记录作为领取凭证
	latestRecharge := todayRecharges[0]
	logs.Info("GetTodayLatestRechargeForActivity 找到当天最新充值: 用户ID=%d, 充值时间=%v, 充值金额=%f, 是否首充=%d",
		userId, latestRecharge.CreateTime, latestRecharge.RealAmount, latestRecharge.IsFirst)

	// 检查这笔充值后是否有投注记录
	hasBetAfterRecharge, err := CheckUserMultipleDepositAfterRecharge(
		int(userId), int(sellerId), int(channelId),
		latestRecharge.CreateTime, now)
	if err != nil {
		logs.Error("GetTodayLatestRechargeForActivity CheckUserMultipleDepositAfterRecharge err", err)
		return nil, false, err
	}

	logs.Info("GetTodayLatestRechargeForActivity 投注检查结果: 用户ID=%d, 充值时间=%v, 充值后是否有投注=%v",
		userId, latestRecharge.CreateTime, hasBetAfterRecharge)

	return latestRecharge, hasBetAfterRecharge, nil
}

// ValidateRechargeForActivity 验证充值记录是否满足活动要求
// 参数:
//   - recharge: 充值记录
//   - selectData: 活动档位配置
//   - activityName: 活动名称（用于日志）
//
// 返回:
//   - bool: 是否满足要求
//   - string: 错误信息
func ValidateRechargeForActivity(recharge *xHashModel.XRecharge, selectData DepositConfig, activityName string) (bool, string) {
	rechargeDecimal := decimal.NewFromFloat(recharge.RealAmount)
	firstChargeUstdLimit := selectData.FirstChargeUstdLimit

	// 检查充值金额是否达到要求
	if rechargeDecimal.LessThan(firstChargeUstdLimit) {
		logs.Info("%s 充值金额验证失败: 充值金额=%s, 要求金额=%s",
			activityName, rechargeDecimal.StringFixed(2), firstChargeUstdLimit.StringFixed(2))
		return false, fmt.Sprintf("充值金额不足，需要至少%s", firstChargeUstdLimit.StringFixed(2))
	}

	logs.Info("%s 充值金额验证通过: 充值金额=%s, 要求金额=%s",
		activityName, rechargeDecimal.StringFixed(2), firstChargeUstdLimit.StringFixed(2))
	return true, ""
}

// MultipleDepositEligibilityResult 复充活动资格检查结果
type MultipleDepositEligibilityResult struct {
	IsEligible bool   // 是否符合资格
	ErrorCode  int    // 错误码
	ErrorMsg   string // 错误信息
}

// CheckMultipleDepositEnable 检查复充活动基本领取资格（封装方法，供外部使用）
// 参数:
//   - userId: 用户ID
//   - skipDepositCheck: 是否跳过充值记录检查，true=跳过检查，false=正常检查
//
// 返回:
//   - MultipleDepositEligibilityResult: 检查结果
func CheckMultipleDepositEnable(userId int32, skipDepositCheck bool) MultipleDepositEligibilityResult {
	return CheckMultipleDepositEnableWithActiveId(userId, utils.MultipleDepositGift, skipDepositCheck)
}

// CheckMultipleDepositEnableWithActiveId 检查复充活动基本领取资格（支持指定活动ID）
// 参数:
//   - userId: 用户ID
//   - activeId: 活动ID
//   - skipDepositCheck: 是否跳过充值记录检查，true=跳过检查，false=正常检查
//
// 返回:
//   - MultipleDepositEligibilityResult: 检查结果
func CheckMultipleDepositEnableWithActiveId(userId int32, activeId int32, skipDepositCheck bool) MultipleDepositEligibilityResult {
	// 用户表相关
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 用户钱包表相关
	userWalletTb := server.DaoxHashGame().XUserWallet
	userWalletDb := server.DaoxHashGame().XUserWallet.WithContext(context.Background())

	// 获取当前时间
	now := time.Now()
	// 获取用户信息并验证用户资格
	user, err := userDb.Select(userTb.UserID, userTb.Amount, userTb.WithdrawLiuSui, userTb.IsTest, userTb.TotalLiuSui, userTb.CSGroup, userTb.CSID, userTb.TopAgentID, userTb.RegisterTime, userTb.Email, userTb.BindEmail, userTb.LoginIP, userTb.LoginDeviceID, userTb.SellerID, userTb.ChannelID).
		Where(userTb.UserID.Eq(userId)).
		First()
	if err != nil {
		logs.Error("CheckMultipleDepositEnable user err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIInternal,
			ErrorMsg:   "系统错误,请稍后再试",
		}
	}
	// 第一步：获取活动定义,验证活动有效性
	activeDefine, err := GetActiveDefine(user.SellerID, user.ChannelID, activeId)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable GetActiveDefine err", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINotDefine,
			ErrorMsg:   "活动不存在或已关闭",
		}
	}

	// 检查活动状态是否开启
	if activeDefine.State != utils.ActiveStateOpen {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINotDefine,
			ErrorMsg:   "活动不存在或已关闭",
		}
	}

	// 校验活动时间范围
	if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
		startTime := time.UnixMilli(activeDefine.EffectStartTime)
		endTime := time.UnixMilli(activeDefine.EffectEndTime)
		if now.Before(startTime) || now.After(endTime) {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINotDefine,
				ErrorMsg:   "活动已过期,您可以继续游戏",
			}
		}
	}

	// 第二步：解析活动配置，检查用户当天领取次数限制
	// 复充活动允许每天多次领取，需要检查的是当天时间内规定领取的次数
	// 解析活动基础配置
	var baseData DepositBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
	if err != nil {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIParamsIllegal,
			ErrorMsg:   "活动配置有误",
		}
	}

	// 检查用户当天是否已达到领取次数限制
	if baseData.MaxDailyIDAttempts > 0 {
		dailyAttemptCount, err := CheckUserDailyActivityAttempts(
			user.UserID, user.SellerID, user.ChannelID, activeId, baseData.MaxDailyIDAttempts)
		if err != nil {
			logs.Error("CheckMultipleDepositEnable CheckUserDailyActivityAttempts err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if dailyAttemptCount >= baseData.MaxDailyIDAttempts {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   fmt.Sprintf("已领取过该活动奖励"),
			}
		}

		logs.Info("CheckMultipleDepositEnable 当天领取次数检查通过: 用户ID=%d, 今日已领取=%d次, 每日限制=%d次",
			userId, dailyAttemptCount, baseData.MaxDailyIDAttempts)
	}

	// 第三步：解析活动配置
	// 解析活动档位配置
	var configData []DepositConfig
	err = json.Unmarshal([]byte(activeDefine.Config), &configData)
	if err != nil {
		logs.Error("CheckMultipleDepositEnable 活动配置异常:", err)
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIParamsIllegal,
			ErrorMsg:   err.Error(),
		}
	}

	// baseData 已在第二步中解析，这里不需要重复解析

	// 检查是否为测试账号
	if user.IsTest == 1 {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveIIsTest,
			ErrorMsg:   "该玩家禁止领取",
		}
	}

	// 检查邮箱绑定要求
	if baseData.IsBindEmail && user.BindEmail == "" {
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   "您未绑定邮箱,请绑定邮箱",
		}
	}

	// 检查钱包地址有效性
	if baseData.IsValidWallet {
		// 查询用户是否有已验证的钱包地址
		userWallet, err := userWalletDb.
			Where(userWalletTb.UserID.Eq(user.UserID)).
			Where(userWalletTb.SellerID.Eq(user.SellerID)).
			Where(userWalletTb.ChannelID.Eq(user.ChannelID)).
			Where(userWalletTb.State.Eq(1)). // 状态 1已验证 2未验证
			First()

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("CheckMultipleDepositEnable query wallet address err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if userWallet == nil || userWallet.Address == "" {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   "您未激活钱包地址,请激活钱包地址",
			}
		}
	}

	// 检查同ID当天最大领取次数
	if baseData.MaxIDAttempts > 0 {
		var idAttemptCount int64
		// 获取当天的开始和结束时间
		todayStart := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
		todayEnd := carbon.Parse(carbon.Now().EndOfDay().String()).StdTime()

		// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
		idAttemptCount, err = activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(activeId)).
			Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
			Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
			Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
			Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
			Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 限制为当天
			Count()
		if err != nil {
			logs.Error("CheckMultipleDepositEnable query ID daily attempt count err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if int(idAttemptCount) >= baseData.MaxIDAttempts {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   fmt.Sprintf("您当天已达到最大领取次数(%d次)", baseData.MaxIDAttempts),
			}
		}

		// 记录日志，方便调试
		logs.Info("CheckMultipleDepositEnable 用户ID当天验证: 用户ID=%d, 活动ID=%d, 用户当天已领取次数=%d, 最大限制=%d次",
			user.UserID, activeId, idAttemptCount, baseData.MaxIDAttempts)
	}

	// 每日领取次数检查已在第二步中完成，这里不需要重复检查
	logs.Info("CheckMultipleDepositEnable 每日领取次数检查已在第二步完成")

	// 计算注册时间和有效时间范围
	registerTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).StartOfDay().String()).StdTime()
	endFindTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay)).StartOfDay().String()).StdTime()

	// 检查是否活动期间内注册的账号
	if baseData.IsDuringReg {
		// 检查用户注册时间是否在活动期间内
		activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
		activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
		if user.RegisterTime.Before(activityStartTime) || user.RegisterTime.After(activityEndTime) {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   "您的账号不在活动时间内注册",
			}
		}
	}

	// 计算活动领取截止时间
	receiveTime := carbon.Parse(carbon.CreateFromStdTime(user.RegisterTime).AddDays(int(baseData.RegisterDay + baseData.ReceiveDay)).StartOfDay().String()).StdTime()
	if now.After(receiveTime) {
		// 已超过领取时间
		return MultipleDepositEligibilityResult{
			IsEligible: false,
			ErrorCode:  utils.ActiveINocondition,
			ErrorMsg:   "当前活动领取时间已过期",
		}
	}

	// 第五步：检查用户充值记录（可选跳过）
	var recharge *xHashModel.XRecharge

	if !skipDepositCheck {
		// 判断是否为X9系列活动
		isX9Activity := activeId == utils.X9SecondDepositGift || activeId == utils.X9ThirdDepositGift ||
			activeId == utils.X9FourthDepositGift || activeId == utils.X9FifthDepositGift

		if isX9Activity {
			// X9系列活动：基于历史充值次数验证
			var requiredRechargeCount int32
			var activityName string

			switch activeId {
			case utils.X9SecondDepositGift:
				requiredRechargeCount = 2
				activityName = "二存活动"
			case utils.X9ThirdDepositGift:
				requiredRechargeCount = 3
				activityName = "三存活动"
			case utils.X9FourthDepositGift:
				requiredRechargeCount = 4
				activityName = "四存活动"
			case utils.X9FifthDepositGift:
				requiredRechargeCount = 5
				activityName = "五存活动"
			}

			logs.Info("CheckMultipleDepositEnable %s充值次数要求: 活动ID=%d, 要求充值次数=%d",
				activityName, activeId, requiredRechargeCount)

			// 查询用户所有充值记录来验证充值次数
			allRecharges, err := rechargeDb.
				Where(rechargeTb.UserID.Eq(user.UserID)).
				Where(rechargeTb.SellerID.Eq(user.SellerID)).
				Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
				Where(rechargeTb.State.Eq(5)). // 状态为成功
				Order(rechargeTb.CreateTime.Asc()).
				Find()
			if err != nil {
				logs.Error("CheckMultipleDepositEnable query all recharges err", err)
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveIInternal,
					ErrorMsg:   "系统错误,请稍后再试",
				}
			}

			if len(allRecharges) < int(requiredRechargeCount) {
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveINocondition,
					ErrorMsg:   fmt.Sprintf("请完成第%d次充值，当前充值次数：%d", requiredRechargeCount, len(allRecharges)),
				}
			}

			// 获取第N次充值记录作为领取凭证
			recharge = allRecharges[requiredRechargeCount-1]
			logs.Info("CheckMultipleDepositEnable %s充值验证通过: 用户ID=%d, 第%d次充值时间=%v, 充值金额=%f",
				activityName, user.UserID, requiredRechargeCount, recharge.CreateTime, recharge.RealAmount)

		} else if baseData.RechargeCount == 0 {
			// 当配置为0时，检查当日是否有非首充的充值记录
			todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			todayEnd := todayStart.Add(24 * time.Hour)

			logs.Info("CheckMultipleDepositEnable RechargeCount=0 开始查询当日非首充: 用户ID=%d, 当前时间=%v, 时间范围=%s至%s",
				user.UserID, now, todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

			// 先查询当日所有充值记录用于调试
			allTodayRecharges, err := rechargeDb.
				Where(rechargeTb.UserID.Eq(user.UserID)).
				Where(rechargeTb.SellerID.Eq(user.SellerID)).
				Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
				Where(rechargeTb.State.Eq(5)).                              // 状态为成功
				Where(rechargeTb.CreateTime.Between(todayStart, todayEnd)). // 当日充值
				Order(rechargeTb.CreateTime.Desc()).
				Find()

			if err == nil {
				logs.Info("CheckMultipleDepositEnable 当日所有充值记录数: %d", len(allTodayRecharges))
				for i, recharge := range allTodayRecharges {
					logs.Info("CheckMultipleDepositEnable 充值记录[%d]: 时间=%v, 金额=%f, 是否首充=%d, 状态=%d",
						i, recharge.CreateTime, recharge.RealAmount, recharge.IsFirst, recharge.State)
				}
			}

			// 查询当日非首充的充值记录
			todayRecharges, err := rechargeDb.
				Where(rechargeTb.UserID.Eq(userId)).
				Where(rechargeTb.SellerID.Eq(user.SellerID)).
				Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
				Where(rechargeTb.State.Eq(5)).                              // 状态为成功
				Where(rechargeTb.IsFirst.Eq(2)).                            // 非首充（IsFirst=2表示不是第一次充值）
				Where(rechargeTb.CreateTime.Between(todayStart, todayEnd)). // 当日充值
				Order(rechargeTb.CreateTime.Desc()).                        // 按时间倒序，获取最新的充值记录
				Find()

			if err != nil {
				logs.Error("CheckMultipleDepositEnable query today recharge err", err)
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveIInternal,
					ErrorMsg:   "系统错误,请稍后再试",
				}
			}

			logs.Info("CheckMultipleDepositEnable 当日非首充记录数: %d", len(todayRecharges))

			if len(todayRecharges) == 0 {
				logs.Error("CheckMultipleDepositEnable 当日无非首充记录: 用户ID=%d, 当日总充值=%d, 当日非首充=%d",
					userId, len(allTodayRecharges), len(todayRecharges))
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveINocondition,
					ErrorMsg:   "请先完成今日充值（非首充）",
				}
			}

			// 使用最新的非首充记录
			recharge = todayRecharges[0]
			logs.Info("CheckMultipleDepositEnable 当日非首充检查通过: 用户ID=%d, 当日非首充次数=%d, 最新充值金额=%f",
				userId, len(todayRecharges), recharge.RealAmount)

		} else {
			// 原有逻辑：检查第N次充值
			recharges, err := rechargeDb.Where(rechargeTb.UserID.Eq(userId)).
				Where(rechargeTb.SellerID.Eq(user.SellerID)).
				Where(rechargeTb.ChannelID.Eq(user.ChannelID)).
				Where(rechargeTb.State.Eq(5)). // 状态为成功
				Where(rechargeTb.CreateTime.Between(registerTime, endFindTime)).
				Order(rechargeTb.CreateTime).
				Find()

			if err != nil {
				logs.Error("CheckMultipleDepositEnable recharge err", err)
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveIInternal,
					ErrorMsg:   "系统错误,请稍后再试",
				}
			}

			rechargeCount := len(recharges)
			if rechargeCount < int(baseData.RechargeCount) {
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveINocondition,
					ErrorMsg:   fmt.Sprintf("请完成第%d次充值,当前充值次数:%d", baseData.RechargeCount, rechargeCount),
				}
			}

			// 获取第N次充值记录
			recharge = recharges[rechargeCount-1]
			logs.Info("CheckMultipleDepositEnable 第%d次充值检查通过: 用户ID=%d, 充值金额=%f",
				baseData.RechargeCount, userId, recharge.RealAmount)
		}

		// 所有复充活动都需要检查充值时间是否在活动期间内
		if activeDefine.EffectStartTime != 0 && activeDefine.EffectEndTime != 0 {
			activityStartTime := time.UnixMilli(activeDefine.EffectStartTime)
			activityEndTime := time.UnixMilli(activeDefine.EffectEndTime)
			if recharge.CreateTime.Before(activityStartTime) || recharge.CreateTime.After(activityEndTime) {
				var activityName string
				isX9ActivityCheck := activeId == utils.X9SecondDepositGift || activeId == utils.X9ThirdDepositGift ||
					activeId == utils.X9FourthDepositGift || activeId == utils.X9FifthDepositGift
				if isX9ActivityCheck {
					switch activeId {
					case utils.X9SecondDepositGift:
						activityName = "X9第二次充值"
					case utils.X9ThirdDepositGift:
						activityName = "X9第三次充值"
					case utils.X9FourthDepositGift:
						activityName = "X9第四次充值"
					case utils.X9FifthDepositGift:
						activityName = "X9第五次充值"
					default:
						activityName = "X9复充"
					}
				} else {
					activityName = "普通复充"
				}
				logs.Info("%s活动资格检查充值时间: 活动ID=%d, 充值时间=%v, 活动开始时间=%v, 活动结束时间=%v",
					activityName, activeId, recharge.CreateTime, activityStartTime, activityEndTime)
				return MultipleDepositEligibilityResult{
					IsEligible: false,
					ErrorCode:  utils.ActiveINocondition,
					ErrorMsg:   "活动仅限活动期间内的充值参与",
				}
			}
		}

		// 第六步：检查用户在复充成功后是否有投注记录
		hasBetAfterRecharge, err := CheckUserMultipleDepositAfterRecharge(
			int(user.UserID), int(user.SellerID), int(user.ChannelID),
			recharge.CreateTime, now)
		if err != nil {
			logs.Error("CheckMultipleDepositEnable CheckUserMultipleDepositAfterRecharge err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if hasBetAfterRecharge {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   "对不起,因您在申请活动前已经投注,已不符合复充活动要求",
			}
		}
	}

	// 第七步：只有当RechargeCount > 0时才检查充值次数
	if baseData.RechargeCount > 0 {
		hasEnoughDeposits, actualCount, err := CheckUserMultipleDepositCount(user.SellerID, user.ChannelID, activeId, user.UserID, baseData.RechargeCount)
		if err != nil {
			logs.Error("CheckMultipleDepositEnable CheckUserMultipleDepositCount err", err)
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveIInternal,
				ErrorMsg:   "系统错误,请稍后再试",
			}
		}

		if !hasEnoughDeposits {
			return MultipleDepositEligibilityResult{
				IsEligible: false,
				ErrorCode:  utils.ActiveINocondition,
				ErrorMsg:   fmt.Sprintf("请完成第%d次充值,当前充值次数:%d", baseData.RechargeCount, actualCount),
			}
		}
	} else {
		logs.Info("CheckMultipleDepositEnable 跳过充值记录检查: UserId=%d", userId)
	}

	// 第八步：复充活动允许每天多次领取，不需要检查历史参与记录
	// 每日领取次数限制已在前面的步骤中检查完成
	logs.Info("CheckMultipleDepositEnable 复充活动允许每天多次领取，跳过历史参与检查")

	// 如果所有检查都通过，返回成功
	return MultipleDepositEligibilityResult{
		IsEligible: true,
		ErrorCode:  utils.ActiveISuccess,
		ErrorMsg:   "",
	}
}

// CheckUserDailyActivityAttempts 检查用户当日活动领取次数
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//   - maxAttempts: 最大允许次数
//
// 返回:
//   - int: 当日已领取次数
//   - error: 错误信息
func CheckUserDailyActivityAttempts(userId, sellerId, channelId, activeId int32, maxAttempts int) (int, error) {
	// 活动奖励审核表相关
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	// 获取今日开始和结束时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	// 只检查待审核、审核通过和自动通过的记录，拒绝状态不算参与过活动
	dailyIdAttemptCount, err := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(activeId)).
		Where(activeRewardAuditTb.AuditState.In(1, 3, 4)). // 检查待审核、审核通过和自动通过的记录
		Where(activeRewardAuditTb.UserID.Eq(userId)).
		Where(activeRewardAuditTb.SellerID.Eq(sellerId)).
		Where(activeRewardAuditTb.ChannelID.Eq(channelId)).
		Where(activeRewardAuditTb.CreateTime.Between(todayStart, todayEnd)). // 限制在今日范围内
		Count()

	if err != nil {
		logs.Error("CheckUserDailyActivityAttempts query daily ID attempt count err", err)
		return 0, err
	}

	logs.Info("CheckUserDailyActivityAttempts 查询结果: 用户ID=%d, 活动ID=%d, 今日已领取=%d次, 最大允许=%d次, 时间范围=%s至%s",
		userId, activeId, dailyIdAttemptCount, maxAttempts,
		todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

	return int(dailyIdAttemptCount), nil
}

// LogDailyActivityAttempts 记录用户每日活动领取详情（用于调试和监控）
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//   - currentAttempts: 当前已领取次数
//   - maxAttempts: 最大允许次数
//   - actionType: 操作类型（"pre-check"预检查, "check"检查, "claim"领取）
func LogDailyActivityAttempts(userId, sellerId, channelId, activeId int32, currentAttempts, maxAttempts int, actionType string) {
	today := time.Now().Format("2006-01-02")

	var actionDesc string
	switch actionType {
	case "pre-check":
		actionDesc = "预检查(第二步)"
	case "check":
		actionDesc = "验证检查"
	case "claim":
		actionDesc = "成功领取"
	default:
		actionDesc = actionType
	}

	logs.Info("复充活动每日领取记录 [%s]: 日期=%s, 用户ID=%d, 活动ID=%d, 运营商=%d, 渠道=%d, 已领取=%d次, 限制=%d次, 剩余=%d次",
		actionDesc, today, userId, activeId, sellerId, channelId,
		currentAttempts, maxAttempts, maxAttempts-currentAttempts)

	// 如果接近限制，记录警告日志
	if currentAttempts >= maxAttempts-1 {
		logs.Warn("复充活动每日领取接近限制: 用户ID=%d, 活动ID=%d, 已领取=%d次, 限制=%d次",
			userId, activeId, currentAttempts, maxAttempts)
	}
}

// GetUserDailyActivityStatus 获取用户当日活动状态（用于前端显示）
// 参数:
//   - userId: 用户ID
//   - sellerId: 运营商ID
//   - channelId: 渠道ID
//   - activeId: 活动ID
//   - maxDailyAttempts: 每日最大领取次数
//
// 返回:
//   - currentAttempts: 当日已领取次数
//   - remainingAttempts: 当日剩余次数
//   - canClaim: 是否还可以领取
//   - error: 错误信息
func GetUserDailyActivityStatus(userId, sellerId, channelId, activeId int32, maxDailyAttempts int) (int, int, bool, error) {
	if maxDailyAttempts <= 0 {
		// 没有每日限制，可以无限领取
		return 0, 999, true, nil
	}

	currentAttempts, err := CheckUserDailyActivityAttempts(userId, sellerId, channelId, activeId, maxDailyAttempts)
	if err != nil {
		return 0, 0, false, err
	}

	remainingAttempts := maxDailyAttempts - currentAttempts
	canClaim := remainingAttempts > 0

	logs.Info("GetUserDailyActivityStatus: 用户ID=%d, 活动ID=%d, 当日已领取=%d次, 剩余=%d次, 可领取=%v",
		userId, activeId, currentAttempts, remainingAttempts, canClaim)

	return currentAttempts, remainingAttempts, canClaim, nil
}

// CalculateMultipleDepositActivityFlow 计算复充活动的流水（从当天开始计算）
//
// 复充活动流水计算规则：
// 1. 流水计算时间从当天00:00:00开始，到当前时间结束
// 2. 不同于首充活动从注册时间开始计算
// 3. 不同于其他活动从活动开始时间计算
// 4. 这样确保用户在当天领取活动时，只计算当天的流水
//
// 参数:
//   - user: 用户信息
//   - minBetAmount: 最小投注金额限制
//   - maxBetAmount: 最大投注金额限制
//   - isExcludeBetLimit: 是否排除投注限制
//
// 返回:
//   - decimal.Decimal: 当天流水总额
//   - error: 错误信息
func CalculateMultipleDepositActivityFlow(user *xHashModel.XUser, minBetAmount, maxBetAmount decimal.Decimal, isExcludeBetLimit bool) (decimal.Decimal, error) {
	// 获取当天的时间范围
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)
	todayEnd := time.Now() // 截止到当前时间

	logs.Info("CalculateMultipleDepositActivityFlow 开始计算复充活动流水: 用户ID=%d, 时间范围=%s至%s",
		user.UserID, todayStart.Format("2006-01-02 15:04:05"), todayEnd.Format("2006-01-02 15:04:05"))

	// 调用现有的流水计算函数，但使用当天的时间范围
	totalFlow, err := CalculateUserTotalFlow(user, todayStart, todayEnd, minBetAmount, maxBetAmount, isExcludeBetLimit)
	if err != nil {
		logs.Error("CalculateMultipleDepositActivityFlow 计算流水失败: 用户ID=%d, 错误=%v", user.UserID, err)
		return decimal.Zero, err
	}

	logs.Info("CalculateMultipleDepositActivityFlow 流水计算完成: 用户ID=%d, 当天流水=%s, 投注限制=%v",
		user.UserID, totalFlow.StringFixed(2), isExcludeBetLimit)

	return totalFlow, nil
}
