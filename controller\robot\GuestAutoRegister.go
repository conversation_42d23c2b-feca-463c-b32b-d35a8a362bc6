package robot

import (
	"context"
	"fmt"
	"html/template"
	"math/rand"
	"strings"
	"time"
	"xserver/controller/xemail"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/spf13/cast"
)

// 游客点击对应域名 自动注册
const DomainName = "域名自动注册渠道"

func DomainAutoRegisterByGuest(ctx context.Context, host string) (accountID string, isRegister bool) {

	isRegister = IfOpenDomainAutoRegister(ctx, host, DomainName)
	if isRegister == false {
		return "", false
	}
	//fmt.Printf("query : %#v", results[0].Host)
	// 生成 ID
	redisKey := "hx-game:guest_id_generator"
	// 从 Redis 中自增
	id, err := server.RdbC.Incr(ctx, redisKey).Result()
	if err != nil {
		return "", false
	}
	return generateRandomLetters(4) + cast.ToString(id), true
}

func IfOpenDomainAutoRegister(ctx context.Context, host string, DomainName string) bool {
	type Result struct {
		model.XChannel
		Host  string `json:"host"`
		State int32  `json:"state"`
	}
	var results []*Result

	if host == "" {
		return false
	}

	dao := server.DaoxHashGame().XChannel
	xChannelHost := server.DaoxHashGame().XChannelHost

	_, err := dao.WithContext(ctx).Select(dao.ALL, xChannelHost.Host, xChannelHost.State).
		Select(dao.ALL, xChannelHost.Host, xChannelHost.State).
		LeftJoin(xChannelHost, xChannelHost.ChannelID.EqCol(dao.ChannelID)).
		Where(xChannelHost.Host.Eq(host)).
		Where(dao.ChannelName.Eq(DomainName)).
		Where(dao.State.Eq(1)).
		Where(xChannelHost.State.Eq(1)).
		Or(xChannelHost.IsAutoDomainReg.Eq(1), xChannelHost.Host.Eq(host)). // 开关配置
		ScanByPage(&results, 0, 1)
	if err != nil {
		return false
	}
	if len(results) < 1 || results[0].Host == "" {
		return false
	}
	return true
}

func generateRandomLetters(n int) string {
	letters := []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

	src := rand.NewSource(time.Now().UnixNano())
	r := rand.New(src)
	result := make([]rune, n)
	for i := range result {
		result[i] = letters[r.Intn(len(letters))]
	}
	return string(result)
}

func CacheUserClientSettingByLangCode(userID int64, langID int) {
	if langID <= 0 {
		logs.Error("CacheUserClientSettingByLangCode error: lang id %d", langID)
		return
	}
	langCode := ""
	if val, ok := LangMap[langID]; ok {
		langCode = val
	}
	if langCode == "" {
		return
	}
	keyName := "hx-game:user:setting:" + cast.ToString(userID)
	_ = server.CRedis().SetString(keyName, langCode)

	_ = server.CRedis().Expire(keyName, 60*60*24*7*4*3)
	return
}

func UpdateUserPasswordFirstTime(ctx context.Context, userID int64) bool {
	dao := server.DaoxHashGame().XUser
	_, err := dao.WithContext(ctx).
		Where(dao.UserID.Eq(cast.ToInt32(userID))).
		Update(dao.UpdatePasswordTime, nil)
	if err != nil {
		return false
	}
	return true
}

var subjectNoticeEmailInfoMap = map[string]string{
	"zh":    "【平台帐号资讯通知信】",
	"zh-tw": "【平台帳號資訊通知信】",
	"en":    "[Platform Account Information Notification]",
}

var bodyNoticeEmailInfoMap = map[string]string{
	"zh": `平台帐号资讯通知

主旨：您的平台帐号资讯已送达，请妥善保管

亲爱的用户您好，
感谢您注册本平台，以下是您的帐号资讯，请务必妥善保存：

登入域名：%s

帐号：%s

若您在登入过程中遇到任何问题，欢迎随时联系我们的线上客服，我们将竭诚为您服务。

祝您使用愉快！
Magic88平台 客户服务中心
（此邮件为系统自动发送，请勿直接回覆）`,

	"zh-tw": `平台帳號資訊通知

主旨：您的平台帳號資訊已送達，請妥善保管

親愛的用戶您好，
感謝您註冊本平台，以下是您的帳號資訊，請務必妥善保存：

登入網域：%s

帳號：%s

若您在登入過程中遇到任何問題，歡迎隨時聯繫我們的線上客服，我們將竭誠為您服務。

祝您使用愉快！
Magic88平台 客戶服務中心
（此郵件為系統自動發送，請勿直接回覆）`,

	"en": `Platform Account Information Notification

Subject: Your platform account information has been delivered, please keep it safe

Dear user,
Thank you for registering on our platform. Below is your account information, please be sure to keep it secure:

Login domain: %s

Account: %s

If you encounter any issues during login, please feel free to contact our online customer service. We will be happy to assist you.

Wishing you an enjoyable experience!
Magic88 Platform Customer Service Center
(This email is automatically generated, please do not reply directly)`,
}

func GetMailTemplate(lang, domain, account string) (string, string) {
	// 如果没有对应语言，默认用英文
	sub, ok := subjectNoticeEmailInfoMap[lang]
	if !ok {
		sub = subjectNoticeEmailInfoMap["en"]
	}

	tpl, ok := bodyNoticeEmailInfoMap[lang]
	if !ok {
		tpl = bodyNoticeEmailInfoMap["en"]
	}
	// 格式化字符串
	text := fmt.Sprintf(tpl, domain, account)

	// 转成 HTML（换行 -> <br>）
	html := template.HTMLEscapeString(text) // 避免特殊字符被解析
	html = strings.ReplaceAll(html, "\n", "<br>")

	// 包装成简单 HTML
	htmlBody := fmt.Sprintf(`<html><body style="font-family: Arial, sans-serif; line-height:1.6; white-space: pre-line;">%s</body></html>`, html)

	return sub, htmlBody
}

func SendNoticeToUserByEmail(email, lang, account, host string) {
	if lang == "" || account == "" || host == "" {
		return
	}
	langCode := strings.ToLower(lang)
	subject, body := GetMailTemplate(langCode, host, account)

	go func() {
		err := xemail.SendEmailCommon(email, subject, body)
		if err != nil {
			logs.Error("SendNoticeToUserByEmail error:邮件通知失败,%v", err)
			return
		}
	}()

}
