package robot

import (
	"context"
	"errors"
	"fmt"
	"net"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/tronscango"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

func (r *Router) GiftRewardFirstTimeStartBoots(ctx *abugo.AbuHttpContent) {
	var errCode int
	var tgTyjAmount float64
	var tgBotId int64
	var tgTyjCurrency = "usdt"
	var tgRobotType int32 // 0-不是从tg进入 1-引客机器人 2-英文接待机器人

	ifCanRewardGift := 0

	reqdata := struct {
		DeviceId   string
		DeviceType string
		Lang       string
		Host       string
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errCode) {
		return
	}
	token := server.GetToken(ctx)
	daoUser := server.DaoxHashGame().XUser
	db := daoUser.WithContext(ctx.Gin())
	user, err := db.Where(daoUser.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errCode) {
		logs.Error(err)
		return
	}
	host, _, _ := net.SplitHostPort(ctx.Host())
	host = strings.Replace(host, "www.", "", -1)
	if reqdata.Host != "" {
		host = reqdata.Host
	}
	if user.AccountType == 5 && user.TgRobotToken != "" {
		// 获取账号的tg属性
		tgRobotGuideDao := server.DaoxHashGame().XRobotConfig

		tgRobotGuide, _ := tgRobotGuideDao.WithContext(ctx.Gin()).Where(tgRobotGuideDao.Token.Eq(user.TgRobotToken)).First()

		if err != nil {
			logs.Error("获取机器人属性错误", err.Error())
		} else {
			if tgRobotGuide != nil {
				tgRobotType = tgRobotGuide.RobotType
				tgBotId = tgRobotGuide.ID
				tgTyjAmount = cast.ToFloat64(tgRobotGuide.GiftAmount)
				if user.IsInResourceDb == 1 {
					tgTyjAmount = cast.ToFloat64(tgRobotGuide.GiftAmountIndb)
				}
				tgTyjCurrency = tgRobotGuide.GiftCurrency
				// 判断
				// 检查领取条件
				if err := CheckTyjRestrictions(ctx, user, tgRobotGuide, cast.ToFloat64(tgTyjAmount)); err != nil {
					logs.Error("API user info CheckTyjRestrictions err:%s", err.Error())
				} else {
					ifCanRewardGift = 1
				}
			}
		}
		if ifCanRewardGift == 0 {
			// 查看是否可领取新机器人礼金
			xRobotMissionRewardDao := server.DaoxHashGame().XRobotMissionReward
			xRobotMissionRewardInfos, _ := xRobotMissionRewardDao.WithContext(nil).
				Where(xRobotMissionRewardDao.UserID.Eq(int64(token.UserId))).
				Where(xRobotMissionRewardDao.Currency.Eq("usdt")).
				Where(xRobotMissionRewardDao.Stat.Eq(1)).
				First()

			if xRobotMissionRewardInfos != nil {
				if _, err = xRobotMissionRewardDao.WithContext(nil).
					Where(xRobotMissionRewardDao.ID.Eq(xRobotMissionRewardInfos.ID)).
					Where(xRobotMissionRewardDao.Stat.Eq(1)).
					Update(xRobotMissionRewardDao.Stat, 2); err != nil {
					return
				}
				tgTyjCurrency = xRobotMissionRewardInfos.Currency
				tgTyjAmount = cast.ToFloat64(xRobotMissionRewardInfos.Amount)
				ifCanRewardGift = 1
			}
		}

	}

	xChannelHost := server.DaoxHashGame().XChannelHost
	channelHost, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	isTyjEnable := 2
	if channelHost != nil {
		isTyjEnable = int(channelHost.IsTyjEnable)
	}
	// 非tg机器人注册体验金状态
	if isTyjEnable == 1 && user.AccountType != 5 {
		RegisterIPCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegisterIP.Eq(user.RegisterIP)).Count()
		RegDeviceIDCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegDeviceID.Eq(user.RegDeviceID)).Count()
		amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdt")
		tgTyjAmount = amount

		if channelHost.GiftIPLimit == 1 {
			if RegisterIPCount > 1 {
				user.UsdtGiftStatus = -1
			}
		}

		if channelHost.GiftDeviceIDLimit == 1 {
			if RegDeviceIDCount > 1 {
				user.UsdtGiftStatus = -1
			}
		}

		if user.UsdtGiftStatus == 2 || user.UsdtGiftStatus == 4 {
			user.UsdtGiftStatus = -1
		}

		if err := CheckNormalRewardGift(ctx); err != nil {
			logs.Error("API user info CheckNormalRewardGift err:%s", err.Error())
		} else {
			ifCanRewardGift = 1
		}

	}

	ctx.Put("IsTyjEnable", isTyjEnable)
	ctx.Put("if_can_reward_gift", ifCanRewardGift)
	ctx.Put("TgTyjAmount", tgTyjAmount)
	ctx.Put("TgTyjCurrency", tgTyjCurrency)
	ctx.Put("TgRobotType", tgRobotType)
	ctx.Put("tgBotId", tgBotId)
	ctx.Put("IsInResourceDb", user.IsInResourceDb)
	logs.Info("用户AccountType", user.AccountType)
	logs.Info("用户TgRobotType", tgRobotType)
	logs.Info("用户UserId", user.UserID)
	logs.Info("用户UsdtGiftStatus", user.UsdtGiftStatus)
	logs.Info("用户TgTyjCurrency", tgTyjCurrency)
	logs.Info("用户TgTyjAmount", tgTyjAmount)
	logs.Info("用户: %d if_can_reward_gift:%d", user.UserID, ifCanRewardGift)
	ctx.RespOK()
}

func CheckNormalRewardGift(ctx *abugo.AbuHttpContent) error {
	reqData := struct {
		Host string
	}{}

	host, _, _ := net.SplitHostPort(ctx.Host())
	host = strings.Replace(host, "www.", "", -1)
	if reqData.Host != "" {
		host = reqData.Host
	}

	token := server.GetToken(ctx)
	user, err := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		//return errors.New("未找到该用户")
		//ctx.RespErrString(true, errCode, "未找到该用户")
		return errors.New("未找到该用户")
	}

	channelHost, err := server.DaoxHashGame().XChannelHost.WithContext(nil).Where(server.DaoxHashGame().XChannelHost.Host.Eq(host)).First()
	if err != nil {
		return errors.New("该域名渠道不存在")
	}

	isTyjEnable := 2
	if channelHost != nil {
		isTyjEnable = int(channelHost.IsTyjEnable)
	}

	if isTyjEnable == 2 {
		return errors.New("该渠道无法领取体验金")
	}

	// 判断用户的体验金状态
	if user.UsdtGiftStatus != 1 && user.UsdtGiftStatus != 3 {
		return errors.New("不符合领取条件")
	}

	amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdt")

	// 设备判断
	if channelHost.GiftDeviceIDLimit == 1 {
		DeviceIDCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegDeviceID.Eq(user.RegDeviceID)).Count()
		if DeviceIDCount > 1 {
			err := createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
			if err != nil {
				return err
			}
			return errors.New("设备ID限制")
		}
	}

	// IP判断
	if channelHost.GiftIPLimit == 1 {
		IPCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegisterIP.Eq(user.RegisterIP)).Count()
		if IPCount > 1 {
			err := createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
			if err != nil {
				return err
			}
			return errors.New("IP限制")
		}
	}

	if err := giveUSDTTyj(user, amount); err != nil {
		return err
	}
	return nil
}

// CheckTyjRestrictions 检查体验金领取条件
// 包括IP限制、设备ID限制和钱包地址限制
func CheckTyjRestrictions(ctx *abugo.AbuHttpContent, user *model2.XUser, robotGuide interface{}, amount float64) error {
	// 根据传入的robotGuide类型获取相关属性
	var isIPRestriction, isDeviceRestriction, isWalletRestriction int32
	var ipWhitelist string

	// 判断用户的体验金状态
	if user.UsdtGiftStatus != 1 && user.UsdtGiftStatus != 3 {
		return errors.New("不符合领取条件")
	}

	// 判断robotGuide的类型并获取相应的属性
	switch guide := robotGuide.(type) {
	case *model2.XTgRobotGuide:
		isIPRestriction = guide.IsIPRestriction
		isDeviceRestriction = guide.IsDeviceRestriction
		isWalletRestriction = guide.IsWalletRestriction
		ipWhitelist = guide.IPWhitelist

	case *model2.XRobotConfig:
		isIPRestriction = guide.IsIPRestriction
		isDeviceRestriction = guide.IsDeviceRestriction
		isWalletRestriction = guide.IsWalletRestriction
		ipWhitelist = guide.IPWhitelist

	default:
		return errors.New("不支持的机器人类型")
	}

	// 检查IP限制
	if isIPRestriction == 1 && strings.Index(ipWhitelist, user.LoginIP) == -1 {
		userXUser := server.DaoxHashGame().XUser
		LoginIPCount, _ := userXUser.WithContext(ctx.Gin()).Where(userXUser.LoginIP.Eq(user.LoginIP)).Count()
		if LoginIPCount > 1 {
			err := createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
			if err != nil {
				//ctx.RespErrString(true, errCode, "系统错误,请稍后再试.")
				return err
			}
			return errors.New("IP限制")
		}
	}

	// 检查设备ID限制
	if isDeviceRestriction == 1 {
		userXUser := server.DaoxHashGame().XUser
		DeviceIDCount, _ := userXUser.WithContext(ctx.Gin()).Where(userXUser.LoginDeviceID.Eq(user.LoginDeviceID)).Count()
		if DeviceIDCount > 1 {
			err := createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
			if err != nil {
				//ctx.RespErrString(true, errCode, "系统错误,请稍后再试.")
				return err
			}

			return errors.New("设备ID限制")
		}
	}

	// 检查钱包地址限制
	if isWalletRestriction == 1 {
		ok, err := checkAddressRelation(user.Address)
		if err != nil {
			//ctx.RespErrString(true, errCode, "系统错误,请稍后再试.")
			return err
		}
		if !ok {
			err := createUsdtTyjRejectOrder(user, "地址关联,拒绝发放", amount)
			if err != nil {
				//ctx.RespErrString(true, errCode, "系统错误,请稍后再试.")
				return err
			}

			return errors.New("钱包地址限制")
		}
	}
	if err := giveUSDTTyj(user, amount); err != nil {
		return err
	}
	return nil
}

// 创建被拒绝的trx体验金申请单
func createTrxTyjRejectOrder(user *model2.XUser, memo string, amount float64) (err error) {
	// amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingTrx")
	return createTyjOrder(user, memo, "trx", 2, amount)
}

// 创建被拒绝的usdt体验金申请单
func createUsdtTyjRejectOrder(user *model2.XUser, memo string, amount float64) (err error) {
	return createTyjOrder(user, memo, "usdt", 2, amount)
}

// 发放USDT体验金(不需要人工审核)
func giveUSDTTyj(user *model2.XUser, amount float64) (err error) {
	return createTyjOrder(user, "系统自动发放", "usdt", 5, amount)
}

func createTyjOrder(user *model2.XUser, memo string, symbol string, state int32, amount float64) (err error) {
	if amount == 0 {
		return
	}
	ctx := context.Background()
	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		RegisterIPCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.RegisterIP.Eq(user.RegisterIP)).Count()
		LoginIPCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.LoginIP.Eq(user.LoginIP)).Count()
		PwdCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.Password.Eq(user.Password)).Count()
		TiYanJingUsdtLiushuiBeishu := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtLiushuiBeishu")

		tbTiyanjing := &model2.XTiyanjing{
			SellerID:     user.SellerID,
			ChannelID:    user.ChannelID,
			UserID:       user.UserID,
			State:        state, // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
			Symbol:       symbol,
			Amount:       amount,
			Memo:         memo,
			Account:      user.Account,
			TgName:       user.TgName,
			RegisterTime: user.RegisterTime,
			LoginTime:    user.LoginTime,
			CSGroup:      user.CSGroup,
			CSID:         user.CSID,
			IPCount:      int32(RegisterIPCount),
			LoginIPCount: int32(LoginIPCount),
			PwdCount:     int32(PwdCount),
		}
		err := tx.XTiyanjing.WithContext(ctx).Select(
			tx.XTiyanjing.SellerID, tx.XTiyanjing.ChannelID, tx.XTiyanjing.UserID, tx.XTiyanjing.State, tx.XTiyanjing.Symbol,
			tx.XTiyanjing.Amount, tx.XTiyanjing.Memo, tx.XTiyanjing.Account, tx.XTiyanjing.TgName, tx.XTiyanjing.RegisterTime,
			tx.XTiyanjing.LoginTime, tx.XTiyanjing.CSGroup, tx.XTiyanjing.CSID, tx.XTiyanjing.IPCount, tx.XTiyanjing.LoginIPCount,
			tx.XTiyanjing.PwdCount,
		).Create(tbTiyanjing)
		if err != nil {
			return err
		}
		if state == 2 && symbol == "trx" {
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).
				Where(tx.XUser.TrxGiftStatus.Eq(1)).
				Update(tx.XUser.TrxGiftStatus, -1)
			if err != nil {
				return err
			}
		} else if state == 2 && symbol == "usdt" {
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).
				Where(tx.XUser.UsdtGiftStatus.Eq(1)).
				Update(tx.XUser.UsdtGiftStatus, -1)
			if err != nil {
				return err
			}
		} else if state == 5 && symbol == "usdt" {
			_, err = tx.XTiyanjing.WithContext(ctx).Where(tx.XTiyanjing.UserID.Eq(user.UserID), tx.XTiyanjing.Symbol.Eq(symbol)).
				Update(tx.XTiyanjing.SendTime, time.Now())
			if err != nil {
				return err
			}
			err = tx.XTiyanjinex.WithContext(ctx).Create(&model2.XTiyanjinex{ID: tbTiyanjing.ID, State: state})
			if err != nil {
				return err
			}
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).Update(tx.XUser.UsdtGiftStatus, tx.XUser.UsdtGiftStatus.Add(1))
			if err != nil {
				return err
			}
			// 加提现流水
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).Update(tx.XUser.WithdrawLiuSui, tx.XUser.WithdrawLiuSui.Add(TiYanJingUsdtLiushuiBeishu*amount))
			if err != nil {
				return err
			}

			if user.AccountType == 5 {
				// 语音播报
				body := struct {
					SellerId    int32           `validate:"required"`
					Symbol      string          `validate:"required"`
					UserId      int32           `validate:"required"`
					KefuAccount string          `validate:"required"`
					Amount      decimal.Decimal `validate:"required"`
				}{
					SellerId:    user.SellerID,
					Symbol:      symbol,
					UserId:      user.UserID,
					KefuAccount: user.CSID,
					Amount:      decimal.NewFromFloat(amount),
				}
				adminapi := viper.GetString("adminapi")
				adminapiReadonly := viper.GetString("adminapi_readonly")
				baseurl := "/api/TyjBroadcast/broadcast"
				resp, err := req.Post(fmt.Sprintf("%s%s", adminapi, baseurl), req.BodyJSON(body))
				if err != nil {
					logs.Error("语音播报 err:", err)
				} else {
					logs.Debug("语音播报 resp:", resp.String())
				}
				_, err = req.Post(fmt.Sprintf("%s%s", adminapiReadonly, baseurl), req.BodyJSON(body))
				if err != nil {
					logs.Error("语音播报 err:", err)
				}

				// tg群组汇报
				go tgSendHuibao(user.UserID, 2, time.Now(), amount, symbol)
			}

		}
		return nil
	})

	return err
}

// 检查地址关联
// returns: 通过:true 不通过:false
func checkAddressRelation(address string) (bool, error) {
	reqData := tronscango.AddressTransferGetListReq{AddressList: address}
	list, err := tronscango.GetClient().AddressTransfer_GetList(reqData)
	if err != nil {
		logs.Error("AddressTransfer_GetList err:", err)
		return false, err
	}
	var addrList []string
	for _, v := range list {
		addrList = append(addrList, v.Address)
	}
	return checkAddress(context.Background(), addrList), nil
}

func checkAddress(ctx context.Context, address []string) bool {
	// 检查是否符合体验金发放条件 满足1或2即可
	ok1, ok2 := false, false
	// 1. 钱包创建时间超过2个月 && 钱包余额大于300U && 有活跃交易记录，每周1条转账记录 && 交易量大于500U
	//ok1 = checkAddressTronData(address)
	// 2. 检查的钱包地址不在数据库中
	if !ok1 {
		statAddrDao := server.DaoxHashGame().XUserStatAddress
		_, err := statAddrDao.WithContext(ctx).
			//Where(statAddrDao.ChannelID.Eq(int32(reqdata.ChannelId))).
			Where(statAddrDao.FromAddress.In(address...)).
			First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		walletDao := server.DaoxHashGame().XUserWallet
		walletDb := walletDao.WithContext(ctx)
		walletDb1 := walletDb.Where(walletDao.Address.In(address...))
		_, err = walletDb1.First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		withdrawDao := server.DaoxHashGame().XWithdrawAddress
		_, err = withdrawDao.WithContext(ctx).
			Where(withdrawDao.Address.In(address...)).
			First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		ok2 = true
	}
	return ok1 || ok2
}

// 通过tg机器人发送群组汇报消息
func tgSendHuibao(UserId, Type int32, Time time.Time, Amount float64, Symbol string) {
	sendReq := struct {
		UserId int32 `validate:"required"`
		Type   int32 `validate:"required,gt=0"` // 1:充值 2:领体验金
		Time   string
		Amount decimal.Decimal
		Symbol string
	}{
		UserId: UserId,
		Type:   Type,
		Time:   Time.Format(time.DateTime),
		Amount: decimal.NewFromFloat(Amount),
		Symbol: Symbol,
	}
	tgservice := viper.GetString("tgservice")
	_, err := req.Post(tgservice+"/api/sendHuibao", req.BodyJSON(sendReq))
	if err != nil {
		logs.Error("tg群组汇报发送失败：", err)
	}
	return
}
