package main

import (
	"fmt"
	"xserver/controller/common"
)

// EVO Token 加密工具使用示例
//
// 使用方法：
// 1. 当申请下EVO商户号时，使用此工具对token进行加密
// 2. 将加密后的token保存到配置中
// 3. 系统会自动在NewEvoLogic中解密token
//
// 运行方式：
// go run controller/common/evo_crypto_example.go

func main() {
	fmt.Println("===== EVO Token 加密工具使用示例  =====")

	// 示例：手动加密token
	plainToken := "2434324"
	fmt.Printf("\n1. 加密EVO Token: %s\n", plainToken)

	// 加密
	encryptedToken, err := common.EncryptEvoToken(plainToken)
	if err != nil {
		fmt.Printf("加密失败: %v\n", err)
		return
	}

	fmt.Printf("加密后的Token: %s\n", encryptedToken)

	// 验证解密
	fmt.Printf("\n2. 验证解密:\n")
	decryptedToken, err := common.DecryptEvoToken(encryptedToken)
	if err != nil {
		fmt.Printf("解密失败: %v\n", err)
		return
	}

	fmt.Printf("解密后的Token: %s\n", decryptedToken)

	if decryptedToken == plainToken {
		fmt.Println("✅ 加密解密验证成功!")
	} else {
		fmt.Println("❌ 加密解密验证失败!")
	}

	// 使用命令行工具函数
	fmt.Printf("\n3. 使用命令行工具函数:\n")
	common.EncryptTokenCmd(plainToken)

	fmt.Printf("\n4. 测试解密命令行工具:\n")
	common.DecryptTokenCmd(encryptedToken)

	fmt.Println("\n===== 使用步骤 =====")
	fmt.Println("1. 申请EVO商户号后，获得原始token")
	fmt.Println("2. 使用 common.EncryptTokenCmd(\"your_token\") 加密token")
	fmt.Println("3. 将加密后的token保存到EVO配置中")
	fmt.Println("4. 系统会在NewEvoLogic中自动解密token")
	fmt.Println("===========================")
}

// 快速加密函数 - 可以直接在代码中调用
func QuickEncrypt() {
	// 在这里替换为你的真实token
	plainToken := "REPLACE_WITH_YOUR_REAL_TOKEN"

	if plainToken == "REPLACE_WITH_YOUR_REAL_TOKEN" {
		fmt.Println("请先替换plainToken为你的真实EVO token")
		return
	}

	common.EncryptTokenCmd(plainToken)
}
