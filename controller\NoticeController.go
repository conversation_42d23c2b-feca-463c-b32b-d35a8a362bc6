package controller

import (
	"encoding/json"
	"fmt"
	"xserver/abugo"
	"xserver/server"
)

type NoticeController struct {
}

func (c *NoticeController) Init() {
	gropu := server.Http().NewGroup("/api/notice")
	{
		gropu.PostNoAuth("/get", c.get)
		gropu.PostNoAuth("/v2/get", c.newNoticeGet)
	}
}

func (c *NoticeController) get(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"`
		LangId   int `json:"Lang" form:"Lang"`
		Host     string
		Type     int `json:"Type" form:"Type"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.LangId == 0 {
		reqdata.LangId = 1
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	rediskey := fmt.Sprintf("%s:%s:notice:%d:%d:%d:%d", server.Project(), server.Module(), SellerId, ChannelId, reqdata.LangId, reqdata.Type)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.Put("notice", jdata)
	} else {
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", SellerId, 0)
		where.Add("and", "State", "=", 1, 0)
		where.Add("and", "ChannelId", "=", ChannelId, 0)
		where.Add("and", "LangId", "=", reqdata.LangId, 0)
		where.Add("and", "Type", "=", reqdata.Type, 0)
		presult, err := server.Db().Table("x_notice").Where(where).OrderBy("sort desc").GetList()
		if ctx.RespErr(err, &errcode) {
			return
		}
		for i, v := range *presult {
			v["UpdateTime"] = abugo.LocalTimeToUtc(abugo.GetStringFromInterface(v["UpdateTime"]))
			if (*presult)[i]["Img"] != nil && len(abugo.GetStringFromInterface((*presult)[i]["Img"])) > 0 {
				(*presult)[i]["Img"] = server.GetImageUrl(SellerId) + abugo.GetStringFromInterface((*presult)[i]["Img"])
			} else {
				(*presult)[i]["Img"] = ""
			}
		}
		ctx.Put("notice", *presult)
		server.Redis().SetEx(rediskey, 10, *presult)
	}
	ctx.RespOK()
}

func (c *NoticeController) newNoticeGet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int `validate:"required"`
		LangId   int `json:"Lang" form:"Lang"`
		Host     string
		Type     int `json:"Type" form:"Type"`
	}
	errcode := 0
	reqdata := RequestData{LangId: 1}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	rediskey := fmt.Sprintf("%s:%s:noticev2:%d:%d:%d:%d", server.Project(), server.Module(), SellerId, ChannelId, reqdata.LangId, reqdata.Type)
	redisdata := server.Redis().Get(rediskey)

	if redisdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.Put("notice", jdata)
	} else {
		dao := server.DaoxHashGame().XNoticeV2
		db := dao.WithContext(nil)
		query := db.Where(dao.SellerID.Eq(int32(SellerId)), dao.ChannelID.Eq(int32(ChannelId)), dao.LangID.Eq(int32(reqdata.LangId)), dao.State.Eq(1))
		if reqdata.Type != 0 {
			query.Where(dao.Type.Eq(int32(reqdata.Type)))
		}

		result, err := query.Order(dao.Sort.Desc()).Find()
		if ctx.RespErr(err, &errcode) {
			return
		}

		for i, _ := range result {
			result[i].Img = server.GetImageUrl(SellerId) + result[i].Img
		}

		ctx.Put("notice", result)
		server.Redis().SetEx(rediskey, 10, result)
	}

	ctx.RespOK()
}
