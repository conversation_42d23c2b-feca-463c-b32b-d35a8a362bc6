// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRecharge(db *gorm.DB, opts ...gen.DOOption) xRecharge {
	_xRecharge := xRecharge{}

	_xRecharge.xRechargeDo.UseDB(db, opts...)
	_xRecharge.xRechargeDo.UseModel(&model.XRecharge{})

	tableName := _xRecharge.xRechargeDo.TableName()
	_xRecharge.ALL = field.NewAsterisk(tableName)
	_xRecharge.ID = field.NewInt32(tableName, "Id")
	_xRecharge.UserID = field.NewInt32(tableName, "UserId")
	_xRecharge.SellerID = field.NewInt32(tableName, "SellerId")
	_xRecharge.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xRecharge.OrderID = field.NewString(tableName, "OrderId")
	_xRecharge.OrderType = field.NewInt32(tableName, "OrderType")
	_xRecharge.Symbol = field.NewString(tableName, "Symbol")
	_xRecharge.Amount = field.NewFloat64(tableName, "Amount")
	_xRecharge.Net = field.NewString(tableName, "Net")
	_xRecharge.FromAddress = field.NewString(tableName, "FromAddress")
	_xRecharge.ToAddress = field.NewString(tableName, "ToAddress")
	_xRecharge.TxID = field.NewString(tableName, "TxId")
	_xRecharge.RealAmount = field.NewFloat64(tableName, "RealAmount")
	_xRecharge.TransferRate = field.NewFloat64(tableName, "TransferRate")
	_xRecharge.CreateTime = field.NewTime(tableName, "CreateTime")
	_xRecharge.Memo = field.NewString(tableName, "Memo")
	_xRecharge.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xRecharge.State = field.NewInt32(tableName, "State")
	_xRecharge.NetFee = field.NewFloat64(tableName, "NetFee")
	_xRecharge.CSGroup = field.NewString(tableName, "CSGroup")
	_xRecharge.CSID = field.NewString(tableName, "CSId")
	_xRecharge.AdJustState = field.NewInt32(tableName, "AdJustState")
	_xRecharge.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xRecharge.PayType = field.NewInt32(tableName, "PayType")
	_xRecharge.PayID = field.NewInt32(tableName, "PayId")
	_xRecharge.PayData = field.NewString(tableName, "PayData")
	_xRecharge.PayTime = field.NewTime(tableName, "PayTime")
	_xRecharge.IsFirst = field.NewInt32(tableName, "IsFirst")
	_xRecharge.ThirdID = field.NewString(tableName, "ThirdId")
	_xRecharge.DataState = field.NewInt32(tableName, "DataState")
	_xRecharge.IsTgCounted = field.NewInt32(tableName, "IsTgCounted")
	_xRecharge.MaidianState = field.NewInt32(tableName, "MaidianState")

	_xRecharge.fillFieldMap()

	return _xRecharge
}

type xRecharge struct {
	xRechargeDo xRechargeDo

	ALL          field.Asterisk
	ID           field.Int32
	UserID       field.Int32   // 玩家
	SellerID     field.Int32   // 运营商
	ChannelID    field.Int32   // 渠道商
	OrderID      field.String  // hbc订单id
	OrderType    field.Int32   // 充值订单分类 21充值 22上庄充值
	Symbol       field.String  // 币种
	Amount       field.Float64 // 金额
	Net          field.String
	FromAddress  field.String  // 打款地址
	ToAddress    field.String  // 收款地址
	TxID         field.String  // 交易哈希
	RealAmount   field.Float64 // 到账金额
	TransferRate field.Float64 // 汇率
	CreateTime   field.Time    // 到账时间
	Memo         field.String  // 备注
	TopAgentID   field.Int32   // 顶级id
	State        field.Int32   // 状态 1未找到玩家 2小于最低充值额  3待支付 4已过期 5充值成功
	NetFee       field.Float64
	CSGroup      field.String
	CSID         field.String
	AdJustState  field.Int32
	SpecialAgent field.Int32
	PayType      field.Int32 // 支付类型 1 链上充值 2.pix
	PayID        field.Int32
	PayData      field.String
	PayTime      field.Time // 支付回调时间
	IsFirst      field.Int32
	ThirdID      field.String
	DataState    field.Int32
	IsTgCounted  field.Int32 // 是否tg统计(0未统计 1已统计)
	MaidianState field.Int32 // 1-已經上報 0-未上報

	fieldMap map[string]field.Expr
}

func (x xRecharge) Table(newTableName string) *xRecharge {
	x.xRechargeDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRecharge) As(alias string) *xRecharge {
	x.xRechargeDo.DO = *(x.xRechargeDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRecharge) updateTableName(table string) *xRecharge {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.OrderID = field.NewString(table, "OrderId")
	x.OrderType = field.NewInt32(table, "OrderType")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.Net = field.NewString(table, "Net")
	x.FromAddress = field.NewString(table, "FromAddress")
	x.ToAddress = field.NewString(table, "ToAddress")
	x.TxID = field.NewString(table, "TxId")
	x.RealAmount = field.NewFloat64(table, "RealAmount")
	x.TransferRate = field.NewFloat64(table, "TransferRate")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Memo = field.NewString(table, "Memo")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.State = field.NewInt32(table, "State")
	x.NetFee = field.NewFloat64(table, "NetFee")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.AdJustState = field.NewInt32(table, "AdJustState")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.PayType = field.NewInt32(table, "PayType")
	x.PayID = field.NewInt32(table, "PayId")
	x.PayData = field.NewString(table, "PayData")
	x.PayTime = field.NewTime(table, "PayTime")
	x.IsFirst = field.NewInt32(table, "IsFirst")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.DataState = field.NewInt32(table, "DataState")
	x.IsTgCounted = field.NewInt32(table, "IsTgCounted")
	x.MaidianState = field.NewInt32(table, "MaidianState")

	x.fillFieldMap()

	return x
}

func (x *xRecharge) WithContext(ctx context.Context) *xRechargeDo {
	return x.xRechargeDo.WithContext(ctx)
}

func (x xRecharge) TableName() string { return x.xRechargeDo.TableName() }

func (x xRecharge) Alias() string { return x.xRechargeDo.Alias() }

func (x xRecharge) Columns(cols ...field.Expr) gen.Columns { return x.xRechargeDo.Columns(cols...) }

func (x *xRecharge) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRecharge) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 32)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["OrderId"] = x.OrderID
	x.fieldMap["OrderType"] = x.OrderType
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["Net"] = x.Net
	x.fieldMap["FromAddress"] = x.FromAddress
	x.fieldMap["ToAddress"] = x.ToAddress
	x.fieldMap["TxId"] = x.TxID
	x.fieldMap["RealAmount"] = x.RealAmount
	x.fieldMap["TransferRate"] = x.TransferRate
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["State"] = x.State
	x.fieldMap["NetFee"] = x.NetFee
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["AdJustState"] = x.AdJustState
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["PayType"] = x.PayType
	x.fieldMap["PayId"] = x.PayID
	x.fieldMap["PayData"] = x.PayData
	x.fieldMap["PayTime"] = x.PayTime
	x.fieldMap["IsFirst"] = x.IsFirst
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["IsTgCounted"] = x.IsTgCounted
	x.fieldMap["MaidianState"] = x.MaidianState
}

func (x xRecharge) clone(db *gorm.DB) xRecharge {
	x.xRechargeDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRecharge) replaceDB(db *gorm.DB) xRecharge {
	x.xRechargeDo.ReplaceDB(db)
	return x
}

type xRechargeDo struct{ gen.DO }

func (x xRechargeDo) Debug() *xRechargeDo {
	return x.withDO(x.DO.Debug())
}

func (x xRechargeDo) WithContext(ctx context.Context) *xRechargeDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRechargeDo) ReadDB() *xRechargeDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRechargeDo) WriteDB() *xRechargeDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRechargeDo) Session(config *gorm.Session) *xRechargeDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRechargeDo) Clauses(conds ...clause.Expression) *xRechargeDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRechargeDo) Returning(value interface{}, columns ...string) *xRechargeDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRechargeDo) Not(conds ...gen.Condition) *xRechargeDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRechargeDo) Or(conds ...gen.Condition) *xRechargeDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRechargeDo) Select(conds ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRechargeDo) Where(conds ...gen.Condition) *xRechargeDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRechargeDo) Order(conds ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRechargeDo) Distinct(cols ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRechargeDo) Omit(cols ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRechargeDo) Join(table schema.Tabler, on ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRechargeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRechargeDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRechargeDo) Group(cols ...field.Expr) *xRechargeDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRechargeDo) Having(conds ...gen.Condition) *xRechargeDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRechargeDo) Limit(limit int) *xRechargeDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRechargeDo) Offset(offset int) *xRechargeDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRechargeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRechargeDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRechargeDo) Unscoped() *xRechargeDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRechargeDo) Create(values ...*model.XRecharge) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRechargeDo) CreateInBatches(values []*model.XRecharge, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRechargeDo) Save(values ...*model.XRecharge) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRechargeDo) First() (*model.XRecharge, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRecharge), nil
	}
}

func (x xRechargeDo) Take() (*model.XRecharge, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRecharge), nil
	}
}

func (x xRechargeDo) Last() (*model.XRecharge, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRecharge), nil
	}
}

func (x xRechargeDo) Find() ([]*model.XRecharge, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRecharge), err
}

func (x xRechargeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRecharge, err error) {
	buf := make([]*model.XRecharge, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRechargeDo) FindInBatches(result *[]*model.XRecharge, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRechargeDo) Attrs(attrs ...field.AssignExpr) *xRechargeDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRechargeDo) Assign(attrs ...field.AssignExpr) *xRechargeDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRechargeDo) Joins(fields ...field.RelationField) *xRechargeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRechargeDo) Preload(fields ...field.RelationField) *xRechargeDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRechargeDo) FirstOrInit() (*model.XRecharge, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRecharge), nil
	}
}

func (x xRechargeDo) FirstOrCreate() (*model.XRecharge, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRecharge), nil
	}
}

func (x xRechargeDo) FindByPage(offset int, limit int) (result []*model.XRecharge, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRechargeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRechargeDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRechargeDo) Delete(models ...*model.XRecharge) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRechargeDo) withDO(do gen.Dao) *xRechargeDo {
	x.DO = *do.(*gen.DO)
	return x
}
