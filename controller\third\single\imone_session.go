package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"xserver/server"

	"github.com/beego/beego/logs"
)

// SessionData 会话数据结构
type SessionData struct {
	UserId        int    `json:"user_id"`
	PlayerId      string `json:"player_id"`
	GameId        string `json:"game_id"`
	CreatedAt     string `json:"created_at"`
	ExpiresAt     string `json:"expires_at"`
	ProductWallet string `json:"product_wallet"`
}

// GenerateSessionToken 生成会话令牌（简化实现：直接返回空，实际使用游戏URL中的token）
func (l *IMOneSingleService) GenerateSessionToken(userId int, playerId, gameId, productWallet string, ttl int) (string, error) {
	// 简化实现：不生成sessionToken，直接使用游戏URL中的token
	return "", nil
}

// ValidateSessionToken 验证会话令牌（简化实现：直接从token中获取会话数据）
func (l *IMOneSingleService) ValidateSessionToken(sessionToken, playerId, gameId string) (*SessionData, error) {
	if sessionToken == "" {
		return nil, errors.New("session token is empty")
	}

	// 简化实现：直接从游戏token中获取会话数据
	tokenKey := fmt.Sprintf("%stoken:%s", cacheKeyIMOne, sessionToken)
	data := server.Redis().Get(tokenKey)
	if data == nil {
		logs.Error("IMOne ValidateSessionToken 会话不存在或已过期 sessionToken=", sessionToken)
		return nil, errors.New("session not found or expired")
	}

	// 反序列化会话数据
	var sessionData SessionData
	err := json.Unmarshal(data.([]byte), &sessionData)
	if err != nil {
		logs.Error("IMOne ValidateSessionToken 反序列化会话数据失败 sessionToken=", sessionToken, " err=", err.Error())
		return nil, errors.New("invalid session data")
	}

	// 验证玩家ID匹配
	if playerId != "" && sessionData.PlayerId != playerId {
		logs.Error("IMOne ValidateSessionToken 玩家ID不匹配 sessionToken=", sessionToken, " expected=", sessionData.PlayerId, " actual=", playerId)
		return nil, errors.New("player id mismatch")
	}

	return &sessionData, nil
}

// RefreshSessionToken 刷新会话令牌（简化实现：直接更新token过期时间）
func (l *IMOneSingleService) RefreshSessionToken(sessionToken string, ttl int) error {
	// 简化实现：直接更新token的过期时间
	tokenKey := fmt.Sprintf("%stoken:%s", cacheKeyIMOne, sessionToken)

	// 检查会话是否存在
	data := server.Redis().Get(tokenKey)
	if data == nil {
		return errors.New("session not found")
	}

	// 直接更新过期时间
	err := server.Redis().Expire(tokenKey, ttl)
	if err != nil {
		logs.Error("IMOne RefreshSessionToken 刷新会话失败 sessionToken=", sessionToken, " err=", err.Error())
		return err
	}

	logs.Info("IMOne RefreshSessionToken 刷新会话成功 sessionToken=", sessionToken, " ttl=", ttl)
	return nil
}

// CleanupSession 清理会话
func (l *IMOneSingleService) CleanupSession(sessionToken, playerId, gameId string) {
	// 删除会话数据
	sessionKey := fmt.Sprintf("%ssession:%s", cacheKeyIMOne, sessionToken)
	server.Redis().Del(sessionKey)

	// 删除玩家游戏会话映射
	playerSessionKey := fmt.Sprintf("%splayer_session:%s:%s", cacheKeyIMOne, playerId, gameId)
	server.Redis().Del(playerSessionKey)

	if l.Debug {
		logs.Info("IMOne CleanupSession 清理会话成功 sessionToken=", sessionToken, " playerId=", playerId, " gameId=", gameId)
	}
}

// InvalidatePlayerSessions 清理玩家的所有会话
func (l *IMOneSingleService) InvalidatePlayerSessions(playerId string) {
	// 这里可以实现清理特定玩家的所有会话的逻辑
	// 由于Redis键的设计，可以通过模式匹配来查找和删除
	pattern := fmt.Sprintf("%splayer_session:%s:*", cacheKeyIMOne, playerId)

	// 注意：在生产环境中，应该避免使用KEYS命令，可以考虑使用SCAN命令
	// 这里简化实现
	if l.Debug {
		logs.Info("IMOne InvalidatePlayerSessions 清理玩家所有会话 playerId=", playerId, " pattern=", pattern)
	}
}

// GetSessionByPlayer 根据玩家和游戏获取会话令牌
func (l *IMOneSingleService) GetSessionByPlayer(playerId, gameId string) (string, error) {
	playerSessionKey := fmt.Sprintf("%splayer_session:%s:%s", cacheKeyIMOne, playerId, gameId)

	data := server.Redis().Get(playerSessionKey)
	if data == nil {
		return "", errors.New("player session not found")
	}

	sessionToken := string(data.([]byte))

	// 验证会话是否仍然有效
	_, err := l.ValidateSessionToken(sessionToken, playerId, gameId)
	if err != nil {
		// 会话无效，清理
		l.CleanupSession(sessionToken, playerId, gameId)
		return "", err
	}

	return sessionToken, nil
}

// findPlayerActiveSession 查找玩家的任何有效会话（简化实现：总是返回nil）
func (l *IMOneSingleService) findPlayerActiveSession(playerId string) *SessionData {
	// 简化实现：由于直接使用游戏token，不需要备用查找机制
	logs.Warn("IMOne findPlayerActiveSession 简化实现，未找到会话 playerId=", playerId)
	return nil
}

// createGameTokenMapping 创建游戏URL中token与会话数据的映射（简化实现：直接使用游戏token作为会话token）
func (l *IMOneSingleService) createGameTokenMapping(gameURL, ourSessionToken string, userId int, gameId string) {
	// 解析游戏URL中的token参数
	gameToken := ourSessionToken
	if gameToken == "" {
		logs.Warn("IMOne createGameTokenMapping 无法从游戏URL中提取token gameURL=", gameURL)
		return
	}

	// 简化实现：直接将游戏token作为sessionToken，保存会话数据--imone_
	playerId := fmt.Sprintf("%d", userId)
	sessionData := SessionData{
		UserId:        userId,
		PlayerId:      playerId,
		GameId:        gameId,
		CreatedAt:     time.Now().Format("2006-01-02 15:04:05"),
		ExpiresAt:     time.Now().Add(24 * time.Hour).Format("2006-01-02 15:04:05"),
		ProductWallet: "",
	}

	sessionDataBytes, _ := json.Marshal(sessionData)
	tokenKey := fmt.Sprintf("%stoken:%s", cacheKeyIMOne, gameToken)

	// 保存会话数据， 过期时间24小时
	err := server.Redis().SetStringEx(tokenKey, 86400, string(sessionDataBytes))
	if err != nil {
		logs.Error("IMOne createGameTokenMapping 保存会话数据失败 gameToken=", gameToken, " err=", err.Error())
		return
	}

	logs.Info("IMOne createGameTokenMapping 创建会话成功 gameToken=", gameToken, " userId=", userId, " gameId=", gameId)
}

// extractTokenFromGameURL 从游戏URL中提取token参数
func (l *IMOneSingleService) extractTokenFromGameURL(gameURL string) string {
	// 解析URL参数
	if idx := strings.Index(gameURL, "token="); idx != -1 {
		tokenStart := idx + 6 // "token="的长度
		tokenEnd := strings.Index(gameURL[tokenStart:], "&")
		if tokenEnd == -1 {
			// token是最后一个参数
			return gameURL[tokenStart:]
		} else {
			return gameURL[tokenStart : tokenStart+tokenEnd]
		}
	}
	return ""
}
