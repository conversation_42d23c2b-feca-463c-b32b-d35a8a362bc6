package convertApi

type EventName string

const (
	EventName_CompleteRegistration = "CompleteRegistration" // 完成注册
	EventName_Purchase             = "Purchase"             // 完成购买或结账流程
	EventName_AddToCart            = "AddToCart"            // 添加到购物车
	EventName_FirstRecharge        = "首次购物"                 // 首次充值
)

type EventReq struct {
	Data          []Data `json:"data"`
	TestEventCode string `json:"test_event_code,omitempty"`
}

// Data 服务器事件参数 docs: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/server-event
type Data struct {
	EventName    string      `json:"event_name"`
	EventTime    int64       `json:"event_time"`
	ActionSource string      `json:"action_source"`
	UserData     UserData    `json:"user_data"`
	EventId      string      `json:"event_id,omitempty"`
	CustomData   *CustomData `json:"custom_data,omitempty"`
}

// UserData 客户信息参数 docs: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/customer-information-parameters
type UserData struct {
	Email           []string `json:"em,omitempty"`
	Phone           []string `json:"ph,omitempty"`
	ClientIpAddress string   `json:"client_ip_address,omitempty"`
	ClientUserAgent string   `json:"client_user_agent,omitempty"`
	FBC             string   `json:"fbc"` // 点击编号
	FBP             string   `json:"fbp"` // 浏览器编号
	ExternalId      string   `json:"external_id"`
}

// CustomData 自定义数据 docs: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/custom-data
type CustomData struct {
	Currency string     `json:"currency"`
	Value    float64    `json:"value"`
	Contents []Contents `json:"contents,omitempty"`
}

type Contents struct {
	Id       string `json:"id"`
	Quantity int    `json:"quantity"`
}

type EventRsp struct {
	Error          *Error   `json:"error,omitempty"`
	EventsReceived int      `json:"events_received,omitempty"`
	Messages       []string `json:"messages,omitempty"`
	FbtraceId      string   `json:"fbtrace_id,omitempty"`
}

type Error struct {
	Message        string `json:"message"`
	Type           string `json:"type"`
	Code           int    `json:"code"`
	ErrorSubcode   int    `json:"error_subcode"`
	IsTransient    bool   `json:"is_transient"`
	ErrorUserTitle string `json:"error_user_title"`
	ErrorUserMsg   string `json:"error_user_msg"`
	FbtraceId      string `json:"fbtrace_id"`
}
