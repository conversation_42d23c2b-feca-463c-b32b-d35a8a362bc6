// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAmountChangeLog(db *gorm.DB, opts ...gen.DOOption) xAmountChangeLog {
	_xAmountChangeLog := xAmountChangeLog{}

	_xAmountChangeLog.xAmountChangeLogDo.UseDB(db, opts...)
	_xAmountChangeLog.xAmountChangeLogDo.UseModel(&model.XAmountChangeLog{})

	tableName := _xAmountChangeLog.xAmountChangeLogDo.TableName()
	_xAmountChangeLog.ALL = field.NewAsterisk(tableName)
	_xAmountChangeLog.ID = field.NewInt32(tableName, "Id")
	_xAmountChangeLog.UserID = field.NewInt32(tableName, "UserId")
	_xAmountChangeLog.AmountType = field.NewInt32(tableName, "AmountType")
	_xAmountChangeLog.BeforeAmount = field.NewFloat64(tableName, "BeforeAmount")
	_xAmountChangeLog.Amount = field.NewFloat64(tableName, "Amount")
	_xAmountChangeLog.AfterAmount = field.NewFloat64(tableName, "AfterAmount")
	_xAmountChangeLog.Reason = field.NewInt32(tableName, "Reason")
	_xAmountChangeLog.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAmountChangeLog.Memo = field.NewString(tableName, "Memo")
	_xAmountChangeLog.SellerID = field.NewInt32(tableName, "SellerId")
	_xAmountChangeLog.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAmountChangeLog.BeforeBonusAmount = field.NewFloat64(tableName, "BeforeBonusAmount")
	_xAmountChangeLog.BonusAmount = field.NewFloat64(tableName, "BonusAmount")
	_xAmountChangeLog.AfterBonusAmount = field.NewFloat64(tableName, "AfterBonusAmount")

	_xAmountChangeLog.fillFieldMap()

	return _xAmountChangeLog
}

type xAmountChangeLog struct {
	xAmountChangeLogDo xAmountChangeLogDo

	ALL          field.Asterisk
	ID           field.Int32
	UserID       field.Int32   // 玩家
	AmountType   field.Int32   // 余额分类 1=真金，2=Bonus币，3=混合
	BeforeAmount field.Float64 // 变化前
	Amount       field.Float64 // 变化值
	AfterAmount  field.Float64 // 变化后
	/*
		变化原因
		1充值
		2提款
		3活动送金
		4佣金领取
		5后台增资
		6提款拒绝退款
		7彩票转出
		8彩票转入
		9彩票下注
		10电子转出
		11电子转入
		12棋牌转出
		13棋牌转入
		14小游戏转出
		15小游戏转入
		16彩票返奖
		17vip返水
		18vip升级礼金
		19vip每月礼金
		20能量补给站投注反水新
		21evo下注
		22evo取消投注
		23evo结算
		24evo重新结算
		25充值活动新
		26哈希闯关活动新
		27棋牌闯关活动新
		28电子闯关活动新
		29救援金活动新
		30邀请好友活动新
		31降龙伏虎活动新
	*/
	Reason            field.Int32
	CreateTime        field.Time // 变化时间
	Memo              field.String
	SellerID          field.Int32
	ChannelID         field.Int32
	BeforeBonusAmount field.Float64 // bonus变化前
	BonusAmount       field.Float64 // bonus变化值
	AfterBonusAmount  field.Float64 // bonus变化后

	fieldMap map[string]field.Expr
}

func (x xAmountChangeLog) Table(newTableName string) *xAmountChangeLog {
	x.xAmountChangeLogDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAmountChangeLog) As(alias string) *xAmountChangeLog {
	x.xAmountChangeLogDo.DO = *(x.xAmountChangeLogDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAmountChangeLog) updateTableName(table string) *xAmountChangeLog {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.AmountType = field.NewInt32(table, "AmountType")
	x.BeforeAmount = field.NewFloat64(table, "BeforeAmount")
	x.Amount = field.NewFloat64(table, "Amount")
	x.AfterAmount = field.NewFloat64(table, "AfterAmount")
	x.Reason = field.NewInt32(table, "Reason")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Memo = field.NewString(table, "Memo")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BeforeBonusAmount = field.NewFloat64(table, "BeforeBonusAmount")
	x.BonusAmount = field.NewFloat64(table, "BonusAmount")
	x.AfterBonusAmount = field.NewFloat64(table, "AfterBonusAmount")

	x.fillFieldMap()

	return x
}

func (x *xAmountChangeLog) WithContext(ctx context.Context) *xAmountChangeLogDo {
	return x.xAmountChangeLogDo.WithContext(ctx)
}

func (x xAmountChangeLog) TableName() string { return x.xAmountChangeLogDo.TableName() }

func (x xAmountChangeLog) Alias() string { return x.xAmountChangeLogDo.Alias() }

func (x xAmountChangeLog) Columns(cols ...field.Expr) gen.Columns {
	return x.xAmountChangeLogDo.Columns(cols...)
}

func (x *xAmountChangeLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAmountChangeLog) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 14)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["AmountType"] = x.AmountType
	x.fieldMap["BeforeAmount"] = x.BeforeAmount
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["AfterAmount"] = x.AfterAmount
	x.fieldMap["Reason"] = x.Reason
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BeforeBonusAmount"] = x.BeforeBonusAmount
	x.fieldMap["BonusAmount"] = x.BonusAmount
	x.fieldMap["AfterBonusAmount"] = x.AfterBonusAmount
}

func (x xAmountChangeLog) clone(db *gorm.DB) xAmountChangeLog {
	x.xAmountChangeLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAmountChangeLog) replaceDB(db *gorm.DB) xAmountChangeLog {
	x.xAmountChangeLogDo.ReplaceDB(db)
	return x
}

type xAmountChangeLogDo struct{ gen.DO }

func (x xAmountChangeLogDo) Debug() *xAmountChangeLogDo {
	return x.withDO(x.DO.Debug())
}

func (x xAmountChangeLogDo) WithContext(ctx context.Context) *xAmountChangeLogDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAmountChangeLogDo) ReadDB() *xAmountChangeLogDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAmountChangeLogDo) WriteDB() *xAmountChangeLogDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAmountChangeLogDo) Session(config *gorm.Session) *xAmountChangeLogDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAmountChangeLogDo) Clauses(conds ...clause.Expression) *xAmountChangeLogDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAmountChangeLogDo) Returning(value interface{}, columns ...string) *xAmountChangeLogDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAmountChangeLogDo) Not(conds ...gen.Condition) *xAmountChangeLogDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAmountChangeLogDo) Or(conds ...gen.Condition) *xAmountChangeLogDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAmountChangeLogDo) Select(conds ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAmountChangeLogDo) Where(conds ...gen.Condition) *xAmountChangeLogDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAmountChangeLogDo) Order(conds ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAmountChangeLogDo) Distinct(cols ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAmountChangeLogDo) Omit(cols ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAmountChangeLogDo) Join(table schema.Tabler, on ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAmountChangeLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAmountChangeLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAmountChangeLogDo) Group(cols ...field.Expr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAmountChangeLogDo) Having(conds ...gen.Condition) *xAmountChangeLogDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAmountChangeLogDo) Limit(limit int) *xAmountChangeLogDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAmountChangeLogDo) Offset(offset int) *xAmountChangeLogDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAmountChangeLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAmountChangeLogDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAmountChangeLogDo) Unscoped() *xAmountChangeLogDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAmountChangeLogDo) Create(values ...*model.XAmountChangeLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAmountChangeLogDo) CreateInBatches(values []*model.XAmountChangeLog, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAmountChangeLogDo) Save(values ...*model.XAmountChangeLog) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAmountChangeLogDo) First() (*model.XAmountChangeLog, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAmountChangeLog), nil
	}
}

func (x xAmountChangeLogDo) Take() (*model.XAmountChangeLog, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAmountChangeLog), nil
	}
}

func (x xAmountChangeLogDo) Last() (*model.XAmountChangeLog, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAmountChangeLog), nil
	}
}

func (x xAmountChangeLogDo) Find() ([]*model.XAmountChangeLog, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAmountChangeLog), err
}

func (x xAmountChangeLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAmountChangeLog, err error) {
	buf := make([]*model.XAmountChangeLog, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAmountChangeLogDo) FindInBatches(result *[]*model.XAmountChangeLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAmountChangeLogDo) Attrs(attrs ...field.AssignExpr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAmountChangeLogDo) Assign(attrs ...field.AssignExpr) *xAmountChangeLogDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAmountChangeLogDo) Joins(fields ...field.RelationField) *xAmountChangeLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAmountChangeLogDo) Preload(fields ...field.RelationField) *xAmountChangeLogDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAmountChangeLogDo) FirstOrInit() (*model.XAmountChangeLog, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAmountChangeLog), nil
	}
}

func (x xAmountChangeLogDo) FirstOrCreate() (*model.XAmountChangeLog, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAmountChangeLog), nil
	}
}

func (x xAmountChangeLogDo) FindByPage(offset int, limit int) (result []*model.XAmountChangeLog, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAmountChangeLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAmountChangeLogDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAmountChangeLogDo) Delete(models ...*model.XAmountChangeLog) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAmountChangeLogDo) withDO(do gen.Dao) *xAmountChangeLogDo {
	x.DO = *do.(*gen.DO)
	return x
}
