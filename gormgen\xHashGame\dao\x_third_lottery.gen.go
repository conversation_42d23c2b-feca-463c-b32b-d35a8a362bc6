// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdLottery(db *gorm.DB, opts ...gen.DOOption) xThirdLottery {
	_xThirdLottery := xThirdLottery{}

	_xThirdLottery.xThirdLotteryDo.UseDB(db, opts...)
	_xThirdLottery.xThirdLotteryDo.UseModel(&model.XThirdLottery{})

	tableName := _xThirdLottery.xThirdLotteryDo.TableName()
	_xThirdLottery.ALL = field.NewAsterisk(tableName)
	_xThirdLottery.ID = field.NewInt64(tableName, "Id")
	_xThirdLottery.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdLottery.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdLottery.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdLottery.UserID = field.NewInt32(tableName, "UserId")
	_xThirdLottery.Brand = field.NewString(tableName, "Brand")
	_xThirdLottery.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdLottery.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdLottery.GameID = field.NewString(tableName, "GameId")
	_xThirdLottery.GameName = field.NewString(tableName, "GameName")
	_xThirdLottery.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdLottery.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdLottery.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdLottery.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdLottery.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdLottery.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdLottery.BetTime = field.NewTime(tableName, "BetTime")
	_xThirdLottery.Currency = field.NewString(tableName, "Currency")
	_xThirdLottery.RawData = field.NewString(tableName, "RawData")
	_xThirdLottery.State = field.NewInt32(tableName, "State")
	_xThirdLottery.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdLottery.DataState = field.NewInt32(tableName, "DataState")
	_xThirdLottery.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdLottery.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdLottery.CSID = field.NewString(tableName, "CSId")
	_xThirdLottery.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdLottery.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdLottery.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdLottery.GameRst = field.NewString(tableName, "GameRst")
	_xThirdLottery.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdLottery.IP = field.NewString(tableName, "Ip")
	_xThirdLottery.Lang = field.NewString(tableName, "Lang")
	_xThirdLottery.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdLottery.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdLottery.CatID = field.NewInt32(tableName, "CatId")
	_xThirdLottery.SubCatID = field.NewInt32(tableName, "SubCatId")
	_xThirdLottery.BetType = field.NewInt32(tableName, "BetType")
	_xThirdLottery.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdLottery.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdLottery.fillFieldMap()

	return _xThirdLottery
}

type xThirdLottery struct {
	xThirdLotteryDo xThirdLotteryDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	ThirdRefID     field.String  // 三方备用注单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	BetTime        field.Time    // 下注时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	CatID          field.Int32   // 六合彩大类ID
	SubCatID       field.Int32   // 六合彩小类ID
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdLottery) Table(newTableName string) *xThirdLottery {
	x.xThirdLotteryDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdLottery) As(alias string) *xThirdLottery {
	x.xThirdLotteryDo.DO = *(x.xThirdLotteryDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdLottery) updateTableName(table string) *xThirdLottery {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.BetTime = field.NewTime(table, "BetTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.CatID = field.NewInt32(table, "CatId")
	x.SubCatID = field.NewInt32(table, "SubCatId")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdLottery) WithContext(ctx context.Context) *xThirdLotteryDo {
	return x.xThirdLotteryDo.WithContext(ctx)
}

func (x xThirdLottery) TableName() string { return x.xThirdLotteryDo.TableName() }

func (x xThirdLottery) Alias() string { return x.xThirdLotteryDo.Alias() }

func (x xThirdLottery) Columns(cols ...field.Expr) gen.Columns {
	return x.xThirdLotteryDo.Columns(cols...)
}

func (x *xThirdLottery) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdLottery) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 39)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["BetTime"] = x.BetTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["CatId"] = x.CatID
	x.fieldMap["SubCatId"] = x.SubCatID
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdLottery) clone(db *gorm.DB) xThirdLottery {
	x.xThirdLotteryDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdLottery) replaceDB(db *gorm.DB) xThirdLottery {
	x.xThirdLotteryDo.ReplaceDB(db)
	return x
}

type xThirdLotteryDo struct{ gen.DO }

func (x xThirdLotteryDo) Debug() *xThirdLotteryDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdLotteryDo) WithContext(ctx context.Context) *xThirdLotteryDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdLotteryDo) ReadDB() *xThirdLotteryDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdLotteryDo) WriteDB() *xThirdLotteryDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdLotteryDo) Session(config *gorm.Session) *xThirdLotteryDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdLotteryDo) Clauses(conds ...clause.Expression) *xThirdLotteryDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdLotteryDo) Returning(value interface{}, columns ...string) *xThirdLotteryDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdLotteryDo) Not(conds ...gen.Condition) *xThirdLotteryDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdLotteryDo) Or(conds ...gen.Condition) *xThirdLotteryDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdLotteryDo) Select(conds ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdLotteryDo) Where(conds ...gen.Condition) *xThirdLotteryDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdLotteryDo) Order(conds ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdLotteryDo) Distinct(cols ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdLotteryDo) Omit(cols ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdLotteryDo) Join(table schema.Tabler, on ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdLotteryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdLotteryDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdLotteryDo) Group(cols ...field.Expr) *xThirdLotteryDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdLotteryDo) Having(conds ...gen.Condition) *xThirdLotteryDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdLotteryDo) Limit(limit int) *xThirdLotteryDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdLotteryDo) Offset(offset int) *xThirdLotteryDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdLotteryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdLotteryDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdLotteryDo) Unscoped() *xThirdLotteryDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdLotteryDo) Create(values ...*model.XThirdLottery) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdLotteryDo) CreateInBatches(values []*model.XThirdLottery, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdLotteryDo) Save(values ...*model.XThirdLottery) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdLotteryDo) First() (*model.XThirdLottery, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLottery), nil
	}
}

func (x xThirdLotteryDo) Take() (*model.XThirdLottery, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLottery), nil
	}
}

func (x xThirdLotteryDo) Last() (*model.XThirdLottery, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLottery), nil
	}
}

func (x xThirdLotteryDo) Find() ([]*model.XThirdLottery, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdLottery), err
}

func (x xThirdLotteryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdLottery, err error) {
	buf := make([]*model.XThirdLottery, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdLotteryDo) FindInBatches(result *[]*model.XThirdLottery, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdLotteryDo) Attrs(attrs ...field.AssignExpr) *xThirdLotteryDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdLotteryDo) Assign(attrs ...field.AssignExpr) *xThirdLotteryDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdLotteryDo) Joins(fields ...field.RelationField) *xThirdLotteryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdLotteryDo) Preload(fields ...field.RelationField) *xThirdLotteryDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdLotteryDo) FirstOrInit() (*model.XThirdLottery, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLottery), nil
	}
}

func (x xThirdLotteryDo) FirstOrCreate() (*model.XThirdLottery, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLottery), nil
	}
}

func (x xThirdLotteryDo) FindByPage(offset int, limit int) (result []*model.XThirdLottery, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdLotteryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdLotteryDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdLotteryDo) Delete(models ...*model.XThirdLottery) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdLotteryDo) withDO(do gen.Dao) *xThirdLotteryDo {
	x.DO = *do.(*gen.DO)
	return x
}
