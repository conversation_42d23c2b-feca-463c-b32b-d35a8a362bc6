// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotPushActivityRecord(db *gorm.DB, opts ...gen.DOOption) xRobotPushActivityRecord {
	_xRobotPushActivityRecord := xRobotPushActivityRecord{}

	_xRobotPushActivityRecord.xRobotPushActivityRecordDo.UseDB(db, opts...)
	_xRobotPushActivityRecord.xRobotPushActivityRecordDo.UseModel(&model.XRobotPushActivityRecord{})

	tableName := _xRobotPushActivityRecord.xRobotPushActivityRecordDo.TableName()
	_xRobotPushActivityRecord.ALL = field.NewAsterisk(tableName)
	_xRobotPushActivityRecord.ID = field.NewInt64(tableName, "id")
	_xRobotPushActivityRecord.RecordTime = field.NewTime(tableName, "record_time")
	_xRobotPushActivityRecord.SellerID = field.NewInt32(tableName, "seller_id")
	_xRobotPushActivityRecord.ChannelID = field.NewInt32(tableName, "channel_id")
	_xRobotPushActivityRecord.Name = field.NewString(tableName, "name")
	_xRobotPushActivityRecord.UserID = field.NewInt64(tableName, "user_id")
	_xRobotPushActivityRecord.UserChatID = field.NewInt64(tableName, "user_chat_id")
	_xRobotPushActivityRecord.UserName = field.NewString(tableName, "user_name")
	_xRobotPushActivityRecord.PushMsgID = field.NewInt32(tableName, "push_msg_id")
	_xRobotPushActivityRecord.PushMsg = field.NewString(tableName, "push_msg")
	_xRobotPushActivityRecord.ClickCnt = field.NewInt32(tableName, "click_cnt")
	_xRobotPushActivityRecord.IsGift = field.NewInt32(tableName, "is_gift")
	_xRobotPushActivityRecord.RechargeCnt = field.NewInt32(tableName, "recharge_cnt")
	_xRobotPushActivityRecord.FirstTimeRechargeAmount = field.NewFloat64(tableName, "first_time_recharge_amount")
	_xRobotPushActivityRecord.RechargeAmount1 = field.NewFloat64(tableName, "recharge_amount_1")
	_xRobotPushActivityRecord.RechargeAmount2 = field.NewFloat64(tableName, "recharge_amount_2")
	_xRobotPushActivityRecord.RechargeAmount3 = field.NewFloat64(tableName, "recharge_amount_3")
	_xRobotPushActivityRecord.RechargeAmountAll = field.NewFloat64(tableName, "recharge_amount_all")
	_xRobotPushActivityRecord.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotPushActivityRecord.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotPushActivityRecord.fillFieldMap()

	return _xRobotPushActivityRecord
}

type xRobotPushActivityRecord struct {
	xRobotPushActivityRecordDo xRobotPushActivityRecordDo

	ALL                     field.Asterisk
	ID                      field.Int64   // pk
	RecordTime              field.Time    // 日期
	SellerID                field.Int32   // 经销商ID
	ChannelID               field.Int32   // 渠道ID
	Name                    field.String  // 机器人名
	UserID                  field.Int64   // 用户ID
	UserChatID              field.Int64   // 玩家飞机号
	UserName                field.String  // 玩家飞机名
	PushMsgID               field.Int32   // 推送的消息Id
	PushMsg                 field.String  // 推送消息名
	ClickCnt                field.Int32   // 点击次数
	IsGift                  field.Int32   // 是否领取彩金
	RechargeCnt             field.Int32   // 是否充值
	FirstTimeRechargeAmount field.Float64 // 首次
	RechargeAmount1         field.Float64 // 第1天充值金额
	RechargeAmount2         field.Float64 // 第2天充值金额
	RechargeAmount3         field.Float64 // 第3天充值金额
	RechargeAmountAll       field.Float64 // 总充值
	CreateTime              field.Time    // 创建
	UpdateTime              field.Time    // 更新

	fieldMap map[string]field.Expr
}

func (x xRobotPushActivityRecord) Table(newTableName string) *xRobotPushActivityRecord {
	x.xRobotPushActivityRecordDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotPushActivityRecord) As(alias string) *xRobotPushActivityRecord {
	x.xRobotPushActivityRecordDo.DO = *(x.xRobotPushActivityRecordDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotPushActivityRecord) updateTableName(table string) *xRobotPushActivityRecord {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.RecordTime = field.NewTime(table, "record_time")
	x.SellerID = field.NewInt32(table, "seller_id")
	x.ChannelID = field.NewInt32(table, "channel_id")
	x.Name = field.NewString(table, "name")
	x.UserID = field.NewInt64(table, "user_id")
	x.UserChatID = field.NewInt64(table, "user_chat_id")
	x.UserName = field.NewString(table, "user_name")
	x.PushMsgID = field.NewInt32(table, "push_msg_id")
	x.PushMsg = field.NewString(table, "push_msg")
	x.ClickCnt = field.NewInt32(table, "click_cnt")
	x.IsGift = field.NewInt32(table, "is_gift")
	x.RechargeCnt = field.NewInt32(table, "recharge_cnt")
	x.FirstTimeRechargeAmount = field.NewFloat64(table, "first_time_recharge_amount")
	x.RechargeAmount1 = field.NewFloat64(table, "recharge_amount_1")
	x.RechargeAmount2 = field.NewFloat64(table, "recharge_amount_2")
	x.RechargeAmount3 = field.NewFloat64(table, "recharge_amount_3")
	x.RechargeAmountAll = field.NewFloat64(table, "recharge_amount_all")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotPushActivityRecord) WithContext(ctx context.Context) *xRobotPushActivityRecordDo {
	return x.xRobotPushActivityRecordDo.WithContext(ctx)
}

func (x xRobotPushActivityRecord) TableName() string { return x.xRobotPushActivityRecordDo.TableName() }

func (x xRobotPushActivityRecord) Alias() string { return x.xRobotPushActivityRecordDo.Alias() }

func (x xRobotPushActivityRecord) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotPushActivityRecordDo.Columns(cols...)
}

func (x *xRobotPushActivityRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotPushActivityRecord) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 20)
	x.fieldMap["id"] = x.ID
	x.fieldMap["record_time"] = x.RecordTime
	x.fieldMap["seller_id"] = x.SellerID
	x.fieldMap["channel_id"] = x.ChannelID
	x.fieldMap["name"] = x.Name
	x.fieldMap["user_id"] = x.UserID
	x.fieldMap["user_chat_id"] = x.UserChatID
	x.fieldMap["user_name"] = x.UserName
	x.fieldMap["push_msg_id"] = x.PushMsgID
	x.fieldMap["push_msg"] = x.PushMsg
	x.fieldMap["click_cnt"] = x.ClickCnt
	x.fieldMap["is_gift"] = x.IsGift
	x.fieldMap["recharge_cnt"] = x.RechargeCnt
	x.fieldMap["first_time_recharge_amount"] = x.FirstTimeRechargeAmount
	x.fieldMap["recharge_amount_1"] = x.RechargeAmount1
	x.fieldMap["recharge_amount_2"] = x.RechargeAmount2
	x.fieldMap["recharge_amount_3"] = x.RechargeAmount3
	x.fieldMap["recharge_amount_all"] = x.RechargeAmountAll
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotPushActivityRecord) clone(db *gorm.DB) xRobotPushActivityRecord {
	x.xRobotPushActivityRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotPushActivityRecord) replaceDB(db *gorm.DB) xRobotPushActivityRecord {
	x.xRobotPushActivityRecordDo.ReplaceDB(db)
	return x
}

type xRobotPushActivityRecordDo struct{ gen.DO }

func (x xRobotPushActivityRecordDo) Debug() *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotPushActivityRecordDo) WithContext(ctx context.Context) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotPushActivityRecordDo) ReadDB() *xRobotPushActivityRecordDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotPushActivityRecordDo) WriteDB() *xRobotPushActivityRecordDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotPushActivityRecordDo) Session(config *gorm.Session) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotPushActivityRecordDo) Clauses(conds ...clause.Expression) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotPushActivityRecordDo) Returning(value interface{}, columns ...string) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotPushActivityRecordDo) Not(conds ...gen.Condition) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotPushActivityRecordDo) Or(conds ...gen.Condition) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotPushActivityRecordDo) Select(conds ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotPushActivityRecordDo) Where(conds ...gen.Condition) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotPushActivityRecordDo) Order(conds ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotPushActivityRecordDo) Distinct(cols ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotPushActivityRecordDo) Omit(cols ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotPushActivityRecordDo) Join(table schema.Tabler, on ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotPushActivityRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotPushActivityRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotPushActivityRecordDo) Group(cols ...field.Expr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotPushActivityRecordDo) Having(conds ...gen.Condition) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotPushActivityRecordDo) Limit(limit int) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotPushActivityRecordDo) Offset(offset int) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotPushActivityRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotPushActivityRecordDo) Unscoped() *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotPushActivityRecordDo) Create(values ...*model.XRobotPushActivityRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotPushActivityRecordDo) CreateInBatches(values []*model.XRobotPushActivityRecord, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotPushActivityRecordDo) Save(values ...*model.XRobotPushActivityRecord) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotPushActivityRecordDo) First() (*model.XRobotPushActivityRecord, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushActivityRecord), nil
	}
}

func (x xRobotPushActivityRecordDo) Take() (*model.XRobotPushActivityRecord, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushActivityRecord), nil
	}
}

func (x xRobotPushActivityRecordDo) Last() (*model.XRobotPushActivityRecord, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushActivityRecord), nil
	}
}

func (x xRobotPushActivityRecordDo) Find() ([]*model.XRobotPushActivityRecord, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotPushActivityRecord), err
}

func (x xRobotPushActivityRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotPushActivityRecord, err error) {
	buf := make([]*model.XRobotPushActivityRecord, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotPushActivityRecordDo) FindInBatches(result *[]*model.XRobotPushActivityRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotPushActivityRecordDo) Attrs(attrs ...field.AssignExpr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotPushActivityRecordDo) Assign(attrs ...field.AssignExpr) *xRobotPushActivityRecordDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotPushActivityRecordDo) Joins(fields ...field.RelationField) *xRobotPushActivityRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotPushActivityRecordDo) Preload(fields ...field.RelationField) *xRobotPushActivityRecordDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotPushActivityRecordDo) FirstOrInit() (*model.XRobotPushActivityRecord, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushActivityRecord), nil
	}
}

func (x xRobotPushActivityRecordDo) FirstOrCreate() (*model.XRobotPushActivityRecord, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushActivityRecord), nil
	}
}

func (x xRobotPushActivityRecordDo) FindByPage(offset int, limit int) (result []*model.XRobotPushActivityRecord, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotPushActivityRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotPushActivityRecordDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotPushActivityRecordDo) Delete(models ...*model.XRobotPushActivityRecord) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotPushActivityRecordDo) withDO(do gen.Dao) *xRobotPushActivityRecordDo {
	x.DO = *do.(*gen.DO)
	return x
}
