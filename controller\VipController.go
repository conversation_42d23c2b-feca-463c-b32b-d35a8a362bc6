package controller

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm/clause"
)

type VipController struct {
}

func (c *VipController) Init() {
	//server.Http().Post("/api/vip/vip_info", c.vip_info)
	server.Http().PostByNoAuthMayUserToken("/api/vip/vip_info", c.vip_info)
	server.Http().Post("/api/vip/vip_reward_list", c.vip_reward_list)
	server.Http().Post("/api/vip/weekly_reward", c.vipWeeklyReward)
	server.Http().Post("/api/vip/monthly_reward", c.vipMonthlyReward)

	server.Http().PostByNoAuthMayUserToken("/api/vip/info", c.info)     // 用户等级详情
	server.Http().PostByNoAuthMayUserToken("/api/vip/define", c.define) // 系统等级大概
	server.Http().PostNoAuth("/api/vip/define_info", c.defineInfo)      // 系统等级详情
	server.Http().PostNoAuth("/api/vip/tips", c.tips)                   // vip权益提示
}

type VipDefine struct {
	Level          string
	Name           string
	Rebate         string
	UpgradeRewards string
	WeeklyGifts    WeeklyGifts
	MonthlyGifts   MonthlyGifts
	WithdrawLimit  WithdrawLimit
	IsShow         bool
}

type VipDefineInfo struct {
	Rebate  []Rebate
	Upgrade []Upgrade
}

type Rebate struct {
	Level          int32
	UpgradeRewards float64
	WeeklyRewards  float64
	MonthlyRewards float64
	Rate           string
}

type Upgrade struct {
	Level       int32
	Recharge    int
	LiuShui     int
	KeepLiuShui int
}

type WeeklyGifts struct {
	Rewards      string
	LiushuiNow   float64
	LiushuiTotal float64
	Display      bool
}

type MonthlyGifts struct {
	Rewards      string
	LiushuiNow   float64
	LiushuiTotal float64
	Display      bool
}

type WithdrawLimit struct {
	Times     string
	ShowTips  bool
	FreeTimes string
	ToLLTimes string
	Fee       string
}

func (c *VipController) info(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := struct {
		SellerId int
		Host     string
	}{}
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errCode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if req.Host != "" {
		host = req.Host
	}
	ChannelId, SellerId := server.GetChannel(ctx, host)

	xVipDefine := server.DaoxHashGame().XVipDefine
	xVipInfo := server.DaoxHashGame().XVipInfo

	token := server.GetToken(ctx)
	type Infos struct {
		Level           int32
		CurrentRecharge float64
		NeedRecharge    float64
		CurrentLiuShui  float64
		NeedLiuShui     float64
	}
	var infos Infos
	var vipInfo *model.XVipInfo

	if token != nil {
		vipInfo, _ = xVipInfo.WithContext(nil).Where(xVipInfo.UserID.Eq(int32(token.UserId))).First()
	}

	if vipInfo != nil {
		vipDefine, _ := xVipDefine.WithContext(nil).
			Where(xVipDefine.SellerID.Eq(int32(SellerId))).
			Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
			Where(xVipDefine.State.Eq(1)).Where(xVipDefine.VipLevel.Eq(vipInfo.VipLevel + 1)).First()

		if vipDefine != nil {
			currentRecharge, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", vipInfo.Recharge), 64)
			infos = Infos{
				Level:           vipInfo.VipLevel,
				CurrentRecharge: currentRecharge,
				NeedRecharge:    float64(vipDefine.Recharge),
				CurrentLiuShui:  vipInfo.LiuSui,
				NeedLiuShui:     float64(vipDefine.LiuSui),
			}
		} else {
			infos = Infos{
				Level:           vipInfo.VipLevel,
				CurrentRecharge: vipInfo.Recharge,
				NeedRecharge:    vipInfo.Recharge,
				CurrentLiuShui:  vipInfo.LiuSui,
				NeedLiuShui:     vipInfo.LiuSui,
			}
		}

	} else {
		vipDefine, _ := xVipDefine.WithContext(nil).
			Where(xVipDefine.SellerID.Eq(int32(SellerId))).
			Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
			Where(xVipDefine.State.Eq(1)).
			Order(xVipDefine.VipLevel.Asc()).
			Limit(1).
			First()

		if vipDefine == nil {
			ctx.RespErrString(true, &errCode, "VIP未配置")
			return
		}

		nextLevelVipDefine, _ := xVipDefine.WithContext(nil).
			Where(xVipDefine.SellerID.Eq(int32(SellerId))).
			Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
			Where(xVipDefine.State.Eq(1)).
			Where(xVipDefine.VipLevel.Eq(vipDefine.VipLevel + 1)).
			First()

		infos = Infos{
			Level:           vipDefine.VipLevel,
			CurrentRecharge: 0,
			NeedRecharge:    float64(nextLevelVipDefine.Recharge),
			CurrentLiuShui:  0,
			NeedLiuShui:     float64(nextLevelVipDefine.LiuSui),
		}
	}

	ctx.RespOK(infos)
}

func (c *VipController) define(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := struct {
		SellerId int
		Host     string
	}{}
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errCode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if req.Host != "" {
		host = req.Host
	}
	ChannelId, SellerId := server.GetChannel(ctx, host)

	xVipDefine := server.DaoxHashGame().XVipDefine
	xVipInfo := server.DaoxHashGame().XVipInfo
	xVipDefines, _ := xVipDefine.WithContext(nil).
		Where(xVipDefine.SellerID.Eq(int32(SellerId))).
		Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
		Where(xVipDefine.State.Eq(1)).
		Order(xVipDefine.VipLevel.Asc()).
		Find()

	if len(xVipDefines) == 0 {
		ctx.RespErr(errors.New("VIP未配置"), &errCode)
		return
	}

	var results []VipDefine
	var levels []struct {
		rangeStart int
		rangeEnd   int
		label      string
		name       string
	}

	token := server.GetToken(ctx)

	// 根据VIP等级个数动态生成级别配置
	levels = generateLevelsByVipCount(len(xVipDefines), xVipDefines)

	// 根据区间动态生成 `VipDefine` 并加入结果集
	var vipInfo *model.XVipInfo
	if token != nil {
		vipInfo, _ = xVipInfo.WithContext(nil).Where(xVipInfo.UserID.Eq(int32(token.UserId))).First()
	}
	for _, level := range levels {
		if len(xVipDefines) > level.rangeEnd {
			results = append(results, generateVipDefine(level.label, level.name, level.rangeStart, level.rangeEnd, xVipDefines, token, vipInfo))
		}
	}

	ctx.RespOK(results)
}

func (c *VipController) defineInfo(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := struct {
		SellerId int
		Host     string
		Level    string
		GameType string // hash,crypto,lottery,lowLottery,chess,electronic,xiaoyouxi,live,sport,texas,roulette
	}{}
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errCode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if req.Host != "" {
		host = req.Host
	}
	ChannelId, SellerId := server.GetChannel(ctx, host)

	// 定义区间和级别标签
	var levelStart int
	var levelEnd int
	var results VipDefineInfo
	var rebates []Rebate
	var upgrades []Upgrade
	xVipDefine := server.DaoxHashGame().XVipDefine

	levelStart, levelEnd, err = spliceLevel(req.Level)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	vipDefines, _ := xVipDefine.WithContext(nil).
		Where(xVipDefine.SellerID.Eq(int32(SellerId))).
		Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
		Where(xVipDefine.State.Eq(1)).
		Where(xVipDefine.VipLevel.Between(int32(levelStart), int32(levelEnd))).Find()

	if len(vipDefines) > 0 {
		for _, define := range vipDefines {
			var rate string
			switch req.GameType {
			case "hash":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateHaXi*100)
			case "lottery":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateLottery*100)
			case "lowLottery":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateLowLottery*100)
			case "crypto":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateCryptoMarket*100)
			case "chess":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateQiPai*100)
			case "electronic":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateDianZhi*100)
			case "xiaoyouxi":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateXiaoYouXi*100)
			case "live":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateLive*100)
			case "sport":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateSport*100)
			case "texas":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateTexas*100)
			case "roulette":
				rate = fmt.Sprintf("%.2f%%", define.RewardRateHaXiRoulette*100)
			default:
				rate = "0%"
			}

			rebates = append(rebates, Rebate{
				Level:          define.VipLevel,
				UpgradeRewards: define.UpgradeReward,
				WeeklyRewards:  define.WeeklyReward,
				MonthlyRewards: define.MonthlyReward,
				Rate:           rate,
			})

			upgrades = append(upgrades, Upgrade{
				Level:       define.VipLevel,
				Recharge:    int(define.Recharge),
				LiuShui:     int(define.LiuSui),
				KeepLiuShui: int(define.KeepLiuSui),
			})
		}
	}

	results = VipDefineInfo{
		Rebate:  rebates,
		Upgrade: upgrades,
	}

	ctx.RespOK(results)

}

func (c *VipController) tips(ctx *abugo.AbuHttpContent) {
	errCode := 0
	req := struct {
		SellerId int
		Host     string
		Type     string
		Level    string
	}{}
	err := ctx.RequestData(&req)
	if ctx.RespErr(err, &errCode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if req.Host != "" {
		host = req.Host
	}
	ChannelId, SellerId := server.GetChannel(ctx, host)

	levelStart, levelEnd, err := spliceLevel(req.Level)
	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	xVipDefine := server.DaoxHashGame().XVipDefine
	xWithdrawLimitConfig := server.DaoxHashGame().XWithdrawLimitConfig

	// 查询符合条件的 VIP 定义数据，按 VipLevel 升序排序
	vipDefines, _ := xVipDefine.WithContext(nil).
		Where(xVipDefine.SellerID.Eq(int32(SellerId))).
		Where(xVipDefine.ChannelID.Eq(int32(ChannelId))).
		Where(xVipDefine.State.Eq(1)).
		Where(xVipDefine.VipLevel.Between(int32(levelStart), int32(levelEnd))).
		Order(xVipDefine.VipLevel.Asc()). // 按 VipLevel 升序排序
		Find()

	if len(vipDefines) == 0 {
		ctx.RespOK()
		return
	}

	switch req.Type {
	case "rebate":
		ctx.RespOK(getVipRebate(vipDefines))
		return
	case "upgrade":
		ctx.RespOK(getVipUpgrade(vipDefines))
		return
	case "weekly":
		ctx.RespOK(getVipWeekly(vipDefines))
		return
	case "monthly":
		ctx.RespOK(getVipMonthly(vipDefines))
		return
	case "withdraw":
		// 提现限制
		withdrawLimits, _ := xWithdrawLimitConfig.WithContext(nil).
			Where(xWithdrawLimitConfig.Type.Eq(2)).
			Where(xWithdrawLimitConfig.SellerID.Eq(int32(SellerId))).
			Where(xWithdrawLimitConfig.ChannelID.Eq(int32(ChannelId))).
			Where(xWithdrawLimitConfig.VipLevel.Between(int32(levelStart), int32(levelEnd))).
			Order(xWithdrawLimitConfig.VipLevel.Asc()).
			Find()
		ctx.RespOK(getVipWithdrawLimit(vipDefines, withdrawLimits))
		return
	}
}

type TipsData interface {
}

type WithdrawForTips struct {
	Times     int
	FreeTimes int
	ToLLTimes int
	Fee       string
}

type TipsWithdrawData struct {
	Level int
	Data  WithdrawForTips
}

func getVipWithdrawLimit(vipDefines []*model.XVipDefine, withdrawLimits []*model.XWithdrawLimitConfig) []TipsWithdrawData {
	var data []TipsWithdrawData
	for _, define := range vipDefines {
		var times int
		var freeTimes int
		var toLLTimes int
		var fee string

		for _, limit := range withdrawLimits {
			if define.VipLevel == limit.VipLevel {
				times = int(limit.MaxCountPerDay)
				freeTimes = int(limit.FeeFreeCountPerDay)
				toLLTimes = times - freeTimes
				fee = fmt.Sprintf("%v%%", limit.FeePercent)
			}
		}

		data = append(data, TipsWithdrawData{
			Level: int(define.VipLevel),
			Data: WithdrawForTips{
				Times:     times,
				FreeTimes: freeTimes,
				ToLLTimes: toLLTimes,
				Fee:       fee,
			},
		})
	}

	return data
}

func getVipRebate(vipDefines []*model.XVipDefine) []TipsData {
	var tipsData []TipsData
	games := []string{
		"hash",
		"lottery",
		"lowLottery",
		"crypto",
		"chess",
		"electronic",
		"xiaoyouxi",
		"live",
		"sport",
		"texas",
		"roulette",
	}

	if len(vipDefines) == 0 {
		return tipsData
	}

	// 遍历每种游戏，构建 rebate 信息
	for _, game := range games {
		gameRebate := map[string]interface{}{"game": game}

		// 根据 vipDefines 的行数来动态填充 rebate 值
		for i, vipDefine := range vipDefines {
			key := fmt.Sprintf("data%v", i+1)
			switch game {
			case "hash":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateHaXi*100)
			case "lottery":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateLottery*100)
			case "lowLottery":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateLowLottery*100)
			case "crypto":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateCryptoMarket*100)
			case "chess":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateQiPai*100)
			case "electronic":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateDianZhi*100)
			case "xiaoyouxi":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateXiaoYouXi*100)
			case "live":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateLive*100)
			case "sport":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateSport*100)
			case "texas":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateTexas*100)
			case "roulette":
				gameRebate[key] = fmt.Sprintf("%.2f%%", vipDefine.RewardRateHaXiRoulette*100)
			}
		}

		tipsData = append(tipsData, gameRebate)
	}

	return tipsData
}

func getVipUpgrade(vipDefines []*model.XVipDefine) []TipsData {
	var tipsData []TipsData
	gameRebate := map[string]interface{}{}
	for i, vipDefine := range vipDefines {
		key := fmt.Sprintf("data%v", i+1)
		// if i == 0 {
		// 	gameRebate[key] = fmt.Sprintf("$%v", vipDefine.UpgradeReward)
		// } else if i == 1 {
		// 	gameRebate[key] = fmt.Sprintf("$%v", vipDefine.UpgradeReward)
		// } else if i == 2 {
		// 	gameRebate[key] = fmt.Sprintf("$%v", vipDefine.UpgradeReward)
		// }
		gameRebate[key] = fmt.Sprintf("$%v", vipDefine.UpgradeReward)
	}
	tipsData = append(tipsData, gameRebate)
	return tipsData
}

func getVipWeekly(vipDefines []*model.XVipDefine) []TipsData {
	var tipsData []TipsData
	gameRebate := map[string]interface{}{}
	gameWeeklyLiuSui := map[string]interface{}{}
	gameWeeklyRechargeTimes := map[string]interface{}{}
	gameWeeklyRechargeAmount := map[string]interface{}{}
	for i, vipDefine := range vipDefines {
		key := fmt.Sprintf("data%v", i+1)
		gameRebate[key] = fmt.Sprintf("$%v", vipDefine.WeeklyReward)
		gameWeeklyLiuSui[key] = fmt.Sprintf("$%v", vipDefine.WeeklyLiuSui)
		gameWeeklyRechargeTimes[key] = fmt.Sprintf("%v", vipDefine.WeeklyRechargeTimes)
		gameWeeklyRechargeAmount[key] = fmt.Sprintf("$%v", vipDefine.WeeklyRechargeAmount)
	}

	gameRebate["data0"] = "free"
	gameWeeklyLiuSui["data0"] = "liushui"
	gameWeeklyRechargeTimes["data0"] = "times"
	gameWeeklyRechargeAmount["data0"] = "amount"

	tipsData = append(tipsData, gameRebate)
	tipsData = append(tipsData, gameWeeklyLiuSui)
	tipsData = append(tipsData, gameWeeklyRechargeTimes)
	tipsData = append(tipsData, gameWeeklyRechargeAmount)

	return tipsData
}

func getVipMonthly(vipDefines []*model.XVipDefine) []TipsData {
	var tipsData []TipsData
	gameRebate := map[string]interface{}{}
	gameMonthlyLiuSui := map[string]interface{}{}
	gameMonthlyRechargeTimes := map[string]interface{}{}
	gameMonthlyRechargeAmount := map[string]interface{}{}
	for i, vipDefine := range vipDefines {
		key := fmt.Sprintf("data%v", i+1)
		gameRebate[key] = fmt.Sprintf("$%v", vipDefine.MonthlyReward)
		gameMonthlyLiuSui[key] = fmt.Sprintf("$%v", vipDefine.MonthlyLiuSui)
		gameMonthlyRechargeTimes[key] = fmt.Sprintf("%v", vipDefine.MonthlyRechargeTimes)
		gameMonthlyRechargeAmount[key] = fmt.Sprintf("$%v", vipDefine.MonthlyRechargeAmount)

	}
	gameRebate["data0"] = "free"
	gameMonthlyLiuSui["data0"] = "liushui"
	gameMonthlyRechargeTimes["data0"] = "times"
	gameMonthlyRechargeAmount["data0"] = "amount"
	tipsData = append(tipsData, gameRebate)
	tipsData = append(tipsData, gameMonthlyLiuSui)
	tipsData = append(tipsData, gameMonthlyRechargeTimes)
	tipsData = append(tipsData, gameMonthlyRechargeAmount)
	return tipsData
}

func (c *VipController) vip_info(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int `validate:"required"`
		Host     string
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	{
		ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
		logs.Info("vip_info channelId=", ChannelId, " sellerId=", SellerId)
		where := abugo.AbuDbWhere{}
		where.Add("and", "SellerId", "=", SellerId, "")
		where.Add("and", "ChannelId", "=", ChannelId, "")
		where.Add("and", "State", "=", 1, "")
		data, _ := server.Db().Table("x_vip_define").Where(where).OrderBy("VipLevel ASC").GetList()

		gameWhere := abugo.AbuDbWhere{}
		gameWhere.Add("and", "SellerId", "=", SellerId, "")
		gameWhere.Add("and", "ChannelId", "=", ChannelId, "")
		rows, _ := server.Db().Table("x_vip_game_define").Where(gameWhere).OrderBy("VipLevel ASC").GetList()

		type GameDefine struct {
			CatId      int64
			RewardRate float64
		}
		type GameDefineArr []GameDefine
		for i := 0; i < len(*data); i++ {
			gameArr := GameDefineArr{}
			for j := 0; j < len(*rows); j++ {
				gameDefine := (*rows)[j]
				tempGameDefine := GameDefine{}
				if gameDefine["VipLevel"].(int64) == (*data)[i]["VipLevel"].(int64) {
					tempGameDefine.CatId = gameDefine["CatId"].(int64)
					tempGameDefine.RewardRate = gameDefine["RewardRate"].(float64)
					gameArr = append(gameArr, tempGameDefine)
				}
			}
			(*data)[i]["RewardRateXgLotteryArr"] = gameArr
		}

		ctx.Put("vip_define", *data)
	}

	token := server.GetToken(ctx)
	if token != nil {
		{
			type result struct {
				model.XVipInfo
				UpgradeStartTime       string
				UpgradeEndTime         string
				TodayRewardFanshui     float64
				YesterDayRewardFanshui float64
				WeeklyRewardState      int
				MonthlyRewardState     int // 0-未解锁 1-(灰色可领取) 2-可领取 3-已领取
			}
			var data result
			dao := server.DaoxHashGame().XVipInfo
			db := dao.WithContext(nil)
			err = db.Where(dao.UserID.Eq(int32(token.UserId))).Scan(&data)
			upgradeTime := carbon.Parse(data.UpgradeTime.String())
			upgradeEndTime := upgradeTime.AddDays(60)
			data.UpgradeStartTime = carbon.Parse(data.UpgradeTime.String()).ToDateTimeString()
			data.UpgradeEndTime = upgradeEndTime.ToDateTimeString()

			// 每日返水，已领返水
			vipDaillyDao := server.DaoxHashGame().XVipDailly
			vipDaillyDb := vipDaillyDao.WithContext(nil)
			todayDate := carbon.Now().ToDateString()
			yesterDate := carbon.Now().SubDay().ToDateString()

			todayDateFormat := carbon.Parse(todayDate).ToStdTime()
			yesterDateFormat := carbon.Parse(yesterDate).ToStdTime()

			todayDataInfos, _ := vipDaillyDb.Where(vipDaillyDao.RecordDate.Eq(todayDateFormat)).Where(vipDaillyDao.UserID.Eq(int32(token.UserId))).Where(vipDaillyDao.State.Eq(1)).First()
			yesterDataInfos, _ := vipDaillyDb.Where(vipDaillyDao.RecordDate.Eq(yesterDateFormat)).Where(vipDaillyDao.UserID.Eq(int32(token.UserId))).Where(vipDaillyDao.State.Eq(2)).First()

			if todayDataInfos != nil {
				data.TodayRewardFanshui = todayDataInfos.RewardAmount
			}

			if yesterDataInfos != nil {
				data.YesterDayRewardFanshui = yesterDataInfos.RewardAmount
			}

			// 周月奖励可领取状态
			if data.VipLevel > 1 {
				// 月礼金
				{
					monthlyDao := server.DaoxHashGame().XVipMonthly
					mothlyDb := monthlyDao.WithContext(nil)

					// 获取上个月第一天和最后一天
					now := carbon.Now()
					// 获取当月第一天
					//firstDayOfMonth := now.SubMonths(1).StartOfMonth().ToDateString()
					//firstDayOfMonthFormt := carbon.Parse(firstDayOfMonth).ToStdTime()
					// 获取当月最后一天
					lastDayOfMonth := now.SubMonths(1).EndOfMonth().ToDateString()
					lastDayOfMonthFormat := carbon.Parse(lastDayOfMonth).ToStdTime()

					notReciveMonthlyCount, _ := mothlyDb.Where(monthlyDao.State.Eq(1)).
						Where(monthlyDao.UserID.Eq(int32(token.UserId))).
						//Where(monthlyDao.RecordDate.Gte(firstDayOfMonthFormt)).
						Where(monthlyDao.VipLevel.Eq(data.VipLevel)).
						Where(monthlyDao.RecordDate.Lte(lastDayOfMonthFormat)).
						Count()
					if notReciveMonthlyCount == 0 {
						data.MonthlyRewardState = 1
					} else {
						data.MonthlyRewardState = 2
					}

					reciveMonthlyCount, _ := mothlyDb.Where(monthlyDao.State.Eq(2)).
						Where(monthlyDao.UserID.Eq(int32(token.UserId))).
						//Where(monthlyDao.RecordDate.Gte(firstDayOfMonthFormt)).
						Where(monthlyDao.VipLevel.Eq(data.VipLevel)).
						Where(monthlyDao.RecordDate.Lte(lastDayOfMonthFormat)).
						Count()

					if reciveMonthlyCount > 0 {
						data.MonthlyRewardState = 3
					}
				}
				// 周礼金
				{
					weeklyDao := server.DaoxHashGame().XVipWeekly
					weeklyDb := weeklyDao.WithContext(nil)

					// 定义星期的常量
					const (
						Sunday    = 0
						Monday    = 1
						Tuesday   = 2
						Wednesday = 3
						Thursday  = 4
						Friday    = 5
						Saturday  = 6
					)

					// 获取当前时间的 Carbon 对象
					now := carbon.Now()

					// 计算上周五的日期
					dayOfWeek := now.DayOfWeek() // 0 (周日) - 6 (周六)
					var lastFriday carbon.Carbon
					if dayOfWeek >= Friday {
						lastFriday = now.SubDays(int(dayOfWeek - Friday))
					} else {
						lastFriday = now.SubDays(int(dayOfWeek + (7 - Friday)))
					}

					// 获取今天的日期
					//today := now.AddDays(1).ToDateString()
					//todayFormt := carbon.Parse(today).ToStdTime()
					// 获取上周五的日期
					lastFridayDate := lastFriday.ToDateString()
					lastFridayDateFormat := carbon.Parse(lastFridayDate).ToStdTime()

					notReciveWeeklyCount, _ := weeklyDb.Where(weeklyDao.State.Eq(1)).
						Where(weeklyDao.UserID.Eq(int32(token.UserId))).
						Where(weeklyDao.VipLevel.Eq(data.VipLevel)).
						Where(weeklyDao.RecordDate.Lte(lastFridayDateFormat)).
						Count()
					if notReciveWeeklyCount == 0 {
						data.WeeklyRewardState = 1
					} else {
						data.WeeklyRewardState = 2
					}

					reciveWeeklyCount, _ := weeklyDb.Where(weeklyDao.State.Eq(2)).
						Where(weeklyDao.UserID.Eq(int32(token.UserId))).
						//Where(weeklyDao.RecordDate.Gte(lastFridayDateFormat)).
						Where(weeklyDao.VipLevel.Eq(data.VipLevel)).
						Where(weeklyDao.RecordDate.Lte(lastFridayDateFormat)).
						Count()

					if reciveWeeklyCount > 0 {
						data.WeeklyRewardState = 3
					}
				}

			}

			if err != nil {
				return
			}

			ctx.Put("vip_info", data)
		}
		{
			where := abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", token.UserId, "")
			where.Add("and", "State", "=", 1, "")
			data, _ := server.Db().Table("x_vip_monthly").OrderBy("Id DESC").Where(where).GetOne()
			if data != nil {
				ctx.Put("vip_monthreward", true)
			} else {
				ctx.Put("vip_monthreward", false)
			}
		}

	} else {
		ctx.Put("vip_info", false)
		ctx.Put("vip_monthreward", false)
	}

	ctx.RespOK()
}

func (c *VipController) getVipInfo(userId int32) (*model.XVipInfo, error) {
	var data model.XVipInfo
	dao := server.DaoxHashGame().XVipInfo
	db := dao.WithContext(nil)
	err := db.Where(dao.UserID.Eq(userId)).Scan(&data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (c *VipController) vip_reward_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId int `validate:"required"` //运营商
		Page     int
		PageSize int
		Host     string
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	where := abugo.AbuDbWhere{}
	where.Add("and", "SellerId", "= ", SellerId, 0)
	where.Add("and", "ChannelId", "= ", ChannelId, "")
	where.Add("and", "UserId", "= ", token.UserId, "")
	total, data := server.Db().Table("x_vip_reward").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("data", data)
	ctx.Put("total", total)
	ctx.RespOK()
}

func (c *VipController) vipWeeklyReward(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	errcode := 0
	// 定义星期的常量
	const (
		Sunday    = 0
		Monday    = 1
		Tuesday   = 2
		Wednesday = 3
		Thursday  = 4
		Friday    = 5
		Saturday  = 6
	)

	// 获取当前时间的 Carbon 对象
	now := carbon.Now()

	// 计算上周五的日期
	dayOfWeek := now.DayOfWeek() // 0 (周日) - 6 (周六)
	var lastFriday carbon.Carbon
	if dayOfWeek >= Friday {
		lastFriday = now.SubDays(int(dayOfWeek - Friday))
	} else {
		lastFriday = now.SubDays(int(dayOfWeek + (7 - Friday)))
	}

	// 获取上周五的日期
	lastFridayDate := lastFriday.ToDateString()
	lastFridayDateFormat := carbon.Parse(lastFridayDate).ToStdTime()

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		weeklyDao := server.DaoxHashGame().XVipWeekly
		weeklyDb := weeklyDao.WithContext(nil)

		info, err := c.getVipInfo(int32(token.UserId))
		if err != nil {
			return err
		}

		weeklyReward, _ := weeklyDb.
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where(weeklyDao.UserID.Eq(int32(token.UserId))).
			Where(weeklyDao.State.Eq(1)).
			Where(weeklyDao.VipLevel.Eq(info.VipLevel)).
			Where(weeklyDao.RecordDate.Lte(lastFridayDateFormat)).
			First()

		if weeklyReward == nil {
			return errors.New("没有可领取的周礼金")
		}

		_, err = weeklyDb.Where(weeklyDao.ID.Eq(weeklyReward.ID)).Updates(map[string]interface{}{
			"State":   2,
			"GetTime": carbon.Now().ToStdTime(),
		})
		if err != nil {
			return err
		}

		// TODO 账变记录

		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()

}

func (c *VipController) vipMonthlyReward(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	errcode := 0

	now := carbon.Now()
	// 获取上月最后一天
	lastDayOfMonth := now.SubMonths(1).EndOfMonth().ToDateString()
	lastDayOfMonthFormat := carbon.Parse(lastDayOfMonth).ToStdTime()

	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		monthlyDao := server.DaoxHashGame().XVipMonthly
		monthlyDb := monthlyDao.WithContext(nil)

		info, err := c.getVipInfo(int32(token.UserId))
		if err != nil {
			return err
		}

		monthReward, _ := monthlyDb.
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where(monthlyDao.UserID.Eq(int32(token.UserId))).
			Where(monthlyDao.State.Eq(1)).
			Where(monthlyDao.RecordDate.Lte(lastDayOfMonthFormat)).
			Where(monthlyDao.VipLevel.Eq(info.VipLevel)).
			First()

		if monthReward == nil {
			return errors.New("没有可领取的月礼金")
		}

		_, err = monthlyDb.Where(monthlyDao.ID.Eq(monthReward.ID)).Updates(map[string]interface{}{
			"State":   2,
			"GetTime": carbon.Now().ToStdTime(),
		})
		if err != nil {
			return err
		}

		// TODO 账变记录

		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()
}

// 提取生成 `VipDefine` 的函数
func generateVipDefine(level string, name string, start, end int, xVipDefines []*model.XVipDefine, token *server.TokenData, vipInfo *model.XVipInfo) VipDefine {
	rebate := fmt.Sprintf("%v%%", fmt.Sprintf("%.2f", xVipDefines[start].RewardRateHaXi*100))
	upgradeRewards := fmt.Sprintf("$%v", xVipDefines[start].UpgradeReward)

	if start != end {
		rebate = fmt.Sprintf("%v%%-%v%%", fmt.Sprintf("%.2f", xVipDefines[start].RewardRateHaXi*100), fmt.Sprintf("%.2f", xVipDefines[end].RewardRateHaXi*100))
		upgradeRewards = fmt.Sprintf("$%v-$%v", xVipDefines[start].UpgradeReward, xVipDefines[end].UpgradeReward)
	}

	var isShow bool
	// 用户当前显示的段位锚点
	if vipInfo != nil && (vipInfo.VipLevel >= xVipDefines[start].VipLevel && vipInfo.VipLevel <= xVipDefines[end].VipLevel) {
		isShow = true
	}

	return VipDefine{
		Level:          level,
		Name:           name,
		Rebate:         rebate,
		UpgradeRewards: upgradeRewards,
		WeeklyGifts:    getWeeklyGifts(xVipDefines[start].WeeklyReward, xVipDefines[end].WeeklyReward, token, vipInfo),
		MonthlyGifts:   getMonthlyGifts(xVipDefines[start].MonthlyReward, xVipDefines[end].MonthlyReward, token, vipInfo),
		WithdrawLimit:  getWithdrawLimit(xVipDefines[start].VipLevel, xVipDefines[end].VipLevel, token, vipInfo),
		IsShow:         isShow,
	}
}

func getWeeklyGifts(rewardStart float64, rewardEnd float64, token *server.TokenData, vipInfo *model.XVipInfo) WeeklyGifts {
	var liushuiNow float64
	var liushuiTotal float64
	// TODO 流水
	if token != nil {
		xVipDefine := server.DaoxHashGame().XVipDefine
		// 获取当前用户vip等级
		userVipLevel, _ := xVipDefine.WithContext(nil).
			Where(xVipDefine.VipLevel.Eq(int32(vipInfo.VipLevel))).
			Where(xVipDefine.SellerID.Eq(int32(token.SellerId))).
			Where(xVipDefine.ChannelID.Eq(int32(token.ChannelId))).
			First()
		if userVipLevel != nil {
			liushuiTotal = userVipLevel.WeeklyLiuSui
		}
		presult, err := server.Db().CallProcedure("PlayManage_x_vip_dailly_GetModel", token.UserId)
		if err != nil {
			logs.Error("RecommendFriendRewardData x_api_new_agent_getcode err", err)
		}

		liushuiNow = abugo.GetFloat64FromInterface((*presult)["WeeklyLiuSui"])
	}

	var display bool
	var rewardsStr string
	if rewardStart == rewardEnd {
		rewardsStr = fmt.Sprintf("$%v", rewardStart)
	} else {
		rewardsStr = fmt.Sprintf("$%v-$%v", rewardStart, rewardEnd)
	}

	display = !(rewardStart == 0 && rewardEnd == 0) && (rewardStart >= 0 || rewardEnd > 0)

	gifts := WeeklyGifts{
		Rewards:      rewardsStr,
		LiushuiNow:   liushuiNow,
		LiushuiTotal: liushuiTotal,
		Display:      display,
	}

	return gifts
}

func getMonthlyGifts(rewardStart float64, rewardEnd float64, token *server.TokenData, vipInfo *model.XVipInfo) MonthlyGifts {
	var liushuiNow float64
	var liushuiTotal float64
	// 流水
	if token != nil {
		xVipDefine := server.DaoxHashGame().XVipDefine
		// 获取当前用户vip等级
		userVipLevel, _ := xVipDefine.WithContext(nil).
			Where(xVipDefine.VipLevel.Eq(int32(vipInfo.VipLevel))).
			Where(xVipDefine.SellerID.Eq(int32(token.SellerId))).
			Where(xVipDefine.ChannelID.Eq(int32(token.ChannelId))).
			First()
		if userVipLevel != nil {
			liushuiTotal = userVipLevel.MonthlyLiuSui
		}

		presult, err := server.Db().CallProcedure("PlayManage_x_vip_dailly_GetModel", token.UserId)
		if err != nil {
			logs.Error("RecommendFriendRewardData x_api_new_agent_getcode err", err)
		}

		liushuiNow = abugo.GetFloat64FromInterface((*presult)["MonthlyLiuSui"])
	}

	var display bool
	var rewardsStr string
	if rewardStart == rewardEnd {
		rewardsStr = fmt.Sprintf("$%v", rewardStart)
	} else {
		rewardsStr = fmt.Sprintf("$%v-$%v", rewardStart, rewardEnd)
	}

	if rewardStart >= 0 && rewardEnd > 0 {
		display = true
	}

	gifts := MonthlyGifts{
		Rewards:      rewardsStr,
		LiushuiNow:   liushuiNow,
		LiushuiTotal: liushuiTotal,
		Display:      display,
	}

	return gifts
}

func getWithdrawLimit(levelStart int32, levelEnd int32, token *server.TokenData, vipInfo *model.XVipInfo) WithdrawLimit {
	xWithdrawLimitConfig := server.DaoxHashGame().XWithdrawLimitConfig

	var limitStart, limitEnd *model.XWithdrawLimitConfig
	if token != nil {
		limitStart, _ = xWithdrawLimitConfig.WithContext(nil).
			Where(xWithdrawLimitConfig.Type.Eq(2)).
			Where(xWithdrawLimitConfig.SellerID.Eq(int32(token.SellerId))).
			Where(xWithdrawLimitConfig.ChannelID.Eq(int32(token.ChannelId))).
			Where(xWithdrawLimitConfig.VipLevel.Eq(levelStart)).
			First()

		limitEnd, _ = xWithdrawLimitConfig.WithContext(nil).
			Where(xWithdrawLimitConfig.Type.Eq(2)).
			Where(xWithdrawLimitConfig.SellerID.Eq(int32(token.SellerId))).
			Where(xWithdrawLimitConfig.ChannelID.Eq(int32(token.ChannelId))).
			Where(xWithdrawLimitConfig.VipLevel.Eq(levelEnd)).
			First()
	}

	var times string
	var freeTimes string
	var tollTimes string
	var fee string

	if limitStart == nil || limitEnd == nil {
		return WithdrawLimit{}
	}

	if levelStart == levelEnd || limitStart.MaxCountPerDay == limitEnd.MaxCountPerDay || limitStart.FeePercent == limitEnd.FeePercent || limitStart.FeeFreeCountPerDay == limitEnd.FeeFreeCountPerDay {
		times = fmt.Sprintf("%v", limitEnd.MaxCountPerDay)
		freeTimes = fmt.Sprintf("%v", limitEnd.FeeFreeCountPerDay)
		tollTimes = fmt.Sprintf("%v", limitEnd.MaxCountPerDay-limitEnd.FeeFreeCountPerDay)
		fee = fmt.Sprintf("%v%%", limitEnd.FeePercent)
	} else {
		times = fmt.Sprintf("%v-%v", limitStart.MaxCountPerDay, limitEnd.MaxCountPerDay)
		freeTimes = fmt.Sprintf("%v-%v", limitStart.FeeFreeCountPerDay, limitEnd.FeeFreeCountPerDay)
		tollTimes = fmt.Sprintf("%v-%v", limitStart.MaxCountPerDay-limitStart.FeeFreeCountPerDay, limitEnd.MaxCountPerDay-limitEnd.FeeFreeCountPerDay)
		fee = fmt.Sprintf("%v%%-%v%%", fmt.Sprintf("%.2f", limitStart.FeePercent), fmt.Sprintf("%.2f", limitEnd.FeePercent))
	}

	if vipInfo != nil && (vipInfo.VipLevel >= levelStart && vipInfo.VipLevel <= levelEnd) {
		userLimit, _ := xWithdrawLimitConfig.WithContext(nil).
			Where(xWithdrawLimitConfig.Type.Eq(2)).
			Where(xWithdrawLimitConfig.VipLevel.Eq(vipInfo.VipLevel)).
			Where(xWithdrawLimitConfig.SellerID.Eq(int32(token.SellerId))).
			Where(xWithdrawLimitConfig.ChannelID.Eq(int32(token.ChannelId))).
			First()
		if userLimit != nil {
			freeTimes = fmt.Sprintf("%v", userLimit.FeeFreeCountPerDay)
			tollTimes = fmt.Sprintf("%v", userLimit.MaxCountPerDay-userLimit.FeeFreeCountPerDay)
			fee = fmt.Sprintf("%v%%", userLimit.FeePercent)
		}

	}

	limit := WithdrawLimit{
		Times:     times,
		ShowTips:  true,
		FreeTimes: freeTimes,
		ToLLTimes: tollTimes,
		Fee:       fee,
	}

	return limit

}

func spliceLevel(level string) (levelStart, levelEnd int, tipsErr error) {
	if level != "" {
		levels := strings.Split(level, "-")
		if len(levels) == 2 {
			start, err := strconv.Atoi(levels[0])
			if err == nil {
				levelStart = int(int32(start))
			} else {
				// 处理转换错误，例如记录日志或返回错误
				log.Println("转换levelStart失败:", err)
				return 0, 0, err
			}
			end, err := strconv.Atoi(levels[1])
			if err == nil {
				levelEnd = int(int32(end))
			} else {
				// 处理转换错误，例如记录日志或返回错误
				log.Println("转换levelEnd失败:", err)
				return 0, 0, err
			}
		}
		if len(levels) == 1 {
			start, err := strconv.Atoi(levels[0])
			if err == nil {
				levelStart = int(int32(start))
			} else {
				// 处理转换错误，例如记录日志或返回错误
				log.Println("转换levelStart失败:", err)
				return 0, 0, err
			}
			end, err := strconv.Atoi(levels[0])
			if err == nil {
				levelEnd = int(int32(end))
			} else {
				// 处理转换错误，例如记录日志或返回错误
				log.Println("转换levelEnd失败:", err)
				return 0, 0, err
			}
		}

		return levelStart, levelEnd, nil
	}

	return 0, 0, errors.New("未获取到Level")
}

// generateLevelsByVipCount 根据VIP等级个数动态生成级别配置
func generateLevelsByVipCount(vipCount int, xVipDefines []*model.XVipDefine) []struct {
	rangeStart int
	rangeEnd   int
	label      string
	name       string
} {
	var levels []struct {
		rangeStart int
		rangeEnd   int
		label      string
		name       string
	}

	if vipCount == 0 {
		return levels
	}

	// 定义级别名称
	levelNames := []string{"青铜", "白银", "黄金", "白金", "钻石", "王者"}

	// 根据VIP等级个数动态分配
	switch {
	case vipCount <= 6:
		// 少于等于6个等级，每个等级一个分组
		for i := 0; i < vipCount; i++ {
			name := "青铜"
			if i < len(levelNames) {
				name = levelNames[i]
			}

			// 使用实际的VIP等级作为标签
			actualVipLevel := xVipDefines[i].VipLevel

			levels = append(levels, struct {
				rangeStart int
				rangeEnd   int
				label      string
				name       string
			}{
				rangeStart: i,
				rangeEnd:   i,
				label:      fmt.Sprintf("%d", actualVipLevel),
				name:       name,
			})
		}
	default:
		// 超过6个等级，分为6组，最后一个段位（王者）只有一个等级
		// 前5组分配剩余的等级
		remainingLevels := vipCount - 1 // 最后一个等级留给王者
		baseGroupSize := remainingLevels / 5
		remainder := remainingLevels % 5
		currentIndex := 0

		// 前5组
		for i := 0; i < 5; i++ {
			name := levelNames[i]
			start := currentIndex

			// 计算当前组的大小
			currentGroupSize := baseGroupSize
			if i < remainder {
				currentGroupSize++ // 前remainder组多分配一个等级
			}

			end := start + currentGroupSize - 1

			// 使用实际的VIP等级作为标签
			startVipLevel := xVipDefines[start].VipLevel
			endVipLevel := xVipDefines[end].VipLevel

			label := fmt.Sprintf("%d", startVipLevel)
			if start != end {
				label = fmt.Sprintf("%d-%d", startVipLevel, endVipLevel)
			}

			levels = append(levels, struct {
				rangeStart int
				rangeEnd   int
				label      string
				name       string
			}{
				rangeStart: start,
				rangeEnd:   end,
				label:      label,
				name:       name,
			})

			currentIndex = end + 1
		}

		// 最后一组（王者）只包含最高等级
		lastVipLevel := xVipDefines[vipCount-1].VipLevel
		levels = append(levels, struct {
			rangeStart int
			rangeEnd   int
			label      string
			name       string
		}{
			rangeStart: vipCount - 1,
			rangeEnd:   vipCount - 1,
			label:      fmt.Sprintf("%d", lastVipLevel),
			name:       "王者",
		})
	}

	return levels
}
