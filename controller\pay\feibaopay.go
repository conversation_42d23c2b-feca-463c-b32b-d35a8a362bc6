package paycontroller

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

// Feibaopay 飞宝支付控制器实例
var Feibaopay = new(feibaopay)

// feibaopay 飞宝支付控制器结构体
type feibaopay struct {
	Base
}

// FeibaopayDecryptedOrderData 飞宝支付解密后的订单数据结构
type FeibaopayDecryptedOrderData struct {
	Amount            string `json:"amount"`              // 订单金额
	Gateway           string `json:"gateway"`             // 支付网关
	MerchantOrderNum  string `json:"merchant_order_num"`  // 商户订单号
	MerchantOrderTime string `json:"merchant_order_time"` // 商户订单时间
	NavigateURL       string `json:"navigate_url"`        // 支付跳转URL
	Status            string `json:"status"`              // 订单状态
}

// Init 初始化函数
// 初始化服务器的HTTP接口，注册充值和提现回调接口
func (c *feibaopay) Init() {
	// 注册充值回调接口
	// 接口路径：/api/feibaopay/recharge/callback
	// 方法：POST
	// 无需鉴权
	server.Http().PostNoAuth("/api/feibaopay/recharge/callback", c.rechargeCallback)

	// 注册提现回调接口
	// 接口路径：/api/feibaopay/withdraw/callback
	// 方法：POST
	// 无需鉴权
	server.Http().PostNoAuth("/api/feibaopay/withdraw/callback", c.withdrawCallback)
}

// Recharge 充值接口
// 处理充值请求，创建订单，调用支付接口
func (c *feibaopay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0 // 初始化错误码

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	// 解析支付方式的额外配置
	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 开始数据库事务
	tx := server.DaoxHashGame().Begin()

	// 创建充值订单
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      19,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}

	// 保存订单到数据库
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 构建支付请求参数
	params := xgo.H{
		"merchant_slug":       cfg["id"],
		"gateway":             payMethod.PayType,
		"device":              "desktop",
		"amount":              fmt.Sprintf("%.2f", req.Amount),
		"merchant_order_time": float64(time.Now().UnixNano()) / 1e9,
		"merchant_order_num":  fmt.Sprintf("%d", rechargeOrder.ID),
		"uid":                 fmt.Sprintf("%d", user.UserID),
		"user_ip":             user.LoginIP,
		"callback_url":        fmt.Sprintf("%s/api/feibaopay/recharge/callback", cfg["cburl"]),
		"return_url":          fmt.Sprintf("%s", cfg["cburl"]),
	}

	// 对参数进行排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sortedParams := make(map[string]interface{})
	for _, k := range keys {
		sortedParams[k] = params[k]
	}

	// 创建AES加密器
	block, err := aes.NewCipher([]byte(cfg["key"].(string)))
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建加密器失败"), &errcode)
		return
	}

	// PKCS7填充函数
	pkcs7Pad := func(data []byte, blockSize int) []byte {
		padding := blockSize - len(data)%blockSize
		padtext := bytes.Repeat([]byte{byte(padding)}, padding)
		return append(data, padtext...)
	}

	// 序列化并加密参数
	jsonParams, err := json.Marshal(sortedParams)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("序列化参数失败"), &errcode)
		return
	}

	logs.Info("feibaopay: 排序后的参数:", sortedParams)

	// 对数据进行PKCS7填充
	paddedData := pkcs7Pad(jsonParams, aes.BlockSize)

	// 获取初始化向量
	iv := []byte(cfg["iv"].(string))

	// 使用CBC模式加密数据
	mode := cipher.NewCBCEncrypter(block, iv)
	encrypted := make([]byte, len(paddedData))
	mode.CryptBlocks(encrypted, paddedData)

	// Base64编码加密后的数据
	encoded := base64.StdEncoding.EncodeToString(encrypted)

	// 构建最终的请求数据
	data := xgo.H{
		"merchant_slug": cfg["id"],
		"data":          encoded,
	}

	// 发送支付请求
	jsonData, _ := json.Marshal(&data)
	resp, err := c.post(cfg["url"].(string)+"/v3/deposit", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("feibaopay: 发起支付请求失败"), &errcode)
		return
	}

	// 定义响应数据结构
	type FeibaopayRechargeResponse struct {
		Msg              string `json:"msg"`
		Code             int    `json:"code"`
		Order            string `json:"order"`
		Action           string `json:"action"`
		MerchantSlug     string `json:"merchant_slug"`
		MerchantOrderNum string `json:"merchant_order_num"`
	}

	response := FeibaopayRechargeResponse{}

	// 检查响应状态码
	if resp.StatusCode() != 200 {
		logs.Error("feibaopay: 错误的状态码:", resp.StatusCode())
		ctx.RespErrString(true, &errcode, fmt.Sprintf("错误的状态码: %d", resp.StatusCode()))
		tx.Rollback()
		return
	}

	// 解析响应数据
	err = json.Unmarshal(resp.Body(), &response)
	if err != nil {
		logs.Error("feibaopay: 解析响应数据失败, err=", err.Error())
		ctx.RespErrString(true, &errcode, "解析响应数据失败")
		tx.Rollback()
		return
	}

	// 检查响应码
	if response.Code != 0 {
		tx.Rollback()
		ctx.RespErr(errors.New("feibaopay: 订单创建失败"), &errcode)
		return
	}

	// 提交事务
	tx.Commit()

	// 解密订单数据
	decryptedOrderStr, err := decryptAES(response.Order, []byte(cfg["key"].(string)), []byte(cfg["iv"].(string)))
	if err != nil {
		ctx.RespErr(errors.New("解密订单数据失败"), &errcode)
		return
	}

	var decryptedOrder FeibaopayDecryptedOrderData
	err = json.Unmarshal([]byte(decryptedOrderStr), &decryptedOrder)
	if err != nil {
		ctx.RespErr(errors.New("解析解密后的订单数据失败"), &errcode)
		return
	}

	// 返回支付URL
	ctx.RespOK(xgo.H{
		"payurl": decryptedOrder.NavigateURL,
	})
}

// rechargeCallback 处理充值回调
func (c *feibaopay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	logs.Info("feibaopay: 开始处理充值回调")

	// 定义回调数据结构
	type FeibaopayRechargeCallBack struct {
		Msg              string `json:"msg"`                // 消息
		Code             int    `json:"code"`               // 状态码
		Order            string `json:"order"`              // 订单数据
		Action           string `json:"action"`             // 动作
		Merchant         string `json:"merchant"`           // 商户信息
		MerchantOrderNum string `json:"merchant_order_num"` // 商户订单号
	}

	// 解析回调数据
	var callBack FeibaopayRechargeCallBack
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("feibaopay: 解析表单数据失败:", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 获取表单数据
	callBack.Msg = ctx.Gin().PostForm("msg")
	callBack.Code, _ = strconv.Atoi(ctx.Gin().PostForm("code"))
	callBack.Order = ctx.Gin().PostForm("order")
	callBack.Action = ctx.Gin().PostForm("action")
	callBack.Merchant = ctx.Gin().PostForm("merchant")
	callBack.MerchantOrderNum = ctx.Gin().PostForm("merchant_order_num")

	logs.Info("feibaopay 充值回调数据:", callBack)

	// 解析订单ID
	payOrderId, err := strconv.Atoi(callBack.MerchantOrderNum)
	if err != nil {
		logs.Error("feibaopay: 解析订单号失败:", err)
		ctx.Gin().String(400, "解析订单号失败")
		return
	}
	logs.Info("feibaopay: 订单ID =", payOrderId)

	// 获取充值订单信息
	order, err := c.getRechargeOrder(payOrderId)
	if err != nil {
		logs.Error("feibaopay: 未找到充值订单:", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付方式信息
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("feibaopay: 未找到支付方式:", err)
		ctx.Gin().String(400, "支付方式不存在")
		return
	}
	logs.Info("feibaopay: 成功获取支付方式")

	// 解析支付配置
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	logs.Info("feibaopay: 配置解密完成")

	// 解密订单数据
	decryptedOrderStr, err := decryptAES(callBack.Order, []byte(cfg["key"].(string)), []byte(cfg["iv"].(string)))
	if err != nil {
		logs.Error("feibaopay: 解密订单数据失败:", err)
		ctx.Gin().String(400, "解密数据失败")
		return
	}
	logs.Info("feibaopay: 订单数据解密结果:", decryptedOrderStr)

	// 解析解密后的订单数据
	var decryptedOrder FeibaopayDecryptedOrderData
	if err := json.Unmarshal([]byte(decryptedOrderStr), &decryptedOrder); err != nil {
		logs.Error("feibaopay: 解析解密后的订单数据失败:", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}
	logs.Info("feibaopay: 解密后的订单数据:", decryptedOrder)

	// 检查订单状态
	if decryptedOrder.Status != "success" {
		logs.Info("feibaopay: 订单状态未成功:", decryptedOrder.Status)
		ctx.Gin().String(200, "订单状态未成功")
		return
	}
	logs.Info("feibaopay: 订单状态成功")

	// 验证订单金额
	amount, _ := strconv.ParseFloat(decryptedOrder.Amount, 64)
	if math.Abs(order.Amount-amount) > 0.01 {
		logs.Info("feibaopay: 订单金额不匹配. 预期:", order.Amount, "实际:", amount)
		ctx.Gin().String(400, "金额不匹配")
		return
	}
	logs.Info("feibaopay: 订单金额匹配")
	logs.Info("feibaopay: 开始处理成功充值")
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	logs.Info("feibaopay: 充值处理完成")

	ctx.Gin().String(200, "success")
	logs.Info("feibaopay: 充值回调处理结束")
}

// withdrawCallback 处理提现回调
func (c *feibaopay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	logs.Info("feibaopay: 开始处理提现回调")

	// 定义提现回调数据结构
	type FeibaopayWithdrawCallBack struct {
		Msg              string `json:"msg"`                // 消息
		Code             int    `json:"code"`               // 状态码
		Order            string `json:"order"`              // 订单数据
		Action           string `json:"action"`             // 动作
		Merchant         string `json:"merchant"`           // 商户信息
		MerchantOrderNum string `json:"merchant_order_num"` // 商户订单号
	}

	// 解析回调数据
	var callBack FeibaopayWithdrawCallBack
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("feibaopay: 解析表单数据失败:", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 获取表单数据
	callBack.Msg = ctx.Gin().PostForm("msg")
	callBack.Code, _ = strconv.Atoi(ctx.Gin().PostForm("code"))
	callBack.Order = ctx.Gin().PostForm("order")
	callBack.Action = ctx.Gin().PostForm("action")
	callBack.Merchant = ctx.Gin().PostForm("merchant")
	callBack.MerchantOrderNum = ctx.Gin().PostForm("merchant_order_num")

	logs.Info("feibaopay 提现回调数据:", callBack)

	// 解析订单ID
	payOrderId, err := strconv.Atoi(callBack.MerchantOrderNum)
	if err != nil {
		logs.Error("feibaopay: 解析订单号失败:", err)
		ctx.Gin().String(400, "解析订单号失败")
		return
	}
	logs.Info("feibaopay: 订单ID =", payOrderId)

	// 获取提现订单信息
	order, err := c.getWithdrawOrder(payOrderId)
	if err != nil {
		logs.Error("feibaopay: 未找到提现订单:", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付方式信息
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("feibaopay: 未找到支付方式:", err)
		ctx.Gin().String(400, "支付方式不存在")
		return
	}

	// 解析支付配置
	var cfg map[string]interface{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 解密订单数据
	decryptedOrderStr, err := decryptAES(callBack.Order, []byte(cfg["key"].(string)), []byte(cfg["iv"].(string)))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("feibaopay: 解密订单数据失败:", err)
		ctx.Gin().String(400, "解密数据失败")
		return
	}
	logs.Info("feibaopay: 订单数据解密结果:", decryptedOrderStr)

	// 解析解密后的订单数据
	var decryptedOrder FeibaopayDecryptedOrderData
	if err := json.Unmarshal([]byte(decryptedOrderStr), &decryptedOrder); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("feibaopay: 解析解密后的订单数据失败:", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 验证订单金额
	amount, _ := strconv.ParseFloat(decryptedOrder.Amount, 64)
	if math.Abs(order.RealAmount-amount) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("feibaopay: 订单金额不匹配. 预期:", order.RealAmount, "实际:", amount)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 检查订单状态
	if decryptedOrder.Status != "success" && decryptedOrder.Status != "success_done" {
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, "订单状态未成功")
		return
	}

	// 更新订单状态为成功
	c.withdrawCallbackHandel(int(order.ID), 6)

	ctx.Gin().String(200, "success")
	logs.Info("feibaopay: 提现回调处理结束")
}

// pkcs7Unpad 移除PKCS7填充
func pkcs7Unpad(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("数据为空")
	}
	padding := int(data[len(data)-1])
	if padding > aes.BlockSize || padding == 0 {
		return nil, errors.New("填充无效")
	}
	if len(data) < padding {
		return nil, errors.New("数据长度小于填充长度")
	}
	return data[:len(data)-padding], nil
}

// decryptAES AES解密函数
func decryptAES(encrypted string, key, iv []byte) (string, error) {
	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", err
	}

	// 创建AES解密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 检查密文长度
	if len(ciphertext) < aes.BlockSize {
		return "", errors.New("密文长度过短")
	}

	// 使用CBC模式解密
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertext, ciphertext)

	// 移除PKCS7填充
	unpadded, err := pkcs7Unpad(ciphertext)
	if err != nil {
		return "", err
	}

	return string(unpadded), nil
}
