package single

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/md5"
	"encoding/base64"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// SA游戏单一钱包类
// SA游戏接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

// 新版SA API客户端
type SASingleService struct {
	apiUrl                string            // api基础接口
	gameUrl               string            // 游戏网页地址
	secretKey             string            // Secret Key
	encryptKey            string            // Encrypt Key
	md5Key                string            // MD5 Key
	suffix                string            // Suffix
	lobbyId               string            // Lobby ID
	currency              string            // 币种
	homeUrl               string            // 跳转URL
	brandName             string            // 厂商标识
	games                 map[string]string // 游戏类型
	Debug                 bool              // 日志调试模式
	RefreshUserAmountFunc func(int) error   // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

// 初始化SA游戏单一钱包
func NewSASingleService(params map[string]string, fc func(int) error) *SASingleService {
	// 游戏类型
	games := map[string]string{
		"bac":           "百家乐",
		"dtx":           "龙虎",
		"sicbo":         "骰宝",
		"rot":           "轮盘",
		"pokdeng":       "博丁",
		"andarbahar":    "安达巴哈",
		"teenpatti2020": "印度炸金花",
		"blackjack":     "黑杰克",
		"xocdia":        "色碟",
		"thaihilo":      "泰国骰宝",
		"fishprawncrab": "鱼虾蟹",
	}

	return &SASingleService{
		apiUrl:                params["api_url"],     // API地址
		gameUrl:               params["game_url"],    // 游戏网页地址
		secretKey:             params["secret_key"],  // Secret Key
		encryptKey:            params["encrypt_key"], // Encrypt Key
		md5Key:                params["md5_key"],     // MD5 Key
		suffix:                params["suffix"],      // Suffix
		lobbyId:               params["lobby_id"],    // Lobby ID
		currency:              params["currency"],    // 币种
		brandName:             "salive",              // 厂商标识
		games:                 games,
		Debug:                 false, // 是否调试模式
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeySA = "cacheKeySA:"

// SA游戏的余额变动原因常量
const (
	BalanceCReasonSABet    = 3461 // SA游戏下注
	BalanceCReasonSASettle = 3462 // SA游戏结算
	BalanceCReasonSACancel = 3463 // SA游戏取消
)

// SA返回错误码
const (
	SA_Code_Success           = 0    // 成功
	SA_Code_UserNotExist      = 1000 // 会员帐号不存在
	SA_Code_CurrencyError     = 1001 // 货币代码不正确
	SA_Code_AmountError       = 1002 // 金额不正确
	SA_Code_UserLocked        = 1003 // 会员帐号已被锁
	SA_Code_InsufficientFunds = 1004 // 不足够点数
	SA_Code_GeneralError      = 1005 // 一般错误
	SA_Code_DecryptionError   = 1006 // 解密错误
	SA_Code_LoginExpired      = 1007 // 登入时段过期，需要重新登入
	SA_Code_SystemError       = 9999 // 系统错误
)

// DES加密
func (s *SASingleService) DESEncrypt(plaintext string) (string, error) {
	// 将密钥转换为字节数组
	key := []byte(s.encryptKey)

	// 创建DES密码块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 对明文进行PKCS5填充
	plainBytes := []byte(plaintext)
	plainBytes = s.PKCS5Padding(plainBytes, block.BlockSize())

	// 创建加密器
	mode := cipher.NewCBCEncrypter(block, key)

	// 加密
	ciphertext := make([]byte, len(plainBytes))
	mode.CryptBlocks(ciphertext, plainBytes)

	// 返回Base64编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DES解密
func (s *SASingleService) DESDecrypt(ciphertext string) (string, error) {
	// 将密钥转换为字节数组
	key := []byte(s.encryptKey)

	// 创建DES密码块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES密码块失败: %v", err)
	}

	// 解码Base64密文
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		// 尝试修复可能的base64编码问题
		// 有时候base64编码可能缺少填充字符'='
		paddedText := ciphertext
		for i := 0; i < 3; i++ {
			paddedText += "="
			cipherBytes, err = base64.StdEncoding.DecodeString(paddedText)
			if err == nil {
				break
			}
		}

		if err != nil {
			return "", fmt.Errorf("解码Base64密文失败: %v, 密文: %s", err, ciphertext)
		}
	}

	// 检查密文长度是否为块大小的倍数
	if len(cipherBytes)%block.BlockSize() != 0 {
		return "", fmt.Errorf("密文长度不是块大小的倍数: %d", len(cipherBytes))
	}

	// 创建解密器
	mode := cipher.NewCBCDecrypter(block, key)

	// 解密
	plaintext := make([]byte, len(cipherBytes))
	mode.CryptBlocks(plaintext, cipherBytes)

	// 去除PKCS5填充
	plaintext, err = s.PKCS5UnpaddingSafe(plaintext)
	if err != nil {
		return "", fmt.Errorf("去除PKCS5填充失败: %v", err)
	}
	// 记录原始请求数据
	logs.Info("SA_single 解后数据: ", string(plaintext))
	return string(plaintext), nil
}

// 安全的PKCS5Unpadding，增加错误检查
func (s *SASingleService) PKCS5UnpaddingSafe(src []byte) ([]byte, error) {
	length := len(src)
	if length == 0 {
		return src, nil
	}

	// 获取填充值
	padding := int(src[length-1])

	// 检查填充值是否有效
	if padding > length || padding == 0 {
		return nil, fmt.Errorf("无效的填充值: %d", padding)
	}

	// 验证所有填充字节是否一致
	for i := length - padding; i < length; i++ {
		if src[i] != byte(padding) {
			return nil, fmt.Errorf("填充字节不一致")
		}
	}

	return src[:(length - padding)], nil
}

// PKCS5填充
func (s *SASingleService) PKCS5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// 去除PKCS5填充
func (s *SASingleService) PKCS5Unpadding(src []byte) []byte {
	length := len(src)
	if length == 0 {
		return src
	}
	unpadding := int(src[length-1])
	return src[:(length - unpadding)]
}

// 创建MD5签名
func (s *SASingleService) BuildMD5(inString string) string {
	hashed := md5.Sum([]byte(inString))
	return fmt.Sprintf("%x", hashed)
}

// 调用SA API
func (s *SASingleService) CallSAAPI(method string, params map[string]string) ([]byte, error) {
	// 构建查询字符串
	var queryParams []string
	queryParams = append(queryParams, "method="+method)
	queryParams = append(queryParams, "Key="+s.secretKey)

	// 添加当前时间
	currentTime := time.Now().Format("20060102150405")
	queryParams = append(queryParams, "Time="+currentTime)

	// 添加其他参数
	for k, v := range params {
		queryParams = append(queryParams, k+"="+v)
	}

	// 构建查询字符串
	queryString := strings.Join(queryParams, "&")

	// DES加密
	encryptedQuery, err := s.DESEncrypt(queryString)
	if err != nil {
		return nil, fmt.Errorf("加密失败: %v", err)
	}

	// URL编码
	q := url.QueryEscape(encryptedQuery)

	// 构建MD5签名
	signature := s.BuildMD5(queryString + s.md5Key + currentTime + s.secretKey)

	// 构建POST请求
	postData := "q=" + q + "&s=" + signature

	// 发送POST请求
	resp, err := http.Post(s.apiUrl, "application/x-www-form-urlencoded", strings.NewReader(postData))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	return body, nil
}

// Balance 获取玩家余额 API URL GetUserBalance
func (s *SASingleService) Balance(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		XMLName  xml.Name `xml:"RequestResponse"`
		Username string   `xml:"username"` // 用户名
		Currency string   `xml:"currency"` // 币种
		Amount   float64  `xml:"amount"`   // 余额
		Error    int      `xml:"error"`    // 错误代码
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("SA_single 获取玩家余额 读取请求体失败: ", err.Error())
		ctx.Gin().String(http.StatusBadRequest, "读取请求失败")
		return
	}

	// 记录原始请求数据
	logs.Info("SA_single 获取玩家余额 原始请求数据: ", string(bodyBytes))

	// 处理URL编码
	decodedData, err := url.QueryUnescape(string(bodyBytes))
	if err != nil {
		logs.Error("SA_single 获取玩家余额 URL解码失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解密请求
	decryptedData, err := s.DESDecrypt(decodedData)
	if err != nil {
		logs.Error("SA_single 获取玩家余额 解密请求失败: ", err.Error(), " 解码后数据: ", decodedData)
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解析请求参数
	values, err := url.ParseQuery(decryptedData)
	if err != nil {
		logs.Error("SA_single 获取玩家余额 解析请求参数失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	username := values.Get("username")
	currency := values.Get("currency")

	if username == "" || currency == "" {
		logs.Error("SA_single 获取玩家余额 请求参数不完整")
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	userId, err := strconv.Atoi(username)
	if err != nil {
		logs.Error("SA_single 获取玩家余额 会员账号错误 用户名转换错误 username=", username, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("SA_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   0,
				Error:    SA_Code_UserNotExist,
			}
			ctx.Gin().XML(http.StatusOK, respData)
		} else {
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   0,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
		}
		return
	}

	// 返回用户余额
	respData := ResponseData{
		Username: username,
		Currency: currency,
		Amount:   userBalance.Amount,
		Error:    SA_Code_Success,
	}

	// 记录成功响应日志
	logs.Info("SA_single 获取玩家余额 响应成功 username=", username, " respdata=", respData)

	ctx.Gin().XML(http.StatusOK, respData)
}

// Bet 下注 API URL PlaceBet
func (s *SASingleService) Bet(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		XMLName  xml.Name `xml:"RequestResponse"`
		Username string   `xml:"username"` // 用户名
		Currency string   `xml:"currency"` // 币种
		Amount   float64  `xml:"amount"`   // 余额
		Error    int      `xml:"error"`    // 错误代码
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("SA_single 下注确认 读取请求体失败: ", err.Error())
		ctx.Gin().String(http.StatusBadRequest, "读取请求失败")
		return
	}

	// 记录原始请求数据
	logs.Info("SA_single 下注确认 原始请求数据: ", string(bodyBytes))

	// 处理URL编码
	decodedData, err := url.QueryUnescape(string(bodyBytes))
	if err != nil {
		logs.Error("SA_single 下注确认 URL解码失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解密请求
	decryptedData, err := s.DESDecrypt(decodedData)
	if err != nil {
		logs.Error("SA_single 下注确认 解密请求失败: ", err.Error(), " 解码后数据: ", decodedData)
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解析请求参数
	values, err := url.ParseQuery(decryptedData)
	if err != nil {
		logs.Error("SA_single 下注确认 解析请求参数失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	username := values.Get("username")
	currency := values.Get("currency")
	amountStr := values.Get("amount")
	txnid := values.Get("txnid")
	timestamp := values.Get("timestamp")
	ip := values.Get("ip")
	gametype := values.Get("gametype")
	platform := values.Get("platform")
	hostid := values.Get("hostid")
	gameid := values.Get("gameid")
	betdetails := values.Get("betdetails")

	// 输出全部参数
	logs.Info("SA_single 下注确认 请求参数: username=", username,
		", currency=", currency,
		", amount=", amountStr,
		", txnid=", txnid,
		", timestamp=", timestamp,
		", ip=", ip,
		", gametype=", gametype,
		", platform=", platform,
		", hostid=", hostid,
		", gameid=", gameid,
		", betdetails=", betdetails)

	// 验证必要参数
	if username == "" || currency == "" || amountStr == "" || txnid == "" || gametype == "" {
		logs.Error("SA_single 下注确认 请求参数不完整")
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 转换金额
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		logs.Error("SA_single 下注确认 金额格式错误 amountStr=", amountStr, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_AmountError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 验证金额
	if amount <= 0 {
		logs.Error("SA_single 下注确认 金额必须大于0 amount=", amount)
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_AmountError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	userId, err := strconv.Atoi(username)
	if err != nil {
		logs.Error("SA_single 下注确认 会员账号错误 用户名转换错误 username=", username, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, s.brandName, hostid); err != nil {
		logs.Error("SA_single 下注确认 权限检查错误 userId=", userId, " gameId=", hostid, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	} else if !allowed {
		logs.Error("SA_single 下注确认 权限被拒绝 userId=", userId, " gameId=", hostid, " hint=", hint)
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 判断游戏是否可用
	gamelist, err, errcode := base.IsGameAvailable(s.brandName, hostid)
	if err != nil {
		logs.Error("SA_single 下注确认 游戏不存在或禁用 username=", username, " err=", err.Error(), " errcode=", errcode)
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 获取游戏名称
	gameName := gamelist.Name
	gameId := gamelist.GameId
	//gameName := s.games[gametype]
	//if gameName == "" {
	//	gameName = fmt.Sprintf("SA游戏(%v)", gametype)
	//}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId     //用户ID
	cacheSignKey.Brand = s.brandName //三方厂商名称
	cacheSignKey.ThirdId = gameid    //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, values, nil)
	if duplicateResult != nil && err == nil {
		ctx.Gin().XML(http.StatusOK, duplicateResult)
		logs.Error(s.brandName, " 检测到重复请求 thirdId=", gameid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, values, nil, nil); e != nil {
			logs.Error(s.brandName, " 设置缓存出错 thirdId=", gameid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	// 开始下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("SA_single 下注确认 获取用户信息失败 txnid=", txnid, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_UserNotExist,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			} else {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_SystemError,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			}
			return e
		}

		// 检查余额是否足够
		if amount > userBalance.Amount {
			e = errors.New("余额不足")
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_InsufficientFunds,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)

		// 查询注单是否存在
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", gameid, s.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e == nil {
			logs.Error("SA_single 下注确认 单号已存在 gameid=", gameid, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_GeneralError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return errors.New("订单号重复")
		}
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("SA_single 下注确认 查询已存在订单失败 gameid=", gameid, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 创建注单
		order = thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userBalance.UserId,
			Brand:        s.brandName,
			ThirdId:      gameid, // 使用三方gameid作为ThirdId
			GameId:       gameId,
			GameName:     gameName,
			BetAmount:    amount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    timestamp,
			Currency:     currency,
			RawData:      decryptedData,
			State:        1,
			Fee:          0,
			DataState:    -1, //未开奖
			CreateTime:   timestamp,
			ThirdRefId:   &txnid,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("SA_single 下注确认 创建订单失败 gameid=", gameid, " order=", order, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 扣除用户余额
		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, amount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", amount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("SA_single 下注确认 扣款失败 gameid=", gameid, " userId=", userId, " amount=", amount, " err=", e.Error())
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -amount,
			AfterAmount:  userBalance.Amount - amount,
			Reason:       BalanceCReasonSABet,
			Memo:         s.brandName + " bet,thirdId:" + gameid,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   timestamp,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("SA_single 下注确认 创建账变记录失败 gameid=", gameid, " amountLog=", amountLog, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 更新用户余额
		userBalance.Amount -= amount
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   userBalance.Amount,
			Error:    SA_Code_Success,
		}

		// 记录成功响应日志
		logs.Info("SA_single 下注确认 响应成功 gameid=", gameid, " txnid=", txnid, " respdata=", respData)
		ctx.Gin().XML(http.StatusOK, respData)
		return nil
	})

	if err != nil {
		logs.Error("SA_single 下注确认 事务处理失败 err=", err)
		return
	} else {
		// 推送投注事件
		if s.thirdGamePush != nil {
			s.thirdGamePush.PushBetEvent(int(userId), gameName, s.brandName, amount, s.currency, s.brandName, gameid, 5)
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if s.RefreshUserAmountFunc != nil {
				tmpErr := s.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][SA_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][SA_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// CancelBet 取消單 API URL PlaceBetCancel
func (s *SASingleService) CancelBet(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		XMLName  xml.Name `xml:"RequestResponse"`
		Username string   `xml:"username"` // 用户名
		Currency string   `xml:"currency"` // 币种
		Amount   float64  `xml:"amount"`   // 余额
		Error    int      `xml:"error"`    // 错误代码
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("SA_single 取消下注 读取请求体失败: ", err.Error())
		ctx.Gin().String(http.StatusBadRequest, "读取请求失败")
		return
	}

	// 记录原始请求数据
	logs.Info("SA_single 取消下注 原始请求数据: ", string(bodyBytes))

	// 处理URL编码
	decodedData, err := url.QueryUnescape(string(bodyBytes))
	if err != nil {
		logs.Error("SA_single 取消下注 URL解码失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解密请求
	decryptedData, err := s.DESDecrypt(decodedData)
	if err != nil {
		logs.Error("SA_single 取消下注 解密请求失败: ", err.Error(), " 解码后数据: ", decodedData)
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解析请求参数
	values, err := url.ParseQuery(decryptedData)
	if err != nil {
		logs.Error("SA_single 取消下注 解析请求参数失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	username := values.Get("username")
	currency := values.Get("currency")
	amountStr := values.Get("amount")
	txnid := values.Get("txnid")
	txn_reverse_id := values.Get("txn_reverse_id")
	timestamp := values.Get("timestamp")
	gametype := values.Get("gametype")
	hostid := values.Get("hostid")
	gameid := values.Get("gameid")
	retry := values.Get("retry")
	gamecancel := values.Get("gamecancel")

	// 输出全部参数
	logs.Info("SA_single 取消下注 请求参数: username=", username,
		", currency=", currency,
		", amount=", amountStr,
		", txnid=", txnid,
		", txn_reverse_id=", txn_reverse_id,
		", timestamp=", timestamp,
		", gametype=", gametype,
		", hostid=", hostid,
		", gameid=", gameid,
		", retry=", retry,
		", gamecancel=", gamecancel)

	// 验证必要参数
	if username == "" || currency == "" || amountStr == "" || txnid == "" || txn_reverse_id == "" {
		logs.Error("SA_single 取消下注 请求参数不完整")
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 转换金额
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		logs.Error("SA_single 取消下注 金额格式错误 amountStr=", amount, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_AmountError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	userId, err := strconv.Atoi(username)
	if err != nil {
		logs.Error("SA_single 取消下注 会员账号错误 用户名转换错误 username=", username, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId     //用户ID
	cacheSignKey.Brand = s.brandName //三方厂商名称
	cacheSignKey.ThirdId = gameid    //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, values, nil)
	if duplicateResult != nil && err == nil {
		ctx.Gin().XML(http.StatusOK, duplicateResult)
		logs.Error(s.brandName, " 检测到重复请求 thirdId=", gameid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, values, nil, nil); e != nil {
			logs.Error(s.brandName, " 设置缓存出错 thirdId=", gameid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	thirdTime := time.Now().Format("2006-01-02 15:04:05")

	// 开始取消下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("SA_single 取消下注 获取用户余额失败 thirdId=", txnid, " userId=", userId, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_UserNotExist,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			} else {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_SystemError,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			}
			return e
		}

		// 查询原始下注订单
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdRefId=? and Brand=?", txn_reverse_id, s.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound {
				// 如果原始订单不存在，直接返回成功，不需要做任何操作
				logs.Info("SA_single 取消下注 原始订单不存在 txn_reverse_id=", txn_reverse_id)
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   userBalance.Amount,
					Error:    SA_Code_Success,
				}
				ctx.Gin().XML(http.StatusOK, respData)
				return nil
			}
			logs.Error("SA_single 取消下注 查询原始订单失败 txn_reverse_id=", txn_reverse_id, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 检查订单状态
		if order.DataState != -1 {
			// 如果订单已经结算或取消，直接返回成功
			logs.Info("SA_single 取消下注 订单已结算或取消 txn_reverse_id=", txn_reverse_id, " DataState=", order.DataState)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_Success,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return nil
		}

		// 更新订单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  -2,
			"ThirdTime":  thirdTime,
			"ValidBet":   0,
			"WinAmount":  0,
			"BetCtx":     decryptedData,
			"GameRst":    decryptedData,
			"BetCtxType": 3,
			"RawData":    decryptedData,
		}).Error
		if e != nil {
			logs.Error("SA_single 取消下注 更新订单状态失败 txn_reverse_id=", txn_reverse_id, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 返还用户余额
		if order.BetAmount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", order.BetAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && order.BetAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("SA_single 取消下注 返还余额失败 userId=", userId, " txn_reverse_id=", txn_reverse_id, " amount=", order.BetAmount, " err=", e.Error())
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   userBalance.Amount,
					Error:    SA_Code_SystemError,
				}
				ctx.Gin().XML(http.StatusOK, respData)
				return e
			}
		}

		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       order.BetAmount,
			AfterAmount:  userBalance.Amount + order.BetAmount,
			Reason:       BalanceCReasonSACancel,
			Memo:         s.brandName + " cancel,thirdId:" + txn_reverse_id,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("SA_single 取消下注 创建账变记录失败 txn_reverse_id=", txn_reverse_id, " amountLog=", amountLog, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 更新用户余额
		userBalance.Amount += order.BetAmount
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   userBalance.Amount,
			Error:    SA_Code_Success,
		}

		// 记录成功响应日志
		logs.Info("SA_single 取消下注 响应成功 gameid=", gameid, " txnid=", txnid, " txn_reverse_id=", txn_reverse_id, " respdata=", respData)

		ctx.Gin().XML(http.StatusOK, respData)
		return nil
	})

	if err != nil {
		logs.Error("SA_single 取消下注 事务处理失败 err=", err)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int) {
			if s.RefreshUserAmountFunc != nil {
				tmpErr := s.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][SA_single] 取消下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][SA_single] 取消下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// BetResult 結算 派彩回调接口 API URL PlayerWin/PlayerLost
func (s *SASingleService) BetResult(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		XMLName  xml.Name `xml:"RequestResponse"`
		Username string   `xml:"username"` // 用户名
		Currency string   `xml:"currency"` // 币种
		Amount   float64  `xml:"amount"`   // 余额
		Error    int      `xml:"error"`    // 错误代码
	}

	// 读取请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("SA_single 派彩 读取请求体失败: ", err.Error())
		ctx.Gin().String(http.StatusBadRequest, "读取请求失败")
		return
	}

	// 记录原始请求数据
	logs.Info("SA_single 派彩 原始请求数据: ", string(bodyBytes))

	// 处理URL编码
	decodedData, err := url.QueryUnescape(string(bodyBytes))
	if err != nil {
		logs.Error("SA_single 派彩 URL解码失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解密请求
	decryptedData, err := s.DESDecrypt(decodedData)
	if err != nil {
		logs.Error("SA_single 派彩 解密请求失败: ", err.Error(), " 解码后数据: ", decodedData)
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_DecryptionError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	// 解析请求参数
	values, err := url.ParseQuery(decryptedData)
	if err != nil {
		logs.Error("SA_single 派彩 解析请求参数失败: ", err.Error())
		respData := ResponseData{
			Username: "",
			Currency: "",
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	username := values.Get("username")
	currency := values.Get("currency")
	txnid := values.Get("txnid")
	timestamp := values.Get("timestamp")
	gametype := values.Get("gametype")
	payouttime := values.Get("Payouttime")
	hostid := values.Get("hostid")
	gameid := values.Get("gameid")
	retry := values.Get("retry")

	// 输出全部参数
	logs.Info("SA_single 派彩 请求参数: username=", username,
		", currency=", currency,
		", txnid=", txnid,
		", timestamp=", timestamp,
		", gametype=", gametype,
		", payouttime=", payouttime,
		", hostid=", hostid,
		", gameid=", gameid,
		", retry=", retry,
		", URL.Path=", ctx.Gin().Request.URL.Path)

	// 判断是PlayerWin还是PlayerLost
	isPlayerWin := strings.Contains(ctx.Gin().Request.URL.Path, "/PlayerWin")

	// 获取金额和洗码量
	var amount, rolling float64
	var payoutdetails string

	if isPlayerWin {
		amountStr := values.Get("amount")
		rollingStr := values.Get("rolling")
		payoutdetails = values.Get("payoutdetails")

		// 输出PlayerWin特有参数
		logs.Info("SA_single 派彩 PlayerWin特有参数: amount=", amountStr,
			", rolling=", rollingStr,
			", payoutdetails长度=", len(payoutdetails))

		// 转换金额
		amount, err = strconv.ParseFloat(amountStr, 64)
		if err != nil {
			logs.Error("SA_single 派彩 金额格式错误 amountStr=", amountStr, " err=", err.Error())
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   0,
				Error:    SA_Code_AmountError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return
		}

		// 转换洗码量
		rolling, err = strconv.ParseFloat(rollingStr, 64)
		if err != nil {
			logs.Error("SA_single 派彩 洗码量格式错误 rollingStr=", rollingStr, " err=", err.Error())
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   0,
				Error:    SA_Code_AmountError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return
		}
	} else {
		// PlayerLost
		rollingStr := values.Get("rolling")
		payoutdetails = values.Get("payoutdetails")

		// 输出PlayerLost特有参数
		logs.Info("SA_single 派彩 PlayerLost特有参数: rolling=", rollingStr,
			", payoutdetails长度=", len(payoutdetails))

		// 转换洗码量
		rolling, err = strconv.ParseFloat(rollingStr, 64)
		if err != nil {
			logs.Error("SA_single 派彩 洗码量格式错误 rollingStr=", rollingStr, " err=", err.Error())
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   0,
				Error:    SA_Code_AmountError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return
		}

		// PlayerLost中amount为0
		amount = 0
	}

	// 验证必要参数
	if username == "" || currency == "" || txnid == "" || gametype == "" {
		logs.Error("SA_single 派彩 请求参数不完整")
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_GeneralError,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	userId, err := strconv.Atoi(username)
	if err != nil {
		logs.Error("SA_single 派彩 会员账号错误 用户名转换错误 username=", username, " err=", err.Error())
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   0,
			Error:    SA_Code_UserNotExist,
		}
		ctx.Gin().XML(http.StatusOK, respData)
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId     //用户ID
	cacheSignKey.Brand = s.brandName //三方厂商名称
	cacheSignKey.ThirdId = gameid    //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, values, nil)
	if duplicateResult != nil && err == nil {
		ctx.Gin().XML(http.StatusOK, duplicateResult)
		logs.Error(s.brandName, " 检测到重复请求 thirdId=", gameid, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, values, nil, nil); e != nil {
			logs.Error(s.brandName, " 设置缓存出错 thirdId=", gameid, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"
	thirdTime := payouttime
	if thirdTime == "" {
		thirdTime = timestamp
	}

	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("SA_single 派彩 获取用户余额失败 userId=", userId, " err=", e)
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_UserNotExist,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			} else {
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   0,
					Error:    SA_Code_SystemError,
				}
				ctx.Gin().XML(http.StatusOK, respData)
			}
			return e
		}

		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", gameid, s.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			if e == daogorm.ErrRecordNotFound {
				// 如果注单不存在，直接返回成功，不需要做任何操作
				logs.Info("SA_single 派彩 订单不存在 gameid=", gameid)
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   userBalance.Amount,
					Error:    SA_Code_Success,
				}
				ctx.Gin().XML(http.StatusOK, respData)
				return nil
			}
			logs.Error("SA_single 派彩 查询订单失败 gameid=", gameid, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 检查订单状态
		if order.DataState != -1 {
			// 如果订单已经结算或取消，直接返回成功
			logs.Info("SA_single 派彩 订单已结算或取消 gameid=", gameid, " DataState=", order.DataState)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_Success,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return nil
		}

		// 计算有效流水
		validBet := rolling
		if validBet > order.BetAmount {
			validBet = order.BetAmount
		}

		// 更新注单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  1,
			"ThirdTime":  thirdTime,
			"ValidBet":   validBet,
			"WinAmount":  amount,
			"BetCtx":     payoutdetails,
			"GameRst":    payoutdetails,
			"BetCtxType": 3,
			"RawData":    decryptedData,
		}).Error
		if e != nil {
			logs.Error("SA_single 派彩 更新订单状态失败 gameid=", gameid, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 复制订单到正式表
		order.DataState = 1
		order.ThirdTime = thirdTime
		order.ValidBet = validBet
		order.WinAmount = amount
		order.BetCtx = payoutdetails
		order.GameRst = payoutdetails
		order.BetCtxType = 3
		order.RawData = decryptedData
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("SA_single 派彩 创建正式表订单失败 gameid=", gameid, " order=", order, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 如果是PlayerWin且金额大于0，则增加用户余额
		if isPlayerWin && amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("SA_single 派彩 增加余额失败 userId=", userId, " gameid=", gameid, " amount=", amount, " err=", e.Error())
				respData := ResponseData{
					Username: username,
					Currency: currency,
					Amount:   userBalance.Amount,
					Error:    SA_Code_SystemError,
				}
				ctx.Gin().XML(http.StatusOK, respData)
				return e
			}
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  userBalance.Amount + amount,
			Reason:       BalanceCReasonSASettle,
			Memo:         s.brandName + " settle,thirdId:" + gameid,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("SA_single 派彩 创建账变记录失败 gameid=", gameid, " amountLog=", amountLog, " error=", e)
			respData := ResponseData{
				Username: username,
				Currency: currency,
				Amount:   userBalance.Amount,
				Error:    SA_Code_SystemError,
			}
			ctx.Gin().XML(http.StatusOK, respData)
			return e
		}

		// 更新用户余额
		userBalance.Amount += amount

		// 返回成功
		respData := ResponseData{
			Username: username,
			Currency: currency,
			Amount:   userBalance.Amount,
			Error:    SA_Code_Success,
		}

		// 记录成功响应日志
		logs.Info("SA_single 派彩 响应成功 gameid=", gameid, " txnid=", txnid, " isPlayerWin=", isPlayerWin, " amount=", amount, " respdata=", respData)

		ctx.Gin().XML(http.StatusOK, respData)
		return nil
	})

	if err != nil {
		logs.Error("SA_single 派彩 事务处理失败 err=", err)
		return
	} else if isPlayerWin && amount > 0 {
		// 推送派奖事件
		if s.thirdGamePush != nil && isPlayerWin && amount > 0 {
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			s.thirdGamePush.PushRewardEvent(5, s.brandName, gameid)
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if s.RefreshUserAmountFunc != nil {
				tmpErr := s.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][SA_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][SA_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// GetLoginUrl 获取登录网址 API URL LoginRequest
func (s *SASingleService) GetLoginUrl(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId   int    `json:"userId"`   // 用户ID
		Username string `json:"username"` // 用户名
		Lang     string `json:"lang"`     // 语言
		GameId   string `json:"gameId"`   // 游戏ID
		IsMobile bool   `json:"isMobile"` // 是否手机端
	}

	type ResponseData struct {
		Code int    `json:"code"` // 状态码
		Msg  string `json:"msg"`  // 消息
		Data struct {
			Url string `json:"url"` // 登录URL
		} `json:"data"` // 数据
	}

	// 解析请求参数
	var reqdata RequestData
	err := ctx.Gin().ShouldBindJSON(&reqdata)
	if err != nil {
		logs.Error("SA_single 获取登录网址 解析请求参数失败: ", err.Error())
		ctx.Gin().JSON(http.StatusBadRequest, map[string]interface{}{
			"code": -1,
			"msg":  "解析请求参数失败: " + err.Error(),
		})
		return
	}

	// 验证必要参数
	if reqdata.UserId <= 0 {
		logs.Error("SA_single 获取登录网址 用户ID不能为空")
		ctx.Gin().JSON(http.StatusBadRequest, map[string]interface{}{
			"code": -1,
			"msg":  "用户ID不能为空",
		})
		return
	}

	// 设置默认语言
	lang := "zh-hans" // 默认简体中文
	if reqdata.Lang != "" {
		// 根据请求语言设置SA游戏语言
		switch reqdata.Lang {
		case "zh-CN":
			lang = "zh-hans" // 简体中文
		case "zh-TW":
			lang = "zh-hant" // 繁体中文
		case "en-US":
			lang = "en-us" // 英语
		case "th-TH":
			lang = "th" // 泰语
		case "vi-VN":
			lang = "vi" // 越南语
		case "id-ID":
			lang = "id" // 印尼语
		case "ja-JP":
			lang = "ja" // 日语
		case "ko-KR":
			lang = "ko" // 韩语
		}
	}

	// 设置平台类型
	mobile := "false"
	if reqdata.IsMobile {
		mobile = "true"
	}

	// 调用SA API获取登录令牌
	params := map[string]string{
		"Username":     strconv.Itoa(reqdata.UserId),
		"CurrencyType": s.currency,
	}

	responseBytes, err := s.CallSAAPI("LoginRequest", params)
	if err != nil {
		logs.Error("SA_single 获取登录网址 调用SA API失败: ", err.Error())
		ctx.Gin().JSON(http.StatusInternalServerError, map[string]interface{}{
			"code": -1,
			"msg":  "调用SA API失败: " + err.Error(),
		})
		return
	}

	// 解析XML响应
	type LoginResponse struct {
		XMLName     xml.Name `xml:"LoginRequestResponse"`
		Token       string   `xml:"Token"`
		DisplayName string   `xml:"DisplayName"`
		ErrorMsgId  int      `xml:"ErrorMsgId"`
		ErrorMsg    string   `xml:"ErrorMsg"`
	}

	var loginResp LoginResponse
	err = xml.Unmarshal(responseBytes, &loginResp)
	if err != nil {
		logs.Error("SA_single 获取登录网址 解析SA API响应失败: ", err.Error(), " response: ", string(responseBytes))
		ctx.Gin().JSON(http.StatusInternalServerError, map[string]interface{}{
			"code": -1,
			"msg":  "解析SA API响应失败: " + err.Error(),
		})
		return
	}

	// 检查登录是否成功
	if loginResp.ErrorMsgId != 0 {
		logs.Error("SA_single 获取登录网址 SA API返回错误: ", loginResp.ErrorMsg, " ErrorMsgId: ", loginResp.ErrorMsgId)
		ctx.Gin().JSON(http.StatusInternalServerError, map[string]interface{}{
			"code": -1,
			"msg":  "SA API返回错误: " + loginResp.ErrorMsg,
		})
		return
	}

	// 构建游戏URL
	username := strconv.Itoa(reqdata.UserId)
	if reqdata.Username != "" {
		username = reqdata.Username
	}

	// 构建游戏URL参数
	urlParams := url.Values{}
	urlParams.Add("username", username)
	urlParams.Add("token", loginResp.Token)
	urlParams.Add("lobby", s.lobbyId)
	urlParams.Add("lang", lang)
	urlParams.Add("mobile", mobile)

	// 如果有指定游戏ID，则直接进入该游戏
	if reqdata.GameId != "" {
		urlParams.Add("options", "defaulttable="+reqdata.GameId)
	}

	// 如果有设置返回URL，则添加
	if s.homeUrl != "" {
		urlParams.Add("returnurl", s.homeUrl)
	}

	// 构建最终URL
	gameUrl := "https://www.sai.slgaming.net/app.aspx?" + urlParams.Encode()

	// 返回登录URL
	respData := ResponseData{
		Code: 0,
		Msg:  "success",
		Data: struct {
			Url string `json:"url"`
		}{
			Url: gameUrl,
		},
	}

	// 记录成功响应日志
	logs.Info("SA_single 获取登录网址 响应成功 userId=", reqdata.UserId, " gameUrl=", gameUrl)

	ctx.Gin().JSON(http.StatusOK, respData)
}

// 获取游戏平台使用的语言代码
func (s *SASingleService) GetGameLangCode(clientLangCode string) string {
	// 语言代码映射
	var langCodeMap = map[string]string{
		"zh-CN": "zh-hans", // 简体中文
		"zh-TW": "zh-hant", // 繁体中文
		"en-US": "en-us",   // 英语
		"th-TH": "th",      // 泰语
		"vi-VN": "vi",      // 越南语
		"id-ID": "id",      // 印尼语
		"ja-JP": "ja",      // 日语
		"ko-KR": "ko",      // 韩语
		"ms-MY": "ms",      // 马来语
		"my-MM": "my",      // 缅甸语
		"pt-PT": "pt",      // 葡萄牙语
		"pt-BR": "pt-br",   // 巴西葡萄牙语
		"es-ES": "es",      // 西班牙语
		"hi-IN": "hi",      // 印地语
		"bn-BD": "bn",      // 孟加拉语
		"te-IN": "te",      // 泰卢固语
		"ar-SA": "ar-001",  // 阿拉伯语(沙特)
		"ar-AE": "ar-001",  // 阿拉伯语(阿联酋)
		"ar-EG": "ar-001",  // 阿拉伯语(埃及)
		"fa-IR": "fa-ir",   // 波斯语
	}
	// 如果存在映射关系，则返回映射后的语言代码
	if langCode, exists := langCodeMap[clientLangCode]; exists {
		return langCode
	}

	// 默认返回简体中文
	return "zh-hans"
}

// Login 登录游戏 (对应api文档 5.2.1.LoginRequest 登录 和 7.1. 获取游戏登录网址api 组合实现)
func (s *SASingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏ID
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
		IsMobile bool   `json:"IsMobile"`                   // 是否手机端
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("SA_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	// 获取用户Token
	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("SA_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeySA, userId); err != nil {
		logs.Error("SA_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, s.brandName, reqdata.GameId); err != nil {
		logs.Error("SA 登录游戏 权限检查错误 userId=", userId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("SA 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 判断游戏是否可用
	_, err, errcode = base.IsGameAvailable(s.brandName, reqdata.GameId)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 设置语言
	lang := s.GetGameLangCode(reqdata.LangCode)

	// 设置平台类型
	mobile := "false"
	if reqdata.IsMobile {
		mobile = "true"
	}

	// 调用SA API获取登录令牌
	params := map[string]string{
		"Username":     strconv.Itoa(userId),
		"CurrencyType": s.currency,
	}

	responseBytes, err := s.CallSAAPI("LoginRequest", params)
	if err != nil {
		logs.Error("SA_single 登录游戏 调用SA API失败: ", err.Error())
		ctx.RespErrString(true, &errcode, "调用SA API失败: "+err.Error())
		return
	}

	// 解析XML响应
	type LoginResponse struct {
		XMLName     xml.Name `xml:"LoginRequestResponse"`
		Token       string   `xml:"Token"`
		DisplayName string   `xml:"DisplayName"`
		ErrorMsgId  int      `xml:"ErrorMsgId"`
		ErrorMsg    string   `xml:"ErrorMsg"`
	}

	var loginResp LoginResponse
	err = xml.Unmarshal(responseBytes, &loginResp)
	if err != nil {
		logs.Error("SA_single 登录游戏 解析SA API响应失败: ", err.Error(), " response: ", string(responseBytes))
		ctx.RespErrString(true, &errcode, "解析SA API响应失败: "+err.Error())
		return
	}

	// 检查登录是否成功
	if loginResp.ErrorMsgId != 0 {
		logs.Error("SA_single 登录游戏 SA API返回错误: ", loginResp.ErrorMsg, " ErrorMsgId: ", loginResp.ErrorMsgId)
		ctx.RespErrString(true, &errcode, "SA API返回错误: "+loginResp.ErrorMsg)
		return
	}

	// 构建游戏URL
	// 设置显示在客户端内的用户名称
	username := strconv.Itoa(userId)
	//if reqdata.DisplayName != "" {
	//	username = reqdata.DisplayName
	//}

	// 构建游戏URL参数
	urlParams := url.Values{}
	urlParams.Add("username", username)
	urlParams.Add("token", loginResp.Token)
	urlParams.Add("lobby", s.lobbyId)
	urlParams.Add("lang", lang)
	urlParams.Add("mobile", mobile)

	// 构建options参数
	var optionsList []string

	// 如果有指定游戏ID，则直接进入该游戏
	if reqdata.GameId != "" {
		optionsList = append(optionsList, "defaulttable="+reqdata.GameId)
	}

	// 添加hidelogo选项，隐藏SA标志/运营商标志
	optionsList = append(optionsList, "hidelogo=1")
	// 如果是在App中使用WebView打开，添加webview选项
	optionsList = append(optionsList, "webview=1")

	// 如果有options选项，添加到URL参数中
	if len(optionsList) > 0 {
		urlParams.Add("options", strings.Join(optionsList, ","))
	}

	// 如果有设置返回URL，则添加
	if reqdata.HomeUrl != "" {
		urlParams.Add("returnurl", reqdata.HomeUrl)
	} else if s.homeUrl != "" {
		urlParams.Add("returnurl", s.homeUrl)
	}

	// 构建最终URL
	gameUrl := s.gameUrl + "?" + urlParams.Encode()

	// 记录日 志
	logs.Info("SA_single 登录游戏  成功生成游戏URL userId=", userId,
		" GameId=", reqdata.GameId,
		" lang=", lang,
		" mobile=", mobile,
		" options=", strings.Join(optionsList, ","))

	// 记录成功响应日志
	logs.Info("SA_single 登录游戏 响应成功 userId=", userId, " GameId=", reqdata.GameId, " gameUrl=", gameUrl)

	// 返回登录URL
	ctx.RespOK(gameUrl)
	return
}
