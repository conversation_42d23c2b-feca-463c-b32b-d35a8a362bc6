package datapush

import (
	"context"
	"fmt"
	"strings"
	"time"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

// SendDepositCreateEvent 发送充值订单创建事件到ThinkingData
func SendDepositCreateEvent(rechargeOrder *model.XRecharge, payMethod *model.XFinanceMethod) {
	// 获取 ThinkingData 服务实例
	service := GetService()

	// 构建充值创建事件数据
	depositEvent := DepositCreateEvent{
		BaseEventProperties: NewBaseEventProperties("", 8), // 使用默认+8时区
		OrderID:             fmt.Sprint(rechargeOrder.ID),
		DepositMethod:       payMethod.Brand,
		DepositAmount:       rechargeOrder.Amount,
		AssetType:           strings.ToUpper(rechargeOrder.Symbol),
		Currency:            strings.ToUpper(rechargeOrder.Symbol),
		IsFirstDeposit:      isFirstDeposit(int32(rechargeOrder.UserID)),
	}

	// 使用正确的服务方法发送事件
	err := service.SendDepositCreateEvent(
		fmt.Sprint(rechargeOrder.UserID), // accountID
		fmt.Sprint(rechargeOrder.UserID), // distinctID (这里使用用户ID作为设备ID)
		depositEvent,                     // 事件数据
	)

	if err != nil {
		logs.Error("发送充值创建事件到ThinkingData失败:", err,
			"用户ID:", rechargeOrder.UserID,
			"订单ID:", rechargeOrder.ID,
			"充值金额:", rechargeOrder.Amount,
			"支付方式:", payMethod.Brand,
			"币种:", rechargeOrder.Symbol)
		return
	}

	// 注意：BatchSize=1时数据会立即发送，无需手动刷新

	logs.Info("充值创建事件发送到ThinkingData成功:",
		"用户ID:", rechargeOrder.UserID,
		"订单ID:", rechargeOrder.ID,
		"充值金额:", rechargeOrder.Amount,
		"支付方式:", payMethod.Brand,
		"币种:", rechargeOrder.Symbol,
		"是否首次充值:", depositEvent.IsFirstDeposit)
}

// isFirstDeposit 检查是否为首次充值
func isFirstDeposit(userId int32) bool {
	// 查询用户历史充值记录数量
	var count int64
	err := server.Db().GormDao().Table("x_recharge").
		Where("UserId = ? AND IsFirst = 1", userId). // 1=成功
		Count(&count).Error

	if err != nil {
		logs.Error("查询用户充值记录失败:", err, "用户ID:", userId)
		return false // 出错时默认不是首次充值
	}

	// 如果记录数为0，则为首次充值
	return count == 0
}

// SendDepositSuccessEvent 发送充值成功事件到ThinkingData
func SendDepositSuccessEvent(order *model.XRecharge, payMethod *model.XFinanceMethod) {
	// 获取 ThinkingData 服务实例
	service := GetService()

	// 确定支付方式名称
	var depositMethod string
	if payMethod != nil {
		depositMethod = payMethod.Brand
	} else {
		// 根据订单信息判断支付类型
		if order.PayID > 0 {
			depositMethod = "未知法币支付方式"
		} else {
			depositMethod = "虚拟币支付"
		}
	}

	// 检查是否为首次充值
	isFirst := isFirstDeposit(order.UserID)

	// 构建充值成功事件数据
	depositEvent := DepositSuccessEvent{
		BaseEventProperties: NewBaseEventProperties("", 8), // 使用默认+8时区
		OrderID:             fmt.Sprint(order.ID),
		DepositMethod:       depositMethod,
		DepositAmount:       order.Amount,
		GetAmount:           order.RealAmount, // 实际到账金额
		AssetType:           strings.ToUpper(order.Symbol),
		Currency:            strings.ToUpper(order.Symbol),
		IsFirstDeposit:      isFirst,
		IsBonus:             order.RealAmount > order.Amount, // 如果到账金额大于充值金额，说明有奖金
	}

	// 使用正确的服务方法发送事件
	err := service.SendDepositSuccessEvent(
		fmt.Sprint(order.UserID), // accountID
		fmt.Sprint(order.UserID), // distinctID (这里使用用户ID作为设备ID)
		depositEvent,             // 事件数据
	)

	if err != nil {
		logs.Error("发送充值成功事件到ThinkingData失败:", err,
			"用户ID:", order.UserID,
			"订单ID:", order.ID,
			"充值金额:", order.Amount,
			"到账金额:", order.RealAmount,
			"支付方式:", depositMethod,
			"币种:", order.Symbol)
		return
	}

	// 同时更新用户充值相关属性
	util := GetThinkingDataUtil()
	accountID := fmt.Sprint(order.UserID)
	distinctID := fmt.Sprint(order.UserID)
	err = util.SetUserPropertiesOnDeposit(accountID, distinctID, order.UserID, order.RealAmount, isFirst)
	if err != nil {
		logs.Error("ThinkingData充值用户属性设置失败:", err,
			"用户ID:", order.UserID,
			"充值金额:", order.RealAmount,
			"是否首次充值:", isFirst)
		// 不返回错误，因为事件已经发送成功
	}

	// 注意：BatchSize=1时数据会立即发送，无需手动刷新

	logs.Info("充值成功事件发送到ThinkingData成功:",
		"用户ID:", order.UserID,
		"订单ID:", order.ID,
		"充值金额:", order.Amount,
		"到账金额:", order.RealAmount,
		"支付方式:", depositMethod,
		"币种:", order.Symbol,
		"是否首次充值:", depositEvent.IsFirstDeposit,
		"是否有奖金:", depositEvent.IsBonus)
}

// SendDepositSuccessEventAuto 自动查询支付方式并发送充值成功事件到ThinkingData
func SendDepositSuccessEventAuto(order *model.XRecharge) {
	var payMethod *model.XFinanceMethod

	// 如果是法币支付，尝试查询支付方式信息
	if order.PayID > 0 {
		financeMethodTb := server.DaoxHashGame().XFinanceMethod
		financeMethodDb := server.DaoxHashGame().XFinanceMethod.WithContext(context.Background())
		payMethod, _ = financeMethodDb.Where(financeMethodTb.ID.Eq(order.PayID)).First()
	}
	// 虚拟币支付时 payMethod 为 nil

	// 调用原函数处理
	SendDepositSuccessEvent(order, payMethod)
}

// SendLoginEvent 发送登录事件到ThinkingData
func SendLoginEvent(user *model.XUser, loginType, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建登录事件数据，使用默认时区偏移量+8
	loginEvent := LoginEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		LoginType:           loginType,
	}

	// 发送登录事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendLoginEvent(accountID, distinctID, loginEvent)
	if err != nil {
		logs.Error("ThinkingData登录事件发送失败:", err,
			"用户ID:", user.UserID,
			"登录类型:", loginType,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnLogin(accountID, distinctID, user.UserID, loginType)
	if err != nil {
		logs.Error("ThinkingData登录用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"登录类型:", loginType)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData登录事件发送成功:",
		"用户ID:", user.UserID,
		"登录类型:", loginType)

	return nil
}

// SendRegisterEvent 发送注册事件到ThinkingData
func SendRegisterEvent(user *model.XUser, registerType, clientIP, email, phone string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建注册事件数据，使用默认时区偏移量+8
	registerEvent := RegisterEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		RegisterType:        registerType,
		Email:               email,
		Phone:               phone,
		ReferralCode:        "", // 如果有推荐码逻辑，可以在这里添加
	}

	// 发送注册事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendRegisterEvent(accountID, distinctID, registerEvent)
	if err != nil {
		logs.Error("ThinkingData注册事件发送失败:", err,
			"用户ID:", user.UserID,
			"注册类型:", registerType,
			"IP:", clientIP,
			"邮箱:", email,
			"手机:", phone)
		return err
	}

	// 同时设置用户注册时的初始属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnRegister(accountID, distinctID, user, registerType, email, phone, "")
	if err != nil {
		logs.Error("ThinkingData注册用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"注册类型:", registerType,
			"邮箱:", email,
			"手机:", phone)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData注册事件发送成功:",
		"用户ID:", user.UserID,
		"注册类型:", registerType,
		"邮箱:", email,
		"手机:", phone)

	return nil
}

// SendPhoneBindEvent 发送手机绑定事件到ThinkingData
func SendPhoneBindEvent(user *model.XUser, clientIP, phoneNumber string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建手机绑定事件数据，使用默认时区偏移量+8
	phoneBindEvent := PhoneNumberBindEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
	}

	// 发送手机绑定事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendPhoneNumberBindEvent(accountID, distinctID, phoneBindEvent)
	if err != nil {
		logs.Error("ThinkingData手机绑定事件发送失败:", err,
			"用户ID:", user.UserID,
			"手机号:", phoneNumber,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户手机绑定属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnPhoneBind(accountID, distinctID, user.UserID, phoneNumber)
	if err != nil {
		logs.Error("ThinkingData手机绑定用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"手机号:", phoneNumber)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData手机绑定事件发送成功:",
		"用户ID:", user.UserID,
		"手机号:", phoneNumber)

	return nil
}

// SendActivityRewardEvent 发送活动奖励获取事件到ThinkingData
func SendActivityRewardEvent(user *model.XUser, activityID int, activityName string, rewardAmount float64, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建活动奖励事件数据，使用默认时区偏移量+8
	activityEvent := ActivityRewardsGetEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		ActivityID:          fmt.Sprint(activityID),
		ActivityName:        activityName,
		ActivityType:        "reward", // 可以根据具体活动类型调整
		RewardType:          "bonus",  // 奖励类型：奖金
		RewardAmount:        rewardAmount,
		Currency:            "USDT",
		RewardStatus:        "success", // 成功获得奖励
		RewardMethod:        "auto",    // 自动发放
	}

	// 发送活动奖励事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendActivityRewardsGetEvent(accountID, distinctID, activityEvent)
	if err != nil {
		logs.Error("ThinkingData活动奖励事件发送失败:", err,
			"用户ID:", user.UserID,
			"活动ID:", activityID,
			"活动名称:", activityName,
			"奖励金额:", rewardAmount,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户活动参与属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnActivity(accountID, distinctID, user.UserID, activityName, rewardAmount)
	if err != nil {
		logs.Error("ThinkingData活动奖励用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"活动名称:", activityName,
			"奖励金额:", rewardAmount)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData活动奖励事件发送成功:",
		"用户ID:", user.UserID,
		"活动ID:", activityID,
		"活动名称:", activityName,
		"奖励金额:", rewardAmount)

	return nil
}

// SendInviteSuccessEvent 发送邀请成功事件到ThinkingData
func SendInviteSuccessEvent(inviterUser *model.XUser, inviteeUserID int32, rewardAmount float64, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建邀请成功事件数据
	inviteEvent := InviteSuccessEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8), // 使用默认+8时区
		InviteCode:          fmt.Sprint(inviterUser.UserID),      // 使用邀请人用户ID作为邀请码
		InviteeUserID:       fmt.Sprint(inviteeUserID),           // 被邀请用户ID
		InviteLevel:         1,                                   // 邀请层级，一般为1级
		RewardAmount:        rewardAmount,                        // 奖励金额
		Currency:            "USDT",                              // 币种
		InviteType:          "first_deposit",                     // 邀请类型：首充
	}

	// 发送邀请成功事件
	accountID := fmt.Sprint(inviterUser.UserID)
	distinctID := fmt.Sprint(inviterUser.UserID)

	err := service.SendInviteSuccessEvent(accountID, distinctID, inviteEvent)
	if err != nil {
		logs.Error("ThinkingData邀请成功事件发送失败:", err,
			"邀请人ID:", inviterUser.UserID,
			"被邀请人ID:", inviteeUserID,
			"奖励金额:", rewardAmount,
			"IP:", clientIP)
		return err
	}

	// 同时更新邀请人的用户属性
	util := GetThinkingDataUtil()

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(inviterUser.UserID)

	inviteProperties := map[string]interface{}{
		"last_invite_time":   time.Now().UTC(),
		"last_invite_reward": rewardAmount,
		"last_active_time":   time.Now().UTC(),
		"seller":             seller,
		"channel":            channel,
	}

	// 累加邀请相关数据
	addProperties := map[string]interface{}{
		"total_invite_count":  1,
		"total_invite_reward": rewardAmount,
	}

	// 先设置基本属性
	err = util.SetUserProperties(accountID, distinctID, inviteProperties)
	if err != nil {
		logs.Error("ThinkingData邀请成功用户属性设置失败:", err,
			"邀请人ID:", inviterUser.UserID,
			"奖励金额:", rewardAmount)
		// 不返回错误，因为事件已经发送成功
	}

	// 再累加邀请数据
	err = util.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		logs.Error("ThinkingData邀请成功累加属性设置失败:", err,
			"邀请人ID:", inviterUser.UserID)
	}

	logs.Info("ThinkingData邀请成功事件发送成功:",
		"邀请人ID:", inviterUser.UserID,
		"被邀请人ID:", inviteeUserID,
		"奖励金额:", rewardAmount)

	return nil
}

// SendGameResultEvent 发送游戏结果事件到ThinkingData
// zoneOffset 参数是可选的，如果不传递则使用默认值+8
func SendGameResultEvent(user *model.XUser, gameID, gameName, gameType, gameMethod string, betAmount, winLossAmount float64, clientIP string, zoneOffset ...int) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 设置默认时区偏移量
	offset := 8 // 默认+8时区
	if len(zoneOffset) > 0 {
		offset = zoneOffset[0]
	}

	// 构建游戏结果事件数据
	gameResultEvent := GameResultEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, offset),
		GameID:              gameID,
		GameName:            gameName,
		GameType:            gameType,
		GameMethod:          gameMethod,
		BetAmount:           betAmount,
		WinLossAmount:       winLossAmount,
		Currency:            "USDT",
	}

	// 发送游戏结果事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendGameResultEvent(accountID, distinctID, gameResultEvent)
	if err != nil {
		logs.Error("ThinkingData游戏结果事件发送失败:", err,
			"用户ID:", user.UserID,
			"游戏ID:", gameID,
			"游戏名称:", gameName,
			"投注金额:", betAmount,
			"盈亏金额:", winLossAmount,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户游戏相关属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnBet(accountID, distinctID, user.UserID, betAmount, winLossAmount, gameType, false)
	if err != nil {
		logs.Error("ThinkingData游戏结果用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"游戏类型:", gameType,
			"投注金额:", betAmount,
			"盈亏金额:", winLossAmount)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData游戏结果事件发送成功:",
		"用户ID:", user.UserID,
		"游戏ID:", gameID,
		"游戏名称:", gameName,
		"投注金额:", betAmount,
		"盈亏金额:", winLossAmount)

	return nil
}

// SendRebateGiveEvent 发送返水发放事件到ThinkingData
func SendRebateGiveEvent(user *model.XUser, rebateType string, rebateAmount float64, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建返水发放事件数据，使用默认时区偏移量+8
	rebateEvent := RebateGiveEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		RebateType:          rebateType,
		RebateAmount:        rebateAmount,
		Currency:            "USDT",
	}

	// 发送返水发放事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendRebateGiveEvent(accountID, distinctID, rebateEvent)
	if err != nil {
		logs.Error("ThinkingData返水发放事件发送失败:", err,
			"用户ID:", user.UserID,
			"返水类型:", rebateType,
			"返水金额:", rebateAmount,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户返水相关属性
	util := GetThinkingDataUtil()

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(user.UserID)

	rebateProperties := map[string]interface{}{
		"last_rebate_time":   time.Now().UTC(),
		"last_rebate_type":   rebateType,
		"last_rebate_amount": rebateAmount,
		"last_active_time":   time.Now().UTC(),
		"seller":             seller,
		"channel":            channel,
	}

	// 累加返水相关数据
	addProperties := map[string]interface{}{
		"total_rebate_amount": rebateAmount,
		"rebate_count":        1,
	}

	// 先设置基本属性
	err = util.SetUserProperties(accountID, distinctID, rebateProperties)
	if err != nil {
		logs.Error("ThinkingData返水发放用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"返水金额:", rebateAmount)
		// 不返回错误，因为事件已经发送成功
	}

	// 再累加返水数据
	err = util.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		logs.Error("ThinkingData返水发放累加属性设置失败:", err,
			"用户ID:", user.UserID)
	}

	logs.Info("ThinkingData返水发放事件发送成功:",
		"用户ID:", user.UserID,
		"返水类型:", rebateType,
		"返水金额:", rebateAmount)

	return nil
}

// SendRewardGiveEvent 发送返奖发放事件到ThinkingData
func SendRewardGiveEvent(UserId int32, RewardType string, RewardAmount float64, Currency string, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建返奖发放事件数据，使用默认时区偏移量+8
	rewardEvent := RewardGiveEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		UserId:              UserId,
		RewardType:          RewardType,
		RewardAmount:        RewardAmount,
		Currency:            Currency,
	}

	// 发送返奖发放事件
	accountID := fmt.Sprint(UserId)
	distinctID := fmt.Sprint(UserId)

	err := service.SendRewardGiveEvent(accountID, distinctID, rewardEvent)
	if err != nil {
		logs.Error("ThinkingData返奖发放事件发送失败:", err,
			"用户ID:", UserId,
			"返奖类型:", RewardType,
			"返奖金额:", RewardAmount,
			"币种:", Currency,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户返奖相关属性
	util := GetThinkingDataUtil()

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(UserId)

	rewardProperties := map[string]interface{}{
		"last_reward_time":   time.Now().UTC(),
		"last_reward_type":   RewardType,
		"last_reward_amount": RewardAmount,
		"last_active_time":   time.Now().UTC(),
		"seller":             seller,
		"channel":            channel,
	}

	// 累加返奖相关数据
	addProperties := map[string]interface{}{
		"total_reward_amount": RewardAmount,
		"reward_count":        1,
	}

	// 先设置基本属性
	err = util.SetUserProperties(accountID, distinctID, rewardProperties)
	if err != nil {
		logs.Error("ThinkingData返奖发放用户属性设置失败:", err,
			"用户ID:", UserId,
			"返奖金额:", RewardAmount)
		// 不返回错误，因为事件已经发送成功
	}

	// 再累加返奖数据
	err = util.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		logs.Error("ThinkingData返奖发放累加属性设置失败:", err,
			"用户ID:", UserId)
	}

	logs.Info("ThinkingData返奖发放事件发送成功:",
		"用户ID:", UserId,
		"返奖类型:", RewardType,
		"返奖金额:", RewardAmount,
		"币种:", Currency)

	return nil
}

// isFirstBet 检查用户是否为首次投注
func isFirstBet(userId int32) bool {
	// Redis缓存键
	cacheKey := fmt.Sprintf("user_first_bet_status:%d", userId)

	// 先从Redis中获取缓存状态
	cachedStatus := server.Redis().Get(cacheKey)
	if cachedStatus != "" {
		// 如果缓存中有数据，直接返回
		if cachedStatus == "first" {
			return true
		} else if cachedStatus == "not_first" {
			return false
		}
	}

	// 缓存中没有数据，查询数据库
	logs.Info("isFirstBet 缓存未命中，查询数据库: userId=%d", userId)

	// 检查所有投注相关表 - 使用原生SQL查询
	tables := []string{
		"x_order",
		"x_third_dianzhi_pre_order",
		"x_third_live_pre_order",
		"x_third_lottery_pre_order",
		"x_third_qipai_pre_order",
		"x_third_quwei_pre_order",
		"x_third_sport_pre_order",
		"x_third_texas",
	}

	isFirst := true // 默认是首次投注

	for _, table := range tables {
		var count int64
		err := server.Db().GormDao().Table(table).
			Where("UserId = ?", userId).
			Count(&count).Error
		if err != nil {
			// 如果查询出错，保守处理，认为不是首次投注
			logs.Error("isFirstBet 查询表失败: table=%s, userId=%d, err=%v", table, userId, err)
			isFirst = false
			break
		}
		if count > 0 {
			// 如果在任何表中找到记录，说明不是首次投注
			logs.Info("isFirstBet 找到投注记录: table=%s, userId=%d, count=%d", table, userId, count)
			isFirst = false
			break
		}
	}

	// 将结果永久缓存到Redis中
	var cacheValue string
	if isFirst {
		cacheValue = "first"
	} else {
		cacheValue = "not_first"
	}

	err := server.Redis().Set(cacheKey, cacheValue)
	if err != nil {
		logs.Error("isFirstBet 设置Redis缓存失败: userId=%d, err=%v", userId, err)
	} else {
		logs.Info("isFirstBet 设置Redis缓存成功: userId=%d, status=%s", userId, cacheValue)
	}

	return isFirst
}

// updateUserFirstBetStatus 更新用户首次投注状态为非首次投注
func updateUserFirstBetStatus(userId int32) {
	cacheKey := fmt.Sprintf("user_first_bet_status:%d", userId)

	// 将用户状态更新为非首次投注
	err := server.Redis().Set(cacheKey, "not_first")
	if err != nil {
		logs.Error("updateUserFirstBetStatus 更新Redis缓存失败: userId=%d, err=%v", userId, err)
	} else {
		logs.Info("updateUserFirstBetStatus 更新Redis缓存成功: userId=%d, status=not_first", userId)
	}
}

// SendBetResultEvent 发送投注结果事件到ThinkingData
func SendBetResultEvent(userId int32, orderId string, gameId string, betMethod string, betType string, betAmount float64, betStatus string, winLossAmount float64, currency string, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("SendBetResultEvent ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 检查是否为首次投注
	isFirst := isFirstBet(userId)

	// 构建投注结果事件数据，使用默认时区偏移量+8
	betResultEvent := BetResultEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8),
		OrderID:             fmt.Sprint(orderId),
		GameID:              gameId,
		BetMethod:           betMethod,
		BetType:             betType,
		BetAmount:           betAmount,
		BetStatus:           betStatus,
		WinLossAmount:       winLossAmount,
		Currency:            currency,
		IsFree:              false,
		IsFirstBet:          isFirst,
	}

	// 发送投注结果事件
	accountID := fmt.Sprint(userId)
	distinctID := fmt.Sprint(userId)

	err := service.SendBetResultEvent(accountID, distinctID, betResultEvent)
	if err != nil {
		logs.Error("ThinkingData投注结果事件发送失败:", err,
			"用户ID:", userId,
			"订单ID:", orderId,
			"游戏ID:", gameId,
			"投注金额:", betAmount,
			"盈亏金额:", winLossAmount)
		return err
	}

	// 如果是首次投注，更新用户状态为非首次投注
	if isFirst {
		updateUserFirstBetStatus(userId)
	}

	// 同时更新用户投注结果相关属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnBet(accountID, distinctID, userId, betAmount, winLossAmount, betType, false)
	if err != nil {
		logs.Error("ThinkingData投注结果用户属性设置失败:", err,
			"用户ID:", userId,
			"投注金额:", betAmount,
			"盈亏金额:", winLossAmount,
			"投注类型:", betType)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingData投注结果事件发送成功:",
		"用户ID:", userId,
		"订单ID:", orderId,
		"游戏ID:", gameId,
		"投注金额:", betAmount,
		"盈亏金额:", winLossAmount,
		"是否首次投注:", isFirst)

	return nil
}

// SendCodeEvent 发送打码事件到ThinkingData
func SendCodeEvent(user *model.XUser, gameID, gameName, gameType string, codeAmount, gameWin, validCodeAmount, balanceBefore, balanceAfter float64, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建打码事件数据
	codeEvent := CodeEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8), // 使用默认+8时区
		GameID:              gameID,
		GameName:            gameName,
		GameType:            gameType,
		CodeAmount:          codeAmount,
		GameWin:             gameWin,
		ValidCodeAmount:     validCodeAmount,
		BalanceBefore:       balanceBefore,
		BalanceAfter:        balanceAfter,
	}

	// 发送打码事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendCodeEvent(accountID, distinctID, codeEvent)
	if err != nil {
		logs.Error("ThinkingData打码事件发送失败:", err,
			"用户ID:", user.UserID,
			"游戏ID:", gameID,
			"游戏名称:", gameName,
			"游戏类型:", gameType,
			"打码量:", codeAmount,
			"开奖派彩:", gameWin,
			"有效打码量:", validCodeAmount,
			"变动前余额:", balanceBefore,
			"变动后余额:", balanceAfter,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户打码相关属性
	util := GetThinkingDataUtil()

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(user.UserID)

	codeProperties := map[string]interface{}{
		"last_code_time":   time.Now().UTC(),
		"last_code_amount": codeAmount,
		"last_game_type":   gameType,
		"last_active_time": time.Now().UTC(),
		"balance_before":   balanceBefore,
		"balance_after":    balanceAfter,
		"seller":           seller,
		"channel":          channel,
	}

	// 累加打码相关数据
	addProperties := map[string]interface{}{
		"total_code_amount":       codeAmount,
		"total_valid_code_amount": validCodeAmount,
		"code_count":              1,
	}

	// 先设置基本属性
	err = util.SetUserProperties(accountID, distinctID, codeProperties)
	if err != nil {
		logs.Error("ThinkingData打码用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"打码金额:", codeAmount)
		// 不返回错误，因为事件已经发送成功
	}

	// 再累加打码数据
	err = util.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		logs.Error("ThinkingData打码累加属性设置失败:", err,
			"用户ID:", user.UserID)
	}

	logs.Info("ThinkingData打码事件发送成功:",
		"用户ID:", user.UserID,
		"游戏ID:", gameID,
		"游戏名称:", gameName,
		"打码量:", codeAmount,
		"有效打码量:", validCodeAmount)

	return nil
}

// SendCommissionReceiveEvent 发送佣金领取事件到ThinkingData
func SendCommissionReceiveEvent(user *model.XUser, commissionType, commissionPeriod, sourceUserID string, commissionAmount, commissionRate float64, commissionLevel int, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建佣金领取事件数据
	commissionEvent := CommissionReceiveEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8), // 使用默认+8时区
		CommissionType:      commissionType,
		CommissionAmount:    commissionAmount,
		Currency:            "USDT",
		CommissionPeriod:    commissionPeriod,
		SourceUserID:        sourceUserID,
		CommissionLevel:     commissionLevel,
		CommissionRate:      commissionRate,
	}

	// 发送佣金领取事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendCommissionReceiveEvent(accountID, distinctID, commissionEvent)
	if err != nil {
		logs.Error("ThinkingData佣金领取事件发送失败:", err,
			"用户ID:", user.UserID,
			"佣金类型:", commissionType,
			"佣金金额:", commissionAmount,
			"佣金周期:", commissionPeriod,
			"来源用户ID:", sourceUserID,
			"佣金层级:", commissionLevel,
			"佣金比例:", commissionRate,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户佣金相关属性
	util := GetThinkingDataUtil()

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(user.UserID)

	commissionProperties := map[string]interface{}{
		"last_commission_time":   time.Now().UTC(),
		"last_commission_type":   commissionType,
		"last_commission_amount": commissionAmount,
		"last_commission_rate":   commissionRate,
		"commission_level":       commissionLevel,
		"last_active_time":       time.Now().UTC(),
		"seller":                 seller,
		"channel":                channel,
	}

	// 累加佣金相关数据
	addProperties := map[string]interface{}{
		"total_commission_amount": commissionAmount,
		"commission_count":        1,
	}

	// 先设置基本属性
	err = util.SetUserProperties(accountID, distinctID, commissionProperties)
	if err != nil {
		logs.Error("ThinkingData佣金领取用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"佣金金额:", commissionAmount)
		// 不返回错误，因为事件已经发送成功
	}

	// 再累加佣金数据
	err = util.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		logs.Error("ThinkingData佣金领取累加属性设置失败:", err,
			"用户ID:", user.UserID)
	}

	logs.Info("ThinkingData佣金领取事件发送成功:",
		"用户ID:", user.UserID,
		"佣金类型:", commissionType,
		"佣金金额:", commissionAmount,
		"来源用户ID:", sourceUserID,
		"佣金层级:", commissionLevel)

	return nil
}

// SendWithdrawCreateEvent 发送提现创建事件到ThinkingData
func SendWithdrawCreateEvent(token *server.TokenData, reqdata struct {
	Net      string  `validate:"required"`
	Address  string  `validate:"required"`
	Amount   float64 `validate:"required"`
	Password string
	Symbol   string
}, data map[string]interface{}) {
	// 构建提现创建事件数据
	withdrawEvent := WithdrawCreateEvent{
		BaseEventProperties: NewBaseEventProperties("", 8),        // 使用默认+8时区
		OrderID:             fmt.Sprint(data["Id"]),               // 提现订单ID
		WithdrawType:        reqdata.Net,                          // 提现方式（网络类型）
		WithdrawAmount:      reqdata.Amount,                       // 提现金额
		Commission:          0,                                    // 手续费，暂时设为0
		ReceivedAmount:      reqdata.Amount,                       // 到账金额，暂时等于提现金额
		AssetType:           strings.ToUpper(reqdata.Symbol),      // 资产类型
		Currency:            strings.ToUpper(reqdata.Symbol),      // 币种
		IsFirstWithdraw:     isFirstWithdraw(int32(token.UserId)), // 是否首次提现
	}

	// 获取 ThinkingData 服务实例
	service := GetService()

	// 使用正确的服务方法发送事件
	err := service.SendWithdrawCreateEvent(
		fmt.Sprint(token.UserId), // accountID
		fmt.Sprint(token.UserId), // distinctID (这里使用用户ID作为设备ID)
		withdrawEvent,            // 事件数据
	)

	if err != nil {
		logs.Error("发送提现创建事件到ThinkingData失败:", err,
			"用户ID:", token.UserId,
			"订单ID:", data["Id"],
			"提现金额:", reqdata.Amount,
			"提现方式:", reqdata.Net,
			"币种:", reqdata.Symbol)
		return
	}

	// 注意：BatchSize=1时数据会立即发送，无需手动刷新

	logs.Info("提现创建事件发送到ThinkingData成功:",
		"用户ID:", token.UserId,
		"订单ID:", data["Id"],
		"提现金额:", reqdata.Amount,
		"提现方式:", reqdata.Net,
		"币种:", reqdata.Symbol,
		"是否首次提现:", withdrawEvent.IsFirstWithdraw)
}

// isFirstWithdraw 检查是否为首次提现
// 条件：IsFirst=1 和 State=6 表示第一次提款成功
func isFirstWithdraw(userId int32) bool {
	// 查询用户是否有首次提现成功的记录
	var count int64
	err := server.Db().GormDao().Table("x_withdraw").
		Where("UserId = ? AND IsFirst = 1 AND State = 6", userId). // IsFirst=1: 首次提现, State=6: 提现成功
		Count(&count).Error

	if err != nil {
		logs.Error("查询用户首次提现记录失败:", err, "用户ID:", userId)
		return false // 出错时默认不是首次提现
	}

	// 如果有首次提现成功记录，则为首次提现
	return count > 0
}

// SendWithdrawSuccessEvent 发送提现成功事件到ThinkingData
func SendWithdrawSuccessEvent(order *model.XWithdraw, withdrawType string) {
	// 获取 ThinkingData 服务实例
	service := GetService()

	// 检查是否为首次提现
	isFirst := isFirstWithdraw(order.UserID)

	// 构建提现成功事件数据
	withdrawEvent := WithdrawSuccessEvent{
		BaseEventProperties: NewBaseEventProperties("", 8), // 使用默认+8时区
		OrderID:             fmt.Sprint(order.ID),
		WithdrawType:        withdrawType,
		WithdrawAmount:      order.Amount,
		Commission:          order.NetFee,     // 手续费
		ReceivedAmount:      order.RealAmount, // 实际到账金额
		AssetType:           strings.ToUpper(order.Symbol),
		Currency:            strings.ToUpper(order.Symbol),
		IsFirstWithdraw:     isFirst,
	}

	// 使用正确的服务方法发送事件
	err := service.SendWithdrawSuccessEvent(
		fmt.Sprint(order.UserID), // accountID
		fmt.Sprint(order.UserID), // distinctID (这里使用用户ID作为设备ID)
		withdrawEvent,            // 事件数据
	)

	if err != nil {
		logs.Error("发送提现成功事件到ThinkingData失败:", err,
			"用户ID:", order.UserID,
			"订单ID:", order.ID,
			"提现金额:", order.Amount,
			"到账金额:", order.RealAmount,
			"手续费:", order.NetFee,
			"提现方式:", withdrawType,
			"币种:", order.Symbol)
		return
	}

	// 同时更新用户提现相关属性
	util := GetThinkingDataUtil()
	accountID := fmt.Sprint(order.UserID)
	distinctID := fmt.Sprint(order.UserID)
	err = util.SetUserPropertiesOnWithdraw(accountID, distinctID, order.UserID, order.RealAmount, isFirst)
	if err != nil {
		logs.Error("ThinkingData提现用户属性设置失败:", err,
			"用户ID:", order.UserID,
			"提现金额:", order.RealAmount,
			"是否首次提现:", isFirst)
		// 不返回错误，因为事件已经发送成功
	}

	// 注意：BatchSize=1时数据会立即发送，无需手动刷新

	logs.Info("提现成功事件发送到ThinkingData成功:",
		"用户ID:", order.UserID,
		"订单ID:", order.ID,
		"提现金额:", order.Amount,
		"到账金额:", order.RealAmount,
		"手续费:", order.NetFee,
		"提现方式:", withdrawType,
		"币种:", order.Symbol,
		"是否首次提现:", withdrawEvent.IsFirstWithdraw)
}

// SendWithdrawFailEvent 发送提现失败事件到ThinkingData
func SendWithdrawFailEvent(user *model.XUser, order *model.XWithdraw, withdrawType string, failReason string) {
	// 获取 ThinkingData 服务实例
	service := GetService()

	// 构建提现失败事件数据
	withdrawEvent := WithdrawFailEvent{
		BaseEventProperties: NewBaseEventProperties("", 8), // 使用默认+8时区
		OrderID:             fmt.Sprint(order.ID),
		WithdrawType:        withdrawType,
		WithdrawAmount:      order.Amount,
		Commission:          order.NetFee,     // 手续费
		ReceivedAmount:      order.RealAmount, // 实际到账金额
		AssetType:           strings.ToUpper(order.Symbol),
		Currency:            strings.ToUpper(order.Symbol),
		IsFirstWithdraw:     isFirstWithdraw(order.UserID),
		FailReason:          failReason, // 失败原因
	}

	// 使用正确的服务方法发送事件
	err := service.SendWithdrawFailEvent(
		fmt.Sprint(user.UserID), // accountID
		fmt.Sprint(user.UserID), // distinctID (这里使用用户ID作为设备ID)
		withdrawEvent,           // 事件数据
	)

	if err != nil {
		logs.Error("发送提现失败事件到ThinkingData失败:", err,
			"用户ID:", user.UserID,
			"订单ID:", order.ID,
			"提现金额:", order.Amount,
			"到账金额:", order.RealAmount,
			"手续费:", order.NetFee,
			"提现方式:", withdrawType,
			"币种:", order.Symbol,
			"失败原因:", failReason)
		return
	}

	// 注意：BatchSize=1时数据会立即发送，无需手动刷新

	logs.Info("提现失败事件发送到ThinkingData成功:",
		"用户ID:", user.UserID,
		"订单ID:", order.ID,
		"提现金额:", order.Amount,
		"到账金额:", order.RealAmount,
		"手续费:", order.NetFee,
		"提现方式:", withdrawType,
		"币种:", order.Symbol,
		"是否首次提现:", withdrawEvent.IsFirstWithdraw,
		"失败原因:", failReason)
}

// SendVipLevelChangeEvent 发送VIP等级变化事件到ThinkingData
func SendVipLevelChangeEvent(user *model.XUser, oldLevel, newLevel int, rewardInfo []RewardInfo, clientIP string) error {
	// 获取 ThinkingData 服务实例
	service := GetService()
	if service == nil {
		logs.Error("ThinkingData服务实例为空")
		return fmt.Errorf("ThinkingData服务实例为空")
	}

	// 构建VIP等级变化事件数据
	vipEvent := VipLevelChangeEvent{
		BaseEventProperties: NewBaseEventProperties(clientIP, 8), // 使用默认+8时区
		LevelBefore:         oldLevel,
		LevelAfter:          newLevel,
		GetRewardInfo:       rewardInfo,
	}

	// 发送VIP等级变化事件
	accountID := fmt.Sprint(user.UserID)
	distinctID := fmt.Sprint(user.UserID)

	err := service.SendVipLevelChangeEvent(accountID, distinctID, vipEvent)
	if err != nil {
		logs.Error("ThinkingDataVIP等级变化事件发送失败:", err,
			"用户ID:", user.UserID,
			"原等级:", oldLevel,
			"新等级:", newLevel,
			"IP:", clientIP)
		return err
	}

	// 同时更新用户VIP等级属性
	util := GetThinkingDataUtil()
	err = util.SetUserPropertiesOnVipLevelChange(accountID, distinctID, user.UserID, newLevel, oldLevel)
	if err != nil {
		logs.Error("ThinkingDataVIP等级变化用户属性设置失败:", err,
			"用户ID:", user.UserID,
			"原等级:", oldLevel,
			"新等级:", newLevel)
		// 不返回错误，因为事件已经发送成功
	}

	logs.Info("ThinkingDataVIP等级变化事件发送成功:",
		"用户ID:", user.UserID,
		"原等级:", oldLevel,
		"新等级:", newLevel)

	return nil
}
