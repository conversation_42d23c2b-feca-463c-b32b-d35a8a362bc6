package single

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"xserver/controller/third/single/base"

	"github.com/beego/beego/logs"
)

// IMOne单一钱包类
type IMOneSingleService struct {
	apiUrl                string          // API基础接口
	merchantCode          string          // 商家代码
	currency              string          // 币种
	brandName             string          // 厂商标识
	Debug                 bool            // 日志调试模式
	RefreshUserAmountFunc func(int) error // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
	proxyURL              string // 代理地址
	proxyUser             string // 代理用户名
	proxyPass             string // 代理密码
	games                 map[string]string
}

// 初始化IMOne单一钱包
func NewIMOneSingleService(params map[string]string, fc func(int) error) *IMOneSingleService {
	games := map[string]string{
		"ESportsbull": "IM电竞",
		"IMSB":        "IM体育",
	}
	return &IMOneSingleService{
		apiUrl:                params["api_url"],       // API地址，如：http://operatorapi-staging.imaegisapi.com
		merchantCode:          params["merchant_code"], // 商家密钥，如：OL6Knh9Dwy69sN8azRgGqqhOProaAWfa
		currency:              params["currency"],      // 币种
		brandName:             "imone",                 // 厂商标识
		Debug:                 true,                    // 是否调试模式
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
		proxyURL:              params["proxy"],      // 代理地址，如：16.163.78.18:8888
		proxyUser:             params["proxy_user"], // 代理用户名，如：hash
		proxyPass:             params["proxy_pass"], // 代理密码，如：hashproxy123
		games:                 games,
	}
}

const cacheKeyIMOne = "cacheKeyIMOne:"

// IMOne返回错误码
const (
	IMOne_Code_Success                     = "0"   // 成功
	IMOne_Code_Invalid_Merchant            = "500" // 营运商或代理商代码无效
	IMOne_Code_Unauthorized_Access         = "501" // 非法访问
	IMOne_Code_Player_Already_Exists       = "503" // 玩家已经存在
	IMOne_Code_Player_Not_Exists           = "504" // 玩家不存在
	IMOne_Code_Required_Field_Empty        = "505" // 必须的参数不能为空
	IMOne_Code_Invalid_Player_ID           = "506" // 玩家账号无效
	IMOne_Code_Invalid_Currency            = "507" // 货币代码无效
	IMOne_Code_Invalid_Product_Wallet      = "508" // 产品钱包无效
	IMOne_Code_Invalid_Language            = "518" // 语言无效
	IMOne_Code_Invalid_Game_Code           = "521" // 游戏代码无效
	IMOne_Code_Invalid_IP_Address          = "522" // IP地址无效
	IMOne_Code_Invalid_Password            = "524" // 密码无效
	IMOne_Code_Game_Not_Active             = "533" // 游戏在停用状态
	IMOne_Code_Game_Not_Supported_Platform = "534" // 此游戏目前不支援此平台
	IMOne_Code_Failed_Start_Game           = "536" // 启动游戏失败（游戏已在进行中）
	IMOne_Code_Setup_In_Progress           = "538" // 设置正在进行中，请联络技术支援
	IMOne_Code_Player_Creation_Failed      = "540" // 玩家未在产品供应商端创建成或在停用状态
	IMOne_Code_Player_Inactive             = "542" // 玩家在停用状态
	IMOne_Code_Game_Not_Activated          = "546" // 游戏不对该营运商开放
	IMOne_Code_API_Frequency_Limit         = "557" // 超出API的访问频率限制
	IMOne_Code_Use_Proper_Domain           = "568" // 请使用正确的域名
	IMOne_Code_Concurrent_Request          = "579" // 检测到并发请求
	IMOne_Code_Provider_Internal_Error     = "600" // 产品供应商内部错误
	IMOne_Code_Unauthorized_Product_Access = "601" // 非法产品访问
	IMOne_Code_Invalid_Argument            = "612" // 无效的参数值
	IMOne_Code_System_Unable_Process       = "998" // 系统目前无法处理您的请求，请重试
	IMOne_Code_System_Failed_Process       = "999" // 系统处理您的请求失败
)

// IMOne通用响应结构
type IMOneResponse struct {
	Code    string `json:"Code"`
	Message string `json:"Message"`
}

// sendHTTPRequest 发送HTTP请求并返回原始响应数据
func (l *IMOneSingleService) sendHTTPRequest(requestURL string, jsonData []byte) ([]byte, error) {
	// 发送HTTP POST请求
	req, err := http.NewRequest("POST", requestURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 配置代理（如果设置了代理）
	if l.proxyURL != "" {
		// 构建代理URL
		proxyURLStr := fmt.Sprintf("http://%s", l.proxyURL)
		if l.proxyUser != "" && l.proxyPass != "" {
			proxyURLStr = fmt.Sprintf("http://%s:%s@%s", l.proxyUser, l.proxyPass, l.proxyURL)
		}

		proxyURL, err := url.Parse(proxyURLStr)
		if err != nil {
			return nil, fmt.Errorf("解析代理URL失败: %v", err)
		}

		// 设置代理
		client.Transport = &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}

		if l.Debug {
			logs.Info("IMOne sendHTTPRequest 使用代理:", l.proxyURL, " 用户:", l.proxyUser)
		}
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne sendHTTPRequest 响应状态:", resp.Status, " URL:", requestURL)
	}

	return body, nil
}

// IMOne通用请求方法
func (l *IMOneSingleService) sendRequest(endpoint string, data interface{}) (*IMOneResponse, error) {
	// 将数据转换为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne API请求: %s, 数据: %s", endpoint, string(jsonData))
	}

	// 构造完整URL
	url := fmt.Sprintf("%s/%s", l.apiUrl, endpoint)

	// 发送HTTP POST请求
	body, err := l.sendHTTPRequest(url, jsonData)
	if err != nil {
		return nil, err
	}

	if l.Debug {
		logs.Info("IMOne API响应: %s", string(body))
	}

	// 解析响应
	var response IMOneResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if response.Code != IMOne_Code_Success {
		return &response, fmt.Errorf("API错误: %s - %s", response.Code, response.Message)
	}

	return &response, nil
}

// 生成用户ID格式 - 参考OFA的做法
func (l *IMOneSingleService) getPlayerIdFromUserId(userId int) string {
	//return fmt.Sprintf("%s_%d", l.brandName, userId)
	return fmt.Sprintf("%d", userId)
}

// 从PlayerID解析用户ID
func (l *IMOneSingleService) getUserIdFromPlayerId(playerId string) (int, error) {
	return base.ParseUserIdFromPlayerName(playerId), nil
}

// 生成用户密码 - 参考OFA的做法
func (l *IMOneSingleService) getUserPassword(userId int) string {
	return base.MD5(fmt.Sprintf("%d_IMOne_Secret_Key", userId))[:16]
}
