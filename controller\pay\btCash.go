package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var BtCash = new(btCash)

type btCash struct {
	Base
}

// btCashMethodConfig 三方配置
type btCashMethodConfig struct {
	Url   string `json:"url"`
	Cburl string `json:"cburl"` // 回调地址
	Key   string `json:"key"`   // 三方密��
}

type btCashRechargeResponse struct {
	Code int `json:"code"` //状态码
	Data struct {
		Number      string `json:"number"`       //我方订单号
		OrderNumber string `json:"order_number"` //商户订单号
		Url         string `json:"url"`          //订单url
	} `json:"data"`
}

type BTCashCallBack struct {
	Number      string `json:"number"`       //订单号
	OrderNumber string `json:"order_number"` //商户订单号
	Amount      string `json:"amount"`       //订单金额
	Status      string `json:"status"`       //状态 (padding: 等待处理, success: 交易成功, fail: 交易失败)
	Remark      string `json:"remark"`       //订单url
}

func (c *btCash) Init() {
	server.Http().GetNoAuth("/api/btcash/recharge/callback", c.rechargeCallback)
	server.Http().GetNoAuth("/api/btcash/withdraw/callback", c.withdrawCallback)
}

// Recharge 充值
func (c *btCash) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	// 获取请求参数
	errcode := 0
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	cfg := btCashMethodConfig{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	// 获取最便宜的通道
	channelId := c.getChannelId(cfg.Url, cfg.Key)
	if channelId == 0 {
		ctx.RespErr(errors.New("获取支付通道失败，请联系客服或稍后重试"), &errcode)
		return
	}
	// 新增订单
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var currentRate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		currentRate = 0              // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		currentRate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / currentRate // 运营商ID不为26时，进行汇率转换
	}
	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 创建订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      15,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: currentRate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}
	cfg.Cburl = fmt.Sprintf("%s/api/btcash/recharge/callback", cfg.Cburl)
	//发起支付请求
	params := xgo.H{
		"channel_cashflow_id": channelId,                                   // 支付通道
		"amount":              strconv.FormatFloat(req.Amount, 'f', 0, 64), // 订单金额
		"order_number":        strconv.Itoa(int(rechargeOrder.ID)),         // 订单号
		"url":                 cfg.Cburl,                                   // 回调地址
		"redirect_url":        cfg.Cburl,                                   // 跳转地址
	}
	jsonData, _ := json.Marshal(&params) //json序列化

	apiurl := fmt.Sprintf("%s/merchant/entrance/invoice/make", cfg.Url)
	resp, err := c.post(apiurl, map[string]string{
		"Content-Type":  "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", cfg.Key),
	}, jsonData)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("BtCash发起支付请求失败 err="), &errcode)
		return
	}
	// 解析响应
	response := btCashRechargeResponse{}
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("BTCASH解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		tx.Rollback()
		return
	}
	// 返回响应
	if response.Code != 0 {
		tx.Rollback()
		ctx.RespErr(errors.New("BTCASH:充值订单失败"), &errcode)
		return
	}

	// 更新订单的三方订单号
	_, err = tx.XRecharge.WithContext(ctx.Gin()).
		Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
		Update(tx.XRecharge.ThirdID, response.Data.Number)
	if err != nil {
		logs.Error("BTCASH更新三方订单号失败:", err)
		tx.Rollback()
		ctx.RespErr(errors.New("更新订单信息失败"), &errcode)
		return
	}

	logs.Info("BTCASH订单创建成功: 订单ID=%d, 三方订单号=%s, 支付URL=%s",
		rechargeOrder.ID, response.Data.Number, response.Data.Url)

	tx.Commit()

	ctx.RespOK(xgo.H{
		"payurl": response.Data.Url,
	})
}

// rechargeCallback 充值回调
func (c *btCash) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 获取请求参数
	callback := BTCashCallBack{}
	callback.Number = ctx.Gin().Query("number")
	callback.Amount = ctx.Gin().Query("amount")
	callback.OrderNumber = ctx.Gin().Query("order_number")
	callback.Status = ctx.Gin().Query("status")
	callback.Remark = ctx.Gin().Query("remark")
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(callback.OrderNumber)
	order, err := c.getRechargeOrder(orderNo)
	if err != nil {
		logs.Info("BTCASH回调获取订单失败", err)
		ctx.Gin().String(200, "订单号不存在")
		return
	}

	// 验证支付金额是否一致
	amount, err := strconv.ParseFloat(callback.Amount, 64)
	if err != nil {
		logs.Info("BTCASH回调金额转换错误", err)
		ctx.Gin().String(200, "金额转换错误")
		return
	}
	if math.Abs((order.Amount - amount)) > 0.01 {
		logs.Info("BTCASH回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}
	// 验证支付方式
	if _, err := c.getPayMethod(int(order.PayID)); err != nil {
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	if callback.Status == "padding" {
		logs.Info("BTCASH回调正在支付中", callback.Status)
		ctx.Gin().String(200, "正在支付中")
		return
	}
	// 是否成功
	if order.State == 5 {
		ctx.Gin().String(200, "success")
		return
	}
	// 更新三方订单号
	err = c.updateThirdOrder(order.ID, callback.Number)
	if err != nil {
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}

	// 获取用户信息
	// user, err := c.getUser(int(order.UserID))
	if err != nil {
		logs.Error("btcash callback: failed to get user info:", err)
		ctx.Gin().String(200, "获取用户信息失败")
		return
	}

	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	ctx.Gin().String(200, "success")
}

// withdrawCallback 提现回调
func (c *btCash) withdrawCallback(ctx *abugo.AbuHttpContent) {
	callback := BTCashCallBack{}
	callback.Number = ctx.Gin().Query("number")
	callback.Amount = ctx.Gin().Query("amount")
	callback.OrderNumber = ctx.Gin().Query("order_number")
	callback.Status = ctx.Gin().Query("status")
	callback.Remark = ctx.Gin().Query("remark")
	logs.Info("BTCASH提现回调数据:", callback)
	// 查看订单是否存在
	orderNo, err := strconv.Atoi(callback.OrderNumber)
	if err != nil {
		c.withdrawCallbackHandel(int(orderNo), 7)
		logs.Error("BTCASH:回调订单号转换错误", err)
		ctx.Gin().String(200, "订单号转换错误")
		return
	}
	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("BTCASH:提现回调获取订单失败", err)
		ctx.Gin().String(200, "订单号不存在")
		return
	}
	amount, err := strconv.ParseFloat(callback.Amount, 64)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("BTCASH:提现回调金额转换错误", err)
		ctx.Gin().String(200, "下发金额转换错误")
		return
	}
	if math.Abs((order.RealAmount-amount)/100) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("BTCASH:提现回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证支付方式
	if _, err := c.getPayMethod(int(order.PayID)); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("BTCASH:提现回调支付方式不存在")
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	// 是否成功订单
	if order.State == 6 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	if callback.Status == "success" {
		c.withdrawCallbackHandel(int(order.ID), 6)
		logs.Info("BTCASH订单号:%d 状态: %d", order.ID, 6)
	} else {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("BTCASH订单号:%d 状态: %d", order.ID, 7)
	}

	ctx.Gin().String(200, "success")
}

// 获取渠道，根据汇率选择最便宜的通道
func (c *btCash) getChannelId(url, token string) int64 {
	api := "/merchant/entrance/channels"
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("%s%s", url, api), nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	res, err := client.Do(req)
	if err != nil {
		logs.Error("BtCash getChannelId HTTP请求失败:", err)
		return 0
	}

	// 检查HTTP状态码
	if res.StatusCode != 200 {
		logs.Error("BtCash getChannelId HTTP状态码错误:", res.StatusCode)
		return 0
	}

	body, _ := io.ReadAll(res.Body)
	logs.Info("BtCash getChannelId API响应:", string(body)) // 添加调试日志

	var jsonData map[string]any
	err = json.Unmarshal(body, &jsonData)
	defer res.Body.Close()
	if err != nil {
		logs.Error("BtCash getChannelId JSON解析错误:", err)
		return 0
	}

	logs.Info("BtCash getChannelId 解析后的JSON:", jsonData) // 添加调试日志

	// 检查API是否返回错误
	if code, exists := jsonData["code"]; exists {
		if codeInt, ok := code.(float64); ok && codeInt != 0 {
			// API返回了错误代码
			errorMsg := "未知错误"
			if errors, exists := jsonData["errors"]; exists {
				if errorMap, ok := errors.(map[string]interface{}); ok {
					if message, exists := errorMap["message"]; exists {
						if msgStr, ok := message.(string); ok {
							errorMsg = msgStr
						}
					}
				}
			}
			logs.Error("BtCash getChannelId API返回错误, code:", codeInt, "message:", errorMsg)
			return 0
		}
	}

	// 安全检查 data 字段是否存在且为数组类型
	dataInterface, exists := jsonData["data"]
	if !exists {
		logs.Error("BtCash getChannelId 响应中缺少data字段, 完整响应:", jsonData)
		return 0
	}

	dataMap, ok := dataInterface.([]any)
	if !ok {
		logs.Error("BtCash getChannelId data字段不是数组类型")
		return 0
	}

	// 检查数组是否为空
	if len(dataMap) == 0 {
		logs.Error("BtCash getChannelId 没有可用的支付通道")
		return 0
	}

	minFee := math.MaxFloat64
	var minID float64

	for _, value := range dataMap {
		// 安全检查每个通道数据
		channelData, ok := value.(map[string]any)
		if !ok {
			logs.Error("BtCash getChannelId 通道数据格式错误")
			continue
		}

		feeInterface, exists := channelData["fee"]
		if !exists {
			logs.Error("BtCash getChannelId 通道缺少fee字段")
			continue
		}

		fee, ok := feeInterface.(string)
		if !ok {
			logs.Error("BtCash getChannelId fee字段不是字符串类型")
			continue
		}

		feeFloat := 0.0
		if _, err := fmt.Sscanf(fee, "%f", &feeFloat); err != nil {
			logs.Error("BtCash getChannelId fee转换失败:", err)
			continue
		}

		if feeFloat < minFee {
			idInterface, exists := channelData["id"]
			if !exists {
				logs.Error("BtCash getChannelId 通道缺少id字段")
				continue
			}

			id, ok := idInterface.(float64)
			if !ok {
				logs.Error("BtCash getChannelId id字段不是数字类型")
				continue
			}

			minFee = feeFloat
			minID = id
		}
	}

	if minID == 0 {
		logs.Error("BtCash getChannelId 未找到有效的支付通道")
		return 0
	}

	return int64(int(minID))
}
