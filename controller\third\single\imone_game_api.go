package single

import (
	"encoding/json"
	"fmt"

	"github.com/beego/beego/logs"
)

// NewLaunchGameRequest PC游戏启动请求结构
type NewLaunchGameRequest struct {
	MerchantCode  string `json:"MerchantCode"`         // 营运商唯一代码
	PlayerId      string `json:"PlayerId"`             // 玩家账号
	GameCode      string `json:"GameCode"`             // 游戏代码
	Language      string `json:"Language"`             // 语言
	IpAddress     string `json:"IpAddress"`            // 玩家的IP地址
	ProductWallet int    `json:"ProductWallet"`        // 产品钱包
	IsDownload    int    `json:"IsDownload"`           // 返回内容类型：0=游戏URL，1=账号密码，2=两者都返回
	LobbyURL      string `json:"LobbyURL,omitempty"`   // 营运商游戏大厅URL（可选）
	Tray          string `json:"Tray,omitempty"`       // 玩家盘口（可选）
	Route         int    `json:"Route,omitempty"`      // 启动游戏的线路选择（可选）
	BetLimitId    string `json:"BetLimitId,omitempty"` // 玩家的下注限额ID（可选）
	RoomId        string `json:"RoomId,omitempty"`     // 游戏厅号（可选）
	ShowTrial     int    `json:"ShowTrial,omitempty"`  // 显示免费试玩（可选）
	IsShowBtn     int    `json:"IsShowBtn,omitempty"`  // 显示按钮（可选）
}

// NewLaunchMobileGameRequest 手机游戏启动请求结构
type NewLaunchMobileGameRequest struct {
	MerchantCode  string `json:"MerchantCode"`            // 营运商唯一代码
	PlayerId      string `json:"PlayerId"`                // 玩家账号
	GameCode      string `json:"GameCode"`                // 游戏代码
	Language      string `json:"Language"`                // 语言
	IpAddress     string `json:"IpAddress"`               // 玩家的IP地址
	ProductWallet int    `json:"ProductWallet"`           // 产品钱包
	IsDownload    int    `json:"IsDownload"`              // 返回内容类型：0=游戏URL，1=账号密码，2=两者都返回
	LobbyURL      string `json:"LobbyURL,omitempty"`      // 营运商游戏大厅URL（可选）
	SupportURL    string `json:"SupportURL,omitempty"`    // 营运商系统支援组的URL（可选）
	DepositURL    string `json:"DepositURL,omitempty"`    // 存款URL（可选）
	Tray          string `json:"Tray,omitempty"`          // 玩家盘口（可选）
	Route         int    `json:"Route,omitempty"`         // 启动游戏的线路选择（可选）
	BetLimitId    string `json:"BetLimitId,omitempty"`    // 玩家的下注限额ID（可选）
	RoomId        string `json:"RoomId,omitempty"`        // 游戏厅号（可选）
	ShowTrial     int    `json:"ShowTrial,omitempty"`     // 显示免费试玩（可选）
	SwipeUpOff    bool   `json:"SwipeUpOff,omitempty"`    // 向上滑动关闭（可选）
	IsShowBtn     int    `json:"IsShowBtn,omitempty"`     // 显示按钮（可选）
	DirectUrlType int    `json:"DirectUrlType,omitempty"` // 直接URL类型（可选）
}

// LaunchGameResponse 游戏启动响应结构
type LaunchGameResponse struct {
	Code           string `json:"Code"`
	Message        string `json:"Message"`
	GameUrl        string `json:"GameUrl,omitempty"`        // 游戏URL
	PlayerID       string `json:"PlayerID,omitempty"`       // 玩家账号
	PlayerPassword string `json:"PlayerPassword,omitempty"` // 玩家密码
	SessionToken   string `json:"SessionToken,omitempty"`   // 会话令牌
	TTL            int    `json:"TTL,omitempty"`            // 令牌有效期（秒）
}

// LaunchGame 启动PC游戏 - API: Game/NewLaunchGame
func (l *IMOneSingleService) LaunchGame(userId int, gameCode, language, ipAddress string, productWallet int, options map[string]interface{}) (*LaunchGameResponse, error) {
	playerId := l.getPlayerIdFromUserId(userId)

	request := NewLaunchGameRequest{
		MerchantCode:  l.merchantCode,
		PlayerId:      playerId,
		GameCode:      gameCode,
		Language:      language,
		IpAddress:     ipAddress,
		ProductWallet: productWallet,
		IsDownload:    0, // 默认返回游戏URL
	}

	// 处理可选参数
	if options != nil {
		if isDownload, ok := options["IsDownload"].(int); ok {
			request.IsDownload = isDownload
		}
		if lobbyURL, ok := options["LobbyURL"].(string); ok {
			request.LobbyURL = lobbyURL
		}
		if tray, ok := options["Tray"].(string); ok {
			request.Tray = tray
		}
		if route, ok := options["Route"].(int); ok {
			request.Route = route
		}
		if betLimitId, ok := options["BetLimitId"].(string); ok {
			request.BetLimitId = betLimitId
		}
		if roomId, ok := options["RoomId"].(string); ok {
			request.RoomId = roomId
		}
		if showTrial, ok := options["ShowTrial"].(int); ok {
			request.ShowTrial = showTrial
		}
		if isShowBtn, ok := options["IsShowBtn"].(int); ok {
			request.IsShowBtn = isShowBtn
		}
	}

	if l.Debug {
		logs.Info("IMOne LaunchGame 请求: userId=%d, playerId=%s, gameCode=%s, productWallet=%d", userId, playerId, gameCode, productWallet)
	}

	// 发送请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化游戏启动请求失败: %v", err)
	}

	response, err := l.sendRequestWithResponse("Game/NewLaunchGame", jsonData)
	if err != nil {
		return nil, err
	}

	var result LaunchGameResponse
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("解析游戏启动响应失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne LaunchGame 响应: userId=%d, code=%s, message=%s", userId, result.Code, result.Message)
	}

	// 为营运商钱包模式生成SessionToken
	//if result.Code == IMOne_Code_Success && result.GameUrl != "" {
	//	sessionToken, err := l.GenerateSessionToken(userId, playerId, gameCode, fmt.Sprintf("%d", productWallet), 86400)
	//	if err != nil {
	//		logs.Error("IMOne LaunchGame 生成会话令牌失败 userId=", userId, " gameCode=", gameCode, " err=", err.Error())
	//	} else {
	//		result.SessionToken = sessionToken
	//		result.TTL = 86400 // 24小时
	//		if l.Debug {
	//			logs.Info("IMOne LaunchGame 生成会话令牌成功 userId=", userId, " sessionToken=", sessionToken)
	//		}
	//	}
	//}

	return &result, nil
}

// LaunchMobileGame 启动手机游戏 - API: Game/NewLaunchMobileGame
func (l *IMOneSingleService) LaunchMobileGame(userId int, gameCode, language, ipAddress string, productWallet int, options map[string]interface{}) (*LaunchGameResponse, error) {
	playerId := l.getPlayerIdFromUserId(userId)

	request := NewLaunchMobileGameRequest{
		MerchantCode:  l.merchantCode,
		PlayerId:      playerId,
		GameCode:      gameCode,
		Language:      language,
		IpAddress:     ipAddress,
		ProductWallet: productWallet,
		IsDownload:    0, // 默认返回游戏URL
	}

	// 处理可选参数
	if options != nil {
		if isDownload, ok := options["IsDownload"].(int); ok {
			request.IsDownload = isDownload
		}
		if lobbyURL, ok := options["LobbyURL"].(string); ok {
			request.LobbyURL = lobbyURL
		}
		if supportURL, ok := options["SupportURL"].(string); ok {
			request.SupportURL = supportURL
		}
		if depositURL, ok := options["DepositURL"].(string); ok {
			request.DepositURL = depositURL
		}
		if tray, ok := options["Tray"].(string); ok {
			request.Tray = tray
		}
		if route, ok := options["Route"].(int); ok {
			request.Route = route
		}
		if betLimitId, ok := options["BetLimitId"].(string); ok {
			request.BetLimitId = betLimitId
		}
		if roomId, ok := options["RoomId"].(string); ok {
			request.RoomId = roomId
		}
		if showTrial, ok := options["ShowTrial"].(int); ok {
			request.ShowTrial = showTrial
		}
		if swipeUpOff, ok := options["SwipeUpOff"].(bool); ok {
			request.SwipeUpOff = swipeUpOff
		}
		if isShowBtn, ok := options["IsShowBtn"].(int); ok {
			request.IsShowBtn = isShowBtn
		}
		if directUrlType, ok := options["DirectUrlType"].(int); ok {
			request.DirectUrlType = directUrlType
		}
	}

	if l.Debug {
		logs.Info("IMOne LaunchMobileGame 请求: userId=%d, playerId=%s, gameCode=%s, productWallet=%d", userId, playerId, gameCode, productWallet)
	}

	// 发送请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化手机游戏启动请求失败: %v", err)
	}

	response, err := l.sendRequestWithResponse("Game/NewLaunchMobileGame", jsonData)
	if err != nil {
		return nil, err
	}

	var result LaunchGameResponse
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("解析手机游戏启动响应失败: %v", err)
	}

	if l.Debug {
		logs.Info("IMOne LaunchMobileGame 响应: userId=%d, code=%s, message=%s", userId, result.Code, result.Message)
	}

	// 为营运商钱包模式生成SessionToken
	//if result.Code == IMOne_Code_Success && result.GameUrl != "" {
	//	sessionToken, err := l.GenerateSessionToken(userId, playerId, gameCode, fmt.Sprintf("%d", productWallet), 86400)
	//	if err != nil {
	//		logs.Error("IMOne LaunchMobileGame 生成会话令牌失败 userId=", userId, " gameCode=", gameCode, " err=", err.Error())
	//	} else {
	//		result.SessionToken = sessionToken
	//		result.TTL = 86400 // 24小时
	//		if l.Debug {
	//			logs.Info("IMOne LaunchMobileGame 生成会话令牌成功 userId=", userId, " sessionToken=", sessionToken)
	//		}
	//	}
	//}

	return &result, nil
}

// GetOrCreatePlayerAndLaunchGame 获取或创建玩家并启动游戏的便利方法，参考OFA的GetOrCreateUserGameURL
func (l *IMOneSingleService) GetOrCreatePlayerAndLaunchGame(userId int, gameCode, language, ipAddress string, productWallet int, isMobile bool, options map[string]interface{}) (*LaunchGameResponse, error) {
	// 步骤1: 确保玩家存在
	err := l.GetOrCreatePlayer(userId, l.currency)
	if err != nil {
		return nil, fmt.Errorf("确保玩家存在失败: %v", err)
	}

	// 步骤2: 启动游戏
	if isMobile {
		return l.LaunchMobileGame(userId, gameCode, language, ipAddress, productWallet, options)
	} else {
		return l.LaunchGame(userId, gameCode, language, ipAddress, productWallet, options)
	}
}
