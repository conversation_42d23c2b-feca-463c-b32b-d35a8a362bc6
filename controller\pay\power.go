package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var Power = new(power)

type power struct {
	Base
}

// 充值-请求参数
type PowerRechargeRequest struct {
	Merchant    string  `json:"merchant"`     // 商户号
	PayMentType string  `json:"payment_type"` // 支付方式 1=>掃碼 , 2=> 網關 , 3=> 直連 , 7=>原生渠道, 10=>USDT
	Amount      float64 `json:"amount"`       // 支付金额
	OrderId     string  `json:"order_id"`     // 订单号
	BankCode    string  `json:"bank_code"`    // 銀行代碼 直連gcash固定值gcash、直連paymaya固定值PMP、直連UnionBank固定值UBP、USDT-TRC固定值usdt-trc
	CallBackUrl string  `json:"callback_url"` // 回調地址
	ReturnUrl   string  `json:"return_url"`   // 返回地址
	Sign        string  `json:"sign"`         // 签名
}

// 充值-返回参数
type PowerRechargeResponse struct {
	RedirectURL string `json:"redirect_url"`
	QrcodeURL   string `json:"qrcode_url"`
	GcashQrURL  string `json:"gcash_qr_url"`
	OrderID     string `json:"order_id"`
	Remark      int    `json:"remark"`
	Amount      int    `json:"amount"`
	Status      string `json:"status"`
	Message     string `json:"message"`
}

type PowerMethodConfig struct {
	AppId string `json:"app_id"` // appid
	Key   string `json:"key"`    // key
	Url   string `json:"url"`    // ��值地址
	Cburl string `json:"cburl"`  // 回調
}

// 定义订单接收参数结构体
type PowerOrderResponse struct {
	OrderID string `json:"order_id"`
	Amount  string `json:"amount"`
	Status  int    `json:"status"`
	Message string `json:"message"`
}

type PowerCallbackResponse struct {
	Merchant string `json:"merchant"`
	OrderID  string `json:"order_id"`
	Amount   string `json:"amount"`
	Message  string `json:"message"`
	Status   string `json:"status"`
	Sign     string `json:"sign"`
}

func (c *power) Init() {
	server.Http().PostNoAuth("/api/power/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/power/withdraw/callback", c.withdrawCallback)
}

var purl string //订单查询api地址

// Recharge 充值
func (c *power) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	cfg := PowerMethodConfig{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	token := server.GetToken(ctx)
	purl = cfg.Url + "/api/query" //查询订单API地址
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 创建订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      16,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}
	cfg.Cburl = fmt.Sprintf("%s/api/power/recharge/callback", cfg.Cburl)

	params := xgo.H{
		"amount":       req.Amount,
		"bank_code":    payMethod.PayType,
		"callback_url": cfg.Cburl,
		"merchant":     cfg.AppId,
		"order_id":     fmt.Sprintf("%v", rechargeOrder.ID),
		"payment_type": "1",
		"return_url":   cfg.Cburl,
	}

	params["sign"] = c.generateMd5Sign(params, cfg.Key)

	//请求参数
	jsonData, _ := json.Marshal(&params)
	resp, err := c.post(cfg.Url+"/api/transfer", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("Power发起支付请求失败"), &errcode)
		return
	}
	response := PowerRechargeResponse{} //定义接收结构体

	// 解析响应
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		logs.Error("Power:解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		tx.Rollback()
		return
	}
	logs.Info("Power:支付返回数据", response)
	// 返回响应
	if response.Status != "1" {
		tx.Rollback()
		ctx.RespErr(errors.New("Power:充值订单失败"), &errcode)
		return
	}

	tx.Commit()
	ctx.RespOK(xgo.H{
		"payurl": response.RedirectURL,
	})
}

// 充值回调
func (c *power) rechargeCallback(ctx *abugo.AbuHttpContent) {
	cb := PowerCallbackResponse{
		Merchant: ctx.Gin().PostForm("merchant"),
		OrderID:  ctx.Gin().PostForm("order_id"),
		Amount:   ctx.Gin().PostForm("amount"),
		Message:  ctx.Gin().PostForm("message"),
		Status:   ctx.Gin().PostForm("status"),
		Sign:     ctx.Gin().PostForm("sign"),
	}

	logs.Info("Power:回调数据", cb)

	if cb.Status != "5" {
		logs.Error("Power:充值回调失败")
		ctx.Gin().String(200, "充值回调失败")
		return
	}
	// 查看订单是否存在
	orderId, _ := strconv.Atoi(cb.OrderID)
	order, err := c.getRechargeOrder(orderId)
	// 订单不存在
	if err != nil {
		logs.Error("Power:订单不存在 err=", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 是否成功订单
	if order.State == 5 {
		ctx.Gin().String(200, "success")
		return
	}
	amount, _ := strconv.ParseFloat(cb.Amount, 64)
	// 验证支付金额是否一致
	if math.Abs(order.Amount-amount) > 0.01 {
		logs.Info("Power:回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证签名
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Info("Power:回调获取支付方式失败", err)
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	cfg := PowerMethodConfig{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 更新三方订单号
	err = c.updateThirdOrder(order.ID, cb.OrderID)
	if err != nil {
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}

	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

	ctx.Gin().String(200, "success")
}

// 提现回调
func (c *power) withdrawCallback(ctx *abugo.AbuHttpContent) {
	callback := PowerCallbackResponse{
		Merchant: ctx.Gin().PostForm("merchant"),
		OrderID:  ctx.Gin().PostForm("order_id"),
		Amount:   ctx.Gin().PostForm("amount"),
		Message:  ctx.Gin().PostForm("message"),
		Status:   ctx.Gin().PostForm("status"),
		Sign:     ctx.Gin().PostForm("sign"),
	}
	logs.Info("Power提现回调数据:", callback)
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(callback.OrderID)
	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		ctx.Gin().String(200, "订单号不存在")
		return
	}
	amount, err := strconv.ParseFloat(callback.Amount, 64)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("Power提现回调金额转换错误")
		ctx.Gin().String(200, "下发金额转换错误")
		return
	}
	if math.Abs((order.RealAmount-amount)/100) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("Power提现回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证支付方式
	if _, err := c.getPayMethod(int(order.PayID)); err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("Power提现回调支付方式不存在")
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	// 是否成功订单
	if order.State == 6 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	if callback.Status == "5" {
		c.withdrawCallbackHandel(int(order.ID), 6)
		ctx.Gin().String(200, "success")
		logs.Info("Power代付成功:", order.ID)
		return
	} else {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("Power代付失败:", order.ID)
		ctx.Gin().String(200, "update failure")
	}
}
