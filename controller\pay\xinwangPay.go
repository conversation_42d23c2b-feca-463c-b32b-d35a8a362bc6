package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/zhms/xgo/xgo"
)

const (
	ApiXwGateway      = "http://openapi.moth1709.vip/api"
	ApiXwBalanceQuery = "/balance"
	ApiXwTrans        = "pay/trans"
	ApiXwCreate       = "pay/v3/create"
	ApiXwTransStatus  = "pay/trans/status"
	ApiXwOrderStatus  = "pay/order/status"
)

func XwVerify(basic *BasicPay, sign, signStr string) int64 {

	code := int64(0)

	md5Str := fmt.Sprintf("%s&key=%s", signStr, basic.Md5Key)
	md5Sign := HexMd5(md5Str)
	if md5Sign == strings.ToLower(sign) {
		code = 1
	}
	return code
}

func XwSign(basic *BasicPay, rawStr string) string {
	signStr := rawStr + basic.Merchant + basic.Md5Key
	signStr = HexMd5(signStr)
	signStr = strings.ToLower(signStr)

	return signStr
}

func XwGetBaseHeader(merchant, sign string) map[string]string {
	jsHeader := make(map[string]string)
	jsHeader["Content-Type"] = "application/json; charset=UTF-8"
	jsHeader["Authorization"] = merchant
	jsHeader["Sign"] = sign

	return jsHeader
}

// 账户余额
func XwQueryBalance(basic *BasicPay) (string, error) {

	balance := ""
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))

	rawStr, err := json.Marshal(params)
	if err != nil {
		return balance, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwBalanceQuery
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return balance, errors.New(RequestError)
	}

	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return balance, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return balance, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return balance, errors.New(errMsg)
	}

	if val, ok := res["Data"]; ok {
		balance = fmt.Sprintf("%v", val)
	}

	return balance, nil
}

// 代付
func XwTransOrder(basic *BasicPay, amount float64, orderNo,
	notifyUrl, holder, cardNo, bankName string) (int, error) {
	iRet := 1
	params := make(map[string]interface{})

	params["MerchantOrderNo"] = orderNo
	params["MerchantOrderAmount"] = amount
	params["MerchantNotifyUrl"] = notifyUrl
	params["BankCardHolder"] = holder
	params["BankCardNo"] = cardNo
	params["BankName"] = bankName

	rawStr, err := json.Marshal(params)
	if err != nil {
		return iRet, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)
	jsHeader["TransType"] = "MYBCT"

	reqUrl := basic.GatewayUrl + ApiXwTrans
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return iRet, errors.New(RequestError)
	}
	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return iRet, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return iRet, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return iRet, errors.New(errMsg)
	}

	return 0, nil
}

// 代收
func XwCreateOrder(basic *BasicPay, amount float64,
	notifyUrl, orderNo, payType, bankCode, name string) (map[string]interface{}, error) {

	var res map[string]interface{}
	params := make(map[string]interface{})

	params["MerchantOrderNo"] = orderNo
	params["MerchantPayAmount"] = amount
	params["MerchantNotifyUrl"] = notifyUrl
	params["MerchantPayType"] = payType
	params["BankCode"] = bankCode
	params["Name"] = name

	rawStr, err := json.Marshal(params)
	if err != nil {
		return res, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)
	jsHeader["TransType"] = "MYBCT"

	reqUrl := basic.GatewayUrl + ApiXwCreate
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return res, errors.New(RequestError)
	}

	err = json.Unmarshal(body, &res)
	if err != nil {
		return res, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return res, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return res, errors.New(errMsg)
	}

	return res, nil

}

// 代付订单状态查询
func XwQueryTransOrder(basic *BasicPay, orderNo string) (interface{}, error) {
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))
	params["MerchantOrderNo"] = orderNo

	rawStr, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwTransStatus
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return nil, errors.New(RequestError)
	}

	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return nil, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return nil, errors.New(errMsg)
	}

	var data interface{}

	if val, ok := res["Data"]; ok {
		data = val
	} else {
		return nil, errors.New(ResultParamError)
	}

	return data, nil
}

// 代收订单状态查询
func XwQueryCreateOrder(basic *BasicPay, orderNo string) (interface{}, error) {
	params := make(map[string]interface{})

	ttamp := time.Now().UnixNano()
	params["Timestamp"] = ttamp
	params["Secret"] = HexMd5(strconv.FormatInt(ttamp, 10))
	params["MerchantOrderNo"] = orderNo

	rawStr, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	signStr := XwSign(basic, string(rawStr))
	jsHeader := XwGetBaseHeader(basic.Merchant, signStr)

	reqUrl := basic.GatewayUrl + ApiXwOrderStatus
	status, body := HttpBody(reqUrl, "POST", string(rawStr), jsHeader)
	if status != 200 {
		return nil, errors.New(RequestError)
	}
	var res map[string]interface{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, errors.New(JsonError)
	}

	if _, ok := res["Code"]; !ok {
		return nil, errors.New(ResultParamError)
	}

	if fmt.Sprintf("%v", res["Code"]) != "0" {
		errMsg := ResultError
		if msg, ok := res["CodeStr"]; ok {
			errMsg = fmt.Sprintf("%v", msg)
		}
		return nil, errors.New(errMsg)
	}

	var data interface{}

	if val, ok := res["Data"]; ok {
		data = val
	} else {
		return nil, errors.New(ResultParamError)
	}

	return data, nil
}

func (c *PayController) xinwang_create_order(ctx *abugo.AbuHttpContent, jcfg map[string]interface{}, rate float64,
	userdata *xgo.XMap, SpecialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap) {

	symbol := strings.ToLower(reqdata.Symbol)
	paytype := 2
	if symbol != "brl" {
		paytype = 7
	}
	// 新增订单
	errcode := 0
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var realAmount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                             // 运营商ID为26时，汇率设为0
		realAmount = float64(reqdata.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(reqdata.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		realAmount = float64(reqdata.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}
	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            reqdata.MethodId,
		"Brand":            paymethod.String("Brand"),
		"Name":             paymethod.String("Name"),
		"IsRechargeActive": reqdata.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	OrderId, _ := server.Db().Table("x_recharge").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      paytype,
		"Amount":       reqdata.Amount,
		"RealAmount":   realAmount,
		"TransferRate": rate,
		"State":        3,
		"CSGroup":      userdata.Int("CSGroup"),
		"CSId":         userdata.Int("CSId"),
		"SpecialAgent": SpecialAgent,
		"TopAgentId":   userdata.Int("TopAgentId"),
		"PayData":      string(payDataBytes), // 添加 PayData 字段
	})

	basic := new(BasicPay)

	//TODO: key的来源??

	amount := float64(reqdata.Amount)
	orderNo := fmt.Sprintf("%d", OrderId)
	notifyUrl := jcfg["cburl"].(string) + "/xinwangcreate"
	payType := reqdata.PayType
	bankCode := reqdata.BankCode
	name := reqdata.RealName

	res, err := XwCreateOrder(basic, amount, notifyUrl, orderNo, payType, bankCode, name)
	if err != nil || nil == res {
		return
	}

	data := make(map[string]interface{}, 0)
	data["PayId"] = reqdata.MethodId
	data["Brand"] = paymethod.String("Brand")
	data["Name"] = paymethod.String("Name")
	data["IsRechargeActive"] = reqdata.IsRechargeActive // 添加充值活动标识
	bytes, _ := json.Marshal(data)

	server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
		"PayData": string(bytes),
		"ThirdId": data["PlatformOrderNo"],
	})
	ctx.RespOK(xgo.H{
		"payurl": data["MobilePayUrl"],
	})

	return
}
