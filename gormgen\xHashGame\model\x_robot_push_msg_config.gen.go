// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotPushMsgConfig = "x_robot_push_msg_config"

// XRobotPushMsgConfig 用户标签
type XRobotPushMsgConfig struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                 // id
	Name       string    `gorm:"column:name;comment:名称" json:"name"`                                           // 名称
	Remark     string    `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建日期" json:"create_time"` // 创建日期
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName XRobotPushMsgConfig's table name
func (*XRobotPushMsgConfig) TableName() string {
	return TableNameXRobotPushMsgConfig
}
