package datapush

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/ThinkingDataAnalytics/go-sdk/v2/src/thinkingdata"
	"github.com/beego/beego/logs"
)

// =============================================================================
// 常量定义 - Event Constants
// =============================================================================

// EventConstants 事件名称常量
const (
	// 基础数据事件
	EventRegister        = "ta_register"
	EventLogin           = "ta_login"
	EventVipLevelChange  = "vip_level_change"
	EventPhoneNumberBind = "phone_number_bind"

	// 充值提款事件
	EventDepositCreate   = "deposit_create"
	EventDepositSuccess  = "deposit_success"
	EventWithdrawCreate  = "withdraw_create"
	EventWithdrawSuccess = "withdraw_success"
	EventWithdrawFail    = "withdraw_fail"

	// 游戏玩法事件
	EventGameResult = "game_result"
	EventBetResult  = "bet_result"
	EventRebateGive = "rebate_give"
	EventRewardGive = "reward_give"

	// 打码事件
	EventCode = "code"

	// 活动奖励事件
	EventActivityRewardsGet = "activity_rewards_get"

	// 推广代理事件
	EventInviteSuccess     = "invite_success"
	EventAgentDividend     = "agent_dividend"
	EventCommissionReceive = "commission_receive"
)

// =============================================================================
// 数据结构定义 - Data Structures
// =============================================================================

// ThinkingDataConfig ThinkingData配置结构
type ThinkingDataConfig struct {
	ServerURL string `mapstructure:"server_url"` // 数据接收端地址
	AppID     string `mapstructure:"app_id"`     // 项目APP ID
	BatchSize int    `mapstructure:"batch_size"` // 批量上报大小，默认20
	Debug     bool   `mapstructure:"debug"`      // 是否开启调试模式
	Compress  bool   `mapstructure:"compress"`   // 是否开启压缩，默认true
}

// BaseEventProperties 基础事件属性
type BaseEventProperties struct {
	Time       string `json:"#time"`        // 事件时间，格式：yyyy-MM-dd HH:mm:ss
	IP         string `json:"#ip"`          // 用户IP
	ZoneOffset int    `json:"#zone_offset"` // 时区偏移量（小时），例如：+8区为8，-5区为-5
}

// NewBaseEventProperties 创建基础事件属性
// ip: 用户IP地址
// zoneOffset: 时区偏移量（小时），例如：+8区为8，-5区为-5，UTC为0
func NewBaseEventProperties(ip string, zoneOffset int) BaseEventProperties {
	// 根据时区偏移量计算目标时区的时间
	targetZone := time.FixedZone("", zoneOffset*3600)
	timeStr := time.Now().In(targetZone).Format("2006-01-02 15:04:05")

	return BaseEventProperties{
		Time:       timeStr, // 使用ThinkingData要求的时间格式
		IP:         ip,
		ZoneOffset: zoneOffset,
	}
}

// RewardInfo 奖励信息
type RewardInfo struct {
	Name string  `json:"name"` // 奖励名称
	Num  float64 `json:"num"`  // 奖励数量
}

// EventData 批量事件数据结构
type EventData struct {
	AccountID  string      `json:"account_id"`
	DistinctID string      `json:"distinct_id"`
	EventName  string      `json:"event_name"`
	EventData  interface{} `json:"event_data"`
}

// =============================================================================
// 事件结构体定义 - Event Structures
// =============================================================================

// RegisterEvent 账号注册事件 (ta_register)
type RegisterEvent struct {
	BaseEventProperties
	RegisterType string `json:"register_type"` // 注册类型，比如：邮箱、电话、Google等
	Email        string `json:"email"`         // 邮箱
	Phone        string `json:"phone"`         // 手机号
	ReferralCode string `json:"referral_code"` // 推荐码
}

// LoginEvent 账号登录事件 (ta_login)
type LoginEvent struct {
	BaseEventProperties
	LoginType string `json:"login_type"` // 登录方式，比如：账户登录、邮箱登录
}

// VipLevelChangeEvent VIP等级变动事件 (vip_level_change)
type VipLevelChangeEvent struct {
	BaseEventProperties
	LevelBefore   int          `json:"level_before"`    // 变动前等级
	LevelAfter    int          `json:"level_after"`     // 变动后等级
	GetRewardInfo []RewardInfo `json:"get_reward_info"` // 获得奖励信息
}

// PhoneNumberBindEvent 手机号绑定事件 (phone_number_bind)
type PhoneNumberBindEvent struct {
	BaseEventProperties
	// 手机号绑定成功时上报，无额外属性
}

// DepositCreateEvent 存款创建事件 (deposit_create)
type DepositCreateEvent struct {
	BaseEventProperties
	OrderID        string  `json:"order_id"`         // 订单ID
	DepositMethod  string  `json:"deposit_method"`   // 存款方式
	DepositAmount  float64 `json:"deposit_amount"`   // 存款金额
	AssetType      string  `json:"asset_type"`       // 资产类型，比如：虚拟币、法币
	Currency       string  `json:"currency"`         // 币种
	IsFirstDeposit bool    `json:"is_first_deposit"` // 是否首次存款
}

// DepositSuccessEvent 存款成功事件 (deposit_success)
type DepositSuccessEvent struct {
	BaseEventProperties
	OrderID        string  `json:"order_id"`         // 订单ID
	DepositMethod  string  `json:"deposit_method"`   // 存款方式
	DepositAmount  float64 `json:"deposit_amount"`   // 存款金额
	GetAmount      float64 `json:"get_amount"`       // 获得金额
	AssetType      string  `json:"asset_type"`       // 资产类型
	Currency       string  `json:"currency"`         // 币种
	IsFirstDeposit bool    `json:"is_first_deposit"` // 是否首次存款
	IsBonus        bool    `json:"is_bonus"`         // 是否有奖金
}

// DepositFailEvent 存款失败事件 (deposit_fail)
type DepositFailEvent struct {
	BaseEventProperties
	OrderID        string  `json:"order_id"`         // 订单ID
	DepositMethod  string  `json:"deposit_method"`   // 存款方式
	DepositAmount  float64 `json:"deposit_amount"`   // 存款金额
	AssetType      string  `json:"asset_type"`       // 资产类型
	Currency       string  `json:"currency"`         // 币种
	IsFirstDeposit bool    `json:"is_first_deposit"` // 是否首次存款
	FailReason     string  `json:"fail_reason"`      // 失败原因
}

// WithdrawCreateEvent 提款创建事件 (withdraw_create)
type WithdrawCreateEvent struct {
	BaseEventProperties
	OrderID         string  `json:"order_id"`          // 订单ID
	WithdrawType    string  `json:"withdraw_type"`     // 提款方式
	WithdrawAmount  float64 `json:"withdraw_amount"`   // 提款金额
	Commission      float64 `json:"commission"`        // 手续费
	ReceivedAmount  float64 `json:"received_amount"`   // 到账金额
	AssetType       string  `json:"asset_type"`        // 资产类型
	Currency        string  `json:"currency"`          // 币种
	IsFirstWithdraw bool    `json:"is_first_withdraw"` // 是否首次提款
}

// WithdrawSuccessEvent 提款成功事件 (withdraw_success)
type WithdrawSuccessEvent struct {
	BaseEventProperties
	OrderID         string  `json:"order_id"`          // 订单ID
	WithdrawType    string  `json:"withdraw_type"`     // 提款方式
	WithdrawAmount  float64 `json:"withdraw_amount"`   // 提款金额
	Commission      float64 `json:"commission"`        // 手续费
	ReceivedAmount  float64 `json:"received_amount"`   // 到账金额
	AssetType       string  `json:"asset_type"`        // 资产类型
	Currency        string  `json:"currency"`          // 币种
	IsFirstWithdraw bool    `json:"is_first_withdraw"` // 是否首次提款
}

// WithdrawFailEvent 提款失败事件 (withdraw_fail)
type WithdrawFailEvent struct {
	BaseEventProperties
	OrderID         string  `json:"order_id"`          // 订单ID
	WithdrawType    string  `json:"withdraw_type"`     // 提款方式
	WithdrawAmount  float64 `json:"withdraw_amount"`   // 提款金额
	Commission      float64 `json:"commission"`        // 手续费
	ReceivedAmount  float64 `json:"received_amount"`   // 到账金额
	AssetType       string  `json:"asset_type"`        // 资产类型
	Currency        string  `json:"currency"`          // 币种
	IsFirstWithdraw bool    `json:"is_first_withdraw"` // 是否首次提款
	FailReason      string  `json:"fail_reason"`       // 失败原因
}

// GameResultEvent 游戏结算事件 (game_result)
type GameResultEvent struct {
	BaseEventProperties
	GameID        string  `json:"game_id"`         // 游戏ID
	GameName      string  `json:"game_name"`       // 游戏名称
	GameType      string  `json:"game_type"`       // 游戏类型，比如：哈希游戏、电子游戏、真人等
	GameMethod    string  `json:"game_method"`     // 游戏玩法，比如：转账
	BetAmount     float64 `json:"bet_amount"`      // 投注金额
	WinLossAmount float64 `json:"win_loss_amount"` // 盈亏金额
	Currency      string  `json:"currency"`        // 币种
}

// BetEvent 投注事件 (bet)
type BetEvent struct {
	BaseEventProperties
	OrderID    string  `json:"order_id"`     // 订单ID
	GameID     string  `json:"game_id"`      // 游戏ID
	BetMethod  string  `json:"bet_method"`   // 投注玩法，比如：余额、转账
	BetType    string  `json:"bet_type"`     // 投注类型，比如：T类、P类
	BetAmount  float64 `json:"bet_amount"`   // 投注金额
	Currency   string  `json:"currency"`     // 币种
	IsFree     bool    `json:"is_free"`      // 是否免费
	IsFirstBet bool    `json:"is_first_bet"` // 是否首次投注
}

// BetResultEvent 投注结果事件 (bet_result)
type BetResultEvent struct {
	BaseEventProperties
	OrderID       string  `json:"order_id"`        // 订单ID
	GameID        string  `json:"game_id"`         // 游戏ID
	BetMethod     string  `json:"bet_method"`      // 投注玩法
	BetType       string  `json:"bet_type"`        // 投注类型
	BetAmount     float64 `json:"bet_amount"`      // 投注金额
	BetStatus     string  `json:"bet_status"`      // 投注状态，比如：成功、失败、取消
	WinLossAmount float64 `json:"win_loss_amount"` // 盈亏金额
	Currency      string  `json:"currency"`        // 币种
	IsFree        bool    `json:"is_free"`         // 是否免费
	IsFirstBet    bool    `json:"is_first_bet"`    // 是否首次投注
}

// RebateGiveEvent 返水发放事件 (rebate_give)
type RebateGiveEvent struct {
	BaseEventProperties
	RebateType   string  `json:"rebate_type"`   // 返水类型
	RebateAmount float64 `json:"rebate_amount"` // 返水金额
	Currency     string  `json:"currency"`      // 币种
}

// RewardGiveEvent 返奖发放事件 (reward_give)
type RewardGiveEvent struct {
	BaseEventProperties
	UserId       int32   `json:"user_id"`       // 用户ID
	RewardType   string  `json:"reward_type"`   // 奖励类型
	RewardAmount float64 `json:"reward_amount"` // 奖励金额
	Currency     string  `json:"currency"`      // 币种
}

// CodeEvent 用户打码事件 (code)
type CodeEvent struct {
	BaseEventProperties
	GameID          string  `json:"game_id"`           // 游戏ID
	GameName        string  `json:"game_name"`         // 游戏名称
	GameType        string  `json:"game_type"`         // 游戏类型
	CodeAmount      float64 `json:"code_amount"`       // 打码量
	GameWin         float64 `json:"game_win"`          // 开奖派彩
	ValidCodeAmount float64 `json:"valid_code_amount"` // 有效打码量
	BalanceBefore   float64 `json:"balance_before"`    // 变动前余额
	BalanceAfter    float64 `json:"balance_after"`     // 变动后余额
}

// ActivityRewardsGetEvent 活动奖励获取事件 (activity_rewards_get)
type ActivityRewardsGetEvent struct {
	BaseEventProperties
	ActivityID   string  `json:"activity_id"`   // 活动ID
	ActivityName string  `json:"activity_name"` // 活动名称
	ActivityType string  `json:"activity_type"` // 活动类型
	RewardType   string  `json:"reward_type"`   // 奖励类型
	RewardAmount float64 `json:"reward_amount"` // 奖励金额
	Currency     string  `json:"currency"`      // 币种
	RewardStatus string  `json:"reward_status"` // 奖励状态，比如：成功、失败
	RewardMethod string  `json:"reward_method"` // 奖励方式，比如：自动、手动
}

// InviteSuccessEvent 邀请成功事件 (invite_success)
type InviteSuccessEvent struct {
	BaseEventProperties
	InviteCode    string  `json:"invite_code"`     // 邀请码
	InviteeUserID string  `json:"invitee_user_id"` // 被邀请用户ID
	InviteLevel   int     `json:"invite_level"`    // 邀请层级
	RewardAmount  float64 `json:"reward_amount"`   // 奖励金额
	Currency      string  `json:"currency"`        // 币种
	InviteType    string  `json:"invite_type"`     // 邀请类型，比如：注册、首充
}

// AgentDividendEvent 代理分红事件 (agent_dividend)
type AgentDividendEvent struct {
	BaseEventProperties
	DividendType    string  `json:"dividend_type"`    // 分红类型
	DividendAmount  float64 `json:"dividend_amount"`  // 分红金额
	Currency        string  `json:"currency"`         // 币种
	DividendPeriod  string  `json:"dividend_period"`  // 分红周期，比如：日、周、月
	TeamPerformance float64 `json:"team_performance"` // 团队业绩
	DividendRate    float64 `json:"dividend_rate"`    // 分红比例
}

// CommissionReceiveEvent 佣金领取事件 (commission_receive)
type CommissionReceiveEvent struct {
	BaseEventProperties
	CommissionType   string  `json:"commission_type"`   // 佣金类型
	CommissionAmount float64 `json:"commission_amount"` // 佣金金额
	Currency         string  `json:"currency"`          // 币种
	CommissionPeriod string  `json:"commission_period"` // 佣金周期
	SourceUserID     string  `json:"source_user_id"`    // 来源用户ID
	CommissionLevel  int     `json:"commission_level"`  // 佣金层级
	CommissionRate   float64 `json:"commission_rate"`   // 佣金比例
}

// =============================================================================
// 核心工具类定义 - Core Utility Class
// =============================================================================

// ThinkingDataUtil ThinkingData工具类
type ThinkingDataUtil struct {
	ta          *thinkingdata.TDAnalytics
	config      *ThinkingDataConfig
	mu          sync.RWMutex
	initialized bool
}

// ThinkingDataService ThinkingData事件发送服务
type ThinkingDataService struct {
	util *ThinkingDataUtil
}

var (
	tdUtil *ThinkingDataUtil
	once   sync.Once
)

// GetThinkingDataUtil 获取ThinkingData工具类单例
func GetThinkingDataUtil() *ThinkingDataUtil {
	once.Do(func() {
		tdUtil = &ThinkingDataUtil{}
		if err := tdUtil.Init(); err != nil {
			log.Printf("ThinkingData初始化失败: %v", err)
		}
	})
	return tdUtil
}

// Init 初始化ThinkingData SDK
func (td *ThinkingDataUtil) Init() error {
	td.mu.Lock()
	defer td.mu.Unlock()

	// 从 server 包获取配置
	config := &ThinkingDataConfig{
		ServerURL: server.ThinkingDataServerURL(),
		AppID:     server.ThinkingDataAppID(),
		BatchSize: server.ThinkingDataBatchSize(),
		Debug:     server.ThinkingDataDebug(),
		Compress:  server.ThinkingDataCompress(),
	}

	td.config = config

	// 验证必要配置
	if config.AppID == "" {
		log.Printf("ThinkingData: APP ID为空，初始化失败")
		return fmt.Errorf("ThinkingData APP ID不能为空")
	}

	// 创建Consumer
	var consumer thinkingdata.Consumer
	var err error

	if config.Debug {
		// 调试模式：逐条实时传输，用于开发调试
		consumer, err = thinkingdata.NewDebugConsumer(config.ServerURL, config.AppID)
		if err != nil {
			return fmt.Errorf("创建DebugConsumer失败: %v", err)
		}
		log.Printf("ThinkingData: 使用DebugConsumer (实时上报模式), ServerURL=%s, AppID=%s", config.ServerURL, config.AppID)
	} else {
		// 生产模式：准实时传输
		// 设置批量大小为1，实现准实时上报（每条数据立即发送）
		batchSize := 1

		// 生产环境使用 BatchConsumer，BatchSize=1 实现准实时上报
		consumer, err = thinkingdata.NewBatchConsumerWithBatchSize(config.ServerURL, config.AppID, batchSize)
		if err != nil {
			return fmt.Errorf("创建BatchConsumer失败: %v", err)
		}

		log.Printf("ThinkingData 生产环境配置: BatchConsumer, BatchSize=%d (准实时模式), ServerURL=%s, AppID=%s",
			batchSize, config.ServerURL, config.AppID)
	}

	// 创建TDAnalytics实例
	ta := thinkingdata.New(consumer)
	td.ta = &ta
	td.initialized = true

	log.Printf("ThinkingData初始化成功，服务器: %s, APP ID: %s", config.ServerURL, config.AppID)

	// 添加详细的初始化信息
	log.Printf("ThinkingData详细配置: Debug=%v, BatchSize=%d, Compress=%v",
		config.Debug, config.BatchSize, config.Compress)

	return nil
}

// TrackEvent 上报事件
func (td *ThinkingDataUtil) TrackEvent(accountID, distinctID, eventName string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	// 添加通用属性
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加时间戳（如果没有设置）
	if _, exists := properties["#time"]; !exists {
		properties["#time"] = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}

	// 调试模式下打印详细信息
	if td.config.Debug {
		log.Printf("ThinkingData准备发送事件: 事件名=%s, 账户ID=%s, 设备ID=%s",
			eventName, accountID, distinctID)
		log.Printf("ThinkingData事件属性: %+v", properties)
		log.Printf("ThinkingData配置: ServerURL=%s, AppID=%s",
			td.config.ServerURL, td.config.AppID)
	}

	// 上报事件
	err := td.ta.Track(accountID, distinctID, eventName, properties)
	if err != nil {
		log.Printf("ThinkingData事件上报失败: 事件=%s, 用户=%s/%s, 错误=%v",
			eventName, accountID, distinctID, err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData事件上报成功: %s, 用户: %s/%s", eventName, accountID, distinctID)
		log.Printf("ThinkingData事件已提交到Consumer，等待发送到服务端")
	}

	return nil
}

// TrackUserLogin 用户登录事件
func (td *ThinkingDataUtil) TrackUserLogin(accountID, distinctID, loginType, ip string, properties map[string]interface{}) error {
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加登录相关属性
	properties["login_type"] = loginType
	if ip != "" {
		properties["#ip"] = ip
	}
	properties["event_type"] = "user_login"

	return td.TrackEvent(accountID, distinctID, "user_login", properties)
}

// TrackUserRegister 用户注册事件
func (td *ThinkingDataUtil) TrackUserRegister(accountID, distinctID, registerType, ip string, properties map[string]interface{}) error {
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加注册相关属性
	properties["register_type"] = registerType
	if ip != "" {
		properties["#ip"] = ip
	}
	properties["event_type"] = "user_register"

	return td.TrackEvent(accountID, distinctID, "user_register", properties)
}

// TrackRecharge 充值事件
func (td *ThinkingDataUtil) TrackRecharge(accountID, distinctID, paymentMethod, currency string, amount float64, ip string, properties map[string]interface{}) error {
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加充值相关属性
	properties["payment_method"] = paymentMethod
	properties["currency"] = currency
	properties["amount"] = amount
	if ip != "" {
		properties["#ip"] = ip
	}
	properties["event_type"] = "recharge"

	return td.TrackEvent(accountID, distinctID, "recharge", properties)
}

// TrackGamePlay 游戏行为事件
func (td *ThinkingDataUtil) TrackGamePlay(accountID, distinctID, gameType, action string, properties map[string]interface{}) error {
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加游戏相关属性
	properties["game_type"] = gameType
	properties["action"] = action
	properties["event_type"] = "game_play"

	return td.TrackEvent(accountID, distinctID, "game_play", properties)
}

// TrackWithdraw 提现事件
func (td *ThinkingDataUtil) TrackWithdraw(accountID, distinctID, withdrawMethod, currency string, amount float64, ip string, properties map[string]interface{}) error {
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加提现相关属性
	properties["withdraw_method"] = withdrawMethod
	properties["currency"] = currency
	properties["amount"] = amount
	if ip != "" {
		properties["#ip"] = ip
	}
	properties["event_type"] = "withdraw"

	return td.TrackEvent(accountID, distinctID, "withdraw", properties)
}

// SetUserProperties 设置用户属性
func (td *ThinkingDataUtil) SetUserProperties(accountID, distinctID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserSet(accountID, distinctID, properties)
	if err != nil {
		log.Printf("ThinkingData用户属性设置失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性设置成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// SetUserPropertiesOnce 设置用户属性（仅首次）
func (td *ThinkingDataUtil) SetUserPropertiesOnce(accountID, distinctID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserSetOnce(accountID, distinctID, properties)
	if err != nil {
		log.Printf("ThinkingData用户属性首次设置失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性首次设置成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// AddUserProperties 累加用户属性
func (td *ThinkingDataUtil) AddUserProperties(accountID, distinctID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserAdd(accountID, distinctID, properties)
	if err != nil {
		log.Printf("ThinkingData用户属性累加失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性累加成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// Close 关闭SDK
func (td *ThinkingDataUtil) Close() error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.Close()
	if err != nil {
		log.Printf("ThinkingData关闭失败: %v", err)
		return err
	}

	log.Println("ThinkingData已关闭")
	return nil
}

// TrackFirstEvent 首次事件上报
func (td *ThinkingDataUtil) TrackFirstEvent(accountID, distinctID, eventName, firstCheckID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	// 添加通用属性
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加时间戳（如果没有设置）
	if _, exists := properties["#time"]; !exists {
		properties["#time"] = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}

	// 上报首次事件
	err := td.ta.TrackFirst(accountID, distinctID, eventName, firstCheckID, properties)
	if err != nil {
		log.Printf("ThinkingData首次事件上报失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData首次事件上报成功: %s, 用户: %s/%s, 校验ID: %s", eventName, accountID, distinctID, firstCheckID)
	}

	return nil
}

// TrackUpdateEvent 可更新事件上报
func (td *ThinkingDataUtil) TrackUpdateEvent(accountID, distinctID, eventName, eventID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	// 添加通用属性
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加时间戳（如果没有设置）
	if _, exists := properties["#time"]; !exists {
		properties["#time"] = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}

	// 上报可更新事件
	err := td.ta.TrackUpdate(accountID, distinctID, eventName, eventID, properties)
	if err != nil {
		log.Printf("ThinkingData可更新事件上报失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData可更新事件上报成功: %s, 用户: %s/%s, 事件ID: %s", eventName, accountID, distinctID, eventID)
	}

	return nil
}

// TrackOverwriteEvent 可重写事件上报
func (td *ThinkingDataUtil) TrackOverwriteEvent(accountID, distinctID, eventName, eventID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	// 添加通用属性
	if properties == nil {
		properties = make(map[string]interface{})
	}

	// 添加时间戳（如果没有设置）
	if _, exists := properties["#time"]; !exists {
		properties["#time"] = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}

	// 上报可重写事件
	err := td.ta.TrackOverwrite(accountID, distinctID, eventName, eventID, properties)
	if err != nil {
		log.Printf("ThinkingData可重写事件上报失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData可重写事件上报成功: %s, 用户: %s/%s, 事件ID: %s", eventName, accountID, distinctID, eventID)
	}

	return nil
}

// AppendUserProperties 追加用户属性（数组类型）
func (td *ThinkingDataUtil) AppendUserProperties(accountID, distinctID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserAppend(accountID, distinctID, properties)
	if err != nil {
		log.Printf("ThinkingData用户属性追加失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性追加成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// UniqAppendUserProperties 去重追加用户属性（数组类型）
func (td *ThinkingDataUtil) UniqAppendUserProperties(accountID, distinctID string, properties map[string]interface{}) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserUniqAppend(accountID, distinctID, properties)
	if err != nil {
		log.Printf("ThinkingData用户属性去重追加失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性去重追加成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// UnsetUserProperties 清空用户属性
func (td *ThinkingDataUtil) UnsetUserProperties(accountID, distinctID string, propertyNames ...string) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserUnset(accountID, distinctID, propertyNames)
	if err != nil {
		log.Printf("ThinkingData用户属性清空失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户属性清空成功: 用户: %s/%s, 属性: %v", accountID, distinctID, propertyNames)
	}

	return nil
}

// DeleteUser 删除用户
func (td *ThinkingDataUtil) DeleteUser(accountID, distinctID string) error {
	if td.ta == nil {
		return fmt.Errorf("ThinkingData未初始化")
	}

	err := td.ta.UserDelete(accountID, distinctID)
	if err != nil {
		log.Printf("ThinkingData用户删除失败: %v", err)
		return err
	}

	if td.config.Debug {
		log.Printf("ThinkingData用户删除成功: 用户: %s/%s", accountID, distinctID)
	}

	return nil
}

// GetConfig 获取当前配置
func (td *ThinkingDataUtil) GetConfig() *ThinkingDataConfig {
	td.mu.RLock()
	defer td.mu.RUnlock()
	return td.config
}

// IsInitialized 检查是否已初始化
func (td *ThinkingDataUtil) IsInitialized() bool {
	td.mu.RLock()
	defer td.mu.RUnlock()
	return td.initialized && td.ta != nil
}

// =============================================================================
// 通用事件发送方法 - Generic Event Methods
// =============================================================================

// convertToMap 将事件结构体转换为map[string]interface{}
func (td *ThinkingDataUtil) convertToMap(event interface{}) (map[string]interface{}, error) {
	jsonData, err := json.Marshal(event)
	if err != nil {
		return nil, fmt.Errorf("序列化事件失败: %v", err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		return nil, fmt.Errorf("反序列化事件失败: %v", err)
	}

	return result, nil
}

// SendEvent 通用事件发送方法
func (td *ThinkingDataUtil) SendEvent(accountID, distinctID, eventName string, event interface{}) error {
	// 如果是map类型，直接使用
	if properties, ok := event.(map[string]interface{}); ok {
		return td.TrackEvent(accountID, distinctID, eventName, properties)
	}

	// 如果是结构体，转换为map
	properties, err := td.convertToMap(event)
	if err != nil {
		return err
	}

	// 确保时间戳存在
	if _, exists := properties["#time"]; !exists {
		properties["#time"] = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}

	return td.TrackEvent(accountID, distinctID, eventName, properties)
}

// =============================================================================
// 高级事件发送服务 - Advanced Event Service
// =============================================================================

// NewThinkingDataService 创建ThinkingData事件发送服务
func NewThinkingDataService() *ThinkingDataService {
	return &ThinkingDataService{
		util: GetThinkingDataUtil(),
	}
}

// SendRegisterEvent 发送注册事件
func (s *ThinkingDataService) SendRegisterEvent(accountID, distinctID string, event RegisterEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventRegister, event)
}

// SendLoginEvent 发送登录事件
func (s *ThinkingDataService) SendLoginEvent(accountID, distinctID string, event LoginEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventLogin, event)
}

// SendVipLevelChangeEvent 发送VIP等级变动事件
func (s *ThinkingDataService) SendVipLevelChangeEvent(accountID, distinctID string, event VipLevelChangeEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventVipLevelChange, event)
}

// SendPhoneNumberBindEvent 发送手机号绑定事件
func (s *ThinkingDataService) SendPhoneNumberBindEvent(accountID, distinctID string, event PhoneNumberBindEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventPhoneNumberBind, event)
}

// SendDepositCreateEvent 发送存款创建事件
func (s *ThinkingDataService) SendDepositCreateEvent(accountID, distinctID string, event DepositCreateEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventDepositCreate, event)
}

// SendDepositSuccessEvent 发送存款成功事件
func (s *ThinkingDataService) SendDepositSuccessEvent(accountID, distinctID string, event DepositSuccessEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventDepositSuccess, event)
}

// SendWithdrawCreateEvent 发送提款创建事件
func (s *ThinkingDataService) SendWithdrawCreateEvent(accountID, distinctID string, event WithdrawCreateEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventWithdrawCreate, event)
}

// SendWithdrawSuccessEvent 发送提款成功事件
func (s *ThinkingDataService) SendWithdrawSuccessEvent(accountID, distinctID string, event WithdrawSuccessEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventWithdrawSuccess, event)
}

// SendWithdrawFailEvent 发送提款失败事件
func (s *ThinkingDataService) SendWithdrawFailEvent(accountID, distinctID string, event WithdrawFailEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventWithdrawFail, event)
}

// SendGameResultEvent 发送游戏结算事件
func (s *ThinkingDataService) SendGameResultEvent(accountID, distinctID string, event GameResultEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventGameResult, event)
}

// SendBetResultEvent 发送投注结果事件
func (s *ThinkingDataService) SendBetResultEvent(accountID, distinctID string, event BetResultEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventBetResult, event)
}

// SendRebateGiveEvent 发送返水发放事件
func (s *ThinkingDataService) SendRebateGiveEvent(accountID, distinctID string, event RebateGiveEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventRebateGive, event)
}

// SendRewardGiveEvent 发送返奖发放事件
func (s *ThinkingDataService) SendRewardGiveEvent(accountID, distinctID string, event RewardGiveEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventRewardGive, event)
}

// SendCodeEvent 发送用户打码事件
func (s *ThinkingDataService) SendCodeEvent(accountID, distinctID string, event CodeEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventCode, event)
}

// SendActivityRewardsGetEvent 发送活动奖励获取事件
func (s *ThinkingDataService) SendActivityRewardsGetEvent(accountID, distinctID string, event ActivityRewardsGetEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventActivityRewardsGet, event)
}

// SendInviteSuccessEvent 发送邀请成功事件
func (s *ThinkingDataService) SendInviteSuccessEvent(accountID, distinctID string, event InviteSuccessEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventInviteSuccess, event)
}

// SendAgentDividendEvent 发送代理分红事件
func (s *ThinkingDataService) SendAgentDividendEvent(accountID, distinctID string, event AgentDividendEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventAgentDividend, event)
}

// SendCommissionReceiveEvent 发送佣金领取事件
func (s *ThinkingDataService) SendCommissionReceiveEvent(accountID, distinctID string, event CommissionReceiveEvent) error {
	if event.Time == "" {
		event.Time = time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")
	}
	return s.util.SendEvent(accountID, distinctID, EventCommissionReceive, event)
}

// =============================================================================
// 批量操作和工具方法 - Batch Operations & Utilities
// =============================================================================

// BatchSendEvents 批量发送事件
func (s *ThinkingDataService) BatchSendEvents(events []EventData) error {
	for _, event := range events {
		var err error
		switch event.EventName {
		case EventRegister:
			if registerEvent, ok := event.EventData.(RegisterEvent); ok {
				err = s.SendRegisterEvent(event.AccountID, event.DistinctID, registerEvent)
			}
		case EventLogin:
			if loginEvent, ok := event.EventData.(LoginEvent); ok {
				err = s.SendLoginEvent(event.AccountID, event.DistinctID, loginEvent)
			}
		case EventDepositSuccess:
			if depositEvent, ok := event.EventData.(DepositSuccessEvent); ok {
				err = s.SendDepositSuccessEvent(event.AccountID, event.DistinctID, depositEvent)
			}
		case EventActivityRewardsGet:
			if activityEvent, ok := event.EventData.(ActivityRewardsGetEvent); ok {
				err = s.SendActivityRewardsGetEvent(event.AccountID, event.DistinctID, activityEvent)
			}
		case EventInviteSuccess:
			if inviteEvent, ok := event.EventData.(InviteSuccessEvent); ok {
				err = s.SendInviteSuccessEvent(event.AccountID, event.DistinctID, inviteEvent)
			}
		case EventAgentDividend:
			if dividendEvent, ok := event.EventData.(AgentDividendEvent); ok {
				err = s.SendAgentDividendEvent(event.AccountID, event.DistinctID, dividendEvent)
			}
		case EventCommissionReceive:
			if commissionEvent, ok := event.EventData.(CommissionReceiveEvent); ok {
				err = s.SendCommissionReceiveEvent(event.AccountID, event.DistinctID, commissionEvent)
			}
		default:
			log.Printf("未知事件类型: %s", event.EventName)
			continue
		}

		if err != nil {
			log.Printf("发送事件失败: %s, 错误: %v", event.EventName, err)
			return err
		}
	}

	return nil
}

// Close 关闭服务
func (s *ThinkingDataService) Close() error {
	return s.util.Close()
}

// =============================================================================
// 用户属性设置方法 - User Properties Methods
// =============================================================================

// getUserSellerAndChannel 获取用户的运营商和渠道信息
func getUserSellerAndChannel(userId int32) (sellerName string, channelName string) {
	// 获取用户信息
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(context.Background())
	user, err := userDb.Where(userDao.UserID.Eq(userId)).First()
	if err != nil {
		logs.Error("getUserSellerAndChannel 获取用户信息失败: userId=%d, err=%v", userId, err)
		return "", ""
	}

	// 获取运营商名称
	if user.SellerID > 0 {
		sellerDao := server.DaoxHashGame().XSeller
		sellerDb := sellerDao.WithContext(context.Background())
		seller, err := sellerDb.Where(sellerDao.SellerID.Eq(user.SellerID)).First()
		if err == nil {
			sellerName = seller.SellerName
		}
	}

	// 获取渠道名称
	if user.ChannelID > 0 {
		channelDao := server.DaoxHashGame().XChannel
		channelDb := channelDao.WithContext(context.Background())
		channel, err := channelDb.Where(channelDao.ChannelID.Eq(user.ChannelID)).First()
		if err == nil {
			channelName = channel.ChannelName
		}
	}

	return sellerName, channelName
}

// SetUserPropertiesOnRegister 用户注册时设置用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnRegister(accountID, distinctID string, user interface{}, registerType, email, phone, referralCode string) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取用户ID
	var userId int32
	if userModel, ok := user.(*model.XUser); ok {
		userId = userModel.UserID
	}

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"register_time":         timeStr,
		"register_type":         registerType,
		"first_login_time":      timeStr,
		"total_login_days":      1,
		"vip_level":             1, // 默认VIP等级为1
		"is_vip":                false,
		"total_deposit_amount":  0.0,
		"total_withdraw_amount": 0.0,
		"total_bet_amount":      0.0,
		"total_win_amount":      0.0,
		"deposit_count":         0,
		"withdraw_count":        0,
		"bet_count":             0,
		"last_active_time":      timeStr,
		"account_status":        "active",
		"seller":                seller,
		"channel":               channel,
	}

	// 设置邮箱（如果提供）
	if email != "" {
		properties["email"] = email
		properties["email_verified"] = true
	}

	// 设置手机号（如果提供）
	if phone != "" {
		properties["phone"] = phone
		properties["phone_verified"] = true
	}

	// 设置推荐码（如果提供）
	if referralCode != "" {
		properties["referral_code"] = referralCode
		properties["is_invited"] = true
	} else {
		properties["is_invited"] = false
	}

	// 使用UserSetOnce确保这些属性只设置一次
	return td.SetUserPropertiesOnce(accountID, distinctID, properties)
}

// SetUserPropertiesOnLogin 用户登录时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnLogin(accountID, distinctID string, userId int32, loginType string) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"last_login_time":  timeStr,
		"last_login_type":  loginType,
		"last_active_time": timeStr,
		"seller":           seller,
		"channel":          channel,
	}

	// 累加登录天数（这里简化处理，实际应该根据日期判断）
	addProperties := map[string]interface{}{
		"total_login_days": 1,
	}

	// 先设置基本属性
	err := td.SetUserProperties(accountID, distinctID, properties)
	if err != nil {
		return err
	}

	// 再累加登录天数
	return td.AddUserProperties(accountID, distinctID, addProperties)
}

// SetUserPropertiesOnVipLevelChange 用户VIP等级变化时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnVipLevelChange(accountID, distinctID string, userId int32, newLevel int, oldLevel int) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"vip_level":          newLevel,
		"is_vip":             newLevel > 1,
		"vip_upgrade_time":   timeStr,
		"previous_vip_level": oldLevel,
		"last_active_time":   timeStr,
		"seller":             seller,
		"channel":            channel,
	}

	return td.SetUserProperties(accountID, distinctID, properties)
}

// SetUserPropertiesOnPhoneBind 用户绑定手机时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnPhoneBind(accountID, distinctID string, userId int32, phoneNumber string) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"phone":            phoneNumber,
		"phone_verified":   true,
		"phone_bind_time":  timeStr,
		"last_active_time": timeStr,
		"seller":           seller,
		"channel":          channel,
	}

	return td.SetUserProperties(accountID, distinctID, properties)
}

// SetUserPropertiesOnEmailBind 用户绑定邮箱时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnEmailBind(accountID, distinctID string, userId int32, email string) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"email":            email,
		"email_verified":   true,
		"email_bind_time":  timeStr,
		"last_active_time": timeStr,
		"seller":           seller,
		"channel":          channel,
	}

	return td.SetUserProperties(accountID, distinctID, properties)
}

// SetUserPropertiesOnDeposit 用户充值时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnDeposit(accountID, distinctID string, userId int32, amount float64, isFirst bool) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"last_deposit_time":   timeStr,
		"last_deposit_amount": amount,
		"last_active_time":    timeStr,
		"seller":              seller,
		"channel":             channel,
	}

	// 如果是首次充值，设置首次充值相关属性
	if isFirst {
		properties["first_deposit_time"] = timeStr
		properties["first_deposit_amount"] = amount
		properties["is_depositor"] = true
	}

	// 累加充值相关数据
	addProperties := map[string]interface{}{
		"total_deposit_amount": amount,
		"deposit_count":        1,
	}

	// 先设置基本属性
	err := td.SetUserProperties(accountID, distinctID, properties)
	if err != nil {
		return err
	}

	// 再累加充值数据
	return td.AddUserProperties(accountID, distinctID, addProperties)
}

// SetUserPropertiesOnWithdraw 用户提现时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnWithdraw(accountID, distinctID string, userId int32, amount float64, isFirst bool) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"last_withdraw_time":   timeStr,
		"last_withdraw_amount": amount,
		"last_active_time":     timeStr,
		"seller":               seller,
		"channel":              channel,
	}

	// 如果是首次提现，设置首次提现相关属性
	if isFirst {
		properties["first_withdraw_time"] = timeStr
		properties["first_withdraw_amount"] = amount
	}

	// 累加提现相关数据
	addProperties := map[string]interface{}{
		"total_withdraw_amount": amount,
		"withdraw_count":        1,
	}

	// 先设置基本属性
	err := td.SetUserProperties(accountID, distinctID, properties)
	if err != nil {
		return err
	}

	// 再累加提现数据
	return td.AddUserProperties(accountID, distinctID, addProperties)
}

// SetUserPropertiesOnBet 用户投注时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnBet(accountID, distinctID string, userId int32, betAmount float64, winAmount float64, gameType string, isFirst bool) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"last_bet_time":    timeStr,
		"last_bet_amount":  betAmount,
		"last_game_type":   gameType,
		"last_active_time": timeStr,
		"seller":           seller,
		"channel":          channel,
	}

	// 如果是首次投注，设置首次投注相关属性
	if isFirst {
		properties["first_bet_time"] = timeStr
		properties["first_bet_amount"] = betAmount
		properties["first_game_type"] = gameType
		properties["is_player"] = true
	}

	// 累加投注相关数据
	addProperties := map[string]interface{}{
		"total_bet_amount": betAmount,
		"total_win_amount": winAmount,
		"bet_count":        1,
	}

	// 先设置基本属性
	err := td.SetUserProperties(accountID, distinctID, properties)
	if err != nil {
		return err
	}

	// 再累加投注数据
	return td.AddUserProperties(accountID, distinctID, addProperties)
}

// SetUserPropertiesOnActivity 用户参与活动时更新用户属性
func (td *ThinkingDataUtil) SetUserPropertiesOnActivity(accountID, distinctID string, userId int32, activityType string, rewardAmount float64) error {
	timeStr := time.Now().In(time.FixedZone("", 8*3600)).Format("2006-01-02 15:04:05")

	// 获取运营商和渠道信息
	seller, channel := getUserSellerAndChannel(userId)

	properties := map[string]interface{}{
		"last_activity_time":   timeStr,
		"last_activity_type":   activityType,
		"last_activity_reward": rewardAmount,
		"last_active_time":     timeStr,
		"seller":               seller,
		"channel":              channel,
	}

	// 累加活动相关数据
	addProperties := map[string]interface{}{
		"total_activity_reward": rewardAmount,
		"activity_count":        1,
	}

	// 将活动类型添加到用户参与的活动列表中
	appendProperties := map[string]interface{}{
		"participated_activities": []string{activityType},
	}

	// 先设置基本属性
	err := td.SetUserProperties(accountID, distinctID, properties)
	if err != nil {
		return err
	}

	// 累加活动数据
	err = td.AddUserProperties(accountID, distinctID, addProperties)
	if err != nil {
		return err
	}

	// 追加活动类型到列表
	return td.UniqAppendUserProperties(accountID, distinctID, appendProperties)
}

// =============================================================================
// 便捷方法 - Convenience Methods
// =============================================================================

// QuickSendEvent 快速发送事件（使用map）
func QuickSendEvent(accountID, distinctID, eventName string, properties map[string]interface{}) error {
	util := GetThinkingDataUtil()
	return util.TrackEvent(accountID, distinctID, eventName, properties)
}

// QuickSendStructEvent 快速发送结构化事件
func QuickSendStructEvent(accountID, distinctID, eventName string, event interface{}) error {
	util := GetThinkingDataUtil()
	return util.SendEvent(accountID, distinctID, eventName, event)
}

// GetService 获取事件发送服务实例
func GetService() *ThinkingDataService {
	return NewThinkingDataService()
}
