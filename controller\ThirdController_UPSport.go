package controller

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/des"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/oschwald/geoip2-golang"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// ag
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

func (c *ThirdController) DesCBCEncrypt(data, key, iv []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}

	data = c.pkcs5Padding(data, block.BlockSize())
	cryptText := make([]byte, len(data))

	blockMode := cipher.NewCBCEncrypter(block, iv)
	blockMode.CryptBlocks(cryptText, data)
	return cryptText, nil
}

func (c *ThirdController) pkcs5Padding(cipherText []byte, blockSize int) []byte {
	padding := blockSize - len(cipherText)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(cipherText, padText...)
}

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// easybet
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

func (c *ThirdController) easybet_http_post(path string, reqdata map[string]interface{}) *map[string]interface{} {
	reqdata["plat_id"] = c.easybet_platid
	keys := []string{}
	for k := range reqdata {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	reqstr := ""
	for i := 0; i < len(keys); i++ {
		reqstr += fmt.Sprintf("%s=%v&", keys[i], reqdata[keys[i]])
	}
	reqstr += c.easybet_key
	hashmd5 := md5.Sum([]byte(reqstr))
	sign := hex.EncodeToString(hashmd5[:])
	reqdata["sign"] = strings.ToUpper(sign)
	url := fmt.Sprintf("%s%s", c.easybet_url, path)
	header := req.Header{
		"Content-Type": "application/json;charset=UTF-8",
	}
	reqbytes, err := json.Marshal(&reqdata)
	resp, err := req.Post(url, header, string(reqbytes))
	if err != nil {
		logs.Error("easybet_http_post request error:", url, err)
		return nil
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("easybet_http_post body error:", url, err)
		return nil
	}
	logs.Debug("easybet_http_post:", url, "|", string(reqbytes), "|", string(body))
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		return nil
	}
	if _, ok := jdata["status"]; !ok {
		return nil
	}
	code, ok := jdata["status"].(float64)
	if !ok || int(code) != 0 {
		return nil
	}
	return &jdata
}

func (c *ThirdController) easybet_login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Lang string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	token := server.GetToken(ctx)

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}

	{
		data := gin.H{
			"username": fmt.Sprintf("%d", token.UserId),
		}
		c.easybet_http_post("/open_api/register", data)
	}
	{
		hash := md5.New()
		hash.Write([]byte(fmt.Sprintf("%v%v", token.UserId, c.easybet_key)))
		bs := hash.Sum(nil)
		data := gin.H{
			"username":            fmt.Sprintf("%d", token.UserId),
			"lang":                reqdata.Lang,
			"merchant_user_token": fmt.Sprintf("%x", bs),
		}
		jresult := c.easybet_http_post("/open_api/game_url", data)
		jextend := (*jresult)["data"].(map[string]interface{})
		ctx.Put("url", jextend["url"])
	}
	ctx.RespOK()
}

func (c *ThirdController) up_login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Lang      string `validate:"required"`
		ReturnURL string `json:"returnURL"` // 添加returnURL参数
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	token := server.GetToken(ctx)
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, nil)
		data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
		if data == nil {
			ctx.RespErrString(true, &errcode, "进入失败")
			return
		}
		istest := abugo.GetInt64FromInterface((*data)["IsTest"])
		if istest == 1 {
			ctx.RespErrString(true, &errcode, "该账号禁止进入")
			return
		}
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v_up1", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁, 请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)
	plaintext, _ := json.Marshal(map[string]interface{}{
		"userId":    token.UserId,
		"timestamp": time.Now().UnixMicro(),
	})

	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"
	encodedCiphertext := c.CBCEncrypt(string(plaintext), key)

	logs.Debug("Encryption  successful")

	userIP := ctx.GetIp()
	oddformat := getRegion(userIP)
	viewFormat := getView(userIP)
	logs.Debug("oddformat", oddformat)

	// 使用前端传递的returnURL，如果为空则使用默认值
	returnURL := "/sport/soccer"
	if reqdata.ReturnURL != "" {
		returnURL = reqdata.ReturnURL
	}

	u := fmt.Sprintf(c.up_url+"/UI/ExternalLogin?loginToken=%s&deviceType=desktop&lang=%s&oddformat=%s&viewFormat=%s&returnURL=%s",
		url.QueryEscape(encodedCiphertext),
		reqdata.Lang,
		oddformat,
		viewFormat, //returnURL
		returnURL,  //url.QueryEscape(returnURL)
	) // 确保returnURL被正确编码
	logs.Debug("up_login:", u)

	ctx.Put("url", u)
	ctx.RespOK()
}

func getIPContinent(ipStr string) string {
	// 打开 MaxMind 数据库文件
	db, err := geoip2.Open("./config/GeoLite2-Country.mmdb")
	if err != nil {
		logs.Error("geoip2.Open", err)
		return ""
	}
	defer db.Close()

	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		logs.Error("Invalid IP address format")
		return ""
	}

	// 查询 IP 地址的国家和洲信息
	record, err := db.Country(ip)
	if err != nil {
		logs.Error("db.Country(ip)", err)
		return ""
	}

	// 返回洲信息
	return record.Continent.Names["zh-CN"]
}

// getRegion 根据 IP 地址返回对应的盘类
func getRegion(ipStr string) string {
	// 根据洲分配盘类
	continent := getIPContinent(ipStr)
	logs.Debug("continent", continent)
	switch continent {
	case "亚洲":
		return "hongkong"
	case "欧洲":
		return "decimal"
		//case "北美洲", "南美洲":
		//	return "american"
		//	up体育亚洲地区使用港赔、其他地区使用欧赔
	case "北美洲", "南美洲":
		return "decimal"
	case "非洲", "南极洲", "大洋洲":
		return "decimal"
	default:
		return "decimal"
	}
}

// up体育亚洲视图控制（亚洲地区登入后展示亚洲让球）
func getView(ipStr string) string {
	// 根据洲分配盘类
	continent := getIPContinent(ipStr)
	logs.Debug("continent", continent)
	switch continent {
	case "亚洲":
		return "asian"
	case "欧洲":
		return "european"
	case "北美洲", "南美洲":
		return "american"
	case "非洲", "南极洲", "大洋洲":
		return "european"
	default:
		return "european"
	}
}

func generateRandomKey(length int) ([]byte, error) {
	key := make([]byte, length)
	_, err := rand.Read(key)
	if err != nil {
		return nil, err
	}
	return key, nil
}

func (e *ThirdController) CBCEncrypt(plaintext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	blockSize := len(key)
	padding := blockSize - len(plaintext)%blockSize // 填充字节
	if padding == 0 {
		padding = blockSize
	}

	// 填充 padding 个 byte(padding) 到 plaintext
	plaintext += string(bytes.Repeat([]byte{byte(padding)}, padding))
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err = rand.Read(iv); err != nil { // 将同时写到 ciphertext 的开头
		return ""
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], []byte(plaintext))

	return base64.StdEncoding.EncodeToString(ciphertext)
}

func (c *ThirdController) CBCDecrypt(ciphertext string, key string) string {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("cbc decrypt err:", err)
		}
	}()

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return ""
	}

	ciphercode, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return ""
	}

	iv := ciphercode[:aes.BlockSize]        // 密文的前 16 个字节为 iv
	ciphercode = ciphercode[aes.BlockSize:] // 正式密文

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphercode, ciphercode)

	plaintext := string(ciphercode) // ↓ 减去 padding
	return plaintext[:len(plaintext)-int(plaintext[len(plaintext)-1])]
}

func (c *ThirdController) UP_Token2UserId(token string) int {
	key := "hwWe\\mS2`kvu8,z/|hvop7^~)ZUgQhHT"

	payload := make(map[string]interface{})
	if err := json.Unmarshal([]byte(c.CBCDecrypt(token, key)), &payload); err != nil {
		logs.Debug("[ERR]UP_Token2UserId:", token, err, payload)
		return -1
	}
	return int(payload["userId"].(float64))
}

func (c *ThirdController) UP_UserAmount(UserId int) float64 {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Select("Amount").Where(where).GetOne()
	balance := float64(0)
	if udata != nil {
		balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	}
	return balance
}

func (c *ThirdController) TokenLogin(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID string `validate:"required"`
		Token     string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	logs.Debug("up TokenLogin:", reqdata)

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(UserId, "UP", "UP")
	if err != nil {
		logs.Error("UP", "检查游戏入口权限失败 userId=", UserId, " gameId=", "UP", " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	balance := c.UP_UserAmount(UserId)
	if UserId < 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "ERR",
			"ErrorDescription": "Token Error",
		})
		return
	}
	ctx.Gin().JSON(200, gin.H{
		"AccountID":        strconv.Itoa(UserId),
		"Balance":          balance,
		"Currency":         "CNY",
		"Status":           "OK",
		"ErrorDescription": "",
	})
}

func (c *ThirdController) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID string `validate:"required"`
		Token     string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	logs.Debug("==========up GetBalance:", reqdata)

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	balance := c.UP_UserAmount(UserId)

	ctx.Gin().JSON(200, gin.H{
		"Balance":          balance,
		"Status":           "OK",
		"ErrorDescription": "",
	})
}

func (c *ThirdController) PlaceBet(ctx *abugo.AbuHttpContent) {
	type Sport struct {
		ID   int    `validate:"required"`
		Name string `validate:"required"`
	}
	type Odd struct {
		Name string `validate:"required"`
	}
	type Event struct {
		Name string `validate:"required"`
	}
	type SelectionDetail struct {
		Sport Sport `validate:"required"`
		Odd   Odd   `validate:"required"`
		Event Event `validate:"required"`
	}
	type RequestData struct {
		RequestID         string            `validate:"required"`
		Token             string            `validate:"required"`
		Stake             float64           `validate:"required"`
		TakenAmount       float64           `validate:"required"`
		TicketID          int               `validate:"required"`
		GroupTicketID     int               `validate:"required"`
		BetType           string            `validate:"required"`
		DeviceType        string            `validate:"required"`
		SelectionsDetails []SelectionDetail `validate:"required"`
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	jsonData, _ := json.Marshal(&reqdata)
	logs.Debug("up PlaceBet:", string(jsonData))

	money := reqdata.Stake

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}

	err = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		var udata map[string]any
		err = tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).Where("UserID = ?", UserId).Scan(&udata).Error
		if err != nil {
			logs.Error("up 获取用户数据失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Balance":          0,
				"Status":           "NO",
				"ErrorDescription": "User not found",
			})
			return err
		}

		balance := abugo.GetFloat64FromInterface(udata["Amount"])

		if balance < money {
			ctx.Gin().JSON(200, gin.H{
				"Balance":          balance,
				"Status":           "NO",
				"ErrorDescription": "Balance NOT enough",
			})
			return errors.New("余额不足")
		}
		before := balance
		balance = balance - money
		err = tx.Table("x_user").Where("UserId = ?", UserId).Update("amount", balance).Error
		if err != nil {
			logs.Error("up 更新用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Balance":          balance,
				"Status":           "NO",
				"ErrorDescription": "Update balance fail",
			})
			return err
		}
		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface(udata["Token"])).Host)

		bs, _ := json.Marshal(&reqdata)
		order := map[string]any{
			"SellerId":     udata["SellerId"],
			"ChannelId":    udata["ChannelId"],
			"BetChannelId": ChannelId,
			"UserId":       UserId,
			"Brand":        "UP",
			"ThirdId":      strconv.Itoa(reqdata.TicketID),
			"GameId":       "",
			"GameName":     "",
			"BetAmount":    reqdata.Stake,
			"WinAmount":    0,
			"ValidBet":     0,
			"ThirdTime":    utils.GetCurrentTime(),
			"BetTime":      utils.GetGMT2Time(), //up体育GMT2时区
			"BetLocalTime": utils.GetCurrentTime(),
			"Currency":     "CNY",
			"RawData":      string(bs),
			"DataState":    -1,
		}
		// 在插入订单之前检查 BetType
		if reqdata.BetType == "Single" && len(reqdata.SelectionsDetails) > 0 {
			var sportsMapping = map[string]string{
				"Alpine Skiing":     "高山滑雪",
				"American Football": "美式足球",
				"Athletics":         "田径",
				"Aussie Rules":      "澳式足球",
				"Badminton":         "羽毛球",
				"Baseball":          "棒球",
				"Basketball":        "篮球",
				"Beach Volley":      "沙滩排球",
				"Biathlon":          "冬季两项",
				"Boxing":            "拳击",
				"Chess":             "国际象棋",
				"Cricket":           "板球",
				"Curling":           "冰壶",
				"Cycling":           "骑自行车",
				"Darts":             "飞镖",
				"eSports":           "电竞",
				"Floorball":         "地板球",
				"Formula 1":         "方程式赛车",
				"Futsal":            "五人制足球",
				"Golf":              "高尔夫",
				"Handball":          "手球",
				"Horse Racing":      "赛马",
				"Ice Hockey":        "冰球",
				"MMA":               "综合格斗",
				"Motor Sports":      "摩托运动或汽车运动",
				"NASCAR":            "美国全国股车汽车比赛协会",
				"Other Sports":      "其他运动",
				"Politics":          "政治",
				"Rugby":             "橄榄球",
				"Ski Jumping":       "跳台滑雪",
				"Snooker":           "斯诺克",
				"Soccer":            "足球",
				"Specials":          "特殊项目",
				"Sumo":              "相扑",
				"Table Tennis":      "乒乓球",
				"Tennis":            "网球",
				"Volleyball":        "排球",
				"Waterpolo":         "水球",
				"Wrestling":         "摔角",
			}

			firstSelection := reqdata.SelectionsDetails[0]
			order["GameId"] = strconv.Itoa(firstSelection.Sport.ID)
			// 检查firstSelection.Sport.Name是否存在于映射中
			if chineseName, ok := sportsMapping[firstSelection.Sport.Name]; ok {
				order["GameName"] = chineseName
			} else {
				// 如果映射中不存在对应的中文名称，可以选择保留原名称或处理方式
				order["GameName"] = firstSelection.Sport.Name
			}
		} else {
			// 如果不是 Single BetType 或没有选择细节，使用原始的赋值
			order["GameId"] = reqdata.BetType
			// 定义BetType到GameName的映射
			betTypeToGameName := map[string]string{
				"Combo":      "组合投注",
				"System":     "系统投注",
				"Ifbet":      "如果投注",
				"Reversebet": "反向投注",
				"Teaser":     "调整点数投注",
			}

			// 查找并设置GameName
			if gameName, ok := betTypeToGameName[reqdata.BetType]; ok {
				order["GameName"] = gameName
			} else {
				order["GameName"] = reqdata.BetType // 默认值或其他处理
			}
		}
		err = tx.Table("x_third_sport_pre_order").Create(&order).Error
		if err != nil {
			logs.Error("up 创建注单失败 error=", err)
			return err
		}
		amountLog := map[string]any{
			"UserId":       UserId,
			"BeforeAmount": before,
			"Amount":       0 - money,
			"AfterAmount":  balance,
			"Reason":       utils.BalanceCReasonUPBet,
			"Memo":         "up bet,thirdId:" + strconv.Itoa(reqdata.TicketID),
			"SellerId":     udata["SellerId"],
			"ChannelId":    udata["ChannelId"],
		}
		err = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if err != nil {
			logs.Error("up 创建余额记录失败 error=", err)
			return err
		}

		ctx.Gin().JSON(200, gin.H{
			"Balance":          balance,
			"Status":           "OK",
			"ErrorDescription": "",
		})

		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		tmpErr := SocketHandler.RefreshUserAmount(notifyUserId)
		if tmpErr != nil {
			logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
		}
	}(UserId)
}

func (c *ThirdController) AcceptBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID string  `validate:"required"`
		Token     string  `validate:"required"`
		Stake     float64 `validate:"required"`
		TicketID  int     `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	//logs.Debug("up AcceptBet:", reqdata)
	jsonData, _ := json.Marshal(&reqdata)
	logs.Debug("up AcceptBet:", string(jsonData))

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	balance := c.UP_UserAmount(UserId)

	ctx.Gin().JSON(200, gin.H{
		"Balance":          balance,
		"Status":           "OK",
		"ErrorDescription": "",
	})
}

func (c *ThirdController) DeclineBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID string `validate:"required"`
		Token     string `validate:"required"`
		TicketID  int    `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	//logs.Debug("up DeclineBet:", reqdata)
	jsonData, _ := json.Marshal(&reqdata)
	logs.Debug("up DeclineBet:", string(jsonData))

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	ticketID := strconv.Itoa(reqdata.TicketID)

	_ = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		var udata map[string]any
		err := tx.Table("x_third_sport_pre_order").Clauses(clause.Locking{Strength: "UPDATE"}).Where("Brand='up' AND ThirdId =?", ticketID).Scan(&udata).Error
		if err != nil {
			logs.Error("up settle 获取注单数据失败 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get order fail",
			})
			return err
		}
		if _, ok := udata["WinAmount"]; !ok {
			logs.Error("up settle 找不到注单数据 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "order not found",
			})
			return err
		}
		BetAmount := abugo.GetFloat64FromInterface(udata["BetAmount"])
		err = tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).Where("UserId = ?", UserId).Find(&udata).Error
		if err != nil {
			logs.Error("up settle 获取用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get balance fail",
			})
			return err
		}
		before := abugo.GetFloat64FromInterface(udata["Amount"])
		after := before + BetAmount
		err = tx.Exec("update x_user set amount =? where UserId = ?", after, UserId).Error
		if err != nil {
			logs.Error("up settle 更新用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update user balance fail",
			})
			return err
		}

		err = tx.Exec("update x_third_sport_pre_order set ValidBet = 0,DataState=-2 where ThirdId = ?", ticketID).Error
		if err != nil {
			logs.Error("up settle 更新注单失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update user balance fail",
			})
			return err
		}

		amountLog := map[string]any{
			"UserId":       UserId,
			"BeforeAmount": before,
			"Amount":       BetAmount,
			"AfterAmount":  after,
			"Reason":       utils.BalanceCReasonUPCancel,
			"Memo":         "up cancel,thirdId:" + ticketID,
			"SellerId":     udata["SellerId"],
			"ChannelId":    udata["ChannelId"],
		}
		err = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if err != nil {
			logs.Error("up settle 创建余额日志 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "create wallet log fail",
			})
			return err
		}

		ctx.Gin().JSON(200, gin.H{
			"Balance":          after,
			"Status":           "OK",
			"ErrorDescription": "",
		})
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		tmpErr := SocketHandler.RefreshUserAmount(notifyUserId)
		if tmpErr != nil {
			logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
		}
	}(UserId)
}

const (
	UltraplayBetStatusPending   = "Pending"
	UltraplayBetStatusCancelled = "Cancelled"
	UltraplayBetStatusWin       = "Win"
	UltraplayBetStatusLost      = "Lost"
	UltraplayBetStatusHalfWin   = "HalfWin"
	UltraplayBetStatusHalfLose  = "HalfLose"
	UltraplayBetStatusRefund    = "Refund"
)

func (c *ThirdController) SettleBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID               string                     `validate:"required"`
		Token                   string                     `validate:"required"`
		TicketID                int                        `validate:"required"`
		Payout                  *float64                   `validate:"required"`
		PayoutStatus            *int                       `validate:"required"`
		CommitDate              string                     `validate:"required"`
		Status                  string                     `validate:"required"`
		SelectionsStatus        [](map[string]interface{}) `validate:"required"`
		EventsInfo              [](map[string]interface{}) `validate:"required"`
		SelectionsPlayerChanges [](map[string]interface{}) `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	//logs.Debug("up SettleBet:", reqdata)
	jsonData, _ := json.Marshal(&reqdata)
	logs.Debug("up SettleBet:", string(jsonData))

	if reqdata.Status == "Pending" {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	ticketID := strconv.Itoa(reqdata.TicketID)
	_ = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		var udata map[string]any
		err := tx.Table("x_third_sport_pre_order").Clauses(clause.Locking{Strength: "UPDATE"}).Where("Brand='up' AND ThirdId =?", ticketID).Scan(&udata).Error
		if err != nil {
			logs.Error("up settle 获取注单数据失败 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get order fail",
			})
			return err
		}
		if _, ok := udata["WinAmount"]; !ok {
			logs.Error("up settle 找不到注单数据 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "order not found",
			})
			return err
		}
		WinAmount := abugo.GetFloat64FromInterface(udata["WinAmount"])

		type Sport struct {
			ID   int    `validate:"required"`
			Name string `validate:"required"`
		}
		type Odd struct {
			Name string `validate:"required"`
		}
		type Event struct {
			Name string `validate:"required"`
		}
		type SelectionDetail struct {
			Sport Sport `validate:"required"`
			Odd   Odd   `validate:"required"`
			Event Event `validate:"required"`
		}
		type RawData struct {
			RequestID         string            `validate:"required"`
			Token             string            `validate:"required"`
			Stake             float64           `validate:"required"`
			TakenAmount       float64           `validate:"required"`
			TicketID          int               `validate:"required"`
			GroupTicketID     int               `validate:"required"`
			BetType           string            `validate:"required"`
			DeviceType        string            `validate:"required"`
			SelectionsDetails []SelectionDetail `validate:"required"`
		}

		thirdTime := time.Now().Format("2006-01-02 15:04:05")
		payout := *reqdata.Payout - WinAmount
		validBet := ultraplayValidBet(reqdata.Status, abugo.GetFloat64FromInterface(udata["BetAmount"]), *reqdata.Payout)
		err = tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).Where("UserId = ?", UserId).Find(&udata).Error
		if err != nil {
			logs.Error("up settle 获取用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get balance fail",
			})
			return err
		}
		before := abugo.GetFloat64FromInterface(udata["Amount"])
		after := before + payout

		err = tx.Exec("update x_user set amount =? where UserId = ?", after, UserId).Error
		if err != nil {
			logs.Error("up settle 更新用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update user balance fail",
			})
			return err
		}
		err = tx.Exec("update x_third_sport_pre_order set WinAmount = ?, ValidBet = ?, DataState = 1, ThirdTime = ?, SettleTime = ? where ThirdId = ?", reqdata.Payout, validBet, thirdTime, utils.GetGMT2Time(), ticketID).Error
		if err != nil {
			logs.Error("up settle 更新注单失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update user balance fail",
			})
			return err
		}
		betTranData := udata
		delete(betTranData, "Id")
		delete(betTranData, "CreateTime")
		betTranData["WinAmount"] = reqdata.Payout
		betTranData["ValidBet"] = validBet
		betTranData["DataState"] = 1
		betTranData["SettleTime"] = utils.GetGMT2Time() //up 体育结算时间是GMT2 三方没传时间故取当前时间

		// 从RawData中获取下注内容
		rawData := RawData{}
		json.Unmarshal([]byte(udata["RawData"].(string)), &rawData)
		betCtx := ""
		gameRst := ""
		for _, detail := range rawData.SelectionsDetails {
			if len(rawData.SelectionsDetails) > 1 {
				betCtx += detail.Odd.Name + ","
				gameRst += detail.Event.Name + ","
			} else {
				betCtx = detail.Odd.Name
				gameRst = detail.Event.Name
			}
		}
		betTranData["BetCtx"] = betCtx
		betTranData["GameRst"] = gameRst
		betTranData["ThirdTime"] = thirdTime

		err = tx.Exec("update x_third_sport_pre_order set BetCtx = ?, GameRst = ?, ThirdTime = ? where ThirdId = ?", betCtx, gameRst, thirdTime, ticketID).Error
		if err != nil {
			logs.Error("up settle 更新注单失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update order fail",
			})
			return err
		}
		err = tx.Table("x_third_sport").Create(betTranData).Error
		if err != nil {
			logs.Error("up settle 创建注单失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "create order fail",
			})
			return err
		}

		amountLog := map[string]any{
			"UserId":       UserId,
			"BeforeAmount": before,
			"Amount":       payout,
			"AfterAmount":  after,
			"Reason":       utils.BalanceCReasonUPWin,
			"Memo":         "up settle,thirdId:" + ticketID,
			"SellerId":     udata["SellerId"],
			"ChannelId":    udata["ChannelId"],
		}
		err = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if err != nil {
			logs.Error("up settle 创建余额日志 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "create wallet log fail",
			})
			return err
		}

		// bs, _ := json.Marshal(&reqdata)

		ctx.Gin().JSON(200, gin.H{
			"Balance":          after,
			"Status":           "OK",
			"ErrorDescription": "",
		})
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		tmpErr := SocketHandler.RefreshUserAmount(notifyUserId)
		if tmpErr != nil {
			logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
		}
	}(UserId)
}

func (c *ThirdController) UnsettleBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RequestID        string                     `validate:"required"`
		Token            string                     `validate:"required"`
		TicketID         int                        `validate:"required"`
		Payout           *float64                   `validate:"required"`
		PayoutStatus     *int                       `validate:"required"`
		SelectionsStatus [](map[string]interface{}) `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	//logs.Debug("up UnsettleBet:", reqdata)
	jsonData, _ := json.Marshal(&reqdata)
	logs.Debug("up UnsettleBet:", string(jsonData))

	UserId := c.UP_Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Status":           "no",
			"ErrorDescription": "",
		})
		return
	}
	ticketID := strconv.Itoa(reqdata.TicketID)
	_ = server.Db().GormDao().Transaction(func(tx *gorm.DB) error {
		var udata map[string]any
		err := tx.Table("x_third_live_pre_order").Clauses(clause.Locking{Strength: "UPDATE"}).Where("Brand = 'UP' AND ThirdId=?", ticketID).Limit(1).Scan(&udata).Error
		if err != nil {
			logs.Error("up unsettle 获取注单数据失败 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get order fail",
			})
			return err
		}
		if _, ok := udata["WinAmount"]; !ok {
			logs.Error("up unsettle 找不到注单数据 error=", err)

			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "order not found",
			})
			return err
		}
		WinAmount := abugo.GetFloat64FromInterface(udata["WinAmount"])

		err = tx.Table("x_user").Clauses(clause.Locking{Strength: "UPDATE"}).Where("UserId = ?", UserId).Scan(&udata).Error
		if err != nil {
			logs.Error("up unsettle 获取用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "get balance fail",
			})
			return err
		}

		payout := *reqdata.Payout - WinAmount
		before := abugo.GetFloat64FromInterface(udata["Amount"])
		after := before + payout

		err = tx.Exec("update x_user set amount =? where UserId = ?", after, UserId).Error
		if err != nil {
			logs.Error("up unsettle 更新用户余额失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update user balance fail",
			})
			return err
		}

		err = tx.Exec("update x_third_live_pre_order set WinAmount = ?, ValidBet = 0, DataState = -3 where ThirdId = ?", reqdata.Payout, reqdata.TicketID).Error
		if err != nil {
			logs.Error("up unsettle 更新注单失败 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "update order fail",
			})
			return err
		}

		amountLog := map[string]any{
			"UserId":       UserId,
			"BeforeAmount": before,
			"Amount":       payout,
			"AfterAmount":  after,
			"Reason":       utils.BalanceCReasonUPWin,
			"Memo":         "up unsettle,thirdId:" + ticketID,
			"SellerId":     udata["SellerId"],
			"ChannelId":    udata["ChannelId"],
		}
		err = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if err != nil {
			logs.Error("up unsettle 创建余额日志 error=", err)
			ctx.Gin().JSON(200, gin.H{
				"Status":           "no",
				"ErrorDescription": "create wallet log fail",
			})
			return err
		}
		// bs, _ := json.Marshal(&reqdata)

		ctx.Gin().JSON(200, gin.H{
			"Balance":          after,
			"Status":           "OK",
			"ErrorDescription": "",
		})
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		tmpErr := SocketHandler.RefreshUserAmount(notifyUserId)
		if tmpErr != nil {
			logs.Error("[ERROR][AstarSwSrvice] Rollin 发送余额变动通知错误", notifyUserId, tmpErr.Error())
		}
	}(UserId)
}

func ultraplayValidBet(_ string, betAmt, payout float64) float64 {
	if payout > 0 {
		subAmount := payout - betAmt

		if subAmount > betAmt {
			return betAmt
		} else {
			return math.Abs(subAmount)
		}
	} else {
		return betAmt
	}

	// switch status {
	// case UltraplayBetStatusHalfWin, UltraplayBetStatusHalfLose:
	// 	return betAmt / 2
	// case UltraplayBetStatusWin:
	// 	// use math max prevent negative value
	// 	return math.Max(payout-betAmt, 0)
	// case UltraplayBetStatusLost:
	// 	return betAmt
	// }
	// return 0
}

// GetListWithStatusCheck 处理GetList请求并异步检查用户输光状态
func (c *ThirdController) GetListWithStatusCheck(ctx *abugo.AbuHttpContent) {
	c.tradingService.GetList(ctx)

	//// 保存原始请求体，因为读取后会消耗
	//token := server.GetToken(ctx)
	//if token == nil {
	//	//logs.Error("GetList 获取token错误")
	//	// 如果解析失败，仍然调用原始方法，但不执行状态检查
	//	c.tradingService.GetList(ctx)
	//	return
	//}
	////logs.Info("GetList 获取token成功, userId=", token.UserId)
	//userId := token.UserId
	//if userId <= 0 {
	//	logs.Error("GetList  获取用户登录状态错误 userId=", userId)
	//	c.tradingService.GetList(ctx)
	//	return
	//}
	//go func(userId int) {
	//	userLostStatus := &third.UserLostStatus{}
	//	_, err := userLostStatus.CheckAndUpdateLostAllOnceStatus(userId)
	//	if err != nil {
	//		logs.Error("异步检查用户输光状态失败 userId=", userId, " err=", err.Error())
	//	}
	//}(userId)
	//// 调用原始的GetList方法 - 这一行确保原始方法被调用
	//c.tradingService.GetList(ctx)
}
