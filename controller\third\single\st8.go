package single

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"github.com/google/uuid"
	"github.com/zhms/xgo/xgo"

	"github.com/beego/beego/logs"
	"github.com/go-playground/validator/v10"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

const (
	ST8CacheExpireTime = 1800 // 缓存过期时间30分钟
	cacheKeyST8        = "cacheKeyST8:"
	cacheKeyST8Info    = "cacheKeyST8:Info:%s"

	// ST8默认公钥
	defaultST8PublicKey = `-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEE7KWVcYKu2ewHcRxiW8gHbvo6dt1
AtjQWGMgJv2Bx8sWe72gpux/cf8asKgPIk+xgIcH3HvW5VNmChERc/as7A==
-----END PUBLIC KEY-----`

	// ST8默认私钥
	defaultST8PrivateKey = `**********************************************************************************************************************************************************************************************************************************`
)

//st8游戏存在以下情况：
//特殊金额处理：
//零金额下注处理 (处理免费游戏的情况)
//余额不足情况处理
//零金额派彩处理 (处理游戏损失或回合结束)

//多重操作：
//同一注单 多次下注
//同一注单 多次派彩
//同一注单 多次取消
//部分取消后的派彩处理

//重复请求处理：
//重复下注请求 (不应重复扣款)
//重复派彩请求 (不应重复加款)
//重复取消请求 (不应重复操作)
//新增参数的向后兼容性

//特殊场景处理：
//游戏内大厅切换游戏代码
//修正下注导致负余额

//并行回合:
//测试同时进行的多个游戏回合
//验证系统正确处理并行交易
//测试取消派彩后的新派彩处理

//特殊说明:
//关于free_debit金额：
//通常为0，不需要实际扣款
//某些情况下可能是"折扣"投注（例如：投注10只扣1）
//关于奖金ID引用：
//通过ST8 Bonus API或后台发放的奖金会包含ID
//供应商或运营商后台发起的奖金可能没有ID
//关于free_credit：
//许多供应商发送free_credit时可能没有对应的free_debit
//运营商必须能处理这种情况
//重要实现注意点:
//
//correction_debit 必须特殊处理：
//即使余额不足也要接受
//可以产生负余额
//必须返回成功响应
//奖金系统集成：
//需要支持单独的奖金余额
//处理没有对应扣款的派彩
//交易关联：
//部分交易可以不关联游戏回合
//部分交易可以在玩家离线时处理

var tzUTC8St8 = time.FixedZone("UTC+8", 8*60*60)

// ST8Service ST8游戏服务结构体
type ST8Service struct {
	OperatorCode string // 运营商代码
	SiteID       string // 站点ID
	BaseHost     string // 基础API地址
	AssetsHost   string // 资源服务器地址

	// 添加亚洲区域配置
	AsiaOperatorCode string // 亚洲区域运营商代码
	AsiaSiteID       string // 亚洲区域站点ID
	AsiaBaseHost     string // 亚洲区域基础API地址
	AsiaPublicKey    string // 亚洲区域公钥
	AsiaPrivateKey   string // 亚洲区域私钥
	AsiaCurrency     string // 亚洲区域货币代码

	// 添加世界其他地区配置
	RowOperatorCode string // 世界其他地区运营商代码
	RowSiteID       string // 世界其他地区站点ID
	RowBaseHost     string // 世界其他地区基础API地址
	RowPublicKey    string // 世界其他地区公钥
	RowPrivateKey   string // 世界其他地区私钥
	RowCurrency     string // 世界其他地区货币代码

	// 密钥配置
	PrivateKey string // 私钥文件路径
	PublicKey  string // 公钥文件路径

	Currency  string // 货币代码
	Language  string // 语言
	brandName string // 厂商标识，固定为 "HS8_"

	Lobby   string // 大厅配置
	Deposit string // 存款配置

	RefreshUserAmountFunc func(int) error
	mu                    sync.Mutex
	thirdGamePush         *base.ThirdGamePush
}

// ST8响应状态
const (
	ST8_STATUS_OK               = "ok"                    // 成功
	ST8_STATUS_PLAYER_LOCKED    = "player_locked"         // 玩家被锁定
	ST8_STATUS_SESSION_EXPIRED  = "session_expired"       // 会话过期
	ST8_STATUS_PLAYER_NOT_FOUND = "player_not_found"      // 玩家未找到
	ST8_STATUS_NOT_ENOUGH_MONEY = "not_enough_money"      // 余额不足
	ST8_STATUS_TRANS_NOT_FOUND  = "transaction_not_found" // 交易未找到
	ST8_STATUS_GAME_DISABLED    = "game_disabled"         // 游戏已禁用
	ST8_STATUS_SITE_DISABLED    = "site_disabled"         // 站点已禁用
	ST8_STATUS_SPENDING_LIMIT   = "spending_limit"        // 超出消费限制
	ST8_STATUS_AUTH_FAILED      = "auth_failed"           // 认证失败
	ST8_STATUS_UNKNOWN          = "unknown"               // 未知错误
)

// ST8账变常量
const (
	ST8_AMOUNT_DEBIT  = 1 // DEBIT//扣款
	ST8_AMOUNT_CREDIT = 2 // CREDIT//加款
	ST8_AMOUNT_CANCEL = 3 // CANCEL//取消余额
	ST8_AMOUNT_BUYIN  = 4 // BUYIN
	ST8_AMOUNT_PAYOUT = 5 // PAYOUT
)

// ST8Response ST8标准响应结构体
type ST8Response struct {
	Status   string `json:"status"`   // 状态: ok/player_locked/session_expired/player_not_found/not_enough_money/transaction_not_found/game_disabled/site_disabled/spending_limit/auth_failed/unknown
	Balance  string `json:"balance"`  // 玩家钱包当前余额,字符串格式的浮点数(Decimal)
	Currency string `json:"currency"` // 当前钱包的货币代码,必须符合ISO 4217标准
}

// AmountChangeLogSt8 账变记录结构
type AmountChangeLogSt8 struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

// ThirdOrderST8 订单结构
type ThirdOrderST8 struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

// NewST8Logic 创建ST8服务实例
func NewST8Logic(params map[string]string, fc func(int) error) *ST8Service {
	// 使用默认值或参数中的值
	privateKey := defaultST8PrivateKey
	if params["private_key"] != "" {
		privateKey = params["private_key"]
	}
	publicKey := defaultST8PublicKey
	if params["public_key"] != "" {
		publicKey = params["public_key"]
	}
	currency := "BRL"
	if params["currency"] != "" {
		currency = params["currency"]
	}

	// 亚洲区域配置
	asiaOperatorCode := params["asia.operator_code"]
	asiaSiteID := params["asia.site_id"]
	asiaBaseHost := params["asia.url"]

	asiaPublicKey := `MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEab79BU5BVFT5voGgJC7HOE7wQx/U
0N1R6bMV4PnXF0z7BhC6960B3/T0rCjJ+Q1g0sqhchFIn48nQlvhdQvIJg==`
	if params["asia.public_key"] != "" {
		asiaPublicKey = params["asia.public_key"]
	}

	asiaPrivateKey := privateKey
	if params["asia.private_key"] != "" {
		asiaPrivateKey = params["asia.private_key"]
	}
	asiaCurrency := params["asia.currency"]
	rowOperatorCode := params["row.operator_code"]
	rowSiteID := params["row.site_id"]
	rowBaseHost := params["row.url"]
	rowPublicKey := `MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEISFxGxWigLNqvdikMI1VURmZh5t7
hW88VScsv5HWlNWl3Weekbt0UR6d0VrLRItC9TrgzhHwuuz5vc/cSD56EA==`
	if params["row.public_key"] != "" {
		rowPublicKey = params["row.public_key"]
	}
	rowPrivateKey := privateKey
	if params["row.private_key"] != "" {
		rowPrivateKey = params["row.private_key"]
	}
	rowCurrency := params["row.currency"]

	return &ST8Service{
		// 默认配置
		PublicKey:  publicKey,
		PrivateKey: privateKey,
		Currency:   currency,

		// 亚洲区域配置
		AsiaOperatorCode: asiaOperatorCode,
		AsiaSiteID:       asiaSiteID,
		AsiaBaseHost:     asiaBaseHost,
		AsiaPublicKey:    asiaPublicKey,
		AsiaPrivateKey:   asiaPrivateKey,
		AsiaCurrency:     asiaCurrency,

		// 世界其他地区配置
		RowOperatorCode: rowOperatorCode,
		RowSiteID:       rowSiteID,
		RowBaseHost:     rowBaseHost,
		RowPublicKey:    rowPublicKey,
		RowPrivateKey:   rowPrivateKey,
		RowCurrency:     rowCurrency,

		AssetsHost:            params["assets_host"],
		Language:              params["language"],
		Lobby:                 params["lobby"],
		Deposit:               params["deposit"],
		brandName:             "HS8_", //st8聚合厂家代码前缀 写死的不能修改
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}
func (s *ST8Service) in_array_string(arr []string, m string) bool {
	searchName := strings.TrimSpace(strings.ToLower(m)) // 去除空格并转小写
	for _, v := range arr {
		if strings.TrimSpace(strings.ToLower(v)) == searchName { // 比较时都去除空格并转小写
			return true
		}
	}
	return false
}

// generateECDSASignature 使用ECDSA私钥生成签名
func (s *ST8Service) generateECDSASignature(payload []byte, priKey string) (string, error) {
	key := fmt.Sprintf("-----BEGIN EC PRIVATE KEY-----\n%s\n-----END EC PRIVATE KEY-----",
		strings.TrimSpace(priKey))
	// 解析私钥
	block, _ := pem.Decode([]byte(key))
	if block == nil {
		return "", errors.New("failed to parse private key")
	}

	privateKey, err := x509.ParseECPrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("failed to parse EC private key: %v", err)
	}

	// 计算消息的SHA256哈希
	hash := sha256.Sum256(payload)

	// 使用私钥签名，使用 crypto/rand 作为随机数源
	signature, err := ecdsa.SignASN1(rand.Reader, privateKey, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign message: %v", err)
	}

	// Base64编码签名
	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifyECDSASignature 使用ECDSA公钥验证签名
func (s *ST8Service) verifyECDSASignature(payload []byte, signatureStr string, pubKey string) (bool, error) {
	// 解析公钥

	key := fmt.Sprintf("-----BEGIN PUBLIC KEY-----\n%s\n-----END PUBLIC KEY-----",
		strings.TrimSpace(pubKey))
	block, _ := pem.Decode([]byte(key))
	if block == nil {
		return false, errors.New("failed to parse public key")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return false, fmt.Errorf("failed to parse public key: %v", err)
	}

	ecdsaPublicKey, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return false, errors.New("not an ECDSA public key")
	}

	// Base64解码签名
	signature, err := base64.StdEncoding.DecodeString(signatureStr)
	if err != nil {
		return false, fmt.Errorf("failed to decode signature: %v", err)
	}

	// 计算消息的SHA256哈希
	hash := sha256.Sum256(payload)

	// 验证签名
	return ecdsa.VerifyASN1(ecdsaPublicKey, hash[:], signature), nil
}

// Balance 查询余额
func (s *ST8Service) Balance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Player   string `json:"player"`   // Unique player ID (case-sensitive) on Operator's platform
		Currency string `json:"currency"` // Current currency code in player's active wallet. MUST comply with ISO 4217 Alpha-3 standard
		Site     string `json:"site"`     // Operator's site where game is being played
		Token    string `json:"token"`    // Player's gameplay session token, max length 255 characters
	}

	type ResponseData struct {
		Status   string `json:"status"`   // Status: ok/player_locked/session_expired/player_not_found/not_enough_money/transaction_not_found/game_disabled/site_disabled/spending_limit/auth_failed/unknown
		Balance  string `json:"balance"`  // Current balance in player's wallet, stringified float value
		Currency string `json:"currency"` // Current currency code in player's active wallet, ISO 4217 Alpha-3 standard
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Balance api Body=", string(bodyBytes))

	response := ResponseData{
		Status: ST8_STATUS_UNKNOWN,
	}

	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Balance api read body error: err=", err)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}
	var reqdata RequestData
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Balance api request decode error: err=", err)
		return
	}

	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	// 验证必填字段
	if reqdata.Player == "" || reqdata.Currency == "" || reqdata.Site == "" {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Balance api missing required fields, request: ", reqdata)
		return
	}
	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	// 获取用户余额
	//获取余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyST8)
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8  Balance 查询用户余额失败: err=", err, " response: ", response)
		return
	}
	// 返回成功响应
	response.Status = ST8_STATUS_OK
	response.Balance = strconv.FormatFloat(balance, 'f', 2, 64)
	response.Currency = reqdata.Currency
	ctx.RespJson(response)
	logs.Info("st8 Balance api success, response: ", response)
}

// Debit 投注扣款
// 操作员应该能够在同一轮中处理多个借记请求。
// 流程1：即使其中一笔借记被取消，操作员也应该能够处理贷记。
// 更正借记导致负余额:操作员必须接受correction_debit，这会导致玩家余额为负数
// 1. 余额请求
// 2. 玩家用全部余额下注
// 3. 更正借记
// 4. 补偿金请求
// 流程2：能够处理同一轮次中的部分取消,支持取消后的新借记请求
// 1. 余额请求
// 2. 借记请求
// 3. 取消第一笔借记请求
// 4. 同一轮次的另一个借记请求
// 5. 第二次借记的信用请求
// 流程3：支持同一轮次中的多笔连续借记
// 1. 余额请求
// 2. 借记请求
// 3. 同一轮次的另一个借记请求
// 4. 同一轮次的另一个借记请求
// 5. 同一轮次的另一个借记请求
// 6. 补偿金请求 (0.363s)
func (s *ST8Service) Debit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Player        string `json:"player"`         // 玩家ID(区分大小写)
		Site          string `json:"site"`           // 运营商站点
		Token         string `json:"token"`          // 游戏会话token,最大长度255字符
		TransactionId string `json:"transaction_id"` // ST8系统中的唯一交易ID
		Round         string `json:"round"`          // ST8系统中的唯一局号ID
		RoundClosed   *bool  `json:"round_closed"`   // 游戏局是否结束
		GameCode      string `json:"game_code"`      // 游戏ID
		DeveloperCode string `json:"developer_code"` // 开发商代码
		Amount        string `json:"amount"`         // 金额,字符串格式的浮点数
		Currency      string `json:"currency"`       // 货币代码,必须符合ISO 4217标准
		ProviderKind  string `json:"provider_kind"`  // 提供商交易类型
		Provider      struct {
			TransactionId string `json:"transaction_id"` // 提供商系统中的交易ID
			Amount        string `json:"amount"`         // 提供商货币中的原始交易金额
			Currency      string `json:"currency"`       // 提供商端的游戏会话货币
			Player        string `json:"player"`         // 提供商系统中的玩家名称
			Round         string `json:"round"`          // 提供商系统中的局号ID
		} `json:"provider"` // 提供商端游戏局的相关信息
		Bonus *struct {
			InstanceId string `json:"instance_id"` // ST8系统中的奖金唯一ID
			Status     string `json:"status"`      // 奖金状态:processing/active/accepted/rejected/finished/canceled/failed
			BonusId    string `json:"bonus_id"`    // 运营商平台中的奖金唯一ID
		} `json:"bonus"` // 奖金相关信息
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Bet api Body=", string(bodyBytes))

	// 统一使用ST8Response响应
	response := ST8Response{
		Status: ST8_STATUS_UNKNOWN, // 默认未知状态
	}

	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Bet 参数错误: err=", err)
		return
	}

	var reqdata RequestData
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		logs.Error("st8 Bet JSON解析失败: err=", err)
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	transactionId := reqdata.TransactionId //三方请求唯一流水号
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, transactionId, ctx.Gin().Request.URL.String())
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 检测是否重复请求 发生错误 transactionId=", transactionId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("st8 Bet 检测到重复请求 transactionId=", transactionId, " resp=", duplicateResult)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
		}
		return
	}
	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if response.Status != ST8_STATUS_OK {
			respCode = 1
		}
		base.AddRequestDB(transactionId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	user, _, err := base.GetUserById(userId)
	if err != nil {
		response.Status = ST8_STATUS_PLAYER_NOT_FOUND
		ctx.RespJson(response)
		logs.Error("st8 Bet 查询用户失败: err=", err)
		return
	}
	response.Balance = strconv.FormatFloat(user.Amount, 'f', 2, 64)
	response.Currency = reqdata.Currency

	amount, _ := strconv.ParseFloat(reqdata.Amount, 64)
	if amount < 0 {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Bet api 金额必须大于0")
		return
	}

	//三方来源的数据整理
	var (
		betAmount = amount
		thirdId   = reqdata.Round
		gameCode  = reqdata.GameCode
		thirdTime = s.getTime()
	)

	brand := s.brandName + strings.TrimSpace(reqdata.DeveloperCode)
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", brand, gameCode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			response.Status = ST8_STATUS_GAME_DISABLED
			ctx.RespJson(response)
			logs.Error("st8 Bet api 游戏ID获取失败,游戏不存在 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand)
		}
		response.Status = ST8_STATUS_GAME_DISABLED
		ctx.RespJson(response)
		logs.Error("st8 Bet api 游戏ID获取失败 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand, " err=", err)
		return
	}

	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人 6=hub体育
	var goldType int
	var tablePre string
	switch gameType {
	case 1: //电子游戏
		tablePre = "x_third_dianzhi_pre_order"
	case 2: //棋牌游戏
		tablePre = "x_third_qipai_pre_order"
	case 5: //真人游戏
		tablePre = "x_third_live_pre_order"
	case 6:
		tablePre = "x_third_sport_pre_order"
	default:
		response.Status = ST8_STATUS_GAME_DISABLED
		ctx.RespJson(response)
		logs.Error("st8 Bet api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameList.GameType, " reply := ", response)
		return
	}
	//根据交易类型代码获取交易类型说明
	var memoType = s.getTransactionType(reqdata.ProviderKind)
	goldType = s.getSt8GoldChangeType(K_ST8CHANGETYPEBET, brandName)
	if goldType == 0 {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Bet api 获取账变类型错误 thirdId=", thirdId, " brandName=", brand, " tablePre=", tablePre, " reply= ", response)
		return
	}
	// 检查游戏权限 涉及多笔连续借记 如果加入权限判断 可能导致 三方系统异常应该扣玩家的钱 却没被扣掉
	//if allowed, hint, err := base.BeforeEnterGameId(userId, brandName, gameCode); err != nil {
	//	logs.Error("st8 Bet api 权限检查错误 userId=", userId, " gameId=", gameCode, " err=", err.Error())
	//	response.Status = ST8_STATUS_PLAYER_NOT_FOUND
	//	ctx.RespJson(response)
	//	return
	//} else if !allowed {
	//	logs.Error("st8 Bet api 权限被拒绝 userId=", userId, " gameId=", gameCode, " hint=", hint)
	//	response.Status = ST8_STATUS_PLAYER_NOT_FOUND
	//	ctx.RespJson(response)
	//	return
	//}

	//获取投注渠道
	BetChannelId := base.GetUserChannelId(ctx, user)
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 行锁处理并发修改用户余额问题
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("st8 Bet 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			response.Status = ST8_STATUS_PLAYER_NOT_FOUND
			ctx.RespJson(response)
			logs.Error("st8 Bet api 查询用户余额失败 thirdId=", thirdId, " reply := ", response, betAmount, " err=", e)
			return e
		}

		//判断余额是否足够 需要四舍五入判断 返回给st8的余额是四舍五入后的余额
		userAmount := userBalance.Amount
		userAmount = math.Round(userAmount*100) / 100
		if userAmount < betAmount {
			response.Status = ST8_STATUS_NOT_ENOUGH_MONEY
			ctx.RespJson(response)
			logs.Error("st8 Bet api 余额不足 thirdId=", thirdId, " reply := ", response, "amount=", userBalance.Amount, "betAmount=", betAmount)
			return errors.New("余额不足")
		}

		// 查询订单是否存在 订单不存在创建新订单 订单已存在 追加投注金额 要允许一单多投
		betTran := ThirdOrderST8{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, brandName).First(&betTran).Error
		if e != nil {
			if e != daogorm.ErrRecordNotFound {
				logs.Error("st8 Bet api 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " e=", e)
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				return e
			} else { // 没有订单，创建新订单
				betTran = ThirdOrderST8{
					UserId:       userId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: BetChannelId,
					Brand:        brandName,
					ThirdId:      thirdId,
					GameId:       gameCode,
					GameName:     gameName,
					BetAmount:    betAmount,
					WinAmount:    0,
					ValidBet:     0,
					ThirdTime:    thirdTime,
					Currency:     s.Currency,
					RawData:      string(bodyBytes),
					State:        1,
					Fee:          0,
					DataState:    -1, // 未开奖状态
					CreateTime:   thirdTime,
				}
				e = tx.Table(tablePre).Create(&betTran).Error
				if e != nil {
					logs.Error("st8 Bet api 创建订单失败 thirdId=", thirdId, " betTran=", betTran, " error=", e)
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return e
				}
			}
		} else { // 订单已存在
			if betTran.UserId != userId {
				logs.Error("st8 Bet api 订单用户不匹配 thirdId=", thirdId, " betTran=", betTran, " userId=", userId)
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				return errors.New("订单用户不匹配")
			}

			// 检查订单状态
			if betTran.DataState != -1 {
				if betTran.DataState == 1 {
					logs.Error("st8 Bet api 订单已结算 thirdId=", thirdId, " betTran=", betTran, " roundClosed=", *reqdata.RoundClosed)
					//游戏已关闭
					if reqdata.RoundClosed == nil || *reqdata.RoundClosed == true {
						response.Status = ST8_STATUS_UNKNOWN
						ctx.RespJson(response)
						logs.Error("st8 Bet api 订单已结算 游戏已关闭 不允许继续下注 thirdId=", thirdId, " roundClosed=", *reqdata.RoundClosed)
						return errors.New("订单已结算")
					}

				} else if betTran.DataState == -2 {
					// 订单允许多次取消，仅记录日志
					logs.Error("st8 Bet api 订单已取消 thirdId=", thirdId, " betTran=", betTran)

				} else {
					logs.Error("st8 Bet api 订单状态异常 thirdId=", thirdId, " betTran=", betTran)
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return errors.New("订单状态异常")
				}
			}

			// 检查是否撤销过下注
			if betTran.State == -1 {
				logs.Error("st8 Bet api 不能继续投注取消过投注的订单 thirdId=", thirdId, " betTran=", betTran)
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				return errors.New("不能继续投注取消过投注的订单")
			}

			// 更新订单金额
			resultTmp := tx.Table(tablePre).Where("Id=?", betTran.Id).Updates(map[string]interface{}{
				"BetAmount": daogorm.Expr("BetAmount + ?", betAmount),
				"ThirdTime": thirdTime,
				"DataState": -1, // 未开奖状态
				"RawData":   string(bodyBytes),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("st8 Bet api 更新订单失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " betOldAmount=", betTran.BetAmount, " err=", e)
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				return e
			}
		}

		//扣除用户余额 //UserId = ? AND Amount >= ? st8 要求支持负数
		afterAmount := userBalance.Amount - betAmount
		if betAmount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", betAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("st8 Bet api 扣款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " err=", e.Error())
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				return e
			}
		}

		//写入账变记录
		amountLog := AmountChangeLogSt8{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       -betAmount,
			AfterAmount:  afterAmount,
			Reason:       goldType,
			CreateTime:   thirdTime,
			Memo:         brandName + " " + memoType + " ,thirdId:" + thirdId + ",tranId:" + transactionId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
		}

		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8 Bet api 写入账变记录失败 thirdId=", thirdId, " reply := ", response, betAmount, " err=", e)
			return e
		}

		memo := amountLog.Memo
		reason := ST8_AMOUNT_DEBIT
		e = s.AddThirdAmountLog(tx, -betAmount, reason, userId, thirdId, transactionId, memo)
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8  Refund 插入三方账变记录错误 thirdId=", thirdId, " reply= ", response, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			return e
		}

		response.Status = ST8_STATUS_OK
		response.Balance = strconv.FormatFloat(afterAmount, 'f', 2, 64)
		response.Currency = reqdata.Currency
		ctx.RespJson(response)
		logs.Info("st8 Bet api 下注成功 thirdId=", thirdId, " reply := ", response, betAmount)
		return nil
	})
	if err == nil {
		// 推送下注事件通知
		if s.thirdGamePush != nil {
			s.thirdGamePush.PushBetEvent(userId, gameName, brand, betAmount, reqdata.Currency, brand, transactionId, gameType)
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if s.RefreshUserAmountFunc != nil {
				tmpErr := s.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][ST8Service] Bet 发送余额变动通知错误: userId=", notifyUserId, " err=", tmpErr)
				}
			} else {
				logs.Error("[ERROR][ST8Service] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// Credit 派奖
// 操作员应该能够在没有预先借记的情况下处理信用请求。
// 操作员应该能够在同一轮中处理多个信用请求
// 流程1：2 轮平行交易。第一轮贷记在第二轮扣款后被取消，导致余额为负数。操作员应该能够处理这两轮交易
// 1. 余额请求
// 2. 第一轮借记请求
// 3. 第一轮信用请求
// 4. 第二轮借记请求
// 5. 取消第一轮信用请求
// 6. 第一轮新信用请求
// 7. 第二轮信用请求
// 8. 第一轮修正金额
// 9. 第二轮修正金额
// 流程2：多重信用请求
// 1. 余额请求
// 2. 借记请求
// 3. 信用请求
// 4. 同一轮次的另一笔信用请求
// 5. 同一轮次的另一笔信用请求
// 6. 同一轮次的另一笔信用请求
// 流程3：零金额的信用请求（用于损失或回合关闭场景）
// 1. 余额请求
// 2. 借记请求
// 3. 信用请求
// 4. 补偿金请求
func (s *ST8Service) Credit(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Player        string `json:"player"`         // 玩家ID(区分大小写)
		Site          string `json:"site"`           // 运营商站点
		Token         string `json:"token"`          // 游戏会话token,最大长度255字符
		TransactionId string `json:"transaction_id"` // ST8系统中的唯一交易ID
		Round         string `json:"round"`          // ST8系统中的唯一局号ID
		RoundClosed   *bool  `json:"round_closed"`   // 游戏局是否结束
		GameCode      string `json:"game_code"`      // 游戏ID
		DeveloperCode string `json:"developer_code"` // 开发商代码
		Amount        string `json:"amount"`         // 金额,字符串格式的浮点数
		Currency      string `json:"currency"`       // 货币代码,必须符合ISO 4217标准
		ProviderKind  string `json:"provider_kind"`  // 提供商交易类型
		Provider      struct {
			TransactionId string `json:"transaction_id"` // 提供商系统中的交易ID
			Amount        string `json:"amount"`         // 提供商货币中的原始交易金额
			Currency      string `json:"currency"`       // 提供商端的游戏会话货币
			Player        string `json:"player"`         // 提供商系统中的玩家名称
			Round         string `json:"round"`          // 提供商系统中的局号ID
		} `json:"provider"` // 提供商信息
		Bonus *struct {
			InstanceId string `json:"instance_id"` // ST8系统中的奖金唯一ID
			Status     string `json:"status"`      // 奖金状态:processing/active/accepted/rejected/finished/canceled/failed
			BonusId    string `json:"bonus_id"`    // 运营商平台中的奖金唯一ID
		} `json:"bonus"` // 奖金信息
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Win api Body := ", string(bodyBytes))

	// 统一使用ST8Response响应
	response := ST8Response{
		Status: ST8_STATUS_UNKNOWN, // 默认未知状态
	}

	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Win 参数错误: err=", err)
		return
	}

	var reqdata RequestData
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		logs.Error("st8 Bet JSON解析失败: err=", err)
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	transactionId := reqdata.TransactionId //三方请求唯一流水号
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, transactionId, ctx.Gin().Request.URL.String())
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 检测是否重复请求 发生错误 transactionId=", transactionId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("st8 Win 检测到重复请求 transactionId=", transactionId, " resp=", duplicateResult)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
		}
		return
	}
	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if response.Status != ST8_STATUS_OK {
			respCode = 1
		}
		base.AddRequestDB(transactionId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()
	amount, _ := strconv.ParseFloat(reqdata.Amount, 64)
	if amount < 0 {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Win api 金额不能小于0")
		return
	}

	//三方来源的数据整理
	var (
		thirdId   = reqdata.Round
		gameCode  = reqdata.GameCode
		thirdTime = s.getTime()
	)

	brand := s.brandName + strings.TrimSpace(reqdata.DeveloperCode)
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", brand, gameCode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			response.Status = ST8_STATUS_GAME_DISABLED
			ctx.RespJson(response)
			logs.Error("st8 Win api 游戏ID获取失败,游戏不存在 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand)
		}
		response.Status = ST8_STATUS_GAME_DISABLED
		ctx.RespJson(response)
		logs.Error("st8 Bet Win 游戏ID获取失败 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand, " err=", err)
		return
	}
	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType

	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人 6=hub体育
	var goldType int
	var tablePre, table string
	switch gameType {
	case 1:
		tablePre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	case 2:
		tablePre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
	case 5:
		tablePre = "x_third_live_pre_order"
		table = "x_third_live"
	case 6:
		tablePre = "x_third_sport_pre_order"
		table = "x_third_sport"
	default:
		response.Status = ST8_STATUS_GAME_DISABLED
		logs.Error("st8 Win api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", response)
		ctx.RespJson(response)
		return
	}

	goldType = s.getSt8GoldChangeType(K_ST8CHANGETYPESETTLE, brandName)
	if goldType == 0 {
		response.Status = ST8_STATUS_UNKNOWN
		logs.Error("st8 Win api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablePre=", tablePre, " reply= ", response)
		ctx.RespJson(response)
		return
	}
	//根据交易类型代码获取交易类型说明
	var memoType = s.getTransactionType(reqdata.ProviderKind)
	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 先判断会员在判断订单
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("st8 Win api 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			response.Status = ST8_STATUS_PLAYER_NOT_FOUND
			ctx.RespJson(response)
			logs.Error("st8 Win api 查询用户余额失败 thirdId=", thirdId, " reply := ", response, amount, " err=", e)
			return e
		}

		betTran := ThirdOrderST8{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			// 允许没有下注直接派奖这种情况
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				// 如果订单不存在，创建新订单
				logs.Info("st8 Credit api 订单不存在，创建新订单 thirdId=", thirdId)
				betTran = ThirdOrderST8{
					UserId:       userId,
					Brand:        brandName,
					ThirdId:      thirdId,
					GameId:       gameCode,
					GameName:     gameName,
					BetAmount:    0, // 没有投注金额
					WinAmount:    amount,
					ValidBet:     0, // 没有有效投注
					ThirdTime:    thirdTime,
					Currency:     reqdata.Currency,
					RawData:      string(bodyBytes),
					State:        1, // 直接设为已结算
					DataState:    1,
					CreateTime:   thirdTime,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					BetChannelId: userBalance.ChannelId,
				}

				// 创建订单记录
				e = tx.Table(tablePre).Create(&betTran).Error
				if e != nil {
					logs.Error("st8 Credit api 创建新订单失败: err=", e.Error())
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return e
				}

				// 同时创建到正式表
				betTran.Id = 0 // 重置ID以便在正式表生成新ID
				e = tx.Table(table).Create(&betTran).Error
				if e != nil {
					logs.Error("st8 Credit api 创建正式表订单失败: err=", e.Error())
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return e
				}

				// 直接更新用户余额和记录账变
				afterAmount := userBalance.Amount + amount
				if amount != 0 {
					resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
						"amount": daogorm.Expr("amount+?", amount),
					})
					if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
						logs.Error("st8 Credit api 更新用户余额失败: err=", resultTmp.Error)
						response.Status = ST8_STATUS_UNKNOWN
						ctx.RespJson(response)
						return errors.New("更新用户余额失败")
					}
				}
				// 记录账变
				amountLog := AmountChangeLogSt8{
					UserId:       userId,
					BeforeAmount: userBalance.Amount,
					Amount:       amount,
					AfterAmount:  afterAmount,
					Reason:       goldType,
					Memo:         brandName + " " + memoType + " ,thirdId:" + thirdId + ",tranId:" + transactionId,
					SellerId:     userBalance.SellerId,
					ChannelId:    userBalance.ChannelId,
					CreateTime:   thirdTime,
				}
				e = tx.Table("x_amount_change_log").Create(&amountLog).Error
				if e != nil {
					logs.Error("st8 Credit api 插入账变记录失败: err=", e.Error())
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return e
				}

				// 记录三方账变
				memo := amountLog.Memo + " 没有下注直接派奖"
				e = s.AddThirdAmountLog(tx, amount, ST8_AMOUNT_CREDIT, userId, thirdId, transactionId, memo)
				if e != nil {
					logs.Error("st8 Credit api 插入三方账变记录失败: err=", e.Error())
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					return e
				}

				response.Status = ST8_STATUS_OK
				response.Balance = strconv.FormatFloat(afterAmount, 'f', 2, 64)
				response.Currency = reqdata.Currency
				ctx.RespJson(response)
				logs.Info("st8 Credit api 直接派奖成功: userId=%d, thirdId=%s, amount=%.2f", userId, thirdId, amount)
				return nil
			}
			logs.Error("st8 Credit api 查询订单失败: err=", e.Error())
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			return e
		}
		dataState := betTran.DataState

		//订单即使被撤单仍然可以在次派奖
		if dataState == -2 { // 订单已被撤销 记录日志
			logs.Error("st8 Win api 订单已被撤销 id=", thirdId, " reply=", response, " betTran=", betTran)
		}

		logs.Info("st8 Win api 订单数据 thirdId=", thirdId, " 原订单状态=", betTran.DataState, " betTran=", betTran, " roundClosed=", *reqdata.RoundClosed)
		betAmount := betTran.BetAmount
		if dataState == -1 || dataState == -2 { // 还没有被移至正式表的处理方式
			//将下注订单移动至结算订单表
			betId := betTran.Id
			validBet := betAmount

			if betId == 0 {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 订单Id是0 id=", thirdId, " reply=", response, " betTran=", betTran)
				return errors.New("订单未找到")
			}

			winAmount := amount

			// 先检查正式表中是否已存在该订单
			var existingOrder ThirdOrderST8
			checkErr := tx.Table(table).Where("ThirdId = ? AND Brand = ?", thirdId, brandName).First(&existingOrder).Error

			var totalWinAmount float64 // 总派奖金额

			if checkErr == nil {
				// 正式表中已存在订单，说明之前已经派奖过，执行累加操作
				totalWinAmount = existingOrder.WinAmount + winAmount // 计算总派奖金额
				logs.Info("st8 Win api 正式表已存在订单，执行累加派奖 thirdId=", thirdId, " 原WinAmount=", existingOrder.WinAmount, " 新增WinAmount=", winAmount, " 总WinAmount=", totalWinAmount)

				// 使用总派奖金额计算有效投注
				if gameType != 6 {
					validBet = math.Abs(totalWinAmount - betAmount)
					if validBet > math.Abs(betAmount) {
						validBet = math.Abs(betAmount)
					}
				}

				// 更新临时表（累加）
				resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
					"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
					"ValidBet":  validBet,
					"RawData":   string(bodyBytes),
					"GameId":    gameCode,
					"GameName":  gameName,
					"ThirdTime": thirdTime,
					"DataState": 1,
				})
				e = resultTmp.Error
				if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
					e = errors.New("更新临时表条数0")
				}
				if e != nil {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Win api 累加修改临时表订单错误 id=", thirdId, " reply=", response, " betTran=", betTran, " e=", e)
					return e
				}

				// 更新正式表（累加）
				updateResult := tx.Table(table).Where("ThirdId = ? AND Brand = ?", thirdId, brandName).Updates(map[string]interface{}{
					"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
					"BetAmount": betAmount,
					"ValidBet":  validBet,
					"RawData":   string(bodyBytes),
					"GameId":    gameCode,
					"GameName":  gameName,
					"ThirdTime": thirdTime,
					"DataState": 1,
					"State":     1,
				})
				e = updateResult.Error
				if e == nil && updateResult.RowsAffected <= 0 {
					e = errors.New("累加更新正式表记录失败：无行受影响")
				}

				if e != nil {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Win api 累加更新正式表记录错误 id=", thirdId, " reply=", response, " table=", table, " e=", e)
					return e
				}

				logs.Info("st8 Win api 累加更新正式表记录成功 thirdId=", thirdId, " table=", table, " 累加金额=", winAmount, " 总派奖=", totalWinAmount, " validBet=", validBet)

			} else if errors.Is(checkErr, daogorm.ErrRecordNotFound) {
				// 正式表中不存在订单，说明是首次派奖，执行覆盖操作
				totalWinAmount = winAmount // 首次派奖，总金额就是当前金额
				logs.Info("st8 Win api 正式表不存在订单，执行首次派奖 thirdId=", thirdId, " WinAmount=", winAmount)

				// 使用当前派奖金额计算有效投注
				if gameType != 6 {
					validBet = math.Abs(totalWinAmount - betAmount)
					if validBet > math.Abs(betAmount) {
						validBet = math.Abs(betAmount)
					}
				}

				betTran.WinAmount = totalWinAmount
				betTran.ValidBet = validBet
				betTran.RawData = string(bodyBytes)
				betTran.GameId = gameCode
				betTran.GameName = gameName
				betTran.ThirdTime = thirdTime
				betTran.DataState = 1

				// 更新临时表（覆盖）
				resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
					"WinAmount": totalWinAmount,
					"ValidBet":  validBet,
					"RawData":   string(bodyBytes),
					"GameId":    gameCode,
					"GameName":  gameName,
					"ThirdTime": thirdTime,
					"DataState": 1,
				})
				e = resultTmp.Error
				if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
					e = errors.New("更新临时表条数0")
				}
				if e != nil {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Win api 修改临时表订单错误 id=", thirdId, " reply=", response, " betTran=", betTran, " e=", e)
					return e
				}

				//移动至统计表（插入新记录）
				betTran.Id = 0
				betTran.State = 1
				betTran.DataState = 1
				betTran.RawData = string(bodyBytes)
				betTran.CreateTime = thirdTime
				betTran.WinAmount = totalWinAmount
				betTran.ValidBet = validBet
				e = tx.Table(table).Create(&betTran).Error
				if e != nil {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Win api 插入正式表记录错误 id=", thirdId, " reply=", response, " betTran=", betTran, " table=", table, " e=", e)
					return e
				}

				logs.Info("st8 Win api 插入正式表记录成功 thirdId=", thirdId, " table=", table, " validBet=", validBet)

			} else {
				// 查询过程中出现其他错误
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 检查正式表记录时出错 id=", thirdId, " reply=", response, " table=", table, " e=", checkErr)
				return checkErr
			}

		} else if dataState == 1 { // 已经派过奖的处理方式
			// debit>cancel>debit>credit
			// || dataState == -2
			// 已经被移动至正式表的处理方式(一单多赢情况)
			// 订单被撤销仍然可以credit
			betId := betTran.Id
			winAmount := amount
			logs.Info("st8 Win api 已经派过奖 重新结算 id=", thirdId, " req=", response, " betTran=", betTran)

			if betId == 0 {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 订单Id是0 id=", thirdId, " reply=", response, " betTran=", betTran)
				return errors.New("订单未找到")
			}

			// 更新临时表
			resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
				"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
				"RawData":   string(bodyBytes),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 多赢修改结算金额错误 id=", thirdId, " reply=", response, " betTran=", betTran, " e=", e)
				return e
			}

			// 更新正式表
			resultTmp = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Updates(map[string]interface{}{
				"WinAmount": daogorm.Expr("WinAmount+?", winAmount),
				"BetAmount": betAmount,
				"RawData":   string(bodyBytes),
				"ThirdTime": thirdTime,
			})
			e = resultTmp.Error
			if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 多赢修改结算金额错误 id=", thirdId, " reply=", response, " betTran=", betTran, " table=", table, " e=", e)
				return e
			}
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8 Win api 订单状态不是-1或1 id=", thirdId, " reply=", response, " betTran=", betTran)
			return errors.New("订单状态不正确")
		}

		// 更新用户余额
		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Win api 修改用户余额错误 id=", thirdId, " reply=", response, " betTran=", betTran, " table=", table, " e=", e)
				return errors.New("修改x_user失败了")
			}
		}

		// 插入账变记录
		afterBalance := userBalance.Amount + amount
		amountLog := AmountChangeLogSt8{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       amount,
			AfterAmount:  afterBalance,
			Reason:       goldType,
			Memo:         brandName + " " + memoType + " ,thirdId:" + thirdId + ",tranId:" + transactionId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Omit("Id").Create(&amountLog).Error
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8 Win api 插入账变记录错误 id=", thirdId, " reply=", response, " betTran=", betTran, " table=", table, " amountLog=", amountLog, " e=", e)
			return e
		}

		memo := amountLog.Memo
		reason := ST8_AMOUNT_CREDIT
		e = s.AddThirdAmountLog(tx, amount, reason, userId, thirdId, transactionId, memo)
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8  Win  api 插入三方账变记录错误 thirdId=", thirdId, " reply= ", response, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			return e
		}

		response.Status = ST8_STATUS_OK
		response.Balance = strconv.FormatFloat(afterBalance, 'f', 2, 64)
		response.Currency = reqdata.Currency
		ctx.RespJson(response)
		logs.Info("st8 Win api 派奖成功 thirdId=", thirdId, " reply := ", response, amount)
		return nil
	})

	if err == nil {
		// 推送派奖事件通知
		if s.thirdGamePush != nil {
			//s.thirdGamePush.PushRewardEvent(userId, gameName, brandName, betTran.BetAmount, amount, s.Currency)
			s.thirdGamePush.PushRewardEvent(gameType, brandName, thirdId)
		}

		go func() {
			//异步更新开奖结果
			time.Sleep(time.Second * 2)
			dUrl, err := s.getRoundDetail(reqdata.Player, reqdata.TransactionId, reqdata.Round, reqdata.Site)
			if err != nil {
				logs.Error("st8 Win 获取游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
				return
			}

			err = server.Db().GormDao().Table(tablePre).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, brandName, userId).Updates(map[string]interface{}{
				//"BetCtx":     dUrl,
				"GameRst":    dUrl,
				"BetCtxType": 2,
			}).Error
			if err != nil {
				logs.Error("st8 Win  更新预设表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
			}
			err = server.Db().GormDao().Table(table).Where("ThirdId = ? and Brand=? and UserId=?", thirdId, brandName, userId).Updates(map[string]interface{}{
				//"BetCtx":     dUrl,
				"GameRst":    dUrl,
				"BetCtxType": 2,
			}).Error
			if err != nil {
				logs.Error("st8 Win 更新正式表游戏详情链接失败 thirdId=", thirdId, " err=", err.Error())
			}
		}()
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if s.RefreshUserAmountFunc != nil {
			tmpErr := s.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][ST8Service] Win 发送余额变动通知错误: userId=", notifyUserId, " err=", tmpErr)
			}
		} else {
			logs.Error("[ERROR][ST8Service] Win 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// Cancel 取消交易
// 操作员应该能够在同一轮中处理多个取消请求。
// 流程1：多重取消场景： 测试结果展示了处理多个取消请求的完整流程：
// 1. 余额请求
// 2. 借记请求
// 3. 同一轮次的另一个借记请求
// 4. 取消请求
// 5. 取消第二笔扣款请求
// 流程2：处理多重取消的完整流程。
// 1. 余额请求
// 2. 借记请求
// 3. 取消请求
// 4. 同一轮次的另一个借记请求
// 5. 取消第二笔扣款请求
func (s *ST8Service) Cancel(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		CancelId      string `json:"cancel_id"`      // 取消操作的唯一ID
		TransactionId string `json:"transaction_id"` // 需要取消的交易ID,可以是借记或贷记交易
		Round         string `json:"round"`          // ST8系统中的唯一局号ID,用于关联借贷操作 可能为空
		Player        string `json:"player"`         // 运营商平台中的唯一玩家ID(区分大小写)
		Token         string `json:"token"`          // 游戏会话令牌,最大长度255字符
		Amount        string `json:"amount"`         // 交易金额,以字符串格式表示的浮点数
		Currency      string `json:"currency"`       // 玩家当前钱包中的货币代码,必须符合ISO 4217标准
		Site          string `json:"site"`           // 运营商站点标识
		DeveloperCode string `json:"developer_code"` // 游戏开发商的唯一标识符
		GameCode      string `json:"game_code"`      // ST8系统中的唯一游戏标识符,可能为空
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("st8 Refund api Body=", string(bodyBytes))

	// 统一使用ST8Response响应
	response := ST8Response{
		Status: ST8_STATUS_UNKNOWN, // 默认未知状态
	}

	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Refund 参数错误: err=", err)
		return
	}

	var reqdata RequestData
	if err := json.Unmarshal(bodyBytes, &reqdata); err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		logs.Error("st8 Bet JSON解析失败: err=", err)
		ctx.RespJson(response)
		return
	}

	//验证签名
	signature := ctx.Gin().GetHeader("X-St8-Sign")
	if signature == "" {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
	}
	// 获取区域配置
	publicKey := s.getPublicKeyBySite(reqdata.Site)
	valid, err := s.verifyECDSASignature(bodyBytes, signature, publicKey)
	if err != nil {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过: err=", err)
		return
	}
	if !valid {
		response.Status = ST8_STATUS_AUTH_FAILED
		ctx.RespJson(response)
		logs.Error("st8 签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	transactionId := reqdata.CancelId //取消请求使用这个唯一ID
	duplicate, duplicateResult, err := base.CheckDuplicateDB(s.brandName, transactionId, ctx.Gin().Request.URL.String())
	if err != nil {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 检测是否重复请求 发生错误 transactionId=", transactionId, " err=", err)
		return
	}
	if duplicate {
		logs.Error("st8 Cancel 检测到重复请求 transactionId=", transactionId, " resp=", duplicateResult)
		if duplicateResult != nil && err == nil {
			ctx.RespJson(duplicateResult)
		} else {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
		}
		return
	}
	//记录请求号用于判断是否重复请求
	defer func() {
		respCode := 0
		if response.Status != ST8_STATUS_OK {
			respCode = 1
		}
		base.AddRequestDB(transactionId, string(bodyBytes), response, respCode, s.brandName, ctx.Gin().Request.URL.String())
	}()

	amount, _ := strconv.ParseFloat(reqdata.Amount, 64)
	if amount < 0 {
		response.Status = ST8_STATUS_UNKNOWN
		ctx.RespJson(response)
		logs.Error("st8 Refund api 金额不能小于0")
		return
	}

	//brandName := "HS8_" + strings.ReplaceAll(reqdata.DeveloperCode, " ", "_")
	//brandName = strings.ReplaceAll(brandName, "'", "")
	//brandName := s.getGameDeveloperName(reqdata.DeveloperCode)

	//Cancel debit = add funds to account 取消投注
	//Cancel credit = reduce funds to account 取消派奖
	// 取消 debit =给玩家增加余额 取消 credit=给玩家减少余额
	// 查询账变记录，确定要取消的交易
	thirdAmountLog := thirdGameModel.ThirdAmountLog{}
	e := server.Db().GormDao().Table("x_third_amount_log").
		Select("*").
		Where("UserId = ? and TransactionId = ?", userId, reqdata.TransactionId).
		First(&thirdAmountLog).Error
	if e != nil {
		logs.Error("st8 Refund 查询账变失败 userId=", userId, "transactionId=", reqdata.TransactionId, " err=", e.Error())
		if errors.Is(e, daogorm.ErrRecordNotFound) {
			response.Status = ST8_STATUS_TRANS_NOT_FOUND
		} else {
			response.Status = ST8_STATUS_UNKNOWN
		}
		ctx.RespJson(response)
		return
	}

	thirdId := thirdAmountLog.ThirdId
	// 根据账变类型判断是加款还是减款操作
	reason := thirdAmountLog.Reason
	amount, _ = strconv.ParseFloat(reqdata.Amount, 64)
	if reason == ST8_AMOUNT_DEBIT { //负数减款 正数加款
		amount = math.Abs(amount) // 取消投注，返还投注金额
	} else if reason == ST8_AMOUNT_CREDIT { //负数减款 正数加款
		amount = -math.Abs(amount) // 取消派奖，收回派奖金额
	} else {
		logs.Error("st8 Refund 获取账变类型错误 userId=", userId, "transactionId=", reqdata.TransactionId)
		response.Status = ST8_STATUS_TRANS_NOT_FOUND
		ctx.RespJson(response)
		return
	}

	brandName := s.brandName + reqdata.DeveloperCode
	//查询订单
	order := s.getOrder(thirdId, brandName, "x_third_dianzhi_pre_order")
	gameType := 1
	if order == nil {
		order = s.getOrder(thirdId, brandName, "x_third_qipai_pre_order")
		gameType = 2
		if order == nil {
			order = s.getOrder(thirdId, brandName, "x_third_live_pre_order")
			gameType = 5
			if order == nil {
				order = s.getOrder(thirdId, brandName, "x_third_sport_pre_order")
				gameType = 6
				if order == nil {
					response.Status = ST8_STATUS_TRANS_NOT_FOUND
					ctx.RespJson(response)
					logs.Error("st8  Refund 订单不存在，thirdId=", thirdId)
				}
				return
			}
		}
	}

	//取消交易游戏ID可能为空，直接取DeveloperCode字段
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人
	var goldType int
	var tablePre, table string
	switch gameType {
	case 1:
		tablePre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	case 2:
		tablePre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
	case 5:
		tablePre = "x_third_live_pre_order"
		table = "x_third_live"
	case 6:
		tablePre = "x_third_sport_pre_order"
		table = "x_third_sport"

	default:
		response.Status = ST8_STATUS_GAME_DISABLED
		logs.Error("st8 Refund api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", response)
		ctx.RespJson(response)
		return
	}

	goldType = s.getSt8GoldChangeType(K_ST8CHANGETYPEROLLBACK, brandName)
	if goldType == 0 {
		response.Status = ST8_STATUS_UNKNOWN
		logs.Error("st8 Refund api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablePre=", tablePre, " reply= ", response)
		ctx.RespJson(response)
		return
	}

	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 先判断会员在判断订单
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("st8 Refund api 失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			response.Status = ST8_STATUS_PLAYER_NOT_FOUND
			ctx.RespJson(response)
			logs.Error("st8 Refund api 查询用户余额失败 thirdId=", thirdId, " reply := ", response, amount, " err=", e)
			return e
		}

		betTran := ThirdOrderST8{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("st8 Refund api 订单不存在 thirdId=", thirdId, " brandName=", brandName, " userId=", userId)
				response.Status = ST8_STATUS_TRANS_NOT_FOUND
				ctx.RespJson(response)
				return e
			}
			logs.Error("st8 Refund api 查询订单失败 thirdId=", thirdId, " brandName=", brandName, " userId=", userId, " err=", e.Error())
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			return e
		}

		balance := userBalance.Amount
		//if betTran.DataState != -1 {
		//	if betTran.DataState == 1 { //已结算的订单仍然可以被取消
		//		logs.Error("st8  Refund 订单已结算 thirdId=", thirdId, " reply= ", response, " tablePre=", tablePre, " betTran=", betTran)
		//		//response.Status = ST8_STATUS_OK
		//		//response.Balance = strconv.FormatFloat(balance, 'f', 2, 64)
		//		//ctx.RespJson(response)
		//		//e = errors.New("订单已经结算")
		//		//return e
		//	} else if betTran.DataState == -2 { // 订单是撤销状态不能在次撤销
		//		logs.Error("st8  Refund 订单已撤销 thirdId=", thirdId, " reply= ", response, " tablePre=", tablePre, " betTran=", betTran)
		//		response.Status = ST8_STATUS_OK
		//		response.Balance = strconv.FormatFloat(balance, 'f', 2, 64)
		//		ctx.RespJson(response)
		//		e = errors.New("订单已经撤销")
		//		return e
		//	} else {
		//		logs.Error("st8  Refund 订单状态不对 thirdId=", thirdId, " reply= ", response, " tablePre=", tablePre, " betTran=", betTran)
		//		response.Status = ST8_STATUS_UNKNOWN
		//		ctx.RespJson(response)
		//		return errors.New("订单状态不对")
		//	}
		//}
		//
		//betId := betTran.Id
		//resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
		//	"RawData":   string(bodyBytes),
		//	"DataState": -2,
		//})
		//e = resultTmp.Error
		//if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
		//	e = errors.New("更新条数0")
		//}
		//if e != nil {
		//	response.Status = ST8_STATUS_UNKNOWN
		//	ctx.RespJson(response)
		//	logs.Error("st8  Refund 修改订单错误 thirdId=", thirdId, " reply= ", response, " tablePre=", tablePre, " betTran=", betTran, " e=", e)
		//	return errors.New("修改订单失败")
		//}

		if betTran.DataState == 1 {
			//回滚已结算订单
			logs.Error("st8 Refund 异常回滚已结算订单 thirdId=", thirdId)
		}

		//订单状态处理逻辑：
		//-1: 未结算订单 - 退还投注金额
		//1: 已结算订单 - 回滚赢钱金额
		//-2: 已取消订单 - 直接返回成功
		//其他状态 - 返回错误
		//已结算订单的处理：
		//清空赢钱金额、有效投注和游戏结果
		//更新临时表和正式表的状态
		//取消赢钱金额
		//未结算订单的处理：
		//直接更新为取消状态
		//退还投注金额

		betId := betTran.Id
		if betTran.DataState != -1 {
			dataState := betTran.DataState

			if dataState == 1 { // 已结算订单的处理
				// 需要回滚已结算金额
				//amount = -amount        // 将赢钱金额取反

				rollbackDataState := -2 // 设置为已取消状态
				// 更新临时表订单状态
				resultTmp := tx.Table(tablePre).
					Where("Id=?", betId).
					Updates(map[string]interface{}{
						"RawData":   string(bodyBytes),
						"DataState": rollbackDataState,
						"WinAmount": 0,  // 清空赢钱金额
						"ValidBet":  0,  // 清空有效投注
						"GameRst":   "", // 清空游戏结果
					})
				if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Refund 修改已结算订单状态失败 thirdId=", thirdId, " err=", resultTmp.Error)
					return errors.New("修改已结算订单状态失败")
				}

				// 更新正式表订单状态
				resultTmp = tx.Table(table).
					Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).
					Updates(map[string]interface{}{
						"RawData":   string(bodyBytes),
						"DataState": -2,
						//"ThirdId":   daogorm.Expr("CONCAT(ThirdId, ?)", betId),
					})
				if resultTmp.Error != nil || resultTmp.RowsAffected <= 0 {
					response.Status = ST8_STATUS_UNKNOWN
					ctx.RespJson(response)
					logs.Error("st8 Refund 更新正式表订单状态失败 thirdId=", thirdId, " err=", resultTmp.Error)
					return errors.New("更新正式表订单状态失败")
				}

			} else if dataState == -2 { // 已取消订单的处理 //操作员应该能够在同一轮中处理多个取消请求
				// 已经取消的订单在次取消时 不需要更新订单状态 但是需要更新 玩家余额 和账变
				logs.Error("st8 Refund 订单已取消 thirdId=", thirdId)
				//response.Status = ST8_STATUS_OK
				//response.Balance = strconv.FormatFloat(balance, 'f', 2, 64)
				//response.Currency = reqdata.Currency
				//ctx.RespJson(response)
				//return errors.New("订单已取消")
			}
		} else { // 未结算订单的处理
			// 更新为取消状态
			resultTmp := tx.Table(tablePre).
				Where("Id=? AND ThirdId=? AND UserId=?",
					betId, thirdId, userId).
				Updates(map[string]interface{}{
					"RawData":   string(bodyBytes),
					"DataState": -2,
				})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8 Refund 修改未结算订单状态失败 thirdId=", thirdId, " err=", resultTmp.Error)
				return errors.New("修改未结算订单状态失败")
			}
		}

		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				response.Status = ST8_STATUS_UNKNOWN
				ctx.RespJson(response)
				logs.Error("st8  Refund 修改用户余额失败 thirdId=", thirdId, " err=", e)
				return errors.New("修改用户余额失败")
			}
		}

		afterAmount := balance + amount
		amountLog := AmountChangeLogSt8{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       amount,
			AfterAmount:  afterAmount,
			Reason:       goldType,
			Memo:         brandName + " 取消交易,thirdId:" + thirdId + ",tranId:" + transactionId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   time.Now().In(tzUTC8St8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Omit("Id").Create(&amountLog).Error
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8  Refund 插入账变记录错误 thirdId=", thirdId, " reply= ", response, " table=", table, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			return e
		}
		memo := amountLog.Memo
		reason = ST8_AMOUNT_CANCEL
		e = s.AddThirdAmountLog(tx, amount, reason, userId, thirdId, transactionId, memo)
		if e != nil {
			response.Status = ST8_STATUS_UNKNOWN
			ctx.RespJson(response)
			logs.Error("st8  Refund 插入三方账变记录错误 thirdId=", thirdId, " reply= ", response, " table=", table, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			return e
		}

		response.Status = ST8_STATUS_OK
		response.Balance = strconv.FormatFloat(afterAmount, 'f', 2, 64)
		response.Currency = reqdata.Currency
		ctx.RespJson(response)
		logs.Info("st8 Refund 订单取消成功 thirdId=", thirdId, " reply= ", response, " table=", table, " betTran=", betTran)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if s.RefreshUserAmountFunc != nil {
			tmpErr := s.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知错误: userId=", notifyUserId, " err=", tmpErr)
			}
		} else {
			logs.Error("[ERROR][ST8Service] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

/**
 * 记录st8账变
 */
func (s *ST8Service) AddThirdAmountLog(tx *daogorm.DB, changeAmount float64, reason int, userId int, thirdId string, transactionId string, memo string) error {
	amountLog := thirdGameModel.ThirdAmountLog{
		UserId:        userId,
		Amount:        changeAmount,
		ThirdId:       thirdId,
		TransactionId: transactionId,
		Reason:        reason,
		Memo:          memo,
		Brand:         s.brandName,
		CreateTime:    time.Now().In(tzUTC8St8).Format("2006-01-02 15:04:05"),
	}
	e := tx.Table("x_third_amount_log").Create(&amountLog).Error
	if e != nil {
		return e
	}
	return nil
}

// getOrder 获取订单信息
func (s *ST8Service) getOrder(thirdId, brand, table string) *map[string]interface{} {
	//brand := s.brandName //聚合三方厂家以HS8_开头
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	//where.Add("and", "Brand", "like", fmt.Sprintf("%s%%", brand), "")
	where.Add("and", "Brand", "=", brand, nil)
	betTran, _ := server.Db().Table(table).Where(where).GetOne()
	if betTran == nil {
		return nil
	} else {
		return betTran
	}
}

// getTime 获取时间
func (s *ST8Service) getTime() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func (s *ST8Service) userId2token(userId int) string {
	token := uuid.NewString()
	if err := server.Redis().SetStringEx(s.OperatorCode+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("ST8Service userId2token set redis error: err=", err)
	}
	return token
}

func (s *ST8Service) token2UserId(token string) int {
	redisdata := server.Redis().Get(s.OperatorCode + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

var st8AccessProductList = []string{
	"Funky Games",     // 第一批上 Funky Games 和 3Oaks前端没加
	"3Oaks",           // 第一批上
	"Big Time Gaming", // 第一批上
	"CQ9",             // 第一批上
	"PlayStar",        // 第一批上
	"JDB",             // 第一批上
	"Hacksaw",         // 第一批上
	"Play'n Go",       // 第一批上
	"Fa Chai",         // 第一批上
	"Booongo",
	"Hacksaw Gaming",
	"Pragmatic Play",
}

// GetGameList 从ST8获取游戏列表并存入数据库
func (s *ST8Service) GetGameList(ctx *abugo.AbuHttpContent) {
	type Game struct {
		Code                  string   `json:"code"`                   // 游戏代码
		Name                  string   `json:"name"`                   // 游戏名称
		Enabled               bool     `json:"enabled"`                // 是否启用
		Developer             string   `json:"developer"`              // 开发商
		BonusTypes            []string `json:"bonus_types"`            // 支持的奖金类型
		Category              string   `json:"category"`               // 游戏分类
		Themes                []string `json:"themes"`                 // 主题列表
		Features              []string `json:"features"`               // 特性列表
		RTP                   string   `json:"rtp"`                    // 返还率
		Volatility            int      `json:"volatility"`             // 波动性
		MaxPayoutCoeff        string   `json:"max_payout_coeff"`       // 最大支付系数
		HitRatio              string   `json:"hit_ratio"`              // 命中率
		FunMode               bool     `json:"fun_mode"`               // 游戏模式，false表示正式模式，true表示试玩模式
		ReleaseDate           string   `json:"release_date"`           // 发布日期
		DeprecationDate       string   `json:"deprecation_date"`       // 弃用日期
		RestrictedTerritories []string `json:"restricted_territories"` // 限制地区
		ProhibitedTerritories []string `json:"prohibited_territories"` // 禁止地区
	}

	type Developer struct {
		Name                  string   `json:"name"`                   // 开发商名称
		Code                  string   `json:"code"`                   // 开发商代码
		RestrictedTerritories []string `json:"restricted_territories"` // 限制地区
		ProhibitedTerritories []string `json:"prohibited_territories"` // 禁止地区
	}

	type Category struct {
		Name string `json:"name"` // 分类名称
		Type string `json:"type"` // 分类类型
	}

	type GameResponse struct {
		Status     string      `json:"status"`     // 状态: ok/error
		Games      []Game      `json:"games"`      // 游戏列表
		Developers []Developer `json:"developers"` // 开发商列表
		Categories []Category  `json:"categories"` // 游戏分类列表
	}
	type RequestData struct {
		Line string `json:"line"` // EU 或者 ASIA
		Add  string `json:"add"`  // 1 添加数据库 非1不添加数据库
	}
	theReq := RequestData{}
	err := ctx.RequestData(&theReq)
	if err != nil {
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 构建请求URL
	apiUrl := fmt.Sprintf("%s/api/operator/v1/games", s.BaseHost)

	// 添加查询参数
	queryParams := url.Values{}
	queryParams.Add("site", s.SiteID)

	// 构建完整URL
	fullUrl := fmt.Sprintf("%s?%s", apiUrl, queryParams.Encode())

	// 创建请求
	req, err := http.NewRequest("GET", fullUrl, nil)
	if err != nil {
		logs.Error("创建请求失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 生成ECDSA签名
	signature, err := s.generateECDSASignature([]byte(fmt.Sprintf("%v", queryParams.Encode())), s.PrivateKey)
	if err != nil {
		logs.Error("st8 生成签名失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 添加签名到请求头
	req.Header.Set("x-st8-sign", signature)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("发送请求失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取响应失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	logs.Info("st8 GetGameList 返回数据", string(body))

	// 解析响应
	var gameResp GameResponse
	if err := json.Unmarshal(body, &gameResp); err != nil {
		logs.Error("解析响应失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 检查响应状态
	if gameResp.Status != "ok" {
		logs.Error("API返回错误状态: %s", gameResp.Status)
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	// 输出Developers信息
	//log.Printf("Developers列表:")
	//for _, dev := range gameResp.Developers {
	//	//logs.Info("  开发商: %s 代码: %s", dev.Name, dev.Code)
	//	if len(dev.ProhibitedTerritories) > 0 {
	//		//log.Printf("    禁止地区: %v", dev.ProhibitedTerritories)
	//	}
	//	if len(dev.RestrictedTerritories) > 0 {
	//		//log.Printf("    限制地区: %v", dev.RestrictedTerritories)
	//	}
	//}

	// 更新游戏列表
	for _, game := range gameResp.Games {

		//判断是否是启用的游戏
		//if !s.in_array_string(st8AccessProductList, game.Developer) {
		//	continue
		//}

		logs.Info("  开发商: %s 代码: %s", game.Developer, game.Code)
		code := s.getProviderCode(strings.TrimSpace(game.Developer)) // 返回 "evo"
		if code == "" {
			continue
		}
		Brand := s.brandName + code
		GameType := 0

		dianzi := []string{
			"Video Slots",
			"Scratch Card",
			"Unknown",
			"Arcade Games",
			"Jackpot Slots",
			"Casual Games",
			"Fishing Games",
			"Hi Lo",
			"Crash",
			"Crash Games",
		}

		qipai := []string{
			"Table Games",
			"Blackjack",
			"Roulette",
			"Poker",
			"Sic Bo",
			"Baccarat",
			"Video Poker",
			"Other Table Games",
			"Dice",
			"Virtual Sports",
			"Scratch Cards",
			"Craps",
			"Money Wheel",
			"Bet On Poker",
			"Top Card",
			"Dragon Tiger",
		}

		//趣味游戏归类到电子
		//quwei := []string{
		//	"Hi Lo",
		//	"Crash",
		//	"Crash Games",
		//}

		lottery := []string{
			"Bingo",
			"Keno",
			"Lottery",
			"Live Lottery",
		}

		zhenren := []string{
			"Live Baccarat",
			"Live Blackjack",
			"Live Roulette",
			"Live Games",
			"Live Dealer",
			"Live Dice",
			"Other Live Games",
			"Live Poker",
			"Live Dragon Tiger",
			"Live Game Show",
			"Live Sic Bo",
			"Game Show",
			"Live Lobby",
		}
		sport := []string{
			"Sportsbook",
		}

		if in_array_string(dianzi, game.Category) {
			GameType = 1
		} else if in_array_string(qipai, game.Category) {
			GameType = 2
			//} else if in_array_string(quwei, game.Category) {
			//	GameType = 3
		} else if in_array_string(lottery, game.Category) { //彩票暂时不需要

			GameType = 4
			continue
		} else if in_array_string(zhenren, game.Category) {
			GameType = 5
		} else if in_array_string(sport, game.Category) {
			GameType = 6
		}

		if theReq.Add == "1" {
			gameList := thirdGameModel.GameList{}
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", Brand, game.Code).First(&gameList).Error
			if err != nil {
				if errors.Is(err, daogorm.ErrRecordNotFound) {
					continue // 数据库中没有这个游戏，跳过
				}
				logs.Error("查询游戏失败: err=%v", err)
				continue
			}
			// 图标文件地址: https://luckmedia.link/bng_egypt_fire/thumb_3_4_custom.jpg
			icon := s.AssetsHost + "/" + game.Code + "/" + "thumb_3_4_custom.jpg"
			eicon := s.AssetsHost + "/" + game.Code + "/" + "thumb_3_4_custom.jpg"
			//写入数据库中
			gameData := xgo.H{
				"Brand":     Brand,
				"GameId":    game.Code,
				"Name":      game.Name,
				"EName":     game.Name,
				"GameType":  GameType,
				"HubType":   1, // 0非聚合类型 1hub88类型
				"State":     1,
				"OpenState": 1,
				"Icon":      icon,
				"EIcon":     eicon,
			}
			server.Db().Table("x_game_list").Insert(gameData)
		}
		logs.Info("st8 GetGameList 成功写入数据")
	}

	return
}

// GameLaunch 游戏登录
func (s *ST8Service) GameLaunch(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId     string `json:"GameId" validate:"required"` // 游戏代码
		LangCode   string `json:"LangCode"`                   // 语言代码
		HomeUrl    string `json:"HomeUrl"`                    // 返回商户地址
		DepositUrl string //我们的存款页面
		DeviceType string `json:"DeviceType"` // 设备类型
	}

	errcode := 0
	var reqdata RequestData
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("st8 Login 请求参数错误: err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	validate := validator.New()
	if err := validate.Struct(reqdata); err != nil {
		logs.Error("st8 Login 请求参数验证失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "请求参数验证失败")
		return
	}

	// 获取token并验证用户登录状态
	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("st8 Login 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId("st8:", userId); err != nil {
		logs.Error("st8 Login 获取用户登录状态错误: userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, s.brandName, reqdata.GameId); err != nil {
		logs.Error("st8 登录游戏 权限检查错误 userId=", userId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("st8 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 查询游戏列表
	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", fmt.Sprintf("%s%%", s.brandName), reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("st8 Login 查询游戏错误 userId=", token.UserId, " gamecode=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("st8 Login 游戏不可用 userId=", token.UserId, " gamecode=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	providerCode := gameList.Brand
	providerCode = strings.TrimPrefix(providerCode, s.brandName)
	// 获取区域配置
	var _, siteID, baseHost, _, privateKey, currency string
	_, siteID, baseHost, _, privateKey, currency = s.getRegionConfigByProviderCode(providerCode)

	// 构建请求参数
	playerToken := s.userId2token(userId)
	currentTime := time.Now().Format("2006-01-02")

	// 构建请求体
	payload := map[string]interface{}{
		"game_code": reqdata.GameId, // 游戏代码，由ST8平台提供的唯一标识符
		"currency":  currency,       // 货币代码，用于游戏内的货币显示和计算
		"site": map[string]interface{}{ // 站点相关信息
			"id":      siteID,                         // 站点ID，用于标识商户站点
			"lobby":   reqdata.HomeUrl,                // 游戏大厅URL，玩家可以返回的地址
			"deposit": reqdata.HomeUrl + "/#/deposit", // 充值页面URL，玩家可以进行充值的地址
		},
		"token":   playerToken,               // 玩家会话令牌，用于验证玩家身份
		"player":  fmt.Sprintf("%d", userId), // 玩家ID，用于标识玩家
		"country": nil,                       //CN                      // 玩家所在国家，使用ISO 3166-1 alpha-2代码
		"lang": func() interface{} {
			if reqdata.LangCode == "" {
				return nil
			}
			return reqdata.LangCode
		}(), // 游戏语言代码，决定游戏界面显示语言
		"device":   reqdata.DeviceType, // 设备类型，如DESKTOP、MOBILE等
		"fun_mode": false,              // 游戏模式，false表示正式模式，true表示试玩模式
		"player_profile": map[string]interface{}{ // 玩家详细信息
			"id":               fmt.Sprintf("%d", userId), // 玩家ID，需要与外层player字段一致
			"jurisdiction":     "CW",                      // 管辖区域，表示玩家所属的监管区域
			"default_currency": currency,                  // 默认货币，玩家账户的默认货币类型
			"reg_country":      "IN",                      // 注册国家，玩家注册时的所在国家 IN
			"affiliate":        nil,                       // 代理ID
			"bet_limits":       "low",                     // 投注限制，可以是low、normal、high等
			"birth_date":       currentTime,               // 出生日期
			"reg_date":         currentTime,               // 注册日期
			"attributes": map[string]interface{}{ // 玩家额外属性
				"labels": []string{"language "}, // 玩家标签，用于标识玩家特征或偏好
			},
		},
	}
	// 构建请求
	requestURL := baseHost + "/api/operator/v1/launch"
	logs.Info("st8 GameLaunch 请求URL: ", requestURL)
	reqBytes, _ := json.Marshal(payload)
	logs.Info("st8 Login 请求参数: ", string(reqBytes))

	// 生成ECDSA签名
	signature, err := s.generateECDSASignature(reqBytes, privateKey)
	if err != nil {
		logs.Error("st8 Login 生成签名失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", requestURL, bytes.NewBuffer(reqBytes))
	if err != nil {
		logs.Error("st8 Login 创建请求失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}

	// 设置请求头
	req.Header.Set("x-st8-sign", signature)
	req.Header.Set("Content-Type", "application/json")
	// 打印请求信息
	//logs.Info("请求头:")
	//for key, values := range req.Header {
	//	for _, value := range values {
	//		logs.Info("  %s: %s", key, value)
	//	}
	//}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("st8 Login 请求失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "请求游戏失败")
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("st8 Login 读取响应失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "读取响应失败")
		return
	}

	logs.Info("st8 Login 响应数据: ", string(body))

	// 解析响应
	var result struct {
		Status  string `json:"status"`
		GameUrl string `json:"game_url"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("st8 Login 解析响应失败: err=", err.Error())
		ctx.RespErrString(true, &errcode, "解析响应失败")
		return
	}

	logs.Info("st8 Login 解析结果: status=", result.Status, " game_url=", result.GameUrl)

	if result.Status != "ok" {
		logs.Error("st8 Login API返回错误: ", result.Status)
		ctx.RespErrString(true, &errcode, "获取游戏地址失败")
		return
	}

	// 返回游戏URL
	ctx.Put("url", result.GameUrl+"&token="+playerToken)
	ctx.Put("token", playerToken)
	ctx.RespOK()
}

// getRoundDetail 返回游戏局详情
func (s *ST8Service) getRoundDetail(reqUser, reqTransactionId, reqRound string, site string) (detailUrl string, err error) {
	// 构造请求参数
	type RequestData struct {
		Player        string `json:"player"`         // 玩家ID
		TransactionId string `json:"transaction_id"` // 交易ID
		Round         string `json:"round"`          // 局号ID
		Format        string `json:"format"`         // 响应格式
	}

	req := RequestData{
		Player:        reqUser,
		TransactionId: reqTransactionId,
		Round:         reqRound,
		Format:        "html",
	}

	// 序列化请求数据
	reqBytes, err := json.Marshal(req)
	if err != nil {
		logs.Error("st8 getRoundDetail 序列化请求数据失败: err=", err.Error())
		return "", err
	}
	logs.Info("st8 getRoundDetail 请求参数:", string(reqBytes))

	// 构建查询参数
	params := url.Values{}
	params.Add("round", req.Round)
	params.Add("transaction_id", req.TransactionId)
	params.Add("player", req.Player)
	params.Add("format", req.Format)

	// 获取区域配置
	var _, _, baseHost, _, privateKey, _ string
	_, _, baseHost, _, privateKey, _ = s.getRegionConfigBySite(site)
	// 生成签名
	// 生成ECDSA签名
	signature, err := s.generateECDSASignature([]byte(fmt.Sprintf("%v", params.Encode())), privateKey)
	if err != nil {
		logs.Error("st8 getRoundDetail 生成签名失败: err=", err.Error())
		return "", err
	}

	// 构建URL
	endpoint := fmt.Sprintf("%s/api/operator/v1/round_info?%s", baseHost, params.Encode())
	//logs.Info("请求URL: %s", endpoint)

	httpReq, err := http.NewRequest(http.MethodGet, endpoint, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("x-st8-sign", signature)
	httpReq.Header.Set("Content-Type", "application/json")

	// 打印请求信息
	//logs.Info("请求方法: %s", httpReq.Method)
	//logs.Info("请求头:")
	//for key, values := range httpReq.Header {
	//	for _, value := range values {
	//		logs.Info("  %s: %s", key, value)
	//	}
	//}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	//logs.Info("响应状态码: %d", resp.StatusCode)
	//logs.Info("响应头:")
	//for key, values := range resp.Header {
	//	for _, value := range values {
	//		logs.Info("  %s: %s", key, value)
	//	}
	//}

	//logs.Info("响应内容: %s", string(body))

	// 解析响应
	type Response struct {
		Status string `json:"status"`
		Url    string `json:"url"`
	}
	var result Response
	if err := json.Unmarshal(body, &result); err != nil {
		logs.Error("st8 getRoundDetail 解析响应失败: err=", err.Error())
		return "", err
	}

	// 检查响应状态并处理特定错误
	if result.Status != ST8_STATUS_OK {
		logs.Error("st8 getRoundDetail 获取游戏局详情失败: status=", result.Status)
		// 如果是未知错误，等待3秒后重试
		if result.Status == ST8_STATUS_UNKNOWN {
			time.Sleep(time.Second * 3)
			return s.getRoundDetail(reqUser, reqTransactionId, reqRound, site)
		}
		return "", fmt.Errorf("获取游戏局详情失败: status=%s", result.Status)
	}

	return result.Url, nil
}

// SyncGameStatus 同步ST8游戏状态
func (s *ST8Service) SyncGameStatus(ctx *abugo.AbuHttpContent) {

	type Game struct {
		Code                  string   `json:"code"`                   // 游戏代码
		Name                  string   `json:"name"`                   // 游戏名称
		Enabled               bool     `json:"enabled"`                // 是否启用
		Developer             string   `json:"developer"`              // 开发商
		BonusTypes            []string `json:"bonus_types"`            // 支持的奖金类型
		Category              string   `json:"category"`               // 游戏分类
		Themes                []string `json:"themes"`                 // 主题列表
		Features              []string `json:"features"`               // 特性列表
		RTP                   string   `json:"rtp"`                    // 返还率
		Volatility            int      `json:"volatility"`             // 波动性
		MaxPayoutCoeff        string   `json:"max_payout_coeff"`       // 最大支付系数
		HitRatio              string   `json:"hit_ratio"`              // 命中率
		FunMode               bool     `json:"fun_mode"`               // 游戏模式，false表示正式模式，true表示试玩模式
		ReleaseDate           string   `json:"release_date"`           // 发布日期
		DeprecationDate       string   `json:"deprecation_date"`       // 弃用日期
		RestrictedTerritories []string `json:"restricted_territories"` // 限制地区
		ProhibitedTerritories []string `json:"prohibited_territories"` // 禁止地区
	}

	type Developer struct {
		Name                  string   `json:"name"`                   // 开发商名称
		Code                  string   `json:"code"`                   // 开发商代码
		RestrictedTerritories []string `json:"restricted_territories"` // 限制地区
		ProhibitedTerritories []string `json:"prohibited_territories"` // 禁止地区
	}

	type Category struct {
		Name string `json:"name"` // 分类名称
		Type string `json:"type"` // 分类类型
	}

	type GameResponse struct {
		Status     string      `json:"status"`     // 状态: ok/error
		Games      []Game      `json:"games"`      // 游戏列表
		Developers []Developer `json:"developers"` // 开发商列表
		Categories []Category  `json:"categories"` // 游戏分类列表
	}
	type RequestData struct {
		Line string `json:"line"` // EU 或者 ASIA
		Add  string `json:"add"`  // 1 添加数据库 非1不添加数据库
	}

	// 构建请求URL
	apiUrl := fmt.Sprintf("%s/api/operator/v1/games", s.BaseHost)

	// 添加查询参数
	queryParams := url.Values{}
	queryParams.Add("site", s.SiteID)

	// 构建完整URL
	fullUrl := fmt.Sprintf("%s?%s", apiUrl, queryParams.Encode())

	// 创建请求
	req, err := http.NewRequest("GET", fullUrl, nil)
	if err != nil {
		logs.Error("创建请求失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 生成ECDSA签名
	signature, err := s.generateECDSASignature([]byte(fmt.Sprintf("%v", queryParams.Encode())), s.PrivateKey)
	if err != nil {
		logs.Error("st8 生成签名失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 添加签名到请求头
	req.Header.Set("x-st8-sign", signature)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("发送请求失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取响应失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 解析响应
	var gameResp GameResponse
	if err := json.Unmarshal(body, &gameResp); err != nil {
		logs.Error("解析响应失败: err=%v", err)
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	// 检查响应状态
	if gameResp.Status != "ok" {
		logs.Error("API返回错误状态: %s", gameResp.Status)
		ctx.RespErrString(true, nil, gameResp.Status)
		return
	}

	// 遍历游戏列表
	for _, game := range gameResp.Games {
		// 获取游戏供应商代码
		code := s.getProviderCode(strings.TrimSpace(game.Developer))
		if code == "" {
			continue
		}

		Brand := s.brandName + code

		// 如果游戏在ST8是禁用状态，检查数据库中的状态
		if !game.Enabled {
			var gameList thirdGameModel.GameList
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", Brand, game.Code).First(&gameList).Error
			if err != nil {
				if errors.Is(err, daogorm.ErrRecordNotFound) {
					continue // 数据库中没有这个游戏，跳过
				}
				logs.Error("查询游戏失败: err=%v", err)
				continue
			}

			// 如果数据库中游戏是开启状态，则关闭它
			if gameList.State == 1 {
				err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", Brand, game.Code).Updates(map[string]interface{}{
					"State": 2,
				}).Error
				if err != nil {
					logs.Error("更新游戏状态失败: Brand=%s, GameId=%s, err=%v", Brand, game.Code, err)
					continue
				}
				logs.Info("st8 游戏状态已更新为关闭: Brand=%s, GameId=%s", Brand, game.Code)
			}
		}
	}

	//ctx.RespString(true, nil, "游戏状态同步完成")
}

// 获取公钥配置
func (s *ST8Service) getPublicKeyBySite(site string) (publicKey string) {
	_, _, _, publicKey, _, _ = s.getRegionConfigBySite(site)
	return publicKey
}

// 获取私钥配置
func (s *ST8Service) getPrivateKeyBySite(site string) (privateKey string) {
	_, _, _, privateKey, _, _ = s.getRegionConfigBySite(site)
	return privateKey
}

// getRegionConfigBySite 根据站点ID获取对应的区域配置
func (s *ST8Service) getRegionConfigBySite(site string) (operatorCode, siteID, baseHost, publicKey, privateKey, currency string) {
	// 默认使用全局配置
	operatorCode = s.OperatorCode
	siteID = s.SiteID
	baseHost = s.BaseHost
	publicKey = s.PublicKey
	privateKey = s.PrivateKey
	currency = s.Currency

	// 根据站点ID选择区域配置
	if site == s.AsiaSiteID {
		operatorCode = s.AsiaOperatorCode
		siteID = s.AsiaSiteID
		baseHost = s.AsiaBaseHost
		publicKey = s.AsiaPublicKey
		privateKey = s.AsiaPrivateKey
		currency = s.AsiaCurrency
		logs.Info("使用亚洲区域配置 site=%s", site)
	} else if site == s.RowSiteID {
		operatorCode = s.RowOperatorCode
		siteID = s.RowSiteID
		baseHost = s.RowBaseHost
		publicKey = s.RowPublicKey
		privateKey = s.RowPrivateKey
		currency = s.RowCurrency
		logs.Info("使用世界其他地区配置 site=%s", site)
	} else {
		logs.Info("使用默认配置 site=%s", site)
	}

	return
}

// getRegionConfigByProviderCode 根据游戏供应商代码获取对应的区域配置
func (s *ST8Service) getRegionConfigByProviderCode(providerCode string) (operatorCode, siteID, baseHost, publicKey, privateKey, currency string) {
	// 需要使用Asia配置的厂商列表
	asiaProviders := map[string]bool{
		"rpl": true, // ReelPlay
		"rlg": true, // Reel Life Games
		"plt": true, // Playtech
		"reg": true, // Relax Gaming
		"rts": true, // RTG Slots
		"qbt": true, // Qubit Games
		"osp": true, // 1Spin4Win
		"nrl": true, // Northern Lights Gaming
		"rfg": true, // Reflex Gaming
		"hrg": true, // Hot Rise Games
		"bpg": true, // Bulletproof Games
		"gmv": true, // GameVy
		"jly": true, // Jelly
		"hbg": true, // Hungry Bear Gaming
		"png": true, // Play'n Go
		"ygg": true, // Yggdrasil
		"bbg": true, // Bang Bang Games
		"brg": true, // Boomerang Studios
		"jil": true, // Jili Games
		"psh": true, // Push Gaming
		"pnt": true, // Print Studios
		"qps": true, // Quickspin
		"ftp": true, // 4ThePlayer
		"bng": true, // Booongo
		"wfg": true, // Win Fast Games
		"rbc": true, // Rabcat Gambling
	}

	// 根据供应商代码选择区域配置
	if _, ok := asiaProviders[providerCode]; ok {
		// 列表中的厂商使用Asia配置
		operatorCode = s.AsiaOperatorCode
		siteID = s.AsiaSiteID
		baseHost = s.AsiaBaseHost
		publicKey = s.AsiaPublicKey
		privateKey = s.AsiaPrivateKey
		currency = s.AsiaCurrency
		logs.Info("使用亚洲区域配置 provider=%s,siteID=%s,currency=%s", providerCode, siteID, currency)
	} else {
		// 其他厂商使用Row配置
		operatorCode = s.RowOperatorCode
		siteID = s.RowSiteID
		baseHost = s.RowBaseHost
		publicKey = s.RowPublicKey
		privateKey = s.RowPrivateKey
		currency = s.RowCurrency
		logs.Info("使用世界其他地区配置 provider=%s,siteID=%s,currency=%s", providerCode, siteID, currency)
	}

	return
}
