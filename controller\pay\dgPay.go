package paycontroller

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var DGPay = new(dgPay)

type dgPay struct {
	Base
}

type DgPayMethodCfg struct {
	OpCode string `json:"op_code"`
	Key    string `json:"key"`
	URL    string `json:"url"`
	Cburl  string `json:"cburl"`
}

type DgPayRechargeProvider struct {
	ProviderID   int `json:"provider_id"`
	ProviderType int `json:"provider_type"`
}

type DgPayRechargeRes struct {
	Code             string  `json:"code"`
	RefID            string  `json:"refId"`
	Amount           int     `json:"amount"`
	AmountToWithdraw float64 `json:"amountToWithdraw"`
	AdminFee         float64 `json:"adminFee"`
	PlayerFee        float64 `json:"playerFee"`
	AgentFee         float64 `json:"agentFee"`
	PaymentUrl       string  `json:"paymentUrl"`
	Description      string  `json:"description"`
}

type DgPayRechargeResSgd struct {
	Code        string `json:"code"`        // 错误代码，参考错误代码附录 (8.Error Code Definition)
	Description string `json:"description"` // 请求状态描述
	RefID       string `json:"refId"`       // 系统生成的转账ID
	PaymentUrl  string `json:"paymentUrl"`  // 支付跳转URL
}

type DgPayRechargeStatusRes struct {
	Code            string  `json:"code"`
	RefID           string  `json:"refId"`
	Status          int     `json:"status"`
	Amount          int     `json:"amount"`
	AmountToDeposit float64 `json:"amountToDeposit"`
	AdminFee        float64 `json:"adminFee"`
	Description     string  `json:"description"`
}

type DgPayWithdrawStatusRes struct {
	Code            string  `json:"code"`
	RefID           string  `json:"refId"`
	Status          int     `json:"status"`
	Amount          int     `json:"amount"`
	AmountToDeposit float64 `json:"amountToDeposit"`
	AdminFee        float64 `json:"adminFee"`
	Description     string  `json:"description"`
}

type DgPayRechargeCallback struct {
	OrderID string `json:"orderId"`
	Amount  string `json:"amount"`
	Status  string `json:"status"`
}

type DgPayWithdrawCallback struct {
	OrderID string `json:"orderId"`
	Amount  string `json:"amount"`
	Status  string `json:"status"`
}

func (c *dgPay) Init() {
	server.Http().PostNoAuth("/api/dgPay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/dgPay/withdraw/callback", c.withdrawCallback)
}

func (c *dgPay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	// 获取请求参数
	errcode := 0

	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	cfg := DgPayMethodCfg{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// provider
	provider := DgPayRechargeProvider{}
	json.Unmarshal([]byte(payMethod.PayType), &provider)
	logs.Info("PayType", provider)
	// 新增订单
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      11,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	data := ""
	if provider.ProviderID == 5 && provider.ProviderType == 10 {
		data = fmt.Sprintf(`{"fromBankCode":%v,"redirectUrl": "success"}`, req.BankCode)
	}
	if provider.ProviderID == 5 && provider.ProviderType == 20 {
		data = `{"redirectUrl": "success"}`
	}
	if provider.ProviderID == 12 && provider.ProviderType == 20 {
		data = fmt.Sprintf(`{"name":"%v","redirectUrl":"success","bankCode":"%v"}`, req.RealName, req.BankCode)
	}
	// 发起支付
	var params map[string]string
	// 判断是否新加坡
	if strings.ToUpper(req.Symbol) == "SGD" {
		params = map[string]string{
			"opCode":      cfg.OpCode,                                 // 运营商代码
			"paymentType": "P2P",                                      // 支付类型
			"orderId":     fmt.Sprintf("%v", rechargeOrder.ID),        // 商户交易唯一ID
			"currency":    strings.ToUpper(req.Symbol),                // 使用 SGD
			"amount":      fmt.Sprintf("%.2f", req.Amount),            // 转账金额
			"callbackUrl": cfg.Cburl + "/api/dgPay/recharge/callback", // 回调通知URL
			"redirectUrl": "success",                                  // 支付完成后的重定向URL
			"reqDateTime": time.Now().Format("2006-01-02 15:04:05"),   // 请求时间
			"sender": fmt.Sprintf(`{
				"senderFirstName": "%v",
				"senderLastName": "",
				"senderNo": "%v",
				"username": "%v",
				"phone": "%v",
				"bankCode": "%v"
			}`, req.RealName,
				user.UserID,
				user.Account,
				user.PhoneNum,
				req.BankCode),
		}
		var builder strings.Builder

		// 按照指定顺序拼接参数
		builder.WriteString(params["opCode"])
		builder.WriteString(params["orderId"])
		builder.WriteString(params["paymentType"])
		builder.WriteString(params["currency"])
		builder.WriteString(params["amount"])
		builder.WriteString(params["callbackUrl"])
		builder.WriteString(params["redirectUrl"])
		builder.WriteString(params["reqDateTime"])
		builder.WriteString(params["sender"])
		builder.WriteString(cfg.Key)
		// 计算 securityToken
		params["securityToken"] = c.Md5SignBySgd(builder)
	} else {
		params = map[string]string{
			"orderId":      fmt.Sprintf("%v", rechargeOrder.ID),
			"providerId":   fmt.Sprintf("%v", provider.ProviderID),
			"providerType": fmt.Sprintf("%v", provider.ProviderType),
			"currency":     strings.ToUpper(req.Symbol),
			"amount":       fmt.Sprintf("%v", req.Amount),
			"reqDateTime":  time.Now().Format("2006-01-02 15:04:05"),
			"opCode":       cfg.OpCode,
		}

		params["securityToken"] = c.dGGenerateMd5Sign(params, cfg.Key)
		params["callbackUrl"] = cfg.Cburl + "/api/dgPay/recharge/callback"
		params["data"] = data
	}

	var requestUrl string
	if strings.ToUpper(req.Symbol) == "SGD" {
		requestUrl = cfg.URL + "/ajax/api/v2/deposit"
	} else {
		requestUrl = cfg.URL + "/ajax/api/deposit"
	}
	resp, err := c.postFrom(requestUrl, map[string]string{
		"Content-Type": "x-www-form-urlencoded",
	}, params)

	if err != nil {
		logs.Info("请求DGPay支付失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("请求DGPay支付失败"), &errcode)
		return
	}
	// 获取支付url
	var payurl string
	if strings.ToUpper(req.Symbol) == "SGD" {
		rechargeRes := DgPayRechargeResSgd{}
		json.Unmarshal(resp.Body(), &rechargeRes)
		if rechargeRes.Code != "0" {
			tx.Rollback()
			ctx.RespErr(errors.New("三方返回失败"), &errcode)
			return
		}
		payurl = rechargeRes.PaymentUrl
		// 更新第三方ID
		_, err = tx.XRecharge.WithContext(ctx.Gin()).
			Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
			Updates(map[string]interface{}{
				"ThirdId": rechargeRes.RefID,
			})
		if err != nil {
			tx.Rollback()
			ctx.RespErr(errors.New("更新第三方ID失败"), &errcode)
			return
		}
	} else {
		rechargeRes := DgPayRechargeRes{}
		json.Unmarshal(resp.Body(), &rechargeRes)
		if rechargeRes.Code != "0" {
			tx.Rollback()
			ctx.RespErr(errors.New("三方返回失败"), &errcode)
			return
		}
		// 更新第三方ID
		_, err = tx.XRecharge.WithContext(ctx.Gin()).
			Where(tx.XRecharge.ID.Eq(rechargeOrder.ID)).
			Updates(map[string]interface{}{
				"ThirdId": rechargeRes.RefID,
			})
		if err != nil {
			tx.Rollback()
			ctx.RespErr(errors.New("更新第三方ID失败"), &errcode)
			return
		}
		payurl = rechargeRes.PaymentUrl
	}

	tx.Commit()
	ctx.RespOK(xgo.H{
		"payurl":  payurl,
		"orderId": rechargeOrder.ID,
	})
}

// rechargeCallback 处理充值回调
// 接收第三方支付平台的充值回调通知，验证订单信息并更新订单状态
// 使用数据库事务确保数据一致性
func (c *dgPay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 解析回调参数
	callback := DgPayRechargeCallback{
		OrderID: ctx.Gin().PostForm("orderId"),
		Amount:  ctx.Gin().PostForm("amount"),
		Status:  ctx.Gin().PostForm("status"),
	}

	// 记录回调数据
	callbackData, _ := json.Marshal(&callback)
	logs.Info("DgPay Recharge Callback Data:", string(callbackData))

	// 解析订单号
	orderSplit := strings.Split(callback.OrderID, "@")
	orderNo, _ := strconv.Atoi(orderSplit[0])

	// 查看订单是否存在
	order, err := c.getRechargeOrder(orderNo)
	if err != nil {
		logs.Error("DGPay充值回调：订单不存在 orderNo=%v, error=%v", orderNo, err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Order not found",
		})
		return
	}

	// 是否已处理成功的订单
	if order.State == 5 {
		logs.Info("DGPay充值回调：订单已处理成功 orderNo=%v", orderNo)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	}

	// 验证支付金额是否一致
	callbackAmount, _ := strconv.ParseFloat(callback.Amount, 64)
	if math.Abs(order.Amount-callbackAmount) > 0.01 {
		logs.Error("DGPay充值回调：金额不匹配 订单实际金额=%v, 回调金额=%v", order.Amount, callbackAmount)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Discrepancy in amounts",
		})
		return
	}

	// 验证回调状态
	if callback.Status != "20" {
		logs.Error("DGPay充值回调：状态不正确 status=%v", callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Invalid status",
		})
		return
	}

	// 获取支付方式配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("DGPay充值回调：支付方式不存在 PayID=%v, error=%v", order.PayID, err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Payment method not found",
		})
		return
	}

	cfg := DgPayMethodCfg{}
	// 解析支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 构建查询订单状态的参数
	params := map[string]string{
		"orderId":     fmt.Sprintf("%v", order.ID),
		"reqDateTime": time.Now().Format("2006-01-02 15:04:05"),
		"opCode":      cfg.OpCode,
	}

	// 构建签名参数生成签名 orderId + reqDateTime + opCode + secretKey
	var builder strings.Builder
	builder.WriteString(params["orderId"])
	builder.WriteString(params["reqDateTime"])
	builder.WriteString(params["opCode"])
	builder.WriteString(cfg.Key)
	params["securityToken"] = c.Md5SignBySgd(builder)

	// 向第三方查询订单状态
	resp, err := c.postFrom(cfg.URL+"/ajax/api/queryDepositTrans", map[string]string{
		"Content-Type": "x-www-form-urlencoded",
	}, params)
	if err != nil {
		logs.Error("DGPay充值回调：查询订单状态失败 error=%v", err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Query order status failed",
		})
		return
	}

	// 解析查询结果
	rechargeStatusRes := DgPayRechargeStatusRes{}
	json.Unmarshal(resp.Body(), &rechargeStatusRes)

	// 验证订单状态
	if rechargeStatusRes.Status != 20 {
		logs.Error("DGPay充值回调：第三方订单状态不正确 status=%v", rechargeStatusRes.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Status not success",
		})
		return
	}

	// 更新第三方订单号
	err = c.updateThirdOrder(order.ID, callback.OrderID)
	if err != nil {
		logs.Error("DGPay充值回调：更新第三方订单号失败 error=%v", err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Update third order failed",
		})
		return
	}

	// 更新订单状态
	// 使用 rechargeCallbackHandel 函数处理充值回调
	// 该函数内部会调用存储过程更新用户余额和相关状态
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

	logs.Info("DGPay充值回调：处理成功 orderNo=%v, amount=%v", orderNo, order.Amount)

	// 返回成功响应
	ctx.Gin().JSON(200, xgo.H{
		"code":        "0",
		"description": "Success",
	})
}

func (c *dgPay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	callback := DgPayWithdrawCallback{
		OrderID: ctx.Gin().PostForm("orderId"),
		Amount:  ctx.Gin().PostForm("amount"),
		Status:  ctx.Gin().PostForm("status"),
	}

	orderSplit := strings.Split(callback.OrderID, "@")

	// 订单号拆分
	orderNo, _ := strconv.Atoi(orderSplit[0])

	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：订单不存在 orderNo=%v, error=%v", orderNo, err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "订单号不存在",
		})
		return
	}

	// 成功订单
	if order.State == 6 {
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	}

	// 验证支付金额是否一致
	callbackAmount, _ := strconv.ParseFloat(callback.Amount, 64)
	if math.Abs(order.RealAmount-callbackAmount) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：金额不匹配 订单实际金额=%v, 回调金额=%v", order.RealAmount, callbackAmount)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "金额不一致",
		})
		return
	}

	// 根据DGPay官方状态码处理
	switch callback.Status {
	case "20": // Success - 成功状态
		logs.Info("DGPay提现回调：收到成功状态 orderNo=%v, status=%v", orderNo, callback.Status)
		// 继续后续处理逻辑
	case "-10": // Reject - 拒绝失败
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：订单被拒绝 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	case "-20": // Expired - 过期失败
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：订单已过期 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	case "0": // Pending - 待处理
		logs.Info("DGPay提现回调：订单待处理 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	case "5": // Pending Verification - 待验证
		logs.Info("DGPay提现回调：订单待验证 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	case "10": // Processing - 处理中
		logs.Info("DGPay提现回调：订单处理中 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	default: // 未知状态
		logs.Warn("DGPay提现回调：收到未知状态 orderNo=%v, status=%v", orderNo, callback.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "0",
			"description": "Success",
		})
		return
	}

	// 获取配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：支付方式不存在 PayID=%v, error=%v", order.PayID, err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Error",
		})
		return
	}

	cfg := DgPayMethodCfg{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 检查订单状态
	params := map[string]string{
		"orderId":     fmt.Sprintf("%v", order.ID),
		"reqDateTime": time.Now().Format("2006-01-02 15:04:05"),
		"opCode":      cfg.OpCode,
	}
	params["securityToken"] = c.dGGenerateMd5Sign(params, cfg.Key)

	resp, err := c.postFrom(cfg.URL+"/ajax/api/queryWithdrawTrans", map[string]string{
		"Content-Type": "x-www-form-urlencoded",
	}, params)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：请求dgPay状态失败 error=%v", err)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Error",
		})
		return
	}

	statusRes := DgPayWithdrawStatusRes{}
	json.Unmarshal(resp.Body(), &statusRes)

	if statusRes.Status != 20 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("DGPay提现回调：DGPay订单状态不正确 status=%v", statusRes.Status)
		ctx.Gin().JSON(200, xgo.H{
			"code":        "-100",
			"description": "Error",
		})
		return
	}

	// 执行到这里说明是状态"20"成功，进行成功处理
	c.withdrawCallbackHandel(int(order.ID), 6)
	logs.Info("DGPay提现回调：处理成功 orderNo=%v, amount=%v", orderNo, order.RealAmount)

	ctx.Gin().JSON(200, xgo.H{
		"code":        "0",
		"description": "Success",
	})
}

func (c *dgPay) dGGenerateMd5Sign(params map[string]string, secretKey string) string {
	var str strings.Builder
	// 需要按顺序拼接的键
	keys := []string{
		"orderId",
		"providerId",
		"providerType",
		"currency",
		"amount",
		"reqDateTime",
		"opCode",
	}
	for _, key := range keys {
		if _, ok := params[key]; !ok {
			continue
		}
		str.WriteString(params[key])
	}

	str.WriteString(secretKey)
	logs.Info("DGPAY请求支付加密前", str.String())

	// 创建一个 MD5 实例
	hash := md5.New()

	// 将字符串转换为字节数组并计算 MD5 值
	hash.Write([]byte(str.String()))
	hashBytes := hash.Sum(nil)

	// 将 MD5 值转换为十六进制字符串
	md5Str := hex.EncodeToString(hashBytes)

	logs.Info("DGPAY请求支付加密后：", md5Str)

	return md5Str
}

func (c *dgPay) Md5SignBySgd(encrypstring strings.Builder) string {
	logs.Info("DGPAY请求支付加密前", encrypstring.String())
	// 创建一个 MD5 实例
	hash := md5.New()
	hash.Write([]byte(encrypstring.String()))
	hashBytes := hash.Sum(nil)
	md5Str := hex.EncodeToString(hashBytes)
	logs.Info("DGPAY请求支付加密后：", md5Str)
	return md5Str
}
