// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXChannel = "x_channel"

// XChannel mapped from table <x_channel>
type XChannel struct {
	ChannelID     int32     `gorm:"column:ChannelId;primaryKey;autoIncrement:true" json:"ChannelId"`
	SellerID      int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`        // 运营商
	ChannelName   string    `gorm:"column:ChannelName;comment:渠道名称" json:"ChannelName"` // 渠道名称
	ShowName      string    `gorm:"column:ShowName" json:"ShowName"`
	State         int32     `gorm:"column:State;comment:1启用 2禁用" json:"State"` // 1启用 2禁用
	Remark        string    `gorm:"column:Remark;comment:备注" json:"Remark"`    // 备注
	Icon          string    `gorm:"column:Icon" json:"Icon"`
	Logo          string    `gorm:"column:Logo" json:"Logo"`
	CreateTime    time.Time `gorm:"column:CreateTime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	Sort          int32     `gorm:"column:Sort" json:"Sort"`
	ChatOpen      int32     `gorm:"column:ChatOpen;default:2;comment:聊天室是否开启 1开启,2关闭" json:"ChatOpen"` // 聊天室是否开启 1开启,2关闭
	ChatCompanyID string    `gorm:"column:ChatCompanyId" json:"ChatCompanyId"`
	AgentMode     int32     `gorm:"column:AgentMode;not null;default:1;comment:代理模式 1-无限代 2-三级代" json:"AgentMode"` // 代理模式 1-无限代 2-三级代
	AgentCaseID   int32     `gorm:"column:AgentCaseId;not null;comment:三级代理方案ID" json:"AgentCaseId"`               // 三级代理方案ID
}

// TableName XChannel's table name
func (*XChannel) TableName() string {
	return TableNameXChannel
}
