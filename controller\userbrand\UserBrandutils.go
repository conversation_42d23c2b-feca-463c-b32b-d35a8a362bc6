// 用户当前游戏状态和禁止游戏厂商的Redis键前缀
package userbrand

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
)

const (
	UserCurrentGameKey       = "user:current_game:"        // 用户当前游戏状态
	UserBlockedGameBrandsKey = "user:blocked_game_brands:" // 用户禁止的游戏厂商列表
)

// SetUserCurrentGame 设置用户当前游戏状态（游戏类型和厂商）
func SetUserCurrentGame(userId int, gameType int, brand string) error {
	key := UserCurrentGameKey + strconv.Itoa(userId)

	// 将游戏类型和厂商组合成JSON格式存储
	gameInfo := map[string]interface{}{
		"gameType": gameType,
		"brand":    brand,
	}

	gameInfoJson, err := json.Marshal(gameInfo)
	if err != nil {
		logs.Error("序列化用户当前游戏信息失败 userId=", userId, " gameType=", gameType, " brand=", brand, " err=", err.Error())
		return err
	}

	err = server.CRedis().SetStringEx(key, 30000, string(gameInfoJson))
	logs.Info("设置玩家当前游戏状态 userId=", userId, " key=", key, " gameType=", gameType, " brand=", brand)
	if err != nil {
		logs.Error("设置用户当前游戏状态失败 userId=", userId, " gameType=", gameType, " brand=", brand, " err=", err.Error())
	}
	return err
}

// GetUserCurrentGame 获取用户当前游戏状态（游戏类型和厂商）
func GetUserCurrentGame(userId int) (int, string, error) {
	key := UserCurrentGameKey + strconv.Itoa(userId)

	rValueInterface := server.CRedis().Get(key)
	rValue := abugo.GetStringFromInterface(rValueInterface)

	if rValue == "" {
		logs.Info("玩家当前游戏状态为空 userId=", userId, " key=", key)
		return 0, "", nil
	}

	// 尝试解析为JSON格式
	var gameInfo map[string]interface{}
	err := json.Unmarshal([]byte(rValue), &gameInfo)
	if err != nil {
		// 如果解析失败，可能是旧格式（只有brand），兼容处理
		logs.Warning("解析用户当前游戏信息失败，可能是旧格式 userId=", userId, " value=", rValue, " err=", err.Error())
		return 0, rValue, nil
	}

	gameType := int(gameInfo["gameType"].(float64))
	brand := gameInfo["brand"].(string)

	logs.Info("获取玩家当前游戏状态 userId=", userId, " gameType=", gameType, " brand=", brand)
	return gameType, brand, nil
}

// ClearUserCurrentGame 清除用户当前游戏状态
func ClearUserCurrentGame(userId int) error {
	key := UserCurrentGameKey + strconv.Itoa(userId)
	err := server.CRedis().Del(key)
	if err != nil {
		logs.Error("清除用户当前游戏状态失败 userId=", userId, " err=", err.Error())
	}
	return err
}

// 设置用户禁止的游戏厂商列表
func SetUserBlockedGameBrands(userId int, brandsJson string) error {
	key := UserBlockedGameBrandsKey + strconv.Itoa(userId)
	err := server.XRedis().Set(key, brandsJson, 7*24*3600) // 7天过期
	if err != nil {
		logs.Error("设置用户禁止游戏厂商列表失败 userId=", userId, " err=", err.Error())
	}
	return err
}

// PublishToUser 发布消息到指定用户的WebSocket
func PublishToUser(userId int, msgType string, data interface{}) error {
	// 构造消息结构 - 使用大写字段，与订阅端保持一致
	message := map[string]interface{}{
		"UserId":    userId,
		"MsgType":   msgType,
		"Data":      data,
		"Timestamp": time.Now().Unix(),
		"MessageId": fmt.Sprintf("%d_%d_%s", userId, time.Now().UnixNano(), msgType),
	}

	// 发布到统一频道 - 让Redis客户端自动处理 JSON序列化
	channel := "ws:user:*"
	err := server.CRedis().Publish(channel, message)
	if err != nil {
		logs.Error("发布WebSocket消息到Redis失败: userId=", userId, " channel=", channel, " err=", err.Error())
		return err
	}

	logs.Debug("WebSocket消息已发布到Redis: userId=", userId, " msgType=", msgType, " channel=", channel)
	return nil
}

// GetBlockedTypeBrands 获取玩家被禁用的厂商ID列表（按后端type分组）
func GetBlockedTypeBrands(userId int) (map[string][]string, error) {
	key := UserBlockedGameBrandsKey + strconv.Itoa(userId)
	blockedConfigJson_, err := server.XRedis().Get(key)
	blockedConfigJson := abugo.GetStringFromInterface(blockedConfigJson_)

	// 如果Redis没有，从数据库查
	if blockedConfigJson == "" {
		var userMore struct {
			BlockedGameBrands string `gorm:"column:BlockedGameBrands"`
		}
		db := server.Db().GormDao()
		err = db.Table("x_user_more").
			Select("BlockedGameBrands").
			Where("UserId = ?", userId).
			First(&userMore).Error
		if err != nil {
			logs.Error("数据库获取用户禁止游戏配置失败 userId=%d err=%v", userId, err)
			return nil, err
		}
		blockedConfigJson = userMore.BlockedGameBrands
	}
	if blockedConfigJson == "" {
		return nil, nil
	}
	var blockedConfig struct {
		TypeBrands map[string][]string `json:"typeBrands"`
	}
	err = json.Unmarshal([]byte(blockedConfigJson), &blockedConfig)
	if err != nil {
		logs.Error("解析用户禁止游戏配置JSON失败 userId=%d json=%v err=%v", userId, blockedConfigJson, err)
		return nil, err
	}
	return blockedConfig.TypeBrands, nil
}

// GetBlockedBrands 获取玩家被禁用的厂商ID列表（所有类型合并）
func GetBlockedBrands(userId int) ([]string, error) {
	typeBrands, err := GetBlockedTypeBrands(userId)
	if err != nil {
		return nil, err
	}
	if len(typeBrands) == 0 {
		return nil, nil
	}

	// 合并所有type下的厂商ID
	brandSet := make(map[string]struct{})
	for _, brands := range typeBrands {
		for _, brand := range brands {
			brandSet[brand] = struct{}{}
		}
	}

	var result []string
	for brand := range brandSet {
		result = append(result, brand)
	}
	return result, nil
}

// GetBlockedBrandsByGameType 根据游戏类型获取该类型下的被禁用厂商列表
func GetBlockedBrandsByGameType(userId int, gameType int) ([]string, error) {
	if gameType <= 0 {
		return nil, nil
	}

	// 获取按类型分组的封禁厂商列表
	typeBrands, err := GetBlockedTypeBrands(userId)
	if err != nil {
		return nil, err
	}
	if len(typeBrands) == 0 {
		return nil, nil
	}

	// 获取该游戏类型下的封禁厂商列表
	gameTypeStr := fmt.Sprintf("%d", gameType)
	blockedBrands, exists := typeBrands[gameTypeStr]
	if !exists || len(blockedBrands) == 0 {
		return nil, nil
	}

	logs.Info("GetBlockedBrandsByGameType: userId=%d gameType=%d blockedBrands=%v", userId, gameType, blockedBrands)
	return blockedBrands, nil
}

// 前端type -> 后端type 映射
var catMap = map[string]int{
	"2":  1,
	"8":  2,
	"5":  3,
	"11": 4,
	"4":  5,
	"7":  6,
	"12": 7,
}

// 禁用品牌处理
func ProcessGameSortEx(GameSortEx map[string]interface{}, userId int) {
	typeBrands, _ := GetBlockedTypeBrands(userId) // map[string][]string，key为后端type
	if len(typeBrands) == 0 {
		return
	}

	for frontType, v := range GameSortEx {
		backendType, ok := catMap[frontType]
		if !ok {
			continue
		}
		blockedBrands, ok := typeBrands[fmt.Sprintf("%d", backendType)]
		if !ok || len(blockedBrands) == 0 {
			continue
		}
		arr, ok := v.([]interface{})
		if !ok {
			continue
		}
		for _, item := range arr {
			m, ok := item.(map[string]interface{})
			if !ok {
				continue
			}
			id := fmt.Sprintf("%v", m["id"])
			for _, blocked := range blockedBrands {
				if id == blocked {
					//	logs.Info("数据库获取用户禁止游戏配置 userId=%d 禁用厂商=%v", userId, blocked)
					m["state"] = 2
					break
				}
			}
		}
	}
}

// ProcessUserBlockedBrands 处理用户禁用厂商，修改GameSortEx中被禁用厂商的state状态
func ProcessUserBlockedBrands(gameSortExStr string, userId int) string {
	//logs.Info("processUserBlockedBrands: 开始处理用户禁用厂商 userId=%d", userId)

	// 获取用户被禁用的厂商列表（按类型分组）
	blockedTypeBrands, err := GetBlockedTypeBrands(userId)
	if err != nil {
		logs.Error("processUserBlockedBrands: 获取用户禁用厂商失败 userId=%d err=%v", userId, err)
		return gameSortExStr
	}

	if len(blockedTypeBrands) == 0 {
		//logs.Info("processUserBlockedBrands: 用户没有禁用厂商配置 userId=%d", userId)
		return gameSortExStr
	}

	// 解析GameSortEx
	GameSortEx := map[string]interface{}{}
	err = json.Unmarshal([]byte(gameSortExStr), &GameSortEx)
	if err != nil {
		logs.Error("processUserBlockedBrands: 解析GameSortEx失败 userId=%d err=%v", userId, err)
		return gameSortExStr
	}

	//logs.Info("processUserBlockedBrands: 禁用配置 userId=%d blockedTypeBrands=%+v", userId, blockedTypeBrands)

	// 遍历GameSortEx，修改被禁用厂商的状态
	for frontType, gameList := range GameSortEx {
		// 获取对应的后端类型
		backendType, exists := catMap[frontType]
		if !exists {
			continue
		}

		// 获取该类型下被禁用的厂商列表
		blockedBrands, hasBlocked := blockedTypeBrands[fmt.Sprintf("%d", backendType)]
		if !hasBlocked || len(blockedBrands) == 0 {
			continue
		}

		// 创建禁用厂商的快速查找map
		blockedMap := make(map[string]bool)
		for _, brand := range blockedBrands {
			blockedMap[brand] = true
		}

		// 遍历该类型下的游戏厂商
		if gameArray, ok := gameList.([]interface{}); ok {
			for _, gameItem := range gameArray {
				if gameMap, ok := gameItem.(map[string]interface{}); ok {
					// 获取厂商ID
					brandId := fmt.Sprintf("%v", gameMap["id"])

					// 检查是否被禁用
					if blockedMap[brandId] {
						gameMap["state"] = 2 // 设置为禁用状态
						logs.Info("processUserBlockedBrands:  设置厂商为禁用状态 userId=%d frontType=%s brandId=%s", userId, frontType, brandId)
					}
				}
			}
		}
	}

	// 将修改后的GameSortEx转回JSON字符串
	modifiedBytes, err := json.Marshal(GameSortEx)
	if err != nil {
		logs.Error("processUserBlockedBrands: 序列化修改后的GameSortEx失败 userId=%d err=%v", userId, err)
		return gameSortExStr
	}

	//logs.Info("processUserBlockedBrands: 处理完成 userId=%d", userId)
	return string(modifiedBytes)
}
