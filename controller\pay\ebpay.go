package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"strings"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

func (c *PayController) InitEbpay() {
	server.Http().PostNoAuth("/api/ebpaycheck", c.ebpaycheck)
	server.Http().PostNoAuth("/api/ebpayrecharge", c.ebpayrecharge)
	server.Http().PostNoAuth("/api/ebpaywithward", c.ebpaywithward)
}

func (c *PayController) ebpaycheck(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Debug("ebpaycheck:", string(body))
	parsedValues, err := url.ParseQuery(string(body))
	if err != nil {
		panic(err)
	}

	jparam := make(map[string]string)
	for key, values := range parsedValues {
		jparam[key] = strings.Join(values, ",")
	}

	paymethod, err := server.XDb().Table("x_finance_method").Where("Brand = ? and Name = ?", "ebpay", "ebpay").First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		logs.Error("ebpaycheck:", err)
		return
	}
	if paymethod == nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "支付方式不存在",
		})
		logs.Error("ebpaycheck:", "支付方式不存在")
		return
	}

	orderid := jparam["merchantOrderId"]
	orderdata, err := server.XDb().Table("x_withdraw").Where("Id = ?", orderid).First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		logs.Error("ebpaycheck:", err)
		return
	}
	if orderdata == nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单不存在",
		})
		logs.Error("ebpaycheck:", err)
		return
	}
	if orderdata.Float64("RealAmount")-xgo.ToFloat(jparam["payAmount"]) > 0.01 {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "金额错误",
		})
		logs.Error("ebpaycheck:", "金额错误")
		return
	}
	logs.Debug("ebpaycheck 通过:", orderid)
	ctx.Gin().JSON(200, gin.H{
		"code": 200,
		"msg":  "该订单存在,验证通过",
	})
}

func (c *PayController) ebpayrecharge(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	fmt.Println("ebpayrecharge:", string(body))
	values, err := url.ParseQuery(string(body))
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  err.Error(),
		})
		return
	}
	PayAmount := xgo.ToFloat(values.Get("virtualPayAmount"))
	merchantOrderId := values.Get("merchantOrderId")
	orderdata, err := server.XDb().Table("x_recharge").Where("Id = ?", merchantOrderId).First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  err.Error(),
		})
		return
	}
	if orderdata == nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "订单不正确",
		})
		return
	}
	userid := orderdata.Int("UserId")

	// 获取用户信息用于通知
	// userdata, err := c.getUser(int(userid))
	// if err != nil {
	// 	logs.Error("获取用户信息失败:", err)
	// }

	if orderdata.Int("State") != 3 {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "订单已处理",
		})
		return
	}

	amount := orderdata.Float64("Amount")
	if amount-PayAmount > 0.01 {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "金额不正确",
		})
		return
	}
	paymethod, err := server.XDb().Table("x_finance_method").Where("Brand = ? and Name = ?", "ebpay", "ebpay").First()
	if err != nil {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  err.Error(),
		})
		return
	}
	if paymethod == nil {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "支付方式不存在",
		})
		return
	}
	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)

	strparam := fmt.Sprintf("merchantNo=%v&merchantOrderId=%v", jcfg["merchantCode"], merchantOrderId)

	sign := xgo.Md5(strparam + "&key=" + jcfg["md5Key"].(string))

	strparam += fmt.Sprintf("&sign=%v", sign)
	verify, err := c.ebpay_http_post(jcfg, "/api/merchant/getDepositOrder/v2", strparam)
	if err != nil {
		// c.PushCustomerIO(fmt.Sprint(userid), map[string]interface{}{
		// 	"email":    userdata.Email,
		// 	"realname": userdata.RealName,
		// 	"symbol":   orderdata.String("Symbol"),
		// 	"amount":   orderdata.Float64("Amount"),
		// 	"orderid":  merchantOrderId,
		// 	"message":  fmt.Sprintf("ebpay deposit_error: failed to verify order: %v", err),
		// },
		// 	"deposit_error", // 事件名称
		// 	map[string]interface{}{
		// 		"action": "deposit", //行为
		// 		"status": "failed",
		// 	})
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  err.Error(),
		})
		return
	}
	if verify == nil {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "查询订单失败",
		})
		return
	}
	orderStatus := xgo.ToInt((*verify)["orderStatus"])
	paidAmount := xgo.ToFloat((*verify)["paidAmount"])
	if paidAmount-PayAmount > 0.0001 {

		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "金额不正确",
		})
		return
	}
	if orderStatus == 0 {
		return
	}
	if orderStatus == 1 {
		c.rechargeCallbackHandel(userid, int(xgo.ToInt(merchantOrderId)), 5)

	} else {
		c.rechargeCallbackHandel(userid, int(xgo.ToInt(merchantOrderId)), 7)
	}
	ctx.Gin().JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}

func (c *PayController) ebpaywithward(ctx *abugo.AbuHttpContent) {
	body, _ := io.ReadAll(ctx.Gin().Request.Body)
	logs.Debug("ebpaywithward:", string(body))
	parsedValues, err := url.ParseQuery(string(body))
	if err != nil {
		panic(err)
	}

	jparam := make(map[string]string)
	for key, values := range parsedValues {
		jparam[key] = strings.Join(values, ",")
	}

	merchantOrderId := jparam["merchantOrderId"]
	orderdata, err := server.XDb().Table("x_withdraw").Where("Id = ?", merchantOrderId).First()
	if err != nil {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,查询订单失败:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	if orderdata == nil {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,订单不存在")
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单不存在",
		})
		return
	}
	if orderdata.Int("State") != 5 {
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "订单已处理",
		})
		return
	}
	if orderdata.Float64("RealAmount")-xgo.ToFloat(jparam["orderAmount"]) > 0.01 {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,金额错误")
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "金额错误",
		})
		return
	}
	paymethod, err := server.XDb().Table("x_finance_method").Where("Brand = ? and Name = ?", "ebpay", "ebpay").First()
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  err.Error(),
		})
		return
	}
	if paymethod == nil {
		ctx.Gin().JSON(200, gin.H{
			"code": 1,
			"msg":  "支付方式不存在",
		})
		return
	}

	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)

	strparam := fmt.Sprintf("merchantNo=%v&merchantOrderId=%v", jcfg["merchantCode"], merchantOrderId)

	sign := xgo.Md5(strparam + "&key=" + jcfg["md5Key"].(string))

	strparam += fmt.Sprintf("&sign=%v", sign)
	verify, err := c.ebpay_http_post(jcfg, "/api/merchant/getWithdrawOrder/v2", strparam)

	if err != nil {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,查询订单失败:", err)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	if verify == nil {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,查询订单失败")
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "查询订单失败",
		})
		return
	}
	b, _ := json.Marshal(verify)
	logs.Debug("ebpaywithward verify:", string(b))
	orderStatus := xgo.ToInt((*verify)["orderStatus"])
	if orderStatus == 0 {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败,回调状态不正确:%v", orderStatus)
		ctx.Gin().JSON(200, gin.H{
			"code": 500,
			"msg":  "回调状态不正确",
		})
		return
	}
	if orderStatus == 1 {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 6)
	} else {
		c.withdrawCallbackHandel(int(xgo.ToInt(merchantOrderId)), 7)
		logs.Error("ebpay提现失败")
	}
	ctx.Gin().JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
	})
}

func (c *PayController) ebpay_http_post(jcfg map[string]interface{}, path string, param string) (*map[string]interface{}, error) {
	url := jcfg["api"].(string) + path
	header := req.Header{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	resp, err := req.Post(url, header, strings.NewReader(param))
	if err != nil {
		logs.Error("ebpay_post:", err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)

	logs.Debug("ebpay_http_post:", url, "|", param, "|", string(body))

	if err != nil {
		logs.Error("ebpay_post:", err)
		return nil, err
	}
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("ebpay_post:", err)
		return nil, err
	}
	code := abugo.GetInt64FromInterface(jdata["code"])
	if code != 200 {
		return nil, errors.New("失败")
	}
	td := jdata["data"].(map[string]interface{})
	return &td, nil
}

func (c *PayController) ebpay_create_recharge_order(ctx *abugo.AbuHttpContent, jcfg map[string]interface{}, rate float64, userdata *xgo.XMap, SpecialAgent int, token *server.TokenData, reqdata *CreateOrderReq) {
	logs.Debug("ebpay recharge")
	errcode := 0

	// 获取支付方式信息用于币种验证
	payMethod, err := c.getPayMethod(reqdata.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(reqdata.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var currentRate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		currentRate = 0                  // 运营商ID为26时，汇率设为0
		amount = float64(reqdata.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		currentRate, err = c.getRechargeRate(reqdata.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(reqdata.Amount) / currentRate // 运营商ID不为26时，进行汇率转换
		rate = currentRate
	}
	OrderId, err := server.Db().Table("x_recharge").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      4,
		"Amount":       reqdata.Amount,
		"RealAmount":   amount,
		"TransferRate": rate,
		"State":        3,
		"CSGroup":      userdata.Int("CSGroup"),
		"CSId":         userdata.Int("CSId"),
		"SpecialAgent": SpecialAgent,
		"TopAgentId":   userdata.Int("TopAgentId"),
		"OrderType":    reqdata.OrderType,
	})
	if err != nil {
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	ebpayreq := map[string]interface{}{
		"merchantNo":       xgo.ToInt(jcfg["merchantCode"]),
		"userName":         fmt.Sprint(token.UserId),
		"deviceType":       7,
		"merchantOrderId":  fmt.Sprint(OrderId),
		"depositNotifyUrl": jcfg["notify"].(string) + "/api/ebpayrecharge",
		"payAmount":        reqdata.Amount,
		"payTypeId":        2182,
		"loginIp":          ctx.GetIp(),
		"virtualProtocol":  0,
	}
	strparam := fmt.Sprintf("merchantNo=%v&merchantOrderId=%v&userName=%v&deviceType=7", xgo.ToInt(jcfg["merchantCode"]), fmt.Sprint(OrderId),
		fmt.Sprint(token.UserId))
	sign := xgo.Md5(strparam + "&key=" + jcfg["md5Key"].(string))
	strparam = ""
	for k, v := range ebpayreq {
		strparam += fmt.Sprintf("%v=%v&", k, v)
	}
	strparam += fmt.Sprintf("sign=%v", sign)
	data, err := c.ebpay_http_post(jcfg, "/api/merchant/deposit/v2", strparam)
	if err != nil {
		ctx.RespErr(errors.New("请求失败"), &errcode)
		return
	}
	// 添加 IsRechargeActive 到 PayData
	(*data)["IsRechargeActive"] = reqdata.IsRechargeActive
	bytes, _ := json.Marshal(data)
	server.XDb().Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
		"PayData": string(bytes),
		"ThirdId": (*data)["orderId"],
	})
	ctx.RespOK(xgo.H{
		"payurl": (*data)["url"],
	})
}
