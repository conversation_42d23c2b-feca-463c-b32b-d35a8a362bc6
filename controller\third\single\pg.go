package single

import (
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/shopspring/decimal"
	"github.com/zhms/xgo/xgo"
)

type PGConfig struct {
	url                   string
	gameUrl               string
	operatorToken         string
	secretKey             string //Secret Key
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func NewPGLogic(params map[string]string, fc func(int) error) *PGConfig {

	return &PGConfig{
		url:                   params["url"],
		gameUrl:               params["gameurl"],
		operatorToken:         params["operator_token"],
		secretKey:             params["secret_key"],
		currency:              params["currency"],
		brandName:             "pg",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyPG = "cacheKeyPG:"

// Login
func (l *PGConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId string `validate:"required"`
		Lang   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyPG, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, l.brandName, reqdata.GameId)
	if err != nil {
		logs.Error(l.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	//sessionId
	sessionId := base.UserId2token(cacheKeyPG, token.UserId)
	ea := fmt.Sprintf("btt=1&ops=%v&l=%v", sessionId, reqdata.Lang)
	querydata := map[string]any{
		"operator_token": l.operatorToken,
		"secret_key":     l.secretKey,
		"currency":       l.currency,
		"path":           fmt.Sprintf("/%v/index.html", reqdata.GameId),
		"extra_args":     ea,
		"url_type":       "game-entry",
		"client_ip":      ctx.GetIp(),
	}
	urlreq := fmt.Sprintf("%s/external-game-launcher/api/v1/GetLaunchURLHTML?trace_id=%s", l.url, abugo.GetUuid())
	reqBytes, _ := json.Marshal(querydata)
	header := map[string]string{
		"Cache-Control": "no-cache, no-store, must-revalidate",
	}
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		RespType:   1,
		Param:      reqBytes,
		PostMethod: httpc.URL_ENCODE,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		logs.Error(" PG登录发生错误", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if data != nil {
		fmt.Println(data["data"])
		//if token.UserId == 59754374 || reqdata.GameId == "65" {
		//	logs.Info("-------------------pg游戏响应数据", " userId=", token.UserId, data["data"])
		//}
		ctx.Put("html", data["data"])
	} else {
		ctx.Put("html", "")
	}

	ctx.RespOK()
}

type verifySessionReq struct {
	OperatorToken         string `form:"operator_token" json:"operator_token"`
	SecretKey             string `form:"secret_key" json:"secret_key"`
	OperatorPlayerSession string `form:"operator_player_session" json:"operator_player_session"`
	GameId                *int   `form:"game_id" json:"game_id"`
	Ip                    string `form:"ip" json:"ip"`
	CustomParameter       string `form:"custom_parameter" json:"custom_parameter"`
	BetType               *int   `form:"bet_type" json:"bet_type"`
}

type resp struct {
	Data  any `json:"data"`
	Error any `json:"error"`
}

type errorResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// VerifySession
func (l *PGConfig) VerifySession(ctx *abugo.AbuHttpContent) {
	authResp := resp{}
	req := verifySessionReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方VerifySession req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, " 三方VerifySession req  data |  traceId ", string(reqBytes), traceId)

	//获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey || userId == -1 {
		logs.Error("PG VerifySession 验证失败", "traceId=", traceId, "userId=", userId, "tokenMatch=", req.OperatorToken == l.operatorToken, "secretMatch=", req.SecretKey == l.secretKey)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	_, _, err = base.GetUserBalance(userId, cacheKeyPG)

	if err != nil {
		logs.Error("PG VerifySession 获取用户余额失败", "traceId=", traceId, "userId=", userId, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 1200
		errorResps.Message = "内部服务器错误"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//成功返回
	type respData struct {
		PlayerName string `json:"player_name"`
		NickName   string `json:"nickname"`
		Currency   string `json:"currency"`
	}

	respdatas := respData{
		PlayerName: l.currency + "_" + fmt.Sprint(userId),
		NickName:   fmt.Sprint(userId),
		Currency:   l.currency,
	}
	authResp.Data = respdatas
	ctx.RespJson(authResp)
	return
}

type cashReq struct {
	OperatorToken         string `form:"operator_token" json:"operator_token"`
	SecretKey             string `form:"secret_key" json:"secret_key"`
	PlayerName            string `form:"player_name" json:"player_name"`
	OperatorPlayerSession string `form:"operator_player_session" json:"operator_player_session"`
	GameId                *int   `form:"game_id" json:"game_id"`
}

// Cash
func (l *PGConfig) Cash(ctx *abugo.AbuHttpContent) {
	authResp := resp{}
	req := cashReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方Cash req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方Cash req  data |  traceId ", string(reqBytes), traceId)

	//获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)

	// 验证PlayerName格式（兼容新旧格式）
	isValidPlayerName := (req.PlayerName == l.currency+"_"+fmt.Sprint(userId)) ||
		(req.PlayerName == fmt.Sprint(userId))

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey ||
		userId == -1 || !isValidPlayerName {
		logs.Error("PG Cash 验证失败", "traceId=", traceId, "userId=", userId, "playerName=", req.PlayerName, "expectedNew=", l.currency+"_"+fmt.Sprint(userId), "expectedOld=", fmt.Sprint(userId), "tokenMatch=", req.OperatorToken == l.operatorToken, "secretMatch=", req.SecretKey == l.secretKey)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	_, balance, err := base.GetUserBalance(userId, cacheKeyPG)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error("PG Cash 获取用户余额失败", "traceId=", traceId, "userId=", userId, "playerName=", req.PlayerName, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 3005
		errorResps.Message = "玩家钱包不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//成功返回
	type respData struct {
		CurrencyCode  string  `json:"currency_code"`
		BalanceAmount float64 `json:"balance_amount"`
		UpdatedTime   int64   `json:"updated_time"`
	}

	respdatas := respData{
		CurrencyCode:  l.currency,
		BalanceAmount: balance2,
		UpdatedTime:   time.Now().UnixMilli(),
	}
	authResp.Data = respdatas
	ctx.RespJson(authResp)
	return
}

type transferInOutReq struct {
	OperatorToken         string   `form:"operator_token" json:"operator_token"`
	SecretKey             string   `form:"secret_key" json:"secret_key"`
	OperatorPlayerSession string   `form:"operator_player_session" json:"operator_player_session"`
	PlayerName            string   `form:"player_name" json:"player_name"`
	GameId                *int     `form:"game_id" json:"game_id"`
	ParentBetId           string   `form:"parent_bet_id" json:"parent_bet_id"`
	BetId                 string   `form:"bet_id" json:"bet_id"`
	CurrencyCode          string   `form:"currency_code" json:"currency_code"`
	BetAmount             *float64 `form:"bet_amount" json:"bet_amount"`
	WinAmount             *float64 `form:"win_amount" json:"win_amount"`
	TransferAmount        *float64 `form:"transfer_amount" json:"transfer_amount"`
	TransactionId         string   `form:"transaction_id" json:"transaction_id"`
	WalletType            string   `form:"wallet_type" json:"wallet_type"`
	BetType               *int     `form:"bet_type" json:"bet_type"`
	Platform              string   `form:"platform" json:"platform"`
	CreateTime            int      `form:"create_time" json:"create_time"`
	UpdatedTime           int64    `form:"updated_time" json:"updated_time"`
	IsValidateBet         bool     `form:"is_validate_bet" json:"is_validate_bet"`
	IsAdjustment          bool     `form:"is_adjustment" json:"is_adjustment"`
	IsParentZeroStake     bool     `form:"is_parent_zero_stake" json:"is_parent_zero_stake"`
	IsFeature             bool     `form:"is_feature" json:"is_feature"`
	IsFeatureBuy          bool     `form:"is_feature_buy" json:"is_feature_buy"`
	Iswager               bool     `form:"is_wager" json:"is_wager"`
	IsEndRound            bool     `form:"is_end_round" json:"is_end_round"`
	FreeGameTransactionId string   `form:"free_game_transaction_id" json:"free_game_transaction_id"`
	FreeGameName          string   `form:"free_game_name" json:"free_game_name"`
	FreeGameId            *int     `form:"free_game_id" json:"free_game_id"`
	IsMinusCount          bool     `form:"is_minus_count" json:"is_minus_count"`
	BonusTransactionId    string   `form:"bonus_transaction_id" json:"bonus_transaction_id"`
	BonusName             string   `form:"bonus_name" json:"bonus_name"`
	BonusId               *int     `form:"bonus_id" json:"bonus_id"`
	BonusBalanceAmount    *float64 `form:"bonus_balance_amount" json:"bonus_balance_amount"`
	BonusRatioAmount      *float64 `form:"bonus_ratio_amount" json:"bonus_ratio_amount"`
}

// 3004 玩家不存在
// 3005 玩家钱包不存在
// 3021 投注不存在
// 3033 投注失败
// 3202 玩家余额不足
// TransferInOut
func (l *PGConfig) TransferInOut(ctx *abugo.AbuHttpContent) {

	authResp := resp{}
	req := transferInOutReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方TransferInOut req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "InvalidRequest"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Info(l.brandName, "三方TransferInOut req  data |  traceId ", string(reqBytes), traceId)

	type respData struct {
		CurrencyCode  string  `json:"currency_code"`
		BalanceAmount float64 `json:"balance_amount"`
		UpdatedTime   int64   `json:"updated_time"`
	}
	respDatas := respData{}

	respDatas.CurrencyCode = l.currency
	//获取玩家ID
	userId := base.Token2UserId(cacheKeyPG, req.OperatorPlayerSession)

	// 在特殊情况下，从PlayerName解析用户ID并验证格式
	var isValidPlayerName bool
	if req.IsValidateBet || req.IsAdjustment {
		parsed := base.ParseUserIdFromPlayerName(req.PlayerName)
		if parsed != 0 {
			userId = parsed
		}
		// 验证格式是否正确（同时兼容新旧格式）
		isValidPlayerName = (req.PlayerName == l.currency+"_"+fmt.Sprint(userId)) || (req.PlayerName == fmt.Sprint(userId))
	} else {
		// 正常情况：兼容新格式和旧格式
		isValidPlayerName = (req.PlayerName == l.currency+"_"+fmt.Sprint(userId)) || (req.PlayerName == fmt.Sprint(userId))
	}

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey ||
		userId == -1 || !isValidPlayerName {
		logs.Error("PG TransferInOut 验证失败", "traceId=", traceId, "userId=", userId, "playerName=", req.PlayerName, "thirdId=", req.TransactionId, "tokenMatch=", req.OperatorToken == l.operatorToken, "secretMatch=", req.SecretKey == l.secretKey, "isValidateBet=", req.IsValidateBet, "isAdjustment=", req.IsAdjustment)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "InvalidRequest"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//检查此订单是否已经成功请求过了
	key := fmt.Sprintf("cacheKeyPG:trace_id:%s", req.TransactionId)
	before := server.Redis().Get(key)
	if before != nil {
		//之前成功请求了  直接返回
		befores := string(before.([]byte)[:])
		err = base.MyJson.Unmarshal([]byte(befores), &authResp)
		if err != nil {
			logs.Error("PG TransferInOut 缓存反序列化失败", "traceId=", traceId, "thirdId=", req.TransactionId, "err=", err)
			// 清除损坏的缓存数据，继续正常流程
			server.Redis().Del(key)
		} else {
			logs.Debug("PG TransferInOut 重复请求 缓存命中", "traceId=", traceId, "thirdId=", req.TransactionId)
			if v, ok := authResp.Data.(map[string]interface{}); ok {
				v["updated_time"] = req.UpdatedTime
				authResp.Data = v
			}
			ctx.RespJson(authResp)
			return
		}
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKeyPG)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error("PG TransferInOut 获取用户余额失败", "traceId=", traceId, "userId=", userId, "playerName=", req.PlayerName, "thirdId=", req.TransactionId, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 3005
		errorResps.Message = "玩家钱包不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//三方来源的数据整理
	var (
		thirdId   = req.BetId
		gamecode  = req.GameId
		thirdTime = time.Now()
	)

	//if *req.TransferAmount == 0 && *req.WinAmount == 0 && *req.BetAmount == 0 {
	//	//不处理这种情况
	//	respDatas.UpdatedTime = time.Now().UnixMilli()
	//	respDatas.BalanceAmount = balance2
	//	authResp.Data = respDatas
	//	ctx.RespJson(authResp)
	//	return
	//}
	k, _ := decimal.NewFromFloat(*req.WinAmount).Sub(decimal.NewFromFloat(*req.BetAmount)).Float64()
	if *req.TransferAmount != k {
		logs.Error("PG TransferInOut 转账金额验证失败", "traceId=", traceId, "userId=", userId, "thirdId=", req.TransactionId, "transferAmount=", *req.TransferAmount, "calculated=", k, "betAmount=", *req.BetAmount, "winAmount=", *req.WinAmount)
		errorResps := errorResp{}
		errorResps.Code = 3073
		errorResps.Message = "BetFailedException"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	//是投注交易  投注金币变化记录
	var (
		betAmount = *req.BetAmount
	)
	if betAmount > balance2 {
		logs.Error("PG TransferInOut 玩家余额不足", "traceId=", traceId, "userId=", userId, "thirdId=", req.TransactionId, "betAmount=", betAmount, "balance=", balance2)
		errorResps := errorResp{}
		errorResps.Code = 3202
		errorResps.Message = "玩家余额不足"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//查询订单是否存在
	betTran, isExist := base.OrderIsExist(thirdId, l.brandName, "x_third_dianzhi_pre_order")
	if isExist {
		respDatas.UpdatedTime = req.UpdatedTime
		respDatas.BalanceAmount = balance2
		authResp.Data = respDatas
		//成功的保存起来
		authRespstr, _ := json.Marshal(authResp)
		err = server.Redis().SetString(key, string(authRespstr))
		if err != nil {
			logs.Error("PG 保存到redis错误 ", err)
		}
		logs.Error("PG 重复请求", " TransactionId=", req.TransactionId, " thirdId=", req.BetId, " rsp= ", authResp)
		ctx.RespJson(authResp)
		return
	}

	// 获取用户渠道ID
	ChannelId := base.GetUserChannelIdFromMap(ctx, udata, userId)

	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name,GameType").Where("GameId = ? and Brand=?", gamecode, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("获取游戏列表失败 GameId=", gamecode, " userId=", userId, " err=", err)
		} else {
			gameName = gameList.Name
		}
	}

	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		if betAmount > 0 {
			ressql, err2 := tx.Tx.Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmount, userId, betAmount)
			row, err3 := ressql.RowsAffected()
			if err2 != nil || err3 != nil || row < 1 {
				logs.Error("pg 下注 修改x_user失败了:  id = ", thirdId, err2, err3)
				errorResps := errorResp{}
				errorResps.Code = 3062
				errorResps.Message = "事务回滚异常"
				authResp.Error = errorResps
				ctx.RespJson(authResp)
				return err
			}
			balance2, _ = decimal.NewFromFloat(balance2).Sub(decimal.NewFromFloat(betAmount)).Float64()
		}
		if betTran != nil {
			betId := abugo.GetInt64FromInterface((*betTran)["Id"])
			_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmount, betId)
			if err != nil {
				logs.Error("PG 下注 修改x_third_dianzhi_pre_order修改失败了:  id = ", thirdId, err)
				errorResps := errorResp{}
				errorResps.Code = 3062
				errorResps.Message = "事务回滚异常"
				authResp.Error = errorResps
				ctx.RespJson(authResp)
				return err
			}
		} else {
			order := xgo.H{
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
				"BetChannelId": ChannelId,
				"UserId":       userId,
				"Brand":        l.brandName,
				"ThirdId":      thirdId,
				"GameId":       gamecode,
				"GameName":     gamecode,
				"BetAmount":    betAmount,
				"WinAmount":    0,
				"ValidBet":     0,
				"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
				"Currency":     l.currency,
				"RawData":      string(reqBytes),
				"DataState":    -1,
			}
			_, err = tx.Table("x_third_dianzhi_pre_order").Insert(order)
			if err != nil {
				logs.Error("PG 下注 修改x_third_dianzhi_pre_order新增失败了:  id = ", thirdId, err)
				errorResps := errorResp{}
				errorResps.Code = 3062
				errorResps.Message = "事务回滚异常"
				authResp.Error = errorResps
				ctx.RespJson(authResp)
				return err
			}
		}
		if betAmount > 0 {
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance2 + betAmount,
				"Amount":       0 - betAmount,
				"AfterAmount":  balance2,
				"Reason":       utils.BalanceCReasonPGBet,
				"Memo":         "PG bet,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Error("PG 下注 新增x_amount_change_log失败了:  id = ", thirdId, err)
				errorResps := errorResp{}
				errorResps.Code = 3062
				errorResps.Message = "事务回滚异常"
				authResp.Error = errorResps
				ctx.RespJson(authResp)
				return err
			}
		}
		return nil
	})

	var (
		amount = *req.WinAmount
	)

	//结算
	//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "=", l.brandName, nil)
	where.Add("and", "DataState", "=", -1, nil)
	betTran, err = server.Db().Table("x_third_dianzhi_pre_order").Where(where).GetOne()
	if err != nil || betTran == nil {
		logs.Error("PG TransferInOut 投注订单不存在", "traceId=", traceId, "userId=", userId, "thirdId=", thirdId, "brand=", l.brandName, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 3021
		errorResps.Message = "投注不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	logs.Info("PG 订单数据：", *betTran)

	// 所有电子的有效流水取不大于下注金额的输赢绝对值
	betAmount = abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
	validBet := math.Abs(amount - betAmount)
	if validBet > math.Abs(betAmount) {
		validBet = math.Abs(betAmount)
	}

	//开启事务
	err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
		//将下注订单移动至结算订单表
		//修改成已经结算了
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		_, err = tx.Tx.Exec(`update x_third_dianzhi_pre_order set
		WinAmount = ?,
		ValidBet = ?,
		GameName = ?,
		RawData = ?,
		GameId = ?,
		ThirdTime = ?,
		DataState = 1
		where Id = ?`,
			amount,
			validBet,
			gameName,
			string(reqBytes),
			gamecode,
			thirdTime,
			betId,
		)
		if err != nil {
			logs.Error("PG 结算 修改成已经结算了 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, err)
			return err
		}
		//移动至统计表
		tmp := (*betTran)
		delete(tmp, "Id")
		delete(tmp, "CreateTime")
		tmp["DataState"] = 1
		tmp["WinAmount"] = amount
		tmp["ValidBet"] = validBet
		tmp["GameName"] = gameName
		tmp["BetCtx"] = fmt.Sprintf("https://public.pg-staging.com/history/redirect.html?trace_id=%s&t=%%s&psid=%s&sid=%s&lang=zh&type=operator", abugo.GetUuid(), req.ParentBetId, req.BetId)
		tmp["GameRst"] = fmt.Sprintf("https://public.pg-staging.com/history/redirect.html?trace_id=%s&t=%%s&psid=%s&sid=%s&lang=zh&type=operator", abugo.GetUuid(), req.ParentBetId, req.BetId)
		tmp["BetCtxType"] = 2
		_, err = tx.Table("x_third_dianzhi").Insert(tmp)
		if err != nil {
			logs.Error("PG 结算 移动至统计表 x_third_dianzhi_pre_order 失败了:  id = ", thirdId, err)
			return err
		}
		//处理结算
		if amount > 0 {
			//win
			_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			if err != nil {
				logs.Error("PG 结算 处理结算 x_user 失败了:  id = ", thirdId, err)
				return err
			}
			balance2, _ = decimal.NewFromFloat(balance2).Add(decimal.NewFromFloat(amount)).Float64()
			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance2 - amount,
				"Amount":       amount,
				"AfterAmount":  balance2,
				"Reason":       utils.BalanceCReasonPGSettle,
				"Memo":         "PG settle,thirdId:" + thirdId,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Error("PG 结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
				return err
			}
		}
		logs.Info("PG 結算成功", "traceId=", traceId, "userId=", userId, "balance=", balance2, "thirdId=", thirdId, "tranId=", req.TransactionId)

		return nil
	})

	// 事务完成后处理响应
	if err != nil {
		logs.Error("PG 结算事务失败", "thirdId=", thirdId, "userId=", userId, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 3062
		errorResps.Message = "事务回滚异常"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	// 事务成功，准备响应数据
	balance2 = float64(int(balance2*100)) / 100
	respDatas.UpdatedTime = req.UpdatedTime
	respDatas.BalanceAmount = balance2
	authResp.Data = respDatas

	// 保存缓存
	authRespstr, _ := json.Marshal(authResp)
	err = server.Redis().SetString(key, string(authRespstr))
	if err != nil {
		logs.Error("PG TransferInOut 保存缓存失败", "traceId=", traceId, "userId=", userId, "thirdId=", thirdId, "err=", err)
	}

	// 记录准备响应的日志
	traceId = ctx.Query("trace_id")
	logs.Info("准备响应PG", "traceId=", traceId, "userId=", userId, "balance=", balance2, "thirdId=", thirdId, "tranId=", req.TransactionId)

	// 发送HTTP响应
	ctx.RespJson(authResp)

	// 响应完成日志
	logs.Info("PG响应完成", "traceId=", traceId, "thirdId=", thirdId, "tranId=", req.TransactionId)

	// if errTa == nil {
	// 	go func() {
	// 		t := l.getPgOperatorSessionForGameRst()
	// 		if t != "" {
	// 			urlreq := fmt.Sprintf("https://public.pg-staging.com/history/redirect.html?trace_id=%s&t=%s&psid=%s&sid=%s&lang=zh&type=operator", abugo.GetUuid(), t, req.ParentBetId, req.BetId)
	// 			logs.Info("PG 获取开奖结果 urlreq=", urlreq)
	// 		}
	// 	}()
	// }

	// 推送奖励事件通知
	if l.thirdGamePush != nil {
		// 将gameName从any类型转换为string类型
		//gameNameStr, ok := gameName.(string)
		//if !ok {
		//	gameNameStr = fmt.Sprintf("%v", gameName)
		//}
		//l.thirdGamePush.PushRewardEvent(userId, gameNameStr, l.brandName, betAmount, amount, l.currency)
		//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		//推送下注事件通知
		l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency, l.brandName, thirdId, 1)
		l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PGConfig] TransferInOut 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PGConfig] TransferInOut 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type adjustmentPGReq struct {
	OperatorToken           string   `form:"operator_token" json:"operator_token"`
	SecretKey               string   `form:"secret_key" json:"secret_key"`
	PlayerName              string   `form:"player_name" json:"player_name"`
	CurrencyCode            string   `form:"currency_code" json:"currency_code"`
	TransferAmount          *float64 `form:"transfer_amount" json:"transfer_amount"`
	AdjustmentId            string   `form:"adjustment_id" json:"adjustment_id"`
	AdjustmentTransactionId string   `form:"adjustment_transaction_id" json:"adjustment_transaction_id"`
	AdjustmentTime          *int64   `form:"adjustment_time" json:"adjustment_time"`
	TransactionType         string   `form:"transaction_type" json:"transaction_type"`
	BetType                 *int     `form:"bet_type" json:"bet_type"`
	PromotionId             *int64   `form:"promotion_id" json:"promotion_id"`
	PromotionType           *int64   `form:"promotion_type" json:"promotion_type"`
	Remark                  string   `form:"remark" json:"remark"`
}

// Adjustment  此段代码未使用  未验证
func (l *PGConfig) Adjustment(ctx *abugo.AbuHttpContent) {

	authResp := resp{}
	req := adjustmentPGReq{}
	err := ctx.Gin().ShouldBind(&req)
	traceId := ctx.Query("trace_id")
	if err != nil {
		logs.Error(l.brandName, "三方Adjustment req  err  |  traceId", err, traceId)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	//记录日志
	reqBytes, _ := json.Marshal(req)
	logs.Error(l.brandName, "三方Adjustment req  data |  traceId ", string(reqBytes), traceId)

	// 解析PlayerName，使用公共方法，兼容新旧格式
	userId := base.ParseUserIdFromPlayerName(req.PlayerName)

	if req.OperatorToken != l.operatorToken || req.SecretKey != l.secretKey {
		logs.Error("PG Adjustment 验证失败", "traceId=", traceId, "playerName=", req.PlayerName, "userId=", userId, "tokenMatch=", req.OperatorToken == l.operatorToken, "secretMatch=", req.SecretKey == l.secretKey)
		errorResps := errorResp{}
		errorResps.Code = 1034
		errorResps.Message = "无效请求"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKeyPG)
	balance2 := float64(int(balance*100)) / 100
	if err != nil {
		logs.Error("PG Adjustment 获取用户余额失败", "traceId=", traceId, "playerName=", req.PlayerName, "userId=", userId, "err=", err)
		errorResps := errorResp{}
		errorResps.Code = 3005
		errorResps.Message = "玩家钱包不存在"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}

	if balance2+(*req.TransferAmount) < 0 {
		logs.Error("PG Adjustment 玩家余额不足", "traceId=", traceId, "playerName=", req.PlayerName, "userId=", userId, "balance=", balance2, "transferAmount=", *req.TransferAmount, "resultBalance=", balance2+(*req.TransferAmount))
		errorResps := errorResp{}
		errorResps.Code = 3202
		errorResps.Message = "玩家余额不足"
		authResp.Error = errorResps
		ctx.RespJson(authResp)
		return
	}
	type respData struct {
		AdjustAmount  float64 `json:"adjust_amount"`
		BalanceBefore float64 `json:"balance_before"`
		BalanceAfter  float64 `json:"balance_after"`
		UpdatedTime   int64   `json:"updated_time"`
	}
	respDatas := respData{}
	//防止重复请求
	key := cacheKeyPG + req.AdjustmentTransactionId
	if server.Redis().Get(key).(string) == "1" {
		respDatas.AdjustAmount = *req.TransferAmount
		respDatas.BalanceBefore = balance2
		respDatas.BalanceAfter = balance2
		respDatas.UpdatedTime = time.Now().UnixMilli()
		authResp.Data = respDatas
		ctx.RespJson(authResp)
		return
	}
	//开启事务
	var (
		amount  = *req.TransferAmount
		thirdId = req.AdjustmentTransactionId
	)
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		//win
		_, err = tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
		if err != nil {
			logs.Error("PG Adjustment 处理Adjustment x_user 失败了:  id = ", thirdId, err)
			errorResps := errorResp{}
			errorResps.Code = 3062
			errorResps.Message = "事务回滚异常"
			authResp.Error = errorResps
			ctx.RespJson(authResp)
			return err
		}
		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance2,
			"Amount":       amount,
			"AfterAmount":  balance2 + amount,
			"Reason":       utils.BalanceCReasonPGAdjustment,
			"Memo":         "PG Adjustment,thirdId:" + thirdId,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		_, err = tx.Table("x_amount_change_log").Insert(amountLog)
		if err != nil {
			logs.Error("PG Adjustment 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)
			errorResps := errorResp{}
			errorResps.Code = 3062
			errorResps.Message = "事务回滚异常"
			authResp.Error = errorResps
			ctx.RespJson(authResp)
			return err
		}
		//成功响应
		server.Redis().SetStringEx(key, 60*60, "1")
		respDatas.AdjustAmount = *req.TransferAmount
		respDatas.BalanceBefore = balance2
		respDatas.BalanceAfter = balance2 + amount
		respDatas.UpdatedTime = time.Now().UnixMilli()
		authResp.Data = respDatas
		ctx.RespJson(authResp)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][PGConfig] Adjustment 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][PGConfig] Adjustment 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// UpdateBetDetail 暂时不做
func (l *PGConfig) UpdateBetDetail(ctx *abugo.AbuHttpContent) {
	ctx.RespJson(1)
}

// pg获取运营商令牌
func (l *PGConfig) getPgOperatorSessionForGameRst() (t string) {
	// 获取pg令牌缓存
	cacheKeyOpSession := fmt.Sprintf("%v:%v:cache:pg:opsession", server.Project(), server.Module())
	if cacheValue := server.Redis().Get(cacheKeyOpSession); cacheValue != nil {
		t = string(cacheValue.([]byte))
		logs.Info("pg 获取运营商session缓存成功:  cacheKeyOpSession = ", cacheKeyOpSession, " t = ", t)
		return
	}

	defer func() {
		if t != "" {
			// 设置pg令牌缓存 30分钟过期 我们取15分钟短 测试先用缓存60秒 上线用缓存900秒
			if e := server.Redis().SetStringEx(cacheKeyOpSession, 60, t); e != nil {
				logs.Error("pg 设置opsession错误:  cacheKeyOpSession = ", cacheKeyOpSession, e)
			}
		}
	}()

	type ResponseData struct {
		Data struct {
			OperatorSession string `json:"operator_session"`
		} `json:"data"`
		Error string `json:"error"`
	}
	rsp := ResponseData{}

	urlreq := fmt.Sprintf("%s/external/Login/v1/LoginProxy?trace_id=%s", l.url, abugo.GetUuid())
	postData := url.Values{}
	postData.Set("operator_token", l.operatorToken)
	postData.Set("secret_key", l.secretKey)
	reqBytes := []byte(postData.Encode())

	client := &http.Client{}
	payload := strings.NewReader(string(reqBytes))
	req, _ := http.NewRequest(http.MethodPost, urlreq, payload)
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("PG 获取运营商session错误 err=", err.Error())
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("PG 获取运营商session错误2 err=", err.Error())
		return
	}
	logs.Info("PG 获取运营商session成功 respBytes=", string(respBytes))
	err = json.Unmarshal(respBytes, &rsp)
	if err != nil {
		logs.Error("PG 获取运营商session错误3 err=", err.Error())
		return
	}
	if rsp.Error != "" {
		logs.Error("PG 获取运营商session错误4 err=", rsp.Error)
		return
	}
	if rsp.Data.OperatorSession != "" {
		t = rsp.Data.OperatorSession
		logs.Info("PG 获取运营商session成功 OperatorSession=", rsp.Data.OperatorSession)
	} else {
		logs.Error("PG 获取运营商session错误5 OperatorSession=", rsp.Data.OperatorSession)
	}
	return
}
