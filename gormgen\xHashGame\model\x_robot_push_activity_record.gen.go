// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXRobotPushActivityRecord = "x_robot_push_activity_record"

// XRobotPushActivityRecord mapped from table <x_robot_push_activity_record>
type XRobotPushActivityRecord struct {
	ID                      int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:pk" json:"id"`                                // pk
	RecordTime              time.Time `gorm:"column:record_time;comment:日期" json:"record_time"`                                            // 日期
	SellerID                int32     `gorm:"column:seller_id;comment:经销商ID" json:"seller_id"`                                             // 经销商ID
	ChannelID               int32     `gorm:"column:channel_id;comment:渠道ID" json:"channel_id"`                                            // 渠道ID
	Name                    string    `gorm:"column:name;comment:机器人名" json:"name"`                                                        // 机器人名
	UserID                  int64     `gorm:"column:user_id;comment:用户ID" json:"user_id"`                                                  // 用户ID
	UserChatID              int64     `gorm:"column:user_chat_id;comment:玩家飞机号" json:"user_chat_id"`                                       // 玩家飞机号
	UserName                string    `gorm:"column:user_name;comment:玩家飞机名" json:"user_name"`                                             // 玩家飞机名
	PushMsgID               int32     `gorm:"column:push_msg_id;comment:推送的消息Id" json:"push_msg_id"`                                       // 推送的消息Id
	PushMsg                 string    `gorm:"column:push_msg;comment:推送消息名" json:"push_msg"`                                               // 推送消息名
	ClickCnt                int32     `gorm:"column:click_cnt;comment:点击次数" json:"click_cnt"`                                              // 点击次数
	IsGift                  int32     `gorm:"column:is_gift;comment:是否领取彩金" json:"is_gift"`                                                // 是否领取彩金
	RechargeCnt             int32     `gorm:"column:recharge_cnt;comment:是否充值" json:"recharge_cnt"`                                        // 是否充值
	FirstTimeRechargeAmount float64   `gorm:"column:first_time_recharge_amount;default:0.00;comment:首次" json:"first_time_recharge_amount"` // 首次
	RechargeAmount1         float64   `gorm:"column:recharge_amount_1;default:0.00;comment:第1天充值金额" json:"recharge_amount_1"`              // 第1天充值金额
	RechargeAmount2         float64   `gorm:"column:recharge_amount_2;default:0.00;comment:第2天充值金额" json:"recharge_amount_2"`              // 第2天充值金额
	RechargeAmount3         float64   `gorm:"column:recharge_amount_3;default:0.00;comment:第3天充值金额" json:"recharge_amount_3"`              // 第3天充值金额
	RechargeAmountAll       float64   `gorm:"column:recharge_amount_all;default:0.00;comment:总充值" json:"recharge_amount_all"`              // 总充值
	CreateTime              time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建" json:"create_time"`                  // 创建
	UpdateTime              time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新" json:"update_time"`                  // 更新
}

// TableName XRobotPushActivityRecord's table name
func (*XRobotPushActivityRecord) TableName() string {
	return TableNameXRobotPushActivityRecord
}
