package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var U2cpay = new(u2cpay)

type u2cpay struct {
	Base
}

// 初始化设置HTTP回调接口端点
func (c *u2cpay) Init() {
	server.Http().PostNoAuth("/api/u2cpay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/u2cpay/withdraw/callback", c.withdrawCallback)
}

// 充值
func (c *u2cpay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("payment method not found"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("user not found"), &errcode)
		return
	}

	// 计算汇率和实际金额
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(payMethod.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      23, // U2CPay 的唯一 ID
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
	}

	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		logs.Error("u2cpay: 创建订单失败", err)
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 准备 U2CPay API 的参数
	params := xgo.H{
		"merchantId":      cfg["merchant_id"],
		"merchantOrderNo": fmt.Sprintf("%d", rechargeOrder.ID),
		"amount":          fmt.Sprintf("%d", int64(rechargeOrder.Amount*100)), // 转换为分
		"payType":         payMethod.PayType,
		"currency":        payMethod.Symbol,
		"content":         "recharge",
		"clientIp":        ctx.Gin().ClientIP(),
		"callback":        fmt.Sprintf("%s/api/u2cpay/recharge/callback", cfg["callback_url"]),
		"redirect":        fmt.Sprintf("%s/api/u2cpay/redirect", cfg["callback_url"]),
	}

	// 生成签名
	params["sign"] = c.generateSign(params, cfg["api_key"].(string))

	// 发送请求
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var reqParams []string
	for _, k := range keys {
		reqParams = append(reqParams, fmt.Sprintf("%s=%v", k, params[k]))
	}
	requestBody := strings.Join(reqParams, "&")

	resp, err := c.post(cfg["api_url"].(string)+"/api/open/merchant/trade/create", map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
	}, []byte(requestBody))

	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		return
	}

	var response struct {
		Data struct {
			Amount          float64 `json:"amount"`
			OrderNo         string  `json:"orderNo"`
			PayType         string  `json:"payType"`
			MerchantId      int     `json:"merchantId"`
			Currency        string  `json:"currency"`
			PayUrl          string  `json:"payUrl"`
			MerchantOrderNo string  `json:"merchantOrderNo"`
			PayRaw          string  `json:"payRaw"`
			Status          string  `json:"status"`
		} `json:"data"`
		Success   bool   `json:"success"`
		ErrorCode string `json:"errorCode"`
	}

	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		tx.Rollback()
		logs.Error("u2cpay: 解析响应失败", err, string(resp.Body()))
		ctx.RespErr(errors.New("解析响应失败"), &errcode)
		return
	}

	if !response.Success || response.ErrorCode != "SUCCESS" {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	tx.Commit()

	// 保存 PayData 字段 - 包含支付相关信息
	payData := xgo.H{
		"PayId":            req.MethodId,
		"Brand":            "u2cpay",
		"Name":             "u2cpay",
		"OrderNo":          response.Data.OrderNo,
		"MerchantOrderNo":  response.Data.MerchantOrderNo,
		"Amount":           response.Data.Amount,
		"Currency":         response.Data.Currency,
		"PayType":          response.Data.PayType,
		"PayUrl":           response.Data.PayUrl,
		"PayRaw":           response.Data.PayRaw,
		"Status":           response.Data.Status,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 更新三方订单号和PayData
	_, err = server.DaoxHashGame().XRecharge.WithContext(ctx.Gin()).
		Where(server.DaoxHashGame().XRecharge.ID.Eq(rechargeOrder.ID)).
		Updates(map[string]interface{}{
			"PayData": string(payDataBytes),
			"ThirdId": response.Data.OrderNo,
		})
	if err != nil {
		logs.Error("u2cpay: 更新订单PayData失败", err)
		ctx.RespErr(errors.New("更新订单失败"), &errcode)
		return
	}

	ctx.RespOK(xgo.H{
		"payurl": response.Data.PayUrl,
	})
}

// 生成签名
func (c *u2cpay) generateSign(params xgo.H, secret string) string {
	// 1. 获取所有非空参数并转为字符串
	paramMap := make(map[string]string)
	for k, v := range params {
		if k != "sign" && v != "" && v != nil {
			paramMap[k] = fmt.Sprint(v)
		}
	}

	// 2. 获取所有键并按ASCII码排序
	var keys []string
	for k := range paramMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 3. 按排序后的顺序拼接参数
	var signParts []string
	for _, k := range keys {
		signParts = append(signParts, fmt.Sprintf("%s=%s", k, paramMap[k]))
	}

	// 4. 拼接密钥
	signStr := strings.Join(signParts, "&") + "&secret=" + secret

	// 5. MD5签名(32位小写)
	return strings.ToLower(c.md5(signStr))
}

// 验证签名
func (c *u2cpay) verifySign(params xgo.H, sign string, secret string) bool {
	calculatedSign := c.generateSign(params, secret)
	return calculatedSign == sign
}

// 充值回调处理
func (c *u2cpay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 解析表单数据
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("u2cpay: 解析表单数据失败", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	form := ctx.Gin().Request.PostForm // 使用 PostForm 获取 POST 表单数据
	logs.Info("u2cpay: 充值回调原始数据", form)

	callback := struct {
		MerchantId      string  // 商户ID
		MerchantOrderNo string  // 商户订单号
		OrderNo         string  // 平台订单号
		Amount          float64 // 金额(单位:分)
		Status          string  // 订单状态 PAID
		Currency        string  // 币种
		PayType         string  // 代收产品编码
		Sign            string  // 签名
	}{
		MerchantId:      form.Get("merchantId"),
		MerchantOrderNo: form.Get("merchantOrderNo"),
		OrderNo:         form.Get("orderNo"),
		Status:          form.Get("status"),
		Currency:        form.Get("currency"),
		PayType:         form.Get("payType"),
		Sign:            form.Get("sign"),
	}

	// 转换金额
	amount, err := strconv.ParseFloat(form.Get("amount"), 64)
	if err == nil {
		callback.Amount = amount
	}

	logs.Info("u2cpay: 充值回调数据", callback)

	orderId, _ := strconv.Atoi(callback.MerchantOrderNo)
	order, err := c.getRechargeOrder(orderId)
	if err != nil {
		logs.Error("u2cpay: 订单不存在", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 获取支付配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		logs.Error("u2cpay: 获取支付配置失败", err)
		ctx.Gin().String(400, "获取支付配置失败")
		return
	}

	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 验证签名
	params := xgo.H{
		"merchantId":      callback.MerchantId,
		"merchantOrderNo": callback.MerchantOrderNo,
		"orderNo":         callback.OrderNo,
		"amount":          form.Get("amount"),
		"status":          callback.Status,
		"currency":        callback.Currency,
		"payType":         callback.PayType,
	}

	if !c.verifySign(params, callback.Sign, cfg["api_key"].(string)) {
		logs.Error("u2cpay: 签名验证失败")
		ctx.Gin().String(400, "签名验证失败")
		return
	}

	// 检查订单状态
	if callback.Status != "PAID" {
		logs.Warn("u2cpay: 订单状态不成功", callback.Status)
		ctx.Gin().String(200, "订单状态不成功,回调状态:"+callback.Status)
		return
	}

	// 更新三方订单号
	if err = c.updateThirdOrder(order.ID, callback.OrderNo); err != nil {
		logs.Error("u2cpay: 更新三方订单失败", err)
		ctx.Gin().String(400, "更新三方订单失败")
		return
	}

	// 处理充值成功
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)

	ctx.Gin().String(200, "success")
}

// 提现回调处理
func (c *u2cpay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 解析表单数据
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("u2cpay: 解析表单数据失败", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	form := ctx.Gin().Request.PostForm // 使用 PostForm 获取 POST 表单数据
	logs.Info("u2cpay: 提现回调原始数据", form)

	callback := struct {
		MerchantId      string  // 商户ID
		MerchantOrderNo string  // 商户订单号
		OrderNo         string  // 平台订单号
		Amount          float64 // 金额(单位:分)
		Status          string  // 订单状态
		Currency        string  // 币种
		ErrorMsg        string  // 失败时返回原因
		Sign            string  // 签名
	}{
		MerchantId:      form.Get("merchantId"),
		MerchantOrderNo: form.Get("merchantOrderNo"),
		OrderNo:         form.Get("orderNo"),
		Status:          form.Get("status"),
		Currency:        form.Get("currency"),
		ErrorMsg:        form.Get("errorMsg"),
		Sign:            form.Get("sign"),
	}

	// 转换金额
	amount, err := strconv.ParseFloat(form.Get("amount"), 64)
	if err == nil {
		callback.Amount = amount
	}

	logs.Info("u2cpay: 提现回调数据", callback)

	orderId, _ := strconv.Atoi(callback.MerchantOrderNo)
	order, err := c.getWithdrawOrder(orderId)
	if err != nil {
		logs.Error("u2cpay: 订单不存在", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 修改金额验证逻辑
	// 根据回调金额的单位进行适当的转换
	// 有些支付渠道可能直接返回元为单位的金额，而不是分
	var convertedAmount float64
	if callback.Amount > order.Amount*10 {
		// 如果回调金额比订单金额大很多，可能是以分为单位
		convertedAmount = callback.Amount / 100
	} else {
		// 否则可能已经是以元为单位
		convertedAmount = callback.Amount
	}

	if math.Abs(order.Amount-convertedAmount) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("u2cpay: 金额不匹配", "订单金额:", order.Amount, "回调金额(转换后):", convertedAmount, "原始回调金额:", callback.Amount)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 获取支付配置
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("u2cpay: 获取支付配置失败", err)
		ctx.Gin().String(400, "获取支付配置失败")
		return
	}

	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 验证签名
	params := xgo.H{
		"merchantId":      callback.MerchantId,
		"merchantOrderNo": callback.MerchantOrderNo,
		"orderNo":         callback.OrderNo,
		"amount":          form.Get("amount"),
		"status":          callback.Status,
		"currency":        callback.Currency,
	}
	// 如果有错误信息，添加到签名参数中
	if callback.ErrorMsg != "" {
		params["errorMsg"] = callback.ErrorMsg
	}

	if !c.verifySign(params, callback.Sign, cfg["api_key"].(string)) {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("u2cpay: 签名验证失败")
		ctx.Gin().String(400, "签名验证失败")
		return
	}
	var respMsg string

	// 根据状态处理回调
	switch callback.Status {
	case "PAID": // 支付成功
		respMsg = "success"
		logs.Info("u2cpay: 提现成功")
		c.withdrawCallbackHandel(int(order.ID), 6)
		ctx.Gin().String(200, respMsg)
		return
	case "PAY_FAILED": // 支付失败
		respMsg = "success"
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, respMsg)
		return
	case "REFUND": // 已退款
		respMsg = "success"
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, respMsg)
		return
	case "WAITING_PAY": // 待支付
		respMsg = "waiting pay"
		logs.Info("u2cpay: 提现待支付")
		ctx.Gin().String(200, respMsg)
		return

	case "PAYING": // 支付中
		respMsg = "paying"
		logs.Info("u2cpay: 提现支付中")
		ctx.Gin().String(200, respMsg)
		return

	default:
		// 未知状态，视为失败
		respMsg = "success"
		c.withdrawCallbackHandel(int(order.ID), 7)
		ctx.Gin().String(200, respMsg)
		return
	}
}
