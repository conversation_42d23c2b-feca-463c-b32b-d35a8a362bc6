// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdTexa(db *gorm.DB, opts ...gen.DOOption) xThirdTexa {
	_xThirdTexa := xThirdTexa{}

	_xThirdTexa.xThirdTexaDo.UseDB(db, opts...)
	_xThirdTexa.xThirdTexaDo.UseModel(&model.XThirdTexa{})

	tableName := _xThirdTexa.xThirdTexaDo.TableName()
	_xThirdTexa.ALL = field.NewAsterisk(tableName)
	_xThirdTexa.ID = field.NewInt64(tableName, "Id")
	_xThirdTexa.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdTexa.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdTexa.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdTexa.UserID = field.NewInt32(tableName, "UserId")
	_xThirdTexa.Brand = field.NewString(tableName, "Brand")
	_xThirdTexa.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdTexa.GameID = field.NewString(tableName, "GameId")
	_xThirdTexa.GameName = field.NewString(tableName, "GameName")
	_xThirdTexa.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdTexa.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdTexa.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdTexa.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdTexa.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdTexa.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdTexa.Currency = field.NewString(tableName, "Currency")
	_xThirdTexa.RawData = field.NewString(tableName, "RawData")
	_xThirdTexa.State = field.NewInt32(tableName, "State")
	_xThirdTexa.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdTexa.DataState = field.NewInt32(tableName, "DataState")
	_xThirdTexa.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdTexa.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdTexa.CSID = field.NewString(tableName, "CSId")
	_xThirdTexa.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdTexa.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdTexa.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdTexa.GameRst = field.NewString(tableName, "GameRst")
	_xThirdTexa.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdTexa.IP = field.NewString(tableName, "Ip")
	_xThirdTexa.Lang = field.NewString(tableName, "Lang")
	_xThirdTexa.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdTexa.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdTexa.BetType = field.NewInt32(tableName, "BetType")
	_xThirdTexa.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdTexa.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")
	_xThirdTexa.ThirdRefID = field.NewString(tableName, "ThirdRefId")

	_xThirdTexa.fillFieldMap()

	return _xThirdTexa
}

type xThirdTexa struct {
	xThirdTexaDo xThirdTexaDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	GameID         field.String  // 游戏Id
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额
	ThirdRefID     field.String  // 三方备用注单号

	fieldMap map[string]field.Expr
}

func (x xThirdTexa) Table(newTableName string) *xThirdTexa {
	x.xThirdTexaDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdTexa) As(alias string) *xThirdTexa {
	x.xThirdTexaDo.DO = *(x.xThirdTexaDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdTexa) updateTableName(table string) *xThirdTexa {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")

	x.fillFieldMap()

	return x
}

func (x *xThirdTexa) WithContext(ctx context.Context) *xThirdTexaDo {
	return x.xThirdTexaDo.WithContext(ctx)
}

func (x xThirdTexa) TableName() string { return x.xThirdTexaDo.TableName() }

func (x xThirdTexa) Alias() string { return x.xThirdTexaDo.Alias() }

func (x xThirdTexa) Columns(cols ...field.Expr) gen.Columns { return x.xThirdTexaDo.Columns(cols...) }

func (x *xThirdTexa) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdTexa) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
}

func (x xThirdTexa) clone(db *gorm.DB) xThirdTexa {
	x.xThirdTexaDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdTexa) replaceDB(db *gorm.DB) xThirdTexa {
	x.xThirdTexaDo.ReplaceDB(db)
	return x
}

type xThirdTexaDo struct{ gen.DO }

func (x xThirdTexaDo) Debug() *xThirdTexaDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdTexaDo) WithContext(ctx context.Context) *xThirdTexaDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdTexaDo) ReadDB() *xThirdTexaDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdTexaDo) WriteDB() *xThirdTexaDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdTexaDo) Session(config *gorm.Session) *xThirdTexaDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdTexaDo) Clauses(conds ...clause.Expression) *xThirdTexaDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdTexaDo) Returning(value interface{}, columns ...string) *xThirdTexaDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdTexaDo) Not(conds ...gen.Condition) *xThirdTexaDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdTexaDo) Or(conds ...gen.Condition) *xThirdTexaDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdTexaDo) Select(conds ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdTexaDo) Where(conds ...gen.Condition) *xThirdTexaDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdTexaDo) Order(conds ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdTexaDo) Distinct(cols ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdTexaDo) Omit(cols ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdTexaDo) Join(table schema.Tabler, on ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdTexaDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdTexaDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdTexaDo) Group(cols ...field.Expr) *xThirdTexaDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdTexaDo) Having(conds ...gen.Condition) *xThirdTexaDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdTexaDo) Limit(limit int) *xThirdTexaDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdTexaDo) Offset(offset int) *xThirdTexaDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdTexaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdTexaDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdTexaDo) Unscoped() *xThirdTexaDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdTexaDo) Create(values ...*model.XThirdTexa) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdTexaDo) CreateInBatches(values []*model.XThirdTexa, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdTexaDo) Save(values ...*model.XThirdTexa) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdTexaDo) First() (*model.XThirdTexa, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdTexa), nil
	}
}

func (x xThirdTexaDo) Take() (*model.XThirdTexa, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdTexa), nil
	}
}

func (x xThirdTexaDo) Last() (*model.XThirdTexa, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdTexa), nil
	}
}

func (x xThirdTexaDo) Find() ([]*model.XThirdTexa, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdTexa), err
}

func (x xThirdTexaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdTexa, err error) {
	buf := make([]*model.XThirdTexa, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdTexaDo) FindInBatches(result *[]*model.XThirdTexa, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdTexaDo) Attrs(attrs ...field.AssignExpr) *xThirdTexaDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdTexaDo) Assign(attrs ...field.AssignExpr) *xThirdTexaDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdTexaDo) Joins(fields ...field.RelationField) *xThirdTexaDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdTexaDo) Preload(fields ...field.RelationField) *xThirdTexaDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdTexaDo) FirstOrInit() (*model.XThirdTexa, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdTexa), nil
	}
}

func (x xThirdTexaDo) FirstOrCreate() (*model.XThirdTexa, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdTexa), nil
	}
}

func (x xThirdTexaDo) FindByPage(offset int, limit int) (result []*model.XThirdTexa, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdTexaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdTexaDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdTexaDo) Delete(models ...*model.XThirdTexa) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdTexaDo) withDO(do gen.Dao) *xThirdTexaDo {
	x.DO = *do.(*gen.DO)
	return x
}
