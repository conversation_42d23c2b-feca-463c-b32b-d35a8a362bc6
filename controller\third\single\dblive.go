package single

import (
	"bytes"
	"crypto/aes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// DB真人单一钱包类
// DB真人接口入参和响应结果，对于时间和日期部分，采用GMT+8北京时区。

type DBLiveSingleService struct {
	apiUrl                string          // api基础接口
	dataUrl               string          // api数据接口
	merchantCode          string          // merchantCode商户号
	merchantCodeLower     string          // merchantCode商户号小写
	requestAesKey         string          // 主动请求接口的商户AES密钥
	requestMd5Key         string          // 主动请求接口的商户MD5密钥
	callbackMd5Key        string          // 回调接口签名的MD5密钥
	currency              string          // 币种
	brandName             string          // 厂商标识
	RefreshUserAmountFunc func(int) error // 余额更新回调
	thirdGamePush         *base.ThirdGamePush
}

func NewDBLiveSingleService(params map[string]string, fc func(int) error) *DBLiveSingleService {
	return &DBLiveSingleService{
		apiUrl:                params["api_url"],
		dataUrl:               params["data_url"],
		merchantCode:          params["merchant_code"],
		merchantCodeLower:     strings.ToLower(params["merchant_code"]),
		requestAesKey:         params["request_aes_key"],
		requestMd5Key:         params["request_md5_key"],
		callbackMd5Key:        params["callback_md5_key"],
		currency:              params["currency"],
		brandName:             "dblive",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyDBLive = "cacheKeyDBLive:"

// DBLive返回错误码
const (
	DBLive_Code_Success                           = "200"   // 200      成功
	DBLive_Code_Fail_User_Not_Exist               = "1000"  // 1000	    会员不存在
	DBLive_Code_Fail_User_Status_Error            = "1001"  // 1001	    验证会员状态错误
	DBLive_Code_Fail_Not_Enough_Balance           = "1002"  // 1002	    余额不足
	DBLive_Code_Fail_Can_Bet_Current              = "1003"  // 1003	    此时不接受投注
	DBLive_Code_Fail_Signature_Error              = "8000"  // 8000	    签名验证失败
	DBLive_Code_Fail_Other_Error                  = "9000"  // 9000	    其他异常
	DBLive_Code_Fail_User_Account_Error           = "30011" // 30011	会员账号有误
	DBLive_Code_Fail_API_Not_Support              = "30016" // 30016	当前钱包类型不支持接口
	DBLive_Code_Fail_Can_Not_Service              = "98888" // 98888	无法正常提供服务
	DBLive_Code_Fail_Game_Account_Not_Exist       = "20001" // 20001	游戏账号不存在
	DBLive_Code_Fail_User_Amount_Acount_Not_Exist = "30003" // 30003	玩家资金账户不存在
	DBLive_Code_Fail_User_Banned                  = "20008" // 20008	账号已停用
	DBLive_Code_Fail_Logic_Error                  = "91111" // 91111	逻辑业务异常
	DBLive_Code_Fail_Illegal_Parameter            = "90000" // 90000	参数错误
	DBLive_Code_Fail_System_Error                 = "99999" // 99999	其他系统错误
)

func (l *DBLiveSingleService) codeString2CodeInt(errCode string) int {
	if errCode == DBLive_Code_Success {
		return 0
	}
	c, err := strconv.ParseInt(errCode, 10, 64)
	if err != nil {
		return -1
	}
	return int(c)
}

func (l *DBLiveSingleService) getLoginNameFromUserId(userId int64) string {
	return fmt.Sprintf("%s_%d", l.merchantCodeLower, userId)
}

func (l *DBLiveSingleService) getUserIdFromLoginName(loginName string) (userId int64, err error) {
	userIdTmp := strings.Split(loginName, "_")
	if len(userIdTmp) != 2 {
		err = errors.New("会员账号格式错误")
		return
	}
	userId, err = strconv.ParseInt(userIdTmp[1], 10, 64)
	if err != nil {
		return
	}
	if !strings.EqualFold(loginName, l.getLoginNameFromUserId(userId)) {
		err = errors.New("会员账号格式错误")
		return
	}
	return
}

// 通过用户ID转换登录密码
func (l *DBLiveSingleService) getUserPwd(userId int) string {
	return base.MD5(fmt.Sprintf("%d", userId) + "_Hx_+-#&6")[:16]
}

// 创建账号 /api/merchant/create/v2
func (l *DBLiveSingleService) register(userId int) (err error) {
	type RequestRegisterParamsData struct {
		LoginName     string `json:"loginName"`     // 用户名 需要包括商户的前缀。平台将对传入的完整账号统一转换成小写。注意：只能包含以下特殊字符[下划线、@、#、&、*  ]
		LoginPassword string `json:"loginPassword"` // 密码 不允许的符合：`''[]./‘”“’$
		Lang          int64  `json:"lang"`          // 语言 固定设为1
		Timestamp     int64  `json:"timestamp"`     // 时间戳 当前时间，不足13位请补000
		// NickName      string `json:"nickName"`      // 昵称 可选参数 注意：昵称将会显示在游戏中，最多只显示12位。且只能是小写字符
	}
	type RequestRegisterData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}

	type ResponseRegisterData struct {
		Code    string `json:"code"`    // 返回码 200成功，其他失败
		Message string `json:"message"` // 返回信息
		Request struct {
			LoginName     string `json:"loginName"`     // 用户名 需要包括商户的前缀。平台将对传入的完整账号统一转换成小写。注意：只能包含以下特殊字符[下划线、@、#、&、*  ]
			LoginPassword string `json:"loginPassword"` // 密码 不允许的符合：`''[]./‘”“’$
			Lang          int64  `json:"lang"`          // 语言 固定设为1
			Timestamp     int64  `json:"timestamp"`     // 时间戳 当前时间，不足13位请补000
		} `json:"request"` // 请求参数
		Data struct {
			Create string `json:"create"` // 创建结果
		} `json:"data"` // 返回数据
	}

	reqdata := RequestRegisterData{
		MerchantCode: l.merchantCode,
	}
	reqParams := RequestRegisterParamsData{
		LoginName:     l.getLoginNameFromUserId(int64(userId)),
		LoginPassword: l.getUserPwd(userId),
		Lang:          1,
		Timestamp:     time.Now().UnixMilli(),
	}
	paramsBytes, _ := json.Marshal(reqParams)
	reqdata.Params, err = l.AESEncrypt([]byte(l.requestAesKey), string(paramsBytes))
	if err != nil {
		logs.Error("dblive_single 创建账号 加密请求参数错误 userId=", userId, " err=", err.Error())
		return
	}
	reqdata.Signature = l.getRequestSign(string(paramsBytes))

	reqdataBytes, _ := json.Marshal(reqdata)
	payload := bytes.NewReader(reqdataBytes)
	url := fmt.Sprintf("%s/api/merchant/create/v2", l.apiUrl)
	logs.Info("db真人请求url:", url, "\n请求参数（加密前的params）:", reqParams, "\n 请求参数reqdata:", reqdata)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("dblive_single 创建账号 请求错误 userId=", userId, " err=", err.Error())
		return
	}
	respBytes, err := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		logs.Error("dblive_single 创建账号 读取响应错误 userId=", userId, " err=", err.Error())
		return
	}
	logs.Info("dblive_single 创建账号 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respdata := ResponseRegisterData{}
	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("dblive_single 创建账号 解析响应消息体错误 userId=", userId, " err=", err.Error())
		return
	}
	if respdata.Code == "20000" { // 游戏账号已存在
		return
	}
	logs.Info("db真人/api/merchant/create/v2接口响应：", respdata)
	if respdata.Code != DBLive_Code_Success {
		logs.Error("dblive_single 创建账号 创建失败 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New(respdata.Message)
		return
	}
	if respdata.Data.Create != "success" {
		logs.Error("dblive_single 创建账号 创建失败 userId=", userId, " Data.Create=", respdata.Data.Create, " Message=", respdata.Message)
		err = errors.New("创建账号失败")
		return
	}
	return
}

// 重置游戏登陆密码 /api/merchant/resetLoginPwd/v1
func (l *DBLiveSingleService) resetUserPwd(userId int) (err error) {
	type RequestResetPwdParamsData struct {
		LoginName   string `json:"loginName"`   // 用户名 需要包括商户的前缀。平台将对传入的完整账号统一转换成小写。注意：只能包含以下特殊字符[下划线、@、#、&、*  ]
		NewPassword string `json:"newPassword"` // 密码 不允许的符合：`''[]./‘”“’$
		Timestamp   int64  `json:"timestamp"`   // 时间戳 当前时间，不足13位请补000
	}
	type RequestResetPwdData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}

	type ResponseResetPwdData struct {
		Code    string `json:"code"`    // 返回码 200成功，其他失败
		Message string `json:"message"` // 返回信息
	}

	reqdata := RequestResetPwdData{
		MerchantCode: l.merchantCode,
	}
	reqParams := RequestResetPwdParamsData{
		LoginName:   l.getLoginNameFromUserId(int64(userId)),
		NewPassword: l.getUserPwd(userId),
		Timestamp:   time.Now().UnixMilli(),
	}
	paramsBytes, _ := json.Marshal(reqParams)
	reqdata.Params, err = l.AESEncrypt([]byte(l.requestAesKey), string(paramsBytes))
	if err != nil {
		logs.Error("dblive_single 重置密码 加密请求参数错误 userId=", userId, " err=", err.Error())
		return
	}
	reqdata.Signature = l.getRequestSign(string(paramsBytes))

	reqdataBytes, _ := json.Marshal(reqdata)
	payload := bytes.NewReader(reqdataBytes)
	url := fmt.Sprintf("%s/api/merchant/resetLoginPwd/v1", l.apiUrl)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("dblive_single 重置密码 请求错误 userId=", userId, " err=", err.Error())
		return
	}
	respBytes, err := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		logs.Error("dblive_single 重置密码 读取响应错误 userId=", userId, " err=", err.Error())
		return
	}
	logs.Info("dblive_single 重置密码 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respdata := ResponseResetPwdData{}
	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("dblive_single 重置密码 解析响应消息体错误 userId=", userId, " err=", err.Error())
		return
	}
	if respdata.Code == "20001" { // 游戏账号不存在
		logs.Error("dblive_single 重置密码失败 游戏账号不存在 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New("游戏账号不存在")
		return
	}
	if respdata.Code != DBLive_Code_Success {
		logs.Error("dblive_single 重置密码失败 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New(respdata.Message)
		return
	}
	return
}

// 会员离开桌台 /api/merchant/foreLeaveTable/v1
func (l *DBLiveSingleService) foreLeaveTable(userId int, tableId int64) (err error) {
	type RequestResetPwdParamsData struct {
		TableId   int64  `json:"tableId"`   // 当前桌台id
		LoginName string `json:"loginName"` // 用户名 需要包括商户的前缀。平台将对传入的完整账号统一转换成小写。注意：只能包含以下特殊字符[下划线、@、#、&、*  ]
		Timestamp int64  `json:"timestamp"` // 时间戳 当前时间，不足13位请补000
	}
	type RequestResetPwdData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}

	type ResponseResetPwdData struct {
		Code    string `json:"code"`    // 返回码 200成功，其他失败
		Message string `json:"message"` // 返回信息
	}

	reqdata := RequestResetPwdData{
		MerchantCode: l.merchantCode,
	}
	reqParams := RequestResetPwdParamsData{
		TableId:   tableId,
		LoginName: l.getLoginNameFromUserId(int64(userId)),
		Timestamp: time.Now().UnixMilli(),
	}
	paramsBytes, _ := json.Marshal(reqParams)
	reqdata.Params, err = l.AESEncrypt([]byte(l.requestAesKey), string(paramsBytes))
	if err != nil {
		logs.Error("dblive_single 会员离开桌台 加密请求参数错误 userId=", userId, " err=", err.Error())
		return
	}
	reqdata.Signature = l.getRequestSign(string(paramsBytes))

	reqdataBytes, _ := json.Marshal(reqdata)
	payload := bytes.NewReader(reqdataBytes)
	url := fmt.Sprintf("%s/api/merchant/foreLeaveTable/v1", l.apiUrl)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("dblive_single 会员离开桌台 请求错误 userId=", userId, " err=", err.Error())
		return
	}
	respBytes, err := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		logs.Error("dblive_single 会员离开桌台 读取响应错误 userId=", userId, " err=", err.Error())
		return
	}
	logs.Info("dblive_single 会员离开桌台 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respdata := ResponseResetPwdData{}
	err = json.Unmarshal(respBytes, &respdata)
	if err != nil {
		logs.Error("dblive_single 会员离开桌台 解析响应消息体错误 userId=", userId, " err=", err.Error())
		return
	}
	if respdata.Code == "20001" { // 游戏账号不存在
		logs.Error("dblive_single 会员离开桌台失败 游戏账号不存在 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New("游戏账号不存在")
		return
	}
	if respdata.Code != DBLive_Code_Success {
		logs.Error("dblive_single 会员离开桌台失败 userId=", userId, " 错误码=", respdata.Code, " Message=", respdata.Message)
		err = errors.New(respdata.Message)
		return
	}
	return
}

// 支持的语言 简体中文cn,繁体中文tw,英文en,越南语vi,泰国语th,韩语kr
var dBLiveSupportLang = []string{"cn", "tw", "en", "vi", "th", "kr"}

// 支持的语言 1=zh-cn(简体中文) 2=zh-tw(䌓体中文） 3=en-us(英语) 4=euc-jp(日语) 5=ko(韩语) 6=th(泰文) 7=vi(越南文) 8=id(印尼语) 9=沙特阿拉伯语 10=德语 11=西班牙语 12=法语 13=俄语
// var dBLiveSupportLang2 = []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"}

// 登录 /api/merchant/forwardGame/v2
func (l *DBLiveSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestLoginParamsData struct {
		LoginName      string `json:"loginName"`      // 游戏账号 需要包括商户的前缀，区分大小写
		LoginPassword  string `json:"loginPassword"`  // 游戏密码 不允许的符合：`''[]./‘”“’$
		DeviceType     int64  `json:"deviceType"`     // 设备类型 请参考设备类型附件:设备ID如果是App内嵌H5游戏则必须且只能传递3和4 1=网页 2=手机网页 3=App iOS或 h5 iOS 4=App Android 或h5 Android 5=其他设备 6=移动端 横竖合一 7=移动端 横竖合一横版 8=移动端 横竖合一竖版
		Lang           int64  `json:"lang"`           // 语言 固定设为1
		Backurl        string `json:"backurl"`        // 返回商户地址 平台跳回商户的首页地址。H5页面上尤其需要直连地址方便跳转
		ShowExit       int64  `json:"showExit"`       // 是否显示退出按钮 可选，默认显示. 0=显示，1=不显示。对于内嵌APP时如果不需要游戏提供退出按钮可以传递参数1。本参数对PC设备不生效。
		GameTypeId     int64  `json:"gameTypeId"`     // 游戏类型ID 可选参数，不传入gameTypeId参数对于首次登陆用户会自动进入大厅, 传入gameTypeId参数对于老用户会自动进入该游戏下的随机桌台(游戏逻辑决定进入哪个桌台)
		Timestamp      int64  `json:"timestamp"`      // 时间戳 当前时间，不足13位请补000
		Ip             string `json:"ip"`             // IP地址 可选透传玩家真实ip  (用于获取对应省份的专属域名)
		PlayerLanguage string `json:"playerLanguage"` // 玩家语言 可选参数，简体中文cn,繁体中文tw,英文en,越南语vi,泰国语th,韩语kr 此字段无效，修改语言请在后台设置
	}
	type RequestLoginData struct {
		MerchantCode string `json:"merchantCode"` // 商户号
		Params       string `json:"params"`       // 原始Json参数进行AES/ECB/PKCS5Padding加密的结果
		Signature    string `json:"signature"`    // 原始Json参数进行MD5签名
	}
	type ResponseLoginData struct {
		Code    string `json:"code"`    // 返回码 200成功，其他失败
		Message string `json:"message"` // 返回信息
		Data    struct {
			Url string `json:"url"` // 游戏登陆地址
		} `json:"data"` // 返回数据
	}

	type RequestData struct {
		GameId     string `json:"GameId" validate:"required"` // 游戏code
		LangCode   string `json:"LangCode"`                   // 语言
		HomeUrl    string `json:"HomeUrl"`                    // 返回商户地址
		ShowExit   int64  `json:"ShowExit"`                   // 0=显示，1=不显示
		DeviceType string `json:"DeviceType"`                 // 设备类型 IOS Android Windows
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("dblive_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}

	loginLang := ""
	if reqdata.LangCode != "" {
		for _, v := range dBLiveSupportLang {
			if v == reqdata.LangCode {
				loginLang = reqdata.LangCode
				break
			}
		}
		if loginLang == "" {
			loginLang = "cn"
		}
	} else {
		loginLang = "cn"
	}

	loginDevideType := int64(5)
	if strings.EqualFold(reqdata.DeviceType, "IOS") {
		loginDevideType = 3
	} else if strings.EqualFold(reqdata.DeviceType, "Android") {
		loginDevideType = 4
	} else if strings.EqualFold(reqdata.DeviceType, "Windows") {
		loginDevideType = 1
	} else {
		loginDevideType = 5
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("dblive_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyDBLive, userId); err != nil {
		logs.Error("dblive_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, reqdata.GameId); err != nil {
		logs.Error("dblive_single 登录游戏 权限检查错误 userId=", userId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("dblive_single 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 先注册用户再登录
	err = l.register(userId)
	if err != nil {
		logs.Error("dblive_single 登录游戏 注册用户失败 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试1")
		return
	}

	// 会员离开桌台
	l.foreLeaveTable(userId, 0)

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("dblive_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试2")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("dblive_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	loginGameId, err := strconv.ParseInt(reqdata.GameId, 10, 64)
	if err != nil {
		logs.Error("dblive_single 登录游戏 GameId 转换错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试3")
		return
	}

	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("dblive_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试4")
		return
	}

	loginHomeUrl := ""
	if reqdata.HomeUrl != "" {
		loginHomeUrl = regexp.MustCompile(`^((http://)|(https://))?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(/)`).FindString(reqdata.HomeUrl)
	}

	reqdataLogin := RequestLoginData{
		MerchantCode: l.merchantCode,
	}
	reqParams := RequestLoginParamsData{
		LoginName:      l.getLoginNameFromUserId(int64(userId)),
		LoginPassword:  l.getUserPwd(userId),
		DeviceType:     loginDevideType,
		Lang:           1,
		Backurl:        loginHomeUrl,
		ShowExit:       reqdata.ShowExit,
		GameTypeId:     loginGameId,
		Timestamp:      time.Now().UnixMilli(),
		Ip:             ctx.GetIp(),
		PlayerLanguage: loginLang,
	}
	paramsBytes, _ := json.Marshal(reqParams)
	logs.Info("dblive_single 登录游戏 开始 userId=", userId, " reqdata=", paramsBytes)
	reqdataLogin.Params, err = l.AESEncrypt([]byte(l.requestAesKey), string(paramsBytes))
	if err != nil {
		logs.Error("dblive_single 登录游戏 加密参数错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试5")
		return
	}
	reqdataLogin.Signature = l.getRequestSign(string(paramsBytes))

	reqdataLoginBytes, _ := json.Marshal(reqdataLogin)
	payload := bytes.NewReader(reqdataLoginBytes)
	url := fmt.Sprintf("%s/api/merchant/forwardGame/v2", l.apiUrl)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodPost, url, payload)
	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("dblive_single 登录游戏 请求错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "网络错误,请稍后再试6")
		return
	}
	defer resp.Body.Close()
	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("dblive_single 登录游戏 读取响应错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试7")
		return
	}
	logs.Info("dblive_single 登录游戏 请求成功 userId=", userId, " respBytes=", string(respBytes))

	respData := ResponseLoginData{}
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		logs.Error("dblive_single 登录游戏 解析响应消息体错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试8")
		return
	}
	if respData.Code == "20009" { // 账号密码错误，重置密码
		go l.resetUserPwd(userId)
	}
	if respData.Code != DBLive_Code_Success {
		logs.Error("dblive_single 登录游戏 登录失败 userId=", userId, " 错误码=", respData.Code, " Message=", respData.Message)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试9")
		return
	}

	ctx.RespOK(respData.Data.Url)
	return
}

// 计算签名字符串
func (l *DBLiveSingleService) getRequestSign(param string) (sign string) {
	src := param + l.requestMd5Key
	sign = base.MD5Up(src)
	return
}

// 计算签名字符串
func (l *DBLiveSingleService) getCallbackSign(param string) (sign string) {
	src := param + l.callbackMd5Key
	sign = base.MD5Up(src)
	return
}

// 生成DBLive token
func (l *DBLiveSingleService) getTokenByUser(userId int, account string) (token string) {
	type DBLiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyDBLive + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyDBLive + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("dblive_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("dblive_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "dblive_" + uuid.NewString()
	tokendata := DBLiveTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyDBLive + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("dblive_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("dblive_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *DBLiveSingleService) getUserByToken(token string) (userId int, account string) {
	type DBLiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyDBLive + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := DBLiveTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("dblive_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyDBLive + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("dblive_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("dblive_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("dblive_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// getBalance 单个会员余额查询接口
// 应用场景：
// 1. 会员登录后需要拉取当前余额显示大厅。
// 2. 会员在下注、派彩等场景。
// 3. 其他场景。
func (l *DBLiveSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		LoginName string `json:"loginName"` // 会员账号
		Currency  string `json:"currency"`  // 会员在OB真人的币种代码。
		Stoken    string `json:"stoken"`    // 可选，会员最近一次进入游戏携带的stoken
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		LoginName string  `json:"loginName"` // 会员账号
		Balance   float64 `json:"balance"`   // 数值类型。支持4个精度。 当前余额是对应币种下的余额。假设此会员是美元币种，余额应为美元数值
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "Success",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	// logs.Info("dblive_single 获取玩家余额 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 获取玩家余额 非法的商户编码 l.merchantCode=", l.merchantCode, " reqdata.MerchantCode=", reqdata.MerchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 获取玩家余额 签名错误 reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 获取玩家余额 解析请求参数params错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 获取玩家余额 会员账号错误 用户名转换错误 reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 获取玩家余额 非法的币种 reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("dblive_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			respdata.Code = DBLive_Code_Fail_User_Not_Exist
			respdata.Message = "会员不存在"
		} else {
			respdata.Code = DBLive_Code_Fail_System_Error
			respdata.Message = "查询会员余额失败"
		}
		ctx.RespJson(respdata)
		return
	}

	params := ResponseParamData{
		LoginName: reqdataParam.LoginName,
		Balance:   userBalance.Amount,
	}
	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	// logs.Info("dblive_single 获取玩家余额 响应成功 respdata=", respdata)
	return
}

// getBatchBalance  批量会员余额查询接口
// 应用场景：
// 1. 会员在下注、派彩之后需要拉取当前桌台内7个人的余额。
func (l *DBLiveSingleService) GetBatchBalance(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		LoginNames []string `json:"loginNames"` // 会员账号集合
		Currency   string   `json:"currency"`   // 会员在OB真人的币种代码。
		Stoken     []string `json:"stoken"`     // 可选，会员最近一次进入游戏携带的stoken
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		LoginName string  `json:"loginName"` // 会员账号
		Balance   float64 `json:"balance"`   // 数值类型。支持4个精度。 当前余额是对应币种下的余额。假设此会员是美元币种，余额应为美元数值
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "Success",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 批量获取玩家余额 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	// logs.Info("dblive_single 批量获取玩家余额 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 批量获取玩家余额 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 批量获取玩家余额 商户编码不正确 l.merchantCode=", l.merchantCode, " reqdata.MerchantCode", reqdata.MerchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 批量获取玩家余额 签名错误 reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 批量获取玩家余额 解析请求参数params错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 批量获取玩家余额 非法的币种 reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	userIds := make([]int64, 0)
	for _, loginName := range reqdataParam.LoginNames {
		userId, err := l.getUserIdFromLoginName(loginName)
		if err != nil {
			logs.Error("dblive_single 批量获取玩家余额 会员账号错误 loginName=", loginName, " err=", err.Error())
			continue
		}
		userIds = append(userIds, userId)
	}

	// 获取用户余额
	userBalances := make([]thirdGameModel.UserBalance, 0)
	err = server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId IN ?", userIds).Find(&userBalances).Error
	if err != nil {
		logs.Error("dblive_single 批量获取玩家余额 失败 userIds=", userIds, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_System_Error
		respdata.Message = "获取用户余额失败"
		ctx.RespJson(respdata)
		return
	}

	params := make([]ResponseParamData, 0)
	for _, v := range userBalances {
		params = append(params, ResponseParamData{
			LoginName: fmt.Sprintf("%s_%d", l.merchantCodeLower, v.UserId),
			Balance:   v.Amount,
		})
	}
	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	// logs.Info("dblive_single 批量获取玩家余额 响应成功 respdata=", respdata)
	return
}

// betConfirm 下注确认回调接口
// 应用场景：
// 1. 会员在桌台内下注时，与运营商确认余额及扣减余额
// 2. 会员在多台内下注时，与运营商确认余额及扣减余额
func (l *DBLiveSingleService) BetConfirm(ctx *abugo.AbuHttpContent) {
	type RequestBetInfo struct {
		BetId      int64   `json:"betId"`      // 下注ID
		BetPointId int64   `json:"betPointId"` // 下注玩法ID
		BetAmount  float64 `json:"betAmount"`  // 下注金额正数
	}
	type RequestParamData struct {
		TransferNo     int64            `json:"transferNo"`     // 交易单号
		GameTypeId     int64            `json:"gameTypeId"`     // 游戏类型ID
		RoundNo        string           `json:"roundNo"`        // 局号
		LoginName      string           `json:"loginName"`      // 会员账号
		BetTime        int64            `json:"betTime"`        // 下注时间 时间戳毫秒
		BetTotalAmount float64          `json:"betTotalAmount"` // 下注总金额正数
		Currency       string           `json:"currency"`       // 币种代码
		BetInfo        []RequestBetInfo `json:"betInfo"`        // 下注明细数组。一次下注可能包含多个玩法，多注。数组里面同玩法只会有一条。
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		Balance       float64          `json:"balance"`       // 数值类型。支持4个精度。 当前余额是对应币种下的余额。假设此会员是美元币种，余额应为美元数值
		LoginName     string           `json:"loginName"`     // 会员账号
		RealBetAmount float64          `json:"realBetAmount"` // 实际下注金额
		RealBetInfo   []RequestBetInfo `json:"realBetInfo"`   // 实际下注明细数组。一次下注可能包含多个玩法，多注。数组里面同玩法只会有一条。
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 下注确认 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("dblive_single 下注确认 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 下注确认 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         strconv.FormatInt(reqdata.TransferNo, 10),
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          l.codeString2CodeInt(respdata.Code), // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("dblive_single 下注确认 记录请求响应日志失败 TransferNo=", reqdata.TransferNo, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 下注确认 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 下注确认 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 下注确认 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 下注确认 会员账号错误 TransferNo=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 下注确认 非法的币种 TransferNo=", reqdata.TransferNo, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}
	if len(reqdataParam.BetInfo) == 0 {
		logs.Error("dblive_single 下注确认 下注明细为空 TransferNo=", reqdata.TransferNo, " BetInfo=", reqdataParam.BetInfo)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "下注明细为空"
		ctx.RespJson(respdata)
		return
	}

	totalBetAmount := float64(0)
	for _, v := range reqdataParam.BetInfo {
		if v.BetAmount < 0 {
			logs.Error("dblive_single 下注确认 下注金额不能为负数 TransferNo=", reqdata.TransferNo, " BetAmount=", v.BetAmount, " BetId=", v.BetId)
			respdata.Code = DBLive_Code_Fail_Illegal_Parameter
			respdata.Message = "下注金额不能为负数"
			ctx.RespJson(respdata)
			return
		}
		totalBetAmount += v.BetAmount
	}

	// 按从小到大的下注金额排序
	sort.Slice(reqdataParam.BetInfo, func(i, j int) bool {
		return reqdataParam.BetInfo[i].BetAmount < reqdataParam.BetInfo[j].BetAmount
	})

	gameId := fmt.Sprintf("%d", reqdataParam.GameTypeId)
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("dblive_single 下注确认 获取游戏名称失败 TransferNo=", reqdata.TransferNo, " gameId=", gameId, " err=", err.Error())
		} else {
			gameName = gameList.Name
		}
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(int(userId), l.brandName, gameId); err != nil {
		logs.Error("dblive_single 登录游戏 权限检查错误 userId=", userId, " gameId=", gameId, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号禁止投注"
		ctx.RespJson(respdata)
	} else if !allowed {
		logs.Error("dblive_single 登录游戏 权限被拒绝 userId=", userId, " gameId=", gameId, " hint=", hint)
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号禁止投注"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"

	params := ResponseParamData{
		Balance:       0,
		LoginName:     reqdataParam.LoginName,
		RealBetAmount: 0,
		RealBetInfo:   make([]RequestBetInfo, 0),
	}

	// 开始下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("dblive_single 下注确认 获取用户余额失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = DBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			params.Balance = userBalance.Amount
		}
		if reqdataParam.BetInfo[0].BetAmount > userBalance.Amount {
			e = errors.New("余额不足")
			respdata.Code = DBLive_Code_Fail_Not_Enough_Balance
			respdata.Message = "余额不足"
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		for _, v := range reqdataParam.BetInfo {
			// 下注金额大于用户余额
			if v.BetAmount > userBalance.Amount {
				logs.Error("dblive_single 下注确认 会员余额不足下注金额 终止后面的注单下注 TransferNo=", reqdata.TransferNo, " v.BetId=", v.BetId, " v.BetAmount=", v.BetAmount, " userBalance.Amount=", userBalance.Amount)
				return nil
			}
			thirdId := fmt.Sprintf("%d", v.BetId)
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    v.BetAmount,
				WinAmount:    0,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    -1, //未开奖
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				logs.Error("dblive_single 下注确认 创建订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建订单失败"
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, v.BetAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", v.BetAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v.BetAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("dblive_single 下注确认 扣款失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " thirdId=", v.BetId, " v.BetAmount=", v.BetAmount, " err=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "扣款失败"
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       -v.BetAmount,
				AfterAmount:  userBalance.Amount - v.BetAmount,
				Reason:       utils.BalanceCReasonDBLiveBet,
				Memo:         l.brandName + " bet,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%d", reqdata.TransferNo),
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("dblive_single 下注确认 创建账变记录失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount -= v.BetAmount

			params.Balance = userBalance.Amount
			params.RealBetAmount += v.BetAmount
			params.RealBetInfo = append(params.RealBetInfo, v)

			//   推送下注事件通知
			if l.thirdGamePush != nil && v.BetAmount > 0 {
				l.thirdGamePush.PushBetEvent(userBalance.UserId, gameName, l.brandName, v.BetAmount, l.currency, l.brandName, thirdId, 5)
			}
		}

		return nil
	})

	if err != nil {
		logs.Error("dblive_single 下注确认 事务处理失败 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][dblive_single] 下注确认 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][dblive_single] 下注确认 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 下注确认 响应成功 TransferNo=", reqdata.TransferNo, " respdata=", respdata)
	return
}

// 根据交易单号获取下注信息
func (l *DBLiveSingleService) getBetPayoutMapByTransferNo(transferNo int64) (res map[string]float64) {
	res = make(map[string]float64)

	type RequestBetInfo struct {
		BetId      int64   `json:"betId"`      // 下注ID
		BetPointId int64   `json:"betPointId"` // 下注玩法ID
		BetAmount  float64 `json:"betAmount"`  // 下注金额正数
	}
	type RequestParamData struct {
		TransferNo     int64            `json:"transferNo"`     // 交易单号
		GameTypeId     int64            `json:"gameTypeId"`     // 游戏类型ID
		RoundNo        string           `json:"roundNo"`        // 局号
		LoginName      string           `json:"loginName"`      // 会员账号
		BetTime        int64            `json:"betTime"`        // 下注时间 时间戳毫秒
		BetTotalAmount float64          `json:"betTotalAmount"` // 下注总金额正数
		Currency       string           `json:"currency"`       // 币种代码
		BetInfo        []RequestBetInfo `json:"betInfo"`        // 下注明细数组。一次下注可能包含多个玩法，多注。数组里面同玩法只会有一条。
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	thirdReqInfo := thirdGameModel.ThirdReqInfo{}
	err := server.Db().GormDao().Table("x_third_request_info").Select("ReqBody").Where("ReqId=? and Brand=?", strconv.FormatInt(transferNo, 10), l.brandName).First(&thirdReqInfo).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			logs.Error("dblive_single getBetPayoutMapByTransferNo 交易单号不存在 transferNo=", transferNo)
		} else {
			logs.Error("dblive_single getBetPayoutMapByTransferNo 查询错误 transferNo=", transferNo, " err=", err.Error())
		}
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal([]byte(thirdReqInfo.ReqBody), &reqdata)
	if err != nil {
		logs.Error("dblive_single getBetPayoutMapByTransferNo 解析请求消息体错误 transferNo=", transferNo, " err=", err.Error())
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single getBetPayoutMapByTransferNo 解析请求参数params错误 transferNo=", transferNo, " err=", err.Error())
		return
	}

	for _, v := range reqdataParam.BetInfo {
		res[strconv.FormatInt(v.BetId, 10)] = v.BetAmount
	}
	return
}

// betCancel 取消下注回调接口
// 应用场景：
// 1. 如果因《下注确认回调》时超时没有收到响应，将做失败处理，将触发此接口请求。
// 2. 收到运营商《下注确认回调》成功响应，但最终因系统问题导致下注失败，将触发此接口请求。
func (l *DBLiveSingleService) BetCancel(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		TransferNo     int64              `json:"transferNo"`     // 交易单号
		GameTypeId     int64              `json:"gameTypeId"`     // 游戏类型ID
		RoundNo        string             `json:"roundNo"`        // 局号
		LoginName      string             `json:"loginName"`      // 会员账号
		CancelTime     int64              `json:"cancelTime"`     // 取消下注时间戳毫秒
		Currency       string             `json:"currency"`       // 币种
		HasTransferOut int64              `json:"hasTransferOut"` // 已上分是否需要还原 0=无 1=需要
		BetPayoutMap   map[string]float64 `json:"betPayoutMap"`   // 注单信息 key是注单id value是实际返奖金额
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		Balance        float64 `json:"balance"`        // 数值类型。支持4个精度。 当前余额是对应币种下的余额。假设此会员是美元币种，余额应为美元数值
		LoginName      string  `json:"loginName"`      // 会员账号
		RollbackAmount float64 `json:"rollbackAmount"` // 实际回滚金额，正数
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 取消下注 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("dblive_single 取消下注 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 取消下注 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         strconv.FormatInt(reqdata.TransferNo, 10) + "_cancel",
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          l.codeString2CodeInt(respdata.Code), // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("dblive_single 取消下注 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 取消下注 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 取消下注 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 取消下注 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 取消下注 用户名转换错误 TransferNo=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 取消下注 非法的币种 TransferNo=", reqdata.TransferNo, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	cancelBetPayoutMap := reqdataParam.BetPayoutMap
	if len(cancelBetPayoutMap) == 0 { // 7. 当请求中没有betPayoutMap参数时，商户回滚transferNo对应的所有注单；否则，仅对betPayoutMap中对应的注单和金额进行回滚操作。
		cancelBetPayoutMap = l.getBetPayoutMapByTransferNo(reqdataParam.TransferNo)
		logs.Info("dblive_single 取消下注 通过交易单号获取注单信息 TransferNo=", reqdataParam.TransferNo, " cancelBetPayoutMap=", cancelBetPayoutMap)
	}

	tablePre := "x_third_live_pre_order"

	params := ResponseParamData{
		Balance:        0,
		LoginName:      reqdataParam.LoginName,
		RollbackAmount: 0,
	}
	// 开始取消下注事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("dblive_single 取消下注 获取用户余额失败 TransferNo=", reqdataParam.TransferNo, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = DBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			params.Balance = userBalance.Amount
		}

		for k, v := range cancelBetPayoutMap {
			thirdId := k
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Info("dblive_single 取消下注 订单不存在 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					continue
				}
				logs.Error("dblive_single 取消下注 查询订单失败 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询订单失败"
				return e
			}
			if order.DataState != -1 {
				e = errors.New("不能取消已结算订单")
				logs.Error("dblive_single 取消下注 不能取消已结算订单 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_Logic_Error
				respdata.Message = "不能取消已结算订单"
				return e
			}
			if v != order.BetAmount {
				e = errors.New("取消金额与下注金额不一致")
				logs.Error("dblive_single 取消下注 取消金额与下注金额不一致 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_Logic_Error
				respdata.Message = "取消金额与下注金额不一致"
				return e
			}

			// 更新注单状态
			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"RawData":   string(bodyBytes),
			}).Error
			if e != nil {
				logs.Error("dblive_single 取消下注 更新订单状态失败 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "更新订单状态失败"
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", v),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("dblive_single 取消下注 加款失败 TransferNo=", reqdataParam.TransferNo, " userId=", userId, " thirdId=", thirdId, " BetAmount=", v, " err=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "加款失败"
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v,
				AfterAmount:  userBalance.Amount + v,
				Reason:       utils.BalanceCReasonDBLiveCancel,
				Memo:         l.brandName + " cancel,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%d", reqdata.TransferNo),
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("dblive_single 取消下注 创建账变记录失败 TransferNo=", reqdataParam.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount += v

			params.Balance = userBalance.Amount
			params.RollbackAmount += v
		}

		return nil
	})

	if err != nil {
		logs.Error("dblive_single 取消下注 事务处理失败 TransferNo=", reqdataParam.TransferNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][dblive_single] 取消下注 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][dblive_single] 取消下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 取消下注 响应成功 TransferNo=", reqdataParam.TransferNo, " respdata=", respdata)
	return
}

// gamePayout 派彩回调接口
// 应用场景：
// 1. 游戏正常结算派彩的，需要通知增加余额；
// 2. 游戏跳局返回余额派彩的，需要通知增加余额；
// 3. 后台发起的取消局派彩的，需要通知增加/扣减余额；
// 4. 后台发起的重算局派彩的，需要通知增加/扣减余额；
func (l *DBLiveSingleService) GamePayout(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		TransferNo   int64              `json:"transferNo"`   // 交易单号
		GameTypeId   int64              `json:"gameTypeId"`   // 游戏类型ID
		RoundNo      string             `json:"roundNo"`      // 局号
		LoginName    string             `json:"loginName"`    // 会员账号
		PlayerId     int64              `json:"playerId"`     // 玩家ID
		PayoutTime   int64              `json:"payoutTime"`   // 派彩时间 时间戳毫秒
		PayoutAmount float64            `json:"payoutAmount"` // 派彩金额 本次派彩金额，运营商需要加/减的金额。 有可能负数，负数需要扣减余额。 (商户直接按我们的payoutAmount字段的金额做加法就行，不需要商户再去转换了 是加钱，还是扣钱，我们来控制)
		TransferType string             `json:"transferType"` // 业务类型 PAYOUT=正常结算； DISCARD=跳局结算； CANCEL=取消局(有可能负数，需要扣款)； REPAYOUT=重算局(有可能负数，需要扣款)； SURRENDER_PAYOUT=投降（仅21点）;
		Currency     string             `json:"currency"`     // 币种
		BetPayoutMap map[string]float64 `json:"betPayoutMap"` // 注单信息 key是注单id value是实际返奖金额
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		Balance    float64 `json:"balance"`    // 数值类型。支持4个精度。 当前余额是对应币种下的余额。假设此会员是美元币种，余额应为美元数值
		LoginName  string  `json:"loginName"`  // 会员账号
		RealAmount float64 `json:"realAmount"` // 实际处理金额 区分正负数：对于加钱是正数 对于扣款是负数
		BadAmount  float64 `json:"badAmount"`  // 坏账金额 对于扣款业务，当前余额不足扣款时，有多少扣多少。余下的作为坏账需要记录，且是正数。对于无坏账的情况，始终返回0。对于派彩的情况，始终返回0。badAmount + realAmount=派彩总额 payoutAmount
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 派彩 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("dblive_single 派彩 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 派彩 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         strconv.FormatInt(reqdata.TransferNo, 10),
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          l.codeString2CodeInt(respdata.Code), // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("dblive_single 派彩 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 派彩 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 派彩 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 派彩 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 派彩 会员账号错误 TransferNo=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 派彩 非法的币种 TransferNo=", reqdata.TransferNo, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	params := ResponseParamData{
		Balance:    0,
		LoginName:  reqdataParam.LoginName,
		RealAmount: 0,
		BadAmount:  0,
	}
	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("dblive_single 派彩 获取用户余额失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = DBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		} else {
			params.Balance = userBalance.Amount
		}

		for k, v := range reqdataParam.BetPayoutMap {
			thirdId := k
			thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			// 查询注单
			order := thirdGameModel.ThirdOrder{}
			e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
			if e != nil {
				if e == daogorm.ErrRecordNotFound { // 如果注单不存在则跳过
					logs.Error("dblive_single 派彩 订单不存在 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					params.BadAmount += v
					continue
				}
				logs.Error("dblive_single 派彩 查询订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询订单失败"
				return e
			}
			if reqdataParam.TransferType == "PAYOUT" || reqdataParam.TransferType == "DISCARD" || reqdataParam.TransferType == "SURRENDER_PAYOUT" {
				if order.DataState != -1 {
					e = errors.New("订单已结算")
					logs.Error("dblive_single 派彩 订单已结算 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_Logic_Error
					respdata.Message = "订单已结算"
					return e
				}

				// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
				validBet := math.Abs(v - order.BetAmount)
				if validBet > math.Abs(order.BetAmount) {
					validBet = math.Abs(order.BetAmount)
				}
				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState": 1,
					"ThirdTime": thirdTime,
					"ValidBet":  validBet,
					"WinAmount": v,
					"RawData":   string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("dblive_single 派彩 更新订单状态失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				order.DataState = 1
				order.ThirdTime = thirdTime
				order.ValidBet = validBet
				order.WinAmount = v
				order.RawData = string(bodyBytes)
				order.Id = 0
				e = tx.Table(table).Create(&order).Error
				if e != nil {
					logs.Error("dblive_single 派彩 创建正式表订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "创建订单失败"
					return e
				}
				// 推送派奖事件到 CustomerIO
				if l.thirdGamePush != nil {
					l.thirdGamePush.PushRewardEvent(5, l.brandName, thirdId)
				}
			} else if reqdataParam.TransferType == "CANCEL" {
				if order.DataState != 1 {
					e = errors.New("订单未结算不能取消结算")
					logs.Error("dblive_single 派彩 订单未结算不能取消结算 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_Logic_Error
					respdata.Message = "订单未结算不能取消结算"
					return e
				}

				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"DataState": -2,
					"ThirdTime": thirdTime,
					"WinAmount": daogorm.Expr("WinAmount + ?", v),
					"RawData":   string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("dblive_single 派彩 更新预设表订单状态失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"DataState": -2,
					"ThirdTime": thirdTime,
					"WinAmount": daogorm.Expr("WinAmount + ?", v),
					"RawData":   string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("dblive_single 派彩 更新正式表订单状态失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

			} else if reqdataParam.TransferType == "REPAYOUT" {
				if order.DataState != 1 {
					e = errors.New("订单未结算不能重新结算")
					logs.Error("dblive_single 派彩 订单未结算不能重新结算 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_Logic_Error
					respdata.Message = "订单未结算不能重新结算"
					return e
				}

				// 更新注单状态
				e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
					"ThirdTime": thirdTime,
					"WinAmount": daogorm.Expr("WinAmount + ?", v),
					"RawData":   string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("dblive_single 派彩 更新预设表订单状态失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

				e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
					"ThirdTime": thirdTime,
					"WinAmount": daogorm.Expr("WinAmount + ?", v),
					"RawData":   string(bodyBytes),
				}).Error
				if e != nil {
					logs.Error("dblive_single 派彩 更新订单状态失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
					respdata.Code = DBLive_Code_Fail_System_Error
					respdata.Message = "更新订单状态失败"
					return e
				}

			} else {
				e = errors.New("非法的TransferType")
				logs.Error("dblive_single 派彩 非法的TransferType TransferNo=", reqdata.TransferNo, " TransferType=", reqdataParam.TransferType, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_Illegal_Parameter
				respdata.Message = "非法的TransferType"
				return e
			}

			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", v),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && v != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("dblive_single 派彩 加扣款失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " thirdId=", thirdId, " v=", v, " err=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "加扣款失败"
				return e
			}
			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: userBalance.Amount,
				Amount:       v,
				AfterAmount:  userBalance.Amount + v,
				Reason:       utils.BalanceCReasonDBLiveSettle,
				Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%d", reqdata.TransferNo) + ",t:" + reqdataParam.TransferType,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("dblive_single 派彩 创建账变记录失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建账变记录失败"
				return e
			}
			userBalance.Amount += v

			params.Balance = userBalance.Amount
			params.RealAmount += v
		}

		return nil
	})

	if err != nil {
		logs.Error("dblive_single 派彩 事务处理失败 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][dblive_single] 派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][dblive_single] 派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 派彩 响应成功 TransferNo=", reqdata.TransferNo, " respdata=", respdata)
	return
}

// activityPayout 活动和小费类回调接口 测试环境不支持打赏 只能正式环境验证这个接口
// 应用场景：
// 1. 红包雨领取到账的，需要通知增加余额；
// 2. 百家乐大赛报名费，需要通知扣减余额；
// 3. 百家乐大赛派彩，需要通知增加余额；
// 4. 荷官打赏的，需要通知扣减余额；
// 5. 主播打赏的，需要通知扣减余额；
func (l *DBLiveSingleService) ActivityPayout(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		LoginName      string  `json:"loginName"`      // 会员账号
		PayoutTime     int64   `json:"payoutTime"`     // 派彩时间时间戳毫秒
		TransferNo     int64   `json:"transferNo"`     // 交易单号
		Currency       string  `json:"currency"`       // 币种
		TransferType   string  `json:"transferType"`   // 业务类型
		PayoutAmount   float64 `json:"payoutAmount"`   // 派彩金额 本次操作金额，商户需要加/减的金额。有可能负数，负数表示需要扣减余额。(商户直接按我们的payoutAmount字段的金额做加法就行，不需要商户再去转换了，是加钱，还是扣钱，我们来控制)
		PayoutType     string  `json:"payoutType"`     // 派彩类型
		PlayerId       int64   `json:"playerId"`       // 玩家ID
		HasTransferOut int64   `json:"hasTransferOut"` // 已上分是否需要还原 0=无 1=需要
	}
	// 派彩类型：
	// •DEDUCTION=扣款业务(扣款)
	// •PAYOUT=派彩业务(加钱)
	// •ROLLBACK=回滚扣款业务(加钱) ROLLBACK类型：如果商户端对于扣款业务，扣款成功，但此时DB未能接收到响应将会发起对应业务单号的回滚操作(一直重试直到响应成功)，商户根据单号进行回滚操作
	// 业务类型：
	// •DEDUCTION_ANCHOR=主播打赏(负数，需要扣款)
	// •DEDUCTION_DEADLER=荷官打赏(负数，需要扣款)
	// •DEDUCTION_COMPETE_REGISTERY=百家乐大赛报名费扣款(负数，需要扣款)
	// •DEDUCTION_COMPETE_INITAMOUNT=百家乐大赛报初始化筹码扣款(负数，需要扣款)
	// •DEDUCTION_COMPETE_BUYMORE=百家乐大赛增购费扣款(负数，需要扣款)
	// •DEDUCTION_COMPETE_REPEAT=百家乐大赛重购费扣款(负数，需要扣款)
	// •DEDUCTION_COMPETE_BUY_JETTON_FEE=百家乐大赛购买筹码费(负数，需要扣款)
	// •PAYOUT_RED_ENVELOPE=红包雨活动派彩
	// •PAYOUT_COMPETE=百家乐大赛派彩
	// •PAYOUT_COMPETE_REGISTERY=百家乐大赛退赛退还报名费
	// •PAYOUT_COMPETE_INITAMOUNT=百家乐大赛退赛退还初始化筹码•PAYOUT_COMPETE_BUY_JETTON=百家乐大赛退回购买筹码
	// •PAYOUT_SETTLEMENT=百家乐大赛结算
	// •ROLLBACK_ANCHOR=主播打赏回滚
	// •ROLLBACK_DEADLER=荷官打赏回滚
	// •ROLLBACK_COMPETE_REGISTERY=百家乐大赛报名费回滚
	// •ROLLBACK_COMPETE_INITAMOUNT=百家乐大赛初始化筹码回滚•ROLLBACK_COMPETE_BUYMORE=百家乐大赛增购费回滚
	// •ROLLBACK_COMPETE_REPEAT=百家乐大赛重购费回滚•ROLLBACK_COMPETE_BUY_JETTON_FEE=百家乐大赛购买筹码回滚
	// •PAYOUT_TASK_REWARD=任务奖励
	// •PAYOUT_LOTTERY_REWARD=抽奖活动
	// •PAYOUT_REDEEM_REWARD=兑奖奖励
	// •PAYOUT_MANUAL_POSITIVE=手动冲正
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		LoginName  string  `json:"loginName"`
		Balance    float64 `json:"balance"`
		RealAmount float64 `json:"realAmount"`
		BadAmount  float64 `json:"badAmount"`
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 活动和小费 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("dblive_single 活动和小费 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 活动和小费 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         strconv.FormatInt(reqdata.TransferNo, 10),
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          l.codeString2CodeInt(respdata.Code), // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("dblive_single 活动和小费 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 活动和小费 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 活动和小费 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 活动和小费 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 活动和小费 用户名转换错误 TransferNo=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Not_Exist
		respdata.Message = "会员不存在"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdataParam.Currency, l.currency) {
		logs.Error("dblive_single 活动和小费 非法的币种 TransferNo=", reqdata.TransferNo, " reqdataParam.Currency=", reqdataParam.Currency, " l.currency=", l.currency)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "币种不正确"
		ctx.RespJson(respdata)
		return
	}

	if reqdataParam.PayoutType != "DEDUCTION" && reqdataParam.PayoutType != "PAYOUT" && reqdataParam.PayoutType != "ROLLBACK" {
		logs.Error("dblive_single 活动和小费 非法的PayoutType TransferNo=", reqdata.TransferNo, " reqdataParam.PayoutType=", reqdataParam.PayoutType)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "非法的PayoutType"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	params := ResponseParamData{
		LoginName:  reqdataParam.LoginName,
		Balance:    0,
		RealAmount: 0,
		BadAmount:  0,
	}

	// 开始活动和小费事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("dblive_single 活动和小费 获取用户余额失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = DBLive_Code_Fail_User_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "查询用户信息失败"
			}
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		if userBalance.Amount+reqdataParam.PayoutAmount < 0 {
			logs.Error("dblive_single 活动和小费 余额不足 TransferNo=", reqdata.TransferNo, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdataParam.PayoutAmount=", reqdataParam.PayoutAmount)
			respdata.Code = DBLive_Code_Fail_Not_Enough_Balance
			respdata.Message = "余额不足"
			return e
		}
		params.Balance = userBalance.Amount

		thirdId := fmt.Sprintf("%d", reqdataParam.TransferNo)
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("dblive_single 活动和小费 查询订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
			respdata.Code = DBLive_Code_Fail_System_Error
			respdata.Message = "查询订单失败"
			return e
		}

		if e != nil {
			if reqdataParam.PayoutType == "ROLLBACK" {
				logs.Error("dblive_single 活动和小费 回滚订单不存在 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_Logic_Error
				respdata.Message = "回滚订单不存在"
				return e
			}
			// 创建注单
			order := thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: ChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       "activity_" + reqdataParam.PayoutType,
				GameName:     "activity_" + reqdataParam.TransferType,
				BetAmount:    0,
				WinAmount:    reqdataParam.PayoutAmount,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    1,
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				logs.Error("dblive_single 活动和小费 创建预设表订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建订单失败"
				return e
			}

			order.Id = 0
			e = tx.Table(table).Create(&order).Error
			if e != nil {
				logs.Error("dblive_single 活动和小费 创建正式表订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "创建正式表订单失败"
				return e
			}
		} else {
			if reqdataParam.PayoutType != "ROLLBACK" {
				e = errors.New("订单已存在")
				logs.Error("dblive_single 活动和小费 订单已存在 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order)
				respdata.Code = DBLive_Code_Fail_Logic_Error
				respdata.Message = "订单已存在"
				return e
			}

			e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"WinAmount": daogorm.Expr("WinAmount + ?", reqdataParam.PayoutAmount),
			}).Error
			if e != nil {
				logs.Error("dblive_single 活动和小费 回滚预设订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "回滚订单失败"
				return e
			}

			e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"WinAmount": daogorm.Expr("WinAmount + ?", reqdataParam.PayoutAmount),
			}).Error
			if e != nil {
				logs.Error("dblive_single 活动和小费 回滚正式订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " error=", e.Error())
				respdata.Code = DBLive_Code_Fail_System_Error
				respdata.Message = "回滚订单失败"
				return e
			}
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", reqdataParam.PayoutAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdataParam.PayoutAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("dblive_single 活动和小费 加扣款失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " thirdId=", thirdId, " PayoutAmount=", reqdataParam.PayoutAmount, " err=", e.Error())
			respdata.Code = DBLive_Code_Fail_System_Error
			respdata.Message = "加扣款失败"
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdataParam.PayoutAmount,
			AfterAmount:  userBalance.Amount + reqdataParam.PayoutAmount,
			Reason:       utils.BalanceCReasonDBLiveTip,
			Memo:         l.brandName + " tip,thirdId:" + thirdId + ",tid:" + fmt.Sprintf("%d", reqdata.TransferNo) + ",t:" + reqdataParam.TransferType,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("dblive_single 活动和小费 创建账变记录失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
			respdata.Code = DBLive_Code_Fail_System_Error
			respdata.Message = "创建账变记录失败"
			return e
		}
		params.Balance = userBalance.Amount + reqdataParam.PayoutAmount
		params.RealAmount = reqdataParam.PayoutAmount

		return nil
	})

	if err != nil {
		logs.Error("dblive_single 活动和小费 事务处理失败 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][dblive_single] 活动和小费 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][dblive_single] 活动和小费 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 活动和小费 响应成功 TransferNo=", reqdata.TransferNo, " respdata=", respdata)
	return
}

type DbliveRequestBettingRecordList struct {
	Id             int64   `json:"id"`             // 注单编号
	PlayerId       int64   `json:"playerId"`       // 玩家编号
	PlayerName     string  `json:"playerName"`     // 玩家账号
	AgentId        int64   `json:"agentId"`        // 代理编号
	BetAmount      float64 `json:"betAmount"`      // 投注额
	ValidBetAmount float64 `json:"validBetAmount"` // 有效投注额
	NetAmount      float64 `json:"netAmount"`      // 输赢金额
	BeforeAmount   float64 `json:"beforeAmount"`   // 下注前余额
	GameTypeId     int64   `json:"gameTypeId"`     // 游戏类型
	PlatformId     int64   `json:"platformId"`     // 厅id
	PlatformName   string  `json:"platformName"`   // 厅名称
	BetStatus      int64   `json:"betStatus"`      // 下注状态 0=未结算 1=已结算
	BetFlag        int64   `json:"betFlag"`        // 重算标志 0=正常结算，2=取消指定局的结算，3=取消该注单的结算 4=重算指定局 5=重算指定注单
	BetPointId     int64   `json:"betPointId"`     // 玩法，下注点
	JudgeResult    string  `json:"judgeResult"`    // 结果
	Currency       string  `json:"currency"`       // 币种
	TableCode      string  `json:"tableCode"`      // 台桌号
	RoundNo        string  `json:"roundNo"`        // 局号
	BootNo         string  `json:"bootNo"`         // 靴号
	LoginIp        string  `json:"loginIp"`        // 游戏ip
	DeviceType     int64   `json:"deviceType"`     // 设备类型 1=网页，2=手机网页，3=Ios，4=Android，5=其他设备
	DeviceId       string  `json:"deviceId"`       // 设备id
	RecordType     int64   `json:"recordType"`     // 注单类别 0、试玩 1、正式  2、内部测试  3、机器人。 只有为1记录的才会返回给商户
	GameMode       int64   `json:"gameMode"`       // 游戏模式 0=常规 1=好路 3=多台
	NickName       string  `json:"nickName"`       // 会员昵称
	DealerName     string  `json:"dealerName"`     // 荷官昵称
	TableName      string  `json:"tableName"`      // 游戏桌台名称
	AgentCode      string  `json:"agentCode"`      // 代理code
	AgentName      string  `json:"agentName"`      // 代理名称
	BetPointName   string  `json:"betPointName"`   // 玩法名称
	GameTypeName   string  `json:"gameTypeName"`   // 游戏名称
	PayAmount      float64 `json:"payAmount"`      // 返奖金额 同派彩额，只是取消局和跳局会记录为投注额
}

func (l *DBLiveSingleService) dbliveGameRecord2String(data DbliveRequestBettingRecordList) (res string) {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf("\"注单编号\":%d,", data.Id))
	sb.WriteString(fmt.Sprintf("\"玩家账号\":\"%s\",", data.PlayerName))
	sb.WriteString(fmt.Sprintf("\"投注额\":%g,", data.BetAmount))
	sb.WriteString(fmt.Sprintf("\"有效投注额\":%g,", data.ValidBetAmount))
	sb.WriteString(fmt.Sprintf("\"返奖金额\":%g,", data.PayAmount))
	sb.WriteString(fmt.Sprintf("\"输赢金额\":%g,", data.NetAmount))
	sb.WriteString(fmt.Sprintf("\"游戏类型ID\":%d,", data.GameTypeId))
	sb.WriteString(fmt.Sprintf("\"游戏名称\":\"%s\",", data.GameTypeName))
	sb.WriteString(fmt.Sprintf("\"玩法ID\":%d,", data.BetPointId))
	sb.WriteString(fmt.Sprintf("\"玩法名称\":\"%s\",", data.BetPointName))
	sb.WriteString(fmt.Sprintf("\"厅名ID\":%d,", data.PlatformId))
	sb.WriteString(fmt.Sprintf("\"厅名称\":\"%s\",", data.PlatformName))
	sb.WriteString(fmt.Sprintf("\"结果\":\"%s\",", data.JudgeResult))
	if data.BetStatus == 0 {
		sb.WriteString("\"下注状态\":\"未结算\",")
	} else if data.BetStatus == 1 {
		sb.WriteString("\"下注状态\":\"已结算\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"下注状态\":\"%d\",", data.BetStatus))
	}
	if data.BetFlag == 0 {
		sb.WriteString("\"重算标志\":\"正常结算\",")
	} else if data.BetFlag == 2 {
		sb.WriteString("\"重算标志\":\"取消指定局的结算\",")
	} else if data.BetFlag == 3 {
		sb.WriteString("\"重算标志\":\"取消该注单的结算\",")
	} else if data.BetFlag == 4 {
		sb.WriteString("\"重算标志\":\"重算指定局\",")
	} else if data.BetFlag == 5 {
		sb.WriteString("\"重算标志\":\"重算指定注单\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"重算标志\":\"%d\",", data.BetFlag))
	}
	sb.WriteString(fmt.Sprintf("\"币种\":\"%s\",", data.Currency))
	sb.WriteString(fmt.Sprintf("\"台桌号\":\"%s\",", data.TableCode))
	sb.WriteString(fmt.Sprintf("\"局号\":\"%s\",", data.RoundNo))
	sb.WriteString(fmt.Sprintf("\"游戏IP\":\"%s\",", data.LoginIp))
	if data.GameMode == 0 {
		sb.WriteString("\"游戏模式\":\"常规\",")
	} else if data.GameMode == 1 {
		sb.WriteString("\"游戏模式\":\"好路\",")
	} else if data.GameMode == 3 {
		sb.WriteString("\"游戏模式\":\"多台\",")
	} else {
		sb.WriteString(fmt.Sprintf("\"游戏模式\":\"%d\",", data.GameMode))
	}
	sb.WriteString(fmt.Sprintf("\"荷官昵称\":\"%s\",", data.DealerName))
	sb.WriteString(fmt.Sprintf("\"游戏桌台名称\":\"%s\"", data.TableName))
	sb.WriteString("}")
	res = sb.String()
	return
}

// playerbetting 玩家下注推送接口
// 应用场景：
// 1. 游戏正常结算派彩的，需要通知下注记录；
// 2. 游戏跳局返回余额派彩的，需要通知下注记录；
// 3. 后台发起的取消局派彩的，需要通知下注记录；
// 4. 后台发起的重算局派彩的，需要通知下注记录；
// 5. 以上场景会主动推送注单数据给商户
// 已经问过三方了，是注单推送接口，不需要处理金额加减
func (l *DBLiveSingleService) Playerbetting(ctx *abugo.AbuHttpContent) {
	type RequestChangePayout struct {
		TransferNo   int64   `json:"transferNo"`   // 交易单号
		TransferType string  `json:"transferType"` // 单号类型
		GameTypeId   int64   `json:"gameTypeId"`   // 游戏类型
		RoundNo      string  `json:"roundNo"`      // 局号
		PlayerId     int64   `json:"playerId"`     // 玩家编号
		LoginName    string  `json:"loginName"`    // 会员账号
		PayoutTime   int64   `json:"payoutTime"`   // 派彩时间
		PayoutAmount float64 `json:"payoutAmount"` // 派彩金额
		Currency     string  `json:"currency"`     // 币种
	}
	type RequestParamData struct {
		ChangePayout      RequestChangePayout              `json:"changePayout"`
		BettingRecordList []DbliveRequestBettingRecordList `json:"bettingRecordList"`
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		MerchantCode string `json:"merchantCode"` // 代理编码
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 玩家下注推送 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	//logs.Info("dblive_single 玩家下注推送 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 玩家下注推送 解析请求消息体错误 err=", err.Error(), "reqdata=", string(bodyBytes))
		respdata.Code = DBLive_Code_Fail_Other_Error
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 玩家下注推送 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 玩家下注推送 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params), "reqdata=", string(bodyBytes))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	var reqdataParam []RequestParamData
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 玩家下注推送 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error(), "reqdata=", string(bodyBytes))
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}
	/*
		reqdataParam := RequestParamData{}
		err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
		if err != nil {
			logs.Error("dblive_single 玩家下注推送 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
			respdata.Code = DBLive_Code_Fail_Illegal_Parameter
			respdata.Message = "json反序列化请求params错误"
			ctx.RespJson(respdata)
			return
		}*/

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	for _, data := range reqdataParam {
		for _, v := range data.BettingRecordList {
			if v.BetStatus != 1 { // 只更新已结算的
				continue
			}
			userId, err := l.getUserIdFromLoginName(v.PlayerName)
			if err != nil {
				logs.Error("dblive_single 玩家下注推送 用户名转换错误 TransferNo=", reqdata.TransferNo, " v.PlayerName=", v.PlayerName, "v=", v, " err=", err.Error())
				continue
			}

			betCtx := l.dbliveGameRecord2String(v)

			err = server.Db().GormDao().Table(tablePre).Where("Brand=? and ThirdId=? and UserId=?", l.brandName, fmt.Sprintf("%d", v.Id), userId).Updates(map[string]interface{}{
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
			}).Error
			if err != nil {
				logs.Error("dblive_single 玩家下注推送 更新预设表失败 TransferNo=", reqdata.TransferNo, " v.Id=", v.Id, " userId=", userId, "v=", v, " error=", err.Error())
			}

			err = server.Db().GormDao().Table(table).Where("Brand=? and ThirdId=? and UserId=?", l.brandName, fmt.Sprintf("%d", v.Id), userId).Updates(map[string]interface{}{
				"BetCtx":     betCtx,
				"GameRst":    betCtx,
				"BetCtxType": 3,
			}).Error
			if err != nil {
				logs.Error("dblive_single 玩家下注推送 更新正式表失败 TransferNo=", reqdata.TransferNo, " v.Id=", v.Id, " userId=", userId, "v=", v, " error=", err.Error())
			}
		}
	}

	params := ResponseParamData{
		MerchantCode: l.merchantCode,
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 玩家下注推送 响应成功 respdata=", respdata)
	return
}

// activityRebate  返利活动推送接口
// 应用场景：
// 1. 百家乐参与返利活动正常结算，需要通知增加返利金额。
// 2. 百家乐参与红包雨活动正常结算，需要通知增加返利金额。
// 3. 以上场景会推送活动彩金数据给商户；
func (l *DBLiveSingleService) ActivityRebate(ctx *abugo.AbuHttpContent) {
	type RequestParamData struct {
		DetailId     int64   `json:"detailId"`     // 明细表ID
		ActivityType int64   `json:"activityType"` // 活动类型
		AgentId      int64   `json:"agentId"`      // 代理编号
		AgentCode    string  `json:"agentCode"`    // 代理编码
		PlayerId     int64   `json:"playerId"`     // 玩家编号
		LoginName    string  `json:"loginName"`    // 玩家账号
		ActivityId   int64   `json:"activityId"`   // 活动ID
		ActivityName string  `json:"activityName"` // 活动名称
		CreatedTime  string  `json:"createdTime"`  // 创建时间
		RewardAmount float64 `json:"rewardAmount"` // 获取金额
	}
	type RequestData struct {
		MerchantCode string `json:"merchantCode"`
		TransferNo   int64  `json:"transferNo"` // 交易单号
		Params       string `json:"params"`
		Signature    string `json:"signature"`
		Timestamp    int64  `json:"timestamp"`
	}

	type ResponseParamData struct {
		MerchantCode string `json:"merchantCode"` // 代理编码
	}
	type ResponseData struct {
		Code      string `json:"code"`      // 返回码 200成功，其他失败
		Message   string `json:"message"`   // 返回信息
		Data      string `json:"data"`      // 返回数据
		Signature string `json:"signature"` // 签名
	}
	respdata := ResponseData{
		Code:    DBLive_Code_Success,
		Data:    "",
		Message: "成功",
	}
	respdata.Signature = l.getCallbackSign(respdata.Data)

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("dblive_single 返利活动 读取请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "读取请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	logs.Info("dblive_single 返利活动 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("dblive_single 返利活动 解析请求消息体错误 err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求消息体错误"
		ctx.RespJson(respdata)
		return
	}
	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         strconv.FormatInt(reqdata.TransferNo, 10) + "_cancel",
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          l.codeString2CodeInt(respdata.Code), // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
			Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
			DoUpdates: daogormclause.Assignments(map[string]interface{}{
				"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
			}),
		}).Create(&thirdReqInfo).Error; e != nil {
			logs.Error("dblive_single 返利活动 记录请求响应日志失败 thirdReqInfo=", thirdReqInfo, " err=", e.Error())
		}
	}()

	if !strings.EqualFold(reqdata.MerchantCode, l.merchantCode) {
		logs.Error("dblive_single 返利活动 商户编码不正确 TransferNo=", reqdata.TransferNo, " reqdata.MerchantCode=", reqdata.MerchantCode, " l.merchantCode=", l.merchantCode)
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "商户编码不正确"
		ctx.RespJson(respdata)
		return
	}

	if !strings.EqualFold(reqdata.Signature, l.getCallbackSign(reqdata.Params)) {
		logs.Error("dblive_single 返利活动 签名错误 TransferNo=", reqdata.TransferNo, " reqdata.Signature=", reqdata.Signature, " getCallbackSign=", l.getCallbackSign(reqdata.Params))
		respdata.Code = DBLive_Code_Fail_Signature_Error
		respdata.Message = "签名错误"
		ctx.RespJson(respdata)
		return
	}

	reqdataParam := RequestParamData{}
	err = json.Unmarshal([]byte(reqdata.Params), &reqdataParam)
	if err != nil {
		logs.Error("dblive_single 返利活动 解析请求参数params错误 TransferNo=", reqdata.TransferNo, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_Illegal_Parameter
		respdata.Message = "json反序列化请求params错误"
		ctx.RespJson(respdata)
		return
	}

	userId, err := l.getUserIdFromLoginName(reqdataParam.LoginName)
	if err != nil {
		logs.Error("dblive_single 返利活动 用户名转换错误 TransferNo=", reqdata.TransferNo, " reqdataParam.LoginName=", reqdataParam.LoginName, " err=", err.Error())
		respdata.Code = DBLive_Code_Fail_User_Account_Error
		respdata.Message = "会员账号错误"
		ctx.RespJson(respdata)
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	thirdId := fmt.Sprintf("reward_%d", reqdataParam.DetailId)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

	params := ResponseParamData{
		MerchantCode: l.merchantCode,
	}
	// 开始返利活动事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("dblive_single 返利活动 获取用户余额失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		// 创建注单
		order := thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userBalance.UserId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       "reward_" + fmt.Sprintf("%d", reqdataParam.ActivityId),
			GameName:     reqdataParam.ActivityName,
			BetAmount:    0,
			WinAmount:    reqdataParam.RewardAmount,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			Fee:          0,
			DataState:    1,
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			if strings.Contains(e.Error(), "Error 1062") || strings.Contains(e.Error(), "Duplicate entry") || errors.Is(e, daogorm.ErrDuplicatedKey) {
				logs.Error("dblive_single 返利活动 预设表订单已存在 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
				return e
			}
			logs.Error("dblive_single 返利活动 创建预设表订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
			return e
		}
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			//订单以存在则跳过
			if strings.Contains(e.Error(), "Error 1062") || strings.Contains(e.Error(), "Duplicate entry") || errors.Is(e, daogorm.ErrDuplicatedKey) {
				logs.Error("dblive_single 返利活动 正式表订单已存在 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
				return e
			}
			logs.Error("dblive_single 返利活动 创建正式表订单失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " order=", order, " err=", e.Error())
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", reqdataParam.RewardAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdataParam.RewardAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("dblive_single 返利活动 加款失败 TransferNo=", reqdata.TransferNo, " userId=", userId, " thirdId=", thirdId, " RewardAmount=", reqdataParam.RewardAmount, " err=", e.Error())
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdataParam.RewardAmount,
			AfterAmount:  userBalance.Amount + reqdataParam.RewardAmount,
			Reason:       utils.BalanceCReasonDBLiveReward,
			Memo:         l.brandName + " reward,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("dblive_single 返利活动 创建账变记录失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " amountLog=", amountLog, " err=", e.Error())
			return e
		}
		return nil
	})

	if err != nil {
		logs.Error("dblive_single 返利活动 事务处理失败 TransferNo=", reqdata.TransferNo, " thirdId=", thirdId, " err=", err.Error())
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][dblive_single] 返利活动 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][dblive_single] 返利活动 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}

	paramsBytes, _ := json.Marshal(params)
	respdata.Data = string(paramsBytes)
	respdata.Signature = l.getCallbackSign(respdata.Data)
	ctx.RespJson(respdata)
	logs.Info("dblive_single 返利活动 响应成功 TransferNo=", reqdata.TransferNo, " reqdata=", reqdata, "respdata=", respdata)
	return
}

// 数据AES加密函数
func (l *DBLiveSingleService) AESEncrypt(key []byte, plaintext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	plainBytes := []byte(plaintext)
	// 对于ECB模式，直接使用cipher.NewCBCEncrypter即可
	//if len(plainBytes)%aes.BlockSize != 0 {
	plainBytes = l.AESPad(plainBytes, aes.BlockSize)
	//}
	ciphertext := make([]byte, len(plainBytes))
	for start := 0; start < len(plainBytes); start += aes.BlockSize {
		block.Encrypt(ciphertext[start:start+aes.BlockSize], plainBytes[start:start+aes.BlockSize])
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 数据AES解密函数
func (l *DBLiveSingleService) AESDecrypt(key []byte, ciphertext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext := make([]byte, len(cipherBytes))
	// 对于ECB模式，直接使用cipher.NewCBCDecrypter即可
	for start := 0; start < len(cipherBytes); start += aes.BlockSize {
		block.Decrypt(plaintext[start:start+aes.BlockSize], cipherBytes[start:start+aes.BlockSize])
	}
	return string(l.AESUnpad(plaintext)), nil
}

// pad 使用PKCS7填充
func (l *DBLiveSingleService) AESPad(buf []byte, blockSize int) []byte {
	padding := blockSize - (len(buf) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(buf, padText...)
}

// unpad 移除PKCS7填充
func (l *DBLiveSingleService) AESUnpad(buf []byte) []byte {
	length := len(buf)
	if length == 0 {
		return buf
	}
	padding := int(buf[length-1])
	return buf[:length-padding]
}
