package paycontroller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/active"
	"xserver/controller/common"
	"xserver/controller/datapush"
	"xserver/server"
	"xserver/utils"

	"golang.org/x/exp/rand"

	"github.com/golang-module/carbon/v2"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

type PayController struct {
	Base
}

func (c *PayController) Init() {

	server.Http().PostNoAuth("/api/pay/get_symbol", c.get_symbol)

	server.Http().Post("/api/pay/get_pay_info", c.get_pay_info)
	server.Http().Post("/api/pay/create_order", c.create_order)
	server.Http().Post("/api/pay/create_recommend_order", c.createRecommendOrder)
	server.Http().PostNoAuth("/api/pay/get_chain_rate", c.get_chain_rate)

	Pay24.Init()
	c.InitEbpay()
	c.InitHambit()
	c.InitTransak()
	c.InitCapitalPay()
	EPay.Init()
	DGPay.Init()
	Paymentiq.Init()
	LPay.Init()        //初始化LPay
	RichWay.Init()     //初始化RichWay
	BtCash.Init()      //初始化BtCash
	Power.Init()       //初始化PowerPay
	Mg99.Init()        //初始化mg99
	Being.Init()       //初始化BeingPay
	Feibaopay.Init()   //初始化Feibaopay
	Ytpay.Init()       //初始化Ytpay
	Expay.Init()       //初始化Expay
	PraxisPay.Init()   //初始化PraxisPay
	U2cpay.Init()      //初始化U2cpay
	RMPay.Init()       //初始化日本支付
	MainPay.Init()     //初始化MainPay
	Cpay.Init()        //初始化cPay
	Pay99.Init()       //初始化pay99
	Alipay.Init()      //初始化alipay
	Bnnpay.Init()      //初始化bnnpay
	SafeGatePay.Init() //初始化SafeGatePay

}

func (c *PayController) get_symbol(ctx *abugo.AbuHttpContent) {
	params := struct {
		Type      int //币种
		ShowIndex int
		Sort      string
		Show      int // 1:支付 2:代付
	}{}

	errcode := 0
	err := ctx.RequestData(&params)
	if err != nil {
		logs.Error(err)
		ctx.RespErr(err, &errcode)
		return
	}

	host := ctx.Host()
	token := server.GetToken(ctx)
	_, SellerId := server.GetChannel(ctx, host)
	if token != nil {
		SellerId = token.SellerId
	}

	// 获取paymethod
	financeMethodDao := server.DaoxHashGame().XFinanceMethod
	financeMethodDb := financeMethodDao.WithContext(ctx.Gin())
	queryMethod := financeMethodDb.Where(financeMethodDao.SellerID.Eq(int32(SellerId))).Where(financeMethodDao.State.Eq(1))
	switch params.Show {
	case 1:
		queryMethod.Where(financeMethodDao.IsRecharge.Eq(1))
	case 2:
		queryMethod.Where(financeMethodDao.IsWithdraw.Eq(1))
	default:
		queryMethod.Where(financeMethodDao.IsRecharge.Eq(1))
	}

	var symbols []string
	err = queryMethod.Pluck(financeMethodDao.Symbol.Distinct(), &symbols)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 获取payment
	financeSymbolDao := server.DaoxHashGame().XFinanceSymbol
	financeSymbolDb := financeSymbolDao.WithContext(ctx.Gin())

	query := financeSymbolDb.Where(financeSymbolDao.Symbol.In(symbols...))
	if params.ShowIndex != 0 {
		query.Where(financeSymbolDao.ShowIndex.Eq(int32(params.ShowIndex)))
	}

	if params.Sort != "" {
		switch params.Sort {
		case "asc":
			query.Order(financeSymbolDao.Sort.Asc())
		case "desc":
			query.Order(financeSymbolDao.Sort.Desc())
		default:
			query.Order(financeSymbolDao.Sort.Desc())
		}
	}

	if params.Type != 0 {
		query.Where(financeSymbolDao.FType.Eq(int32(params.Type)))
	}
	type Select struct {
		Id      int
		Symbol  string
		Country string
		Icon    string
		Sort    int
	}

	var res []Select
	query.Where(financeSymbolDao.State.Eq(1)).
		Where(financeSymbolDao.SellerID.Eq(int32(SellerId))).
		Select(financeSymbolDao.ID, financeSymbolDao.Symbol, financeSymbolDao.Icon, financeSymbolDao.Country, financeSymbolDao.Sort).
		Scan(&res)

	for i, _ := range res {
		v := &(res)[i]
		v.Symbol = strings.ToLower(v.Symbol)
	}

	ctx.RespOK(res)
}

func (c *PayController) get_pay_info(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		Symbol           string //币种
		RechargeWithward int    `validate:"required"` //1充值 2提现
	}{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	reqdata.Symbol = strings.ToLower(reqdata.Symbol)

	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	if token != nil {
		sellerId = token.SellerId
	}

	// 获取活动配置信息（已经过滤处理）
	fconfig, mconfig := active.GetActivityConfigs(sellerId, channelId, reqdata.RechargeWithward, token.UserId)
	logs.Info("get_pay_info 获取活动配置结果: UserId=%d, SellerId=%d, ChannelId=%d, RechargeWithward=%d, FconfigCount=%d, MconfigCount=%d",
		token.UserId, sellerId, channelId, reqdata.RechargeWithward, len(fconfig), len(mconfig))

	symbols, _ := server.XDb().Table("x_finance_symbol").Where("Symbol = ?", reqdata.Symbol, "").Where("SellerId = ?", sellerId, "").OrderBy("sort desc").Find()
	if symbols == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	sql := ""
	table := server.XDb().Table("x_finance_method").OrderBy("sort desc").Where("Symbol = ?", reqdata.Symbol, "").Where("SellerId = ?", sellerId, "")
	table = table.Where("State = 1")
	if reqdata.RechargeWithward == 1 {
		table = table.Where("IsRecharge = 1")
		sql = "Id,Brand,Name,MinRecharge,MaxRecharge,RechargeAmountOptions,IsInputRechargeAmount,Icon,Symbol,PayType,IsBankRecharge,Banks,HasBankNoRecharge,WithdrawWayType,RechargeWayType,BankType,WithdrawBanks,PoundSign,IdentifyType,Rtype,JumpType"
	}
	if reqdata.RechargeWithward == 2 {
		table = table.Where("IsWithdraw = 1")
		sql = "Id,Brand,Name,MinWithdraw,MaxWithdraw,WithdrawAmountOptions,IsInputWithdrawAmount,Icon,Symbol,PayType,IsBankRecharge,Banks,HasBankNoRecharge,WithdrawWayType,RechargeWayType,BankType,WithdrawBanks,PoundSign,IdentifyType,Rtype,JumpType"
	}
	data, _ := table.Select(sql).Find()
	data.ForEach(func(item *xgo.XMap) bool {
		if reqdata.RechargeWithward == 1 {
			item.Delete("WithdrawBanks")
		}

		if reqdata.RechargeWithward == 2 && item.String("WithdrawBanks") != "" {
			item.Set("Banks", item.String("WithdrawBanks"))
			item.Delete("WithdrawBanks")
		}
		symbols.ForEach(func(symbol *xgo.XMap) bool {
			temp1 := strings.ToLower(symbol.String("Symbol"))
			temp2 := strings.ToLower(item.String("Symbol"))
			if temp1 == temp2 {
				rate := 0.0
				if symbol.Int("AutoState") == 1 {
					if reqdata.RechargeWithward == 1 {
						rate = symbol.Float64("RechargeRate")
					}
					if reqdata.RechargeWithward == 2 {
						rate = symbol.Float64("WithwardRate")
					}
				} else {
					if reqdata.RechargeWithward == 1 {
						rate = symbol.Float64("RechargeRateEx")
					}
					if reqdata.RechargeWithward == 2 {
						rate = symbol.Float64("WithwardRateEx")
					}
				}

				fmt.Println(strings.ToLower(symbol.String("Symbol")), strings.ToLower(item.String("Symbol")), rate)
				if reqdata.RechargeWithward == 1 {
					ratefixtype := symbol.Int("RechargeFixType")
					if ratefixtype == 1 {
						rateDecimal := decimal.NewFromFloat(rate)
						fixDecimal := decimal.NewFromFloat(symbol.Float64("RechargeFix"))
						rate, _ = rateDecimal.Add(fixDecimal).Float64()
					} else {
						rateDecimal := decimal.NewFromFloat(rate)
						fixDecimal := decimal.NewFromFloat(symbol.Float64("RechargeFix"))

						rate, _ = rateDecimal.Mul(fixDecimal).Add(rateDecimal).Float64()
					}
				}
				if reqdata.RechargeWithward == 2 {
					ratefixtype := symbol.Int("WithwardFixType")
					if ratefixtype == 1 {
						rateDecimal := decimal.NewFromFloat(rate)
						fixDecimal := decimal.NewFromFloat(symbol.Float64("WithwardFix"))
						rate, _ = rateDecimal.Sub(fixDecimal).Float64()
					} else {
						rateDecimal := decimal.NewFromFloat(rate)
						fixDecimal := decimal.NewFromFloat(symbol.Float64("WithwardFix"))

						rate, _ = rateDecimal.Sub(rateDecimal.Mul(fixDecimal)).Float64()
					}

				}
				if strings.ToLower(item.String("Symbol")) == "shib" || strings.ToLower(item.String("Symbol")) == "doge" {
					rate /= 1000000
				}
				item.Set("State", symbol.Int("State"))
				item.Set("Rate", rate)
				item.Set("NetJson", symbol.String("NetJson"))

				// 添加充值赠送活动配置信息到每个支付方式项目中
				if reqdata.RechargeWithward == 1 {
					// 创建活动配置对象
					activityConfig := make(map[string]interface{})
					flag := 0
					// 只有在有首充配置时才添加首充活动配置
					if len(fconfig) > 0 {
						// 过滤首充配置
						filteredFconfig := active.FilterActivityConfig(fconfig)
						activityConfig["Config"] = filteredFconfig
						flag = 1
					}

					// 只有在有复充配置时才添加复充活动配置
					if len(mconfig) > 0 {
						// 过滤复充配置
						filteredMconfig := active.FilterActivityConfig(mconfig)
						activityConfig["Config"] = filteredMconfig
						flag = 2
					}

					// 如果有有效的活动配置，查询并添加倒计时信息
					if flag > 0 {
						// 根据AwardType=4查询充值活动的BaseConfig
						activeDefineDao := server.DaoxHashGame().XActiveDefine
						activeDefineDb := activeDefineDao.WithContext(context.Background())

						activeDefine, err := activeDefineDb.Where(activeDefineDao.AwardType.Eq(4)).
							Where(activeDefineDao.State.Eq(1)).
							Where(activeDefineDao.SellerID.Eq(int32(sellerId))).
							Where(activeDefineDao.ChannelID.Eq(int32(channelId))).First()

						if err == nil && activeDefine != nil && activeDefine.BaseConfig != "" {
							// 解析BaseConfig中的倒计时信息
							var baseConfig active.DepositBaseConfig
							err := json.Unmarshal([]byte(activeDefine.BaseConfig), &baseConfig)
							if err == nil {
								// 将倒计时信息添加到ActivityConfig顶层
								activityConfig["IsCountdown"] = baseConfig.IsCountdown
								activityConfig["CountdownType"] = baseConfig.CountdownType
								activityConfig["CountdownValue"] = baseConfig.CountdownValue
							}
						}

						item.Set("ActivityConfig", activityConfig)
						item.Set("RechargeActive", flag)
					}
				}

				return false
			}
			return true
		})

		return true
	})
	ctx.RespOK(data.Maps())
}

func (c *PayController) get_chain_rate(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		Symbol string `validate:"required"` //币种
	}{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	reqdata.Symbol = strings.ToLower(reqdata.Symbol)
	host := ctx.Host()
	token := server.GetToken(ctx)
	_, sellerId := server.GetChannel(ctx, host)
	if token != nil {
		sellerId = token.SellerId
	}
	symbol, err := server.XDb().Table("x_finance_symbol").Where("Symbol = ?", reqdata.Symbol, "").Where("SellerId = ?", sellerId, "").OrderBy("sort desc").First()
	if err != nil {
		ctx.RespOK([]map[string]interface{}{})
	}
	if symbol == nil {
		ctx.RespOK([]map[string]interface{}{})
		return
	}
	rate := 0.0
	if symbol.Int("AutoState") == 1 {
		rate = symbol.Float64("RechargeRate")

	} else {
		rate = symbol.Float64("RechargeRateEx")

	}
	// 充值
	ratefixtype := symbol.Int("RechargeFixType")
	if ratefixtype == 1 {
		// 固定汇率偏差
		rate -= symbol.Float64("RechargeFix")
	} else {
		// 百分比汇率偏差
		rate -= rate * symbol.Float64("RechargeFix")
	}

	ctx.RespOK(gin.H{
		"MinRecharge": symbol.Float64("RechargeMin"),
		"MaxRecharge": symbol.Float64("RechargeMax"),
		"MinWithdraw": symbol.Float64("WithwardMin"),
		"MaxWithdraw": symbol.Float64("WithwardMax"),
		"Rate":        rate,
	})
}

func isValidEmail(email string) bool {
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(email)
}

type CreateOrderReq struct {
	MethodId         int     `validate:"required"`                                  //支付方式
	RechargeWithward int     `validate:"required"`                                  //1充值 2提现
	Symbol           string  `validate:"required"`                                  //币种
	Amount           float64 `validate:"required"`                                  //金额
	Password         string  `validate:"required_if=RechargeWithward 2 Symbol inr"` //密码
	RealName         string  `validate:"required_if=RechargeWithward 2 Symbol inr"` //real name         pix required
	CPFNo            string  //CPF号(税卡号)    pix必填
	PIXType          string  `json:"PIXType"` //pix类型	   pix必填   PHONE / EMAIL / CPF / CNPJ / RANDOM
	PIXAccount       string  //pix账号	   pix必填
	PhoneNum         string  //手机号 	 ebpay充值必填
	WalletAddress    string  //钱包地址         ebpay提现必填
	IsRechargeActive bool    `json:"IsRechargeActive"` // 是否是充值活动

	Ip string

	PayType      string // 支付类型
	BankCode     string `validate:"required_if=RechargeWithward 2 Symbol inr"` // 银行代码
	BankNo       string `validate:"required_if=RechargeWithward 2 Symbol inr"` // 银行卡号
	IFSC         string `validate:"required_if=RechargeWithward 2 Symbol inr"` // IFSC
	BankName     string // 银行名称
	BranchName   string // 支行名称
	BankType     string // 银行类型
	OrderType    int    // 充值订单分类
	IdentifyType string
	IdentifyNum  string

	// MainPay 特定字段
	BankAccount     string // 银行账号
	BankAccountName string // 银行账户名
	CardNumber      string // 信用卡号
	CardHolderName  string // 信用卡持卡人姓名
	CardExpMonth    string // 信用卡过期月份
	CardExpYear     string // 信用卡过期年份
	CardCVV         string // 信用卡CVV码

	Email string `json:"Email"` // 邮箱

	// alipay
	AlipayAccount string `json:"AlipayAccount"` // alipay账号

	// 地址信息字段 - 用于cpay等支付方式
	Address    string `json:"Address"`    // 地址
	City       string `json:"City"`       // 城市
	State      string `json:"State"`      // 州/省
	PostalCode string `json:"PostalCode"` // 邮编
	Country    string `json:"Country"`    // 国家代码
}

// 自定义 ResponseWriter 用来捕获输出
type responseCapture struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (r *responseCapture) Write(data []byte) (int, error) {
	r.body.Write(data)    // 捕获返回的数据
	return len(data), nil // 阻止数据真正返回给浏览器
}

func (r *responseCapture) WriteHeaderNow() {
	// 覆盖默认行为，避免立即发送状态码
}

func (r *responseCapture) WriteHeader(code int) {
	// 覆盖默认行为，避免发送状态码
}

// 创建推介通道支付订单
func (c *PayController) createRecommendOrder(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Symbol string  `validate:"required"` //币种
		Amount float64 `validate:"required"` //金额
	}

	req := RequestData{}
	errCode := 0
	if err := ctx.RequestData(&req); ctx.RespErr(err, &errCode) {
		return
	}

	// 获取推介配置
	type RecommendConfigData struct {
		Way  int
		Hour int
	}
	var recommendConfig RecommendConfigData
	redisData := server.CRedis().HGet("CONFIG", "SET_RECHARGE_RECOMMEND")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)
		// 反序列化
		if err := json.Unmarshal([]byte(dataStr), &recommendConfig); err != nil {
			fmt.Println("JSON 反序列化失败:", err)
			return
		}
	}

	// 获取该币种的厂商数据
	type xFinanceResults struct {
		Id      int
		Brand   string
		Symbol  string
		PayType string
		Hv      int //权重值
	}
	var financeResults []xFinanceResults
	xFinance := server.DaoxHashGame().XFinanceMethod
	token := server.GetToken(ctx)
	err := xFinance.WithContext(nil).Select(xFinance.ID.As("Id"), xFinance.Brand, xFinance.Symbol, xFinance.Hv, xFinance.PayType).
		Where(xFinance.Symbol.Eq(req.Symbol)).
		Where(xFinance.Ftype.Eq(1)).
		Where(xFinance.Rtype.Eq(3)).
		Where(xFinance.SellerID.Eq(int32(token.SellerId))).
		Where(xFinance.State.Eq(1)).
		Group(xFinance.Brand, xFinance.Symbol).
		Scan(&financeResults)

	if err != nil {
		ctx.RespErr(err, &errCode)
		return
	}

	if financeResults == nil {
		ctx.RespErrString(true, &errCode, "无可用的支付通道")
		return
	}

	// 如果配置按成功率轮询，则获取成功率
	if recommendConfig.Way == 1 {
		type PaidResult struct {
			PaidRate float64 `json:"PaidRate" gorm:"column:PaidRate"`
		}
		var rechargeResult PaidResult

		startTime := carbon.Now().StartOfDay().StdTime()
		endTime := carbon.Now().EndOfDay().SubHours(recommendConfig.Hour).StdTime()

		for i, _ := range financeResults {
			rechargeResult = PaidResult{}
			server.Db().Gorm().Table("x_recharge").Select(`
				ROUND(COUNT(CASE WHEN x_recharge.State = 5 THEN x_recharge.Id END) / COUNT(x_recharge.Id), 2) AS PaidRate`).
				Joins("LEFT JOIN x_finance_method ON x_recharge.PayId = x_finance_method.Id").
				Where("x_finance_method.Brand = ? AND x_finance_method.Symbol = ?", financeResults[i].Brand, financeResults[i].Symbol).
				Where("x_recharge.CreateTime BETWEEN ? and ?", startTime, endTime).
				Scan(&rechargeResult)

			financeResults[i].Hv = int(rechargeResult.PaidRate * 100)
			// 如果结果为0则默认权重为1
			if financeResults[i].Hv == 0 {
				financeResults[i].Hv = 1
			}
		}
	}

	// 解析响应数据
	var result struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			ErrCode int    `json:"errcode"`
			ErrMsg  string `json:"errmsg"`
			PayUrl  string `json:"payurl"`
		} `json:"data"`
	}
	originalWriter := ctx.Gin().Writer
	// 循环尝试直到 abc 成功或者 financeResults 列表为空
	for len(financeResults) > 0 {
		// 计算权重总和
		totalWeight := 0
		for _, item := range financeResults {
			// 生成一个 0 到 totalWeight 之间的随机数
			totalWeight += item.Hv
		}

		rand.Seed(uint64(time.Now().UnixNano()))
		randomWeight := rand.Intn(totalWeight)
		// 累加权重并选择元素
		currentWeight := 0
		selectedIndex := -1
		for i, item := range financeResults {
			currentWeight += item.Hv
			if randomWeight < currentWeight {
				selectedIndex = i
				break
			}
		}

		ctx.Gin().Set("Symbol", req.Symbol)
		ctx.Gin().Set("Amount", req.Amount)
		ctx.Gin().Set("MethodId", financeResults[selectedIndex].Id)
		ctx.Gin().Set("RechargeWithward", 1)
		ctx.Gin().Set("PayType", financeResults[selectedIndex].PayType)

		// 创建一个新的 ResponseWriter 捕获响应数据
		writer := &responseCapture{
			ResponseWriter: ctx.Gin().Writer,
			body:           bytes.NewBufferString(""),
		}
		ctx.Gin().Writer = writer
		c.create_order(ctx)

		// 从捕获的缓冲区中获取响应数据
		responseData := writer.body.Bytes()
		logs.Info(string(responseData))

		if err := json.Unmarshal(responseData, &result); err != nil {
			// 解析失败时，记录日志并返回
			logs.Error("Failed to parse response: ", err)
			return
		}

		// 清除捕获的响应内容，避免输出到浏览器
		writer.body.Reset()

		logs.Info("financeResults:", financeResults)
		logs.Info("result:", result)
		// 检查是否是最后一个支付通道且失败
		if len(financeResults) == 1 && result.Code == 100 {
			ctx.Gin().Writer = originalWriter
			errcode := -1
			ctx.RespErr(errors.New("无可用的支付通道"), &errcode)
			return
		}

		if result.Code == 200 {
			break
		} else {
			financeResults = append(financeResults[:selectedIndex], financeResults[selectedIndex+1:]...)
		}
	}
	// 恢复原始的 ResponseWriter
	ctx.Gin().Writer = originalWriter

	// 检查是否有有效的结果
	if result.Code == 0 {
		// 如果没有任何有效结果（所有通道都失败且没有捕获到响应）
		errcode := -1
		ctx.RespErr(errors.New("所有支付通道都不可用"), &errcode)
		return
	}

	// 根据最终结果状态使用框架标准方法返回响应
	if result.Code == 200 {
		// 成功情况：使用框架标准成功响应
		ctx.RespOK(xgo.H{
			"payurl": result.Data.PayUrl,
		})
	} else {
		// 失败情况：直接传递原始错误响应（已经是正确格式）
		ctx.Gin().JSON(200, result)
	}
}

func (c *PayController) create_order(ctx *abugo.AbuHttpContent) {
	reqdata := CreateOrderReq{}
	errcode := 0
	var paymethod *xgo.XMap
	var symbol *xgo.XMap
	rate := 0.0
	reqdata.Ip = ctx.GetIp()

	if symbol, exist := ctx.Gin().Get("Symbol"); exist {
		reqdata.Symbol = symbol.(string)
	}

	if methodId, exist := ctx.Gin().Get("MethodId"); exist {
		reqdata.MethodId = methodId.(int)
	}

	if amount, exist := ctx.Gin().Get("Amount"); exist {
		reqdata.Amount = amount.(float64)
	}

	if rechargeWithdraw, exist := ctx.Gin().Get("RechargeWithward"); exist {
		reqdata.RechargeWithward = rechargeWithdraw.(int)
	}

	if _, exist := ctx.Gin().Get("RechargeWithward"); !exist {
		err := ctx.RequestData(&reqdata)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	if reqdata.OrderType == 0 {
		if reqdata.RechargeWithward == 1 {
			reqdata.OrderType = utils.RechargeType
		} else {
			reqdata.OrderType = utils.WithdrawType
		}
	}

	paymethod, _ = server.XDb().Table("x_finance_method").Where("Id = ?", reqdata.MethodId).First()
	if paymethod == nil {
		ctx.RespErr(errors.New("支付方式不存在"), &errcode)
		return
	}

	if reqdata.RechargeWithward == 1 {
		if reqdata.Amount < paymethod.Float64("MinRecharge") {
			ctx.RespErr(errors.New("小于最小充值金额"), &errcode)
			return
		}
		if reqdata.Amount > paymethod.Float64("MaxRecharge") {
			ctx.RespErr(errors.New("大于最大充值金额"), &errcode)
			return
		}
	} else if reqdata.RechargeWithward == 2 {
		if reqdata.Amount < paymethod.Float64("MinWithdraw") {
			ctx.RespErr(errors.New("小于最小提现金额"), &errcode)
			return
		}
		if reqdata.Amount > paymethod.Float64("MaxWithdraw") {
			ctx.RespErr(errors.New("大于最大提现金额"), &errcode)
			return
		}
	}

	token := server.GetToken(ctx)
	if paymethod.String("Brand") != "mainpay" {
		symbol, _ = server.XDb().Table("x_finance_symbol").Where("Symbol = ?", reqdata.Symbol).Where("SellerId = ?", token.SellerId).First()
		if symbol == nil {
			ctx.RespErr(errors.New("币种不存在"), &errcode)
			return
		}

		if symbol.Int("AutoState") == 1 {
			if reqdata.RechargeWithward == 1 {
				rate = symbol.Float64("RechargeRate")
			}
			if reqdata.RechargeWithward == 2 {
				rate = symbol.Float64("WithwardRate")
			}
		} else {
			if reqdata.RechargeWithward == 1 {
				rate = symbol.Float64("RechargeRateEx")
			}
			if reqdata.RechargeWithward == 2 {
				rate = symbol.Float64("WithwardRateEx")
			}
		}
		// 充值
		if reqdata.RechargeWithward == 1 {
			ratefixtype := symbol.Int("RechargeFixType")
			if ratefixtype == 1 {
				// 固定汇率偏差
				rate += symbol.Float64("RechargeFix")
			} else {
				// 百分比汇率偏差
				rate += rate * symbol.Float64("RechargeFix")
			}
		}
		// 提现
		if reqdata.RechargeWithward == 2 {
			ratefixtype := symbol.Int("WithwardFixType")
			if ratefixtype == 1 {
				rate -= symbol.Float64("WithwardFix")
			} else {
				rate -= rate * symbol.Float64("WithwardFix")
			}
		}
	}

	logs.Debug("create_order:", paymethod.String("Brand"), paymethod.String("Name"))
	jcfg := map[string]interface{}{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &jcfg)
	if reqdata.RechargeWithward == 1 {
		userdata, _ := server.XDb().Table("x_user").Where("UserId = ?", token.UserId).First()
		agent, _ := server.XDb().Table("x_agent_independence").Where("UserId = ?", token.UserId).First()
		SpecialAgent := 2
		if agent != nil {
			SpecialAgent = 1
		}
		if paymethod.String("Brand") == "pix" && paymethod.String("Name") == "pix" {
			Pay24.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "ebpay" && paymethod.String("Name") == "ebpay" {
			c.ebpay_create_recharge_order(ctx, jcfg, rate, userdata, SpecialAgent, token, &reqdata)
		} else if paymethod.String("Brand") == "hambit" && paymethod.String("Name") == "hambit" {
			c.hambit_create_recharge_order(ctx, jcfg, rate, userdata, SpecialAgent, token, &reqdata, paymethod)
		} else if paymethod.String("Brand") == "pay24" && paymethod.String("Name") == "pay24" {
			Pay24.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "epay" && paymethod.String("Name") == "epay" {
			EPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "dgpay" && paymethod.String("Name") == "dgpay" {
			DGPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "lpay" && paymethod.String("Name") == "lpay" { //增加菲律宾lpay
			LPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "richway" && paymethod.String("Name") == "richway" { //增加菲律宾richway
			RichWay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "btcash" && paymethod.String("Name") == "btcash" { //增加菲律宾btcash
			BtCash.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "powerpay" && paymethod.String("Name") == "powerpay" { //增加菲律宾powerpay
			Power.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "mg99" && paymethod.String("Name") == "mg99" { //增加菲律宾mg99
			Mg99.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "being" && paymethod.String("Name") == "being" { //增加香港支付being
			Being.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "feibaopay" && paymethod.String("Name") == "feibaopay" { //增加印度feibaopay
			Feibaopay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "ytpay" && paymethod.String("Name") == "ytpay" { //增加印度ytpay
			Ytpay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "expay" && paymethod.String("Name") == "expay" { //增加俄罗斯expay
			Expay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "praxispay" && paymethod.String("Name") == "praxispay" { //增加俄罗斯praxispay
			PraxisPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "u2cpay" && paymethod.String("Name") == "u2cpay" { //增加巴西u2cpay
			U2cpay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "rmpay" && paymethod.String("Name") == "rmpay" { //增加日本支付
			RMPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "mainpay" && paymethod.String("Name") == "mainpay" { //增加马来西亚MainPay
			MainPay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "cpay" && paymethod.String("Name") == "cpay" { //增加俄罗斯cPay
			Cpay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "pay99" && paymethod.String("Name") == "pay99" { //新增99pay
			Pay99.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "alipay" && paymethod.String("Name") == "alipay" { //新增alipay
			Alipay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "bnnpay" && paymethod.String("Name") == "bnnpay" { //新增bnnpay
			Bnnpay.Recharge(ctx, &reqdata, SpecialAgent)
		} else if paymethod.String("Brand") == "safegatepay" && paymethod.String("Name") == "safegatepay" { //新增SafeGatePay
			SafeGatePay.Recharge(ctx, &reqdata, SpecialAgent)
		}

		// 获取最新创建的充值订单
		// 注意：这里不需要 OrderId 变量，因为不同支付方式的实现中已经创建了订单
		// 我们可以通过查询最新的订单来获取
		rechargeDao := server.DaoxHashGame().XRecharge
		rechargeDb := rechargeDao.WithContext(ctx.Gin())
		rechargeOrder, err := rechargeDb.Where(rechargeDao.UserID.Eq(int32(token.UserId))).
			Order(rechargeDao.CreateTime.Desc()).
			First()

		if err != nil {
			logs.Error("获取充值订单失败:", err.Error())
		} else {
			// 获取支付方式
			payMethod, err := c.getPayMethod(reqdata.MethodId)
			if err != nil {
				logs.Error("获取支付方式失败:", err.Error())
			} else {
				// 发送充值订单创建事件到ThinkingData
				datapush.SendDepositCreateEvent(rechargeOrder, payMethod)
			}
		}
	}

	if reqdata.RechargeWithward == 2 {
		if reqdata.Password == "" {
			ctx.RespErr(errors.New("请输入提款密码"), &errcode)
			return
		}
		reqdata.Password = xgo.Md5(reqdata.Password)
		userdata, _ := server.XDb().Table("x_user").Where("UserId = ?", token.UserId).First()
		if reqdata.Password != userdata.String("WalletPassword") {
			ctx.RespErr(errors.New("密码不正确"), &errcode)
			return
		}
		// 检查用户余额是否被冻结
		frozen, message := common.CheckUserFrozen(token.UserId)
		if frozen {
			logs.Error("提现失败  玩家账号余额被冻结 userId=", token.UserId, " message=", message)
			//errcode = 201
			ctx.RespErrString(true, &errcode, "当前账号暂时无法提款，请联系客服")
			return
		}

		// 检查用户所有活动的提现流水要求
		if err := active.CheckAllActiveWithdrawable(token.UserId); err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		if reqdata.PIXType == "PIXType" && !isValidEmail(reqdata.PIXAccount) {
			ctx.RespErr(errors.New("邮箱格式不正确"), &errcode)
			return
		}
		if reqdata.PIXType == "PHONE" {
			if len(reqdata.PIXAccount) != 11 {
				ctx.RespErr(errors.New("手机号不正确"), &errcode)
				return
			}
			_, err := strconv.Atoi(reqdata.PIXAccount)
			if err != nil {
				ctx.RespErr(errors.New("手机号不正确"), &errcode)
				return
			}
		}
		realamount := int(reqdata.Amount * rate)
		PayType := 2
		if paymethod.String("Brand") == "hambit" && paymethod.String("Name") == "hambit" {
			PayType = 6
		}
		if paymethod.String("Brand") == "ebpay" && paymethod.String("Name") == "ebpay" {
			if reqdata.WalletAddress == "" {
				ctx.RespErr(errors.New("请输入提款地址"), &errcode)
				return
			}
			PayType = 4
		}
		if paymethod.String("Brand") == "pix" && paymethod.String("Name") == "pix" {
			if reqdata.RealName == "" || reqdata.CPFNo == "" || reqdata.PIXType == "" || reqdata.PIXAccount == "" {
				ctx.RespErr(errors.New("PIX 参数错误"), &errcode)
				return
			}
			if reqdata.PIXType != "PHONE" && reqdata.PIXType != "EMAIL" && reqdata.PIXType != "CPF" && reqdata.PIXType != "CNPJ" && reqdata.PIXType != "RANDOM" {
				ctx.RespErr(errors.New("PIXType 参数错误 "), &errcode)
				return
			}
			PayType = 2
		}
		if paymethod.String("Brand") == "pay24" && paymethod.String("Name") == "pay24" {
			PayType = 7
			if reqdata.Symbol == "hkd" {
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "php" {
				if reqdata.PhoneNum == "" {
					ctx.RespErr(errors.New("手机号不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "sgd" {
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "thb" {
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "idr" {
				// 印尼
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
					return
				}
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "inr" {
				// 印度
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
					return
				}
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "mxn" {
				// 墨西哥
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行代码不能为空"), &errcode)
					return
				}
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "myr" {
				// 马来
				if reqdata.BankCode == "" {
					ctx.RespErr(errors.New("银行编码不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "krw" {
				// 韩国
				if reqdata.BankName == "" {
					ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
					return
				}
				if reqdata.BankNo == "" {
					ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
					return
				}
				if reqdata.RealName == "" {
					ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
					return
				}
			} else if reqdata.Symbol == "brl" {
				// 巴西
				if reqdata.RealName == "" || reqdata.CPFNo == "" || reqdata.PIXType == "" || reqdata.PIXAccount == "" {
					ctx.RespErr(errors.New("PIX 参数错误"), &errcode)
					return
				}
				if reqdata.PIXType != "PHONE" && reqdata.PIXType != "EMAIL" && reqdata.PIXType != "CPF" && reqdata.PIXType != "CNPJ" && reqdata.PIXType != "RANDOM" {
					ctx.RespErr(errors.New("PIXType 参数错误 "), &errcode)
					return
				}
			} else if paymethod.String("Brand") == "pay24" && paymethod.String("Name") == "pay24" && reqdata.Symbol == "cny" && paymethod.String("WPayType") == "EB" {
				// 24pay CNY EB类型提现
				if reqdata.WalletAddress == "" {
					ctx.RespErr(errors.New("EB钱包地址不能为空"), &errcode)
					return
				}
			}
		}
		if paymethod.String("Brand") == "epay" && paymethod.String("Name") == "epay" {
			PayType = 10
		}
		if paymethod.String("Brand") == "dgpay" && paymethod.String("Name") == "dgpay" {
			PayType = 11
		}
		if paymethod.String("Brand") == "lpay" && paymethod.String("Name") == "lpay" {
			PayType = 13
		}
		if paymethod.String("Brand") == "richway" && paymethod.String("Name") == "richway" {
			PayType = 14
		}
		if paymethod.String("Brand") == "btcash" && paymethod.String("Name") == "btcash" {
			PayType = 15
		}
		if paymethod.String("Brand") == "powerpay" && paymethod.String("Name") == "powerpay" {
			PayType = 16
		}
		if paymethod.String("Brand") == "mg99" && paymethod.String("Name") == "mg99" {
			PayType = 17
		}
		if paymethod.String("Brand") == "being" && paymethod.String("Name") == "being" {
			PayType = 18
		}
		if paymethod.String("Brand") == "feibaopay" && paymethod.String("Name") == "feibaopay" {
			PayType = 19
		}
		if paymethod.String("Brand") == "ytpay" && paymethod.String("Name") == "ytpay" {
			PayType = 20
		}
		if paymethod.String("Brand") == "expay" && paymethod.String("Name") == "expay" {
			PayType = 21
		}
		if paymethod.String("Brand") == "praxispay" && paymethod.String("Name") == "praxispay" {
			PayType = 22
		}
		if paymethod.String("Brand") == "u2cpay" && paymethod.String("Name") == "u2cpay" {
			PayType = 23
			//如果币种是巴西币
			if strings.ToLower(reqdata.Symbol) == "brl" {
				//判断账户类型必须有accountType: PIX_EMAIL, PIX_PHONE, PIX_CPF, PIX_CNPJ, PIX_BANK
				if reqdata.PIXType == "" {
					ctx.RespErr(errors.New("账户类型不能为空"), &errcode)
					return
				}
				if reqdata.PIXType != "PIX_EMAIL" && reqdata.PIXType != "PIX_PHONE" && reqdata.PIXType != "PIX_CPF" && reqdata.PIXType != "PIX_CNPJ" && reqdata.PIXType != "PIX_BANK" {
					ctx.RespErr(errors.New("账户类型错误"), &errcode)
					return
				}
			}
		}
		if paymethod.String("Brand") == "rmpay" && paymethod.String("Name") == "rmpay" {
			PayType = 24
			// 添加 rmpay 提现验证
			if reqdata.BankNo == "" {
				ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
				return
			}
			if reqdata.BankName == "" {
				ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
				return
			}
			if reqdata.BranchName == "" {
				ctx.RespErr(errors.New("支行名称不能为空"), &errcode)
				return
			}
		}
		if paymethod.String("Brand") == "mainpay" && paymethod.String("Name") == "mainpay" {
			PayType = 25
			// 添加 MainPay 提现验证
			if reqdata.BankNo == "" {
				ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
				return
			}
			if reqdata.BankName == "" {
				ctx.RespErr(errors.New("银行名称不能为空"), &errcode)
				return
			}
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("持卡人姓名不能为空"), &errcode)
				return
			}
		}
		if paymethod.String("Brand") == "cpay" && paymethod.String("Name") == "cpay" {
			PayType = 26
		}
		if paymethod.String("Brand") == "pay99" && paymethod.String("Name") == "pay99" {
			PayType = 27
		}
		if paymethod.String("Brand") == "alipay" && paymethod.String("Name") == "alipay" {
			PayType = 28
			// alipay提现参数验证
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return
			}
			if reqdata.AlipayAccount == "" {
				ctx.RespErr(errors.New("alipay账号不能为空"), &errcode)
				return
			}
		}
		if paymethod.String("Brand") == "safegatepay" && paymethod.String("Name") == "safegatepay" {
			PayType = 30
			// SafeGatePay提现参数验证
			if reqdata.RealName == "" {
				ctx.RespErr(errors.New("姓名不能为空"), &errcode)
				return
			}
			if reqdata.BankNo == "" {
				ctx.RespErr(errors.New("银行卡号不能为空"), &errcode)
				return
			}
		}
		if paymethod.String("Brand") == "bnnpay" && paymethod.String("Name") == "bnnpay" {
			PayType = 29
		}

		agent, _ := server.XDb().Table("x_agent_independence").Where("UserId = ?", token.UserId).First()
		SpecialAgent := 2
		if agent != nil {
			SpecialAgent = 1
		}

		ChannelId, _ := server.GetChannel(ctx, ctx.Host())

		bytes, _ := json.Marshal(reqdata)
		var orderId interface{}
		err := server.XDb().Transaction2(func(tx *xgo.XTx) error {
			// 总流水是否大于提现流水
			if userdata.Float64("TotalLiuSui") < userdata.Float64("WithdrawLiuSui") {
				return errors.New("流水不足,无法提款")
			}
			r, err := tx.Tx.Exec("update x_user set Amount = Amount - ?,WithdrawCount=IFNULL(WithdrawCount,0)+1,WithdrawLiuSui = 0,TotalLiuSui = 0,CaijinLiuSui = 0 where UserId = ? and Amount > ?", reqdata.Amount, token.UserId, reqdata.Amount)
			if err != nil {
				logs.Error("update x_user err: ", err)
				return err
			}
			row, err := r.RowsAffected()
			if err != nil {
				return err
			}
			if row == 0 {
				return errors.New("余额不足")
			}
			lastWithdrawTime := userdata.String("LastWithdrawTime")
			if lastWithdrawTime == "" {
				lastWithdrawTime = userdata.String("RegisterTime")
			}
			logs.Info("lastWithdrawTime: ", lastWithdrawTime)
			lastWithdrawFinishTime := userdata.String("LastWithdrawFinishTime")
			if lastWithdrawFinishTime == "" {
				lastWithdrawFinishTime = userdata.String("RegisterTime")
			}
			logs.Info("lastWithdrawFinishTime: ", lastWithdrawFinishTime)
			// 2024-07-31T12:59:05Z
			lastWithdrawTimeP := carbon.Parse(lastWithdrawTime).StdTime()
			lastWithdrawFinishTimeP := carbon.Parse(lastWithdrawFinishTime).StdTime()
			withdrawOrder := xgo.H{
				"SellerId":               token.SellerId,
				"ChannelId":              token.ChannelId,
				"BetChannelId":           ChannelId,
				"UserId":                 token.UserId,
				"Symbol":                 reqdata.Symbol,
				"PayId":                  reqdata.MethodId,
				"PayType":                PayType,
				"Amount":                 reqdata.Amount,
				"RealAmount":             realamount,
				"TransferRate":           rate,
				"State":                  0,
				"CSGroup":                userdata.Int("CSGroup"),
				"CSId":                   userdata.Int("CSId"),
				"SpecialAgent":           SpecialAgent,
				"PayData":                string(bytes),
				"Account":                userdata.String("Account"),
				"TopAgentId":             userdata.Int("TopAgentId"),
				"Net":                    "",
				"WithdrawCount":          userdata.Int("WithdrawCount") + 1,
				"WithdrawSuccessCount":   userdata.Int("WithdrawSuccessCount"),
				"LastWithdrawTime":       lastWithdrawTimeP,
				"LastWithdrawFinishTime": lastWithdrawFinishTimeP,
			}
			// 根据支付方式设置Address字段
			if paymethod.String("Brand") == "alipay" && paymethod.String("Name") == "alipay" {
				withdrawOrder["Address"] = reqdata.AlipayAccount
			} else if paymethod.String("Brand") == "pay24" && paymethod.String("Name") == "pay24" && reqdata.Symbol == "cny" && paymethod.String("WPayType") == "EB" {
				// 24pay CNY-EB类型提现使用WalletAddress
				withdrawOrder["Address"] = reqdata.WalletAddress
			} else {
				switch reqdata.Symbol {
				case "php":
					withdrawOrder["Address"] = reqdata.PhoneNum
				case "brl":
					withdrawOrder["Address"] = reqdata.PIXAccount
				default:
					withdrawOrder["Address"] = reqdata.BankNo
				}
			}

			orderId, err = tx.Table("x_withdraw").Insert(withdrawOrder)
			if err != nil {
				logs.Error("add x_withdraw err: ", err)
				return err
			}
			tx.Table("x_amount_change_log").Insert(xgo.H{
				"SellerId":     userdata.Int("SellerId"),
				"ChannelId":    userdata.Int("ChannelId"),
				"UserId":       userdata.Int("UserId"),
				"BeforeAmount": userdata.Float64("Amount"),
				"Amount":       -reqdata.Amount,
				"AfterAmount":  userdata.Float64("Amount") - float64(reqdata.Amount),
				"Reason":       2,
				"Memo":         fmt.Sprint(orderId),
			})
			ctx.Put("Amount", userdata.Float64("Amount")-float64(reqdata.Amount))
			ctx.Put("OrderId", orderId)

			return nil
		})

		// 事务提交后，向数据分析平台上报事件
		if err == nil && orderId != nil {
			user, err := c.getUser(token.UserId)
			if err != nil {
				logs.Error("获取用户信息失败:", err.Error())
			} else {
				// 获取提现订单
				withdrawOrder, err := c.getWithdrawOrder(int(orderId.(int64)))
				if err != nil {
					logs.Error("获取提现订单失败:", err.Error())
				} else {
					// 向数据分析平台上报提款创建事件
					tokenData := &server.TokenData{
						UserId: int(user.UserID),
					}
					reqDataStruct := struct {
						Net      string  `validate:"required"`
						Address  string  `validate:"required"`
						Amount   float64 `validate:"required"`
						Password string
						Symbol   string
					}{
						Net:      "法币提现",                // 使用默认值
						Address:  reqdata.WalletAddress, // 使用钱包地址
						Amount:   reqdata.Amount,
						Password: reqdata.Password,
						Symbol:   reqdata.Symbol,
					}
					withdrawData := map[string]interface{}{
						"Id": withdrawOrder.ID,
					}
					datapush.SendWithdrawCreateEvent(tokenData, reqDataStruct, withdrawData)
				}
			}
		}

		// 处理事务错误
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}

		ctx.RespOK()
	}
}
