package msg

import (
	"encoding/json"
	"github.com/beego/beego/logs"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/server"
)

// UserMessageController 用户站内信控制器
type UserMessageController struct {
}

// Init 初始化路由
func (c *UserMessageController) Init() {
	group := server.Http().NewGroup("/api")
	{
		// 用户端接口
		group.Post("/user/messages", c.GetUserMessages)            // 获取用户消息列表
		group.Post("/user/message/read", c.MarkMessageRead)        // 标记消息为已读
		group.Post("/user/message/delete", c.DeleteUserMessage)    // 假删除消息
		group.Post("/user/message/unread_count", c.GetUnreadCount) // 获取未读消息记录数

		group.Post("/user/message/restore", c.RestoreUserMessage) // 恢复已删除消息
		group.Post("/trigger_by_type", c.TriggerMessageByType)    // 根据业务类型触发消息
	}
}

// GetUserMessages 获取用户站内信列表
func (c *UserMessageController) GetUserMessages(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Page     int    `json:"page"`
		PageSize int    `json:"pageSize"`
		IsRead   int    `json:"isRead"` // -1:全部, 0:未读, 1:已读
		Lang     string `json:"lang"`   // 1：中文 2：英文 请求的语言，如不提供则使用用户默认语言
	}

	var reqData RequestData
	errcode := 0
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		ctx.RespErrString(true, &errcode, "解析请求数据失败")
		return
	}

	// 获取用户ID（临时注释）
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, nil, "未登录")
		return
	}
	// 如果token为nil，设置一个模拟的用户ID用于测试
	//if token == nil {
	//	token = &server.TokenData{UserId: 8127}
	//}

	// 创建消息管理服务
	messageManageService := NewMessageManageService()

	// 获取用户消息列表
	messages, total, err := messageManageService.GetUserMessages(int64(token.UserId), reqData.Page, reqData.PageSize)
	if err != nil {
		logs.Error("获取用户消息列表失败: %v", err)
		ctx.RespErrString(true, &errcode, "获取用户消息列表失败")
		return
	}

	// 将消息转换为map格式
	var result []map[string]interface{}
	for _, msg := range messages {
		msgMap := map[string]interface{}{
			"Id":          msg.ID,
			"UserId":      msg.UserID,
			"Title":       msg.Title,
			"Type":        msg.Type,
			"GameType":    msg.GameType,
			"Content":     msg.Content,
			"TitleLang":   msg.TitleLang,
			"ContentLang": msg.ContentLang,
			"IsRead":      msg.IsRead,
			"IsDeleted":   msg.IsDeleted,
			"SentAt":      msg.SentAt,
			"ReadAt":      msg.ReadAt,
			"DeletedAt":   msg.DeletedAt,
		}

		// 添加游戏类型相关信息
		//msgMap["GameTypeName"] = GetGameTypeName(msg.GameType)
		//msgMap["CanJumpToGame"] = msg.GameType != "" && msg.GameType != "0" // 只有选择了游戏类型才能跳转
		//msgMap["JumpUrl"] = GetGameJumpUrl(msg.GameType)

		// 处理语言
		requestLang := reqData.Lang
		//if requestLang == "" {
		//	// 使用用户默认语言
		//	where := abugo.AbuDbWhere{}
		//	where.Add("and", "UserID", "=", token.UserId, 0)
		//	users, err := server.Db().Table("x_user").Where(where).GetList()
		//	if err == nil && len(*users) > 0 {
		//		user := (*users)[0]
		//		requestLang = abugo.GetStringFromInterface(user["Lang"])
		//	} else {
		//		requestLang = "2" // 默认使用英文
		//	}
		//}

		langId := requestLang
		// 处理多语言标题和内容
		titleLangJSON := msg.TitleLang
		contentLangJSON := msg.ContentLang

		var titleLangMap, contentLangMap map[string]string
		json.Unmarshal([]byte(titleLangJSON), &titleLangMap)
		json.Unmarshal([]byte(contentLangJSON), &contentLangMap)

		// 如果请求的语言存在，则使用该语言的标题和内容
		if title, ok := titleLangMap[langId]; ok {
			msgMap["Title"] = title
		} else {
			// 如果请求的语言不存在
			if langId == "1" || langId == "13" {
				// 如果是中文或繁体中文，使用中文
				if title, ok := titleLangMap["1"]; ok {
					msgMap["Title"] = title
				}
			} else {
				// 其他语言默认使用英文
				if title, ok := titleLangMap["2"]; ok {
					msgMap["Title"] = title
				}
			}
		}

		if content, ok := contentLangMap[langId]; ok {
			msgMap["Content"] = content
		} else {
			// 如果请求的语言不存在
			if langId == "1" || langId == "13" {
				// 如果是中文或繁体中文，使用中文
				if content, ok := contentLangMap["1"]; ok {
					msgMap["Content"] = content
				}
			} else {
				// 其他语言默认使用英文
				if content, ok := contentLangMap["2"]; ok {
					msgMap["Content"] = content
				}
			}
		}

		result = append(result, msgMap)
	}

	ctx.Put("data", result)
	ctx.Put("total", total)
	ctx.RespOK()
}

// MarkMessageRead 标记站内信为已读
func (c *UserMessageController) MarkMessageRead(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ID string `json:"Ids" validate:"required"` // 修改为string类型，支持逗号分隔的多个ID
	}

	var reqData RequestData
	errcode := 0
	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v", err)
		ctx.RespErrString(true, &errcode, "解析请求数据失败")
		return
	}

	// 获取用户ID（临时注释）
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, nil, "未登录")
		return
	}
	//// 如果token为nil，设置一个模拟的用户ID用于测试
	//if token == nil {
	//	token = &server.TokenData{UserId: 10002877}
	//}

	// 创建消息管理服务
	messageManageService := NewMessageManageService()

	// 解析ID字符串，支持逗号分隔的多个ID
	idStr := reqData.ID
	var messageIds []int64

	// 检查是否包含逗号，如果包含则分割处理
	if strings.Contains(idStr, ",") {
		// 分割字符串
		idStrings := strings.Split(idStr, ",")
		for _, idString := range idStrings {
			// 去除空白字符
			idString = strings.TrimSpace(idString)
			if idString == "" {
				continue
			}

			// 转换为int64
			id, err := strconv.ParseInt(idString, 10, 64)
			if err != nil {
				logs.Error("无效的消息ID: %s, %v", idString, err)
				continue
			}
			messageIds = append(messageIds, id)
		}
	} else {
		// 单个ID的情况
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			logs.Error("无效的消息ID: %s, %v", idStr, err)
			ctx.RespErrString(true, &errcode, "无效的消息ID")
			return
		}
		messageIds = append(messageIds, id)
	}

	// 检查是否有有效的ID
	if len(messageIds) == 0 {
		ctx.RespErrString(true, &errcode, "没有有效的消息ID")
		return
	}

	// 根据ID数量选择不同的处理方法
	var err error
	if len(messageIds) == 1 {
		// 单个ID使用原有方法
		err = messageManageService.MarkMessageAsRead(messageIds[0], int64(token.UserId))
	} else {
		// 多个ID使用新方法
		err = messageManageService.MarkMultipleMessagesAsRead(messageIds, int64(token.UserId))
	}

	if err != nil {
		logs.Error("标记已读失败: %v", err)
		errcode := 0
		ctx.RespErrString(true, &errcode, "标记已读失败")
		return
	}

	ctx.RespOK()
}

// GetUnreadCount 获取未读站内信数量
func (c *UserMessageController) GetUnreadCount(ctx *abugo.AbuHttpContent) {
	// 获取用户ID（临时注释）
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, nil, "未登录")
		return
	}
	// 如果token为nil，设置一个模拟的用户ID用于测试
	//if token == nil {
	//	token = &server.TokenData{UserId: 8127}
	//}

	// 创建消息管理服务
	messageManageService := NewMessageManageService()

	// 获取未读消息数量
	count, err := messageManageService.GetUserUnreadCount(int64(token.UserId))
	if err != nil {
		logs.Error("查询未读消息数量失败: %v", err)
		errcode := 0
		ctx.RespErrString(true, &errcode, "查询未读消息数量失败")
		return
	}

	ctx.Put("count", count)
	ctx.RespOK()
}

// DeleteUserMessage 假删除用户消息
func (c *UserMessageController) DeleteUserMessage(ctx *abugo.AbuHttpContent) {
	// 解析请求数据
	var reqData struct {
		ID int64 `json:"Id" validate:"required"`
	}

	// 获取用户ID（临时注释）
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, nil, "未登录")
		return
	}

	//// 如果token为nil，设置一个模拟的用户ID用于测试
	//if token == nil {
	//	token = &server.TokenData{UserId: 8127}
	//}

	errcode := 0
	if ctx.RespErr(ctx.RequestData(&reqData), &errcode) {
		return
	}

	// 创建消息管理服务
	messageManageService := NewMessageManageService()

	// 删除消息
	err := messageManageService.DeleteUserMessage(reqData.ID, int64(token.UserId))
	if ctx.RespErr(err, &errcode) {
		return
	}

	ctx.RespOK()
}

// RestoreUserMessage 恢复已删除的用户消息
func (c *UserMessageController) RestoreUserMessage(ctx *abugo.AbuHttpContent) {
	// 解析请求数据
	var reqData struct {
		ID     int64 `json:"Id" validate:"required"`
		UserID int64 `json:"UserId" validate:"required"`
	}

	errcode := 0
	if ctx.RespErr(ctx.RequestData(&reqData), &errcode) {
		return
	}

	// 创建消息管理服务
	messageManageService := NewMessageManageService()

	// 恢复消息
	err := messageManageService.RestoreUserMessage(reqData.ID, reqData.UserID)
	if ctx.RespErr(err, &errcode) {
		return
	}

	ctx.RespOK()
}

// TriggerMessageByType 根据业务类型触发发送站内信
func (c *UserMessageController) TriggerMessageByType(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 解析请求数据
	var reqData struct {
		Type      string                 `json:"Type"`      //  业务类型，如 "1"=领取彩金提醒, "2"=活动参与提醒
		UserId    int                    `json:"UserId"`    // 用户ID
		Amount    float64                `json:"Amount"`    // 金额 自动替换变量金额
		Variables map[string]interface{} `json:"Variables"` // 扩展变量数据
	}

	if err := ctx.RequestData(&reqData); err != nil {
		logs.Error("解析请求数据失败: %v ", err)
		ctx.RespErrString(true, &errcode, "解析请求数据失败")
		return
	}

	// 验证必填参数
	if reqData.Type == "" {
		ctx.RespErrString(true, &errcode, "业务类型不能为空")
		return
	}

	if reqData.UserId <= 0 {
		ctx.RespErrString(true, &errcode, "用户ID无效")
		return
	}

	// 创建消息发送服务
	messageSendService := NewMessageSendService()

	// 将金额添加到变量中
	if reqData.Variables == nil {
		reqData.Variables = make(map[string]interface{})
	}

	// 根据消息类型设置对应的变量名
	switch reqData.Type {
	case TemplateTypeBonusReminder:
		reqData.Variables["{彩金金额}"] = reqData.Amount
	case TemplateTypeDepositReminder:
		reqData.Variables["{充值金额}"] = reqData.Amount
	case TemplateTypeWithdrawSuccess:
		reqData.Variables["{提现金额}"] = reqData.Amount
	default:
		// 默认使用通用变量 名
		reqData.Variables["{金额}"] = reqData.Amount
	}

	// 触发发送消息
	err := messageSendService.SendMessageByType(reqData.Type, reqData.UserId, reqData.Variables)
	if err != nil {
		logs.Error("触发发送消息失败: %v", err)
		ctx.RespErrString(true, &errcode, "触发发送消息失败: "+err.Error())
		return
	}

	ctx.RespOK()
}

// getGameTypeName 根据游戏类型ID获取游戏类型名称
func getGameTypeName(gameType int) string {
	switch gameType {
	case 1:
		return "电子"
	case 2:
		return "棋牌"
	case 3:
		return "趣味"
	case 4:
		return "彩票"
	case 5:
		return "真人"
	case 6:
		return "体育"
	case 7:
		return "真人"
	case 0:
		return "未选择"
	default:
		return "未知"
	}
}

// getGameJumpUrl 根据游戏类型获取跳转URL
func getGameJumpUrl(gameType int) string {
	switch gameType {
	case 1:
		return "/game/electronic" // 电子游戏页面
	case 2:
		return "/game/chess" // 棋牌游戏页面
	case 3:
		return "/game/fun" // 趣味游戏页面
	case 4:
		return "/game/lottery" // 彩票游戏页面
	case 5:
		return "/game/live" // 真人游戏页面
	case 6:
		return "/game/sport" // 体育游戏页面
	case 7:
		return "/game/live" // 真人游戏页面（与5相同）
	case 0:
		return "" // 未选择游戏类型，不跳转
	default:
		return ""
	}
}
