package active

import (
	"time"

	"github.com/shopspring/decimal"
)

type SpinConfig struct {
	Round []struct {
		InviteCount int   `json:"invitecount"`
		Recharge    int   `json:"recharge"`
		RandRate    []int `json:"randrate"`
		ExpireTime  int   `json:"expiretime"`
	} `json:"round"`
	SpinData []struct {
		Id   int    `json:"id"`
		Name string `json:"name"`
		Type int    `json:"type"`
		Data []int  `json:"data"`
	} `json:"spindata"`
}

type DepositBaseConfig struct {
	IsBindEmail        bool            `json:"IsBindEmail"`        // 是否需要绑定邮箱
	IsDuringReg        bool            `json:"IsDuringReg" `       // 是否活动期间内注册的账号
	IsValidWallet      bool            `json:"IsValidWallet"`      // 是否有效的钱包地址
	IsDeviceLimit      bool            `json:"IsDeviceLimit"`      // 同设备号是否可参与
	MaxIPAttempts      int             `json:"MaxIPAttempts"`      // 同一IP最大领取次数，0表示不限制
	MaxIDAttempts      int             `json:"MaxIDAttempts"`      // 同ID最大领取次数，0表示不限制
	MaxDailyIDAttempts int             `json:"MaxDailyIDAttempts"` // 同ID每日最大领取次数，0表示不限制
	BlockedIPList      string          `json:"BlockedIPList"`      // 被限制参与的IP，多个IP用逗号分隔
	RegisterDay        int32           `json:"RegisterDay"`        // 注册天数限制
	ReceiveDay         int32           `json:"ReceiveDay"`         // 领取天数限制
	RechargeCount      int32           `json:"RechargeCount"`      // 充值次数要求
	ActivityMethod     int32           `json:"ActivityMethod"`     // 活动方式：1=前置（先领奖励后打流水），2=后置（先打流水再领奖励)
	MinBetAmount       decimal.Decimal `json:"MinBetAmount"`       // 最小投注金额限制
	MaxBetAmount       decimal.Decimal `json:"MaxBetAmount"`       // 最大投注金额限制
	BetType            int32           `json:"BetType"`            // 打码条件：1=真金，2=彩金,3=彩金+真金
	IsCalcActiveWager  bool            `json:"IsCalcActiveWager"`  // 玩家参与此活动所产生的流水是否纳入会员返水及代理返佣统计，由前端传递
	RewardWalletType   int32           `json:"RewardWalletType"`   // 奖励账户类型，0=真金账户，1=彩金账户
	TotalRewardLimit   decimal.Decimal `json:"TotalRewardLimit"`   // 总派发金额上限
	DailyRewardLimit   decimal.Decimal `json:"DailyRewardLimit"`   // 每日派发金额上限
	IsCountdown        bool            `json:"IsCountdown"`        // 是否倒计时
	CountdownType      int32           `json:"CountdownType"`      // 倒计时类型 1=小时 2=分钟
	CountdownValue     int32           `json:"CountdownValue"`     // 倒计时数值
}

// DepositConfig 首充活动档位配置
type DepositConfig struct {
	ID                   int32           `json:"Id"`                   // 档位ID，从1开始递增
	FirstChargeUstdLimit decimal.Decimal `json:"FirstChargeUstdLimit"` // 首次单笔最低充值(U)
	LiushuiMultiple      decimal.Decimal `json:"LiushuiMultiple"`      // 真金流水倍数
	BonusMultiple        decimal.Decimal `json:"BonusMultiple"`        // 彩金流水倍数
	GiveAmount           float32         `json:"GiveAmount"`           // 赠送金额
	GiveProportion       decimal.Decimal `json:"GiveProportion"`       // 赠送比例
	GiveLimit            decimal.Decimal `json:"GiveLimit"`            // 赠送上限(U)
}

// AwardData 彩金钱包下的充值活动奖励配置数据
type DepositAwardData struct {
	AwardBetType int `json:"AwardBetType"` // 奖励打码倍数类型 1: 真金 2: 彩金 3: 彩金+真金
	// 激活期限配置
	ActivationType           int   `json:"ActivationType"`           // 激活期限类型 1 无期限 2 倒计时 3 自定义
	ActivetionCountdownCount int   `json:"ActivetionCountdownCount"` // 激活期限，倒计时数值
	ActivetionCountdownType  int   `json:"ActivetionCountdownType"`  // 激活期限 倒计时类型 1 天 2 小时
	ActivetionDate           int64 `json:"ActivetionDate"`           // 激活期限 自定义结束时间(毫秒)

	// 流水完成期限配置
	TurnoverType           int   `json:"TurnoverType"`           // 流水完成期限类型 1 无期限 2 倒计时 3 自定义
	TurnoverCountdownCount int   `json:"TurnoverCountdownCount"` // 流水完成期限，倒计时数值
	TurnoverCountdownType  int   `json:"TurnoverCountdownType"`  // 流水完成期限 倒计时类型 1 天 2 小时
	TurnoverDate           int64 `json:"TurnoverDate"`           // 流水完成期限 自定义结束时间(毫秒)

	// 领取期限配置
	ReceiveType           int   `json:"ReceiveType"`           // 领取期限类型 1 无期限 2 倒计时 3 自定义
	ReceiveCountdownCount int   `json:"ReceiveCountdownCount"` // 领取期限，倒计时数值
	ReceiveCountdownType  int   `json:"ReceiveCountdownType"`  // 领取期限 倒计时类型 1 天 2 小时
	ReceiveDate           int64 `json:"ReceiveDate"`           // 领取期限 自定义结束时间(毫秒)

	// 投注限制配置
	MinBetAmount int `json:"MinBetAmount"` // 奖励单笔投注限制 最小值
	MaxBetAmount int `json:"MaxBetAmount"` // 奖励单笔投注限制 最大值

	// 游戏限制配置
	LimitGameType     int    `json:"LimitGameType"`     // 奖励投注游戏限制 1 不限制 2 限制
	AwardVenueCfg     string `json:"AwardVenueCfg"`     // LimitGameType==2时，可以计算打码的游戏配置，json字符串
	LimitGameIds      string `json:"LimitGameIds"`      // 被限制参与的游戏ID列表 (需要特殊限制的游戏ID，限制后这些游戏不参与活动打码)
	LimitMaxWinAmount int    `json:"LimitMaxWinAmount"` // 奖励投注最高盈利金额限制

	// 其他配置
	IsCalcAwardValid int `json:"IsCalcAwardValid"` // 奖励投注金额是否计入有效流水 1: 是 0: 否

	// 奖励配置数组
	AwardConfig []RechargeAwardConfig `json:"AwardConfig"` // 奖励配置列表，包含多个档位的配置
}

// RechargeAwardConfig 奖励配置结构体 对应界面中的奖励配置表格，包含档位、充值要求、流水倍数、赠送配置等
type RechargeAwardConfig struct {
	ID                   int32   `json:"Id"`                   // 档位ID，从1开始递增
	FirstChargeUstdLimit float32 `json:"FirstChargeUstdLimit"` // 首次单笔最低充值(U)
	LiushuiMultiple      int32   `json:"LiushuiMultiple"`      // 真金流水倍数
	BonusMultiple        int32   `json:"BonusMultiple"`        // 彩金流水倍数
	GiveProportion       float32 `json:"GiveProportion"`       // 赠送比例
	GiveAmount           float32 `json:"GiveAmount"`           // 赠送金额
	GiveLimit            float32 `json:"GiveLimit"`            // 赠送上限(U)
	MaxWinLimit          float32 `json:"MaxWinLimit"`          // 奖励投注最高盈利限制
}

type FirstDepositGiftLiuSui struct {
	LiuSuiUsdt      decimal.Decimal
	LiuSuiLottery   decimal.Decimal
	LiuSuiQiPai     decimal.Decimal
	LiuSuiDianZhi   decimal.Decimal
	LiuSuiXiaoYouXi decimal.Decimal
	LiuSuiLive      decimal.Decimal
	LiuSuiSport     decimal.Decimal
	LiuSuiTexas     decimal.Decimal
}

type CumulativeWeeklyRechargeConfig struct {
	ID             int32           `json:"Id"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
	GiveAmount     decimal.Decimal `json:"GiveAmount"`
}

type CumulativeWeeklyRechargeParam struct {
	ID             int32           `json:"Id"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
	GiveAmount     decimal.Decimal `json:"GiveAmount"`
	State          int8            `json:"State"`
}

type CumulativeWeeklyRechargeRes struct {
	TotalRecharge float64                         `json:"TotalRecharge"`
	List          []CumulativeWeeklyRechargeParam `json:"List"`
	Seconds       float64                         `json:"Seconds"`
}

type RealAmountLiuSui struct {
	RealAmount decimal.Decimal
}

type SignRewardBaseConfig struct {
	MixBetLimit decimal.Decimal `json:"MixBetLimit"`
	TrxPrice    decimal.Decimal `json:"TrxPrice"`
	RemakeDay   int8            `json:"RemakeDay"`
}

type SignRewardConfig struct {
	ID               int32           `json:"Id"`
	SignDay          int32           `json:"SignDay"`
	Award            decimal.Decimal `json:"Award"`
	AdditionalReward decimal.Decimal `json:"AdditionalReward"`
}

type SignRewardRes struct {
	FirstSignTime string          `json:"FirstSignTime"`
	LastSignTime  string          `json:"LastSignTime"`
	Days          int64           `json:"Days"`
	RewardAmount  decimal.Decimal `json:"RewardAmount"`
	IsTodaySign   uint8           `json:"IsTodaySign"` // 今天是否签到
}

type SignRewardSum struct {
	RecordID     int64           `json:"RecordID" gorm:"column:RecordId"`
	RewardAmount decimal.Decimal `json:"RewardAmount"`
}

type SignRewardLiuSui struct {
	LiuSuiUsdt      decimal.Decimal
	LiuSuiLottery   decimal.Decimal
	LiuSuiQiPai     decimal.Decimal
	LiuSuiDianZhi   decimal.Decimal
	LiuSuiXiaoYouXi decimal.Decimal
	LiuSuiLive      decimal.Decimal
	LiuSuiSport     decimal.Decimal
	LiuSuiTexas     decimal.Decimal
	LiuSuiTrx       decimal.Decimal
}

type CustomUsdtLiuSui struct {
	LiuSuiUsdt decimal.Decimal
}
type CustomTrxLiuSui struct {
	LiuSuiTrx decimal.Decimal
}

type SaveActiveDataInfo struct {
	AuditType         int
	SellerID          int32
	ChannelID         int32
	ActiveId          int
	Level             int
	RealAmount        float64
	TotalLiushui      float64
	WithdrawLiuSuiAdd float64
	ActiveName        string
	ActiveMemo        string
	BalanceCReason    int
	ConfigStr         []byte
	BastConfigStr     []byte
	FirstRecharge     float64
	TotalRecharge     float64
	FirstSignTime     *time.Time
	LastSignTime      *time.Time
	InviteRewardType  int32 // 邀请好友奖励分类 0其他 1单人奖励 2额外奖励
	ChildUserID       int32
	InviteRewardUsers int32 // 邀请好友额外奖励用户数量
	OrderId           int32
	GameType          string // 游戏分类
	MinLiuShui        decimal.Decimal
	UserIP            string // 用户IP
	DeviceID          string // 设备ID
	RedeemCode        string // 本次使用的兑换码
}

// 推荐好友多重奖励
type RecommendFriendRewardBaseConfig struct {
	RegisterDay          int32           `json:"RegisterDay"`          // 注册天数
	FirstChargeUstdLimit decimal.Decimal `json:"FirstChargeUstdLimit"` // 首次存款
	Level                int32           `json:"Level"`                // vip等级
	Award                decimal.Decimal `json:"Award"`                // 单次奖励
}

type RecommendFriendRewardConfig struct {
	ID               int32           `json:"Id"`
	TotalMin         int32           `json:"TotalMin"`         // 积累邀请人数最小
	TotalMax         int32           `json:"TotalMax"`         // 积累邀请人数最大
	AdditionalReward decimal.Decimal `json:"AdditionalReward"` // 额外奖励
}

type RecommendFriendRewardRes struct {
	Num          int32           `json:"Num"`
	RewardAmount decimal.Decimal `json:"RewardAmount"`
	AgentCode    string          `json:"AgentCode"`
}

type RecommendFriendRewardSum struct {
	Amount decimal.Decimal `json:"Amount"`
}

type BreakThroughConfig struct {
	ID          int32           `json:"Id"`
	LimitValue  decimal.Decimal `json:"LimitValue"`
	RewardValue decimal.Decimal `json:"RewardValue"`
}
type BreakThroughBaseConfig struct {
	TrxPrice       float32         `json:"TrxPrice"`
	RechargeAmount decimal.Decimal `json:"RechargeAmount"`
}

type BreakThroughLiuShui struct {
	LiuSui decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`
}

type WeekendBreakThroughParam struct {
	ID          int32           `json:"Id"`
	LimitValue  decimal.Decimal `json:"LimitValue"`
	RewardValue decimal.Decimal `json:"RewardValue"`
	State       int8            `json:"State"`
}

type WeekendBreakThroughRes struct {
	List   []WeekendBreakThroughParam `json:"List"`
	LiuSui decimal.Decimal            `json:"LiuSui" gorm:"column:LiuSui"`
}

type TodayBreakThroughRes struct {
	LiuSui decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`
}

type NewFirstDepositConfig struct {
	ID              int32           `json:"Id"`
	GiveProportion  decimal.Decimal `json:"GiveProportion"`
	GiveLimit       decimal.Decimal `json:"GiveLimit"`
	LiushuiMultiple decimal.Decimal `json:"LiushuiMultiple"`
}
type XRecharge struct {
	ID         int32
	RealAmount float64
}

type XRecharges []XRecharge

func (s XRecharges) Len() int {
	return len(s)
}
func (s XRecharges) Less(i, j int) bool {
	return s[i].ID < s[j].ID
}
func (s XRecharges) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

type NewFirstData struct {
	RealAmount        float64
	WithdrawLiuSuiAdd float64
	Level             int32
	TotalRecharge     float64
	MinLiuShui        decimal.Decimal
}
