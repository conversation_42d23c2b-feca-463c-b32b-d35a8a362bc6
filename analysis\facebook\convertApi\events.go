package convertApi

import (
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
)

func toSHA256(s string) string {
	h := sha256.New()
	h.Write([]byte(s))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func NewUserData(phone, email, ip, clientUserAgent string, eventId string, fbc string, fbp string, userId int) UserData {
	userData := UserData{}
	if phone != "" {
		phone = strings.TrimPrefix(strings.TrimSpace(phone), "+")
		phone = toSHA256(phone)
		userData.Phone = make([]string, 1)
		userData.Phone = append(userData.Phone, phone)
	}
	if email != "" {
		email = strings.ToLower(strings.TrimSpace(email))
		email = toSHA256(email)
		userData.Email = make([]string, 1)
		userData.Email = append(userData.Email, email)
	}
	//if ip != "" && ((ip != "127.0.0.1" && ip != "::1") || server.Debug()) {
	if ip != "" {
		userData.ClientIpAddress = ip
	}
	if clientUserAgent != "" {
		userData.ClientUserAgent = clientUserAgent
	}
	// MD5 userId
	externalId := toSHA256(strconv.Itoa(userId))
	userData.ExternalId = externalId
	userData.FBC = fbc
	userData.FBP = fbp
	return userData
}

func (c *Client) PushEvent(eventName string, eventId string, userData UserData, customData *CustomData) (err error) {
	reqData := EventReq{
		Data: []Data{
			{
				EventName:    eventName,
				EventTime:    time.Now().Unix(),
				ActionSource: "website",
				EventId:      eventId,
				UserData:     userData,
				CustomData:   customData,
			},
		},
	}
	//if server.Debug() {
	//	reqData.TestEventCode = "TEST67764"
	//}

	reqDataJson, _ := json.Marshal(reqData)
	logs.Info("FB ConvertApi Event:%s, EventId:%s, Data:%s", eventName, eventId, string(reqDataJson))
	var resp *req.Resp
	resp, err = req.Post(c.api, req.BodyJSON(reqData))
	if err != nil {
		logs.Error(err)
		return
	}
	var res EventRsp
	err = resp.ToJSON(&res)
	if err != nil {
		logs.Error(err)
		return
	}
	if res.Error != nil {
		logs.Error(res.Error.Message)
		logs.Error(res.Error.ErrorUserTitle)
		logs.Error(res.Error.ErrorUserMsg)
		err = errors.New(res.Error.Message)
	}
	return
}

func (c *Client) CompleteRegistration(userId int32, phone, email, ip, clientUserAgent string, fbc string, fbp string) (err error) {
	customData := &CustomData{}
	return c.PushEvent(EventName_CompleteRegistration, strconv.Itoa(int(userId)), NewUserData(phone, email, ip, clientUserAgent, strconv.Itoa(int(userId)), fbc, fbp, int(userId)), customData)
}

func (c *Client) Purchase(orderId string, phone, email, ip, clientUserAgent string, amount float64, fbc string, fbp string, userId int) (err error) {
	customData := &CustomData{
		Currency: "usd",
		Value:    amount,
	}
	return c.PushEvent(EventName_Purchase, orderId, NewUserData(phone, email, ip, clientUserAgent, orderId, fbc, fbp, userId), customData)
}

func (c *Client) FirstRecharge(orderId string, phone, email, ip, clientUserAgent string, amount float64, fbc string, fbp string, userId int) (err error) {
	customData := &CustomData{
		Currency: "usd",
		Value:    amount,
	}
	return c.PushEvent(EventName_FirstRecharge, orderId, NewUserData(phone, email, ip, clientUserAgent, orderId, fbc, fbp, userId), customData)
}

func (c *Client) AddToCart(userId int32, phone, email, ip, clientUserAgent string, fbc string, fbp string) (err error) {
	customData := &CustomData{}
	return c.PushEvent(EventName_AddToCart, strconv.Itoa(int(userId)), NewUserData(phone, email, ip, clientUserAgent, strconv.Itoa(int(userId)), fbc, fbp, int(userId)), customData)
}
