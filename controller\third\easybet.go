package third

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

func NewEasybetSrvice(url, platId, key string, gameId, gameName string, callbackurl string, fc func(int) error) *EasybetSrvice {
	return &EasybetSrvice{
		Url:                   url,
		PlatId:                platId,
		ScrectKey:             key,
		tokenEncryptKey:       "3ZIpBwRZGDeDd7mZVy7YFLbJUGF9RnWS",
		brandName:             "easybetsp",
		gameId:                gameId,
		gameName:              gameName,
		callbackurl:           callbackurl,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

type EasybetSrvice struct {
	Url                   string
	PlatId                string
	ScrectKey             string
	tokenEncryptKey       string
	brandName             string
	gameId                string
	gameName              string
	callbackurl           string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

// lang support
// zh_TW, 繁体中文
// zh_CN, 简体中文
// en_US, 英文
// vi_VN, 越南文
// pt_PT, 葡萄牙语-alpha
// hi_IN, 印度语-alpha
// fr_FR, 法语-alpha
// es_ES, 西班牙语-alpha
// de_DE, 德语-alpha

func (e *EasybetSrvice) sign(data map[string]interface{}) (reqstr, sign string) {
	keys := []string{}
	for k := range data {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	for i := 0; i < len(keys); i++ {
		v := fmt.Sprintf("%v", (data)[keys[i]])
		if len(v) > 0 {
			reqstr += fmt.Sprintf("%s=%v&", keys[i], data[keys[i]])
		}
	}
	reqstr += e.ScrectKey
	hashmd5 := md5.Sum([]byte(reqstr))
	sign = strings.ToUpper(hex.EncodeToString(hashmd5[:]))
	return
}

func (e *EasybetSrvice) userId2token(userId int) string {
	return CBCEncrypt(strconv.Itoa(userId), e.tokenEncryptKey)
}

func (e *EasybetSrvice) token2UserId(token string) int {
	userid := -1
	userIdStr := CBCDecrypt(token, e.tokenEncryptKey)
	if n, err := strconv.Atoi(userIdStr); err == nil {
		userid = n
	}

	return userid
}

func (e *EasybetSrvice) errorResponse(ctx *abugo.AbuHttpContent, status int, msg string) {
	ctx.RespJson(map[string]interface{}{
		"status": status,
		"msg":    msg,
	})
	logs.Debug("easybet EasybetCallinApi errorResponse status: %d, msg: %s", status, msg)
}

func (e *EasybetSrvice) easybet_http_post(path string, reqdata map[string]interface{}) (map[string]interface{}, error) {
	reqdata["plat_id"] = e.PlatId
	reqstr, sign := e.sign(reqdata)
	reqdata["sign"] = sign
	url := fmt.Sprintf("%s%s", e.Url, path)
	header := req.Header{
		"Content-Type": "application/json;charset=UTF-8",
	}
	reqbytes, _ := json.Marshal(&reqdata)
	resp, err := req.Post(url, header, string(reqbytes))
	if err != nil {
		logs.Error("easybet_http_post %s request error:%s", url, err)
		return nil, err
	}
	if resp.Response().StatusCode != http.StatusOK {
		err = fmt.Errorf("http error, status code: %d", resp.Response().StatusCode)
		logs.Error("easybet_http_post %s json %s, error:%s", url, string(reqstr), err)
		return nil, err
	}

	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("easybet_http_post %s body error: %s", url, err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	logs.Debug("easybet_http_post:", url, "|", string(reqbytes), "|", string(body))
	logs.Info("[INFO][EasyBet] POST ==>", url, "|", string(reqbytes), "|", string(body))
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("easybet_http_post %s json %s, error:%s", url, string(reqstr), err)
		return nil, err
	}

	var status int64
	if statusInf, ok := jdata["status"]; ok {
		status = abugo.GetInt64FromInterface(statusInf)
	} else {
		return nil, errors.New("easybet_http_post 解析失败")
	}
	if status != 0 {
		msg := "response msg 異常"
		if rmsg, ok := jdata["msg"].(string); ok {
			msg = rmsg
		}
		return nil, errors.New(msg)
	}
	return jdata, nil
}

func (e *EasybetSrvice) EasybetLogin(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		Lang  string
		Theme int //日夜模式 1=日 2=夜, 默认日模式
	}
	errcode := 0

	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	if reqdata.Theme == 0 {
		reqdata.Theme = 2
	}
	token := server.GetToken(ctx)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
	if data == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}
	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, "easybet", "easybet")
	if err != nil {
		logs.Error(e.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", "UP", " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v_easybet", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	{
		data := gin.H{
			"username": fmt.Sprintf("%d", token.UserId),
		}
		e.easybet_http_post("/open_api/register", data)
	}
	{
		data := gin.H{
			"username":            fmt.Sprintf("%d", token.UserId),
			"lang":                reqdata.Lang,
			"merchant_user_token": e.userId2token(token.UserId),
			"daynight_type":       reqdata.Theme,
		}
		jresult, err := e.easybet_http_post("/open_api/game_url", data)
		if err != nil {
			logs.Error("/open_api/game_url json %s, error:%s", data, err)
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
		jdata := (jresult)["data"].(map[string]interface{})
		ctx.Put("url", jdata["url"])
	}
	ctx.RespOK()
}

func (e *EasybetSrvice) getUser(userId int) (udata *map[string]interface{}, balance float64, err error) {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	udata, err = server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		return nil, 0, err
	}
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	return udata, balance, nil
}

func (e *EasybetSrvice) EasybetCallinApi(ctx *abugo.AbuHttpContent) {

	if strings.Index(xgo.Env(), "prd") >= 0 {
		host := ctx.Host()
		host = strings.Replace(host, "www.", "", -1)
		host = strings.Split(host, ":")[0]
		if host != e.callbackurl {
			return
		}
	}

	reqtype := ctx.Gin().PostForm("type")
	username := ctx.Gin().PostForm("username")
	merchant_user_token := ctx.Gin().PostForm("merchant_user_token")
	money_type := ctx.Gin().PostForm("money_type")
	order_id := ctx.Gin().PostForm("order_id")
	money := ctx.Gin().PostForm("money")
	resettleVersion := ctx.Gin().PostForm("resettleVersion")
	currency_type := ctx.Gin().PostForm("currency_type")
	sign := ctx.Gin().PostForm("sign")

	reqDataByte, _ := json.Marshal(gin.H{
		"ReqType":           reqtype,
		"UserName":          username,
		"OrderId":           order_id,
		"MerchantUserToken": merchant_user_token,
		"MoneyType":         abugo.InterfaceToInt(money_type),
		"Money":             abugo.InterfaceToInt(money),
		"ResettleVersion":   resettleVersion,
		"currency_type":     currency_type,
		"Sign":              sign,
	})
	reqData := string(reqDataByte)
	logs.Debug("easybet EasybetCallinApi type: %s, data: %s", reqtype, reqData)
	logs.Info("[INFO][EasyBet] EasybetCallinApi type: %s, data: %s", reqtype, reqData)
	data := map[string]interface{}{
		"type":                reqtype,
		"username":            username,
		"merchant_user_token": merchant_user_token,
		"money_type":          money_type,
		"order_id":            order_id,
		"money":               money,
		"resettleVersion":     resettleVersion,
		"currency_type":       currency_type,
	}
	_, signex := e.sign(data)
	if sign != signex {
		e.errorResponse(ctx, 1, "签名错误")

		return
	}

	userId := e.token2UserId(merchant_user_token)
	if userId == -1 {
		e.errorResponse(ctx, 2, "merchant_user_token错误")
		return
	}

	retdata := map[string]interface{}{}
	_, emptysign := e.sign(retdata)
	if reqtype == "BALANCE" {
		_, balance, err := e.getUser(userId)
		if err != nil {
			ctx.RespJson(map[string]interface{}{
				"status": 400,
				"msg":    err.Error(),
				"data":   retdata,
				"sign":   emptysign,
			})
			return
		}
		retdata := map[string]interface{}{
			"balance": balance * 1000,
		}
		_, rsign := e.sign(retdata)
		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "",
			"data":   retdata,
			"sign":   rsign,
		})
	} else if reqtype == "BET" {
		betAmt := abugo.InterfaceToFloat64(money) / 1000
		udata, balance, err := e.getUser(userId)
		if err != nil {
			e.errorResponse(ctx, 101, err.Error())
			return
		}

		// 检查游戏权限
		if allowed, hint, err := base.BeforeEnterGame(userId, utils.GameTypeSports, "easybet"); err != nil {
			logs.Error("EasyBet BET 权限检查错误 userId=", userId, " gameId=", e.brandName, " err=", err.Error())
			e.errorResponse(ctx, 101, "用户不存在")
			return
		} else if !allowed {
			logs.Error("EasyBet BET 权限被拒绝 userId=", userId, " gameId=", e.brandName, " hint=", hint)
			e.errorResponse(ctx, 101, "用户不存在")
			return
		}

		if betAmt > balance {
			e.errorResponse(ctx, 102, "余额不足")
			return
		}

		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		betTran, _ := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if betTran != nil {
			e.errorResponse(ctx, 103, "下注失败，注单已存在，重复投注")
			return
		}
		_, err = server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmt, userId, betAmt)
		if err != nil {
			e.errorResponse(ctx, 102, "余额不足")
			return
		}
		balance -= betAmt

		order := xgo.H{
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
			"BetChannelId": ChannelId,
			"UserId":       userId,
			"Brand":        e.brandName,
			"ThirdId":      order_id,
			"BetAmount":    betAmt,
			"WinAmount":    0,
			"ValidBet":     0,
			"ThirdTime":    utils.GetCurrentTime(),
			"BetTime":      utils.GetCurrentTime(),
			"BetLocalTime": utils.GetCurrentTime(),
			"Currency":     "CNY",
			"RawData":      string(reqData),
			"DataState":    -1,
		}
		_, err = server.Db().Table("x_third_sport_pre_order").Insert(order)
		if err != nil {
			logs.Error("easybet EasybetCallinApi bet error: %s", err)
			e.errorResponse(ctx, 100, "下注失败")
			return
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance + betAmt,
			"Amount":       0 - betAmt,
			"AfterAmount":  balance,
			"Reason":       utils.BalanceCReasoneEasybetBet,
			"Memo":         "eastbet bet,thirdId:" + order_id,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		resData := gin.H{
			"type":     reqtype,
			"plat_id":  e.PlatId,
			"order_id": order_id,
			"user_id":  userId,
			"money":    balance * 1000,
		}
		_, sign := e.sign(resData)

		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "下注成功",
			"data":   resData,
			"sign":   sign,
		})
		logs.Debug("easybet EasybetCallinApi: %s", "下注成功")
		logs.Info("[INFO][EasyBet] EasybetCallinApi  %s", "下注成功", resData)
		// 推送投注事件
		if e.thirdGamePush != nil {
			e.thirdGamePush.PushBetEvent(userId, e.gameName, "三方投注", betAmt, "CNY", e.brandName, order_id, 6)
		}
		e.updateGameName()
	} else if reqtype == "CANCEL" {
		// e.updateGameName(userId, order_id) 该体育三方没有传过来thirdTime,因此使用实时服务器时间
		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if err != nil || betTran == nil {
			e.errorResponse(ctx, 104, "注单不存在，无法结算或取消")
			return
		}

		betId := abugo.GetInt64FromInterface((*betTran)["Id"])

		betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
		winAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"])
		// refundMoney := abugo.InterfaceToFloat64(money) / 1000
		// if refundMoney > betAmount {
		// 	e.errorResponse(ctx, 100, err.Error())
		// 	return
		// }
		refundMoney := betAmount - winAmount
		server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", refundMoney, userId)
		server.Db().Conn().Exec("update x_third_sport_pre_order set BetAmount = 0, WinAmount = 0, ValidBet = 0, DataState = -2 where Id = ?", betId)

		udata, balance, err := e.getUser(userId)
		if err != nil {
			e.errorResponse(ctx, 100, err.Error())
			return
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance - refundMoney,
			"Amount":       refundMoney,
			"AfterAmount":  balance,
			"Reason":       utils.BalanceCReasonEasybetCancel,
			"Memo":         "eastbet cancel,thirdId:" + order_id,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		resData := gin.H{
			"type":     reqtype,
			"plat_id":  e.PlatId,
			"order_id": order_id,
			"user_id":  userId,
			"money":    balance * 1000,
		}
		_, sign := e.sign(resData)

		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "取消成功",
			"data":   resData,
			"sign":   sign,
		})
		logs.Debug("easybet EasybetCallinApi:%s", "取消成功")
		logs.Info("[INFO][EasyBet] EasybetCallinApi  %s", "取消成功", resData)
	} else if reqtype == "SETTLE" {
		// e.updateGameName(userId, order_id) 该体育三方没有传过来thirdTime,因此使用实时服务器时间
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if err != nil || betTran == nil {
			e.errorResponse(ctx, 104, "注单不存在，无法结算或取消")
			return
		}

		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		payMoney := abugo.InterfaceToFloat64(money) / 1000

		betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
		validAmount := easybetValidBet(betAmount, payMoney)
		server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", payMoney, userId)
		server.Db().Conn().Exec("update x_third_sport_pre_order set WinAmount = WinAmount + ?, ValidBet = ?, DataState = 1, ThirdTime = ?, SettleTime = ? where Id = ?", payMoney, validAmount, thirdTime, thirdTime, betId)

		udata, balance, err := e.getUser(userId)
		if err != nil {
			e.errorResponse(ctx, 100, err.Error())
			return
		}

		betTranData := *betTran
		delete(betTranData, "Id")
		delete(betTranData, "CreateTime")
		betTranData["WinAmount"] = payMoney
		betTranData["ValidBet"] = validAmount
		betTranData["RawData"] = string(reqDataByte)
		betTranData["DataState"] = 1
		betTranData["ThirdTime"] = thirdTime
		betTranData["SettleTime"] = thirdTime //该体育没有传结算时间因此取系统当前时间

		// 获取注单链接
		data := gin.H{
			"plat_id":  e.PlatId,
			"order_id": order_id,
		}
		jresult, err2 := e.easybet_http_post("/open_api/order_detail_page", data)
		if err2 != nil {
			logs.Debug("游戏注单链接获取失败", err2)
		} else {
			jdata := (jresult)["data"].(string)
			betTranData["BetCtx"] = jdata
			betTranData["BetCtxType"] = 2

			server.Db().Conn().Exec("update x_third_sport_pre_order set BetCtx = ?, BetCtxType = ? where Id = ?", jdata, 2, betId)
		}

		server.Db().Table("x_third_sport").Insert(betTranData)

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance - payMoney,
			"Amount":       payMoney,
			"AfterAmount":  balance,
			"Reason":       utils.BalanceCReasonEasybetWin,
			"Memo":         "eastbet settle,thirdId:" + order_id,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		resData := gin.H{
			"type":     reqtype,
			"plat_id":  e.PlatId,
			"order_id": order_id,
			"user_id":  userId,
			"money":    balance * 1000,
		}
		_, sign := e.sign(resData)

		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "结算成功",
			"data":   resData,
			"sign":   sign,
		})
		logs.Debug("easybet EasybetCallinApi:%s", "结算成功")
		logs.Info("[INFO][EasyBet] EasybetCallinApi  %s", "结算成功", resData)
		// 推送派奖事件
		if e.thirdGamePush != nil {
			//e.thirdGamePush.PushRewardEvent(userId, e.gameName, e.gameName, betAmount, payMoney, "CNY")
			e.thirdGamePush.PushRewardEvent(6, e.brandName, order_id)
		}
	} else if reqtype == "RESETTLE" {
		// e.updateGameName(userId, order_id)
		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if err != nil || betTran == nil {
			e.errorResponse(ctx, 104, "注单不存在，无法结算或取消")
			return
		}
		if abugo.GetInt64FromInterface((*betTran)["DataState"]) != 1 {
			e.errorResponse(ctx, 104, "注单未结算，无法重新结算")
			return
		}

		winAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"])
		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		payMoney := abugo.InterfaceToFloat64(money) / 1000
		moneyType := abugo.InterfaceToInt(money_type)
		betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
		rawData := abugo.GetStringFromInterface((*betTran)["RawData"])
		rawData += string(reqDataByte)
		payout := payMoney
		if moneyType == 1 { // 加款
			payout = payMoney
		} else if moneyType == 2 { // 扣款
			payout = -payMoney
		} else {
			e.errorResponse(ctx, 104, "结算类型money_type错误")
			return
		}
		validAmount := easybetValidBet(betAmount, winAmount+payout)
		if payout != 0 {
			server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", payout, userId)
			server.Db().Conn().Exec("update x_third_sport_pre_order set WinAmount = WinAmount + ?, ValidBet = ?, RawData=?  where Id = ? and DataState = 1", payout, validAmount, rawData, betId)
			server.Db().Conn().Exec("update x_third_sport_pre set WinAmount = WinAmount + ?, ValidBet = ?, RawData=? where ThirdId = ? and Brand=? and DataState = 1", payout, validAmount, rawData, order_id, e.brandName)
		}

		udata, balance, err := e.getUser(userId)
		if err != nil {
			e.errorResponse(ctx, 100, err.Error())
			return
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance - payout,
			"Amount":       payout,
			"AfterAmount":  balance,
			"Reason":       utils.BalanceCReasonEasybetWin,
			"Memo":         "eastbet resettle,thirdId:" + order_id,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		resData := gin.H{
			"type":     reqtype,
			"plat_id":  e.PlatId,
			"order_id": order_id,
			"user_id":  userId,
			"money":    balance * 1000,
		}
		_, sign := e.sign(resData)

		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "重新结算成功",
			"data":   resData,
			"sign":   sign,
		})
		logs.Debug("easybet EasybetCallinApi:%s", "重新结算成功")
		logs.Info("[INFO][EasyBet] EasybetCallinApi  %s", "重新结算成功", resData)
	}

	// 发送余额变动通知
	if reqtype != "BALANCE" {
		go func(notifyUserId int) {
			if e.RefreshUserAmountFunc != nil {
				tmpErr := e.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][EasyBet] EasybetCallinApi 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][EasyBet] EasybetCallinApi 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

func (e *EasybetSrvice) updateGameName() bool { //updateGameName(userId int, order_id string) bool {
	dKey := fmt.Sprintf("%v:sync_order_easybetsp", server.Project())
	dValue := time.Now().Add(time.Second).In(tzUTC8).Format("2006-01-02 15:04:05")
	err := server.Redis().RPushString(dKey, dValue)
	if err != nil {
		logs.Error("easybet updateGameName RPush redis error: dKey=", dKey, " dValue=", dValue, " err=", err.Error())
	} else {
		//logs.Info("easybet updateGameName RPush redis success: dKey=", dKey, " dValue=", dValue)
		return true
	}
	return false
	/*
		// Get the current date and time
		currentTime := time.Now()

		// Subtract 2 days from the current date
		twoDaysBefore := currentTime.AddDate(0, 0, -2)

		data2 := gin.H{
			"username":          fmt.Sprintf("%d", userId),
			"page_size":         2000,
			"page_count":        1,
			"update_time_start": twoDaysBefore.Format("2006-01-02 15:04:05"),
		}

		jresult, err2 := e.easybet_http_post("/open_api/game_log", data2)
		if err2 != nil {
			logs.Debug("游戏列表获取失败", err2)
			//ctx.RespErrString(true, &errcode, "游戏列表获取失败")
			//return
		}
		//abugo.LogInfo("游戏列表", jresult["data"])
		type ResponseLog struct {
			Status int    `json:"status"`
			Msg    string `json:"msg"`
			Data   struct {
				List []struct {
					SportID      int    `json:"sport_id"`      // The field we're interested in
					PartnerOrder string `json:"partner_order"` // The field we're interested in
				} `json:"list"`
			} `json:"data"`
		}

		// Marshal the map into a JSON string
		jsonData, err2 := json.Marshal(jresult)
		if err2 != nil {
			logs.Debug("Error occurred during marshaling. Error: %s", err2.Error())
		}

		// Convert bytes to string since json.Marshal returns []byte
		jsonString := string(jsonData)

		var response ResponseLog

		// Unmarshal the JSON string into the response struct
		if err2 = json.Unmarshal([]byte(jsonString), &response); err2 != nil {
			logs.Debug("Error parsing JSON:", err2)
		}

		for i := range response.Data.List {
			SportID := response.Data.List[i].SportID
			PartnerOrder := response.Data.List[i].PartnerOrder
			logs.Debug("PartnerOrder", PartnerOrder, "order_id", order_id)
			if PartnerOrder == order_id {

				sportsMapping := map[int]string{
					0: "串关",
					1: "足球",
					4: "篮球",
					5: "美式足球",
					7: "赛马",
					8: "赛狗",
					9: "马车赛",
				}
				GameName := ""
				if chineseName, ok := sportsMapping[SportID]; ok {
					GameName = chineseName
				}
				server.Db().Conn().Exec("update x_third_sport_pre_order set GameId =  ?, GameName = ? where ThirdId= ? and Brand=? ", SportID, GameName, order_id, e.brandName)
				logs.Debug("PartnerOrder", PartnerOrder, "SportID", SportID, "GameName", GameName)
				logs.Info("[INFO][EasyBet] PartnerOrder", PartnerOrder, "SportID", SportID, "GameName", GameName)
			}
		}
		return true
	*/
}

func easybetValidBet(betAmt, winAmount float64) float64 {
	if winAmount > 0 {
		subAmount := winAmount - betAmt

		if subAmount > betAmt {
			return betAmt
		} else {
			return math.Abs(subAmount)
		}
	} else {
		return betAmt
	}
}

// easybet 获取足球热门赛事接口
func (e *EasybetSrvice) GetFootballHotEvents(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		PlatId     string `json:"plat_id"`     // 商户ID
		Size       string `json:"size"`        // 返回热门赛事数量，不携带或者参数大于3只返回3场热门赛事数据
		MarketType string `json:"market_type"` // 盘口名称 多个盘口使用英文字符逗号隔开，可指定返回的盘口数据
		Lang       string `json:"lang"`        // 语言代号，不携带默认返回英文内容
	}

	/*
		type MatchesData struct {
			Id                int    `json:"id"`                  // 赛事id
			CompetitionId     int    `json:"competition_id"`      // 联赛id
			EventId           string `json:"event_id"`            // 三方赛事id
			StartTime         string `json:"start_time"`          // 开赛日期
			Name              string `json:"name"`                // 赛事名称
			SportId           int    `json:"sport_id"`            // 体育类型id
			HomeTeam          string `json:"home_team"`           // 主队名称
			AwayTeam          string `json:"away_team"`           // 客队名称
			MarketAmount      int    `json:"market_amount"`       // 盘口数量
			AnimationId       string `json:"animation_id"`        // 动画id
			AnimationStatus   int    `json:"animation_status"`    // 动画状态  1 是 2 否
			IsChampion        int    `json:"is_champion"`         // 是否为冠军赛  1 是 0 否
			CompSort          int    `json:"comp_sort"`           // 联赛排序
			Country           string `json:"country"`             // 国家代码
			SystemCountryCode string `json:"system_country_code"` // 系统国家代码
			CName             string `json:"c_name"`              // 联赛名称
			SbTime            int    `json:"sb_time"`             // 赛事开始时间
			LiveStatus        int    `json:"live_status"`         // 直播状态 1 是 2 否
			LiveUrl           string `json:"live_url"`            // 直播地址
			HomeTeamIcon      string `json:"home_team_icon"`      // 主队图标
			AwayTeamIcon      string `json:"away_team_icon"`      // 客队图标
			// market []Array `json:"market"` // 盘口数据，可参考 盘口
			// states Object `json:"states"` // 进程数据，可参考 进程
		}

		type EventData struct {
			CompetitionId   int           `json:"competition_id"`   // 联赛id
			CompetiitonName string        `json:"competiiton_name"` // 联赛名称
			CountryIcon     string        `json:"country_icon"`     // 联赛icon图示
			Count           int           `json:"count"`            // 该联赛下赛事笔数
			Sort            int           `json:"sort"`             // 排序值
			SportId         int           `json:"sport_id"`         // 体育类型id
			Matches         []MatchesData `json:"matches"`          // 赛事列表
		}

		type ResponseData struct {
			Status int         `json:"status"`
			Msg    string      `json:"msg"`
			Data   []EventData `json:"data"`
			Extend interface{} `json:"extend"`
		}
	*/
	type ResponseData struct {
		Status interface{} `json:"status"`
		Msg    interface{} `json:"msg"`
		Data   interface{} `json:"data"`
		Extend interface{} `json:"extend"`
	}

	reqdata := RequestData{}
	rspdata := ResponseData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("[ERROR][EasyBet] GetFootballHotEvents", err.Error())
		err = nil
	}

	// 设置请求值
	reqdata.PlatId = e.PlatId
	reqdata.Size = "3"
	reqdata.MarketType = ""

	// 缓存key
	rKey := fmt.Sprintf("%s:%s:easybet:footballhotevents:%s", server.Project(), server.Module(), reqdata.Lang)
	rValueInterface := server.Redis().Get(rKey)
	rValue := abugo.GetStringFromInterface(rValueInterface)
	if rValue != "" {
		// 已经有缓存 直接返回
		// logs.Info("[Info][EasyBet] GetFootballHotEvents 直接从缓存返回数据")
		json.Unmarshal([]byte(rValue), &rspdata)
		ctx.RespOK(rspdata)
		return
	}

	errcode := 0
	url := fmt.Sprintf("%s/public/event/openlist", e.Url)
	header := req.Header{
		"Content-Type": "application/json;charset=UTF-8",
	}
	reqbytes, _ := json.Marshal(&reqdata)
	resp, err := req.Post(url, header, string(reqbytes))
	if err != nil {
		logs.Error("[ERROR][EasyBet] GetFootballHotEvents url=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	if resp.Response().StatusCode != http.StatusOK {
		err = fmt.Errorf("http error, status code: %d", resp.Response().StatusCode)
		logs.Error("[ERROR][EasyBet] GetFootballHotEvents url=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("[ERROR][EasyBet] GetFootballHotEvents url=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}
	err = json.Unmarshal(body, &rspdata)
	if err != nil {
		logs.Error("[ERROR][EasyBet] GetFootballHotEvents url=", url, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "获取热门赛事失败")
		return
	}

	defer func() {
		if e := server.Redis().SetStringEx(rKey, 30, string(body)); e != nil {
			logs.Error("[ERROR][EasyBet] GetFootballHotEvents Redis SetStringEx err=", e.Error())
		} /*else {
			logs.Info("[Info][EasyBet] GetFootballHotEvents 设置缓存成功")
		}*/
	}()

	ctx.RespOK(rspdata)
	return
}

// 手动更新游戏名称和ID
func (e *EasybetSrvice) ManualUpdateGameName(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AuthKey   string `json:"AuthKey"`   // 授权码
		StartTime string `json:"StartTime"` // 开始时间
		EndTime   string `json:"EndTime"`   // 结束时间
	}
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 参数错误 err=", err.Error())
		ctx.RespErrString(true, nil, "参数错误")
		return
	}
	if reqdata.AuthKey != "easybetsp" {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 授权码错误")
		ctx.RespErrString(true, nil, "参数错误2")
		return
	}
	sstr, err := time.Parse("2006-01-02 15:04:05", reqdata.StartTime)
	if err != nil {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 时间格式错误 StartTime=", reqdata.StartTime, " err=", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	if reqdata.StartTime != sstr.Format("2006-01-02 15:04:05") {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 时间格式错误 StartTime=", reqdata.StartTime, " err=", err.Error())
		ctx.RespErrString(true, nil, "StartTime时间格式错误")
		return
	}
	estr, err := time.Parse("2006-01-02 15:04:05", reqdata.EndTime)
	if err != nil {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 时间格式错误 EndTime=", reqdata.EndTime, " err=", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	if reqdata.EndTime != estr.Format("2006-01-02 15:04:05") {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 时间格式错误 EndTime=", reqdata.EndTime, " err=", err.Error())
		ctx.RespErrString(true, nil, "EndTime时间格式错误")
		return
	}

	// 先查出正式订单哪些没有游戏ID的注单
	oList := make([]thirdGameModel.ThirdOrder, 0)
	err = server.Db().GormDao().Table("x_third_sport").Where("Brand = ? and GameId IS NULL and ThirdTime >= ? and ThirdTime <= ?", e.brandName, reqdata.StartTime, reqdata.EndTime).Find(&oList).Error
	if err != nil {
		logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 查询正式订单错误 err=", err.Error())
		ctx.RespErrString(true, nil, err.Error())
		return
	}

	if len(oList) <= 0 {
		logs.Info("[INFO][EasyBet] 手动更新游戏名称和ID 时间段", reqdata.StartTime, "到", reqdata.EndTime, "没有空GameId")
		ctx.RespOK()
		return
	}

	for _, il := range oList {
		time.Sleep(time.Second * 2)
		t, err := time.Parse("2006-01-02T15:04:05-07:00", il.ThirdTime)
		if err != nil {
			logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 时间格式错误 thirdId=", il.ThirdId, " err=", err.Error())
			continue
		}
		sTime := t.Add(-time.Hour * 48).Format("2006-01-02 15:04:05")
		eTime := t.Add(time.Hour * 48).Format("2006-01-02 15:04:05")

		data := gin.H{
			"username":          fmt.Sprintf("%d", il.UserId),
			"page_size":         2000,
			"page_count":        1,
			"update_time_start": sTime,
			"update_time_end":   eTime,
		}
		jresult, err := e.easybet_http_post("/open_api/game_log", data)
		if err != nil {
			if strings.Contains(err.Error(), "操作频繁") {
				time.Sleep(time.Second)
				jresult, err = e.easybet_http_post("/open_api/game_log", data)
			}
			logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 请求数据错误 err=", err.Error())
			if err != nil {
				continue
			}
		}

		type ResponseLog struct {
			Status int    `json:"status"`
			Msg    string `json:"msg"`
			Data   struct {
				List []struct {
					SportID      int    `json:"sport_id"`      // The field we're interested in
					PartnerOrder string `json:"partner_order"` // The field we're interested in
				} `json:"list"`
			} `json:"data"`
		}

		// Marshal the map into a JSON string
		jsonData, err := json.Marshal(jresult)
		if err != nil {
			logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 响应数据格式错误 err=", err.Error())
			ctx.RespErrString(true, nil, err.Error())
			return
		}

		// Convert bytes to string since json.Marshal returns []byte
		jsonString := string(jsonData)

		var response ResponseLog

		// Unmarshal the JSON string into the response struct
		if err = json.Unmarshal([]byte(jsonString), &response); err != nil {
			logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 响应数据格式错误2 err=", err.Error())
			ctx.RespErrString(true, nil, err.Error())
			return
		}

		for i := range response.Data.List {
			sportId := response.Data.List[i].SportID
			partnerOrder := response.Data.List[i].PartnerOrder
			if partnerOrder == il.ThirdId {
				sportsMapping := map[int]string{
					0: "串关",
					1: "足球",
					4: "篮球",
					5: "美式足球",
					7: "赛马",
					8: "赛狗",
					9: "马车赛",
				}
				GameName := ""
				if chineseName, ok := sportsMapping[sportId]; ok {
					GameName = chineseName
				}
				tmpe := server.Db().GormDao().Table("x_third_sport_pre_order").Where("ThirdId=? and Brand=? and UserId=? and GameId IS NULL", il.ThirdId, e.brandName, il.UserId).Updates(map[string]interface{}{
					"GameId":   fmt.Sprintf("%d", sportId),
					"GameName": GameName,
				}).Error
				if tmpe != nil {
					logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 更新数据错误 预设表 ThirdId=", il.ThirdId, " tmpe=", tmpe.Error())
				}
				tmpe = server.Db().GormDao().Table("x_third_sport").Where("ThirdId=? and Brand=? and UserId=? and GameId IS NULL", il.ThirdId, e.brandName, il.UserId).Updates(map[string]interface{}{
					"GameId":   fmt.Sprintf("%d", sportId),
					"GameName": GameName,
				}).Error
				if tmpe != nil {
					logs.Error("[ERROR][EasyBet] 手动更新游戏名称和ID 更新数据错误 正式表 ThirdId=", il.ThirdId, " tmpe=", tmpe.Error())
				}
				logs.Info("[INFO][EasyBet] 手动更新游戏名称和ID 完成 ThirdId=", il.ThirdId)
				break
			}
		}
	}
	ctx.RespOK()
	return
}
