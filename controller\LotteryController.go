package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/customer"
	"xserver/controller/datapush"
	"xserver/gormgen/xHashGame/dao"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"
	"xserver/utils/invaildAddress"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/thoas/go-funk"
	gorm2 "gorm.io/gorm"

	"xserver/controller/common"

	"github.com/spf13/viper"
)

type LotteryController struct {
	hashmakerapi     string
	hashmakerethapi  string
	hashmakerbscapi  string
	hashmakersolapi  string
	nextdata         map[string]interface{}
	locker           sync.Mutex
	baseController   *customer.BaseController
	socketController *SocketController
}

type RewardRateEx struct {
	Limit struct {
		Num struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"num"`
		Size struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"size"`
		Color struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"color"`
		OddEven struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"oddEven"`
		Col struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"col"`
		Row struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"row"`
		Jiaozhu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"jiaozhu"`
		Jiezhu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"jiezhu"`
		Sanshu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"sanshu"`
		Fenzhu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"fenzhu"`
		Xianzhu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"xianzhu"`
		Sishu struct {
			Min int `json:"min"`
			Max int `json:"max"`
		} `json:"sishu"`
	} `json:"limit"`
}

func (c *LotteryController) Init() {
	c.hashmakerapi = viper.GetString("hashmakerapi")
	c.hashmakerethapi = viper.GetString("hashmakerethapi")
	c.hashmakerbscapi = viper.GetString("hashmakerbscapi")
	c.hashmakersolapi = viper.GetString("hashmakersolapi")
	c.nextdata = map[string]interface{}{}

	// 初始化 BaseController 并检查是否成功
	c.baseController = customer.NewBaseController()
	if c.baseController == nil {
		logs.Error("LotteryController 初始化失败: BaseController 创建失败")
	} else {
		logs.Info("LotteryController 成功初始化 BaseController")
	}

	// 初始化 SocketController
	c.socketController = SocketHandler
	if c.socketController == nil {
		logs.Error("LotteryController 初始化失败: SocketController 未找到")
	} else {
		logs.Info("LotteryController 成功初始化 SocketController")
	}

	gropu := server.Http().NewGroup("/api/lottery")
	{
		gropu.PostNoAuth("/open_history", c.open_history)
		gropu.PostNoAuth("/next", c.next)
		gropu.PostNoAuth("/query", c.query)
		gropu.PostNoAuth("/get_hash_bet_info", c.get_hash_bet_info)
		gropu.Post("/bet", c.bet)
		gropu.Post("/rebet", c.rebet)
		gropu.Post("/get", c.get)
		//gropu.Post("/send_pub_hash_bet", c.send_pub_hash_bet)
		gropu.PostByNoAuthMayUserToken("/get_config", c.get_config)
		gropu.PostNoAuth("/check/address", c.check_address)
	}
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.Error("LotteryController.go", "Init", err)
			}
		}()
		var gamekey string
		for {
			games := []int{6, 7}
			for _, gameid := range games {
				sql := "SELECT GameId,Period,OpenTime,TIME_TO_SEC(TIMEDIFF(OpenTime,NOW())) AS LeftTime FROM x_lottery_period WHERE GameId = ? and OpenTime < DATE_ADD(NOW(),INTERVAL 180 SECOND) ORDER BY OpenTime DESC LIMIT 1"
				presult, err := server.Db().Query(sql, []interface{}{gameid})
				if err != nil {
					logs.Error(err)
					continue
				}
				if len((*presult)) > 0 {
					c.locker.Lock()
					gamekey = fmt.Sprintf("%d_%d", gameid, 0)
					c.nextdata[gamekey] = (*presult)[0]
					c.locker.Unlock()
				}
			}
			games = []int{101, 102, 103, 104, 105, 106, 116, 126, 131, 132, 133, 134, 135, 136}
			for _, gameid := range games {
				t := carbon.Parse(carbon.Now().String()).StdTime()
				s, _ := time.ParseDuration(fmt.Sprintf("-%ds", utils.BlockTimeSecond))
				date := t.Add(s)
				sql := "SELECT ChainType,GameId,MIN(Period) AS Period FROM x_game_period WHERE GameId = ? and State = ? AND BlockTime >=? group by ChainType,GameId"
				presult, err := server.Db().Query(sql, []interface{}{gameid, utils.GamePeriodState, date.Format(utils.TimeFormatStr)})
				if err != nil {
					logs.Error(err)
					continue
				}
				if len((*presult)) > 0 {
					c.locker.Lock()
					for i, _ := range *presult {
						gamekey = fmt.Sprintf("%d_%d", gameid, (*presult)[i]["ChainType"])
						c.nextdata[gamekey] = (*presult)[i]
					}
					c.locker.Unlock()
				}
			}
			// bytes, _ := json.Marshal(c.nextdata)
			// logs.Debug("nextdata:", string(bytes))
			time.Sleep(1 * time.Second)
		}
	}()
}

func (c *LotteryController) open_history(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		GameId    int `validate:"required"`
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if (reqdata.GameId < 6 || reqdata.GameId > 7) && (reqdata.GameId < 100 || reqdata.GameId > 200) && (reqdata.GameId < 301 || reqdata.GameId > 400) {
		ctx.RespErrString(true, &errcode, "游戏不存在")
		return
	}
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1 // 默认波场
	}
	rediskey := fmt.Sprintf("%s:%s:lottery_history:%d:%d", server.Project(), server.Module(), reqdata.GameId, reqdata.ChainType)
	redisdata := server.Redis().Get(rediskey)
	if redisdata != nil {
		jdata := []interface{}{}
		json.Unmarshal(redisdata.([]byte), &jdata)
		ctx.RespOK(jdata)
	} else {
		if reqdata.GameId < 100 || reqdata.GameId > 300 {
			where := abugo.AbuDbWhere{}
			where.Add("and", "GameId", "=", reqdata.GameId, 0)
			where.Add("and", "State", ">", 1, 0)
			where.Add("and", "ChainType", "=", reqdata.ChainType, 0)
			fields := "GameId,Period,OpenTime,OpenHash,OpenResult"
			presult, err := server.Db().Table("x_lottery_period").Select(fields).Where(where).OrderBy("Period DESC").Limit(200).GetList()
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.RespOK(*presult)
			server.Redis().SetEx(rediskey, 5, *presult)
		} else {
			where := abugo.AbuDbWhere{}
			where.Add("and", "GameId", "=", reqdata.GameId, 0)
			where.Add("and", "State", ">", 2, 0)
			where.Add("and", "ChainType", "=", reqdata.ChainType, 0)
			fields := "GameId,Period,OpenHash,OpenResult"
			presult, err := server.Db().Table("x_game_period").Select(fields).Where(where).OrderBy("Period DESC").Limit(200).GetList()
			// for _, v := range *presult {
			// 	if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 {
			// 		v["OpenHash"] = v["RandomHash"]
			// 		v["Period"] = v["RandomBlock"]
			// 	}
			// }
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.RespOK(*presult)
			server.Redis().SetEx(rediskey, 5, *presult)
		}
	}
}

func (c *LotteryController) next(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		GameId    int `validate:"required"`
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if (reqdata.GameId < 6 || reqdata.GameId > 7) && (reqdata.GameId < 100 || reqdata.GameId > 200) {
		ctx.RespErrString(true, &errcode, "游戏不存在")
		return
	}
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1 // 默认波场
	}
	c.locker.Lock()
	gamekey := fmt.Sprintf("%d_%d", reqdata.GameId, reqdata.ChainType)
	presult := c.nextdata[gamekey]
	c.locker.Unlock()
	ctx.RespOK(presult)
	logs.Debug("next:", reqdata.GameId, presult)
}

func (c *LotteryController) query(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		GameId    int    `validate:"required"` //运营商
		Period    string `validate:"required"` //运营商
		ChainType int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if (reqdata.GameId < 6 || reqdata.GameId > 7) && (reqdata.GameId < 100 || reqdata.GameId > 200) && (reqdata.GameId < 301 || reqdata.GameId > 400) {
		ctx.RespErrString(true, &errcode, "游戏不存在")
		return
	}
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1 // 默认波场
	}
	sql := ""
	var presult *[]map[string]interface{}
	if reqdata.GameId < 100 || (reqdata.GameId > 300 && reqdata.GameId < 400) {
		sql = "SELECT OpenHash,OpenResult,OpenTime FROM x_lottery_period WHERE GameId = ? and Period = ?"
		presult, err = server.Db().Query(sql, []interface{}{reqdata.GameId, reqdata.Period})
	} else {
		sql = "SELECT OpenHash,OpenResult FROM x_game_period WHERE GameId = ? and Period = ? and ChainType = ? and State > 2"
		presult, err = server.Db().Query(sql, []interface{}{reqdata.GameId, reqdata.Period, reqdata.ChainType})
	}
	if ctx.RespErr(err, &errcode) {
		return
	}
	if len((*presult)) > 0 {
		(*presult)[0]["Period"] = reqdata.Period
		// (*presult)[0]["OpenHash"] = (*presult)[0]["OpenHash"]
		// if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 {
		// 	(*presult)[0]["OpenHash"] = (*presult)[0]["RandomHash"]
		// }
		ctx.RespOK((*presult)[0])
	} else {
		ctx.Put("Period", reqdata.Period)
		ctx.Put("OpenHash", nil)
		ctx.Put("OpenResult", nil)
		ctx.Put("OpenTime", nil)
		ctx.RespOK()
	}
}

type RouletteBet struct {
	Type   string  `json:"type"`
	Amount float64 `json:"amount"`
	Area   string  `json:"area"`
}

// bet函数用于处理用户的下注请求
func (c *LotteryController) bet(ctx *abugo.AbuHttpContent) {
	// 使用defer recover()来捕获并恢复可能发生的panic,确保程序不会崩溃
	defer recover()

	// 定义请求数据的结构体,包含所有下注相关的参数
	type RequestData struct {
		GameId    int     `validate:"required"` // 游戏ID,必填参数
		RoomLevel int     `validate:"required"` // 房间等级,必填参数
		BetArea   string  // 下注区域,如"大","小","单","双"等
		BetAmount float64 // 下注金额,必填参数
		Period    string  `validate:"required"` // 游戏期号,必填参数
		Host      string  // 请求的主机地址

		BetArea2   string  // 第二个下注区域,用于同时下注多个区域
		BetAmount2 float64 // 第二个下注金额
		UtType     int32   // 用户类型标识,用于区分ut用户
		ThirdIp    string  // 第三方IP地址
		Lang       string  // 用户语言设置
		ChainType  int     // 区块链类型(1:波场 2:以太坊 3:BSC 4:Solana)
		AgentCode  string  // 代理商编码

		RouletteBets []RouletteBet // 轮盘游戏下注区域
	}

	// 初始化错误码为0
	errcode := 0
	// 创建请求数据结构体实例
	reqdata := RequestData{}
	// 将HTTP请求数据解析到结构体中
	err := ctx.RequestData(&reqdata)
	// 如果解析出错,返回错误响应
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 从上下文中获取用户的认证token
	token := server.GetToken(ctx)

	if reqdata.GameId != 106 && reqdata.GameId != 116 && reqdata.GameId != 126 && reqdata.GameId != 136 && reqdata.GameId != 206 {
		// 构造redis键名,用于防止用户重复下注
		rediskey := fmt.Sprintf("%v:%v:game_bet_%v", server.Project(), server.Module(), token.UserId)
		// 在redis中设置锁,有效期3秒,防止同一用户短时间内重复下注
		lck := server.Redis().SetNxString(rediskey, "1", 3)
		// 如果设置锁失败,说明用户操作太频繁
		if lck != nil {
			ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
			return
		}
	}

	// 获取已开过奖的期号
	// periods, _ := server.DaoxHashGame().XGamePeriod.WithContext(ctx.Gin()).
	// 	Where(server.DaoxHashGame().XGamePeriod.GameID.Eq(int32(reqdata.GameId))).
	// 	Where(server.DaoxHashGame().XGamePeriod.Period.Eq(reqdata.Period)).
	// 	Where(server.DaoxHashGame().XGamePeriod.State.Eq(3)).
	// 	Where(server.DaoxHashGame().XGamePeriod.ChainType.Eq(int32(reqdata.ChainType))).
	// 	First()
	// if periods != nil {
	// 	ctx.RespErrString(true, &errcode, "本期下注已截止")
	// 	return
	// }

	// 检查用户余额是否被冻结
	frozen, message := common.CheckUserFrozen(token.UserId)
	if frozen {
		logs.Error("下注失败 玩家账号余额被冻结 userId=", token.UserId, " message=", message)
		//errcode = 201
		ctx.RespErrString(true, &errcode, message)
		return
	}

	// 如果没有提供第三方IP或IP长度不合法,使用当前请求的IP地址
	if len(reqdata.ThirdIp) < 7 {
		reqdata.ThirdIp = ctx.GetIp()
	}

	// 如果未指定链类型,默认使用波场链(类型1)
	if reqdata.ChainType == 0 {
		reqdata.ChainType = 1
	}

	// 验证链类型是否为支持的类型之一
	if reqdata.ChainType != 1 && reqdata.ChainType != 2 && reqdata.ChainType != 3 && reqdata.ChainType != 4 {
		ctx.RespErrString(true, &errcode, "游戏链不存在")
		return
	}
	// 获取游戏链数据表对象
	gameChainTb := server.DaoxHashGame().XGameChain
	// 创建带有上下文的数据库查询对象
	gameChainDb := server.DaoxHashGame().XGameChain.WithContext(ctx.Gin())
	// 查询指定链类型的游戏链信息
	gameChain, err := gameChainDb.Where(gameChainTb.ChainType.Eq(int32(reqdata.ChainType))).First()
	// 处理查询错误,但忽略未找到记录的错误
	if err != nil && !errors.Is(err, gorm2.ErrRecordNotFound) {
		// 记录错误日志
		logs.Error("bet find gameChain err: ", err)
		// 返回系统错误响应
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	// 检查游戏链是否存在且未关闭(状态2表示关闭)
	if gameChain == nil || gameChain.State == 2 {
		ctx.RespErrString(true, &errcode, "游戏链已关闭")
		return
	}

	// 验证游戏ID是否为支持的游戏类型
	if reqdata.GameId != 101 && reqdata.GameId != 102 && reqdata.GameId != 103 && reqdata.GameId != 104 && reqdata.GameId != 105 && reqdata.GameId != 106 &&
		reqdata.GameId != 116 && reqdata.GameId != 126 &&
		reqdata.GameId != 201 && reqdata.GameId != 202 && reqdata.GameId != 203 && reqdata.GameId != 204 && reqdata.GameId != 205 && reqdata.GameId != 206 &&
		reqdata.GameId != 131 && reqdata.GameId != 132 && reqdata.GameId != 133 && reqdata.GameId != 134 && reqdata.GameId != 135 && reqdata.GameId != 136 {
		ctx.RespErrString(true, &errcode, "游戏不存在")
		return
	}

	// 验证房间等级是否在有效范围内(1-3)
	if reqdata.RoomLevel < 1 && reqdata.RoomLevel > 3 {
		ctx.RespErrString(true, &errcode, "房间不存在")
		return
	}
	// 验证下注金额是否大于最小值1
	if reqdata.BetAmount < 0 {
		ctx.RespErrString(true, &errcode, "下注金额不可小于0")
		return
	}

	// 如果有第二个下注区域,验证其金额是否也大于最小值1
	if reqdata.BetArea2 != "" && reqdata.BetAmount2 < 0 {
		ctx.RespErrString(true, &errcode, "下注金额不可小于0")
		return
	}
	// 验证大小游戏(游戏ID:101/201/331)的下注区域必须是"大"或"小"
	if (reqdata.GameId == 101 || reqdata.GameId == 201 || reqdata.GameId == 331) && reqdata.BetArea != "大" && reqdata.BetArea != "小" {
		// 如果下注区域不是"大"或"小",返回错误
		ctx.RespErrString(true, &errcode, "下注区域不正确")
		return
	}

	// 验证单双游戏(游戏ID:102/202/332)的下注区域必须是"单"或"双"
	if (reqdata.GameId == 102 || reqdata.GameId == 202 || reqdata.GameId == 332) && reqdata.BetArea != "单" && reqdata.BetArea != "双" {
		// 如果下注区域不是"单"或"双",返回错误
		ctx.RespErrString(true, &errcode, "下注区域不正确")
		return
	}

	// 验证游戏(游戏ID:104/204)的下注区域必须是"庄"、"闲"或"和"
	// 同时如果有第二个下注区域(BetAmount2>0),其区域也必须是"庄"、"闲"或"和"
	if (reqdata.GameId == 104 || reqdata.GameId == 204) && (reqdata.BetArea != "庄" && reqdata.BetArea != "闲" && reqdata.BetArea != "和" || (reqdata.BetAmount2 > 0 && reqdata.BetArea2 != "庄" && reqdata.BetArea2 != "闲" && reqdata.BetArea2 != "和")) {
		// 如果下注区域不符合要求,返回错误
		ctx.RespErrString(true, &errcode, "下注区域不正确")
		return
	}

	// 对于特殊游戏(游戏ID:103/105/203/205),将下注区域设置为"--"
	// 这些游戏可能是幸运数字或其他特殊玩法,不需要指定具体下注区域
	if reqdata.GameId == 103 || reqdata.GameId == 105 || reqdata.GameId == 203 || reqdata.GameId == 205 {
		reqdata.BetArea = "--"
	}

	// 如果游戏ID是106、116、126、136或206,则需要验证轮盘游戏的下注区域
	if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 || reqdata.GameId == 206 {
		// 调用_checkRouletteBetArea方法检查下注区域是否合法
		if !c._checkRouletteBetArea(reqdata.RouletteBets) {
			// 如果下注区域不合法,返回错误
			ctx.RespErrString(true, &errcode, "下注区域不正确")
			return
		}
	}

	// 推送下注情况
	if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 || reqdata.GameId == 206 {
		// RouletteBets
		var rouletteBetAmount float64
		for _, bet := range reqdata.RouletteBets {
			rouletteBetAmount += bet.Amount
		}
		go func(gameId int, betAmount float64, userId int, chainType int) {
			if c.socketController != nil {
				c.socketController.push_bet(gameId, betAmount, userId, chainType)
			} else {
				logs.Error("推送下注情况失败: SocketController 未初始化")
			}
		}(reqdata.GameId, rouletteBetAmount, token.UserId, reqdata.ChainType)
	} else {
		betAmount := reqdata.BetAmount
		if reqdata.BetAmount2 > 0 {
			betAmount += reqdata.BetAmount2
		}
		go func(gameId int, betAmount float64, userId int, chainType int) {
			if c.socketController != nil {
				c.socketController.push_bet(gameId, betAmount, userId, chainType)
			} else {
				logs.Error("推送下注情况失败: SocketController 未初始化")
			}
		}(reqdata.GameId, betAmount, token.UserId, reqdata.ChainType)
	}

	// 从数据库中获取用户数据
	userdata, _ := server.XDb().Table("x_user").Where("UserId = ?", token.UserId).First()
	// 获取用户的游戏限制配置
	GameLimit := userdata.String("GameLimit")
	// 如果游戏限制配置为空,则设置为默认的空JSON对象
	if GameLimit == "" {
		GameLimit = "{}"
	}

	// 初始化第一个下注区域的最小和最大限额为0
	limitmin := 0.0
	limitmax := 0.0
	// 初始化第二个下注区域的最小和最大限额为0
	limitmin2 := 0.0
	limitmax2 := 0.0
	// 创建一个空的map用于存储解析后的游戏限制数据
	jlimit := map[string]interface{}{}
	// 将GameLimit字符串解析为JSON对象并存储到jlimit中
	json.Unmarshal([]byte(GameLimit), &jlimit)
	// 根据房间等级获取对应的限制配置
	jroom, ok := jlimit[fmt.Sprintf("%d", reqdata.RoomLevel)]
	// 如果房间等级的限制配置存在
	if ok {
		// 获取USDT相关的限制配置
		jsymbol, ok := jroom.(map[string]interface{})["usdt"]
		// 如果USDT配置存在
		if ok {
			// 检查USDT配置的状态是否为1(启用)
			if jsymbol.(map[string]interface{})["state"].(float64) == 1 {
				// 获取当前游戏ID的限制配置
				jgame, ok := jsymbol.(map[string]interface{})["games"].(map[string]interface{})[fmt.Sprintf("%d", reqdata.GameId)]
				// 如果游戏限制配置存在
				if ok {
					// 如果游戏限制配置为true(启用)
					if jgame.(bool) {
						// 如果游戏ID是104或204且下注区域是"和"
						if (reqdata.GameId == 104 || reqdata.GameId == 204) && reqdata.BetArea == "和" {
							// 获取"和"的限额配置
							jd := jsymbol.(map[string]interface{})["data"].(map[string]interface{})["3"].(map[string]interface{})
							limitmin = jd["min"].(float64)
							limitmax = jd["max"].(float64)
						} else if reqdata.GameId == 105 || reqdata.GameId == 205 {
							// 如果游戏ID是105或205,获取对应的限额配置
							jd := jsymbol.(map[string]interface{})["data"].(map[string]interface{})["2"].(map[string]interface{})
							limitmin = jd["min"].(float64)
							limitmax = jd["max"].(float64)
						} else {
							// 其他情况,获取默认的限额配置
							jd := jsymbol.(map[string]interface{})["data"].(map[string]interface{})["1"].(map[string]interface{})
							limitmin = jd["min"].(float64)
							limitmax = jd["max"].(float64)
						}
						// 如果有第二个下注区域
						if reqdata.BetAmount2 > 0 {
							// 如果游戏ID是104或204且第二个下注区域是"和"
							if (reqdata.GameId == 104 || reqdata.GameId == 204) && reqdata.BetArea2 == "和" {
								// 获取第二个下注区域"和"的限额配置
								jd := jsymbol.(map[string]interface{})["data"].(map[string]interface{})["3"].(map[string]interface{})
								limitmin2 = jd["min"].(float64)
								limitmax2 = jd["max"].(float64)
							} else {
								// 其他情况,获取第二个下注区域的默认限额配置
								jd := jsymbol.(map[string]interface{})["data"].(map[string]interface{})["1"].(map[string]interface{})
								limitmin2 = jd["min"].(float64)
								limitmax2 = jd["max"].(float64)
							}
						}
					}
				}
			}
		}
	}

	// 检查第一个下注区域的限额是否有效
	if limitmin > 0 && limitmax > 0 {
		// 如果下注金额小于最低限额,返回错误
		if reqdata.BetAmount < limitmin {
			ctx.RespErrString(true, &errcode, "下注低限额")
			return
		}
		// 如果下注金额大于最高限额,返回错误
		if reqdata.BetAmount > limitmax {
			ctx.RespErrString(true, &errcode, "下注高限额")
			return
		}
	}

	// 检查第二个下注区域的限额是否有效
	if limitmin2 > 0 && limitmax2 > 0 {
		// 如果第二个下注金额小于最低限额,返回错误
		if reqdata.BetAmount2 < limitmin2 {
			ctx.RespErrString(true, &errcode, "下注低限额")
			return
		}
		// 如果第二个下注金额大于最高限额,返回错误
		if reqdata.BetAmount2 > limitmax2 {
			ctx.RespErrString(true, &errcode, "下注高限额")
			return
		}
	}

	// 针对轮盘游戏的限红检查
	rouletteGameIds := []int{206, 106, 116, 126, 136} // 定义轮盘游戏ID列表
	// 如果当前游戏ID在轮盘游戏ID列表中
	if funk.Contains(rouletteGameIds, reqdata.GameId) {
		// 调用轮盘限红检查函数
		err := rouletteLimitCheck(reqdata.RouletteBets, reqdata.GameId, token)
		// 如果检查失败,返回错误
		if err != nil {
			ctx.RespErrString(true, &errcode, err.Error())
			return
		}
	}

	// 获取渠道ID和卖家ID
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)

	// 检查限额是否未设置或游戏ID为104或204且下注区域为"和"
	if ((limitmin <= 0 || limitmax <= 0) || (limitmin2 <= 0 || limitmax2 <= 0) && (reqdata.GameId == 104 || reqdata.GameId == 204)) && (reqdata.BetArea == "和" || reqdata.BetArea2 == "和") {
		// 创建数据库查询条件
		where := abugo.AbuDbWhere{}
		// 获取代理用户ID
		agentUserId := server.GetAgentIndependenceUserId(ctx, reqdata.Host, reqdata.AgentCode)
		// 添加查询条件: 卖家ID
		where.Add("and", "SellerId", "=", SellerId, nil)
		// 添加查询条件: 渠道ID
		where.Add("and", "ChannelId", "=", ChannelId, nil)
		// 添加查询条件: 游戏ID
		where.Add("and", "GameId", "=", reqdata.GameId, nil)
		// 添加查询条件: 房间等级
		where.Add("and", "RoomLevel", "=", reqdata.RoomLevel, nil)
		// 如果代理用户ID大于0,添加代理查询条件
		if agentUserId > 0 {
			where.Add("and", "TopAgentId", "=", agentUserId, 0)
		}
		// 从数据库中获取游戏数据
		data, _ := server.Db().Table("x_game").Where(where).GetOne()
		// 如果未找到数据,返回错误
		if data == nil {
			ctx.RespErrString(true, &errcode, "下注失败,游戏或房间不存在")
			return
		}
		// 检查第二个下注区域是否为"和"
		if reqdata.BetArea2 == "和" {
			// 如果第二个下注金额小于最低限额,返回错误
			if reqdata.BetAmount2 < abugo.GetFloat64FromInterface((*data)["UsdtLimitMinHe"]) {
				ctx.RespErrString(true, &errcode, "下注低限额")
				return
			}
			// 如果第二个下注金额大于最高限额,返回错误
			if reqdata.BetAmount2 > abugo.GetFloat64FromInterface((*data)["UsdtLimitMaxHe"]) {
				ctx.RespErrString(true, &errcode, "下注高限额")
				return
			}
		} else {
			// 如果第一个下注金额小于最低限额,返回错误
			if reqdata.BetAmount < abugo.GetFloat64FromInterface((*data)["UsdtLimitMinHe"]) {
				ctx.RespErrString(true, &errcode, "下注低限额")
				return
			}
			// 如果第一个下注金额大于最高限额,返回错误
			if reqdata.BetAmount > abugo.GetFloat64FromInterface((*data)["UsdtLimitMaxHe"]) {
				ctx.RespErrString(true, &errcode, "下注高限额")
				return
			}
		}
	}
	// 初始化交易ID为空字符串
	txid := ""
	// 如果游戏ID在201到206之间,则需要进行链上交易
	if reqdata.GameId == 201 || reqdata.GameId == 202 || reqdata.GameId == 203 || reqdata.GameId == 204 || reqdata.GameId == 205 || reqdata.GameId == 206 {
		var url string
		// 根据链类型选择不同的API接口
		if reqdata.ChainType == 1 {
			url = c.hashmakerapi + "/api/send" // 波场链
		} else if reqdata.ChainType == 2 {
			url = c.hashmakerethapi + "/api/eth/send" // 以太坊链
		} else if reqdata.ChainType == 3 {
			url = c.hashmakerbscapi + "/api/bsc/send" // BSC链
		} else if reqdata.ChainType == 4 {
			url = c.hashmakerbscapi + "/api/sol/send" // Solana链
		}

		// 发送POST请求到指定的URL
		resp, err := req.Post(url)
		// 如果请求出错,记录错误日志并返回错误响应
		if err != nil {
			logs.Error("bet error:", url, err)
			ctx.RespErrString(true, &errcode, "下注失败,请稍后再试")
			return
		}
		// 确保响应体在函数结束时关闭
		defer resp.Response().Body.Close()
		// 读取响应体内容
		body, err := io.ReadAll(resp.Response().Body)
		// 如果读取出错,记录错误日志并返回错误响应
		if err != nil {
			logs.Error("bet error:", url, err)
			ctx.RespErrString(true, &errcode, "下注失败,请稍后再试")
			return
		}
		// 将响应体内容解析为JSON对象
		jdata := map[string]interface{}{}
		json.Unmarshal(body, &jdata)
		// 记录获取到的交易ID日志
		logs.Info("bet get txid: ", url, string(body), jdata)
		// 从JSON对象中提取交易ID
		txid = jdata["txid"].(string)
		// 如果交易ID为空,记录错误日志并返回错误响应
		if txid == "" {
			logs.Error("bet error get txid: ", url, string(body), jdata)
			ctx.RespErrString(true, &errcode, "下注失败,请稍后再试")
			return
		}
	}

	// 获取顶级代理ID
	TopAgentId := server.GetTopAgentId(ctx, reqdata.Host, reqdata.AgentCode)

	// 记录调试日志,包括用户ID、期号、游戏ID、房间等级、下注区域、下注金额、交易ID和顶级代理ID
	logs.Debug("x_game_bet:", token.UserId, reqdata.Period, reqdata.GameId, reqdata.RoomLevel, reqdata.BetArea, reqdata.BetAmount, txid, TopAgentId)

	// 调用存储过程进行批量下注操作
	var data *map[string]interface{}
	if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 || reqdata.GameId == 206 {
		for _, bet := range reqdata.RouletteBets {
			var result interface{}
			result, err = server.Db().CallProcedure("x_game_bet_batch_v4",
				token.UserId, reqdata.Period, reqdata.GameId, reqdata.RoomLevel,
				bet.Area, bet.Amount, reqdata.BetArea2, reqdata.BetAmount2,
				txid, TopAgentId, ChannelId, reqdata.UtType, reqdata.ThirdIp,
				getLanguage(reqdata.Lang), reqdata.ChainType)
			if resultMap, ok := result.(*map[string]interface{}); ok {
				data = resultMap
			}
		}
	} else {
		var result interface{}
		result, err = server.Db().CallProcedure("x_game_bet_batch_v4",
			token.UserId, reqdata.Period, reqdata.GameId, reqdata.RoomLevel,
			reqdata.BetArea, reqdata.BetAmount, reqdata.BetArea2, reqdata.BetAmount2,
			txid, TopAgentId, ChannelId, reqdata.UtType, reqdata.ThirdIp,
			getLanguage(reqdata.Lang), reqdata.ChainType)
		if resultMap, ok := result.(*map[string]interface{}); ok {
			data = resultMap
		}
	}

	// 检查存储过程返回的错误码
	if data == nil {
		ctx.RespErrString(true, &errcode, "下注失败,请稍后再试")
		return
	}
	dberrcode, ok := (*data)["errcode"]
	if ok {
		// 如果错误码为1062,表示下注失败,返回错误响应
		if abugo.GetInt64FromInterface(dberrcode) == 1062 {
			ctx.RespErrString(true, &errcode, "下注失败,请稍后再试")
			return
		}
	}
	// 如果存储过程调用出错,返回错误响应
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 如果存储过程返回错误,返回错误响应
	if ctx.RespProcedureErr(data) {
		return
	}
	// 异步发送哈希下注信息
	go c.send_pub_hash_bet(token.UserId, reqdata.GameId, reqdata.RoomLevel, reqdata.BetAmount, reqdata.Period, reqdata.BetArea, reqdata.BetAmount2, reqdata.BetArea2)

	// 向数据分析平台上报投注事件
	go func(transactionId string, orderData *map[string]interface{}) {
		defer recover()
		if err != nil {
			logs.Error("bet 获取用户信息失败: userID=%d, err=%v", token.UserId, err)
			return
		}
		// 获取订单ID，优先使用存储过程返回的ID，其次使用交易ID
		var orderID string
		if orderData != nil {
			if id, exists := (*orderData)["Id"]; exists && id != nil {
				orderID = fmt.Sprintf("%v", id)
			} else if id, exists := (*orderData)["id"]; exists && id != nil {
				orderID = fmt.Sprintf("%v", id)
			}
		}
		// 如果没有从存储过程获取到ID，则使用交易ID
		if orderID == "" {
			orderID = transactionId
		}

		// 确定游戏类型
		var gameType string
		switch reqdata.GameId {
		case 1, 101, 131, 201, 301, 331:
			gameType = "哈希大小"
		case 2, 102, 132, 202, 302, 332:
			gameType = "哈希单双"
		case 3, 103, 133, 203:
			gameType = "哈希幸运"
		case 4, 104, 134, 204:
			gameType = "哈希庄闲"
		case 5, 105, 135, 205:
			gameType = "哈希牛牛"
		case 106, 116, 126, 136, 206:
			gameType = "哈希轮盘"
		default:
			gameType = "哈希游戏"
		}

		// 计算总投注金额
		totalBetAmount := reqdata.BetAmount
		if reqdata.BetAmount2 > 0 {
			totalBetAmount += reqdata.BetAmount2
		}

		// 轮盘游戏计算总金额
		if reqdata.GameId == 106 || reqdata.GameId == 116 || reqdata.GameId == 126 || reqdata.GameId == 136 || reqdata.GameId == 206 {
			totalBetAmount = 0
			for _, bet := range reqdata.RouletteBets {
				totalBetAmount += bet.Amount
			}
		}

		// 上报投注结果事件到ThinkingData
		orderIdStr := fmt.Sprintf("%d", abugo.GetInt64FromInterface((*data)["Id"])) // 从存储过程返回的订单ID转为字符串
		err = datapush.SendBetResultEvent(
			int32(token.UserId),               // 用户ID
			orderIdStr,                        // 订单ID（字符串）
			fmt.Sprintf("%d", reqdata.GameId), // 游戏ID
			reqdata.BetArea,                   // 投注方法（下注区域）
			gameType,                          // 投注类型（游戏类型）
			totalBetAmount,                    // 投注金额
			"success",                         // 投注状态（成功）
			0,                                 // 盈亏金额（下注时为0，开奖后更新）
			"USDT",                            // 币种
			reqdata.ThirdIp,                   // 客户端IP
		)
		if err != nil {
			logs.Error("bet_result 上报投注结果事件失败: userID=%d, gameId=%d, orderID=%s, err=%v", token.UserId, reqdata.GameId, orderIdStr, err)
		} else {
			logs.Info("bet_result 投注结果事件上报成功: userID=%d, gameId=%d, orderID=%s, amount=%.2f",
				token.UserId, reqdata.GameId, orderIdStr, totalBetAmount)
		}
	}(txid, data)

	// 返回成功响应
	ctx.RespOK(data)
}

// rebet撤销下注
func (c *LotteryController) rebet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId    int
		ChainType int
		Cnt       int // 撤回笔数
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, &errcode, "请先登录")
		return
	}

	// 如果未指定撤回笔数，默认为1
	if reqdata.Cnt <= 0 {
		reqdata.Cnt = 1
	}

	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		// 查询用户最近的未开奖订单，按照撤回笔数限制
		orders, err := tx.XOrder.WithContext(nil).
			Where(tx.XOrder.UserID.Eq(int32(token.UserId))).
			Where(tx.XOrder.GameID.Eq(int32(reqdata.GameId))).
			Where(tx.XOrder.DataState.Eq(-1)).
			Order(tx.XOrder.ID.Desc()).
			Limit(reqdata.Cnt).
			Find()

		if err != nil {
			return err
		}

		if len(orders) == 0 {
			return errors.New("没有可撤回的订单")
		}

		// 遍历所有需要撤回的订单
		for _, order := range orders {
			// 删除订单
			_, err := tx.XOrder.WithContext(nil).Where(tx.XOrder.ID.Eq(order.ID)).Delete()
			if err != nil {
				return err
			}

			// 账变回退
			activeAddUseBalancerInfo := struct {
				UserId            int32
				ActiveName        string
				RealAmount        float64
				WithdrawLiuSuiAdd float64
				BalanceCReason    int
			}{
				UserId:            int32(token.UserId),
				ActiveName:        "注单撤销",
				RealAmount:        order.Amount,
				WithdrawLiuSuiAdd: 0,
				BalanceCReason:    9,
			}
			// 更新用户流水和账变记录
			err = ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		ctx.RespErrString(true, &errcode, "撤回失败: "+err.Error())
		return
	}

	ctx.RespOK()
}

func rouletteLimitCheck(betArea []RouletteBet, gameId int, token *server.TokenData) error {
	xGame := server.DaoxHashGame().XGame
	var gameInfos model.XGame
	err := xGame.WithContext(nil).Where(xGame.SellerID.Eq(int32(token.SellerId))).
		Where(xGame.GameID.Eq(int32(gameId))).
		Scan(&gameInfos)

	if err != nil {
		return err
	}

	var rewardRateEx RewardRateEx
	jsonErr := json.Unmarshal([]byte(gameInfos.RewardRateEx), &rewardRateEx)
	if jsonErr != nil {
		logs.Error("Failed to unmarshal RewardRateEx: %v", jsonErr)
		return jsonErr
	}

	// betType, err := getRouletteBetType(betArea)
	// if err != nil {
	// 	return err
	// }
	betTypeArrs := map[string]string{
		"num":     "数字",
		"color":   "红黑",
		"size":    "大小",
		"oddEven": "单双",
		"col":     "列",
		"row":     "打",
		"jiaozhu": "角注",
		"jiezhu":  "街注",
		"sanshu":  "三数",
		"fenzhu":  "分注",
		"xianzhu": "线注",
	}

	for _, bet := range betArea {
		betType := bet.Type
		betAmount := bet.Amount

		var limitMin float64
		var limitMax float64
		switch betType {
		case "num":
			limitMin = float64(rewardRateEx.Limit.Num.Min)
			limitMax = float64(rewardRateEx.Limit.Num.Max)
		case "color":
			limitMin = float64(rewardRateEx.Limit.Color.Min)
			limitMax = float64(rewardRateEx.Limit.Color.Max)
		case "size":
			limitMin = float64(rewardRateEx.Limit.Size.Min)
			limitMax = float64(rewardRateEx.Limit.Size.Max)
		case "oddEven":
			limitMin = float64(rewardRateEx.Limit.OddEven.Min)
			limitMax = float64(rewardRateEx.Limit.OddEven.Max)
		case "col":
			limitMin = float64(rewardRateEx.Limit.Col.Min)
			limitMax = float64(rewardRateEx.Limit.Col.Max)
		case "row":
			limitMin = float64(rewardRateEx.Limit.Row.Min)
			limitMax = float64(rewardRateEx.Limit.Row.Max)
		case "jiaozhu":
			limitMin = float64(rewardRateEx.Limit.Jiaozhu.Min)
			limitMax = float64(rewardRateEx.Limit.Jiaozhu.Max)
		case "jiezhu":
			limitMin = float64(rewardRateEx.Limit.Jiezhu.Min)
			limitMax = float64(rewardRateEx.Limit.Jiezhu.Max)
		case "sanshu":
			limitMin = float64(rewardRateEx.Limit.Sanshu.Min)
			limitMax = float64(rewardRateEx.Limit.Sanshu.Max)
		case "fenzhu":
			limitMin = float64(rewardRateEx.Limit.Fenzhu.Min)
			limitMax = float64(rewardRateEx.Limit.Fenzhu.Max)
		case "xianzhu":
			limitMin = float64(rewardRateEx.Limit.Xianzhu.Min)
			limitMax = float64(rewardRateEx.Limit.Xianzhu.Max)
		case "sishu":
			limitMin = float64(rewardRateEx.Limit.Sishu.Min)
			limitMax = float64(rewardRateEx.Limit.Sishu.Max)
		default:
			return errors.New("未知的投注类型")

		}

		if betAmount < limitMin {
			return errors.New(betTypeArrs[betType] + "下注低限额")
		}
		if betAmount > limitMax {
			return errors.New(betTypeArrs[betType] + "下注高限额")
		}
	}
	return nil
}

func (c *LotteryController) get(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		TxId string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, &errcode, "请先登录")
		return
	}
	data, err := server.XDb().Table("x_order").Where("UserId = ? and TxId = ?", token.UserId, reqdata.TxId).First()
	if err != nil {
		ctx.RespErrString(true, &errcode, "读取订单失败")
		return
	}
	if data == nil {
		ctx.RespErrString(true, &errcode, "订单不存在")
		return
	}
	b, _ := json.Marshal(data.RawData)
	logs.Debug("lottery get :"+reqdata.TxId, string(b))
	ctx.RespOK(data.Map())
}

// 处理哈希推送
func (c *LotteryController) send_pub_hash_bet(userId, gameId, room_level int, bet_amount float64, period, bet_area string, bet_amount2 float64, bet_area2 string) {
	defer recover()
	base := make(map[string]interface{})
	if gameId == 1 || gameId == 101 || gameId == 131 || gameId == 201 || gameId == 301 || gameId == 331 { // 大小
		base["大"] = 0
		base["小"] = 0
		base["da_number"] = 0
		base["xiao_number"] = 0
	} else if gameId == 2 || gameId == 102 || gameId == 132 || gameId == 202 || gameId == 302 || gameId == 332 { // 单双
		base["单"] = 0
		base["双"] = 0
		base["dan_number"] = 0
		base["shuang_number"] = 0
	} else if gameId == 3 || gameId == 103 || gameId == 133 || gameId == 203 { // 幸运
		base["value"] = 0
		bet_area = "value"
		base["value_number"] = 0
	} else if gameId == 4 || gameId == 104 || gameId == 134 || gameId == 204 { // 庄闲
		base["庄"] = 0
		base["闲"] = 0
		base["和"] = 0
		base["zhuang_number"] = 0
		base["xian_number"] = 0
		base["he_number"] = 0
	} else if gameId == 5 || gameId == 105 || gameId == 135 || gameId == 205 { // 牛牛
		base["value"] = 0
		bet_area = "value"
		base["value_number"] = 0
	} else {
		return
	}
	now := time.Now()
	// 获取今天的日期
	year, month, day := now.Date()
	// 获取明天的日期
	tomorrow := time.Date(year, month, day, 0, 0, 0, 0, now.Location()).AddDate(0, 0, 1)
	// 计算今天结束的时间戳
	expired := tomorrow.Unix() - now.Unix()
	timeDate := now.Format("2006-01-02")
	var rkey string
	if period == "0" {
		rkey = fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d", server.Project(), server.Module(), timeDate, gameId, room_level)
	} else {
		// 一分/3分哈希
		rkey = fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d:%s", server.Project(), server.Module(), timeDate, gameId, room_level, period)
	}
	exists := server.Redis().Exists(rkey)
	if !exists {
		base["gameId"] = gameId
		base["room_level"] = room_level
		base["period"] = period
		setOk := server.Redis().HMSet(rkey, base)
		if !setOk {
			logs.Error("send_pub_hash_bet HMSet error")
			return
		}
		err := server.Redis().Expire(rkey, int(expired))
		if err != nil {
			logs.Error("send_pub_hash_bet Expire error")
			return
		}
	}
	err := PutHashDataRedis(userId, rkey, bet_amount, bet_area, expired)
	if err != nil {
		logs.Error("send_pub_hash_bet PutHashDataRedis error")
		return
	}
	if bet_area2 != "" && bet_amount2 > 0 {
		err = PutHashDataRedis(userId, rkey, bet_amount2, bet_area2, expired)
		if err != nil {
			logs.Error("send_pub_hash_bet PutHashDataRedis2 error")
			return
		}
	}
	getAll, err := server.Redis().HGetAll(rkey)
	if err != nil {
		logs.Error("send_pub_hash_bet HGetAll error:", err)
		return
	}
	err = server.Redis().Publish(fmt.Sprintf("%v:%v:sub_hash_bet", server.Project(), server.Module()), getAll)
	if err != nil {
		logs.Error("send_pub_hash_bet Publish error:", err)
		return
	}
}

// PutHashDataRedis 设置金额和人数
func PutHashDataRedis(userId int, rkey string, bet_amount float64, bet_area string, expired int64) (err error) {
	// 添加金额
	err = server.Redis().HIncrByFloat(rkey, bet_area, bet_amount)
	if err != nil {
		logs.Error("send_pub_hash_bet HIncrByFloat error:", err)
		return
	}
	// 获取投注区块人数
	skey := fmt.Sprintf("%s:%s", rkey, bet_area)
	userIdStr := fmt.Sprintf("%v", userId)
	sIsMember := server.Redis().SIsMember(skey, userIdStr)
	if !sIsMember {
		exists := server.Redis().Exists(skey)
		err = server.Redis().SAdd(skey, userIdStr)
		if err != nil {
			logs.Error("send_pub_hash_bet SAdd error:", err)
			return
		}
		var area_number string
		switch bet_area {
		case "大":
			area_number = "da_number"
		case "小":
			area_number = "xiao_number"
		case "单":
			area_number = "dan_number"
		case "双":
			area_number = "shuang_number"
		case "庄":
			area_number = "zhuang_number"
		case "闲":
			area_number = "xian_number"
		case "和":
			area_number = "he_number"
		default:
			area_number = "value_number"
		}
		err = server.Redis().HIncrBy(rkey, area_number, 1)
		if err != nil {
			logs.Error("send_pub_hash_bet HIncrBy error:", err)
			return
		}
		if !exists {
			// 设置集合的过期时间
			err = server.Redis().Expire(skey, int(expired))
			if err != nil {
				logs.Error("send_pub_hash_bet Expire error")
				return
			}
		}
	}
	return
}

func (c *LotteryController) get_hash_bet_info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId int    `validate:"required"`
		Period string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 获取今天的日期
	timeDate := time.Now().Format("2006-01-02")
	rkeys := make([]string, 0, 3)
	projectName := server.Project()
	moduleName := server.Module()
	if reqdata.Period == "0" {
		rkeys = append(rkeys, fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d", projectName, moduleName, timeDate, reqdata.GameId, 1),
			fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d", projectName, moduleName, timeDate, reqdata.GameId, 2),
			fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d", projectName, moduleName, timeDate, reqdata.GameId, 3))
	} else {
		// 一分/3分哈希
		rkeys = append(rkeys, fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d:%s", projectName, moduleName, timeDate, reqdata.GameId, 1, reqdata.Period),
			fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d:%s", projectName, moduleName, timeDate, reqdata.GameId, 2, reqdata.Period),
			fmt.Sprintf("%v:%v:pub_hash_bet:%v:%d:%d:%s", projectName, moduleName, timeDate, reqdata.GameId, 2, reqdata.Period))
	}
	result := make([]any, 0, 3)
	for i, rkey := range rkeys {
		betInfo := get_redis_hash_bet_info(reqdata.GameId, i+1, rkey, reqdata.Period)
		result = append(result, betInfo)
	}
	ctx.RespOK(result)
}

func get_redis_hash_bet_info(gameId, roomLevel int, rkey, period string) any {
	getAllMap, err := server.Redis().HGetAll(rkey)
	_, ok := getAllMap["gameId"]
	if err != nil || !ok {
		base := make(map[string]interface{})
		base["gameId"] = gameId
		base["room_level"] = roomLevel
		base["period"] = period
		if gameId == 1 || gameId == 101 || gameId == 131 || gameId == 201 || gameId == 301 || gameId == 331 { // 大小
			base["大"] = 0
			base["小"] = 0
			base["da_number"] = 0
			base["xiao_number"] = 0
		} else if gameId == 2 || gameId == 102 || gameId == 132 || gameId == 202 || gameId == 302 || gameId == 332 { // 单双
			base["单"] = 0
			base["双"] = 0
			base["dan_number"] = 0
			base["shuang_number"] = 0
		} else if gameId == 3 || gameId == 103 || gameId == 133 || gameId == 203 { // 幸运
			base["value"] = 0
			base["value_number"] = 0
		} else if gameId == 4 || gameId == 104 || gameId == 134 || gameId == 204 { // 庄闲
			base["庄"] = 0
			base["闲"] = 0
			base["和"] = 0
			base["zhuang_number"] = 0
			base["xian_number"] = 0
			base["he_number"] = 0
		} else if gameId == 5 || gameId == 105 || gameId == 135 || gameId == 205 { // 牛牛
			base["value"] = 0
			base["value_number"] = 0
		}
		return base
	}
	return getAllMap
}

func (c *LotteryController) get_config(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		SellerId int `validate:"required"`
		Host     string
	}{}
	channelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	// 游戏筹码
	gameChip := server.GetConfigString(SellerId, channelId, "GameChip")
	type Chip struct {
		Chip   int  `json:"chip"`
		Select int8 `json:"select"`
	}
	var chipDatas []Chip
	var result string
	err = json.Unmarshal([]byte(gameChip), &chipDatas)
	if err == nil {
		for i := len(chipDatas); i < 15; i++ {
			chipDatas = append(chipDatas, Chip{})
		}
		chipData, _ := json.Marshal(chipDatas)
		result = string(chipData)
	}
	if token != nil {
		// 获取自定义筹码
		userSettingTb := server.DaoxHashGame().XUserSetting
		userSettingDb := server.DaoxHashGame().XUserSetting.WithContext(context.Background())
		setting, err := userSettingDb.Select(userSettingTb.ChipList).Where(userSettingTb.UserID.Eq(int32(token.UserId))).First()
		if err == nil {
			result = setting.ChipList
		}
	}
	ctx.Put("GameChip", result)
	ctx.RespOK()
}

func (c *LotteryController) _checkRouletteBetArea(betArea []RouletteBet) bool {
	betAreaSlice := []string{
		"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12",
		"13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24",
		"25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36",
		"单", "双", "红", "黑", "大", "小", "打一", "打二", "打三", "列一", "列二", "列三",
	}

	specialBetAreaSlice := []string{
		"角注", "街注", "三数", "分注", "线注", "四个号码",
	}

	if len(betArea) == 0 {
		return false
	}

	for _, bet := range betArea {

		if !strings.Contains(bet.Area, "-") && !funk.Contains(betAreaSlice, bet.Area) {
			return false
		}

		if strings.Contains(bet.Area, "-") {
			betStr := strings.Split(bet.Area, "-")
			if len(betStr) != 2 {
				return false
			}
			betStrArea := betStr[0]
			betStrNum := strings.Split(betStr[1], ",")
			if betStrArea != "" && funk.Contains(specialBetAreaSlice, betStrArea) {
				if betStrArea == "角注" && len(betStrNum) != 4 {
					return false
				}
				if betStrArea == "街注" && len(betStrNum) != 3 {
					return false
				}
				if betStrArea == "三数" && len(betStrNum) != 3 {
					return false
				}
				if betStrArea == "分注" && len(betStrNum) != 2 {
					return false
				}
				if betStrArea == "线注" && len(betStrNum) != 6 {
					return false
				}
				if betStrArea == "四个号码" && len(betStrNum) != 4 {
					return false
				}
			}
		}
	}
	return true
}

func (c *LotteryController) check_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address []string `validate:"required"`
	}

	type BalanceInfo struct {
		TRX  float64 `json:"trx"`
		USDT float64 `json:"usdt"`
	}

	// 解析请求数据
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// TRON API密钥列表
	keys := []string{
		"1ce6e82d-d251-437d-b039-04cd698373ad",
		"beec7111-3912-4dc1-82a1-29c1747887f2",
		"8bdc9c2a-2c3b-4816-a688-61c43e0d9e77",
		"8554a080-80bf-4961-a375-8a39d4314070",
		"f69528e4-a51c-45d3-a3f1-6ec4467fdabe",
		"112655ad-9e22-4a3d-aff4-83a9f27fe57f",
	}
	keysIndex := 0

	result := make(map[string]BalanceInfo)

	// 遍历每个地址查询余额
	for _, address := range reqdata.Address {
		balance := BalanceInfo{
			TRX:  0,
			USDT: 0,
		}

		// 验证地址格式
		if !invaildAddress.InvalidTronAddress(address) {
			logs.Error("Invalid address: %s", address)
			result[address] = balance
			continue
		}

		// 构建API请求
		url := fmt.Sprintf("https://apilist.tronscanapi.com/api/account/tokens?address=%s", address)

		// 设置请求头
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logs.Error("Failed to create request for address %s: %v", address, err)
			result[address] = balance
			continue
		}

		// 轮询使用API密钥
		req.Header.Set("TRON-PRO-API-KEY", keys[keysIndex%len(keys)])
		keysIndex++

		// 发送HTTP请求
		client := &http.Client{Timeout: 10 * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			logs.Error("Failed to request API for address %s: %v", address, err)
			result[address] = balance
			continue
		}
		defer resp.Body.Close()

		// 检查HTTP状态码
		if resp.StatusCode != 200 {
			logs.Error("API request failed for address %s, status code: %d", address, resp.StatusCode)
			result[address] = balance
			continue
		}

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			logs.Error("Failed to read response for address %s: %v", address, err)
			result[address] = balance
			continue
		}

		// 记录API响应以便调试
		logs.Info("API response for address %s: %s", address, string(body))

		// 解析JSON响应
		type TokenInfo struct {
			TokenType string      `json:"tokenType"`
			TokenName string      `json:"tokenName"`
			Quantity  json.Number `json:"quantity"`
		}

		type APIResponse struct {
			Data    []TokenInfo `json:"data"`
			Success bool        `json:"success"`
			Error   string      `json:"error"`
			Message string      `json:"message"`
		}

		var apiResp APIResponse
		err = json.Unmarshal(body, &apiResp)
		if err != nil {
			logs.Error("Failed to parse JSON for address %s: %v, response body: %s", address, err, string(body))
			result[address] = balance
			continue
		}

		// 检查API是否返回错误
		if len(apiResp.Data) == 0 {
			logs.Info("No token data found for address %s", address)
		}

		// 解析代币余额
		for _, token := range apiResp.Data {
			if token.TokenType == "trc10" && token.TokenName == "trx" {
				if quantity, err := token.Quantity.Float64(); err == nil {
					// 保留两位小数，确保精度问题
					balance.TRX = math.Round(quantity*100) / 100
				}
			} else if token.TokenType == "trc20" && token.TokenName == "Tether USD" {
				if quantity, err := token.Quantity.Float64(); err == nil {
					// 保留两位小数，确保精度问题
					balance.USDT = math.Round(quantity*100) / 100
				}
			}
		}

		result[address] = balance

		// 添加延迟避免API限制
		time.Sleep(100 * time.Millisecond)
	}

	// 返回结果
	ctx.RespOK(result)
}

// getRouletteBetType 根据不同的 bet 返回相应的类型
func getRouletteBetType(bet string) (string, error) {
	// 尝试将 bet 转换为数字类型
	if betNumber, err := strconv.Atoi(bet); err == nil {
		if betNumber >= 0 && betNumber <= 36 {
			return "num", nil
		}
	}

	// 判断单双
	if bet == "单" || bet == "双" {
		return "oddEven", nil
	}

	// 判断大小 (small/large)
	if bet == "大" || bet == "小" {
		return "size", nil
	}

	// 判断红黑 (red/black)
	if bet == "红" || bet == "黑" {
		return "color", nil
	}

	// 判断打几 (打一、打二、打三)
	if bet == "打一" || bet == "打二" || bet == "打三" {
		return "row", nil
	}

	// 判断列 (列一、列二、列三)
	if bet == "列一" || bet == "列二" || bet == "列三" {
		return "col", nil
	}

	// 分割带 "-" 的字符串并提取类型
	betArr := strings.Split(bet, "-")
	if len(betArr) == 2 {
		betStr := betArr[0]

		switch betStr {
		case "角注":
			return "jiaozhu", nil
		case "街注":
			return "jiezhu", nil
		case "三数":
			return "sanshu", nil
		case "分注":
			return "fenzhu", nil
		case "线注":
			return "xianzhu", nil
		case "四个号码":
			return "sishu", nil
		}
	}

	// 如果未匹配到任何类型，返回错误
	return "", errors.New("无法识别的投注类型")
}
