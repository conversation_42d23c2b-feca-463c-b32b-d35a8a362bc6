package paycontroller

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

// alipay实例
var Alipay = new(alipay)

type alipay struct {
	Base
}

// Init 初始化alipay支付，注册回调路由
func (c *alipay) Init() {
	server.Http().PostNoAuth("/api/pay/alipay/recharge_callback", c.RechargeCallback)
	server.Http().PostNoAuth("/api/pay/alipay/withdraw_callback", c.WithdrawCallback)
}

// RSA签名生成
func (c *alipay) generateSignature(data string, privateKeyPem string, password string) (string, error) {
	// 解析PEM格式的私钥
	block, _ := pem.Decode([]byte(privateKeyPem))
	if block == nil {
		return "", errors.New("PEM私钥解析失败")
	}

	var privateKey *rsa.PrivateKey
	var err error

	if password != "" {
		// 有密码的私钥
		decryptedKey, err := x509.DecryptPEMBlock(block, []byte(password))
		if err != nil {
			return "", fmt.Errorf("私钥解密失败: %v", err)
		}
		privateKey, err = x509.ParsePKCS1PrivateKey(decryptedKey)
		if err != nil {
			return "", fmt.Errorf("私钥格式解析失败: %v", err)
		}
	} else {
		// 无密码的私钥
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("私钥格式解析失败: %v", err)
		}
	}

	// 计算SHA256哈希
	hashed := sha256.Sum256([]byte(data))

	// RSA签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return "", fmt.Errorf("RSA签名失败: %v", err)
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(signature), nil
}

// RSA签名验证
func (c *alipay) verifySignature(data string, signature string, publicKeyPem string) error {
	// 解析PEM格式的公钥
	block, _ := pem.Decode([]byte(publicKeyPem))
	if block == nil {
		return errors.New("PEM公钥解析失败")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("公钥格式解析失败: %v", err)
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return errors.New("不是RSA公钥")
	}

	// Base64解码签名
	signatureBytes, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return fmt.Errorf("签名Base64解码失败: %v", err)
	}

	// 计算SHA256哈希
	hashed := sha256.Sum256([]byte(data))

	// 验证签名
	err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA256, hashed[:], signatureBytes)
	if err != nil {
		return fmt.Errorf("签名验证失败: %v", err)
	}

	return nil
}

// HTTP请求封装
func (c *alipay) httpPost(apiUrl string, data map[string]interface{}, cfg map[string]interface{}) (map[string]interface{}, error) {
	// 生成JSON数据
	dataJson, _ := json.Marshal(data)
	dataString := string(dataJson)

	// 获取私钥配置
	privateKey, ok := cfg["private_key"].(string)
	if !ok || privateKey == "" {
		return nil, errors.New("私钥配置错误")
	}

	password, _ := cfg["private_key_password"].(string)

	// 生成签名
	signature, err := c.generateSignature(dataString, privateKey, password)
	if err != nil {
		return nil, fmt.Errorf("签名生成失败: %v", err)
	}

	// 构建POST参数
	formData := url.Values{}
	formData.Set("data", dataString)
	formData.Set("signature", signature)

	// 发送请求
	resp, err := req.Post(apiUrl, req.Header{
		"Content-Type": "application/x-www-form-urlencoded",
	}, formData.Encode())

	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Response().Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	logs.Info("alipay HTTP response: %s", string(body))

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("响应JSON解析失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.Response().StatusCode < 200 || resp.Response().StatusCode >= 300 {
		message := "未知错误"
		if msg, ok := result["message"].(string); ok {
			message = msg
		}
		return nil, fmt.Errorf("API错误 [%d]: %s", resp.Response().StatusCode, message)
	}

	return result, nil
}

// 支付宝充值接口
func (c *alipay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0
	logs.Info("alipay 进入Recharge, MethodId=%v, Symbol=%v, Amount=%v", req.MethodId, req.Symbol, req.Amount)

	// 参数验证 - 姓名校验
	if req.RealName == "" {
		ctx.RespErr(errors.New("用户姓名不能为空"), &errcode)
		return
	}

	// 获取支付方式
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		logs.Error("alipay 未找到支付方式, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}

	logs.Info("alipay 获取支付方式成功, MethodId=%v", req.MethodId)

	// 币种校验
	if err := c.ValidateSymbol(req.Symbol, payMethodData.String("Symbol")); err != nil {
		logs.Error("alipay 币种校验失败, Symbol=%v", req.Symbol)
		ctx.RespErr(err, &errcode)
		return
	}

	logs.Info("alipay 币种校验通过")

	// 解析配置
	var cfg map[string]interface{}
	err = json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)
	if err != nil {
		logs.Error("alipay ExtraConfig解析失败, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("ExtraConfig解析失败"), &errcode)
		return
	}

	logs.Info("alipay ExtraConfig解析成功")

	// 获取用户信息
	userToken := server.GetToken(ctx)
	user, err := c.getUser(userToken.UserId)
	if err != nil {
		logs.Error("alipay 未找到用户, UserId=%v", userToken.UserId)
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	logs.Info("alipay 获取用户成功, UserId=%v", userToken.UserId)

	// 检查充值限制
	limitReq := &LimitRequest{
		SellerId: fmt.Sprintf("%d", user.SellerID),
		UserID:   fmt.Sprintf("%d", userToken.UserId),
		Amount:   req.Amount,
		Currency: req.Symbol,
		ClientIP: ctx.Gin().ClientIP(),
	}

	limitService := NewLimitService(server.XRedis())
	if err := limitService.CheckLimit(limitReq, 20*time.Second); err != nil {
		logs.Warn("alipay 充值限制触发, UserId=%v, Amount=%v, Error=%v", userToken.UserId, req.Amount, err.Error())
		ctx.RespErr(err, &errcode)
		return
	}

	logs.Info("alipay 充值限制检查通过, UserId=%v, Amount=%v", userToken.UserId, req.Amount)

	// 获取汇率
	rate, err := c.getRechargeRate(req.Symbol, user.SellerID)
	if err != nil {
		logs.Error("alipay 获取汇率失败, Symbol=%v", req.Symbol)
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}

	logs.Info("alipay 获取汇率成功")

	// 创建充值订单
	c.createRechargeOrder(ctx, cfg, rate, user, specialAgent, userToken, req, payMethodData)
	logs.Info("alipay createRechargeOrder已调用")
}

// 创建支付宝充值订单
func (c *alipay) createRechargeOrder(ctx *abugo.AbuHttpContent, cfg map[string]interface{}, rate float64, user *model.XUser, specialAgent int, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap) {
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		errcode := 0
		amount := reqdata.Amount / rate

		// 验证配置参数
		apiUrl, ok := cfg["api_url"].(string)
		if !ok || apiUrl == "" {
			ctx.RespErr(errors.New("API地址配置错误"), &errcode)
			return errors.New("API地址配置错误")
		}

		accountName, ok := cfg["account_name"].(string)
		if !ok || accountName == "" {
			ctx.RespErr(errors.New("商户账号配置错误"), &errcode)
			return errors.New("商户账号配置错误")
		}

		notifyUrl, ok := cfg["notify_url"].(string)
		if !ok || notifyUrl == "" {
			ctx.RespErr(errors.New("回调地址配置错误"), &errcode)
			return errors.New("回调地址配置错误")
		}

		// 从x_finance_method表的PayType字段获取payment_method
		paymentMethod := paymethod.String("PayType")
		if paymentMethod == "" {
			paymentMethod = "alipay" // 默认值
		}

		// 构建 PayData
		payData := map[string]interface{}{
			"PayId":            reqdata.MethodId,
			"Brand":            paymethod.String("Brand"),
			"Name":             paymethod.String("Name"),
			"RealName":         reqdata.RealName,
			"IsRechargeActive": reqdata.IsRechargeActive,
			"Account":          user.Account,
			"PaymentMethod":    paymentMethod,
		}
		payDataBytes, _ := json.Marshal(payData)

		// 创建订单记录
		OrderId, err := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":     user.SellerID,
			"ChannelId":    user.ChannelID,
			"UserId":       user.UserID,
			"Symbol":       reqdata.Symbol,
			"PayId":        reqdata.MethodId,
			"PayType":      28, // 硬编码alipay的PayType=28
			"Amount":       reqdata.Amount,
			"RealAmount":   amount,
			"TransferRate": rate,
			"State":        3, // 待支付
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": specialAgent,
			"TopAgentId":   user.TopAgentID,
			"OrderType":    reqdata.OrderType,
			"PayData":      string(payDataBytes),
		})

		if err != nil {
			ctx.RespErr(errors.New("创建订单失败"), &errcode)
			return err
		}

		// 构建请求参数
		requestData := map[string]interface{}{
			"account_name":           accountName,
			"merchant_order_id":      fmt.Sprintf("%v", OrderId),
			"total_amount":           reqdata.Amount,
			"timestamp":              time.Now().Format(time.RFC3339),
			"notify_url":             notifyUrl + "/api/pay/alipay/recharge_callback",
			"return_url":             notifyUrl, // 支付成功后跳转地址
			"subject":                "支付宝充值",
			"payment_method":         paymentMethod,
			"merchant_user_identity": user.Account,
			"guest_real_name":        reqdata.RealName,
		}

		// 发送充值请求
		result, err := c.httpPost(apiUrl+"/merchant_api/v1/orders/payment", requestData, cfg)
		if err != nil {
			logs.Error("alipay 充值请求失败: %v", err)
			ctx.RespErr(errors.New("充值请求失败"), &errcode)
			return err // 事务回滚
		}

		// 保存三方订单ID
		if thirdId, ok := result["id"].(string); ok && thirdId != "" {
			tx.Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
				"ThirdId": thirdId,
			})
		}

		// 提取支付URL - 统一返回格式
		payUrl := ""
		if url, ok := result["payUrl"].(string); ok {
			payUrl = url
		} else if url, ok := result["payment_url"].(string); ok {
			payUrl = url
		} else if url, ok := result["redirect_url"].(string); ok {
			payUrl = url
		} else if url, ok := result["url"].(string); ok {
			payUrl = url
		}

		logs.Info("alipay 充值订单创建成功: OrderId=%v, ThirdId=%v", OrderId, result["id"])
		// 统一返回格式，与cpay/pay24保持一致
		ctx.RespOK(xgo.H{
			"payurl":  payUrl,
			"orderId": OrderId,
		})
		return nil // 事务提交
	})
}

// 支付宝提现接口
func (c *alipay) Withdraw(ctx *abugo.AbuHttpContent, req *CreateOrderReq) {
	errcode := 0
	logs.Info("alipay 进入Withdraw, MethodId=%v, Symbol=%v, Amount=%v", req.MethodId, req.Symbol, req.Amount)

	// 参数验证 - 使用RealName作为支付宝姓氏
	if req.RealName == "" {
		ctx.RespErr(errors.New("支付宝姓氏不能为空"), &errcode)
		return
	}

	// 参数验证 - 使用AlipayAccount作为支付宝账号
	if req.AlipayAccount == "" {
		ctx.RespErr(errors.New("支付宝账号不能为空"), &errcode)
		return
	}

	// 获取支付方式
	payMethodData, err := server.XDb().Table("x_finance_method").Where("Id = ?", req.MethodId).First()
	if err != nil || payMethodData == nil {
		logs.Error("alipay 未找到提现方式, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("未找到提现方式"), &errcode)
		return
	}

	// 解析配置
	var cfg map[string]interface{}
	err = json.Unmarshal([]byte(payMethodData.String("ExtraConfig")), &cfg)
	if err != nil {
		logs.Error("alipay ExtraConfig解析失败, MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("ExtraConfig解析失败"), &errcode)
		return
	}

	// 获取用户信息
	userToken := server.GetToken(ctx)
	user, err := c.getUser(userToken.UserId)
	if err != nil {
		logs.Error("alipay 未找到用户, UserId=%v", userToken.UserId)
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}

	// 获取汇率
	rate, err := c.getWithdrawRate(req.Symbol, user.SellerID)
	if err != nil {
		logs.Error("alipay 获取提现汇率失败, Symbol=%v", req.Symbol)
		ctx.RespErr(errors.New("获取汇率失败"), &errcode)
		return
	}

	// 创建支付宝提现订单
	c.createWithdrawOrder(ctx, cfg, rate, user, userToken, req, payMethodData)
}

// 创建支付宝提现订单
func (c *alipay) createWithdrawOrder(ctx *abugo.AbuHttpContent, cfg map[string]interface{}, rate float64, user *model.XUser, token *server.TokenData, reqdata *CreateOrderReq, paymethod *xgo.XMap) {
	errcode := 0
	amount := reqdata.Amount / rate

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":         reqdata.MethodId,
		"Brand":         paymethod.String("Brand"),
		"Name":          paymethod.String("Name"),
		"AlipayAccount": reqdata.AlipayAccount, // 使用AlipayAccount字段作为支付宝账号
		"RealName":      reqdata.RealName,      // 保存完整姓名，Worker中会提取首字
	}
	payDataBytes, _ := json.Marshal(payData)

	// 创建提现订单记录
	OrderId, err := server.XDb().Table("x_withdraw").Insert(xgo.H{
		"SellerId":     user.SellerID,
		"ChannelId":    user.ChannelID,
		"UserId":       user.UserID,
		"Symbol":       reqdata.Symbol,
		"PayId":        reqdata.MethodId,
		"PayType":      28, // 硬编码alipay的PayType=28
		"Amount":       reqdata.Amount,
		"RealAmount":   amount,
		"TransferRate": rate,
		"State":        4, // 待审核
		"CSGroup":      user.CSGroup,
		"CSId":         user.CSID,
		"TopAgentId":   user.TopAgentID,
		"PayData":      string(payDataBytes),
	})

	if err != nil {
		ctx.RespErr(errors.New("创建提现订单失败"), &errcode)
		return
	}

	// 获取API地址
	apiUrl, ok := cfg["api_url"].(string)
	if !ok || apiUrl == "" {
		ctx.RespErr(errors.New("API地址配置错误"), &errcode)
		return
	}

	// 获取商户账号
	accountName, ok := cfg["account_name"].(string)
	if !ok || accountName == "" {
		ctx.RespErr(errors.New("商户账号配置错误"), &errcode)
		return
	}

	// 获取回调地址
	notifyUrl, ok := cfg["notify_url"].(string)
	if !ok || notifyUrl == "" {
		ctx.RespErr(errors.New("回调地址配置错误"), &errcode)
		return
	}

	// 提取姓氏首字（支付宝姓氏要求）
	surname := ""
	if len(reqdata.RealName) > 0 {
		// 获取第一个字符作为姓氏
		runes := []rune(reqdata.RealName)
		surname = string(runes[0])
	}

	if surname == "" {
		ctx.RespErr(errors.New("无法提取姓氏首字"), &errcode)
		return
	}

	// 构建支付宝代付请求参数
	requestData := map[string]interface{}{
		"account_name":        accountName,
		"merchant_order_id":   fmt.Sprintf("%v", OrderId),
		"total_amount":        reqdata.Amount,
		"timestamp":           time.Now().Format(time.RFC3339),
		"notify_url":          notifyUrl + "/api/pay/alipay/withdraw_callback",
		"alipay_account_name": reqdata.AlipayAccount, // 支付宝账号
		"alipay_surname":      surname,               // 支付宝姓氏首字
	}

	// 发送支付宝代付请求
	result, err := c.httpPost(apiUrl+"/merchant_api/v1/orders/alipay_payment_transfer", requestData, cfg)
	if err != nil {
		logs.Error("alipay 支付宝代付请求失败: %v", err)
		ctx.RespErr(errors.New("提现请求失败"), &errcode)
		return
	}

	// 保存三方订单ID
	if thirdId, ok := result["id"].(string); ok && thirdId != "" {
		server.XDb().Table("x_withdraw").Where("Id = ?", OrderId).Update(xgo.H{
			"ThirdId": thirdId,
			"State":   5, // 更新为处理中
		})
	}

	logs.Info("alipay 支付宝代付订单创建成功: OrderId=%v, ThirdId=%v", OrderId, result["id"])
	// 统一返回格式，与其他支付方式保持一致
	ctx.RespOK(xgo.H{
		"orderId": OrderId,
		"status":  "success",
		"message": "alipay代付订单创建成功",
	})
}

// 充值回调处理
func (c *alipay) RechargeCallback(ctx *abugo.AbuHttpContent) {
	// 读取POST参数
	data := ctx.Gin().PostForm("data")
	signature := ctx.Gin().PostForm("signature")

	if data == "" || signature == "" {
		logs.Error("alipay 充值回调缺少必要参数")
		ctx.Gin().String(http.StatusBadRequest, "missing parameters")
		return
	}

	logs.Info("alipay 充值回调 data: %s", data)
	logs.Info("alipay 充值回调 signature: %s", signature)

	// 解析data字段
	var callbackData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &callbackData); err != nil {
		logs.Error("alipay 充值回调 data解析失败: %v", err)
		ctx.Gin().String(http.StatusBadRequest, "invalid data format")
		return
	}

	// 提取订单信息
	order, ok := callbackData["order"].(map[string]interface{})
	if !ok {
		logs.Error("alipay 充值回调 order字段缺失")
		ctx.Gin().String(http.StatusBadRequest, "missing order")
		return
	}

	// 提取通知类型
	notifyType, _ := callbackData["notify_type"].(string)
	if notifyType != "trade_completed" {
		logs.Info("alipay 充值回调 非完成通知，跳过: %s", notifyType)
		ctx.Gin().String(http.StatusOK, "ok")
		return
	}

	// 获取商户订单号
	merchantOrderId, ok := order["merchant_order_id"].(string)
	if !ok || merchantOrderId == "" {
		logs.Error("alipay 充值回调 merchant_order_id缺失")
		ctx.Gin().String(http.StatusBadRequest, "missing merchant_order_id")
		return
	}

	// 获取金额
	totalAmount, _ := order["total_amount"].(string)
	amount, _ := strconv.ParseFloat(totalAmount, 64)

	// 获取状态
	status, _ := order["status"].(string)

	logs.Info("alipay 充值回调处理: OrderId=%s, Amount=%f, Status=%s", merchantOrderId, amount, status)

	// 查询订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", merchantOrderId, "")
	orderData, err := server.Db().Table("x_recharge").Where(where).GetOne()
	if err != nil || orderData == nil {
		logs.Error("alipay 充值回调 订单不存在: %s", merchantOrderId)
		ctx.Gin().String(http.StatusBadRequest, "order not found")
		return
	}

	// 检查订单状态，避免重复处理
	if xgo.ToInt((*orderData)["State"]) == 5 {
		logs.Info("alipay 充值回调 订单已处理成功: %s", merchantOrderId)
		ctx.Gin().String(http.StatusOK, "ok")
		return
	}

	// 金额验证
	orderAmount := xgo.ToFloat((*orderData)["Amount"])
	if math.Abs(orderAmount-amount) > 0.01 {
		logs.Error("alipay 充值回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, amount)
		ctx.Gin().String(http.StatusBadRequest, "amount mismatch")
		return
	}

	logs.Info("alipay 充值回调金额校验通过: 订单金额=%f, 回调金额=%f", orderAmount, amount)

	// 获取支付方式配置用于签名验证
	payId := int(xgo.ToInt((*orderData)["PayId"]))
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", payId).First()
	if err != nil || paymethod == nil {
		logs.Error("alipay 充值回调 获取支付方式失败: %d", payId)
		ctx.Gin().String(http.StatusInternalServerError, "config error")
		return
	}

	// 解析配置
	var cfg map[string]interface{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &cfg)

	// 验证签名
	publicKey, ok := cfg["public_key"].(string)
	if !ok || publicKey == "" {
		logs.Error("alipay 充值回调 公钥配置错误")
		ctx.Gin().String(http.StatusInternalServerError, "public key config error")
		return
	}

	if err := c.verifySignature(data, signature, publicKey); err != nil {
		logs.Error("alipay 充值回调 签名验证失败: %v", err)
		ctx.Gin().String(http.StatusBadRequest, "signature verification failed")
		return
	}

	logs.Info("alipay 充值回调签名验证通过")

	// 处理充值状态
	userId := int(xgo.ToInt((*orderData)["UserId"]))
	orderId := int(xgo.ToInt((*orderData)["Id"]))

	var result int
	switch status {
	case "completed":
		result = 5 // 成功
		logs.Info("alipay 充值成功: OrderId=%s, UserId=%d", merchantOrderId, userId)
	case "failed", "denied":
		result = 7 // 失败/拒绝
		logs.Info("alipay 充值失败: OrderId=%s, Status=%s", merchantOrderId, status)
	case "payment_expired":
		result = 6 // 超时/逾时未付款
		logs.Info("alipay 充值超时: OrderId=%s, Status=%s", merchantOrderId, status)
	case "init", "pending_allocation", "pending_payment", "pending_confirmation", "pending_completed":
		result = 3 // 待支付/处理中状态
		logs.Info("alipay 充值处理中: OrderId=%s, Status=%s", merchantOrderId, status)
	default:
		result = 7 // 未知状态按失败处理
		logs.Warn("alipay 充值未知状态: OrderId=%s, Status=%s", merchantOrderId, status)
	}

	// 更新订单状态
	c.rechargeCallbackHandel(userId, orderId, result)

	ctx.Gin().String(http.StatusOK, "ok")
}

// 提现回调处理
func (c *alipay) WithdrawCallback(ctx *abugo.AbuHttpContent) {
	// 读取POST参数
	data := ctx.Gin().PostForm("data")
	signature := ctx.Gin().PostForm("signature")

	if data == "" || signature == "" {
		logs.Error("alipay 提现回调缺少必要参数")
		ctx.Gin().String(http.StatusBadRequest, "missing parameters")
		return
	}

	logs.Info("alipay 提现回调 data: %s", data)
	logs.Info("alipay 提现回调 signature: %s", signature)

	// 解析data字段
	var callbackData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &callbackData); err != nil {
		logs.Error("alipay 提现回调 data解析失败: %v", err)
		ctx.Gin().String(http.StatusBadRequest, "invalid data format")
		return
	}

	// 提取订单信息
	order, ok := callbackData["order"].(map[string]interface{})
	if !ok {
		logs.Error("alipay 提现回调 order字段缺失")
		ctx.Gin().String(http.StatusBadRequest, "missing order")
		return
	}

	// 提取通知类型
	notifyType, _ := callbackData["notify_type"].(string)

	// 获取商户订单号
	merchantOrderId, ok := order["merchant_order_id"].(string)
	if !ok || merchantOrderId == "" {
		logs.Error("alipay 提现回调 merchant_order_id缺失")
		ctx.Gin().String(http.StatusBadRequest, "missing merchant_order_id")
		return
	}

	// 获取金额和状态
	totalAmount, _ := order["total_amount"].(string)
	amount, _ := strconv.ParseFloat(totalAmount, 64)
	status, _ := order["status"].(string)

	logs.Info("alipay 提现回调处理: OrderId=%s, Amount=%f, Status=%s, NotifyType=%s", merchantOrderId, amount, status, notifyType)

	// 查询提现订单
	where := abugo.AbuDbWhere{}
	where.Add("and", "Id", "=", merchantOrderId, "")
	orderData, err := server.Db().Table("x_withdraw").Where(where).GetOne()
	if err != nil || orderData == nil {
		logs.Error("alipay 提现回调 订单不存在: %s", merchantOrderId)
		ctx.Gin().String(http.StatusBadRequest, "order not found")
		return
	}

	// 金额验证
	orderAmount := xgo.ToFloat((*orderData)["RealAmount"])
	if math.Abs(orderAmount-amount) > 0.01 {
		logs.Error("alipay 提现回调金额不匹配: 订单金额=%f, 回调金额=%f", orderAmount, amount)
		ctx.Gin().String(http.StatusBadRequest, "amount mismatch")
		return
	}

	logs.Info("alipay 提现回调金额校验通过: 订单金额=%f, 回调金额=%f", orderAmount, amount)

	// 获取支付方式配置用于签名验证
	payId := int(xgo.ToInt((*orderData)["PayId"]))
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", payId).First()
	if err != nil || paymethod == nil {
		logs.Error("alipay 提现回调 获取支付方式失败: %d", payId)
		ctx.Gin().String(http.StatusInternalServerError, "config error")
		return
	}

	// 解析配置
	var cfg map[string]interface{}
	json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &cfg)

	// 验证签名
	publicKey, ok := cfg["public_key"].(string)
	if !ok || publicKey == "" {
		logs.Error("alipay 提现回调 公钥配置错误")
		ctx.Gin().String(http.StatusInternalServerError, "public key config error")
		return
	}

	if err := c.verifySignature(data, signature, publicKey); err != nil {
		logs.Error("alipay 提现回调 签名验证失败: %v", err)
		ctx.Gin().String(http.StatusBadRequest, "signature verification failed")
		return
	}

	logs.Info("alipay 提现回调签名验证通过")

	// 根据官方状态码处理提现回调
	orderId := int(xgo.ToInt((*orderData)["Id"]))

	var result int
	switch status {
	case "completed":
		result = 6 // 提现成功
		logs.Info("alipay 提现成功: OrderId=%s", merchantOrderId)
	case "failed":
		result = 7 // 提现失败
		logs.Info("alipay 提现失败: OrderId=%s", merchantOrderId)
	case "init", "pending_processing", "processing":
		result = 5 // 处理中状态
		logs.Info("alipay 提现处理中: OrderId=%s, Status=%s", merchantOrderId, status)
	default:
		result = 7 // 未知状态按失败处理
		logs.Warn("alipay 提现未知状态: OrderId=%s, Status=%s", merchantOrderId, status)
	}

	// 更新提现订单状态
	c.withdrawCallbackHandel(orderId, result)

	ctx.Gin().String(http.StatusOK, "ok")
}
