// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserRechargeWithardDate = "x_user_recharge_withard_date"

// XUserRechargeWithardDate mapped from table <x_user_recharge_withard_date>
type XUserRechargeWithardDate struct {
	RecordDate          time.Time `gorm:"column:RecordDate;primaryKey" json:"RecordDate"`
	UserID              int32     `gorm:"column:UserId;primaryKey" json:"UserId"`
	SellerID            int32     `gorm:"column:SellerId" json:"SellerId"`
	ChannelID           int32     `gorm:"column:ChannelId" json:"ChannelId"`
	TopAgentID          int32     `gorm:"column:TopAgentId" json:"TopAgentId"`
	RechargeCount       int32     `gorm:"column:RechargeCount;comment:充值笔数" json:"RechargeCount"`                            // 充值笔数
	RechargeAmount      float64   `gorm:"column:RechargeAmount;default:0.000000;comment:充值金额" json:"RechargeAmount"`         // 充值金额
	ManRechargeCount    int32     `gorm:"column:ManRechargeCount;comment:人工充值笔数" json:"ManRechargeCount"`                    // 人工充值笔数
	ManRechargeAmount   float64   `gorm:"column:ManRechargeAmount;default:0.000000;comment:人工充值金额" json:"ManRechargeAmount"` // 人工充值金额
	WithdrawCount       int32     `gorm:"column:WithdrawCount;comment:体现笔数" json:"WithdrawCount"`                            // 体现笔数
	WithdrawAmount      float64   `gorm:"column:WithdrawAmount;default:0.000000;comment:提现金额" json:"WithdrawAmount"`         // 提现金额
	WithdrawFee         float64   `gorm:"column:WithdrawFee;default:0.000000;comment:提现手续费" json:"WithdrawFee"`              // 提现手续费
	FirstRechargeAmount float64   `gorm:"column:FirstRechargeAmount;default:0.000000" json:"FirstRechargeAmount"`
	FirstRechargeTime   time.Time `gorm:"column:FirstRechargeTime" json:"FirstRechargeTime"`
	CreateTime          time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime          time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
}

// TableName XUserRechargeWithardDate's table name
func (*XUserRechargeWithardDate) TableName() string {
	return TableNameXUserRechargeWithardDate
}
