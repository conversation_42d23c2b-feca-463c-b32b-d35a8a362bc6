package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"xserver/abugo"
	"xserver/server"

	"github.com/zhms/xgo/xgo"

	"xserver/gormgen/xHashGame/model"

	"github.com/beego/beego/logs"
)

var LPay = new(lPay)

type lPay struct {
	Base
}

// PayMethodConfig 三方配置
type LPayMethodConfig struct {
	Type  string `json:"type"`   // 三方类型
	AppID string `json:"app_id"` // 三方ID
	Key   string `json:"key"`    // 三方密钥
	Url   string `json:"url"`    // 三方地址
	Cburl string `json:"cburl"`  // 回调地址

}

// LpayRechargeProvider 三方返回的参数
type LPayRechargeProvider struct {
	ProviderID   int `json:"provider_id"`
	ProviderType int `json:"provider_type"`
}

// LPayRechargeResponse 三方返回的参数
type LPayRechargeResponse struct {
	Code  int    `json:"code"` // 返回码
	Info  string `json:"info"` // 返回信息
	Param struct {
		Payment_Url string `json:"payment_url"` // 支付链接
	} `json:"param"`
}

type LPayCallBackRequest struct {
	Parter  string `json:"parter"`  // 商户ID
	Orderid string `json:"orderid"` // 订单号
	Remark  string `json:"remark"`  // 备注
	OpState int    `json:"opstate"` // 交易状态 0-未支付 1-已支付
	Ovalue  string `json:"ovalue"`  // 交易金额
	Sign    string `json:"sign"`    // 签名
}

type LPayWithdrawCallBackRequest struct {
	Parter  string `json:"parter"`  // 商户ID
	Orderid string `json:"orderid"` // 订单号
	Opstate string `json:"opstate"` // 交易状态 0-未支付 1-已支付
	Ovalue  string `json:"ovalue"`  // 交易金额
	Sign    string `json:"sign"`    // 签名
	Info    string `json:"info"`    // 备注
}

// Init 初始化
func (c *lPay) Init() {
	server.Http().GetNoAuth("/api/lpay/recharge/callback", c.rechargeCallback)
	server.Http().GetNoAuth("/api/lpay/withdraw/callback", c.withdrawCallback)
}

// Recharge 充值
func (c *lPay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	// 获取请求参数
	errcode := 0
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("未找到支付方式"), &errcode)
		return
	}
	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	cfg := LPayMethodConfig{}
	// 获取支付方式配置
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	// 新增订单
	token := server.GetToken(ctx)

	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("未找到用户"), &errcode)
		return
	}
	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	// 创建订单
	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      13,
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}
	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	cfg.Cburl = fmt.Sprintf("%s/api/lpay/recharge/callback", cfg.Cburl)

	params := xgo.H{
		"callbackurl": cfg.Cburl,
		"notifyurl":   cfg.Cburl,
		"orderid":     fmt.Sprintf("%d", rechargeOrder.ID),
		"parter":      cfg.AppID,
		"type":        payMethod.PayType,
		"value":       req.Amount,
	}
	params["sign"] = c.generateMd5Sign(params, cfg.Key)
	jsonData, _ := json.Marshal(&params) //json序列
	resp, err := c.post(cfg.Url+"/payment/create", map[string]string{
		"Content-Type": "application/json",
	}, jsonData)
	logs.Info("支付请求地址：", cfg.Url+"/payment/create")

	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("发起支付请求失败"), &errcode)
		return
	}

	response := LPayRechargeResponse{} //定义接收结构体
	json.Unmarshal(resp.Body(), &response)
	// 返回响应
	if response.Code != 200 {
		tx.Rollback()
		ctx.RespErr(errors.New(response.Info), &errcode)
		return
	}
	tx.Commit()

	ctx.RespOK(xgo.H{
		"payurl": response.Param.Payment_Url,
	})
}

// rechargeCallback 充值回调
func (c *lPay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 获取请求参数
	cb := LPayCallBackRequest{}
	cb.OpState, _ = strconv.Atoi(ctx.Gin().Query("opstate"))
	cb.Orderid = ctx.Gin().Query("orderid")
	cb.Ovalue = ctx.Gin().Query("ovalue")
	cb.Parter = ctx.Gin().Query("parter")
	cb.Sign = ctx.Gin().Query("sign")
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(cb.Orderid)
	order, err := c.getRechargeOrder(orderNo)
	if err != nil {
		logs.Info("LPay回调获取订单失败", err)
		ctx.Gin().String(200, "订单号不存在")
		return
	}

	// 是否成功订单
	if order.State == 5 {
		ctx.Gin().String(200, "success")
		return
	}
	// 验证支付金额是否一致
	amount, err := strconv.ParseFloat(cb.Ovalue, 64)
	if err != nil {
		logs.Info("LPay回调金额转换错误", err)
		ctx.Gin().String(200, "金额转换错误")
		return
	}
	if math.Abs((order.Amount-amount)/100) > 0.01 {
		logs.Info("LPay回调金额不一致")
		ctx.Gin().String(200, "金额不一致")
		return
	}

	// 验证签名
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		ctx.Gin().String(200, "支付方式不存在")
		return
	}
	cfg := LPayMethodConfig{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	sign := c.md5(fmt.Sprintf("opstate=%d&orderid=%s&ovalue=%s&parter=%s&key=%s", cb.OpState, cb.Orderid, cb.Ovalue, cb.Parter, cfg.Key))
	// 验证签名
	if sign != cb.Sign {
		//查订单状态，防止伪造回调数据，假充值
		ctx.Gin().String(200, "验签失败")
		return
	}

	// 更新三方订单号
	err = c.updateThirdOrder(order.ID, cb.Orderid)
	if err != nil {
		ctx.Gin().String(200, "更新三方订单号失败")
		return
	}

	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	ctx.Gin().String(200, "success")
}

// withdrawCallback 提现下发回调
func (c *lPay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	cb := LPayWithdrawCallBackRequest{}
	cb.Opstate = ctx.Gin().Query("opstate")
	cb.Orderid = ctx.Gin().Query("orderid")
	cb.Ovalue = ctx.Gin().Query("ovalue")
	cb.Parter = ctx.Gin().Query("parter")
	cb.Sign = ctx.Gin().Query("sign")
	cb.Info = ctx.Gin().Query("info")
	// 查看订单是否存在
	orderNo, _ := strconv.Atoi(cb.Orderid)
	order, err := c.getWithdrawOrder(orderNo)
	if err != nil {
		ctx.Gin().String(200, "下发订单不存在")
		return
	}
	// 验证支付金额是否一致
	amount, err := strconv.ParseFloat(cb.Ovalue, 64)
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("LPay提现回调金额转换错误")
		ctx.Gin().String(200, "下发金额转换错误")
		return
	}
	if math.Abs((order.RealAmount-amount)/100) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("LPay提现回调金额不一致")
		ctx.Gin().String(200, "下发金额不一致")
		return
	}
	// 支付方式
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err != nil {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("LPay提现回调支付方式不存在")
		ctx.Gin().String(200, "支付方式不存在")
		return
	}

	cfg := LPayMethodConfig{}
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)
	sign := c.md5(fmt.Sprintf("opstate=%s&orderid=%s&ovalue=%s&parter=%s&key=%s", cb.Opstate, cb.Orderid, cb.Ovalue, cb.Parter, cfg.Key))
	// 验证签名
	if sign != cb.Sign {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error("LPay回调签名不一致")
		ctx.Gin().String(200, "验签失败")
		return
	}

	if order.State == 6 {
		ctx.Gin().String(200, "success")
		return
	}
	status, _ := strconv.Atoi(cb.Opstate)
	if status == 1 {
		logs.Info(fmt.Sprintf("LPay回调数据status:%s,orderid:%s", cb.Opstate, cb.Orderid))
		c.withdrawCallbackHandel(int(order.ID), 6)
		ctx.Gin().String(200, "success")
		return
	} else {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Error(fmt.Sprintf("LPay回调数据status:%s,orderid:%s", cb.Opstate, cb.Orderid))
		ctx.Gin().String(200, "update failure")
	}
}
