package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"strconv"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/imroc/req"
	"github.com/shopspring/decimal"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

type GFGConfigShow struct {
	url                   string
	key                   string
	company               string
	theme                 string
	Agent                 string
	currency              string
	appUrl                string
	brandName             string
	RefreshUserAmountFunc func(int) error
}

type ThirdOrderGfgShow struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

type AmountChangeLogGfgShow struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

type UserBalanceGfgShow struct {
	UserId    int     `json:"UserId" gorm:"column:UserId"`
	SellerId  int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId int     `json:"ChannelId" gorm:"column:ChannelId"`
	Amount    float64 `json:"Amount" gorm:"column:Amount"`
	Token     string  `json:"Token" gorm:"column:Token"`
}

type GameListGfgShow struct {
	Id        int    `json:"Id" gorm:"column:Id"`
	Brand     string `json:"Brand" gorm:"column:Brand"`
	GameId    string `json:"GameId" gorm:"column:GameId"`
	Name      string `json:"Name" gorm:"column:Name"`
	EName     string `json:"EName" gorm:"column:EName"`
	State     int    `json:"State" gorm:"column:State"`
	OpenState int    `json:"OpenState" gorm:"column:OpenState"`
	GameType  int    `json:"GameType" gorm:"column:GameType"`
	HubType   int    `json:"HubType" gorm:"column:HubType"`
}

func NewGFGLogicShow(params map[string]string, fc func(int) error) *GFGConfigShow {
	return &GFGConfigShow{
		url:                   params["url"],
		key:                   params["key"],
		company:               params["company"],
		theme:                 params["theme"],
		Agent:                 params["agent"],
		currency:              params["currency"],
		appUrl:                params["appUrl"],
		brandName:             "gfg_show",
		RefreshUserAmountFunc: fc,
	}
}

const cacheKeyGFGShow = "cacheKeyGFGShow:"

func (l *GFGConfigShow) signShow(s string) string {
	return base.MD5(s + l.key)
}

func (l *GFGConfigShow) userId2tokenByGfgShow(cacheKey string, userId int) string {
	token := "gfgShow_" + uuid.NewString()
	log.Println("login-userId2tokenByGfgShow-token:", token)
	if err := server.Redis().SetStringEx(cacheKey+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("gfg_show单一钱包 userId2token set redis key=", cacheKey+token, " userId=", userId, " error=", err.Error())
	}
	return token
}

func (l *GFGConfigShow) token2UserIdByGfgShow(cacheKey, token string) int {
	redisdata := server.Redis().Get(cacheKey + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

func (l *GFGConfigShow) GetGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"`
		ExitUrl  string
	}
	querydata := make(req.Param)
	querydata["agent"] = l.Agent
	querydata["companyKey"] = l.company
	querydata["timestamp"] = 1725105600000

	urlreq := l.url + "/getGameList"
	reqBytes, _ := json.Marshal(querydata)
	au := l.signShow(string(reqBytes))
	header := map[string]string{
		"Authorization": au,
	}
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		ctx.RespJson("gfgShow-GetGameList请求/gameList 出现错误")
		return
	}
	ctx.RespJson(data)
	return
}

// Login
func (l *GFGConfigShow) LoginShow(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"`
		ExitUrl  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	// 此处演示环境暂时不做测试账号校验和重复登陆校验
	//if err, errcode = base.IsLoginByUserId(cacheKeyGFGShow, token.UserId); err != nil {
	//	ctx.RespErrString(true, &errcode, err.Error())
	//	return
	//}
	//sessionId
	log.Println("loginShow-token:", token)
	sessionId := l.userId2tokenByGfgShow(cacheKeyGFGShow, token.UserId)
	//account
	account := fmt.Sprintf("%s_%d", l.Agent, token.UserId)

	gamecode := fmt.Sprintf("%d", reqdata.GameId)
	gameList := GameListGfg{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("gfgShow-LoginShow单一钱包 Login 查询游戏错误 userId=", token.UserId, " gamecode=", gamecode, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	// 801=虎虎生财 802=亡灵大盗 803=麻将胡了2 804=十倍金牛
	if gamecode != "801" && gamecode != "802" && gamecode != "803" && gamecode != "804" {
		if gameList.State != 1 || gameList.OpenState != 1 {
			logs.Error("gfgShow-LoginShow单一钱包 Login 游戏不可用 userId=", token.UserId, " gamecode=", gamecode, " gameList=", gameList)
			ctx.RespErrString(true, &errcode, "游戏未开放")
			return
		}
	}

	//todo  gfg要求先去掉
	////尝试去踢人下线
	//kickParam := make(req.Param)
	//kickParam["account"] = account
	//kickParam["companyKey"] = l.company
	//kickreq := l.url + "/kickUser"
	//kickParamBytes, _ := json.Marshal(kickParam)
	//kickau := l.sign(string(kickParamBytes))
	//kickheader := map[string]string{
	//	"Authorization": kickau,
	//}
	//kickhttpclient := httpc.DoRequest{
	//	UrlPath:    kickreq,
	//	Param:      kickParamBytes,
	//	PostMethod: httpc.JSON_DATA,
	//	Header:     kickheader,
	//}
	//kickdata, err1 := kickhttpclient.DoPost()
	////仅仅打印一下 不管是否成功
	//logs.Info("踢人信息 account  | kickdata | err1", account, kickdata, err1)

	querydata := make(req.Param)
	querydata["agent"] = l.Agent
	querydata["companyKey"] = l.company
	querydata["theme"] = l.theme
	querydata["account"] = account
	querydata["gameId"] = reqdata.GameId
	querydata["ip"] = ctx.GetIp()
	querydata["platform"] = 2
	querydata["token"] = sessionId
	querydata["nickName"] = fmt.Sprint(token.UserId)
	querydata["timestamp"] = fmt.Sprintf("%d", time.Now().UnixMilli())
	querydata["languageType"] = reqdata.LangCode
	querydata["exitUrl"] = reqdata.ExitUrl
	querydata["appUrl"] = l.appUrl
	urlreq := l.url + "/login"
	reqBytes, _ := json.Marshal(querydata)
	au := l.signShow(string(reqBytes))
	header := map[string]string{
		"Authorization": au,
	}
	log.Println("gfgShow-LoginShow. LoginShow-Auth:", au)
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if code, ok := data["code"]; ok {
		if abugo.GetInt64FromInterface(code) == 0 {
			ctx.RespOK(data["data"].(map[string]interface{})["url"])
			return
		}
	}
	logs.Error("gfgShow-LoginShow单一钱包  登录三方错误 msg=", data["msg"].(string))
	ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
	return
}

// api/Balance/GetBalance
func (l *GFGConfigShow) GetBalanceShow(ctx *abugo.AbuHttpContent, token string) {
	// logs.Info("gfg单一钱包 GetBalance:", token)
	if token == "" {
		logs.Error("gfgShow-GetBalanceShow单一钱包 GetBalance:-Warning No Token")
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "token为空",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	UserId := l.token2UserIdByGfgShow(cacheKeyGFGShow, token)
	if UserId <= 0 {
		logs.Error("gfgShow-GetBalanceShow单一钱包 GetBalance 无效token=", token)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "token无效",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	userBalance := UserBalanceGfg{}
	err := server.Db().GormDao().Table("x_user").Where("UserId = ?", UserId).First(&userBalance).Error
	if err != nil {
		logs.Error("gfgShow-GetBalanceShow单一钱包 Bet 查询用户余额错误 userId=", UserId, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfgShow-GetBalanceShow单一钱包 Bet 用户余额为负数 userId=", UserId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
			"data":      gin.H{"Data": float64(int(userBalance.Amount*100)) / 100},
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100

	// logs.Info("gfg单一钱包 GetBalance:", UserId, fmt.Sprintf("%v", balance2))
	ctx.Gin().JSON(200, gin.H{
		"code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance2},
		"msg":       "",
	})
}

func (l *GFGConfigShow) checkShow(authorization, bodyBytes string) bool {
	log.Println("参数加密auth：", base.MD5(bodyBytes+l.key), "  三方传入auth:", authorization)
	return authorization != "" && base.MD5(bodyBytes+l.key) == authorization
}

// api/Balance/LockBalance
func (l *GFGConfigShow) BetShow(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfgShow-BetShow单一钱包 Bet bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfgShow-BetShow单一钱包 Bet 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	// logs.Info("gfg单一钱包 Bet authorization=", authorization)
	if !l.checkShow(authorization, string(bodyBytes)) {
		logs.Error("gfgShow-BetShow单一钱包 Bet 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}
	type betReq struct {
		Token        string  `json:"token"`
		AccountId    string  `json:"accountId"`
		Money        float64 `json:"money"`
		GameId       int     `json:"gameId"`
		OrderId      string  `json:"orderId"`
		MinLockMoney float64 `json:"minLockMoney"`
		Timestamp    int64   `json:"timestamp"`
	}
	requests := betReq{}
	err = json.Unmarshal(bodyBytes, &requests)
	if err != nil {
		logs.Error("gfgShow-BetShow单一钱包 Bet json解析错误 bodyBytes=", string(bodyBytes), " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "非法参数",
		})
		return
	}

	//获取玩家信息
	userId := l.token2UserIdByGfgShow(cacheKeyGFGShow, requests.Token)
	if userId <= 0 {
		logs.Error("gfgShow-BetShow单一钱包 Bet 无效token token2UserIdByGfg token=", requests.Token, "userId=", userId, " thirdId=", requests.OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"timestamp": time.Now().Unix(),
			"msg":       "token无效",
		})
		return
	}

	userBalance := UserBalanceGfg{}
	err = server.Db().GormDao().Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("gfgShow-BetShow单一钱包 Bet 查询用户余额错误 userId=", userId, " thirdId=", requests.OrderId, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfgShow-BetShow单一钱包 Bet 用户余额为负数 userId=", userId, " thirdId=", requests.OrderId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100

	//三方来源的数据整理
	var (
		betAmount1   = requests.Money
		betAmountMin = requests.MinLockMoney
		thirdId      = requests.OrderId
		gamecode     = fmt.Sprintf("%d", requests.GameId)
		thirdTime    = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05") // time.UnixMilli(requests.Timestamp)
	)

	if betAmountMin > balance2 {
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足",
		})
		return
	}
	var betAmount float64 = 0
	if betAmount1 > balance2 {
		//实际冻结额度，如果可用余额大于“申请冻结额”，则返回“申请冻结额”，如果可用余额小于“申请冻结额”大于“最低冻结金额”，则返回所有余额，否则失败
		betAmount = balance2
	} else {
		betAmount = betAmount1
	}
	gameList := GameListGfg{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		logs.Error("gfgShow-BetShow单一钱包 Bet 查询游戏错误 userId=", userId, " gamecode=", gamecode, " requests=", requests, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询游戏错误",
		})
		return
	}
	gameName := gameList.Name
	gameType := gameList.GameType
	var tablepre string
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
	} else {
		logs.Error("gfgShow-BetShow单一钱包 Bet gameType错误  GameId=", gamecode, " gameType=", gameType, " thirdId=", thirdId)
		ctx.Gin().JSON(200, gin.H{
			"code":      702,
			"timestamp": time.Now().Unix(),
			"msg":       "游戏ID对应的类型错误",
		})
		return
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

	// 开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderGfgShow{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e == nil {
			//已经存在订单
			logs.Error("gfgShow-BetShow单一钱包 Bet 订单已存在 thirdId=", thirdId, " betTran=", betTran)
			ctx.Gin().JSON(200, gin.H{
				"code":      2,
				"timestamp": time.Now().Unix(),
				"msg":       "注单已存在",
			})
			return nil
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("gfgShow-BetShow单一钱包 Bet 查询错误 thirdId=", thirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询注单错误",
			})
			return e
		}

		order := ThirdOrderGfgShow{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gamecode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(bodyBytes),
			State:        1,
			DataState:    -1,
			CreateTime:   thirdTime,
			BetCtxType:   1,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("gfgShow-BetShow单一钱包 Bet 创建注单错误 thirdId=", thirdId, " order=", order, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      9013,
				"timestamp": time.Now().Unix(),
				"msg":       "创建注单失败",
			})
			return e
		}

		logs.Info("gfgShow-BetShow单一钱包 Bet 下注成功 thirdId=", thirdId, " order=", order)
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"money":     betAmount,
				"accountId": requests.AccountId,
				"balance":   balance2,
			},
			"msg": "",
		})
		return nil
	})

	// gfg下一局开始的时候更新余额
	// 发送余额变动通知
	go func(notifyUserId int) {
		rKey := cacheKeyGFGShow + "ntyAmount:" + strconv.Itoa(userId)
		tmpErr := server.Redis().Del(rKey)
		if tmpErr != nil {
			logs.Error("gfgShow-BetShow单一钱包  redis 删除余额通知锁 userId=", userId, " tmpErr=", tmpErr.Error())
		}

		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][GFGConfigShow] gfgShow-BetShow 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][GFGConfigShow] gfgShow-BetShow 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// api/Balance/UnLockBalance 派奖结算
func (l *GFGConfigShow) ResultShow(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfgShow-ResultShow Result bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfgShow-ResultShow Result 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	// logs.Info("gfg单一钱包 Result authorization=", authorization)
	if !l.checkShow(authorization, string(bodyBytes)) {
		logs.Error("gfgShow-ResultShow Result 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}
	type betReq struct {
		Token     string  `json:"token"`
		OrderId   string  `json:"orderId"`
		LockMoney float64 `json:"lockMoney"`
		Money     float64 `json:"money"` // 跟win参数重复 可以不使用
		AccountId string  `json:"accountId"`
		GameId    int     `json:"gameId"`
		RoundId   string  `json:"roundId"`
		Timestamp int64   `json:"timestamp"`
		ValidBet  float64 `json:"validBet"`
		Win       float64 `json:"win"`
		Bet       float64 `json:"bet"`
		Fee       float64 `json:"fee"`
	}
	requests := betReq{}
	err = json.Unmarshal(bodyBytes, &requests)
	if err != nil {
		logs.Error("gfgShow-ResultShow Result json解析错误 bodyBytes=", string(bodyBytes), " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "非法参数",
		})
		return
	}

	//获取玩家信息
	userId := l.token2UserIdByGfgShow(cacheKeyGFGShow, requests.Token)
	if userId <= 0 {
		logs.Error("gfgShow-ResultShow Result 无效token token2UserIdByGfg token=", requests.Token, "userId=", userId, " thirdId=", requests.OrderId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"timestamp": time.Now().Unix(),
			"msg":       "token无效",
		})
		return
	}

	// 参数校验 有效下注
	if requests.ValidBet < 0 || requests.Bet < 0 || requests.Fee < 0 {
		// 参数不正确 返回8019结算异常
		logs.Error("gfgShow-ResultShow Result 参数不正确 userId=", userId, " thirdId=", requests.OrderId, " requests=", requests)
		ctx.Gin().JSON(200, gin.H{
			"code":      8019,
			"timestamp": time.Now().Unix(),
			"msg":       "参数错误,下注金额或者有效下注或者费率为负数",
		})
		return
	}

	//三方来源的数据整理
	var (
		winAmount       = requests.Bet + requests.Win // + requests.Fee 费率用户出 Christine Hsieh 确定的
		thirdId         = requests.OrderId
		thirdIdRealLook = requests.RoundId
		gamecode        = fmt.Sprintf("%d", requests.GameId)
		thirdTime       = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05") // time.UnixMilli(requests.Timestamp)
		validBet        = requests.ValidBet
	)
	if requests.ValidBet > requests.Bet {
		validBet = requests.Bet
	}

	gameList := GameListGfgShow{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		logs.Error("gfgShow-ResultShow Bet 查询游戏错误 userId=", userId, " gamecode=", gamecode, " requests=", requests, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询游戏错误",
		})
		return
	}
	gameType := gameList.GameType
	var tablepre string
	var table string
	var betType int
	var settleType int
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
		betType = utils.BalanceCReasonGFGBetDianZi
		settleType = utils.BalanceCReasonGFGSettleDianZi
		// if requests.ValidBet != 0 && requests.Win == 0 { // 只有电子不输不赢的时候流水取下注金额
		// 	validBet = requests.Bet
		// }
		// 所有电子的有效流水取不大于下注金额的输赢绝对值
		validBet = math.Abs(requests.Win + requests.Fee)
		if validBet > math.Abs(requests.Bet) {
			validBet = math.Abs(requests.Bet)
		}
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
		betType = utils.BalanceCReasonGFGBet
		settleType = utils.BalanceCReasonGFGSettle
		// if requests.Win == 0 { // 只有棋牌不输不赢的时候流水取0
		// 	validBet = 0
		// }
		// 所有棋牌的有效流水取不大于下注金额的输赢绝对值
		validBet = math.Abs(requests.Win + requests.Fee)
		if validBet > math.Abs(requests.Bet) {
			validBet = math.Abs(requests.Bet)
		}
	} else {
		logs.Error("gfgShow-ResultShow Result gameType错误 userId=", userId, " GameId=", gamecode, " gameType=", gameType, "thirdId=", thirdId)
		ctx.Gin().JSON(200, gin.H{
			"code":      702,
			"timestamp": time.Now().Unix(),
			"msg":       "游戏ID对应的类型错误",
		})
		return
	}

	// 开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderGfgShow{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			logs.Error("gfgShow-ResultShow单一钱包 Result 查询注单错误 thirdId=", thirdId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				ctx.Gin().JSON(200, gin.H{
					"code":      3,
					"timestamp": time.Now().Unix(),
					"msg":       "注单不存在",
				})
			} else {
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "查询注单错误",
				})
			}
			return e
		}
		if betTran.DataState != -1 {
			logs.Error("gfgShow-ResultShow单一钱包 Result 注单状态错误 thirdId=", thirdId, " betTran=", betTran)
			ctx.Gin().JSON(200, gin.H{
				"code":      3,
				"timestamp": time.Now().Unix(),
				"msg":       "注单不是未开奖状态",
			})
			return errors.New("注单不是未开奖状态")
		}

		userBalance := UserBalanceGfgShow{}
		e = tx.Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("gfgShow-ResultShow单一钱包 Result 查询用户余额错误 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额错误",
			})
			return e
		}
		if userBalance.Amount < 0 {
			if thirdIdRealLook == "" {
				thirdIdRealLook = requests.OrderId
			}
			e = tx.Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"RawData":   string(bodyBytes),
				"ThirdId":   thirdIdRealLook,
				"DataState": -2,
			}).Error
			if e != nil {
				logs.Error("gfgShow-ResultShow单一钱包 Result 用户余额为负数 设置注单无效错误 userId=", userId, " thirdId=", thirdId, " userBalance=", userBalance.Amount, " betTran=", betTran, " err=", e.Error())
			} else {
				logs.Error("gfgShow-ResultShow单一钱包 Result 用户余额为负数 设置注单无效成功 userId=", userId, " thirdId=", thirdId, " userBalance=", userBalance.Amount, " betTran=", betTran)
			}

			ctx.Gin().JSON(200, gin.H{
				"code":      9004,
				"timestamp": time.Now().Unix(),
				"msg":       "用户余额不足,负余额",
			})
			return nil
		}
		balance2 := float64(int(userBalance.Amount*100)) / 100

		if requests.RoundId == "" {
			if requests.Bet == 0 && requests.Win == 0 && requests.Fee == 0 && requests.ValidBet == 0 {
				// RoundId空表示无效订单 下一局的unlockBalance消息
				e = tx.Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Delete(&betTran).Error
				if e != nil {
					logs.Error("gfgShow-ResultShow单一钱包 Result 删除无效订单错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
					ctx.Gin().JSON(200, gin.H{
						"code":      503,
						"timestamp": time.Now().Unix(),
						"msg":       "删除无效订单失败",
					})
					return e
				} else {
					ctx.Gin().JSON(200, gin.H{
						"code":      0,
						"timestamp": time.Now().Unix(),
						"data": gin.H{
							"Data": balance2,
						},
						"msg": "",
					})
					return nil
				}
			} else {
				thirdIdRealLook = requests.OrderId
			}
		}

		updataData := map[string]interface{}{
			"BetAmount": requests.Bet,
			"WinAmount": winAmount + requests.Fee,
			"ValidBet":  validBet,
			"RawData":   string(bodyBytes),
			"GameId":    gamecode,
			"ThirdId":   thirdIdRealLook,
			"Fee":       requests.Fee,
			"DataState": 1,
		}
		if winAmount+requests.Fee != 0 {
			updataData["ThirdTime"] = thirdTime
		}
		resultTmp := tx.Table(tablepre).Where("Id=?", betTran.Id).Updates(updataData)
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("gfgShow-ResultShow单一钱包 Result 更新结算信息错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "更新结算信息失败",
			})
			return e
		}

		e = tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdIdRealLook, l.brandName).First(&betTran).Error
		if e != nil {
			logs.Error("gfgShow-ResultShow单一钱包 Result 查询注单错误 thirdId=", thirdIdRealLook, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询注单错误",
			})
			return e
		}

		// 处理下注
		if requests.Bet > 0 {
			if requests.Win > 0 {
				// 赢钱判断是否余额足够
				resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, requests.Bet).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount - ?", requests.Bet),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					e = tx.Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdIdRealLook, l.brandName, userId).Updates(map[string]interface{}{
						"DataState": -2,
					}).Error
					if e != nil {
						logs.Error("gfgShow-ResultShow单一钱包 Result 用户余额不够下注额 设置注单无效错误 userId=", userId, " thirdId=", thirdIdRealLook, " userBalance=", userBalance.Amount, "requests=", requests, " betTran=", betTran, " err=", e.Error())
					} else {
						logs.Error("gfgShow-ResultShow单一钱包 Result 用户余额不够下注额 设置注单无效成功 userId=", userId, " thirdId=", thirdIdRealLook, " userBalance=", userBalance.Amount, "requests=", requests, " betTran=", betTran)
					}

					ctx.Gin().JSON(200, gin.H{
						"code":      9004,
						"timestamp": time.Now().Unix(),
						"msg":       "用户余额不够下注",
					})
					return nil
				}
			} else {
				// 输钱直接扣钱，支持负余额
				resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
					"Amount": daogorm.Expr("Amount - ?", requests.Bet),
				})
				e = resultTmp.Error
				if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
					logs.Error("gfgShow-ResultShow单一钱包 Result 用户输钱更新用户余额错误 userId=", userId, " thirdId=", thirdId, " userBalance=", userBalance.Amount, "requests=", requests, " betTran=", betTran)
					e = errors.New("更新条数0")
				}
			}
			if e != nil {
				logs.Error("gfgShow-ResultShow单一钱包 Result 更新用户余额错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}
			amountLog := AmountChangeLogGfgShow{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       0 - requests.Bet,
				AfterAmount:  userBalance.Amount - requests.Bet,
				Reason:       betType,
				CreateTime:   betTran.CreateTime,
				Memo:         l.brandName + " bet,thirdId:" + thirdIdRealLook,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfgShow-ResultShow单一钱包 Result 创建账变记录错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " amountLog=", amountLog, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "创建账变记录错误",
				})
				return e
			}
		}
		// 处理结算
		if winAmount != 0 {
			resultTmp = tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("gfgShow-ResultShow单一钱包 Result 更新用户余额错误 userId=", userId, " thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " winAmount=", winAmount, " betTran=", betTran, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}
		}
		if requests.Bet != 0 || winAmount != 0 {
			amountLog := AmountChangeLogGfgShow{
				UserId:       userId,
				BeforeAmount: userBalance.Amount - requests.Bet,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount - requests.Bet + winAmount,
				Reason:       settleType,
				CreateTime:   thirdTime,
				Memo:         l.brandName + " settle,thirdId:" + thirdIdRealLook,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfgShow-ResultShow单一钱包 Result 创建账变记录错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " amountLog=", amountLog, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "创建账变记录错误",
				})
				return e
			}
		}

		betTran.Id = 0
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			logs.Error("gfgShow-ResultShow单一钱包 Result 移动至统计表错误 thirdId=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "移动至统计表错误",
			})
			return e
		}

		logs.Info("gfgShow-ResultShow单一钱包 Result 结算成功 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran)
		retBalance, _ := decimal.NewFromFloat(balance2).Sub(decimal.NewFromFloat(requests.Bet)).Add(decimal.NewFromFloat(winAmount)).Float64()
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"Data": retBalance, //balance2 - requests.Bet + winAmount,
			},
			"msg": "",
		})
		return nil
	})

	if err == nil {
		// 发送余额变动通知
		go func(notifyUserId int) {
			if requests.Win != 0 {
				rKey := cacheKeyGFGShow + "ntyAmount:" + strconv.Itoa(notifyUserId)
				tmpErr := server.Redis().SetNxString(rKey, time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"), 59)
				if tmpErr != nil {
					return
				}
				time.Sleep(1 * time.Minute) // gfg延时一分钟更新余额
				tmpErr = server.Redis().SetNxString(rKey, time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"), 59)
				if tmpErr != nil {
					return
				}
			}
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][GFGConfig] Result 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][GFGConfig] Result 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// 踢人下线
func (l *GFGConfigShow) KickUser(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Account    string `json:"account"`
		CompanyKey string `json:"companyKey"`
	}

	type ResponseData struct {
		Code      int   `json:"code"`
		Timestamp int64 `json:"timestamp"`
	}

	requestData := RequestData{}
	err := ctx.RequestData(&requestData)
	if err != nil {
		log.Println("参数转换失败")
	}
	responseData := ResponseData{}
	responseData.Timestamp = time.Now().Unix()
	responseData.Code = 0

	//尝试去踢人下线
	kickParam := make(req.Param)
	kickParam["account"] = requestData.Account
	kickParam["companyKey"] = l.company
	kickreq := l.url + "/kickUser"
	kickParamBytes, _ := json.Marshal(kickParam)
	kickau := l.signShow(string(kickParamBytes))
	kickheader := map[string]string{
		"Authorization": kickau,
	}
	kickhttpclient := httpc.DoRequest{
		UrlPath:    kickreq,
		Param:      kickParamBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     kickheader,
	}
	kickdata, err1 := kickhttpclient.DoPost()
	//仅仅打印一下 不管是否成功
	logs.Info("踢人信息 account  | kickdata | err1", requestData.Account, kickdata, err1)
}
