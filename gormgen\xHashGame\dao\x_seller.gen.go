// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXSeller(db *gorm.DB, opts ...gen.DOOption) xSeller {
	_xSeller := xSeller{}

	_xSeller.xSellerDo.UseDB(db, opts...)
	_xSeller.xSellerDo.UseModel(&model.XSeller{})

	tableName := _xSeller.xSellerDo.TableName()
	_xSeller.ALL = field.NewAsterisk(tableName)
	_xSeller.SellerID = field.NewInt32(tableName, "SellerId")
	_xSeller.SellerName = field.NewString(tableName, "SellerName")
	_xSeller.State = field.NewInt32(tableName, "State")
	_xSeller.Remark = field.NewString(tableName, "Remark")
	_xSeller.CreateTime = field.NewTime(tableName, "CreateTime")
	_xSeller.Icon = field.NewString(tableName, "Icon")
	_xSeller.Logo = field.NewString(tableName, "Logo")
	_xSeller.Logo2 = field.NewString(tableName, "Logo2")
	_xSeller.ShowName = field.NewString(tableName, "ShowName")
	_xSeller.SampleName = field.NewString(tableName, "SampleName")
	_xSeller.SocialLinks = field.NewString(tableName, "SocialLinks")
	_xSeller.IosIcon = field.NewString(tableName, "IosIcon")
	_xSeller.ThirdAuth = field.NewString(tableName, "ThirdAuth")
	_xSeller.CustomerLinks = field.NewString(tableName, "CustomerLinks")
	_xSeller.Lang = field.NewString(tableName, "Lang")
	_xSeller.AiSwitch = field.NewInt32(tableName, "AiSwitch")

	_xSeller.fillFieldMap()

	return _xSeller
}

type xSeller struct {
	xSellerDo xSellerDo

	ALL           field.Asterisk
	SellerID      field.Int32  // 运营商
	SellerName    field.String // 运营名称
	State         field.Int32  // 状态 1启用 2禁用
	Remark        field.String // 备注
	CreateTime    field.Time   // 创建时间
	Icon          field.String // 标签ICON
	Logo          field.String // Logo
	Logo2         field.String // Logo2
	ShowName      field.String // 显示名称
	SampleName    field.String // 运营商简称
	SocialLinks   field.String // 社媒链接
	IosIcon       field.String // 标签ICON
	ThirdAuth     field.String // 三方登录appId
	CustomerLinks field.String // 客服链接
	Lang          field.String // 语言
	AiSwitch      field.Int32  // ai客服开关 1-开启 2-关闭

	fieldMap map[string]field.Expr
}

func (x xSeller) Table(newTableName string) *xSeller {
	x.xSellerDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xSeller) As(alias string) *xSeller {
	x.xSellerDo.DO = *(x.xSellerDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xSeller) updateTableName(table string) *xSeller {
	x.ALL = field.NewAsterisk(table)
	x.SellerID = field.NewInt32(table, "SellerId")
	x.SellerName = field.NewString(table, "SellerName")
	x.State = field.NewInt32(table, "State")
	x.Remark = field.NewString(table, "Remark")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Icon = field.NewString(table, "Icon")
	x.Logo = field.NewString(table, "Logo")
	x.Logo2 = field.NewString(table, "Logo2")
	x.ShowName = field.NewString(table, "ShowName")
	x.SampleName = field.NewString(table, "SampleName")
	x.SocialLinks = field.NewString(table, "SocialLinks")
	x.IosIcon = field.NewString(table, "IosIcon")
	x.ThirdAuth = field.NewString(table, "ThirdAuth")
	x.CustomerLinks = field.NewString(table, "CustomerLinks")
	x.Lang = field.NewString(table, "Lang")
	x.AiSwitch = field.NewInt32(table, "AiSwitch")

	x.fillFieldMap()

	return x
}

func (x *xSeller) WithContext(ctx context.Context) *xSellerDo { return x.xSellerDo.WithContext(ctx) }

func (x xSeller) TableName() string { return x.xSellerDo.TableName() }

func (x xSeller) Alias() string { return x.xSellerDo.Alias() }

func (x xSeller) Columns(cols ...field.Expr) gen.Columns { return x.xSellerDo.Columns(cols...) }

func (x *xSeller) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xSeller) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 16)
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["SellerName"] = x.SellerName
	x.fieldMap["State"] = x.State
	x.fieldMap["Remark"] = x.Remark
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["Logo"] = x.Logo
	x.fieldMap["Logo2"] = x.Logo2
	x.fieldMap["ShowName"] = x.ShowName
	x.fieldMap["SampleName"] = x.SampleName
	x.fieldMap["SocialLinks"] = x.SocialLinks
	x.fieldMap["IosIcon"] = x.IosIcon
	x.fieldMap["ThirdAuth"] = x.ThirdAuth
	x.fieldMap["CustomerLinks"] = x.CustomerLinks
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["AiSwitch"] = x.AiSwitch
}

func (x xSeller) clone(db *gorm.DB) xSeller {
	x.xSellerDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xSeller) replaceDB(db *gorm.DB) xSeller {
	x.xSellerDo.ReplaceDB(db)
	return x
}

type xSellerDo struct{ gen.DO }

func (x xSellerDo) Debug() *xSellerDo {
	return x.withDO(x.DO.Debug())
}

func (x xSellerDo) WithContext(ctx context.Context) *xSellerDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xSellerDo) ReadDB() *xSellerDo {
	return x.Clauses(dbresolver.Read)
}

func (x xSellerDo) WriteDB() *xSellerDo {
	return x.Clauses(dbresolver.Write)
}

func (x xSellerDo) Session(config *gorm.Session) *xSellerDo {
	return x.withDO(x.DO.Session(config))
}

func (x xSellerDo) Clauses(conds ...clause.Expression) *xSellerDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xSellerDo) Returning(value interface{}, columns ...string) *xSellerDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xSellerDo) Not(conds ...gen.Condition) *xSellerDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xSellerDo) Or(conds ...gen.Condition) *xSellerDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xSellerDo) Select(conds ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xSellerDo) Where(conds ...gen.Condition) *xSellerDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xSellerDo) Order(conds ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xSellerDo) Distinct(cols ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xSellerDo) Omit(cols ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xSellerDo) Join(table schema.Tabler, on ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xSellerDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xSellerDo) RightJoin(table schema.Tabler, on ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xSellerDo) Group(cols ...field.Expr) *xSellerDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xSellerDo) Having(conds ...gen.Condition) *xSellerDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xSellerDo) Limit(limit int) *xSellerDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xSellerDo) Offset(offset int) *xSellerDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xSellerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xSellerDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xSellerDo) Unscoped() *xSellerDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xSellerDo) Create(values ...*model.XSeller) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xSellerDo) CreateInBatches(values []*model.XSeller, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xSellerDo) Save(values ...*model.XSeller) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xSellerDo) First() (*model.XSeller, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSeller), nil
	}
}

func (x xSellerDo) Take() (*model.XSeller, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSeller), nil
	}
}

func (x xSellerDo) Last() (*model.XSeller, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSeller), nil
	}
}

func (x xSellerDo) Find() ([]*model.XSeller, error) {
	result, err := x.DO.Find()
	return result.([]*model.XSeller), err
}

func (x xSellerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XSeller, err error) {
	buf := make([]*model.XSeller, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xSellerDo) FindInBatches(result *[]*model.XSeller, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xSellerDo) Attrs(attrs ...field.AssignExpr) *xSellerDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xSellerDo) Assign(attrs ...field.AssignExpr) *xSellerDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xSellerDo) Joins(fields ...field.RelationField) *xSellerDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xSellerDo) Preload(fields ...field.RelationField) *xSellerDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xSellerDo) FirstOrInit() (*model.XSeller, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSeller), nil
	}
}

func (x xSellerDo) FirstOrCreate() (*model.XSeller, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XSeller), nil
	}
}

func (x xSellerDo) FindByPage(offset int, limit int) (result []*model.XSeller, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xSellerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xSellerDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xSellerDo) Delete(models ...*model.XSeller) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xSellerDo) withDO(do gen.Dao) *xSellerDo {
	x.DO = *do.(*gen.DO)
	return x
}
