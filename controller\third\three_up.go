package third

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/server"
	"xserver/utils"

	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"

	"github.com/beego/beego/logs"
	"github.com/oschwald/geoip2-golang"
)

// http://tapi5.zyue88.com/api
// oan
// 3ZIpBwRZGDeDd7mZVy7YFLbJUGF9RnWS
// ['**************']
// {"url":"http://tapi5.zyue88.com/api","prefix_code":"oan","ips":["**************"]}

func NewThreeUpSrvice(url, prefixCode string, outGoingIp []string, platform, view, currency, passkey string, fc func(int) error) *ThreeUpSrvice {
	games := map[string]string{
		"足球":   "1",
		"篮球":   "2",
		"网球":   "3",
		"板球":   "7",
		"羽毛球":  "12",
		"美式足球": "13",
		"冰球":   "14",
		"桌球":   "15",
		"手球":   "19",
		"排球":   "21",
		"电子竞技": "20",
	}
	gamesEn := map[string]string{
		"Soccer":           "足球",
		"Basketball":       "篮球",
		"Outright":         "冠军",
		"Tennis":           "网球",
		"Americanfootball": "美式橄榄球",
		"Cricket":          "板球",
		"Handball":         "手球",
		"Icehockey":        "冰球",
		"Snooker":          "斯诺克",
		"Volleyball":       "排球",
		"Badminton":        "羽毛球",
		"Multiple Parlay":  "过关",
		"Esport":           "电子竞技",
	}
	return &ThreeUpSrvice{
		Url:                   url,
		brandName:             "three_up",
		prefixCode:            prefixCode,
		outGoingIp:            outGoingIp,
		platform:              platform,
		view:                  view,
		currency:              currency,
		passkey:               passkey,
		games:                 games,
		games_en:              gamesEn,
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

type ThreeUpSrvice struct {
	Url                   string
	brandName             string
	prefixCode            string
	outGoingIp            []string
	platform              string
	view                  string
	currency              string
	passkey               string
	games                 map[string]string
	games_en              map[string]string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func (e *ThreeUpSrvice) gunzip(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	// 读取解压缩后的数据
	uncompressed, err := ioutil.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	return uncompressed, nil
}

func (e *ThreeUpSrvice) threeup_http_get(path string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s%s", e.Url, path)
	client := &http.Client{}
	logs.Info("threeup_http_get url %s", url)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logs.Error("threeup_http_get request error:", url, err)
		return nil, err
	}
	//"Content-Type":    "application/json;charset=UTF-8",
	req.Header.Add("passkey", e.passkey)
	req.Header.Add("langs", "3")
	req.Header.Add("prefix", e.prefixCode)
	req.Header.Add("Accept-Encoding", "gzip")

	resp, err := client.Do(req)
	logs.Debug("resp", resp)
	logs.Debug("err", err)
	if resp == nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("http error, status code: %d", resp.StatusCode)
		logs.Error("threeup_http_get json error:", url, err)
		return nil, err
	}

	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logs.Error("threeup_http_get json error:", url, err)
		return nil, err
	}

	// 如果响应头中包含"Content-Encoding: gzip"，则解压缩
	if resp.Header.Get("Content-Encoding") == "gzip" {
		body, err = e.gunzip(body)
		if err != nil {
			logs.Error("threeup_http_get json error:", url, err)
			return nil, err
		}
	}

	// logs.Debug("threeup_http_get:", url, "|", "|", string(body))

	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("threeup_http_get json error:", url, err)
		return nil, err
	}

	if _, ok := jdata["error_status"]; ok {
		if abugo.GetInt64FromInterface(jdata["error_status"]) == 0 || abugo.GetFloat64FromInterface(jdata["error_status"]) == float64(0) {
			return jdata, nil
		} else {
			logs.Error("threeup_http_get json error:", url, string(body))
			return nil, errors.New("threeup_http_get 解析失败")
		}

	}
	return jdata, nil
}

func (e *ThreeUpSrvice) ThreeUpLogin(username string, platform string, lobby string) (map[string]interface{}, error) {
	// logs.Info("ThreeUpLogin")
	_username := fmt.Sprintf("%s%s", e.prefixCode, username)
	lobby = base64.URLEncoding.EncodeToString([]byte(lobby))

	get, err := e.threeup_http_get(fmt.Sprintf("/GetUrl?username=%s&platform=%s&interface=%s&lobby=%s", _username, platform, e.view, lobby))
	if err != nil {
		logs.Error("ThreeUpLogin:", err)
		return nil, err
	}
	logs.Info("[INFO][TreeUp] ThreeUpLogin ==>", get)
	logs.Debug("ThreeUpLogin:", get)
	return get, nil
}

func (e *ThreeUpSrvice) ThreeUpRegister(username string, userId int) (map[string]interface{}, error) {
	// logs.Info("ThreeUpRegister")

	_username := fmt.Sprintf("%s%s", e.prefixCode, username)

	thirdId := _username
	get, err := e.threeup_http_get(fmt.Sprintf("/CreateMember?username=%s&currency=%s&vendor_member_id=%s", _username, e.currency, thirdId))
	if err != nil {
		logs.Error("ThreeUpRegister:", err)
		return nil, err
	}
	logs.Debug("ThreeUpRegister:", get)
	logs.Info("[INFO][TreeUp] ThreeUpRegister ==>", get)
	if value, ok := get["error_status"]; ok {
		if value == 0 {
			_, err = server.Db().Conn().Exec("update x_user set thirdId = ? where UserId = ?", thirdId, userId)
			if err != nil {
				logs.Error("ThreeUpRegister:", err)
				return nil, err
			}
		}
	} else {
		// error_status key doesn't exist
		logs.Error("ThreeUpRegister:", get)
		return nil, errors.New("ThreeUpRegister 解析失败")
	}
	return get, nil
}

func (e *ThreeUpSrvice) ThreeUpLoginAndRegister(ctx *abugo.AbuHttpContent) {
	logs.Info("ThreeUpLoginAndRegister")
	type RequestData struct {
		Lang     string
		Platform string
		Lobby    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("ThreeUpLoginAndRegister:", err)
		return
	}

	token := server.GetToken(ctx)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
	if data == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, e.brandName, e.brandName)
	if err != nil {
		logs.Error(e.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", e.brandName, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v_threeup", server.Project(), server.Module(), token.UserId)
	username := strconv.Itoa(token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	login, err := e.ThreeUpLogin(username, reqdata.Platform, reqdata.Lobby)
	if err != nil {
		register, err := e.ThreeUpRegister(username, token.UserId)
		if err != nil {
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
		if abugo.GetInt64FromInterface(register["error_status"]) == 0 {
			login, err := e.ThreeUpLogin(strconv.Itoa(token.UserId), reqdata.Platform, reqdata.Lobby)
			if err != nil {
				ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
				return
			}
			if value, ok := login["Data"]; ok {
				ctx.Put("url", value.(string)+"&lang="+reqdata.Lang)
			}
		} else {
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
	}

	userIP := ctx.GetIp()
	ot := getRegion(userIP)
	logs.Debug("ot", ot)

	if value, ok := login["Data"]; ok {
		url := fmt.Sprintf("%s&langs=%s&ot=%d", value.(string), reqdata.Lang, ot)
		ctx.Put("url", url)

	} else {
		ctx.RespJson(map[string]interface{}{
			"status": 0,
			"msg":    "",
		})
		return
	}

	ctx.RespOK()
}

func (e *ThreeUpSrvice) getUser(thirdId string) (udata *map[string]interface{}, balance float64, err error) {
	where := abugo.AbuDbWhere{}
	// logs.Info(thirdId)
	// logs.Info(e.prefixCode)
	userId := strings.Replace(thirdId, e.prefixCode, "", 1)
	// logs.Info(userId)
	where.Add("and", "UserId", "=", userId, nil)
	udata, err = server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		return nil, 0, err
	}
	// logs.Info(udata)
	logs.Info("[INFO][TreeUp] getUserInfo ==>", udata)
	if udata == nil {
		return nil, 0, errors.New("user not found")
	}
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	// balanceStr := fmt.Sprintf("%.2f", balance)
	// balance, _ = strconv.ParseFloat(balanceStr, 64)

	return udata, balance, nil
}

func (e *ThreeUpSrvice) getUserById(userId int) (udata *map[string]interface{}, balance float64, err error) {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", userId, nil)
	udata, err = server.Db().Table("x_user").Where(where).GetOne()
	if err != nil {
		return nil, 0, err
	}
	if udata == nil {
		return nil, 0, errors.New("user not found")
	}
	balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	// balanceStr := fmt.Sprintf("%.2f", balance)
	// balance, _ = strconv.ParseFloat(balanceStr, 64)
	return udata, balance, nil
}

func (e *ThreeUpSrvice) getBetCtxFromRowData(_rawData string) (_betCtx string) {
	// 拼接下注内容 开始
	_betCtx = _rawData
	// sport 游戏项目
	_betCtx = strings.ReplaceAll(_betCtx, "\"sport\"", "\"sport游戏项目\"")
	// wager_id 注单编号
	_betCtx = strings.ReplaceAll(_betCtx, "\"wager_id\"", "\"wager_id注单编号\"")
	// bet_date 下注日期
	_betCtx = strings.ReplaceAll(_betCtx, "\"bet_date\"", "\"bet_date下注日期\"")
	// match_date 赛事日期
	_betCtx = strings.ReplaceAll(_betCtx, "\"match_date\"", "\"match_date赛事日期\"")
	// match_id 赛事编号
	_betCtx = strings.ReplaceAll(_betCtx, "\"match_id\"", "\"match_id赛事编号\"")
	// bet_type 赌注项目
	_betCtx = strings.ReplaceAll(_betCtx, "\"bet_type\"", "\"bet_type赌注项目\"")
	// team_bet 赌注项目
	_betCtx = strings.ReplaceAll(_betCtx, "\"team_bet\"", "\"team_bet赌注项目\"")
	// handicap_value 让球
	_betCtx = strings.ReplaceAll(_betCtx, "\"handicap_value\"", "\"handicap_value让球\"")
	// odds_type 赔率种类
	_betCtx = strings.ReplaceAll(_betCtx, "\"odds_type\"", "\"odds_type赔率种类\"")
	// odds 下注赔率
	_betCtx = strings.ReplaceAll(_betCtx, "\"odds\"", "\"odds下注赔率\"")
	// currency 会员货币
	_betCtx = strings.ReplaceAll(_betCtx, "\"currency\"", "\"currency会员货币\"")
	// stake 下注金额
	_betCtx = strings.ReplaceAll(_betCtx, "\"stake\"", "\"stake下注金额\"")
	// settlement_date 结算日期
	_betCtx = strings.ReplaceAll(_betCtx, "\"settlement_date\"", "\"settlement_date结算日期\"")
	// payoff 输赢金额
	_betCtx = strings.ReplaceAll(_betCtx, "\"payoff\"", "\"payoff输赢金额\"")
	// final_stake 有效会员金额
	_betCtx = strings.ReplaceAll(_betCtx, "\"final_stake\"", "\"final_stake有效会员金额\"")
	// running_score 只限于走地
	_betCtx = strings.ReplaceAll(_betCtx, "\"running_score\"", "\"running_score只限于走地\"")
	// last_update 最后更新时间
	_betCtx = strings.ReplaceAll(_betCtx, "\"last_update\"", "\"last_update最后更新时间\"")
	// playtype 玩法
	_betCtx = strings.ReplaceAll(_betCtx, "\"playtype\"", "\"playtype玩法\"")
	// league_name 联赛名称
	_betCtx = strings.ReplaceAll(_betCtx, "\"league_name\"", "\"league_name联赛名称\"")
	// teamA_name 队伍A名称
	_betCtx = strings.ReplaceAll(_betCtx, "\"teamA_name\"", "\"teamA_name队伍A名称\"")
	// teamB_name 队伍B名称
	_betCtx = strings.ReplaceAll(_betCtx, "\"teamB_name\"", "\"teamB_name队伍B名称\"")
	// score 比分
	_betCtx = strings.ReplaceAll(_betCtx, "\"score\"", "\"score比分\"")
	// goal_sequence 进球顺序
	_betCtx = strings.ReplaceAll(_betCtx, "\"goal_sequence\"", "\"goal_sequence进球顺序\"")
	// result 比赛结果
	_betCtx = strings.ReplaceAll(_betCtx, "\"result\"", "\"result比赛结果\"")
	// cancel_reason 注单被取消的原因
	_betCtx = strings.ReplaceAll(_betCtx, "\"cancel_reason\"", "\"cancel_reason注单被取消的原因\"")
	// 拼接下注内容 结束
	return
}

// settleapi

func (e *ThreeUpSrvice) CallbackSettle(ctx *abugo.AbuHttpContent) {
	bodyBytes, _ := io.ReadAll(ctx.Gin().Request.Body)
	bodyStr, _ := url.QueryUnescape(string(bodyBytes))
	logs.Info("three_up CallbackSettle bodyBytes=", string(bodyBytes), "\n requrl=", ctx.Gin().Request.URL.String())
	logs.Info("three_up CallbackSettle bodyStr=", bodyStr)
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Transids []string `json:"dat" form:"dat"`
	}
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("CallbackSettle:", err)
		return
	}
	logs.Info("three_up CallbackSettle reqdata2=", reqdata)
	if len(reqdata.Transids) <= 0 {
		// logs.Info("ThreeUpSrvice CallbackSettle:", reqdata)
		// logs.Info("ThreeUpSrvice CallbackSettle:", (ctx.Gin().Request.Body))
		//logs.Info("ThreeUpSrvice CallbackSettle:", (ctx.Gin().Request.Form))
		str := ctx.Gin().Request.Form
		// logs.Info("", str)
		for _, i := range str {
			// logs.Error("dat", i)
			reqdata.Transids = append(reqdata.Transids, i[0])
		}

	}

	thirdIds := reqdata.Transids
	resps := []map[string]interface{}{}

	//todo  由于未加事务  三方又有并发问题  加个锁
	thirdIds_str, _ := json.Marshal(thirdIds)
	rediskey := fmt.Sprintf("%s", string(thirdIds_str))
	server.Redis().SetNxString(rediskey, "1", 5)

	for _, trxResult := range thirdIds {

		//交易码(Post 交易码),
		//会员帐号,
		//交易金额 ,
		//注明 (交易种类),
		//交易日期 (日期+时间（GMT-4） 格式: yyyymmddHHmmss),
		//注单号码
		//下注金额
		//输赢(未派彩注单/取消注单='-') 正负
		//注单状况 (1=未派彩注单, 2=已派彩注单t, 3=取消注单)
		split := strings.Split(abugo.InterfaceToString(trxResult), ",")
		logs.Info("split", split)
		trans_id := split[0]
		// 解析字符串为 time.Time
		thirdTime, err := time.Parse("20060102150405", split[4])
		if err != nil {
			fmt.Println("thirdTime解析错误:", err)
			//手动释放
			server.Redis().Del(rediskey)
			return
		}
		thirdTime = thirdTime.Add(12 * time.Hour)

		order_id := split[5]
		betAmount := abugo.InterfaceToFloat64(split[6])
		money := split[7]
		_type := split[8]
		userThirdId := split[1]
		r_id := userThirdId

		if _type == "1" {
			resps = append(resps, (map[string]interface{}{
				"d_id":       trans_id,
				"r_id":       r_id,
				"error_code": "EC-01",
				"result":     0}))
			logs.Error("CallbackSettle:", err)
			continue
		}

		udata, balance, err := e.getUser(userThirdId)
		if err != nil {
			resps = append(resps, (map[string]interface{}{
				"d_id":       trans_id,
				"r_id":       r_id,
				"error_code": "EC-01",
				"result":     0}))
			logs.Error("CallbackSettle:", err)
			continue
		}
		userId := abugo.InterfaceToInt((*udata)["UserId"])

		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		where.Add("and", "DataState", "=", -1, nil)
		betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if err != nil || betTran == nil {
			resps = append(resps, (map[string]interface{}{
				"d_id":   trans_id,
				"r_id":   r_id,
				"result": 1,
			}))
			continue
		}

		if (*betTran)["DataState"] == "1" || (*betTran)["DataState"] == 1 {
			resps = append(resps, (map[string]interface{}{
				"d_id":   trans_id,
				"r_id":   r_id,
				"result": 1,
			}))
			continue
		}

		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		payMoney := abugo.InterfaceToFloat64(money)
		payMoney = betAmount + payMoney

		validAmount := threeupValidBet(betAmount, payMoney)

		get, err := e.threeup_http_get(fmt.Sprintf("/GetBetDetailByWagerID?wager_id=%s", order_id))
		_gameId, _gameName, _rawData, _result, _betCtx := "", "", "", "", ""
		// 从原始数据中解析bet_date和settlement_date
		var betTime, settleTime, betLocalTime string
		if err == nil {
			if value, ok := get["error_status"]; ok {
				if value == 0 || value == 1 || value == float64(0) || value == float64(1) {
					betDetails, ok := get["Data"].(map[string]interface{})["BetDetailsNormal"]
					if !ok {
						betDetails, ok = get["Data"].(map[string]interface{})["BetDetailsParlay"]
						if !ok {
							logs.Error("BetDetailsNormal 、BetDetailsParlay none:", get["Data"])
						}
					}
					gameName, ok := betDetails.([]interface{})[0].(map[string]interface{})["sport"].(string)
					if !ok {
						logs.Error("betDetails:", betDetails)
					}

					logs.Error("gameName:", gameName)
					gameNameCh := strings.ToLower(gameName)
					gameNameCh = e.games_en[gameName]
					if len(gameNameCh) > 0 {
						gameId := e.games[gameNameCh]
						_gameId = gameId
						_gameName = gameNameCh
					} else {
						_gameId = gameName
						_gameName = gameName
					}

					result, ok := betDetails.([]interface{})[0].(map[string]interface{})["result"].(string)
					if !ok {
						logs.Error("betDetails:", betDetails)
					}
					_result = result

					rawData, err := json.Marshal(betDetails.([]interface{})[0])
					if err != nil {
						logs.Debug("Error marshalling data to JSON: %v", err)
					}
					_rawData = string(rawData)

					_betCtx = e.getBetCtxFromRowData(_rawData)

					// 从原始数据中解析bet_date和settlement_date 2025-2-15 需求记录投注时间和结算时间
					var rawDataMap map[string]interface{}
					_rawData := string(rawData)
					if err := json.Unmarshal([]byte(_rawData), &rawDataMap); err == nil {
						if betDate, ok := rawDataMap["bet_date"].(string); ok { //存在投注日期
							betTime = utils.ParseTimeString(betDate)          //保留原始时间 西4区
							betLocalTime = utils.ParseTimeToLocalStr(betDate) //转北京时间
						}
						if settlementDate, ok := rawDataMap["settlement_date"].(string); ok { //存在结算日期才需要解析
							if t, err := time.Parse("2006-01-02", settlementDate); err == nil {
								logs.Info("CallbackTracker 结算日期：", t)
								settleTime = utils.ToWest4(time.Now()) //使用西4区时间
							}
						}
					}

					_, err = server.Db().Conn().Exec("update x_third_sport_pre_order set GameId =  ?, GameName = ? ,ThirdTime = ? ,RawData = ?, BetCtx=?, GameRst=?, BetCtxType=3, BetTime = ?,betLocalTime = ?, SettleTime = NULLIF(?, '') where Id = ? ", _gameId, _gameName, thirdTime, _rawData, _betCtx, _betCtx, betTime, betLocalTime, settleTime, betId)
					if err != nil {
						logs.Error("CallbackSettle:", err)
						continue
					}
				}
			} else {
				// error_status key doesn't exist
				logs.Error("CallbackSettle:", get)
				continue
			}
		}
		if _result == "Draw" {
			validAmount = 0
		}
		winAmount := payMoney
		reason := utils.BalanceCReasonThreeupWin
		memo := "three_up settle,thirdId:" + order_id
		if _type == "3" {
			payMoney = betAmount
			reason = utils.BalanceCReasonThreeupCancel
			memo = "three_up cancel,thirdId:" + order_id
			winAmount = 0
			validAmount = 0
		}

		server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", payMoney, userId)
		if _type == "3" {
			server.Db().Conn().Exec("update x_third_sport_pre_order set WinAmount = WinAmount + ?, ValidBet = ?, DataState=-2, BetCtx=?, GameRst=?, BetCtxType=3 where Id = ? ", winAmount, validAmount, _betCtx, _betCtx, betId)
		} else {
			server.Db().Conn().Exec("update x_third_sport_pre_order set WinAmount = WinAmount + ?, ValidBet = ?, DataState=1, BetCtx=?, GameRst=?, BetCtxType=3 where Id = ? ", winAmount, validAmount, _betCtx, _betCtx, betId)
		}

		betTranData := *betTran
		delete(betTranData, "Id")
		delete(betTranData, "CreateTime")
		betTranData["WinAmount"] = winAmount
		betTranData["ValidBet"] = validAmount
		betTranData["RawData"] = _rawData
		betTranData["BetCtx"] = _betCtx
		betTranData["GameRst"] = _betCtx
		betTranData["BetCtxType"] = 3
		betTranData["DataState"] = 1
		betTranData["ThirdTime"] = thirdTime
		// 只有当 settleTime 不为空时才设置
		if len(settleTime) > 0 {
			betTranData["SettleTime"] = settleTime
		}

		if _gameName != "" {
			betTranData["GameId"] = _gameId
			betTranData["GameName"] = _gameName
		}

		if _type == "2" {
			server.Db().Table("x_third_sport").Insert(betTranData)
		}

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance,
			"Amount":       payMoney,
			"AfterAmount":  balance + payMoney,
			"Reason":       reason,
			"Memo":         memo,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		resps = append(resps, (map[string]interface{}{
			"d_id":   trans_id,
			"r_id":   r_id,
			"result": 1,
		}))

		// 推送派奖事件
		if e.thirdGamePush != nil && _type == "2" {
			e.thirdGamePush.PushRewardEvent(6, e.brandName, order_id)
		}
		// 发送余额变动通知
		go func(notifyUserId int) {
			if e.RefreshUserAmountFunc != nil {
				tmpErr := e.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][TreeUp] CallbackSettle 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][TreeUp] CallbackSettle 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int(userId))
		continue
	}

	logs.Debug("CallbackSettle:", "结算成功")
	logs.Info("[INFO][TreeUp] CallbackSettle SUCC ==>", reqdata)
	ctx.Gin().Header("Content-Type", "text/plain")
	//手动释放
	server.Redis().Del(rediskey)
	ctx.RespJson(resps)
}

// acknowledgement
func (e *ThreeUpSrvice) CallbackAck(ctx *abugo.AbuHttpContent) {
	bodyBytes, _ := io.ReadAll(ctx.Gin().Request.Body)
	bodyStr, _ := url.QueryUnescape(string(bodyBytes))
	logs.Info("three_up CallbackAck bodyBytes=", string(bodyBytes), "\n requrl=", ctx.Gin().Request.URL.String())
	logs.Info("three_up CallbackAck bodyStr=", bodyStr)
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Transid string `json:"transid" form:"transid"`
		Type    int    `json:"type" form:"type"`
		Wagerid string `json:"wagerid" form:"wagerid"`
	}
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("CallbackAck:", err)
		return
	}
	logs.Info("three_up CallbackAck reqdata2=", reqdata)
	thirdIds := reqdata.Transid
	_type := reqdata.Type
	wagerid := reqdata.Wagerid
	where := abugo.AbuDbWhere{}
	logs.Debug("CallbackAck:", thirdIds, _type, wagerid)
	logs.Info("[INFO][TreeUp] CallbackAck ==>", thirdIds, _type, wagerid)
	where.Add("and", "ThirdId", "=", thirdIds, nil)
	where.Add("and", "Brand", "=", e.brandName, nil)
	betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()

	if _type == 1 {
		get, _ := e.threeup_http_get(fmt.Sprintf("/GetBetDetailByWagerID?wager_id=%s", wagerid))
		_gameId, _gameName, _rawData := "", "", ""
		if err == nil {
			if value, ok := get["error_status"]; ok {
				if value == 0 || value == 1 || value == float64(0) || value == float64(1) {
					betDetails, ok := get["Data"].(map[string]interface{})["BetDetailsNormal"]
					if !ok {
						betDetails, ok = get["Data"].(map[string]interface{})["BetDetailsParlay"]
						if !ok {
							logs.Error("BetDetailsNormal 、BetDetailsParlay none:", get["Data"])
						}
					}
					gameName, ok := betDetails.([]interface{})[0].(map[string]interface{})["sport"].(string)
					if !ok {
						logs.Error("betDetails:", betDetails)
					}

					logs.Error("gameName:", gameName)
					gameNameCh := strings.ToLower(gameName)
					gameNameCh = e.games_en[gameName]
					if len(gameNameCh) > 0 {
						gameId := e.games[gameNameCh]
						_gameId = gameId
						_gameName = gameNameCh
					} else {
						_gameId = gameName
						_gameName = gameName
					}

					rawData, err := json.Marshal(betDetails.([]interface{})[0])
					if err != nil {
						logs.Debug("Error marshalling data to JSON: %v", err)
					}
					_rawData = string(rawData)
				}
			} else {
				// error_status key doesn't exist
				logs.Error("CallbackAck:", get)
			}
		}
		server.Db().Conn().Exec("update x_third_sport_pre_order set ThirdId = ?, GameId = ?,GameName=? ,RawData =?  where ThirdId = ?", wagerid,
			_gameId, _gameName, _rawData, thirdIds)
		//新订单号绑定到日志上
		server.Db().Conn().Exec("update x_amount_change_log set Memo = ?  where Memo = ?", "three_up bet,thirdId:"+wagerid, "three_up bet,thirdId:"+thirdIds)
	}

	ctx.Gin().Header("Content-Type", "text/plain")

	if err != nil || betTran == nil {
		logs.Error("order none:", err)
		ctx.RespJson(map[string]interface{}{
			"transid":    thirdIds,
			"status":     0,
			"error_code": "ERR-01",
			"remark":     "注单不存在",
		})
		return
	}
	ctx.RespJson(map[string]interface{}{
		"transid":    thirdIds,
		"status":     1,
		"error_code": "",
		"remark":     "",
	})
	logs.Debug("threeup CallbackAck:", "下注成功")
}

// rollback
func (e *ThreeUpSrvice) CallbackRollback(ctx *abugo.AbuHttpContent) {
	bodyBytes, _ := io.ReadAll(ctx.Gin().Request.Body)
	bodyStr, _ := url.QueryUnescape(string(bodyBytes))
	logs.Info("three_up CallbackRollback bodyBytes=", string(bodyBytes), "\n requrl=", ctx.Gin().Request.URL.String())
	logs.Info("three_up CallbackRollback bodyStr=", bodyStr)
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Trxresult []string `json:"trxresult" form:"trxresult"`
	}
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("CallbackRollback:", err)
		return
	}
	logs.Info("three_up CallbackRollback reqdata2=", reqdata)
	if len(reqdata.Trxresult) <= 0 {
		str := ctx.Gin().Request.Form
		//logs.Info("", str)
		for _, i := range str {
			//logs.Error("dat", i)
			reqdata.Trxresult = append(reqdata.Trxresult, i[0])
		}
	}
	thirdIds := reqdata.Trxresult
	if len(reqdata.Trxresult) <= 0 {
		return
	}
	ctx.Gin().Header("Content-Type", "text/plain")
	logs.Debug("ThreeUpSrvice CallbackRollback type: %s, ", reqdata)
	logs.Info("[INFO][TreeUp] CallbackRollback ==>", reqdata)
	for _, trxResult := range thirdIds {

		split := strings.Split(abugo.InterfaceToString(trxResult), ",")
		order_id := split[0]
		status := split[1]
		if abugo.InterfaceToInt(status) == 1 {
			continue
		}
		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", order_id, nil)
		where.Add("and", "Brand", "=", e.brandName, nil)
		where.Add("and", "DataState", "=", -1, nil)
		betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
		if err != nil || betTran == nil {
			ctx.RespJson(map[string]interface{}{
				"recv":          0,
				"trx":           split[0],
				"dtime":         utils.GetCurrentTime(),
				"balancebefore": "",
				"balanceafter":  "",
				"transamt":      "",
				"error_code":    "ERR-02",
				"rolltrx":       split[3],
			})
			return
		}
		userId := abugo.InterfaceToInt((*betTran)["UserId"])
		udata, balance, _ := e.getUserById(int(userId))

		betId := abugo.GetInt64FromInterface((*betTran)["Id"])
		betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])

		refundMoney := betAmount
		server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?",
			refundMoney, userId)

		server.Db().Conn().Exec("update x_third_sport_pre_order set BetAmount = 0, WinAmount = 0, ValidBet = 0 ,DataState = -2 where Id = ?",
			betId)

		amountLog := xgo.H{
			"UserId":       userId,
			"BeforeAmount": balance,
			"Amount":       refundMoney,
			"AfterAmount":  balance + refundMoney,
			"Reason":       utils.BalanceCReasonThreeupCancel,
			"Memo":         "three_up cancel,thirdId:" + order_id,
			"SellerId":     (*udata)["SellerId"],
			"ChannelId":    (*udata)["ChannelId"],
		}
		server.Db().Table("x_amount_change_log").Insert(amountLog)

		ctx.RespJson(map[string]interface{}{
			"recv":          1,
			"trx":           split[0],
			"dtime":         utils.GetCurrentTime(),
			"balancebefore": abugo.GetFloat64FromInterface(balance),
			"balanceafter":  abugo.GetFloat64FromInterface(balance) + abugo.GetFloat64FromInterface((*betTran)["BetAmount"]),
			"transamt":      abugo.GetFloat64FromInterface((*betTran)["BetAmount"]),
			"error_code":    "",
			"rolltrx":       split[3],
		})

		logs.Debug("threeup CallbackRollback:", "取消成功")
		// 发送余额变动通知
		go func(notifyUserId int) {
			if e.RefreshUserAmountFunc != nil {
				tmpErr := e.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][TreeUp] CallbackRollback 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][TreeUp] CallbackRollback 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int(userId))

		return
	}
}

// deductapi
func (e *ThreeUpSrvice) CallbackDeduct(ctx *abugo.AbuHttpContent) {
	bodyBytes, _ := io.ReadAll(ctx.Gin().Request.Body)
	bodyStr, _ := url.QueryUnescape(string(bodyBytes))
	logs.Info("three_up CallbackDeduct bodyBytes=", string(bodyBytes), "\n requrl=", ctx.Gin().Request.URL.String())
	logs.Info("three_up CallbackDeduct bodyStr=", bodyStr)
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Acc     string  `json:"acc" form:"acc"`
		Transid string  `json:"transid" form:"transid"`
		Amt     float64 `json:"amt" form:"amt"`
	}
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("CallbackDeduct:", err)
		return
	}
	logs.Info("three_up CallbackDeduct reqdata2=", reqdata)
	ctx.Gin().Header("Content-Type", "text/plain")

	thirdId := reqdata.Acc
	order_id := reqdata.Transid
	amt := reqdata.Amt

	resp := map[string]interface{}{
		"transid":       order_id,
		"result":        0,
		"error_code":    "",
		"remark":        "",
		"dtime":         utils.GetCurrentTime(),
		"balancebefore": "",
		"balanceafter":  "",
		"transamt":      abugo.InterfaceToString(amt),
	}

	reqDataByte, _ := json.Marshal(gin.H{
		"acc":     thirdId,
		"transid": order_id,
		"amt":     amt,
	})
	reqData := string(reqDataByte)
	logs.Debug("ThreeUpSrvice CallbackDeduct type: %s, ", reqData)
	logs.Info("[INFO][TreeUp] CallbackDeduct type: %s, ==>", reqData)
	betAmt := abugo.InterfaceToFloat64(amt)

	udata, balance, err := e.getUser(thirdId)
	if err != nil {
		resp["error_code"] = "EC-01"
		resp["remark"] = "User not found"
		ctx.RespJson(resp)
		return
	}

	// 检查游戏权限
	userId := int(abugo.GetInt64FromInterface((*udata)["UserId"]))
	if allowed, hint, err := base.BeforeEnterGame(userId, utils.GameTypeSports, e.brandName); err != nil {
		logs.Error("ThreeUp CallbackDeduct 权限检查错误 userId=", userId, " gameId=", e.brandName, " err=", err.Error())
		resp["error_code"] = "EC-01"
		resp["remark"] = "User disabled"
		ctx.RespJson(resp)
		return
	} else if !allowed {
		logs.Error("ThreeUp CallbackDeduct 权限被拒绝 userId=", userId, " gameId=", e.brandName, " hint=", hint)
		resp["error_code"] = "EC-01"
		resp["remark"] = "User disabled"
		ctx.RespJson(resp)
		return
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	resp["balancebefore"] = balance
	resp["balanceafter"] = balance
	if betAmt > balance {
		resp["error_code"] = "EC-06"
		resp["remark"] = "Insufficient Fund"
		ctx.RespJson(resp)
		return
	}
	resp["balanceafter"] = balance - betAmt

	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", order_id, nil)
	where.Add("and", "Brand", "=", e.brandName, nil)
	betTran, _ := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
	if betTran != nil {
		resp["error_code"] = "EC-04"
		resp["remark"] = "下注失败，注单已存在，重复投注"
		ctx.RespJson(resp)
		return
	}
	_, err = server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?",
		betAmt, e.getUserFromThirdId(thirdId), betAmt)
	if err != nil {
		resp["error_code"] = "EC-06"
		resp["remark"] = "Insufficient Fund"
		ctx.RespJson(resp)
		return
	}
	amountLog := xgo.H{
		"UserId":       (*udata)["UserId"],
		"BeforeAmount": balance,
		"Amount":       0 - betAmt,
		"AfterAmount":  balance - betAmt,
		"Reason":       utils.BalanceCReasonThreeupBet,
		"Memo":         "three_up bet,thirdId:" + order_id,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)

	balance -= betAmt
	//三升体育投注时间和结算时间按西四区 同三方后台保持一致
	betTime := utils.ToWest4(time.Now())
	betLocalTime := utils.GetCurrentTime() //三方没有传投注日期 取系统当前日期
	order := xgo.H{
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
		"BetChannelId": ChannelId,
		"UserId":       (*udata)["UserId"],
		"Brand":        e.brandName,
		"ThirdId":      order_id,
		"GameId":       "",
		"GameName":     "",
		"BetAmount":    betAmt,
		"WinAmount":    0,
		"ValidBet":     0,
		"ThirdTime":    time.Now().Format("2006-01-02 15:04:05"),
		"BetTime":      betTime,
		"BetLocalTime": betLocalTime,
		"Currency":     "CNY",
		"RawData":      string(reqData),
		"DataState":    -1,
	}
	_, err = server.Db().Table("x_third_sport_pre_order").Insert(order)
	if err != nil {
		logs.Error(" CallbackDeduct bet", err)
		ctx.RespJson(map[string]interface{}{
			"transid":       order_id,
			"result":        1,
			"error_code":    "EC-06",
			"remark":        "Insufficient Fund",
			"dtime":         time.Now().Format("2006-01-02 15:04:05"),
			"balancebefore": balance + betAmt,
			"balanceafter":  balance,
			"transamt":      abugo.InterfaceToString(amt),
		})
		return
	}

	resp["result"] = "1"

	ctx.RespJson(resp)
	logs.Debug("threeup ThreeUpCallinApi:", "下注扣款成功")
	// 推送投注事件
	if e.thirdGamePush != nil {
		e.thirdGamePush.PushBetEvent(int(abugo.InterfaceToInt((*udata)["UserId"])), "三升体育", e.brandName, betAmt, "CNY", e.brandName, thirdId, 6)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][TreeUp] CallbackDeduct 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][TreeUp] CallbackDeduct 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(int(abugo.InterfaceToInt((*udata)["UserId"])))
}

// getcclapi
func (e *ThreeUpSrvice) CallbackBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Acc string `json:"acc" form:"acc"`
	}
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("CallbackBalance:", err)
		return
	}
	ctx.Gin().Header("Content-Type", "text/plain")
	currentDateString := utils.GetCurrentTime()

	thirdId := reqdata.Acc
	if !strings.Contains(thirdId, e.prefixCode) {
		ctx.RespJson(map[string]interface{}{
			"result":           0,
			"error_code":       "EC-01",
			"maintenance_flag": 0,
			"dtime":            currentDateString,
		})
		return
	}
	_, balance, err := e.getUser(thirdId)
	if err != nil {
		ctx.RespJson(map[string]interface{}{
			"result":           0,
			"error_code":       "EC-02",
			"maintenance_flag": 0,
			"dtime":            currentDateString,
		})
		return
	}
	ctx.RespJson(map[string]interface{}{
		"result":           1,
		"error_code":       "",
		"balance":          balance,
		"maintenance_flag": 0,
		"dtime":            currentDateString,
	})
}

// trackerapi 此API接口是用于注单现时状况

func (e *ThreeUpSrvice) CallbackTracker(ctx *abugo.AbuHttpContent) {
	bodyBytes, _ := io.ReadAll(ctx.Gin().Request.Body)
	bodyStr, _ := url.QueryUnescape(string(bodyBytes))
	logs.Info("three_up CallbackTracker bodyBytes=", string(bodyBytes), "\n requrl=", ctx.Gin().Request.URL.String())
	logs.Info("three_up CallbackTracker bodyStr=", bodyStr)
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type TrackerDataEntry struct {
		WagerID int    `json:"wager_id" form:"wager_id"`
		Result  string `json:"result" form:"result"`
	}

	type RequestData map[int]TrackerDataEntry

	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("trackerapi:", err)
		return
	}
	logs.Info("three_up CallbackTracker reqdata2=", reqdata)
	inputData := ctx.Gin().Request.Form

	// 正则表达式用于提取trackerdata的索引和字段名
	re := regexp.MustCompile(`trackerdata\[(\d+)\]\[(\w+)\]`)

	// 创建一个映射来存储每个trackerdata条目的属性
	trackerData := make(map[string]map[string]string)

	// 遍历输入数据，填充trackerData映射
	for key, value := range inputData {
		matches := re.FindStringSubmatch(key)
		if len(matches) == 3 {
			index := matches[1]
			field := matches[2]

			if trackerData[index] == nil {
				trackerData[index] = make(map[string]string)
			}

			// 由于输入值是一个字符串数组，这里我们只取第一个元素
			trackerData[index][field] = value[0]
		}
	}

	// 检查每个条目的result，并打印符合条件的wager_id
	for _, entry := range trackerData {
		result := entry["result"]
		if result == "Lose" {

			logs.Debug("输单", "Wager ID:", entry["wager_id"], "Result:", result)
			logs.Info("[INFO][TreeUp] 输单==>", "Wager ID:", entry["wager_id"], "Result:", result)
			//更新失败注单
			order_id := entry["wager_id"]

			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", order_id, nil)
			where.Add("and", "Brand", "=", e.brandName, nil)
			where.Add("and", "DataState", "=", -1, nil)
			betTran, err := server.Db().Table("x_third_sport_pre_order").Where(where).GetOne()
			if err != nil || betTran == nil {
				continue
			}
			betId := abugo.GetInt64FromInterface((*betTran)["Id"])

			get, err := e.threeup_http_get(fmt.Sprintf("/GetBetDetailByWagerID?wager_id=%s", order_id))
			_gameId, _gameName := "", ""
			if err == nil {
				if value, ok := get["error_status"]; ok {
					if value == 0 || value == 1 || value == float64(0) || value == float64(1) {
						betDetails, ok := get["Data"].(map[string]interface{})["BetDetailsNormal"]
						if !ok {
							betDetails, ok = get["Data"].(map[string]interface{})["BetDetailsParlay"]
							if !ok {
								logs.Error("BetDetailsNormal 、BetDetailsParlay none:", get["Data"])
							}
						}
						gameName, ok := betDetails.([]interface{})[0].(map[string]interface{})["sport"].(string)
						if !ok {
							logs.Error("betDetails:", betDetails)
						}

						logs.Error("gameName:", gameName)
						gameNameCh := strings.ToLower(gameName)
						gameNameCh = e.games_en[gameName]
						if len(gameNameCh) > 0 {
							gameId := e.games[gameNameCh]
							_gameId = gameId
							_gameName = gameNameCh
						} else {
							_gameId = gameName
							_gameName = gameName
						}

						betDate, ok := betDetails.([]interface{})[0].(map[string]interface{})["bet_date"].(string)
						if !ok {
							logs.Error("betDetails:", betDetails)
						}
						// 时间格式，必须使用这个特定的时间作为例子
						layout := "2006-01-02T15:04:05"
						// 解析时间字符串
						timeObj, err := time.Parse(layout, betDate)
						if err != nil {
							fmt.Println("Error parsing time:", err)
							return
						}
						// 加上12小时
						thirdTime := timeObj.Add(12 * time.Hour)

						rawData, err := json.Marshal(betDetails.([]interface{})[0])
						if err != nil {
							logs.Debug("Error marshalling data to JSON: %v", err)
						}
						// 从原始数据中解析bet_date和settlement_date 2025-2-15 需求记录投注时间和结算时间
						var betTime, settleTime, betLocalTime string
						var rawDataMap map[string]interface{}
						_rawData := string(rawData)
						if err := json.Unmarshal([]byte(_rawData), &rawDataMap); err == nil {
							if betDate, ok := rawDataMap["bet_date"].(string); ok { //存在投注日期
								betTime = utils.ParseTimeString(betDate)          //保留原始时间 西4区
								betLocalTime = utils.ParseTimeToLocalStr(betDate) //转北京时间
							}
							if settlementDate, ok := rawDataMap["settlement_date"].(string); ok { //存在结算日期才需要解析
								if t, err := time.Parse("2006-01-02", settlementDate); err == nil {
									logs.Info("CallbackTracker 结算日期：", t)
									settleTime = utils.ToWest4(time.Now()) //使用西4区时间
								}
							}
						}

						_betCtx := e.getBetCtxFromRowData(string(rawData))
						_, err = server.Db().Conn().Exec("update x_third_sport_pre_order set WinAmount=0, ValidBet=BetAmount, DataState=1, GameId =  ?, GameName = ?,ThirdTime = ? ,RawData = ? ,BetTime = ?,BetLocalTime = ?, SettleTime = NULLIF(?, '')  where Id = ? ", _gameId, _gameName, thirdTime, string(rawData), betTime, betLocalTime, settleTime, betId)
						if err != nil {
							logs.Error("CallbackTracker:", err)
							continue
						}

						betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
						betTranData := *betTran
						delete(betTranData, "Id")
						delete(betTranData, "CreateTime")
						betTranData["WinAmount"] = 0
						betTranData["ValidBet"] = betAmount
						betTranData["RawData"] = string(rawData)
						betTranData["BetCtx"] = _betCtx
						betTranData["GameRst"] = _betCtx
						betTranData["BetCtxType"] = 3
						betTranData["DataState"] = 1
						// 只有当 settleTime 不为空时才设置
						if len(settleTime) > 0 {
							betTranData["SettleTime"] = settleTime
						}

						if _gameName != "" {
							betTranData["GameId"] = _gameId
							betTranData["GameName"] = _gameName
						}

						server.Db().Table("x_third_sport").Insert(betTranData)

						fmt.Println("处理成功")
					}
				} else {
					// error_status key doesn't exist
					logs.Error("CallbackTracker:", get)
					continue
				}
			}
		}
	}

	ctx.Gin().Header("Content-Type", "text/plain")
	ctx.RespJson(map[string]interface{}{
		"status": 1,
	})
	if len(inputData) > 0 {

	}
}

// ping
func (e *ThreeUpSrvice) CallbackPing(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Type int `json:"type" form:"type"`
	}
	ctx.Gin().Header("Content-Type", "text/plain")

	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		return
	}
	currentDateString := utils.GetCurrentTime()

	if reqdata.Type == 1 {
		gate := PingRespCheckGate{
			Dtime:           currentDateString,
			OutGoingIp:      strings.Join(e.outGoingIp, ","),
			MaintenanceFlag: 0,
		}
		ctx.RespJson(gate)
	} else if reqdata.Type == 2 {
		ping := PingRespCheckPing{
			Dtime:           currentDateString,
			PingStatus:      "1",
			PingTime:        "1",
			MaintenanceFlag: 0,
			PingRemark:      "",
		}
		ctx.RespJson(ping)
	} else {
		err := PingRespCheckErr{
			Dtime:           currentDateString,
			PingStatus:      "0",
			PingTime:        "0",
			MaintenanceFlag: 0,
			PingRemark:      "Time out",
		}
		ctx.RespJson(err)
	}
	ctx.RespOK()
}

func (e *ThreeUpSrvice) getUserFromThirdId(thirdId string) string {
	return strings.Replace(thirdId, e.prefixCode, "", 1)
}

type PingRequest struct {
	Type int `json:"type"`
}
type PingRespCheckGate struct {
	Dtime           string `json:"dtime"`
	OutGoingIp      string `json:"out_going_ip"`
	MaintenanceFlag int    `json:"maintenance_flag"`
}

type PingRespCheckPing struct {
	Dtime           string `json:"dtime"`
	PingStatus      string `json:"ping_status"`
	PingTime        string `json:"ping_time"`
	MaintenanceFlag int    `json:"maintenance_flag"`
	PingRemark      string `json:"ping_remark"`
}
type PingRespCheckErr struct {
	Dtime           string `json:"dtime"`
	PingStatus      string `json:"ping_status"`
	PingTime        string `json:"ping_time"`
	MaintenanceFlag int    `json:"maintenance_flag"`
	PingRemark      string `json:"ping_remark"`
}

func threeupValidBet(betAmt, winAmount float64) float64 {
	if winAmount > 0 {
		subAmount := winAmount - betAmt

		if subAmount > betAmt {
			return betAmt
		} else {
			return math.Abs(subAmount)
		}
	} else {
		return betAmt
	}
}

func getIPContinent(ipStr string) string {
	// 打开 MaxMind 数据库文件
	db, err := geoip2.Open("./config/GeoLite2-Country.mmdb")
	if err != nil {
		logs.Error("geoip2.Open", err)
		return ""
	}
	defer db.Close()

	// 解析 IP 地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		logs.Error("Invalid IP address format")
		return ""
	}

	// 查询 IP 地址的国家和洲信息
	record, err := db.Country(ip)
	if err != nil {
		logs.Error("db.Country(ip)", err)
		return ""
	}

	// 返回洲信息
	return record.Continent.Names["zh-CN"]
}

// getRegion 根据 IP 地址返回对应的盘类
func getRegion(ipStr string) int {
	// 根据洲分配盘类
	continent := getIPContinent(ipStr)
	logs.Debug("continent", continent)
	// 亚洲地区全部改港赔、其他地区全部改欧赔
	switch continent {
	case "亚洲":
		return 1
	case "欧洲":
		return 4
	case "北美洲", "南美洲":
		return 4
	case "非洲", "南极洲", "大洋洲":
		return 4
	default:
		return 4
	}
	//switch continent {
	//case "亚洲":
	//	return 1
	//case "欧洲":
	//	return 4
	//case "北美洲", "南美洲":
	//	return 5
	//case "非洲", "南极洲", "大洋洲":
	//	return 4
	//default:
	//	return 4
	//}
}
