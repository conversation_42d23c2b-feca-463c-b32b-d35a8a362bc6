package active

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BaseConfig 兑换码活动会员参与条件限制
type RedeemCodeBaseConfig struct {
	RegisterDays         int      `json:"RegisterDays"`         // 注册多少天内的会员可兑换，留空或填0表示不限制
	IsBindEmail          bool     `json:"IsBindEmail"`          // 是否已绑定邮箱地址
	IsActiveWallet       bool     `json:"IsActiveWallet"`       // 是否有激活的钱包地址
	BlockedIPList        string   `json:"BlockedIPList"`        // 被限制参与的IP，多个用英文逗号隔开，如：127.0.0.1,*******
	SpecificPlayerIDs    string   `json:"SpecificPlayerIDs"`    // 指定玩家可领取，多个ID用英文逗号隔开，如：123,4553
	ApplicableGames      int      `json:"ApplicableGames"`      // 参与活动的场馆，0=全站通用，1=指定场馆
	GameTypes            []string `json:"GameTypes"`            // 指定游戏类型列表，仅当ApplicableGames=1时有效
	VIPLevels            []int32  `json:"VIPLevels"`            // VIP层级，可多选，如：[1,2,3,4,5]，空数组表示全选
	DailyFlowAmount      float64  `json:"DailyFlowAmount"`      // 当日流水达到
	HistoryDepositAmount float64  `json:"HistoryDepositAmount"` // 历史累积充值金额
	HistoryDepositCount  int      `json:"HistoryDepositCount"`  // 历史累积充值次数
	HistoryDepositType   string   `json:"HistoryDepositType"`   // 历史累积充值类型，"金额"或"次数"
}

// 单个兑换码配置
type RedeemCodeItem struct {
	// 兑换码设置
	RedeemCode      string  `json:"RedeemCode"`      // 兑换码，仅包含数字和字母（大小写），长度3-12位
	ExchangeCount   *int    `json:"ExchangeCount"`   // 此码可兑换次数，不填则不限次数
	MinRewardAmount float64 `json:"MinRewardAmount"` // 奖励金额下限
	MaxRewardAmount float64 `json:"MaxRewardAmount"` // 奖励金额上限
	ExchangeTimes   int     `json:"ExchangeTimes"`   // 已经兑换次数
}

// 兑换码活动配置
type RedeemCodeConfig struct {
	// 兑换码列表
	RedeemCodes []RedeemCodeItem `json:"RedeemCodes"` // 兑换码列表

	// 奖励提取配置
	FlowMultiple float64 `json:"FlowMultiple"` // 提取奖励需打流水倍数
}

// 领取兑换码活动奖励(活动ID，活动等级，用户令牌，兑换码，运营商ID，渠道ID)
func RedeemCodeActive(activeId int, level int, ctx *abugo.AbuHttpContent, code string) (status int) {
	const (
		CodeSuccess   = 600 // 兑换成功
		CodeInvalid   = 601 // 此代码无效
		CodeExpired   = 602 // 此代码已过期
		CodeUsed      = 603 // 此代码已被使用，无法重复领取
		CodeExhausted = 604 // 此代码不可用，已达领取上限
		CodeError     = 605 // 此代码不可用，暂未达到领取要求
		CodeNotMatch  = 606 // 系统异常，请稍后重试或联系客服
	)
	host := ctx.Host()
	token := server.GetToken(ctx)
	channelId, sellerId := server.GetChannel(ctx, host)
	// 查询活动是否已开启 state 1 是开启 2 是关闭
	activeDefine, err := GetActiveDefine(int32(sellerId), int32(channelId), int32(activeId))
	if err != nil {
		logs.Error("RedeemCodeActive 活动ID=%d 在运营商=%d, 渠道=%d 下不存在或未开启", activeId, sellerId, channelId)
		return CodeInvalid
	}

	// 添加调试信息
	logs.Info("RedeemCodeActive 找到活动: 主键ID=%d, 活动ID=%d, 运营商=%d, 渠道=%d, 活动名称=%s, 状态=%d",
		activeDefine.Id, activeDefine.ActiveId, activeDefine.SellerId, activeDefine.ChannelId, activeDefine.Title, activeDefine.State)

	// 检查活动是否在有效期内
	currentTime := time.Now().Unix()

	// 将毫秒级时间戳转换为秒级时间戳
	startTimeInSeconds := activeDefine.EffectStartTime / 1000
	endTimeInSeconds := activeDefine.EffectEndTime / 1000

	// 如果配置了开始时间，检查活动是否已开始
	if startTimeInSeconds > 0 && startTimeInSeconds > currentTime {
		logs.Error("RedeemCodeActive 活动未开始异常")
		return CodeInvalid
	}

	// 如果配置了结束时间，检查活动是否已结束
	if endTimeInSeconds > 0 && endTimeInSeconds < currentTime {
		logs.Error("RedeemCodeActive 活动已结束异常")
		return CodeUsed
	}

	// 检查用户是否满足活动条件
	// 解析活动基础配置
	var baseConfig RedeemCodeBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseConfig)
	if err != nil {
		logs.Error("RedeemCodeActive 解析活动基础配置异常", err)
		return CodeNotMatch
	}

	// 解析活动配置
	var config RedeemCodeConfig

	// 检查配置是否为空
	if activeDefine.Config == "" {
		logs.Error("RedeemCodeActive 解析活动配置异常")
		return CodeNotMatch
	}

	// 尝试解析配置
	err = json.Unmarshal([]byte(activeDefine.Config), &config)
	if err != nil {
		logs.Error("RedeemCodeActive 解析活动配置异常: %v, config: %s", err, activeDefine.Config)
		return CodeNotMatch
	}

	// 查找匹配的兑换码配置
	var matchedCodeItem *RedeemCodeItem
	for i := range config.RedeemCodes {
		if config.RedeemCodes[i].RedeemCode == code {
			matchedCodeItem = &config.RedeemCodes[i]
			break
		}
	}

	// 如果没有找到匹配的兑换码，无效的兑换码
	if matchedCodeItem == nil {
		logs.Error("RedeemCodeActive 无效的兑换码异常")
		return CodeInvalid
	}

	// 获取用户信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("RedeemCodeActive 获取用户信息异常", err)
		return CodeNotMatch
	}

	// 1. 检查注册天数限制
	if baseConfig.RegisterDays > 0 {
		registerTime := user.RegisterTime
		// 计算注册天数
		daysPassed := int(time.Since(registerTime).Hours() / 24)
		if daysPassed > baseConfig.RegisterDays {
			logs.Error("RedeemCodeActive 注册天数限制异常")
			return CodeError
		}
	}

	// 2. 检查是否需要绑定邮箱
	if baseConfig.IsBindEmail && user.Email == "" {
		logs.Error("RedeemCodeActive 需要绑定邮箱异常")
		return CodeError
	}

	// 3. 检查是否需要激活钱包地址
	if baseConfig.IsActiveWallet {
		userWalletTb := server.DaoxHashGame().XUserWallet
		userWalletDb := server.DaoxHashGame().XUserWallet.WithContext(context.Background())

		// 查询用户是否有已验证的钱包地址
		walletCount, err := userWalletDb.
			Where(userWalletTb.UserID.Eq(int32(token.UserId))).
			Where(userWalletTb.SellerID.Eq(int32(sellerId))).
			Where(userWalletTb.ChannelID.Eq(int32(channelId))).
			Where(userWalletTb.State.Eq(1)). // 状态 1已验证 2未验证
			Count()

		if err != nil {
			logs.Error("RedeemCodeActive 获取钱包地址异常", err)
			return CodeError
		}

		if walletCount == 0 {
			logs.Error("RedeemCodeActive 需要激活钱包地址异常")
			return CodeError
		}
	}

	// 4. 检查IP限制
	if baseConfig.BlockedIPList != "" {
		// 获取用户IP
		userIPInfo, err := userDb.Select(userTb.LoginIP).
			Where(userTb.UserID.Eq(int32(token.UserId))).
			Where(userTb.SellerID.Eq(int32(sellerId))).
			Where(userTb.ChannelID.Eq(int32(channelId))).
			First()
		if err != nil {
			logs.Error("RedeemCodeActive 获取用户IP异常", err)
			return CodeError
		}

		userIP := userIPInfo.LoginIP
		blockedIPs := strings.Split(baseConfig.BlockedIPList, ",")
		for _, ip := range blockedIPs {
			if strings.TrimSpace(ip) == userIP {
				logs.Error("RedeemCodeActive 当前IP已限制异常")
				return CodeError
			}
		}
	}

	// 5. 检查指定玩家限制
	if baseConfig.SpecificPlayerIDs != "" {
		specificIDs := strings.Split(baseConfig.SpecificPlayerIDs, ",")
		userIDStr := strconv.FormatInt(int64(token.UserId), 10)

		found := false
		for _, id := range specificIDs {
			if strings.TrimSpace(id) == userIDStr {
				found = true
				break
			}
		}

		if !found {
			logs.Error("RedeemCodeActive 当前玩家不可参与异常")
			return CodeError
		}
	}

	// 6. 检查VIP等级限制
	if len(baseConfig.VIPLevels) > 0 {
		// 获取用户VIP等级
		xVipInfo := server.DaoxHashGame().XVipInfo
		vipInfoDb := server.DaoxHashGame().XVipInfo.WithContext(context.Background())

		vipInfo, err := vipInfoDb.Where(xVipInfo.UserID.Eq(int32(token.UserId))).
			Where(xVipInfo.SellerID.Eq(int32(sellerId))).
			Where(xVipInfo.ChannelID.Eq(int32(channelId))).
			First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Error("RedeemCodeActive 获取用户VIP等级异常", err)
			return CodeError
		}

		userVIPLevel := int32(1) // 默认VIP等级为1
		if vipInfo != nil {
			userVIPLevel = vipInfo.VipLevel
		}

		found := false
		for _, level := range baseConfig.VIPLevels {
			if level == userVIPLevel {
				found = true
				break
			}
		}

		if !found {
			logs.Error("RedeemCodeActive 当前玩家VIP等级不满足要求异常")
			return CodeError
		}
	}

	// 7. 检查当日流水要求
	if baseConfig.DailyFlowAmount > 0 {
		// 获取当日开始时间和结束时间
		now := time.Now()
		todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		todayEnd := todayStart.Add(24 * time.Hour)

		// 查询用户在当日的各类游戏流水总和（使用统一的流水计算函数）
		totalFlow, err := CalculateRedeemCodeActivityFlow(token.UserId, todayStart, todayEnd)
		if err != nil {
			logs.Error("RedeemCodeActive 获取当日流水异常", err)
			return CodeNotMatch
		}

		if totalFlow < baseConfig.DailyFlowAmount {
			logs.Error("RedeemCodeActive 当日流水不足异常")
			return CodeError
		}
	}

	// 8. 检查历史充值要求
	if baseConfig.HistoryDepositType == "金额" && baseConfig.HistoryDepositAmount > 0 {
		// 查询用户历史充值金额
		rechargeTb := server.DaoxHashGame().XRecharge
		rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

		// 查询用户所有成功充值的总金额
		type RechargeAmountResult struct {
			RechargeAmount float64
		}
		var rechargeAmountResult RechargeAmountResult
		err = rechargeDb.Select(rechargeTb.RealAmount.Sum().As("RechargeAmount")).
			Where(rechargeTb.UserID.Eq(int32(token.UserId))).
			Where(rechargeTb.SellerID.Eq(int32(sellerId))).
			Where(rechargeTb.ChannelID.Eq(int32(channelId))).
			Where(rechargeTb.State.Eq(5)). // 状态 5成功
			Scan(&rechargeAmountResult)
		if err != nil {
			logs.Error("RedeemCodeActive 获取历史充值金额异常", err)
			return CodeNotMatch
		}

		if rechargeAmountResult.RechargeAmount < baseConfig.HistoryDepositAmount {
			logs.Error("RedeemCodeActive 历史充值金额不足异常")
			return CodeError
		}
	} else if baseConfig.HistoryDepositType == "次数" && baseConfig.HistoryDepositCount > 0 {
		// 查询用户历史充值次数
		rechargeTb := server.DaoxHashGame().XRecharge
		rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

		// 查询用户所有成功充值的次数
		rechargeCount, err := rechargeDb.Where(rechargeTb.UserID.Eq(int32(token.UserId))).
			Where(rechargeTb.SellerID.Eq(int32(sellerId))).
			Where(rechargeTb.ChannelID.Eq(int32(channelId))).
			Where(rechargeTb.State.Eq(5)). // 状态 5成功
			Count()
		if err != nil {
			logs.Error("RedeemCodeActive 获取历史充值次数异常", err)
			return CodeNotMatch
		}

		if int(rechargeCount) < baseConfig.HistoryDepositCount {
			logs.Error("RedeemCodeActive 历史充值次数不足异常")
			return CodeError
		}
	}

	// 9. 检查兑换码是否已使用

	// 查询该兑换码已使用的次数
	// 使用 Gen 生成的查询方法替代原生 SQL
	redeemRecordTb := server.DaoxHashGame().XActiveRedeemcodeRecord
	redeemRecordDb := server.DaoxHashGame().XActiveRedeemcodeRecord.WithContext(context.Background())

	totalUsageCount, err := redeemRecordDb.
		Where(redeemRecordTb.RedeemCode.Eq(code)).
		Where(redeemRecordTb.ActiveID.Eq(int32(activeId))).
		Where(redeemRecordTb.SellerID.Eq(int32(sellerId))).
		Where(redeemRecordTb.ChannelID.Eq(int32(channelId))).
		Count()

	if err != nil {
		logs.Error("RedeemCodeActive 获取兑换码使用次数异常: %v", err)
		return CodeNotMatch
	}

	// 检查兑换码使用次数是否已达上限
	if matchedCodeItem.ExchangeCount != nil && *matchedCodeItem.ExchangeCount > 0 && int(totalUsageCount) >= *matchedCodeItem.ExchangeCount {
		logs.Error("RedeemCodeActive 兑换码已使用完")
		return CodeExhausted
	}

	// 查询当前用户是否已使用过该兑换码
	// 使用之前创建的 redeemRecordDb 和 redeemRecordTb
	usedByCurrentUser, err := redeemRecordDb.
		Where(redeemRecordTb.UserID.Eq(int32(token.UserId))).
		Where(redeemRecordTb.RedeemCode.Eq(code)).
		Where(redeemRecordTb.ActiveID.Eq(int32(activeId))).
		Where(redeemRecordTb.SellerID.Eq(int32(sellerId))).
		Where(redeemRecordTb.ChannelID.Eq(int32(channelId))).
		Count()

	if err != nil {
		logs.Error("RedeemCodeActive 获取用户使用兑换码次数异常: %v", err)
		return CodeExhausted
	}

	if usedByCurrentUser > 0 {
		logs.Error("RedeemCodeActive 当前用户已使用过该兑换码异常")
		return CodeUsed
	}

	// 10. 计算奖励金额
	// 如果配置了奖励金额范围，则在范围内随机生成奖励金额
	var rewardAmount float64
	if matchedCodeItem.MinRewardAmount > 0 && matchedCodeItem.MaxRewardAmount > 0 {
		// 生成随机奖励金额
		rand.Seed(time.Now().UnixNano())
		rewardAmount = matchedCodeItem.MinRewardAmount + rand.Float64()*(matchedCodeItem.MaxRewardAmount-matchedCodeItem.MinRewardAmount)
		// 保留两位小数
		rewardAmount = math.Floor(rewardAmount*100) / 100
	} else {
		// 如果没有配置奖励金额范围，则使用最小奖励金额
		rewardAmount = matchedCodeItem.MinRewardAmount
	}

	// 11. 计算流水要求
	flowMultiple := config.FlowMultiple
	if flowMultiple <= 0 {
		flowMultiple = 1 // 默认流水倍数为1
	}
	flowRequirement := rewardAmount * flowMultiple

	// 12. 保存奖励记录
	// 准备UseCount字段的值
	var useCountStr string
	if matchedCodeItem.ExchangeCount != nil && *matchedCodeItem.ExchangeCount > 0 {
		useCountStr = fmt.Sprintf("%d次", *matchedCodeItem.ExchangeCount)
	} else {
		useCountStr = "多次"
	}

	// 使用东八区时间（中国标准时间，CST，UTC+8）
	cst := time.FixedZone("CST", 8*3600)
	operationTime := time.Now().In(cst)

	// 打印时间，方便调试
	timeStr := operationTime.Format("2006-01-02 15:04:05")
	logs.Info("RedeemCodeActive 操作时间: %s 兑换码: %s, 奖励金额: %.2f, 流水要求: %.2f", timeStr, code, rewardAmount, flowRequirement)

	// 创建兑换码领取记录
	redeemRecord := &model.XActiveRedeemcodeRecord{
		SellerID:     int32(sellerId),
		ChannelID:    int32(channelId),
		UserID:       int32(token.UserId),
		RedeemCode:   code,
		UseCount:     useCountStr,
		RewardAmount: rewardAmount,
		ActiveID:     int32(activeId),
		ActiveName:   activeDefine.Title,
		RedeemTime:   operationTime,
		CreateTime:   operationTime,
	}

	// 使用 Gen 生成的方法插入记录
	err = redeemRecordDb.Create(redeemRecord)
	if err != nil {
		logs.Error("RedeemCodeActive 保存奖励记录 err: %v", err)
		return CodeError
	}

	// 13. 准备活动数据保存（SaveActiveData会自动处理余额更新和日志创建）

	// 构建活动数据保存信息
	configStr, _ := json.Marshal(config)
	baseConfigStr, _ := json.Marshal(baseConfig)
	minLiuShuiDecimal := decimal.NewFromFloat(flowRequirement)
	activeMemo := fmt.Sprintf("%d%s", activeDefine.ActiveId, activeDefine.Title)
	saveActiveDataInfo := SaveActiveDataInfo{
		AuditType:         int(activeDefine.AuditType), // 审核类型
		SellerID:          int32(sellerId),             // 运营商ID
		ChannelID:         int32(channelId),            // 渠道ID
		ActiveId:          int(activeId),               // 活动ID
		Level:             int(level),                  // 活动等级
		RealAmount:        rewardAmount,                // 实际奖励金额
		TotalLiushui:      0.0,                         // 总流水
		WithdrawLiuSuiAdd: flowRequirement,             // 提现流水增加值（使用流水要求）
		ActiveName:        activeDefine.Title,          // 活动名称
		ActiveMemo:        activeMemo,                  // 活动备注
		BalanceCReason:    utils.RedeemCodeGift_1003,   // 余额变更原因
		ConfigStr:         configStr,                   // 配置字符串
		BastConfigStr:     baseConfigStr,               // 基础配置字符串
		FirstRecharge:     0.0,                         // 首充金额（兑换码活动不涉及充值）
		TotalRecharge:     0.0,                         // 总充值金额
		MinLiuShui:        minLiuShuiDecimal,           // 总流水要求
		UserIP:            user.LoginIP,                // 用户IP
		DeviceID:          user.LoginDeviceID,          // 设备ID
		RedeemCode:        code,                        // 本次使用的兑换码
	}

	// 保存活动数据并发放奖励
	err = SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		logs.Error("RedeemCodeActive saveActiveData err: %v", err)
		return CodeNotMatch
	}

	logs.Info("RedeemCodeActive 兑换码奖励发放成功: userId=%d, amount=%.2f, flowRequirement=%.2f",
		token.UserId, rewardAmount, flowRequirement)
	// 发送兑换码奖励到账提醒消息
	endTime := time.Unix(activeDefine.EffectEndTime/1000, 0).In(cst)
	endTimeStr := endTime.Format("2006-01-02 15:04:05")
	endTimeMonthDay := endTime.Format("1月2日")
	redeem := map[string]interface{}{
		"{兑换码}":        code,
		"{到期时间-全格式}":   endTimeStr,      // - 样式：2008-08-08 18:28:38
		"{到期时间-月/日格式}": endTimeMonthDay, // - 样式：8月8日
		"{变动时间-全格式}":   operationTime,
	}
	messageService := msg.NewSendMessageAPIService()
	if err := messageService.SendAccountMessage("10", int(token.UserId), rewardAmount, "USDT", redeem); err != nil {
		logs.Error("发送兑换码奖励到账提醒失败: %v", err)
	}

	return CodeSuccess
}

// IsWidthdrawableActive 检查用户是否达到提现条件
// 参数:
//   - userId: 用户ID
//   - activeId: 活动ID
//
// 返回:
//   - bool: 是否可以提现 (true: 可以, false: 不可以)
//   - string: 错误信息 (如果不可以提现)
//   - decimal.Decimal: 流水差额 (如果流水不足，返回还需要的流水金额；如果流水充足，返回0)
func IsWidthdrawableActive(userId int, activeId int) (bool, string, decimal.Decimal) {
	// 2. 获取用户信息（运营商和渠道）
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	user, err := userDb.Where(userTb.UserID.Eq(int32(userId))).First()
	if err != nil {
		logs.Error("IsWidthdrawableActive 获取用户信息错误: %v", err)
		return true, "success", decimal.Zero // 查询出错，不阻止提现
	}

	// 3. 获取活动配置（根据运营商、渠道和活动ID查询）
	activeTb := server.DaoxHashGame().XActiveDefine
	activeDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	// 添加调试信息
	logs.Info("IsWidthdrawableActive 开始查询活动ID=%d 的配置, 用户ID=%d, 运营商=%d, 渠道=%d",
		activeId, userId, user.SellerID, user.ChannelID)

	activeDefine, err := activeDb.Where(activeTb.ActiveID.Eq(int32(activeId))).
		Where(activeTb.SellerID.Eq(user.SellerID)).
		Where(activeTb.ChannelID.Eq(user.ChannelID)).
		Where(activeTb.State.Eq(1)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 活动不存在，先查询所有活动看看是否有类似的
			allActives, queryErr := activeDb.Where(activeTb.ActiveID.Eq(int32(activeId))).Find()
			if queryErr == nil {
				logs.Info("IsWidthdrawableActive 当前数据库中活动ID=%d的所有配置:", activeId)
				for _, active := range allActives {
					logs.Info("主键ID=%d, 活动ID=%d, 运营商=%d, 渠道=%d, 活动名称=%s, 状态=%d",
						active.ID, active.ActiveID, active.SellerID, active.ChannelID, active.Title, active.State)
				}
			}
			logs.Info("IsWidthdrawableActive 活动ID=%d 在运营商=%d, 渠道=%d 下不存在或未开启，不阻止提现",
				activeId, user.SellerID, user.ChannelID)
			return true, "success", decimal.Zero
		}
		logs.Error("IsWidthdrawableActive 获取活动配置错误: %v", err)
		return true, "success", decimal.Zero // 查询出错，不阻止提现
	}

	// 添加调试信息
	logs.Info("IsWidthdrawableActive 找到活动: 主键ID=%d, 活动ID=%d, 运营商=%d, 渠道=%d, 活动名称=%s, 状态=%d",
		activeDefine.ID, activeDefine.ActiveID, activeDefine.SellerID, activeDefine.ChannelID, activeDefine.Title, activeDefine.State)

	// 4. 解析活动配置
	var baseConfig RedeemCodeBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseConfig)
	if err != nil {
		logs.Error("IsWidthdrawableActive 解析活动基础配置错误: %v", err)
		return false, "配置解析错误", decimal.Zero
	}

	var config RedeemCodeConfig
	err = json.Unmarshal([]byte(activeDefine.Config), &config)
	if err != nil {
		logs.Error("IsWidthdrawableActive 解析活动配置错误: %v", err)
		return false, "配置解析错误", decimal.Zero
	}

	// 5. 获取用户的奖励记录
	var rewardAmount float64

	// 使用 Gen 生成的查询方法替代原生 SQL
	redeemRecordTb := server.DaoxHashGame().XActiveRedeemcodeRecord
	redeemRecordDb := server.DaoxHashGame().XActiveRedeemcodeRecord.WithContext(context.Background())

	// 定义结果结构体
	type SumResult struct {
		Total float64
	}
	var sumResult SumResult

	// 执行查询
	err = redeemRecordDb.Select(redeemRecordTb.RewardAmount.Sum().As("Total")).
		Where(redeemRecordTb.SellerID.Eq(int32(user.SellerID))).
		Where(redeemRecordTb.ChannelID.Eq(int32(user.ChannelID))).
		Where(redeemRecordTb.UserID.Eq(int32(userId))).
		Where(redeemRecordTb.ActiveID.Eq(int32(activeId))).
		Scan(&sumResult)

	if err != nil {
		logs.Error("IsWidthdrawableActive 获取用户奖励记录错误: %v", err)
		return false, "查询奖励记录错误", decimal.Zero
	}

	rewardAmount = sumResult.Total

	// 如果用户没有奖励记录，直接返回true（不需要检查流水要求）
	if rewardAmount <= 0 {
		return true, "success", decimal.Zero
	}

	// 6. 计算流水要求
	flowMultiple := config.FlowMultiple
	if flowMultiple <= 0 {
		flowMultiple = 1 // 默认流水倍数为1
	}

	// 7. 检查用户是否达到提取奖励所需的流水要求
	requiredFlow := rewardAmount * flowMultiple

	// 获取活动期间的时间作为流水计算的时间范围
	// 使用东八区时间
	cst := time.FixedZone("CST", 8*3600)

	// 活动开始时间（毫秒转秒）
	var startTime time.Time
	if activeDefine.EffectStartTime > 0 {
		startTime = time.Unix(activeDefine.EffectStartTime/1000, 0).In(cst)
	} else {
		// 如果没有设置活动开始时间，无法确定流水计算范围，不阻止提现
		logs.Info("IsWidthdrawableActive 活动ID=%d 未设置开始时间，不阻止提现", activeId)
		return true, "success", decimal.Zero
	}

	// 活动结束时间（毫秒转秒），如果活动还在进行中，使用当前时间
	var endTime time.Time
	if activeDefine.EffectEndTime > 0 {
		activityEndTime := time.Unix(activeDefine.EffectEndTime/1000, 0).In(cst)
		currentTime := time.Now().In(cst)
		// 使用活动结束时间和当前时间中较早的那个
		if activityEndTime.Before(currentTime) {
			endTime = activityEndTime
		} else {
			endTime = currentTime
		}
	} else {
		// 如果没有设置活动结束时间，无法确定流水计算范围，不阻止提现
		logs.Info("IsWidthdrawableActive 活动ID=%d 未设置结束时间，不阻止提现", activeId)
		return true, "success", decimal.Zero
	}

	// 记录活动时间范围，方便调试
	logs.Info("IsWidthdrawableActive 活动时间范围: 开始时间=%s, 结束时间=%s",
		startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))

	// 使用专门的兑换码活动流水计算方法
	userFlow, err := CalculateRedeemCodeActivityFlow(userId, startTime, endTime)
	if err != nil {
		logs.Error("IsWidthdrawableActive 计算兑换码活动流水错误: %v", err)
		return false, "计算流水错误", decimal.Zero
	}

	// 检查流水是否达到要求
	isFlowEnough := userFlow >= requiredFlow

	// 8. 记录流水要求信息
	logs.Info("用户ID: %d, 奖励总额: %.2f, 当前流水: %.2f, 要求流水: %.2f, 是否达到要求: %v",
		userId, rewardAmount, userFlow, requiredFlow, isFlowEnough)

	if !isFlowEnough {
		// 计算差额并保留小数点后2位
		diff := requiredFlow - userFlow
		// 使用 decimal 进行精确计算
		requiredFlowDecimal := decimal.NewFromFloat(requiredFlow)
		userFlowDecimal := decimal.NewFromFloat(userFlow)
		diffDecimal := requiredFlowDecimal.Sub(userFlowDecimal)
		roundedDiff := diffDecimal.Round(2)

		logs.Info("IsWidthdrawableActive 流水不足! 需要流水=%.2f, 当前流水=%.2f, 差额=%.2f, 四舍五入后=%s",
			requiredFlow, userFlow, diff, roundedDiff.StringFixed(2))
		return false, "流水不足，还需要" + roundedDiff.StringFixed(2), roundedDiff
	}

	return true, "success", decimal.Zero
}

// CalculateRedeemCodeActivityFlow 计算用户在兑换码活动期间的总流水
// 参数:
//   - userId: 用户ID
//   - startTime: 活动开始时间
//   - endTime: 活动结束时间
//
// 返回:
//   - float64: 用户总流水
//   - error: 错误信息
func CalculateRedeemCodeActivityFlow(userId int, startTime time.Time, endTime time.Time) (float64, error) {
	var totalFlow float64

	// 0. 获取用户的运营商和渠道信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	user, err := userDb.Where(userTb.UserID.Eq(int32(userId))).First()
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 获取用户信息错误: %v", err)
		return 0, err
	}

	// 1. 查询哈希游戏流水 (x_order表)
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())

	type OrderFlowResult struct {
		TotalFlow float64
	}
	var orderFlow OrderFlowResult

	err = orderDb.Select(orderTb.ValidBetAmount.Sum().As("TotalFlow")).
		Where(orderTb.UserID.Eq(int32(userId))).
		Where(orderTb.SellerID.Eq(user.SellerID)).
		Where(orderTb.ChannelID.Eq(user.ChannelID)).
		Where(orderTb.CreateTime.Between(startTime, endTime)).
		Scan(&orderFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询哈希游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += orderFlow.TotalFlow

	// 2. 查询电子游戏流水 (x_third_dianzhi表)
	dianzhiTb := server.DaoxHashGame().XThirdDianzhi
	dianzhiDb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())

	type DianzhiFlowResult struct {
		TotalFlow float64
	}
	var dianzhiFlow DianzhiFlowResult

	err = dianzhiDb.Select(dianzhiTb.ValidBet.Sum().As("TotalFlow")).
		Where(dianzhiTb.UserID.Eq(int32(userId))).
		Where(dianzhiTb.SellerID.Eq(user.SellerID)).
		Where(dianzhiTb.ChannelID.Eq(user.ChannelID)).
		Where(dianzhiTb.CreateTime.Between(startTime, endTime)).
		Scan(&dianzhiFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询电子游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += dianzhiFlow.TotalFlow

	// 3. 查询真人游戏流水 (x_third_live表)
	liveTb := server.DaoxHashGame().XThirdLive
	liveDb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())

	type LiveFlowResult struct {
		TotalFlow float64
	}
	var liveFlow LiveFlowResult

	err = liveDb.Select(liveTb.ValidBet.Sum().As("TotalFlow")).
		Where(liveTb.UserID.Eq(int32(userId))).
		Where(liveTb.SellerID.Eq(user.SellerID)).
		Where(liveTb.ChannelID.Eq(user.ChannelID)).
		Where(liveTb.CreateTime.Between(startTime, endTime)).
		Scan(&liveFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询真人游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += liveFlow.TotalFlow

	// 4. 查询彩票游戏流水 (x_third_lottery表)
	lotteryTb := server.DaoxHashGame().XThirdLottery
	lotteryDb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())

	type LotteryFlowResult struct {
		TotalFlow float64
	}
	var lotteryFlow LotteryFlowResult

	err = lotteryDb.Select(lotteryTb.ValidBet.Sum().As("TotalFlow")).
		Where(lotteryTb.UserID.Eq(int32(userId))).
		Where(lotteryTb.SellerID.Eq(user.SellerID)).
		Where(lotteryTb.ChannelID.Eq(user.ChannelID)).
		Where(lotteryTb.CreateTime.Between(startTime, endTime)).
		Scan(&lotteryFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询彩票游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += lotteryFlow.TotalFlow

	// 5. 查询棋牌游戏流水 (x_third_qipai表)
	qipaiTb := server.DaoxHashGame().XThirdQipai
	qipaiDb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())

	type QipaiFlowResult struct {
		TotalFlow float64
	}
	var qipaiFlow QipaiFlowResult

	err = qipaiDb.Select(qipaiTb.ValidBet.Sum().As("TotalFlow")).
		Where(qipaiTb.UserID.Eq(int32(userId))).
		Where(qipaiTb.SellerID.Eq(user.SellerID)).
		Where(qipaiTb.ChannelID.Eq(user.ChannelID)).
		Where(qipaiTb.CreateTime.Between(startTime, endTime)).
		Scan(&qipaiFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询棋牌游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += qipaiFlow.TotalFlow

	// 6. 查询体育游戏流水 (x_third_sport表)
	sportTb := server.DaoxHashGame().XThirdSport
	sportDb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())

	type SportFlowResult struct {
		TotalFlow float64
	}
	var sportFlow SportFlowResult

	err = sportDb.Select(sportTb.ValidBet.Sum().As("TotalFlow")).
		Where(sportTb.UserID.Eq(int32(userId))).
		Where(sportTb.SellerID.Eq(user.SellerID)).
		Where(sportTb.ChannelID.Eq(user.ChannelID)).
		Where(sportTb.CreateTime.Between(startTime, endTime)).
		Scan(&sportFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询体育游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += sportFlow.TotalFlow

	// 7. 查询德州扑克游戏流水 (x_third_texas表)
	texasTb := server.DaoxHashGame().XThirdTexa
	texasDb := server.DaoxHashGame().XThirdTexa.WithContext(context.Background())

	type TexasFlowResult struct {
		TotalFlow float64
	}
	var texasFlow TexasFlowResult

	err = texasDb.Select(texasTb.ValidBet.Sum().As("TotalFlow")).
		Where(texasTb.UserID.Eq(int32(userId))).
		Where(texasTb.SellerID.Eq(user.SellerID)).
		Where(texasTb.ChannelID.Eq(user.ChannelID)).
		Where(texasTb.CreateTime.Between(startTime, endTime)).
		Scan(&texasFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询德州扑克游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += texasFlow.TotalFlow

	// 8. 查询趣味游戏流水 (x_third_quwei表)
	quweiTb := server.DaoxHashGame().XThirdQuwei
	quweiDb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())

	type QuweiFlowResult struct {
		TotalFlow float64
	}
	var quweiFlow QuweiFlowResult

	err = quweiDb.Select(quweiTb.ValidBet.Sum().As("TotalFlow")).
		Where(quweiTb.UserID.Eq(int32(userId))).
		Where(quweiTb.SellerID.Eq(user.SellerID)).
		Where(quweiTb.ChannelID.Eq(user.ChannelID)).
		Where(quweiTb.CreateTime.Between(startTime, endTime)).
		Scan(&quweiFlow)
	if err != nil {
		logs.Error("CalculateRedeemCodeActivityFlow 查询趣味游戏流水错误: %v", err)
		return 0, err
	}
	totalFlow += quweiFlow.TotalFlow

	// 记录详细的流水信息，方便调试
	logs.Info("CalculateRedeemCodeActivityFlow 用户ID=%d 活动期间流水详情: 哈希=%.2f, 电子=%.2f, 真人=%.2f, 彩票=%.2f, 棋牌=%.2f, 体育=%.2f, 德州=%.2f, 趣味=%.2f, 总计=%.2f",
		userId, orderFlow.TotalFlow, dianzhiFlow.TotalFlow, liveFlow.TotalFlow, lotteryFlow.TotalFlow,
		qipaiFlow.TotalFlow, sportFlow.TotalFlow, texasFlow.TotalFlow, quweiFlow.TotalFlow, totalFlow)

	return totalFlow, nil
}
