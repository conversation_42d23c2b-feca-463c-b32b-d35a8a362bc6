package single

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	_type "xserver/controller/third/type"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

type AgConfig struct {
	Agent                 string
	gciUrl                string
	giUrl                 string
	sessionUrl            string
	desKey                string
	md5Key                string
	currency              string
	productId             string
	oddType               string
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func NewAgLogic(params map[string]string, fc func(int) error) *AgConfig {

	return &AgConfig{
		Agent:                 params["agent"],
		gciUrl:                params["gci_url"],
		giUrl:                 params["gi_url"],
		sessionUrl:            params["session_url"],
		desKey:                params["deskey"],
		md5Key:                params["md5key"],
		currency:              params["currency"],
		productId:             params["productId"],
		oddType:               params["odd_type"],
		brandName:             "ag",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyAG = "cacheKeyAG:"

func (l *AgConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId string `validate:"required"`
		Lang   string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("ag 单一钱包 参数解析失败", err)
		ctx.RespErr(err, &errcode)
		return
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyAG, token.UserId); err != nil {
		logs.Error("ag 单一钱包 登录失败", err)
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, l.brandName, reqdata.GameId)
	if err != nil {
		logs.Error(l.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	loginname := l.Agent + "_8000_" + strconv.Itoa(token.UserId)
	var param = fmt.Sprintf("cagent=%s/\\\\/loginname=%s/\\\\/method=lg/\\\\/actype=1/\\\\/password=%s/\\\\/oddtype=%s/\\\\/cur=%s",
		l.Agent,
		loginname,
		loginname,
		l.oddType,
		l.currency)
	p, k := l.DesEncrypt(param)

	heard := map[string]string{
		"User-Agent": "WEB_LIB_GI_" + l.Agent,
	}
	urlPath := fmt.Sprintf("%s/doBusiness.do?params=%s&key=%s", l.giUrl, p, k)
	httpcClient := httpc.DoRequest{
		UrlPath: urlPath,
		Header:  heard,
	}
	data, err := httpcClient.DoGet()
	logs.Info("ag 单一钱包,发发送登录请求", "url=", urlPath, "请求=", param, " 响应=", string(data))
	if err != nil || data == nil {
		logs.Error("ag 单一钱包 CheckOrCreateGameAccout", err, string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	type checkOrCreateGameAccout struct {
		Info string `xml:"info,attr"`
		Msg  string `xml:"msg,attr"`
	}

	checkOrCreateGameAccoutResp := checkOrCreateGameAccout{}
	err = xml.Unmarshal(data, &checkOrCreateGameAccoutResp)
	if err != nil {
		logs.Error("checkOrCreateGameAccoutResp Unmarshal", err, string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	if checkOrCreateGameAccoutResp.Info != "0" { //日志
		logs.Error("checkOrCreateGameAccoutResp", string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	//sessionId
	sessionId := base.UserId2token(cacheKeyAG, token.UserId)

	_, balance, err := base.GetUserBalance(token.UserId, cacheKeyAG)
	balance = float64(int(balance*100)) / 100

	if err != nil {
		logs.Error("Ag 查询玩家余额失败", err, balance)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	httpcClient = httpc.DoRequest{
		UrlPath: fmt.Sprintf("%s/resource/player-tickets.ucs?productid=%s&username=%s&session_token=%s&credit=%f", l.sessionUrl, l.productId, loginname, sessionId, balance),
		Header:  heard,
	}
	data, err = httpcClient.DoGet()
	if err != nil || data == nil {
		logs.Error("ag 单一钱包 CheckOrCreateGameAccout", err, httpcClient.UrlPath, " 响应=", string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	type playerTicketResponse struct {
		ResponseCode string `xml:"ResponseCode"`
	}
	playerTicketResponseRes := playerTicketResponse{}
	err = xml.Unmarshal(data, &playerTicketResponseRes)
	if err != nil {
		logs.Error("ag 单一钱包 playerTicketResponseRes Unmarshal", err, httpcClient.UrlPath, " 响应=", string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	if playerTicketResponseRes.ResponseCode != "OK" {
		logs.Error("ag 单一钱包 playerTicketResponseRes", err, httpcClient.UrlPath, " 响应=", string(data))
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	sid := l.Agent + fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%d", time.Now().Unix()+int64(token.UserId)))))[0:15]
	param = fmt.Sprintf("cagent=%s/\\\\/loginname=%s/\\\\/actype=1/\\\\/password=%s/\\\\/dm=%s/\\\\/sid=%s/\\\\/lang=%s/\\\\/gameType=%s/\\\\/oddtype=%s/\\\\/cur=%s",
		l.Agent,
		loginname,
		loginname,
		"",
		sid,
		reqdata.Lang,
		reqdata.GameId,
		l.oddType,
		l.currency)
	p, k = l.DesEncrypt(param)
	ctx.Put("url", fmt.Sprintf("%s/forwardGame.do?params=%s&key=%s", l.gciUrl, p, k))
	ctx.RespOK()
}

func (l *AgConfig) Do(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info(l.brandName, "Do bodyBytes =================  := ", string(bodyBytes))
	response := `<?xml version=”1.0” encoding=”UTF-8” standalone=”yes”?><TransferResponse><ResponseCode>%s</ResponseCode><Balance>%f</Balance></TransferResponse>`
	if err != nil {
		response = fmt.Sprintf(response, "INVALID_TRANSACTION", 0.00)
		logs.Error("ag 单一钱包 Do ReadAll", err, response)
		ctx.Gin().Writer.Write([]byte(response))
		return
	}
	type Data struct {
		Record struct {
			TransactionType string `xml:"transactionType"`
			SessionToken    string `xml:"sessionToken"`
			AgentCode       string `xml:"agentCode"`
		}
	}
	result := Data{}
	err = xml.Unmarshal(bodyBytes, &result)
	if err != nil {
		response = fmt.Sprintf(response, "INVALID_TRANSACTION", 0.00)
		logs.Error("ag 单一钱包 Do ReadAll Unmarshal transactionType", err, response)
		ctx.Gin().Writer.Write([]byte(response))
		return
	}
	if result.Record.AgentCode != l.productId {
		response = fmt.Sprintf(response, "INVALID_DATA", 0.00)
		logs.Error("ag 单一钱包 Do ReadAll Unmarshal AgentCode", err, response)
		ctx.Gin().Writer.Write([]byte(response))
		return
	}

	userId := base.Token2UserId(cacheKeyAG, result.Record.SessionToken)

	if userId <= 0 {
		response = fmt.Sprintf(response, "INVALID_SESSION", 0.00)
		logs.Error("ag 单一钱包  Token2UserId", err, response)
		ctx.Gin().Writer.Write([]byte(response))
		return
	}

	udata, balance, err := base.GetUserBalance(userId, cacheKeyAG)
	if err != nil {
		response = fmt.Sprintf(response, "INCORRECT_SESSION_TYPE", 0.00)
		logs.Error("ag 单一钱包  GetUserBalance", err, response)
		ctx.Gin().Writer.Write([]byte(response))
		return
	}
	balance = float64(int(balance*100)) / 100

	var gameNames = map[string]string{
		"BAC":  "百家乐",
		"DT":   "龙虎",
		"SHB":  "骰宝",
		"ROU":  "轮盘",
		"NN":   "牛牛",
		"ZJH":  "炸金花",
		"BJ":   "21点",
		"SG":   "三公",
		"BF":   "斗牛",
		"CBAC": "包桌百家乐",
		"LBAC": "竞咪百家乐",
	}

	switch result.Record.TransactionType {
	case "BET":
		type Data struct {
			Record struct {
				SessionToken    string  `xml:"sessionToken"`
				Currency        string  `xml:"currency"`
				Value           float64 `xml:"value"`
				AgentCode       string  `xml:"agentCode"`
				BetTime         string  `xml:"betTime"`
				TransactionID   string  `xml:"transactionID"`
				PlatformType    string  `xml:"platformType"`
				Round           string  `xml:"round"`
				Gametype        string  `xml:"gametype"`
				GameCode        string  `xml:"gameCode"`
				TableCode       string  `xml:"tableCode"`
				TransactionType string  `xml:"transactionType"`
				TransactionCode string  `xml:"transactionCode"`
				DeviceType      string  `xml:"deviceType"`
				Playtype        string  `xml:"playtype"`
			}
		}
		resultBet := Data{}
		err = xml.Unmarshal(bodyBytes, &resultBet)
		if err != nil {
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", 0.00)
			logs.Error("ag 单一钱包 Do ReadAll Unmarshal resultBet", err, response)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		reqDataByte, _ := json.Marshal(resultBet.Record)

		//三方来源的数据整理
		var (
			betAmount = resultBet.Record.Value
			thirdId   = resultBet.Record.TransactionID
			//gamecode  = resultBet.Record.TableCode
			gameName  = gameNames[resultBet.Record.Gametype]
			thirdTime = time.Now()
		)

		// 检查游戏权限
		if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, resultBet.Record.TableCode); err != nil {
			logs.Error("ag 单一钱包 BET 权限检查错误 userId=", userId, " gameId=", resultBet.Record.TableCode, " err=", err.Error())
			response = fmt.Sprintf(response, "INVALID_SESSION", balance)
			ctx.Gin().Writer.Write([]byte(response))
			return
		} else if !allowed {
			logs.Error("ag 单一钱包 BET 权限被拒绝 userId=", userId, " gameId=", resultBet.Record.TableCode, " hint=", hint)
			response = fmt.Sprintf(response, "INVALID_SESSION", balance)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		if balance < betAmount {
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			logs.Error("ag 单一钱包  余额不足", balance, resultBet.Record.Value)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}
		//查询订单是否存在
		betTran, isExist := base.OrderIsExist(resultBet.Record.TransactionID, "ag", "x_third_live_pre_order")
		if isExist {
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			logs.Error("ag 单一钱包  下注订单已经存在了", response)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

		//开启事务
		server.XDb().Transaction(func(tx *xgo.XTx) error {
			ressql, err2 := tx.Db.Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmount, userId, betAmount)
			row, err3 := ressql.RowsAffected()
			if err2 != nil || err3 != nil || row < 1 {
				response = fmt.Sprintf(response, "INSUFFICIENT_FUNDS", balance)
				logs.Error("ag 单一钱包  开启事务 修改x_user失败了", response)
				ctx.Gin().Writer.Write([]byte(response))

				return errors.New("修改x_user失败了")
			}
			afterBalance := balance - betAmount
			if betTran != nil {
				betId := abugo.GetInt64FromInterface((*betTran)["Id"])
				_, err = tx.Db.Exec(`update x_third_live_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmount, betId)
				if err != nil {
					response = fmt.Sprintf(response, "ERROR", balance)
					logs.Error("ag 单一钱包  开启事务 betTran 存在的情况", response)
					ctx.Gin().Writer.Write([]byte(response))
					return err
				}
			} else {
				order := xgo.H{
					"SellerId":     (*udata)["SellerId"],
					"ChannelId":    (*udata)["ChannelId"],
					"BetChannelId": ChannelId,
					"UserId":       userId,
					"Brand":        "ag",
					"ThirdId":      thirdId,
					"GameId":       resultBet.Record.TableCode,
					"GameName":     gameName,
					"BetAmount":    betAmount,
					"WinAmount":    0,
					"ValidBet":     0,
					"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
					"Currency":     l.currency,
					"RawData":      string(reqDataByte),
					"DataState":    -1,
				}
				_, err = tx.Table("x_third_live_pre_order").Insert(order)
				if err != nil {
					logs.Debug("ag 下注 修改x_third_live_pre_order新增失败了:  id = ", thirdId, err)
					response = fmt.Sprintf(response, "ERROR", balance)
					logs.Error("ag 单一钱包  开启事务 ERROR 存在的情况", response)
					ctx.Gin().Writer.Write([]byte(response))
					return err
				}
			}

			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance,
				"Amount":       0 - betAmount,
				"AfterAmount":  afterBalance,
				"Reason":       utils.BalanceCReasonAGBet,
				"Memo":         "ag bet,thirdId:" + thirdId + ",局号:" + resultBet.Record.GameCode,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				response = fmt.Sprintf(response, "ERROR", balance)
				logs.Debug("ag 下注 新增x_amount_change_log失败了:  id = ", thirdId, err, response)
				ctx.Gin().Writer.Write([]byte(response))
				return err
			}

			response = fmt.Sprintf(response, "OK", afterBalance)
			logs.Error("ag 单一钱包  下注成功", balance, resultBet.Record.Value, afterBalance)
			ctx.Gin().Writer.Write([]byte(response))
			return nil
		})
		// 推送下注事件通知
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency, l.brandName, thirdId, 5)
		}
		break
	case "WIN", "LOSE":
		type Data struct {
			Record struct {
				SessionToken   string   `xml:"sessionToken"`
				Currency       string   `xml:"currency"`
				NetAmount      float64  `xml:"netAmount"`
				ValidBetAmount float64  `xml:"validBetAmount"`
				Playname       string   `xml:"playname"`
				AgentCode      string   `xml:"agentCode"`
				Settletime     string   `xml:"settletime"`
				BillNo         string   `xml:"billNo"`
				TransactionID  []string `xml:"transactionID"`
				Gametype       string   `xml:"gametype"`
				GameCode       string   `xml:"gameCode"`
				//TableCode       string   `xml:"tableCode"`
				TransactionType string `xml:"transactionType"`
				TransactionCode string `xml:"transactionCode"`
				TicketStatus    string `xml:"ticketStatus"`
				GameResult      string `xml:"gameResult"`
				Finish          string `xml:"finish"`
				PlayType        string `xml:"playtype"`
			}
		}
		resultSettle := Data{}
		err = xml.Unmarshal(bodyBytes, &resultSettle)
		if err != nil {
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			logs.Error("ag 单一钱包 Do ReadAll Unmarshal resultSettle", err, response)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		reqDataByte, _ := json.Marshal(resultSettle.Record)

		//三方来源的数据整理
		var (
			amount    = resultSettle.Record.NetAmount
			betAmount = resultSettle.Record.ValidBetAmount
			winAmount = amount + betAmount
			billNo    = resultSettle.Record.BillNo
			gamecode  = resultSettle.Record.Gametype
			gameName  = gameNames[gamecode]
			thirdTime = time.Now()
			roundCode = resultSettle.Record.GameCode //记录局号 方便对账排查问题
		)

		// 转换playType
		betCxt := ""
		agPlayType := _type.NewAgType().PlayType
		type RawData struct {
			Gametype string
			Playtype string `json:"playtype"`
		}

		if resultSettle.Record.Gametype == "ROU" {
			playTypes, ok := agPlayType[resultSettle.Record.Gametype]
			if !ok {
				betCxt = "未知投注类型"
			} else {
				betCxt, ok = playTypes.(map[string]string)[resultSettle.Record.PlayType]
				if !ok {
					betCxt = "未知投注类型"
				}
			}
		} else {
			for _, v := range resultSettle.Record.TransactionID {
				// 查询订单是否存在
				//betTran, isExist := base.OrderIsExist(v, "ag", "x_third_live_pre_order")
				where := abugo.AbuDbWhere{}
				where.Add("and", "ThirdId", "=", v, nil)
				where.Add("and", "Brand", "=", "ag", nil)
				betTran, _ := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()

				if betTran != nil {
					// 获取RawData中的playType
					rawData := RawData{}
					if err := json.Unmarshal([]byte((*betTran)["RawData"].(string)), &rawData); err != nil {
						logs.Error("ag 结算 playType Unmarshal", err)
						response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
						ctx.Gin().Writer.Write([]byte(response))
						return
					}

					playTypes, ok := agPlayType[rawData.Gametype]
					if !ok {
						betCxt = "未知投注类型"
					} else {
						betCxt, ok = playTypes.(map[string]string)[rawData.Playtype]
						if !ok {
							betCxt = "未知投注类型"
						}
					}
				}
			}
		}

		logs.Info("ag 投注详情：", betCxt)

		//开启事务
		server.XDb().Transaction(func(tx *xgo.XTx) error {

			//当不是轮盘时 修改一下中间表
			if resultSettle.Record.Gametype != "ROU" {
				for _, v := range resultSettle.Record.TransactionID {
					_, err = tx.Db.Exec(`update x_third_live_pre_order set DataState = 1 where ThirdId = ?`, v)
				}
			}

			// 获取中间表数据
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", resultSettle.Record.TransactionID[0], nil)
			where.Add("and", "Brand", "=", "ag", nil)
			betTran, _ := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()

			// 除了体育所有的有效流水取不大于下注金额的输赢绝对值
			validBet := math.Abs(winAmount - betAmount)
			if validBet > math.Abs(betAmount) {
				validBet = math.Abs(betAmount)
			}
			//直接进入统计表
			order := xgo.H{
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
				"BetChannelId": (*betTran)["BetChannelId"].(int64),
				"UserId":       userId,
				"Brand":        "ag",
				"ThirdId":      billNo,
				"GameId":       (*betTran)["GameId"].(string),
				"GameName":     gameName,
				"BetAmount":    betAmount,
				"WinAmount":    winAmount,
				"ValidBet":     validBet,
				"ThirdTime":    thirdTime.In(tzUTC8).Format("2006-01-02 15:04:05"),
				"Currency":     l.currency,
				"RawData":      string(reqDataByte),
				"DataState":    1,
				"GameRst":      resultSettle.Record.GameResult,
				"BetCtx":       betCxt,
			}
			_, err = tx.Table("x_third_live").Insert(order)

			if err != nil {
				logs.Debug("ag 结算 计入统计表 x_third_live 失败了:  id = ", billNo, err)
				response = fmt.Sprintf(response, "ERROR", balance)
				ctx.Gin().Writer.Write([]byte(response))
				return err
			}
			//处理结算
			if winAmount > 0 {
				//win
				ressql, err2 := tx.Db.Exec("update x_user set amount = amount + ? where UserId = ?", winAmount, userId)
				row, err3 := ressql.RowsAffected()
				if err2 != nil || err3 != nil || row < 1 {
					logs.Debug("ag 结算 处理结算 x_user 失败了:  id = ", billNo, err2, err3)
					response = fmt.Sprintf(response, "ERROR", balance)
					ctx.Gin().Writer.Write([]byte(response))
					return errors.New("修改x_user失败了")
				}
				amountLog := xgo.H{
					"UserId":       userId,
					"BeforeAmount": balance,
					"Amount":       winAmount,
					"AfterAmount":  balance + winAmount,
					"Reason":       utils.BalanceCReasonAGSettle,
					"Memo":         "ag settle,thirdId:" + billNo + ",局号:" + roundCode,
					"SellerId":     (*udata)["SellerId"],
					"ChannelId":    (*udata)["ChannelId"],
				}
				_, err = tx.Table("x_amount_change_log").Insert(amountLog)
				if err != nil {
					logs.Debug("ag 结算 处理结算 x_amount_change_log 失败了:  id = ", billNo, err)
					response = fmt.Sprintf(response, "ERROR", balance)
					ctx.Gin().Writer.Write([]byte(response))
					return err
				}
			}
			logs.Debug("ag sw :", "結算成功")
			response = fmt.Sprintf(response, "OK", balance+winAmount)
			ctx.Gin().Writer.Write([]byte(response))
			return nil
		})
		// 推送派奖事件通知
		if l.thirdGamePush != nil {
			//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, betAmount, winAmount, l.currency)
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(5, l.brandName, billNo)
		}
		break
	case "REFUND":
		type Data struct {
			Record struct {
				TicketStatus    string  `xml:"ticketStatus"`
				SessionToken    string  `xml:"sessionToken"`
				Currency        string  `xml:"currency"`
				Value           float64 `xml:"value"`
				Playname        string  `xml:"playname"`
				AgentCode       string  `xml:"agentCode"`
				BetTime         string  `xml:"betTime"`
				TransactionID   string  `xml:"transactionID"`
				BillNo          string  `xml:"billNo"`
				PlatformType    string  `xml:"platformType"`
				Round           string  `xml:"round"`
				Gametype        string  `xml:"gametype"`
				GameCode        string  `xml:"gameCode"`
				TableCode       string  `xml:"tableCode"`
				TransactionType string  `xml:"transactionType"`
				TransactionCode string  `xml:"transactionCode"`
				Playtype        string  `xml:"playtype"`
			}
		}
		resultRefund := Data{}
		err = xml.Unmarshal(bodyBytes, &resultRefund)
		if err != nil {
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			logs.Error("ag 单一钱包 Do ReadAll Unmarshal resultRefund", err, response)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		reqDataByte, _ := json.Marshal(resultRefund.Record)

		//三方来源的数据整理
		var (
			amount  = resultRefund.Record.Value
			thirdId = resultRefund.Record.TransactionID
		)

		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", thirdId, nil)
		where.Add("and", "Brand", "=", "ag", nil)
		betTran, err := server.Db().Table("x_third_live_pre_order").Where(where).GetOne()
		if betTran == nil || err != nil {
			//订单不存在
			logs.Debug("ag 订单不存在: id = ", thirdId)
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
		if dataState == -2 {
			//已经取消了
			logs.Debug("ag 订单已经取消了:  id = ", thirdId)
			response = fmt.Sprintf(response, "INVALID_TRANSACTION", balance)
			ctx.Gin().Writer.Write([]byte(response))
			return
		}

		//开启事务
		server.XDb().Transaction(func(tx *xgo.XTx) error {
			//修改成取消了
			betId := abugo.GetInt64FromInterface((*betTran)["Id"])
			_, err = tx.Db.Exec(`update x_third_live_pre_order set
		RawData = ?,
		DataState = -2
		where Id = ?`,
				string(reqDataByte),
				betId,
			)
			if err != nil {
				logs.Debug("ag 订单取消 修改x_third_live_pre_order状态失败了:  id = ", thirdId, err)
				response = fmt.Sprintf(response, "ERROR", balance)
				ctx.Gin().Writer.Write([]byte(response))
				return err
			}

			ressql, err2 := tx.Db.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
			row, err3 := ressql.RowsAffected()
			if err2 != nil || err3 != nil || row < 1 {
				logs.Debug("ag 订单取消 操作金币上分失败了:  id = ", thirdId, err2, err3)
				response = fmt.Sprintf(response, "ERROR", balance)
				ctx.Gin().Writer.Write([]byte(response))
				return errors.New("修改x_user失败了")
			}

			amountLog := xgo.H{
				"UserId":       userId,
				"BeforeAmount": balance,
				"Amount":       amount,
				"AfterAmount":  balance + amount,
				"Reason":       utils.BalanceCReasonAGREFUND,
				"Memo":         "ag cancel,thirdId:" + thirdId + ",局号:" + resultRefund.Record.GameCode,
				"SellerId":     (*udata)["SellerId"],
				"ChannelId":    (*udata)["ChannelId"],
			}
			_, err = tx.Table("x_amount_change_log").Insert(amountLog)
			if err != nil {
				logs.Debug("ag 订单取消 操作金币日志失败了:  id = ", thirdId, err)
				response = fmt.Sprintf(response, "ERROR", balance)
				ctx.Gin().Writer.Write([]byte(response))
				return err
			}
			logs.Debug("ag 订单取消成功了:  id = ", thirdId, err)
			response = fmt.Sprintf(response, "OK", balance+amount)
			ctx.Gin().Writer.Write([]byte(response))

			return nil
		})

		break
	default:
		response = fmt.Sprintf(response, "INVALID_DATA", balance)
		ctx.Gin().Writer.Write([]byte(response))
		break
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][AgConfig] Do 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][AgConfig] Do 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func (l *AgConfig) DesEncrypt(param string) (string, string) {
	if result, err := l.DesCBCEncrypt([]byte(param), []byte(l.desKey), []byte(l.desKey)); err == nil {
		p := strings.ToUpper(hex.EncodeToString(result))
		return p, fmt.Sprintf("%x", md5.Sum([]byte(p+l.md5Key)))
	} else {
		return "", ""
	}
}

func (l *AgConfig) DesCBCEncrypt(data, key, iv []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}

	data = l.pkcs5Padding(data, block.BlockSize())
	cryptText := make([]byte, len(data))

	blockMode := cipher.NewCBCEncrypter(block, iv)
	blockMode.CryptBlocks(cryptText, data)
	return cryptText, nil
}

func (l *AgConfig) pkcs5Padding(cipherText []byte, blockSize int) []byte {
	padding := blockSize - len(cipherText)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(cipherText, padText...)
}
