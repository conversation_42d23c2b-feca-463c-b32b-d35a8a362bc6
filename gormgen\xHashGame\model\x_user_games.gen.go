// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserGames = "x_user_games"

// XUserGames mapped from table <x_user_games>
type XUserGames struct {
	ID         int32     `gorm:"column:Id;primaryKey;autoIncrement:true" json:"Id"`
	UserID     int32     `gorm:"column:UserId;not null;comment:用户ID" json:"UserId"`                                        // 用户ID
	Brand      string    `gorm:"column:Brand;not null;comment:游戏品牌" json:"Brand"`                                         // 游戏品牌
	GameID     string    `gorm:"column:GameId;not null;comment:游戏ID" json:"GameId"`                                       // 游戏ID
	CreateTime time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`    // 创建时间
	UpdateTime time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`    // 更新时间
}

// TableName XUserGames's table name
func (*XUserGames) TableName() string {
	return TableNameXUserGames
}
