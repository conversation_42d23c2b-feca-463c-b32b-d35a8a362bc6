package abugo

import (
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
)

// SQLInjectionMiddleware 是 一个防止SQL注入攻击的中间件
func SQLInjectionMiddleware(next AbuHttpHandler) AbuHttpHandler {
	return func(ctx *AbuHttpContent) {
		// 获取原始请求体
		rawBody := ctx.GetRawBody()
		if len(rawBody) > 0 {
			// 检查请求体中是否存在SQL注入攻击模式
			if containsSQLInjection(string(rawBody)) {
				ctx.RespJsonWithCode(http.StatusBadRequest, "Invalid query parameters 非法sql", nil)
				return
			}

			// 尝试解析JSON并清理参数
			var requestMap map[string]interface{}
			if err := json.Unmarshal(rawBody, &requestMap); err == nil {
				// 清理所有字符串类型的参数
				cleanRequestParams(requestMap)

				// 将清理后的数据重新设置到请求上下文中
				if cleanedJSON, err := json.Marshal(requestMap); err == nil {
					ctx.SetRawBody(cleanedJSON)
				}
			}
		}

		// 检查URL查询参数
		queryValues := ctx.Gin().Request.URL.Query()
		for key, values := range queryValues {
			for i, value := range values {
				if containsSQLInjection(value) {
					ctx.RespJsonWithCode(http.StatusBadRequest, "Invalid query parameters 非法sql", nil)
					return
				}
				queryValues[key][i] = sanitizeInput(value)
			}
		}

		// 继续处理请求
		next(ctx)
	}
}

// cleanRequestParams  递归清理请求参数
func cleanRequestParams(params map[string]interface{}) {
	for key, value := range params {
		switch v := value.(type) {
		case string:
			// 清理字符串参数
			params[key] = sanitizeInput(v)
		case map[string]interface{}:
			// 递归清理嵌套对象
			cleanRequestParams(v)
		case []interface{}:
			// 清理数组
			for i, item := range v {
				if strItem, ok := item.(string); ok {
					v[i] = sanitizeInput(strItem)
				} else if mapItem, ok := item.(map[string]interface{}); ok {
					cleanRequestParams(mapItem)
				}
			}
		}
	}
}

// containsSQLInjection 检查输入是否包含SQL注入或其他安全攻击模式
func containsSQLInjection(input string) bool {
	// 预处理输入，替换一些常见的分隔符
	// 将//替换为空格，以便检测如"1//and//3=DBMS_PIPE.RECEIVE_MESSAGE"这样的模式
	input = strings.ReplaceAll(input, "//", " ")

	// 检测路径遍历攻击 - 使用更精确的检测方式
	// 仅在值是字符串且包含敏感路径时检测
	// 使用正则表达式进行更精确的匹配
	pathTraversalPatterns := []string{
		// 敏感系统文件
		`(?i)(/|\\)etc(/|\\)passwd`,
		`(?i)(/|\\)etc(/|\\)shadow`,
		`(?i)(/|\\)proc(/|\\)(self|[0-9]+)`,
		`(?i)(/|\\)var(/|\\)log`,
		// Windows系统文件 - 使用更精确的模式
		`(?i)([A-Z]:\\Windows)`,
		`(?i)([A-Z]:\\boot\.ini)`,
		// 目录遍历模式 - 使用更精确的模式
		// 仅匹配可能的攻击模式，如多个连续的../或..\\
		`(?i)(\.\.(\\|/)){2,}`,     // 两个或更多的../或..\\
		`(?i)(\\|/)\.\.(\\/|)\.\.`, // /../../或\\..\\..
	}

	// 检测路径遍历模式
	for _, pattern := range pathTraversalPatterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}

	// SQL注入检测正则表达式
	patterns := []string{
		`(?i)(\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION|CREATE|TRUNCATE)(\s|$)`,
		`(?i)(\s|^)(OR|AND)(\s+)('|")?[0-9]+('|")?(\s*)=(\s*)('|")?[0-9]+('|")?`,
		`(?i)(\s|^|=)(DBMS_PIPE\.RECEIVE_MESSAGE)`, // 增加=前缀以检测如"3=DBMS_PIPE"
		`(?i)(\s|^)(SLEEP\([0-9]+\))`,
		`(?i)(\s|^)(BENCHMARK\([0-9]+,)`,
		`(?i)(\s|^)(WAITFOR DELAY)`,
		`(?i)(\s|^)(--|\#|\*\/|\/)`,
		`(?i)(\s|^)(;)(\s|$)`,
		// 检测分割的SQL关键字，如S/**/E/**/L/**/E/**/C/**/T
		`(?i)S\s*E\s*L\s*E\s*C\s*T`,
		`(?i)I\s*N\s*S\s*E\s*R\s*T`,
		`(?i)U\s*P\s*D\s*A\s*T\s*E`,
		`(?i)D\s*E\s*L\s*E\s*T\s*E`,
		`(?i)D\s*R\s*O\s*P`,
		`(?i)A\s*L\s*T\s*E\s*R`,
		`(?i)U\s*N\s*I\s*O\s*N`,
		`(?i)C\s*R\s*E\s*A\s*T\s*E`,
		`(?i)T\s*R\s*U\s*N\s*C\s*A\s*T\s*E`,
		`(?i)F\s*R\s*O\s*M`,
		// 检测分割的条件操作符
		`(?i)O\s*R\s+.*?=`,
		`(?i)A\s*N\s*D\s+.*?=`,
		// 直接检测常见的SQL关键字组合
		`(?i)\band\b.*?=`, // 检测如"and 3=DBMS_PIPE"
		`(?i)\bor\b.*?=`,  // 检测OR条件
		// 检测SQL注释
		`(?i)/\*.*?\*/`,
		// 检测常见的时间延迟函数
		`(?i)\bRECEIVE_MESSAGE\b`,
		`(?i)\bpg_sleep\b`,
		`(?i)\bwaitfor\b`,
	}

	for _, pattern := range patterns {
		matched, _ := regexp.MatchString(pattern, input)
		if matched {
			return true
		}
	}

	return false
}

// sanitizeInput 净化输入字符串，移除可能的SQL注入字符
func sanitizeInput(input string) string {
	// 首先移除所有SQL注释
	commentPattern := regexp.MustCompile(`(?i)/\*.*?\*/`)
	result := commentPattern.ReplaceAllString(input, "")

	// 移除或替换危险字符
	replacements := map[string]string{
		"'":       "",
		"\"":      "",
		";":       "",
		"--":      "",
		"/*":      "",
		"*/":      "",
		"#":       "",
		"=":       "",
		"OR":      "",
		"AND":     "",
		"SELECT":  "",
		"INSERT":  "",
		"UPDATE":  "",
		"DELETE":  "",
		"DROP":    "",
		"UNION":   "",
		"FROM":    "",
		"WHERE":   "",
		"HAVING":  "",
		"GROUP":   "",
		"ORDER":   "",
		"INTO":    "",
		"EXEC":    "",
		"EXECUTE": "",
	}

	for old, new := range replacements {
		// 忽略大小写替换
		pattern := "(?i)" + regexp.QuoteMeta(old)
		re := regexp.MustCompile(pattern)
		result = re.ReplaceAllString(result, new)
	}

	// 处理分割的SQL关键字
	// 先将所有空白字符压缩为一个空格
	whitespacePattern := regexp.MustCompile(`\s+`)
	result = whitespacePattern.ReplaceAllString(result, " ")

	// 检测并移除分割的关键字
	splitKeywords := []string{
		"S E L E C T",
		"I N S E R T",
		"U P D A T E",
		"D E L E T E",
		"F R O M",
		"W H E R E",
		"O R",
		"A N D",
	}

	for _, keyword := range splitKeywords {
		pattern := "(?i)" + keyword
		re := regexp.MustCompile(pattern)
		result = re.ReplaceAllString(result, "")
	}

	return result
}
