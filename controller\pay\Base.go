package paycontroller

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"
	"xserver/controller"
	"xserver/controller/active"
	"xserver/controller/datapush"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/go-resty/resty/v2"
	"github.com/zhms/xgo/xgo"
	"golang.org/x/exp/rand"
)

type Base struct {
}

// GetLanguage 获取语言标签对应的本地化名称
// @description 根据输入的语言标签返回对应的本地化语言名称
// @param tag 语言标签，支持以下格式：
//   - 完整格式：如 "zh-CN"（中文简体）、"en-US"（美式英语）
//   - 简短格式：如 "zh"（中文）、"en"（英语）
//   - 国家/地区代码：如 "CN"（返回"zh-CN"）、"GB"（返回"en-GB"）
//
// @return 返回对应的本地化语言名称：
//   - 如果在映射表中找到完整格式的匹配，返回对应的本地化名称
//   - 如果找不到完整格式，会尝试匹配简短格式
//   - 如果都找不到匹配，则返回原始标签
func (c *Base) GetLanguage(tag string) string {
	// 国家/地区代码到语言标签的映射
	countryToLang := map[string]string{
		// 英语区域
		"SG": "en-SG",
		"GB": "en-GB",
		"US": "en-US",
		"AU": "en-AU",
		"CA": "en-CA", // 加拿大默认使用英语
		"IE": "en-IE",
		"NZ": "en-NZ",

		// 中文区域
		"CN": "zh-CN",
		"TW": "zh-TW",
		"HK": "zh-HK",

		// 法语区域
		"FR": "fr-FR",
		"BE": "fr-BE",

		// 德语区域
		"DE": "de-DE",
		"AT": "de-AT",
		"LI": "de-LI",
		"CH": "de-CH", // 瑞士默认使用德语

		// 意大利语区域
		"IT": "it-IT",

		// 西班牙语区域
		"ES": "es-ES",
		"MX": "es-US",

		// 葡萄牙语区域
		"PT": "pt-PT",
		"BR": "pt-BR",

		// 孟加拉语区域
		"BD": "bn-BD",

		// 克罗地亚语区域
		"HR": "hr-HR",

		// 捷克语区域
		"CZ": "cs-CZ",

		// 丹麦语区域
		"DK": "da-DK",

		// 希腊语区域
		"GR": "el-GR",

		// 希伯来语区域
		"IL": "he-IL", // 以色列默认使用希伯来语

		// 匈牙利语区域
		"HU": "hu-HU",

		// 印度尼西亚语区域
		"ID": "in-ID",

		// 日语区域
		"JP": "ja-JP",

		// 韩语区域
		"KR": "ko-KR",

		// 马来语区域
		"MY": "ms-MY",

		// 波斯语区域
		"IR": "fa-IR",

		// 波兰语区域
		"PL": "pl-PL",

		// 罗马尼亚语区域
		"RO": "ro-RO",

		// 俄语区域
		"RU": "ru-RU",

		// 塞尔维亚语区域
		"RS": "sr-RS",

		// 瑞典语区域
		"SE": "sv-SE",

		// 泰语区域
		"TH": "th-TH",

		// 土耳其语区域
		"TR": "tr-TR",

		// 乌尔都语区域
		"PK": "ur-PK",

		// 越南语区域
		"VN": "vi-VN",

		// 拉脱维亚语区域
		"LV": "lv-LV",

		// 立陶宛语区域
		"LT": "lt-LT",

		// 挪威语区域
		"NO": "nb-NO",

		// 斯洛伐克语区域
		"SK": "sk-SK",

		// 斯洛文尼亚语区域
		"SI": "sl-SI",

		// 保加利亚语区域
		"BG": "bg-BG",

		// 乌克兰语区域
		"UA": "uk-UA",

		// 菲律宾语区域
		"PH": "tl-PH",

		// 芬兰语区域
		"FI": "fi-FI",

		// 南非语区域
		"ZA": "af-ZA",

		// 缅甸语区域
		"MM": "my-MM",
		"ZG": "my-ZG",

		// 柬埔寨语区域
		"KH": "km-KH",

		// 阿姆哈拉语区域
		"ET": "am-ET",

		// 白俄罗斯语区域
		"BY": "be-BY",

		// 爱沙尼亚语区域
		"EE": "et-EE",

		// 斯瓦希里语区域
		"TZ": "sw-TZ",

		// 阿塞拜疆语区域
		"AZ": "az-AZ",

		// 亚美尼亚语区域
		"AM": "hy-AM",

		// 格鲁吉亚语区域
		"GE": "ka-GE",

		// 老挝语区域
		"LA": "lo-LA",

		// 蒙古语区域
		"MN": "mn-MN",

		// 尼泊尔语区域
		"NP": "ne-NP",

		// 哈萨克语区域
		"KZ": "kk-KZ",

		// 冰岛语区域
		"IS": "is-rIS",

		// 吉尔吉斯语区域
		"KG": "ky-rKG",

		// 马其顿语区域
		"MK": "mk-rMK",

		// 乌兹别克语区域
		"UZ": "uz-rUZ",

		// 僧伽罗语区域
		"LK": "si-LK",

		// 阿拉伯语区域
		"SA": "ar",
		"EG": "ar-EG",
		"AE": "ar",

		// 印度多语言（默认使用英语）
		"IN": "en-IN",
	}

	// 如果是国家/地区代码，直接返回对应的语言标签
	if langTag, exists := countryToLang[tag]; exists {
		return langTag
	}
	return ""
}

func (c *Base) getPayMethod(id int) (*model.XFinanceMethod, error) {
	// 获取支付配置信息
	dao := server.DaoxHashGame().XFinanceMethod
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(id))).First()
	if err != nil {
		return nil, err
	}

	return res, err
}

func (c *Base) getRechargeRate(symbol string, sellerId int32) (float64, error) {
	dao := server.DaoxHashGame().XFinanceSymbol
	db := dao.WithContext(context.Background())
	res, err := db.Where(dao.Symbol.Eq(symbol)).Where(dao.SellerID.Eq(sellerId)).First()
	if err != nil {
		logs.Error("getRechargeRate - 查询汇率配置失败: Symbol=%v, SellerId=%v, Error=%v", symbol, sellerId, err)
		return 0.00, err
	}
	if res == nil {
		logs.Error("getRechargeRate - 未找到汇率配置: Symbol=%v, SellerId=%v", symbol, sellerId)
		return 0.00, errors.New("未找到汇率配置")
	}

	rate := 0.00
	if res.AutoState == 1 {
		rate = res.RechargeRate
	} else {
		rate = res.RechargeRateEx
	}

	if res.RechargeFixType == 1 {
		rate += res.RechargeFix
	} else {
		rate += rate * res.RechargeFix
	}

	return rate, nil
}

func (c *Base) getWithdrawRate(symbol string, sellerId int32) (float64, error) {
	dao := server.DaoxHashGame().XFinanceSymbol
	db := dao.WithContext(context.Background())
	res, err := db.Where(dao.Symbol.Eq(symbol)).Where(dao.SellerID.Eq(sellerId)).First()
	if err != nil {
		logs.Error("getWithdrawRate - 查询汇率配置失败: Symbol=%v, SellerId=%v, Error=%v", symbol, sellerId, err)
		return 0.00, err
	}
	if res == nil {
		logs.Error("getWithdrawRate - 未找到汇率配置: Symbol=%v, SellerId=%v", symbol, sellerId)
		return 0.00, errors.New("未找到汇率配置")
	}

	rate := 0.00
	if res.AutoState == 1 {
		rate = res.WithwardRate
	} else {
		rate = res.WithwardRateEx
	}

	if res.WithwardFixType == 1 {
		rate += res.WithwardFix
	} else {
		rate += rate * res.WithwardFix
	}

	return rate, nil
}

func (c *Base) getUser(id int) (*model.XUser, error) {
	dao := server.DaoxHashGame().XUser
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.UserID.Eq(int32(id))).First()
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) getRechargeOrder(no int) (*model.XRecharge, error) {
	dao := server.DaoxHashGame().XRecharge
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(no))).First()

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) getWithdrawOrder(no int) (*model.XWithdraw, error) {
	dao := server.DaoxHashGame().XWithdraw
	db := dao.WithContext(context.Background())

	res, err := db.Where(dao.ID.Eq(int32(no))).First()

	if err != nil {
		return nil, err
	}

	return res, nil
}

func (c *Base) generateNum() string {
	// 获取当前时间戳
	now := time.Now()
	timestamp := now.Format("20060102150405") // 格式化时间戳

	// 生成随机数
	rand.Seed(uint64(now.UnixNano()))
	randomNum := rand.Int63n(100000000)

	// 拼接时间戳和随机数生成订单号
	orderID := fmt.Sprintf("%s%05d", timestamp, randomNum)
	return orderID
}

func (c *Base) post(url string, header map[string]string, data []byte) (*resty.Response, error) {
	// 将请求头转换为JSON格式便于阅读
	headerJson, _ := json.Marshal(header)

	// 一次性打印所有请求信息
	logs.Info("支付请求信息 - URL: %s, Headers: %s, Body: %s", url, string(headerJson), string(data))

	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetBody(string(data)).
		Post(url)

	if err != nil {
		logs.Error("发送请求失败:", err)
		return nil, err
	}

	// 打印响应内容
	logs.Info("支付响应内容:", resp.String())

	return resp, nil
}

func (c *Base) postFrom(url string, header map[string]string, data map[string]string) (*resty.Response, error) {
	// 将请求头和表单数据转换为JSON格式便于阅读
	headerJson, _ := json.Marshal(header)
	dataJson, _ := json.Marshal(data)

	// 一次性打印所有表单请求信息
	logs.Info("支付表单请求信息 - URL: %s, Headers: %s, FormData: %s", url, string(headerJson), string(dataJson))

	client := resty.New()

	// 发送HTTP POST请求
	resp, err := client.R().
		SetHeaders(header).
		SetFormData(data).
		Post(url)

	if err != nil {
		logs.Error("发送表单请求失败:", err)
		return nil, err
	}

	// 打印响应内容
	logs.Info("支付表单响应内容:", resp.String())

	return resp, nil
}

func (c *Base) generateMd5Sign(params xgo.H, cfgkey string) string {
	// 对参数按照键排序
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	var sortedParams []string
	for _, key := range keys {
		if params[key] == "" {
			continue
		}
		//str := key + "=" + params[key].(string)
		sortedParams = append(sortedParams, fmt.Sprintf("%s=%v", key, params[key]))
	}

	sortedParamsStr := strings.Join(sortedParams, "&")
	str := sortedParamsStr + "&key=" + cfgkey
	logs.Info("请求支付加密前：", str)

	// 创建一个 MD5 实例
	hash := md5.New()

	// 将字符串转换为字节数组并计算 MD5 值
	hash.Write([]byte(str))
	hashBytes := hash.Sum(nil)

	// 将 MD5 值转换为十六进制字符串
	md5Str := hex.EncodeToString(hashBytes)

	logs.Info("请求支付加密后：", md5Str)

	return md5Str
}

// rechargeCallbackHandel 处理充值回调
// @description 处理第三方支付平台的充值回调，更新订单状态并处理相关业务逻辑
// @param userId int 用户ID
// @param id int 充值订单ID
// @param result int 充值结果状态码:
//   - 3: 待支付
//   - 5: 成功
//   - 6: 超时
//   - 7: 失败
func (c *Base) rechargeCallbackHandel(userId int, id int, result int) {
	logs.Info("开始处理充值回调: 用户ID=%d, 订单ID=%d, 结果状态=%d", userId, id, result)

	// 使用事务处理所有操作，确保数据一致性
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		if result == 5 {
			// 处理充值成功的情况
			return c.handleDepositSuccess(tx, userId, id)
		} else {
			// 处理其他状态（待支付、超时、失败）
			return c.handleDepositOtherStatus(id, result)
		}
	})

	logs.Info("充值回调处理完成: 用户ID=%d, 订单ID=%d, 结果状态=%d", userId, id, result)
}

// handleDepositSuccess 处理充值成功的业务逻辑
func (c *Base) handleDepositSuccess(tx *xgo.XTx, userId int, orderId int) error {
	// 1. 调用存储过程处理充值成功的核心业务逻辑
	if err := c.executeDepositProcedure(tx, orderId); err != nil {
		return err
	}

	// 2. 获取订单信息
	order, err := c.getRechargeOrder(orderId)
	if err != nil {
		logs.Error("获取充值订单信息失败:", err, "订单ID:", orderId)
		return err
	}

	// 3. 发送成功通知和事件
	c.sendDepositSuccessNotifications(userId, order)

	// 4. 处理首存相关业务
	if order.IsFirst == 1 {
		c.handleFirstDepositBusiness(userId, order)
	}

	// 5. 处理充值活动奖励
	c.handleDepositGifts(userId, orderId)

	logs.Info("充值成功处理完成，订单ID:", orderId)
	return nil
}

// executeDepositProcedure 执行充值存储过程
func (c *Base) executeDepositProcedure(tx *xgo.XTx, orderId int) error {
	result, err := tx.Db.CallProcedure("x_admin_recharge_legal", orderId)
	if err != nil {
		logs.Error("调用充值存储过程失败:", err, "订单ID:", orderId)
		return fmt.Errorf("处理充值失败: %v", err)
	}

	// 检查存储过程返回结果
	if result != nil {
		// 检查错误码
		if result.Exists("errcode") {
			errCode := result.Int("errcode")
			logs.Error("充值存储过程返回错误码:", errCode, "订单ID:", orderId)
			return fmt.Errorf("充值处理失败，错误码: %d", errCode)
		}

		// 检查返回值
		if result.Exists("p_ReturnValue") {
			returnValue := result.Int("p_ReturnValue")
			if returnValue != 0 {
				logs.Error("充值存储过程返回非零值:", returnValue, "订单ID:", orderId)
				return fmt.Errorf("充值处理失败，返回值: %d", returnValue)
			}
		}
	}

	return nil
}

// sendDepositSuccessNotifications 发送充值成功通知
func (c *Base) sendDepositSuccessNotifications(userId int, order *model.XRecharge) {
	// 发送ThinkingData事件
	payMethod, err := c.getPayMethod(int(order.PayID))
	if err == nil {
		datapush.SendDepositSuccessEvent(order, payMethod)
	} else {
		logs.Error("获取支付方式失败:", err, "订单ID:", order.ID)
	}

	// 发送充值到账提醒消息
	messageService := msg.NewSendMessageAPIService()
	if err := messageService.SendAccountMessage("3", userId, order.RealAmount, order.Symbol, nil); err != nil {
		logs.Error("发送充值到账提醒失败: %v", err, "用户ID:", userId, "订单ID:", order.ID)
	}
}

// handleFirstDepositBusiness 处理首存相关业务
func (c *Base) handleFirstDepositBusiness(userId int, order *model.XRecharge) {
	// 处理推荐好友奖励
	if controller.ProcessRecommendFriendReward(int32(userId), order.ID) {
		logs.Info("推荐好友奖励处理成功: userId=%d, orderId=%d", userId, order.ID)
	} else {
		logs.Error("推荐好友奖励处理失败: userId=%d, orderId=%d", userId, order.ID)
	}

	// 充值成功后，重新计算注册赠送活动的流水要求
	if err := active.UpdateRegisterGiftWager(userId, order.RealAmount); err != nil {
		logs.Error("更新注册赠送活动流水要求失败:", err, "用户ID:", userId)
		// 不返回错误，避免影响充值主流程
	}
}

// handleDepositGifts 处理充值活动奖励
func (c *Base) handleDepositGifts(userId int, orderId int) {
	if err := active.AutoGiveDepositGift(int32(userId), int32(orderId)); err != nil {
		logs.Error("自动赠送充值活动奖励失败: userId=%d, orderId=%d, err=%v", userId, orderId, err)
	} else {
		logs.Info("自动赠送充值活动奖励成功: userId=%d, orderId=%d", userId, orderId)
	}
}

// handleDepositOtherStatus 处理充值的其他状态（待支付、超时、失败）
func (c *Base) handleDepositOtherStatus(orderId int, result int) error {
	rechargeDao := server.DaoxHashGame().XRecharge
	rechargeDb := rechargeDao.WithContext(context.Background())

	// 更新订单状态，只处理状态为3(待支付)的订单
	updateResult, err := rechargeDb.Where(rechargeDao.ID.Eq(int32(orderId)), rechargeDao.State.Eq(3)).
		Updates(map[string]interface{}{
			"State": int32(result),
		})

	if err != nil {
		logs.Error("更新充值订单状态失败:", err, "订单ID:", orderId)
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	if updateResult.RowsAffected == 0 {
		logs.Error("充值订单不存在或状态不正确，订单ID:", orderId)
		return errors.New("订单不存在或状态不正确")
	}

	logs.Info("充值订单状态更新成功: 订单ID=%d, 新状态=%d", orderId, result)
	return nil
}

// withdrawCallbackHandel 处理提现回调
// @description 处理第三方支付平台的提现回调，更新订单状态并处理相关业务逻辑
// @param orderId int 提现订单ID
// @param result int 提现结果状态码:
//   - 5: 处理中
//   - 6: 成功
//   - 其他状态: 失败，需要退回金额
func (c *Base) withdrawCallbackHandel(orderId int, result int) {
	logs.Info("withdrawCallbackHandel 开始处理提现回调: OrderId=%d, Result=%d", orderId, result)
	// 处理提现成功的情况
	if result == 6 {
		dao := server.DaoxHashGame().XWithdraw
		db := dao.WithContext(context.Background())
		// 直接更新提现订单状态为成功，只处理状态为5(处理中)的订单
		updateResult, err := db.Where(dao.ID.Eq(int32(orderId)), dao.State.Eq(5)).Updates(map[string]interface{}{
			"State": int32(result),
		})
		if err != nil {
			logs.Error("withdrawCallbackHandel 更新提现订单状态失败: OrderId=%d, Error=%v", orderId, err)
			return
		}

		if updateResult.RowsAffected == 0 {
			logs.Warn("withdrawCallbackHandel 没有更新任何记录: OrderId=%d, 可能订单状态不是5或订单不存在", orderId)
			return
		}

		logs.Info("withdrawCallbackHandel 订单状态更新成功: OrderId=%d, NewState=%d", orderId, result)

		// 获取订单信息用于推送消息
		order, err := db.Where(dao.ID.Eq(int32(orderId))).First()
		if err == nil {
			// 获取支付方式信息
			payMethod, err := c.getPayMethod(int(order.PayID))
			if err != nil {
				logs.Error("获取支付方式失败:", err)
				return
			}
			// 发送提现成功事件到ThinkingData
			datapush.SendWithdrawSuccessEvent(order, payMethod.Brand)
			// 发送提现成功提醒消息
			messageService := msg.NewSendMessageAPIService()
			if err := messageService.SendAccountMessage("4", int(order.UserID), order.RealAmount, order.Symbol, nil); err != nil {
				logs.Error("发送提现成功提醒失败: %v", err)
			}

		} else {
			logs.Error("获取提现订单信息失败:", err)
		}
	} else {
		// 处理提现失败的情况，需要在事务中处理退款
		logs.Info("withdrawCallbackHandel 处理提现失败回调: OrderId=%d, Result=%d", orderId, result)
		server.XDb().Transaction(func(tx *xgo.XTx) error {
			// 获取提现订单详情
			withdrawDao := server.DaoxHashGame().XWithdraw
			withdrawDb := withdrawDao.WithContext(context.Background())

			// 先查询订单当前状态
			currentOrder, err := withdrawDb.Where(withdrawDao.ID.Eq(int32(orderId))).First()
			if err != nil {
				logs.Error("withdrawCallbackHandel 查询订单失败: OrderId=%d, Error=%v", orderId, err)
				return err
			}
			logs.Info("withdrawCallbackHandel 当前订单状态: OrderId=%d, CurrentState=%d", orderId, currentOrder.State)

			// 更新提现订单状态，只处理状态为5(处理中)的订单
			row, err := withdrawDb.Where(withdrawDao.ID.Eq(int32(orderId)), withdrawDao.State.Eq(5)).Updates(map[string]interface{}{
				"State": int32(result),
			})
			if err != nil {
				logs.Error("withdrawCallbackHandel 更新提现订单状态失败: OrderId=%d, Error=%v", orderId, err)
				return err
			}

			logs.Info("withdrawCallbackHandel 提现失败订单状态更新结果: OrderId=%d, RowsAffected=%d", orderId, row.RowsAffected)

			var user *model.XUser
			var withdraw *model.XWithdraw
			// 如果成功更新了订单状态
			if row.RowsAffected > 0 {
				logs.Info("withdrawCallbackHandel 开始处理提现失败退款: OrderId=%d", orderId)
				// 获取提现订单信息
				if withdraw, err = withdrawDb.Where(withdrawDao.ID.Eq(int32(orderId))).First(); err != nil {
					logs.Error("withdrawCallbackHandel 获取提现订单失败: OrderId=%d, Error=%v", orderId, err)
					return err
				}
				logs.Info("withdrawCallbackHandel 获取提现订单成功: OrderId=%d, UserId=%d, Amount=%f", orderId, withdraw.UserID, withdraw.Amount)

				// 获取用户信息
				userDao := server.DaoxHashGame().XUser
				userDb := userDao.WithContext(context.Background())

				if user, err = userDb.Where(userDao.UserID.Eq(withdraw.UserID)).First(); err != nil {
					logs.Error("获取用户信息失败:", err)
					return err
				}

				// 更新用户余额，将提现金额退回
				_, err = userDb.Where(userDao.UserID.Eq(withdraw.UserID)).Updates(map[string]interface{}{
					"Amount": user.Amount + withdraw.Amount,
				})
				if err != nil {
					logs.Error("更新用户余额失败:", err)
					return err
				}
				// 记录资金变动日志
				amountChangeDao := server.DaoxHashGame().XAmountChangeLog
				amountChangeDb := amountChangeDao.WithContext(context.Background())
				err = amountChangeDb.Create(&model.XAmountChangeLog{
					SellerID:     withdraw.SellerID,
					ChannelID:    withdraw.ChannelID,
					UserID:       withdraw.UserID,
					BeforeAmount: user.Amount,
					Amount:       withdraw.Amount,
					AfterAmount:  user.Amount + withdraw.Amount,
					Reason:       6, // 提现失败退回
					Memo:         fmt.Sprint(orderId),
					CreateTime:   time.Now(),
				})
				if err != nil {
					logs.Error("记录资金变动日志失败:", err)
					return err
				}
			} else {
				logs.Warn("withdrawCallbackHandel 没有更新任何记录: OrderId=%d, 可能订单状态不是5或订单不存在", orderId)
				return errors.New("订单不存在,或订单状态不正确")
			}
			// 推送提现失败消息到ThinkingData
			// 获取支付方式信息
			payMethod, err := c.getPayMethod(int(withdraw.PayID))
			if err == nil {
				// 发送提现失败事件到ThinkingData
				datapush.SendWithdrawFailEvent(user, withdraw, payMethod.Brand, "提现失败")
			} else {
				logs.Error("获取支付方式失败:", err)
				// 即使获取支付方式失败，也发送事件，使用默认值
				datapush.SendWithdrawFailEvent(user, withdraw, "未知", "提现失败")
			}
			logs.Info("withdrawCallbackHandel 提现失败处理完成: OrderId=%d, UserId=%d", orderId, user.UserID)
			return nil
		})
	}
	logs.Info("withdrawCallbackHandel 提现回调处理完成: OrderId=%d, Result=%d", orderId, result)
}

func (c *Base) updateThirdOrder(id int32, no string) error {
	dao := server.DaoxHashGame().XRecharge
	db := dao.WithContext(context.Background())

	_, err := db.Where(dao.ID.Eq(id)).Update(dao.ThirdID, no)
	if err != nil {
		return err
	}

	return nil
}

func (c *Base) md5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// ValidateSymbol 验证请求的币种是否与支付方式配置的币种匹配
// @param reqSymbol 请求的币种
// @param paySymbol 支付方式配置的币种
// @return error 如果币种不匹配返回错误，匹配则返回nil
func (c *Base) ValidateSymbol(reqSymbol string, paySymbol string) error {
	// 验证币种是否匹配（使用数据库中配置的币种）
	reqSymbol = strings.ToUpper(reqSymbol)
	paySymbol = strings.ToUpper(paySymbol)
	if reqSymbol != paySymbol {
		errorMsg := fmt.Sprintf("该支付方式只支持%s币种", paySymbol)
		return errors.New(errorMsg)
	}

	return nil
}

// 限制请求结构
type LimitRequest struct {
	SellerId string  `json:"seller_id"`
	UserID   string  `json:"user_id"`
	Amount   float64 `json:"amount"`
	Currency string  `json:"currency"`
	ClientIP string  `json:"client_ip"`
}

// 限制服务
type LimitService struct {
	redisClient *xgo.XRedis
}

// 创建限制服务
func NewLimitService(redisClient *xgo.XRedis) *LimitService {
	return &LimitService{
		redisClient: redisClient,
	}
}

// 检查五维限制：厂商 + 用户ID + 金额 + 币种 + IP
func (s *LimitService) CheckLimit(req *LimitRequest, ttl time.Duration) error {
	// 生成五维key
	key := fmt.Sprintf("limit:%s:user:%s:amount:%.2f:currency:%s:ip:%s",
		req.SellerId, req.UserID, req.Amount, req.Currency, req.ClientIP)

	// 检查是否存在
	exists, err := s.redisClient.Exists(key)
	if err != nil {
		return fmt.Errorf("检查限制失败: %v", err)
	}

	if exists {
		return errors.New("操作过于频繁,请稍后再试")
	}

	// 设置限制时间
	err = s.redisClient.Set(key, "1", int(ttl.Seconds()))
	if err != nil {
		return fmt.Errorf("设置限制失败: %v", err)
	}

	return nil
}
