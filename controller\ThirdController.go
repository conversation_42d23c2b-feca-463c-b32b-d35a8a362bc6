package controller

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/third"
	"xserver/controller/third/single"
	"xserver/controller/third/single/base"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/imroc/req"
	"github.com/zhms/xgo/xgo"
)

type ThirdController struct {
	optlocker sync.Map

	lottery_url string
	lottery_key string

	xiaoyouxi_url           string
	xiaoyouxi_merchant_code string
	xiaoyouxi_secure_key    string
	xiaoyouxi_sign_key      string
	xiaoyouxi_token         string

	wm_url       string
	wm_vendor    string
	wm_signature string

	evo_cfg map[string]interface{}

	easybet_url    string
	easybet_platid string
	easybet_key    string

	up_url string

	lotterygames *map[string]interface{}
	qipaigames   *map[string]interface{}

	astar_url        string
	astar_channel_id string
	astar_api_key    string
	astar_auth_token string
	// 9Wickets配置字段
	wickets_api_key         string // API密钥
	wickets_agent           string // 代理ID
	wickets_currency_code   int
	wickets_website         string // 网站域名
	wickets_proxy           string // 代理服务器 hash部署的服务器在新加坡 9w不支持故通过代理访问
	wickets_proxy_user      string //代理服务器用户名
	wickets_proxy_pass      string //代理服务器密码
	wickets_private_domain  string // 私有域名
	wickets_exchange_host   string // player server
	wickets_api_server_host string // API info server
	easybetSrvice           *third.EasybetSrvice
	astarSwSrvice           *third.AstarSwSrvice
	threeupSrvice           *third.ThreeUpSrvice
	jbdService              *third.JDBService
	jdService               *third.JDService
	coin2Service            *third.Coin2Service
	bearcowService          *third.BCService
	fortuneService          *third.FortuneService
	stargrabberService      *third.StargrabberService
	hashLottoryService      *third.HashLottoryService
	tradingService          *third.TradingService
	hub88Service            *third.Hub88Service
	updownService           *third.UpDownService
	//a sen  单一钱包开始
	EvoSingle              single.EVOT
	PPSingle               *single.PPConfig
	PGSingle               *single.PGConfig
	GFGSingle              *single.GFGConfig
	AgSingle               *single.AgConfig
	GFGLotteryConfig       *single.GFGLotteryConfig
	Hub88Config            *single.Hub88Config
	OmgSingle              *single.OmgSingleService
	KaiyuanSingle          *single.KaiyuanSingleService
	AmigoSingle            *single.AmigoSingleService
	DBLiveSingleService    *single.DBLiveSingleService
	JiLiSingleService      *single.JiLiSingleService
	CBKSingle              *single.CbkConfig
	SabaService            *single.SabaService //沙巴体育投注
	FbService              *single.FbService   //FB体育投注
	FcSingleService        *single.FcSingleService
	OpsService             *single.OpsService
	FBLiveSingleService    *single.FBLiveSingleService
	WESingleService        *single.WESingleService
	DGSingleService        *single.DGSingleService
	OFALiveSingleService   *single.OFALiveSingleService
	IMOneSingleService     *single.IMOneSingleService
	SASingleService        *single.SASingleService
	ABSingleService        *single.ABSingleService
	St8Config              *single.ST8Service
	QQPokerService         *single.QQPokerService
	PachinkoService        *single.PachinkoService
	RSGSingleService       *single.RSGSingleService
	VGSingleService        *single.VGSingleService // VG 真人
	ThreeParyLonginService *single.ThreeParyLonginService
	WaliClient             *WaliClient
	TGFUNClient            *TGFUNClient
	callbackurl            string
}

// 添加一个广播适配器，实现 BroadcastInterface 接口
type SocketBroadcaster struct{}

// WinBroadcast 实现 BroadcastInterface 接口
func (s *SocketBroadcaster) WinBroadcast(orderID int, gameType string) error {
	return SocketHandler.WinBoradcast(orderID, gameType)
}

func (c *ThirdController) Init() {
	// 设置全局广播器
	base.GlobalBroadcaster = &SocketBroadcaster{}
	c.callbackurl = "callback.fpdk0.live"

	// 添加三方回调安全验证中间件
	logs.Info("注册三方回调安全验证中间件")
	server.Http().Gin().Use(c.thirdCallbackSecurityMiddleware)
	cfgdata, _ := server.Db().Query("select * from x_third_config", []interface{}{})
	for i := 0; i < len(*cfgdata); i++ {
		cfg := (*cfgdata)[i]
		brand := cfg["Brand"].(string)
		logs.Info(brand)
		if brand == "gfg_hash" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)

			c.lottery_url = mcv["url"].(string)
			c.lottery_key = mcv["key"].(string)
		} else if brand == "laobanniang" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)

			c.xiaoyouxi_url = mcv["url"].(string)
			c.xiaoyouxi_merchant_code = mcv["merchant_code"].(string)
			c.xiaoyouxi_secure_key = mcv["secure_key"].(string)
			c.xiaoyouxi_sign_key = mcv["sign_key"].(string)
		} else if brand == "wm" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)

			c.wm_url = mcv["url"].(string)
			c.wm_vendor = mcv["vendor"].(string)
			c.wm_signature = mcv["signature"].(string)
		} else if brand == "evo" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)
			c.evo_cfg = mcv
		} else if brand == "up" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)

			c.up_url = mcv["url"].(string)
		} else if brand == "astar" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.astar_url = mcv["url"]
			c.astar_channel_id = mcv["channel_id"]
			c.astar_api_key = mcv["secure_key"]
			c.astarSwSrvice = third.NewAstarSwSrvice(mcv["channel_id"], mcv["vendor_token"], mcv["game_url"], mcv["report_url"], mcv["currency"], c.callbackurl, SocketHandler.RefreshUserAmount)
		} else if brand == "easybetsp" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.easybetSrvice = third.NewEasybetSrvice(mcv["url"], mcv["plat_id"], mcv["secure_key"], mcv["game_id"], mcv["game_name"], c.callbackurl, SocketHandler.RefreshUserAmount)
		} else if brand == "three_up" {
			logs.Info("threeup up")
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			var ips []string
			ips = append(ips, "**************")
			c.threeupSrvice = third.NewThreeUpSrvice(mcv["url"], mcv["prefix_code"], ips, mcv["platform"], mcv["interface"], mcv["currency"], mcv["passkey"], SocketHandler.RefreshUserAmount)
		} else if brand == "jdb" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.jbdService = third.NewJDBService(mcv["url"], mcv["currency"], mcv["dc"], mcv["iv"], mcv["key"], mcv["parent"], SocketHandler.RefreshUserAmount)
		} else if brand == "jd" {
			cv := cfg["ConfigValue"].(string)
			c.jdService = third.NewJDService(cv, SocketHandler.RefreshUserAmount)
		} else if brand == "coin2" {
			cv := cfg["ConfigValue"].(string)
			c.coin2Service = third.NewCoin2Service(cv, SocketHandler.RefreshUserAmount)
		} else if brand == "bearcow" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.bearcowService = third.NewBearService(mcv["appUrl"], mcv["url"], mcv["name"], mcv["key"], mcv["agent"], SocketHandler.RefreshUserAmount)
		} else if brand == "fortune" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.fortuneService = third.NewFortuneService(mcv["appUrl"], mcv["url"], mcv["companyKey"], mcv["name"], mcv["key"], mcv["agent"], SocketHandler.RefreshUserAmount)
		} else if brand == "stargrabber" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.stargrabberService = third.NewStargrabberService(mcv["appUrl"], mcv["url"], mcv["companyKey"], mcv["name"], mcv["key"], mcv["agent"], SocketHandler.RefreshUserAmount)
		} else if brand == "hash_lottory" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.hashLottoryService = third.NewHashLottoryService(mcv["appUrl"], mcv["url"], mcv["companyKey"], mcv["name"], mcv["key"], mcv["agent"], SocketHandler.RefreshUserAmount)
		} else if brand == "trading" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.tradingService = third.NewTradingService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "updown" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.updownService = third.NewUpDownService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "hub88" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.hub88Service = third.NewHub88Service(mcv["url"], mcv["name"], mcv["key"], SocketHandler.RefreshUserAmount)
		} else if brand == "evosignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)
			c.EvoSingle = single.NewEvoLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "ppsignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.PPSingle = single.NewPPLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "pgsignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.PGSingle = single.NewPGLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "gfgsignle" { //gfg电子
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{
				"config": cv, // 直接传递原始JSON字符串
			}
			c.GFGSingle = single.NewGFGLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "agsignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.AgSingle = single.NewAgLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "gfg_hash_signle" { //gfg彩票
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.GFGLotteryConfig = single.NewGFGLotteryLogic(mcv, SocketHandler.RefreshUserAmount)
			//go c.GFGLotteryConfig.Sync_order()
		} else if brand == "hub88_signle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.Hub88Config = single.NewHub88Logic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "omg_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.OmgSingle = single.NewOmgSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "kaiyuan_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.KaiyuanSingle = single.NewKaiyuanSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "amigo_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.AmigoSingle = single.NewAmigoSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "dblive_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.DBLiveSingleService = single.NewDBLiveSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "jili_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.JiLiSingleService = single.NewJiLiSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "cbksignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.CBKSingle = single.NewCbkLogic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "sabasignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.SabaService = single.NewSabaService(mcv, SocketHandler.RefreshUserAmount) //沙巴体育投注
		} else if brand == "fc_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.FcSingleService = single.NewFcSingleService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "fbsignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.FbService = single.NewFbService(mcv, SocketHandler.RefreshUserAmount) //FB体育投注
		} else if brand == "opssignle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.OpsService = single.NewOpsLogic(mcv, SocketHandler.RefreshUserAmount) //OPS
		} else if brand == "fblive_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.FBLiveSingleService = single.NewFBLiveSingleService(mcv, SocketHandler.RefreshUserAmount) //OPS
		} else if brand == "we_single" {
			cv := cfg["ConfigValue"].(string)

			//logs.Info("we读取到配置:", " cv=", cv)
			// 处理包含实际换行符的 JSON
			var mcvInterface map[string]interface{}
			err := json.Unmarshal([]byte(cv), &mcvInterface)
			if err != nil {
				logs.Error("解析WE配置失败1:", " err=", err)
				// 尝试替换实际换行符为转义字符
				cv = strings.Replace(cv, "\r\n", "\\n", -1)
				cv = strings.Replace(cv, "\n", "\\n", -1)
				err = json.Unmarshal([]byte(cv), &mcvInterface)
				if err != nil {
					logs.Error("解析WE配置失败2:", " err=", err, " cv=", cv)
				}
			}

			// 转换为 map[string]string
			mcv := map[string]string{}
			if mcvInterface != nil {
				for k, v := range mcvInterface {
					mcv[k] = fmt.Sprintf("%v", v)
				}
				//logs.Info("WE配置处理完成，channel_id=", mcv["channel_id"])
			} else {
				logs.Error("WE配置解析失败， 无法创建服务", " cv=", cv)
			}
			c.WESingleService = single.NewWESingleService(mcv, SocketHandler.RefreshUserAmount) //WE真人
		} else if brand == "dg_single" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.DGSingleService = single.NewDGSingleService(mcv, SocketHandler.RefreshUserAmount) //DG真人
		} else if brand == "wickets" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]interface{}{}
			json.Unmarshal([]byte(cv), &mcv)

			c.wickets_private_domain = mcv["url"].(string)
			c.wickets_api_key = mcv["api_key"].(string)
			c.wickets_agent = mcv["agent"].(string)
			c.wickets_website = mcv["website"].(string)
			c.wickets_exchange_host = mcv["exchange_host"].(string)     // player server
			c.wickets_api_server_host = mcv["api_server_host"].(string) // API info server
			c.wickets_proxy = mcv["wickets_proxy"].(string)
			c.wickets_proxy_user = mcv["wickets_proxy_user"].(string) //代理服务器用户名
			c.wickets_proxy_pass = mcv["wickets_proxy_pass"].(string) //代理服务器密码
			currencyCode, _ := strconv.Atoi(mcv["currency_code"].(string))

			c.wickets_currency_code = currencyCode //测试环境PKR的货币代码:29
		} else if brand == "st8_signle" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.St8Config = single.NewST8Logic(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "qqpoker" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.QQPokerService = single.NewQQPokerService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "pachinko" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.PachinkoService = single.NewPachinkoService(mcv, SocketHandler.RefreshUserAmount)
		} else if brand == "ofalive" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.OFALiveSingleService = single.NewOFALiveSingleService(mcv, SocketHandler.RefreshUserAmount) //OPS
		} else if brand == "imone" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.IMOneSingleService = single.NewIMOneSingleService(mcv, SocketHandler.RefreshUserAmount) //IMOne
		} else if brand == "wali" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.WaliClient = NewWaliClient(mcv, c)
		} else if brand == "tgfun" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.TGFUNClient = NewTGFUNClient(mcv, c)
		} else if brand == "ab" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.ABSingleService = single.NewABSingleService(mcv, SocketHandler.RefreshUserAmount) //OPS
		} else if brand == "salive" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.SASingleService = single.NewSASingleService(mcv, SocketHandler.RefreshUserAmount) //OPS
		} else if brand == "rsg" {
			cv := cfg["ConfigValue"].(string)
			mcv := map[string]string{}
			json.Unmarshal([]byte(cv), &mcv)
			c.RSGSingleService = single.NewRSGSingleService(mcv, SocketHandler.RefreshUserAmount) //RSG
		} else if brand == "vglive" {
			cv := cfg["ConfigValue"].(string)
			config := single.VGSingleConfig{}
			_ = json.Unmarshal([]byte(cv), &config)
			c.VGSingleService = single.NewVGSingleService(config, SocketHandler.RefreshUserAmount) // VG 真人
		}
	}
	// fc_single
	if c.FcSingleService != nil {
		server.Http().Get("/api/third/fc_game_list", c.FcSingleService.InitFcGameList)
		server.Http().PostNoAuth("/api/third/fc/fc_login", c.FcSingleService.Login)
		server.Http().PostNoAuth("/api/third/fc/GetBalance", c.FcSingleService.GetBalance)
		server.Http().PostNoAuth("/api/third/fc/BetNInfo", c.FcSingleService.BetNInfo)
		server.Http().PostNoAuth("/api/third/fc/CancelBetNInfo", c.FcSingleService.CancelBetNInfo)
		server.Http().PostNoAuth("/api/third/fc/Bet", c.FcSingleService.Bet)
		server.Http().PostNoAuth("/api/third/fc/Settle", c.FcSingleService.Settle)
		server.Http().PostNoAuth("/api/third/fc/CancelBet", c.FcSingleService.CancelBet)

		server.Http().GetNoAuth("/api/third/sportsBook.css", c.FcSingleService.SportsBookCss)

	}

	////////////单一钱包开始
	//evo single
	server.Http().Post("/api/third/evo_single_login", c.EvoSingle.Login)
	server.Http().PostNoAuth("/api/third/callback/evo/single/check", c.EvoSingle.Check)
	server.Http().PostNoAuth("/api/third/callback/evo/sid", c.EvoSingle.Check)
	server.Http().PostNoAuth("/api/third/callback/evo/single/balance", c.EvoSingle.Balance)
	server.Http().PostNoAuth("/api/third/callback/evo/single/debit", c.EvoSingle.Debit)
	server.Http().PostNoAuth("/api/third/callback/evo/single/credit", c.EvoSingle.Credit)
	server.Http().PostNoAuth("/api/third/callback/evo/single/cancel", c.EvoSingle.Cancel)
	// 获取EVO游戏列表
	//server.Http().PostNoAuth("/api/single/evo_game_list", c.EvoSingle.GameList)

	//pp single
	server.Http().Post("/api/third/pp_single_login", c.PPSingle.Login)
	server.Http().PostNoAuth("/api/third/pp_single_auth", c.PPSingle.Auth)
	server.Http().PostNoAuth("/api/third/pp_single_balance", c.PPSingle.Balance)
	server.Http().PostNoAuth("/api/third/pp_single_bet", c.PPSingle.Bet)
	server.Http().PostNoAuth("/api/third/pp_single_result", c.PPSingle.Result)

	////根据三方回应 此接口可以取消
	//server.Http().PostNoAuth("/api/third/pp_single_bonusWin", c.PPSingle.BonusWin)
	server.Http().PostNoAuth("/api/third/pp_single_jackpotWin", c.PPSingle.JackpotWin)
	server.Http().PostNoAuth("/api/third/pp_single_endRound", c.PPSingle.EndRound)
	server.Http().PostNoAuth("/api/third/pp_single_promoWin", c.PPSingle.PromoWin)
	server.Http().PostNoAuth("/api/third/pp_single_adjustment", c.PPSingle.Adjustment)
	server.Http().PostNoAuth("/api/third/pp_single_roundDetails", c.PPSingle.RoundDetails)
	server.Http().PostNoAuth("/api/third/pp_single_refund", c.PPSingle.Refund)

	//pp新回调
	server.Http().PostNoAuth("/api/third/pp/pp_single_jackpotWin", c.PPSingle.JackpotWin)
	server.Http().PostNoAuth("/api/third/pp/pp_single_endRound", c.PPSingle.EndRound)
	server.Http().PostNoAuth("/api/third/pp/pp_single_promoWin", c.PPSingle.PromoWin)
	server.Http().PostNoAuth("/api/third/pp/pp_single_adjustment", c.PPSingle.Adjustment)
	server.Http().PostNoAuth("/api/third/pp/pp_single_roundDetails", c.PPSingle.RoundDetails)
	server.Http().PostNoAuth("/api/third/pp/pp_single_refund", c.PPSingle.Refund)

	//pg single
	server.Http().Post("/api/third/pg_single_login", c.PGSingle.Login)
	server.Http().PostNoAuth("/api/third/pg_single_verifySession", c.PGSingle.VerifySession)
	server.Http().PostNoAuth("/api/third/pg_single_cash", c.PGSingle.Cash)
	server.Http().PostNoAuth("/api/third/pg_single_transferInOut", c.PGSingle.TransferInOut)
	server.Http().PostNoAuth("/api/third/pg_single_adjustment", c.PGSingle.Adjustment)
	server.Http().PostNoAuth("/api/third/pg_single_updateBetDetail", c.PGSingle.UpdateBetDetail)

	//gfg single
	server.Http().Post("/api/third/gfg_single_login", c.GFGSingle.Login)

	//ag  single
	server.Http().Post("/api/third/ag_single_login", c.AgSingle.Login)
	server.Http().PostNoAuth("/api/third/ag_single_transfer", c.AgSingle.Do)
	//新的地址
	server.Http().PostNoAuth("/api/third/ag/ag_single_transfer", c.AgSingle.Do)

	//gfg_hash single
	server.Http().Post("/api/third/gfg_hash_single_login", c.GFGLotteryConfig.Login)
	server.Http().PostNoAuth("/api/third/gfg_single_lottery_get_banlance", c.GFGLotteryConfig.GetUserBalance)
	server.Http().PostNoAuth("/api/third/gfg_single_lottery_get_batch_banlance", c.GFGLotteryConfig.GetAllUserBalance)
	server.Http().PostNoAuth("/api/third/gfg_single_lottery_in_out", c.GFGLotteryConfig.GetSingleWalletQuota)
	server.Http().PostNoAuth("/api/third/push_lose_order", c.GFGLotteryConfig.PushLoseOrder)
	server.Http().PostNoAuth("/api/third/gfg_single_lottery_check", c.GFGLotteryConfig.CheckWalletRequestExisted)
	server.Http().PostNoAuth("/api/third/gfg_single_lottery_reportdetail", c.GFGLotteryConfig.GetReportDetail)

	//gfg  single
	server.Http().PostNoAuth("/api/GetLotteryGamePeriodData", c.GFGLotteryConfig.GetGamePeriodData)

	//hub88 single
	if c.Hub88Config != nil {
		server.Http().Post("/api/third/hub_single_login", c.Hub88Config.Login)
		server.Http().PostNoAuth("/api/third/hub_single_game_list", c.Hub88Config.GetGameList)
		server.Http().PostNoAuth("/api/third/hub88/user/info", c.Hub88Config.Info)
		server.Http().PostNoAuth("/api/third/hub88/user/authorize", c.Hub88Config.Authorize)
		server.Http().PostNoAuth("/api/third/hub88/user/balance", c.Hub88Config.Balance)
		server.Http().PostNoAuth("/api/third/hub88/transaction/win", c.Hub88Config.Win)
		server.Http().PostNoAuth("/api/third/hub88/transaction/rollback", c.Hub88Config.Rollback)
		server.Http().PostNoAuth("/api/third/hub88/transaction/bet", c.Hub88Config.Bet)
	}

	//omg single
	if c.OmgSingle != nil {
		server.Http().Post("/api/third/omg_single_login", c.OmgSingle.Login)                    // 进入游戏
		server.Http().PostNoAuth("/api/luck/user/verify_session", c.OmgSingle.VerifySession)    // 验证
		server.Http().PostNoAuth("/api/luck/balance/get_balance", c.OmgSingle.GetBalance)       // 获取余额
		server.Http().PostNoAuth("/api/luck/balance/change_balance", c.OmgSingle.ChangeBalance) // 下注撤销返奖
		server.Http().PostNoAuth("/api/luck/game/load_list", c.OmgSingle.LoadGameList)          // 获取游戏列表

		//新的回调地址
		server.Http().PostNoAuth("/api/third/luck/user/verify_session", c.OmgSingle.VerifySession)    // 验证
		server.Http().PostNoAuth("/api/third/luck/balance/get_balance", c.OmgSingle.GetBalance)       // 获取余额
		server.Http().PostNoAuth("/api/third/luck/balance/change_balance", c.OmgSingle.ChangeBalance) // 下注撤销返奖
		server.Http().PostNoAuth("/api/third/luck/game/load_list", c.OmgSingle.LoadGameList)          // 获取游戏列表
	}

	//kaiyuan_single 开元棋牌
	if c.KaiyuanSingle != nil {
		server.Http().Post("/api/third/kaiyuan/login", c.KaiyuanSingle.Login)                    //登陆开元棋牌游戏
		server.Http().GetNoAuth("/api/third/kaiyuan/singleWallet", c.KaiyuanSingle.RequestIndex) //开元单一钱包
	}

	//amigo single 日本弹珠
	if c.AmigoSingle != nil {
		server.Http().Post("/api/third/amigo_single_login", c.AmigoSingle.Login)                             // 进入游戏
		server.Http().GetNoAuth("/api/third/amigo/getCurrency", c.AmigoSingle.GetBalance)                    // 获取余额
		server.Http().PostNoAuth("/api/third/amigo/moneyTransfer", c.AmigoSingle.ChangeBalance)              // 下注返奖
		server.Http().PostNoAuth("/api/third/amigo/gameOver", c.AmigoSingle.GameOver)                        // 游戏结束
		server.Http().PostNoAuth("/api/third/amigo/game/load_list", c.AmigoSingle.LoadGameList)              // 获取游戏列表
		server.Http().PostNoAuth("/api/third/amigo/game/load_list_status", c.AmigoSingle.LoadGameListStatus) // 获取游戏列表状态
		// server.Http().GetNoAuth("/api/third/amigo/game/set_game_hot_new", c.AmigoSingle.SetGameHotNew)       // 设置游戏的热门和新属性
	}

	// dblive single DB真人
	if c.DBLiveSingleService != nil {
		server.Http().Post("/api/third/dblive_single_login", c.DBLiveSingleService.Login)                    // 进入游戏
		server.Http().PostNoAuth("/api/third/dblive/getBalance", c.DBLiveSingleService.GetBalance)           // 单个会员余额查询接口
		server.Http().PostNoAuth("/api/third/dblive/getBatchBalance", c.DBLiveSingleService.GetBatchBalance) // 批量会员余额查询接口
		server.Http().PostNoAuth("/api/third/dblive/betConfirm", c.DBLiveSingleService.BetConfirm)           // 下注确认回调接口
		server.Http().PostNoAuth("/api/third/dblive/betCancel", c.DBLiveSingleService.BetCancel)             // 取消下注回调接口
		server.Http().PostNoAuth("/api/third/dblive/gamePayout", c.DBLiveSingleService.GamePayout)           // 派彩回调接口
		server.Http().PostNoAuth("/api/third/dblive/activityPayout", c.DBLiveSingleService.ActivityPayout)   // 活动和小费类回调接口
		server.Http().PostNoAuth("/api/third/dblive/playerbetting", c.DBLiveSingleService.Playerbetting)     // 玩家下注推送接口
		server.Http().PostNoAuth("/api/third/dblive/activityRebate", c.DBLiveSingleService.ActivityRebate)   // 返利活动推送接口
	}

	// jili single 吉利
	if c.JiLiSingleService != nil {
		server.Http().Post("/api/third/jili_single_login", c.JiLiSingleService.Login)                      // 进入游戏
		server.Http().PostNoAuth("/api/third/jili/auth", c.JiLiSingleService.Auth)                         // 获取余额
		server.Http().PostNoAuth("/api/third/jili/bet", c.JiLiSingleService.Bet)                           // 投注中奖
		server.Http().PostNoAuth("/api/third/jili/cancelBet", c.JiLiSingleService.CancelBet)               // 取消投注中奖
		server.Http().PostNoAuth("/api/third/jili/sessionBet", c.JiLiSingleService.SessionBet)             // 下注结算
		server.Http().PostNoAuth("/api/third/jili/cancelSessionBet", c.JiLiSingleService.CancelSessionBet) // 回滚一笔下注
		server.Http().PostNoAuth("/api/third/jili/game/load_list", c.JiLiSingleService.LoadGameList)       // 获取游戏列表
	}

	server.Http().Post("/api/third/sync_amount", c.sync_amount)
	server.Http().Post("/api/third/lottery_login", c.lottery_login)
	server.Http().PostNoAuth("/api/third/lottery_game", c.lottery_game)
	server.Http().Post("/api/third/xiaoyouxi_login", c.xiaoyouxi_login)
	server.Http().Post("/api/third/mw_login", c.mw_login)

	server.Http().Post("/api/third/easybet_login", c.easybet_login)
	//server.Http().Post("/api/third/pg_login", c.pg_login)
	//server.Http().Post("/api/third/pp_login", c.pp_login)
	//server.Http().Post("/api/third/ag_login", c.ag_login)

	server.Http().Post("/api/third/up_login", c.up_login)
	server.Http().Post("/api/third/wickets_login", c.wickets_login) //9Wickets游戏登录
	if c.jbdService != nil {
		server.Http().Post("/api/third/jdb_login", c.jbdService.JDB_Login)
		server.Http().PostNoAuth("/api/jdb", c.jbdService.JDB_apiRequest)
	}

	if c.jdService != nil {
		c.jdService.BindRouter()
	}

	if c.coin2Service != nil {
		c.coin2Service.BindRouter()
	}

	//og group games union login link
	server.Http().Post("/api/third/og_login", c.Og_Union_Login)
	server.Http().PostNoAuth("/api/Balance/GetBalance", c.Og_GetBalance)
	server.Http().PostNoAuth("/api/Balance/LockBalance", c.Og_Union_LockBalance)
	server.Http().PostNoAuth("/api/Balance/UnLockBalance", c.Og_Union_UnLockBalance)
	server.Http().PostByNoAuthMayUserToken("/api/third/GetList", c.GetListWithStatusCheck)

	//gfg去掉token字段后不能用通用的url分发 需要和trad up分开
	server.Http().PostNoAuth("/api/third/gfg/api/Balance/GetBalance", c.GFGSingle.GetBalance)
	server.Http().PostNoAuth("/api/third/gfg/api/Balance/LockBalance", c.GFGSingle.Bet)
	server.Http().PostNoAuth("/api/third/gfg/api/Balance/UnLockBalance", c.GFGSingle.Result)
	//https://d2xsnz67th9zqq.cloudfront.net/api/balance/cancel
	server.Http().PostNoAuth("/api/third/gfg/api/balance/cancel", c.GFGSingle.CancelBet)

	if c.hub88Service != nil {
		server.Http().Post("/api/third/hub88_login", c.hub88Service.JD_Login)
	}

	if c.easybetSrvice != nil {
		server.Http().Post("/api/third/easybetsp_login", c.easybetSrvice.EasybetLogin)
		server.Http().PostNoAuth("/api/third/easybetsp_api", c.easybetSrvice.EasybetCallinApi)
		server.Http().PostNoAuth("/api/third/easybetsp_manualupdategamename", c.easybetSrvice.ManualUpdateGameName) // 手动更新游戏名称
		server.Http().PostNoAuth("/api/third/easybetsp_getfootballhotevents", c.easybetSrvice.GetFootballHotEvents) // 获取热门赛事
	}

	// astar transfer wallet
	// server.Http().Post("/api/third/astar_login", c.astar_login)

	if c.astarSwSrvice != nil {
		server.Http().Post("/api/third/astar_login", c.astarSwSrvice.GameLaunch)
		server.Http().Post("/api/third/astar_sw_login", c.astarSwSrvice.GameLaunch)
		server.Http().Post("/api/third/astar_game_report", c.astarSwSrvice.GameReport)

		{
			astarGrp := server.Http().NewGroup(fmt.Sprintf("/api/third/astar_api/gamepool/%s", c.astar_channel_id))
			{
				astarGrp.PostNoAuth("/player/auth", c.astarSwSrvice.Auth)
				astarGrp.GetNoAuth("/player/balance/:id", c.astarSwSrvice.GetBalance)
				astarGrp.PostNoAuth("/player/logout", c.astarSwSrvice.Logout)
				astarGrp.PostNoAuth("/table/rollout", c.astarSwSrvice.Rollout)
				astarGrp.PostNoAuth("/table/rollin", c.astarSwSrvice.Rollin)
				astarGrp.PostNoAuth("/game/detailtoken", c.astarSwSrvice.CheckDetailToken)
				astarGrp.GetNoAuth("/game/roundcheck", c.astarSwSrvice.GameRoundCheck)
				astarGrp.PostNoAuth("/game/bet/refund", c.astarSwSrvice.Refund)
			}
		}
		{
			astarGrp := server.Http().NewGroup(fmt.Sprintf("/api/third/astar_api/gamepool/%s", strings.ToLower(c.astar_channel_id)))
			{
				astarGrp.PostNoAuth("/player/auth", c.astarSwSrvice.Auth)
				astarGrp.GetNoAuth("/player/balance/:id", c.astarSwSrvice.GetBalance)
				astarGrp.PostNoAuth("/player/logout", c.astarSwSrvice.Logout)
				astarGrp.PostNoAuth("/table/rollout", c.astarSwSrvice.Rollout)
				astarGrp.PostNoAuth("/table/rollin", c.astarSwSrvice.Rollin)
				astarGrp.PostNoAuth("/game/detailtoken", c.astarSwSrvice.CheckDetailToken)
				astarGrp.GetNoAuth("/game/roundcheck", c.astarSwSrvice.GameRoundCheck)
				astarGrp.PostNoAuth("/game/bet/refund", c.astarSwSrvice.Refund)
			}
		}
	}

	if c.threeupSrvice != nil {
		server.Http().PostNoAuth("/api/three_up/settleapi", c.threeupSrvice.CallbackSettle)
		server.Http().PostNoAuth("/api/three_up/acknowledgement", c.threeupSrvice.CallbackAck)
		server.Http().PostNoAuth("/api/three_up/rollbackapi", c.threeupSrvice.CallbackRollback)
		server.Http().PostNoAuth("/api/three_up/deductapi", c.threeupSrvice.CallbackDeduct)
		server.Http().PostNoAuth("/api/three_up/getcclapi", c.threeupSrvice.CallbackBalance)
		server.Http().PostNoAuth("/api/three_up/trackerapi", c.threeupSrvice.CallbackTracker)
		server.Http().PostNoAuth("/api/three_up/pingapi", c.threeupSrvice.CallbackPing)

		//新的回调地址
		server.Http().PostNoAuth("/api/third/three_up/settleapi", c.threeupSrvice.CallbackSettle)
		server.Http().PostNoAuth("/api/third/three_up/acknowledgement", c.threeupSrvice.CallbackAck)
		server.Http().PostNoAuth("/api/third/three_up/rollbackapi", c.threeupSrvice.CallbackRollback)
		server.Http().PostNoAuth("/api/third/three_up/deductapi", c.threeupSrvice.CallbackDeduct)
		server.Http().PostNoAuth("/api/third/three_up/getcclapi", c.threeupSrvice.CallbackBalance)
		server.Http().PostNoAuth("/api/third/three_up/trackerapi", c.threeupSrvice.CallbackTracker)
		server.Http().PostNoAuth("/api/third/three_up/pingapi", c.threeupSrvice.CallbackPing)
	}

	//up体育回调接口
	server.Http().Post("/api/third/threeup_login", c.threeupSrvice.ThreeUpLoginAndRegister)

	server.Http().PostNoAuth("/api/third/TokenLogin", c.TokenLogin)
	server.Http().PostNoAuth("/api/third/GetBalance", c.GetBalance)
	server.Http().PostNoAuth("/api/third/PlaceBet", c.PlaceBet)
	server.Http().PostNoAuth("/api/third/AcceptBet", c.AcceptBet)
	server.Http().PostNoAuth("/api/third/SettleBet", c.SettleBet)
	server.Http().PostNoAuth("/api/third/UnsettleBet", c.UnsettleBet)
	server.Http().PostNoAuth("/api/third/DeclineBet", c.DeclineBet)

	//新的回调地址
	server.Http().PostNoAuth("/api/third/up/GetBalance", c.GetBalance)
	server.Http().PostNoAuth("/api/third/up/PlaceBet", c.PlaceBet)
	server.Http().PostNoAuth("/api/third/up/AcceptBet", c.AcceptBet)
	server.Http().PostNoAuth("/api/third/up/SettleBet", c.SettleBet)
	server.Http().PostNoAuth("/api/third/up/UnsettleBet", c.UnsettleBet)
	server.Http().PostNoAuth("/api/third/up/DeclineBet", c.DeclineBet)

	if xgo.Env() != "dev" {
		go c.get_xiaoyouxi_token()
		go c.get_lottery_games()
	}

	////////////cbk板球单一钱包开始
	//cbk板球 single
	//板球回调URL:https://callback.fpdk0.live/api/third/
	//如板球查询余额:https://callback.fpdk0.live/api/third/cbk/balance
	server.Http().Post("/api/third/cbk_single_login", c.CBKSingle.Login)                //登录
	server.Http().PostNoAuth("/api/third/cbk/check", c.CBKSingle.Check)                 //检查用户状态
	server.Http().PostNoAuth("/api/third/cbk/balance", c.CBKSingle.Balance)             //余额查询
	server.Http().PostNoAuth("/api/third/cbk/debit", c.CBKSingle.Debit)                 //扣款
	server.Http().PostNoAuth("/api/third/cbk/credit", c.CBKSingle.Credit)               //结算
	server.Http().PostNoAuth("/api/third/cbk/cancel", c.CBKSingle.Cancel)               //取消
	server.Http().PostNoAuth("/api/third/cbk/data", c.CBKSingle.Data)                   //注单上报
	server.Http().PostNoAuth("/api/third/cbk/resettle", c.CBKSingle.Resettle)           //重新结算
	server.Http().PostNoAuth("/api/third/cbk/get_hot_events", c.CBKSingle.GetHotEvents) //热门赛事

	//沙巴体育
	server.Http().Post("/api/third/saba_single_login", c.SabaService.SabaLoginAndRegister)
	server.Http().PostNoAuth("/api/third/sabasport/getbalance", c.SabaService.GetBalance)
	server.Http().PostNoAuth("/api/third/sabasport/placebet", c.SabaService.Bet) //
	server.Http().PostNoAuth("/api/third/sabasport/confirmbet", c.SabaService.ConfirmBet)
	server.Http().PostNoAuth("/api/third/sabasport/cancelbet", c.SabaService.CancelBet)
	server.Http().PostNoAuth("/api/third/sabasport/settle", c.SabaService.Settle)
	server.Http().PostNoAuth("/api/third/sabasport/resettle", c.SabaService.Resettle)
	server.Http().PostNoAuth("/api/third/sabasport/unsettle", c.SabaService.Unsettle)
	server.Http().PostNoAuth("/api/third/sabasport/healthcheck", c.SabaService.HealthCheck)
	server.Http().PostNoAuth("/api/third/sabasport/adjustbalance", c.SabaService.AdjustBalance)
	server.Http().PostNoAuth("/api/third/sabasport/placebetparlay", c.SabaService.BetParlay)          //
	server.Http().PostNoAuth("/api/third/sabasport/confirmbetparlay", c.SabaService.ConfirmBetParlay) //

	//fb体育
	server.Http().Post("/api/third/fb_single_login", c.FbService.FbLoginAndRegister)                 //登录
	server.Http().PostNoAuth("/api/third/fb/callback/balance", c.FbService.GetBalance)               //获取余额
	server.Http().PostNoAuth("/api/third/fb/callback/order_pay", c.FbService.Bet)                    //下注扣款
	server.Http().PostNoAuth("/api/third/fb/callback/health", c.FbService.HealthCheck)               //心跳检测
	server.Http().PostNoAuth("/api/third/fb/callback/sync_transaction", c.FbService.SyncTransaction) //流水数据推送
	server.Http().PostNoAuth("/api/third/fb/callback/check_order_pay", c.FbService.ConfirmBet)       //查看渠道支付状态
	server.Http().PostNoAuth("/api/third/fb/callback/sync_orders", c.FbService.SyncOrders)           //订单数据推送
	server.Http().PostNoAuth("/api/third/fb/get_hot_events", c.FbService.GetHotEvents)               //FB热门赛事

	//OPS聚合
	server.Http().Post("/api/third/ops_single_login", c.OpsService.Login)                        //登录
	server.Http().PutNoAuth("/api/third/ops/seamless/balance", c.OpsService.GetBalance)          //获取余额
	server.Http().PutNoAuth("/api/third/ops/seamless/bet", c.OpsService.Bet)                     //下注
	server.Http().PutNoAuth("/api/third/ops/seamless/win", c.OpsService.Win)                     //开奖
	server.Http().PutNoAuth("/api/third/ops/seamless/round_status", c.OpsService.GetOrderStatus) //获取订单状态
	server.Http().PutNoAuth("/api/third/ops/seamless/refund", c.OpsService.Refund)               //回退订单

	// FB真人
	if c.FBLiveSingleService != nil {
		server.Http().Post("/api/third/fblive_single_login", c.FBLiveSingleService.Login)                      // 进入游戏
		server.Http().PostNoAuth("/api/third/fblive/v1/get-credit", c.FBLiveSingleService.GetBalance)          // 单个会员余额查询接口
		server.Http().PostNoAuth("/api/third/fblive/v1/bet-confirm", c.FBLiveSingleService.BetConfirm)         // 下注确认回调接口
		server.Http().PostNoAuth("/api/third/fblive/v1/bet-cancel", c.FBLiveSingleService.BetCancel)           // 取消下注回调接口
		server.Http().PostNoAuth("/api/third/fblive/v1/game-payout", c.FBLiveSingleService.GamePayout)         // 派彩回调接口
		server.Http().PostNoAuth("/api/third/fblive/v1/order-change", c.FBLiveSingleService.GameResettle)      // 游戏结算后, 操作取消局、重算局等影响玩家余额的操作
		server.Http().PostNoAuth("/api/third/fblive/v1/health", c.FBLiveSingleService.GetHealth)               // 检查回调地址可用性
		server.Http().PostNoAuth("/api/third/fblive/v1/activity-payout", c.FBLiveSingleService.ActivityPayout) // 活动和小费类回调接口

	}

	// WE真人
	if c.WESingleService != nil {
		server.Http().Post("/api/third/we_single_login", c.WESingleService.Login)                          // 进入游戏
		server.Http().PostNoAuth("/api/third/we/validate", c.WESingleService.Validate)                     // 验证
		server.Http().PostNoAuth("/api/third/we/syncCredit", c.WESingleService.SyncCredit)                 // 单个会员余额查询接口
		server.Http().PostNoAuth("/api/third/we/increaseCredit", c.WESingleService.IncreaseCredit)         // 用于通知渠道更改玩家金额接口
		server.Http().PostNoAuth("/api/third/we/refundSingleWallet", c.WESingleService.RefundSingleWallet) // WE对失败投注進行手动退款请求接口
		server.Http().PostNoAuth("/api/third/we/resettlement", c.WESingleService.Resettlement)             // 该API是注单结算后，WE判断需更改游戏结果时，回传该局必须修正的金额。
		server.Http().PostNoAuth("/api/third/we/netCheck", c.WESingleService.NetCheck)                     // 监控检测渠道的server连线是否正常

	}

	// DG真人
	if c.DGSingleService != nil {
		server.Http().Post("/api/third/dg_single_login", c.DGSingleService.Login)                                  // 进入游戏
		server.Http().PostNoAuth("/api/v2/specification/user/getBalance/:agentName", c.DGSingleService.GetBalance) // 单个会员余额查询接口
		server.Http().PostNoAuth("/api/v2/specification/account/inform/:agentName", c.DGSingleService.Inform)      // 检查并完成处理
		server.Http().PostNoAuth("/api/v2/specification/account/transfer/:agentName", c.DGSingleService.Transfer)  // 转账

	}

	//st8 single
	if c.St8Config != nil {
		server.Http().Post("/api/third/st8_single_login", c.St8Config.GameLaunch)
		server.Http().PostNoAuth("/api/third/st8/check", c.St8Config.Check)
		//https://operator.example/api/st8/player_profile
		server.Http().PostNoAuth("/api/third/st8/player_profile", c.St8Config.PlayerProfile)
		server.Http().PostNoAuth("/api/third/st8/balance", c.St8Config.Balance)
		//https://operator.example/api/st8/debit
		server.Http().PostNoAuth("/api/third/st8/debit", c.St8Config.Debit)
		//https://operator.example/api/st8/credit
		server.Http().PostNoAuth("/api/third/st8/credit", c.St8Config.Credit)
		//https://operator.example/api/st8/cancel
		server.Http().PostNoAuth("/api/third/st8/cancel", c.St8Config.Cancel)
		server.Http().PostNoAuth("/api/third/st8/get_game_list", c.St8Config.GetGameList)
		server.Http().PostNoAuth("/api/third/st8/buyin", c.St8Config.Buyin)
		server.Http().PostNoAuth("/api/third/st8/payout", c.St8Config.Payout)

	}

	//德州棋牌 single
	if c.QQPokerService != nil {
		server.Http().Post("/api/third/qqpoker_single_login", c.QQPokerService.Login)    //登录
		server.Http().PostNoAuth("/api/third/qqpoker/balance", c.QQPokerService.Balance) //余额查询
		server.Http().PostNoAuth("/api/third/qqpoker/debit", c.QQPokerService.Debit)
		server.Http().PostNoAuth("/api/third/qqpoker/credit", c.QQPokerService.Credit)               //增加余额
		server.Http().PostNoAuth("/api/third/qqpoker/get_hot_events", c.QQPokerService.GetHotEvents) //热门赛事
	}

	//日本弹珠 single
	if c.PachinkoService != nil {
		server.Http().Post("/api/third/pachinko_single_login", c.PachinkoService.Login)      //登录
		server.Http().PostNoAuth("/api/third/pachinko/cmd", c.PachinkoService.TradingPoints) //余额获取
	}

	// OFA真人
	if c.OFALiveSingleService != nil {
		server.Http().Post("/api/third/ofalive_single_login", c.OFALiveSingleService.Login)        // 进入游戏
		server.Http().PostNoAuth("/api/third/ofa/Balance", c.OFALiveSingleService.Balance)         // 查询余额
		server.Http().PostNoAuth("/api/third/ofa/Bet", c.OFALiveSingleService.Bet)                 // 投注
		server.Http().PostNoAuth("/api/third/ofa/BetResult", c.OFALiveSingleService.BetResult)     // 结算
		server.Http().PostNoAuth("/api/third/ofa/CancelBet", c.OFALiveSingleService.CancelBet)     // 取消投注
		server.Http().PostNoAuth("/api/third/ofa/ReBetResult", c.OFALiveSingleService.ReBetResult) // 重新结算
		server.Http().PostNoAuth("/api/third/ofa/Gift", c.OFALiveSingleService.Gift)               // 送礼
	}

	// IMOne游戏
	if c.IMOneSingleService != nil {
		server.Http().Post("/api/third/imone_single_login", c.IMOneSingleService.Login) // 进入游戏
		// IMOne营运商钱包回调接口
		server.Http().PostNoAuth("/api/third/imone/GetBalance", c.IMOneSingleService.GetBalance) // 查询余额
		server.Http().PostNoAuth("/api/third/imone/PlaceBet", c.IMOneSingleService.PlaceBet)     // 下注
		server.Http().PostNoAuth("/api/third/imone/SettleBet", c.IMOneSingleService.SettleBet)   // 结算

		server.Http().PostNoAuth("/api/third/imone/Refund", c.IMOneSingleService.Refund)                         // 退款/取消
		server.Http().PostNoAuth("/api/third/imone/FreeRoundPlaceBet", c.IMOneSingleService.FreeRoundPlaceBet)   // 免费回合下注
		server.Http().PostNoAuth("/api/third/imone/FreeRoundWinResult", c.IMOneSingleService.FreeRoundWinResult) // 免费回合结果
		server.Http().PostNoAuth("/api/third/imone/FreezeWithdrawal", c.IMOneSingleService.FreezeWithdrawal)     // 冻结/解冻提款
		server.Http().PostNoAuth("/api/third/imone/GetApproval", c.IMOneSingleService.GetApproval)               // 索取批准
	}
	server.Http().Post("/api/third/wali_login", c.WaliClient.GetLoginUrl)  //Wali游戏登录
	server.Http().Post("/api/third/tfun_login", c.TGFUNClient.GetLoginUrl) //tfun游戏登录

	//sw真人 single
	if c.ABSingleService != nil {
		server.Http().Post("/api/third/ab_single_login", c.ABSingleService.Login)                  //登录
		server.Http().GetNoAuth("/api/third/sw/GetBalance/:uid", c.ABSingleService.GetBalance)     //余额获取
		server.Http().PostNoAuth("/api/third/sw/Transfer", c.ABSingleService.Transfer)             //转额
		server.Http().PostNoAuth("/api/third/sw/CancelTransfer", c.ABSingleService.CancelTransfer) //取消转额
		server.Http().PostNoAuth("/api/third/sw/GetGameTables", c.ABSingleService.GetGameTables)   //查询桌台

	}

	// SA真人
	if c.SASingleService != nil {
		server.Http().Post("/api/third/salive_single_login", c.SASingleService.Login)         // 进入游戏
		server.Http().PostNoAuth("/api/third/sa/GetUserBalance", c.SASingleService.Balance)   // 查询余额
		server.Http().PostNoAuth("/api/third/sa/PlaceBet", c.SASingleService.Bet)             // 投注
		server.Http().PostNoAuth("/api/third/sa/PlayerWin", c.SASingleService.BetResult)      // 结算
		server.Http().PostNoAuth("/api/third/sa/PlayerLost", c.SASingleService.BetResult)     // 结算
		server.Http().PostNoAuth("/api/third/sa/PlaceBetCancel", c.SASingleService.CancelBet) // 取消投注
	}

	// RSG电子
	if c.RSGSingleService != nil {
		server.Http().Post("/api/third/rsg_single_login", c.RSGSingleService.Login)          // 进入游戏
		server.Http().PostNoAuth("/api/third/rsg/GetBalance", c.RSGSingleService.Balance)    // 查询余额
		server.Http().PostNoAuth("/api/third/rsg/Bet", c.RSGSingleService.Bet)               // 投注
		server.Http().PostNoAuth("/api/third/rsg/BetResult", c.RSGSingleService.BetResult)   // 结算
		server.Http().PostNoAuth("/api/third/rsg/JackpotResult", c.RSGSingleService.Jackpot) // 中将
		server.Http().PostNoAuth("/api/third/rsg/CancelBet", c.RSGSingleService.CancelBet)   // 取消投注

		//捕鱼类
		server.Http().PostNoAuth("/api/third/rsg/Prepay", c.RSGSingleService.Prepay)                     // 预扣
		server.Http().PostNoAuth("/api/third/rsg/CheckTransaction", c.RSGSingleService.CheckTransaction) // 检查交易
		server.Http().PostNoAuth("/api/third/rsg/Refund", c.RSGSingleService.Refund)                     // 退款
	}

	// VG真人
	if c.VGSingleService != nil {
		server.Http().Post("/api/third/vglive_single_login", c.VGSingleService.Login) // VG live 登入
		vgGroup := server.Http().NewGroup("/api/third/vglive")
		{
			vgGroup.PostNoAuth("/balance", c.VGSingleService.Balance)   // 余额查询
			vgGroup.PostNoAuth("/bet", c.VGSingleService.Bet)           // 下注
			vgGroup.PostNoAuth("/cancel", c.VGSingleService.Cancel)     // 取消投注
			vgGroup.PostNoAuth("/win", c.VGSingleService.Win)           // 派彩
			vgGroup.PostNoAuth("/resettle", c.VGSingleService.ReSettle) // 重新派彩
		}
	}
	server.Http().PutNoAuth("/api/third/callback/twitter", c.ThreeParyLonginService.Refund) //twitter回调地址
	server.Http().PutNoAuth("/api/third/callback/line", c.ThreeParyLonginService.Refund)    //twitter回调地址
}

func (c *ThirdController) get_xiaoyouxi_token() {
	if len(c.xiaoyouxi_url) == 0 {
		return
	}
	for {
		params := req.Param{}
		params["merchant_code"] = c.xiaoyouxi_merchant_code
		params["secure_key"] = c.xiaoyouxi_secure_key
		data, _ := c.xiaoyouxi_http_req("post", "/gameapi/v2/generate_token", &params)
		if data != nil {
			jdata := (*data)["detail"].(map[string]interface{})
			c.xiaoyouxi_token = jdata["auth_token"].(string)
			time.Sleep(time.Second * 300)
		}
		time.Sleep(time.Second * 600)
	}
}

func (c *ThirdController) get_lottery_games() {
	if len(c.lottery_url) == 0 {
		return
	}
	for {
		reqstr := fmt.Sprintf(`{"Merchantkey": "%s"}`, c.lottery_key)
		jdata, _ := c.lottery_http_get("/Account/GetGameInfoData", reqstr, "")
		c.lotterygames = jdata
		time.Sleep(time.Second * 600)
	}
}

const (
	TRANSFER_DIR_OUT = 1
	TRANSFER_DIR_IN  = 2
)

const (
	TRANSFER_PLATFORM_HASH_LOTTERY = 1
	TRANSFER_PLATFORM_GAMES_GFG    = 2
	TRANSFER_PLATFORM_LAOBANNIANG  = 3
	TRANSFER_PLATFORM_LIVE_WM      = 4
	TRANSFER_PLATFORM_LIVE_EVO     = 5
	//TRANSFER_PLATFORM_GAMES_PG     = 6
	//TRANSFER_PLATFORM_GAMES_PP     = 7
	TRANSFER_PLATFORM_GAMES_AG    = 8
	TRANSFER_PLATFORM_GAMES_UP    = 9
	TRANSFER_PLATFORM_GAMES_ASTAR = 10
	TRANSFER_PLATFORM_GAMES_IDN   = 11
	TRANSFER_PLATFORM_WICKETS     = 12 // 9Wickets
	TRANSFER_PLATFORM_WALI        = 13 // Wali游戏
	TRANSFER_PLATFORM_TGFUN       = 14 //TGFUN游戏
)

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 转入转出 1彩票,2gfg棋牌电子,3小游戏,4mw视讯,5evo视讯,6pg电子,7pp电子,8ag
// ///////////////////////////////////////////////////////////////////////////////////////////////////////////////
func (c *ThirdController) transfer_check(platform int, UserId, id int, dir int, amount float64, Symbol string) error {
	if platform == TRANSFER_PLATFORM_HASH_LOTTERY {
		reqstr := fmt.Sprintf(`{"Merchantkey": "%s","OrderNumber": "%d"}`, c.lottery_key, id)
		jdata, err := c.lottery_http_get("/Account/GetOrderNumber", reqstr, "")
		if err == nil {
			if jdata == nil {
				logs.Error("查询订单失败:" + reqstr)
				return errors.New("查询订单失败")
			} else {
				if (*jdata)["Data"] == nil {
					server.Db().CallProcedure("x_api_transfer_update", id, 3) //订单不存在,订单失败
				} else {
					server.Db().CallProcedure("x_api_transfer_update", id, 2) //订单存在,订单成功
				}
			}
		} else {
			logs.Error("/Account/GetOrderNumber", err)
			return errors.New("查询订单失败")
		}
	} else if platform == TRANSFER_PLATFORM_LAOBANNIANG {
		params := make(req.Param)
		params["merchant_code"] = c.xiaoyouxi_merchant_code
		params["auth_token"] = c.xiaoyouxi_token
		params["username"] = fmt.Sprint(UserId)
		params["external_trans_id"] = fmt.Sprint(id)
		jdata, err := c.xiaoyouxi_http_req("get", "/gameapi/v2/query_transaction", &params)
		if err == nil {
			if jdata == nil {
				server.Db().CallProcedure("x_api_transfer_update", id, 3)
			} else {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		} else {
			logs.Error("/gameapi/v2/query_transaction", err)
			return errors.New("查询订单失败")
		}
	} else if platform == TRANSFER_PLATFORM_LIVE_WM {
		data := gin.H{
			"user":      fmt.Sprintf("%d", UserId),
			"order":     fmt.Sprintf("%d", id),
			"timestamp": time.Now().Unix(),
		}
		jdata, err := c.wm_http_post("GetMemberTradeReport", data)
		if err == nil {
			if jdata == nil {
				server.Db().CallProcedure("x_api_transfer_update", id, 3)
			} else {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		} else {
			logs.Error("GetMemberTradeReport", err)
			return errors.New("查询订单失败")
		}
	} else if platform == TRANSFER_PLATFORM_WICKETS {
		// 检查转账记录

		tsCode := fmt.Sprintf("%d", id)
		status, err := c.checkTransferRecord(UserId, tsCode)
		if err != nil {
			logs.Error("wickets transfer_check failed:", err)

			// 根据状态码决定操作
			if status == 2 {
				// 订单失败
				server.Db().CallProcedure("x_api_transfer_update", id, 3)
				return fmt.Errorf("wickets check failed: %v", err)
			} else if status == 3 {
				// 订单处理中，不更新状态，等待下次检查
				return fmt.Errorf("订单处理中，请稍后再试")
			} else {
				// 网络错误或其它，不更新状态，等待后续检查
				return fmt.Errorf("网络错误，请稍后再试: %v", err)
			}
		}
		// 订单成功
		if status == 1 {
			server.Db().CallProcedure("x_api_transfer_update", id, 2)
		}
	} else if platform == TRANSFER_PLATFORM_WALI {
		// 检查Wali订单状态
		orderId := fmt.Sprintf("%d", id)
		status, err := c.WaliClient.QueryOrder(orderId, UserId)
		if err != nil {
			logs.Error("wali transfer_check failed:", err)

			// 根据状态码决定操作
			if status == 2 {
				// 订单失败
				server.Db().CallProcedure("x_api_transfer_update", id, 3)
				return fmt.Errorf("查询订单失败: %v", err)
			} else if status == 3 {
				// 订单处理中，不更新状态，等待下次检查
				return fmt.Errorf("订单处理中，请稍后再试")
			} else {
				// 网络错误或其它，不更新状态，等待后续检查
				return fmt.Errorf("网络错误，请稍后再试: %v", err)
			}
		}
		// 订单成功
		if status == 1 {
			server.Db().CallProcedure("x_api_transfer_update", id, 2)
		}
	} else if platform == TRANSFER_PLATFORM_TGFUN {
		// 检查订单状态
		orderId := fmt.Sprintf("%d", id)
		status, err := c.TGFUNClient.QueryOrder(orderId, UserId)
		if err != nil {
			logs.Error("tfun transfer_check failed:", err)

			// 根据状态码决定操作
			if status == 2 {
				// 订单失败
				server.Db().CallProcedure("x_api_transfer_update", id, 3)
				return fmt.Errorf("查询订单失败: %v", err)
			} else if status == 3 {
				// 订单处理中，不更新状态，等待下次检查
				return fmt.Errorf("订单处理中，请稍后再试")
			} else {
				// 网络错误或其它，不更新状态，等待后续检查
				return fmt.Errorf("网络错误，请稍后再试: %v", err)
			}
		}
		// 订单成功
		if status == 1 {
			server.Db().CallProcedure("x_api_transfer_update", id, 2)
		}
	}
	return nil

}

func (c *ThirdController) third_transfer_out(platform int, UserId int, Symbol string) error {

	//手动释放
	defer func() {
		//手动释放锁
		rediskey2 := fmt.Sprintf("%v:%v:third_login_lock2_%v", server.Project(), server.Module(), UserId)
		server.Redis().Del(rediskey2)
	}()

	data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, 0, TRANSFER_DIR_OUT, platform, Symbol)
	if err != nil {
		logs.Error("third_transfer_out1", err)
		return err
	}
	errcode := abugo.GetInt64FromInterface((*data)["errcode"])
	if errcode == 10 {
		logs.Error("third_transfer_out2", errcode)
		return errors.New("操作频繁,请稍后再试")
	}
	if errcode == 3 {
		logs.Error("余额不足:", platform, UserId, Symbol)
		return nil //余额不足,不用做任何事情
	}
	if errcode == 2 { //有未完成订单,先完成为完成订单
		id := abugo.GetInt64FromInterface((*data)["Id"])
		dbplatform := abugo.GetInt64FromInterface((*data)["Platform"])
		dir := abugo.GetInt64FromInterface((*data)["Dir"])
		amount := abugo.GetFloat64FromInterface((*data)["Amount"])
		err = c.transfer_check(int(dbplatform), UserId, int(id), int(dir), amount, Symbol)
		if err != nil {
			logs.Error("third_transfer_out3:", err)
			return err
		}
		data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, 0, TRANSFER_DIR_OUT, dbplatform, Symbol)
		if err != nil {
			logs.Error("third_transfer_out4:", err)
			return err
		}
	}
	errcode = abugo.GetInt64FromInterface((*data)["errcode"])
	if errcode == 10 {
		logs.Error("third_transfer_out5:", errcode)
		return errors.New("操作频繁,请稍后再试")
	}
	if errcode == 3 {
		return nil //余额不足,不用做任何事情
	}
	if (*data)["Id"] != nil && (*data)["Amount"] != nil {
		id := abugo.GetInt64FromInterface((*data)["Id"])
		amount := abugo.GetFloat64FromInterface((*data)["Amount"])
		amount = math.Floor(amount*10000) / 10000
		if platform == TRANSFER_PLATFORM_HASH_LOTTERY {
			reqstr := fmt.Sprintf(`{"Merchantkey": "%s","Account": "%d","OrderNumber":"%d","Amount":%.4f}`, c.lottery_key, UserId, id, amount)
			jdata, err := c.lottery_http_get("/Account/GetDeposit", reqstr, "")
			if err == nil {
				if jdata == nil {
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return errors.New("请求失败,请稍后再试")
				} else {
					if (*jdata)["Success"].(bool) == true {
						server.Db().CallProcedure("x_api_transfer_update", id, 2)
					} else {
						server.Db().CallProcedure("x_api_transfer_update", id, 3)
						if (*jdata)["Message"].(string) != "登录名不存在" {
							return errors.New("请求失败,请稍后再试")
						}
					}
				}
			} else {
				return errors.New("请求失败,请稍后再试")
			}
		} else if platform == TRANSFER_PLATFORM_LAOBANNIANG {
			params := make(req.Param)
			params["merchant_code"] = c.xiaoyouxi_merchant_code
			params["auth_token"] = c.xiaoyouxi_token
			params["username"] = fmt.Sprint(UserId)
			params["action_type"] = "deposit"
			params["amount"] = amount
			params["external_trans_id"] = fmt.Sprint(id)
			jdata, err := c.xiaoyouxi_http_req("post", "/gameapi/v2/transfer_player_fund", &params)
			if err == nil {
				if jdata == nil {
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return errors.New("请求失败,请稍后再试")
				} else {
					server.Db().CallProcedure("x_api_transfer_update", id, 2)
					return nil
				}
			}
		} else if platform == TRANSFER_PLATFORM_LIVE_WM {
			data := gin.H{
				"user":      fmt.Sprintf("%d", UserId),
				"money":     amount,
				"order":     fmt.Sprintf("%d", id),
				"timestamp": time.Now().Unix(),
			}
			jdata, err := c.wm_http_post("ChangeBalance", data)
			if err == nil {
				if jdata == nil {
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					logs.Error("wm改完玩家余额失败", err)
					return errors.New("请求失败,请稍后再试")
				}
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		} else if platform == TRANSFER_PLATFORM_WICKETS {
			// 调用ThirdControllerWickets中的转出方法
			status, err := c.wickets_transfer_deposit(UserId, amount, fmt.Sprint(id))
			if err != nil {
				logs.Error(fmt.Sprintf("Wickets TransferDeposit 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))

				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return err
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试: %v", err)
				}
			}
			// 转账成功
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		} else if platform == TRANSFER_PLATFORM_WALI {
			// 调用ThirdControllerWali中的转出方法
			status, err := c.WaliClient.TransferDeposit(UserId, amount, fmt.Sprint(id))
			if err != nil {
				logs.Error(fmt.Sprintf("Wali TransferDeposit 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))
				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return err
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试: %v", err)
				}
			}
			// 转账成功
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		} else if platform == TRANSFER_PLATFORM_TGFUN {
			// 调用转出方法
			status, err := c.TGFUNClient.TransferDeposit(UserId, amount, fmt.Sprint(id))
			if err != nil {
				logs.Error(fmt.Sprintf("TGFUN TransferDeposit 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))

				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return err
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试: %v", err)
				}
			}
			// 转账成功
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		}

	}
	return nil
}

func (c *ThirdController) third_transfer_in(platform int, UserId int, Symbol string) error {
	if platform == TRANSFER_PLATFORM_HASH_LOTTERY {
		reqstr := fmt.Sprintf(`{"Merchantkey": "%s","Account": "%d"}`, c.lottery_key, UserId)
		jdata, err := c.lottery_http_get("/Account/GetUserInfo", reqstr, "")
		if err == nil && jdata != nil {
			if (*jdata)["Success"].(bool) {
				subdata := (*jdata)["Data"].(map[string]interface{})
				balance := abugo.GetFloat64FromInterface(subdata["Balance"])
				if balance <= 0 {
					return nil
				}
				data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
				if err != nil {
					logs.Error(err)
					return err
				}
				errcode := abugo.GetInt64FromInterface((*data)["errcode"])
				if errcode == 10 {
					return errors.New("操作频繁,请稍后再试")
				}
				if errcode == 3 {
					return nil // 余额不足,不用做任何事情
				}
				if errcode == 2 { //有未完成订单,先清除我未完成订单
					id := abugo.GetInt64FromInterface((*data)["Id"])
					dir := abugo.GetInt64FromInterface((*data)["Dir"])
					amount := abugo.GetFloat64FromInterface((*data)["Amount"])
					err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
					if err != nil {
						logs.Error(err)
						return err
					}
					data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
					if err != nil {
						logs.Error(err)
						return err
					}
					errcode := abugo.GetInt64FromInterface((*data)["errcode"])
					if errcode == 10 {
						return errors.New("操作频繁,请稍后再试")
					}
					if errcode != 0 {
						return errors.New(fmt.Sprint("x_api_transfer_create 失败1:", errcode))
					}
				}
				if (*data)["Id"] != nil && (*data)["Amount"] != nil {
					id := abugo.GetInt64FromInterface((*data)["Id"])
					amount := abugo.GetFloat64FromInterface((*data)["Amount"])
					reqstr := fmt.Sprintf(`{"Merchantkey": "%s","Account": "%d","OrderNumber":"%d","Amount":%.4f}`, c.lottery_key, UserId, id, amount)
					jdata, err := c.lottery_http_get("/Account/GetWithDrwal", reqstr, "")
					if err == nil {
						if jdata == nil {
							server.Db().CallProcedure("x_api_transfer_update", id, 3)
							return errors.New("请求失败,请稍后再试")
						} else {
							if (*jdata)["Success"].(bool) == true {
								server.Db().CallProcedure("x_api_transfer_update", id, 2)
								return nil
							} else {
								logs.Error(jdata)
								server.Db().CallProcedure("x_api_transfer_update", id, 3)
								return errors.New("请求失败,请稍后再试")
							}
						}
					} else {
						return errors.New("请求失败,请稍后再试")
					}
				}
			}
		}
	} else if platform == TRANSFER_PLATFORM_LAOBANNIANG {
		params := make(req.Param)
		params["auth_token"] = c.xiaoyouxi_token
		params["merchant_code"] = c.xiaoyouxi_merchant_code
		params["username"] = fmt.Sprint(UserId)
		jdata, err := c.xiaoyouxi_http_req("get", "/gameapi/v2/query_player_balance", &params)
		if err == nil && jdata != nil {
			balance := abugo.GetFloat64FromInterface((*jdata)["detail"].(map[string]interface{})["game_platform_balance"])
			balance = math.Floor(balance*10000) / 10000
			if balance < 0.0001 {
				return nil
			}
			data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			errcode := abugo.GetInt64FromInterface((*data)["errcode"])
			if errcode == 10 {
				return errors.New("操作频繁,请稍后再试")
			}
			if errcode == 3 {
				return nil // 余额不足,不用做任何事情
			}
			if errcode == 2 { //有未完成订单,先清除我未完成订单
				id := abugo.GetInt64FromInterface((*data)["Id"])
				dir := abugo.GetInt64FromInterface((*data)["Dir"])
				amount := abugo.GetFloat64FromInterface((*jdata)["Amount"])
				err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
				if err != nil {
					logs.Error(err)
					return err
				}
				data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
				if err != nil {
					logs.Error(err)
					return err
				}
				errcode := abugo.GetInt64FromInterface((*data)["errcode"])
				if errcode == 10 {
					return errors.New("操作频繁,请稍后再试")
				}
				if errcode != 0 {
					return errors.New(fmt.Sprint("x_api_transfer_create 失败2:", errcode))
				}
			}
			if (*data)["Id"] != nil && (*data)["Amount"] != nil {
				id := abugo.GetInt64FromInterface((*data)["Id"])
				amount := abugo.GetFloat64FromInterface((*data)["Amount"])
				amount = math.Floor(amount*10000) / 10000
				amount, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", amount), 64)

				params := make(req.Param)
				params["merchant_code"] = c.xiaoyouxi_merchant_code
				params["auth_token"] = c.xiaoyouxi_token
				params["username"] = fmt.Sprint(UserId)
				params["action_type"] = "withdraw"
				params["amount"] = amount
				params["external_trans_id"] = fmt.Sprint(id)
				jdata, err := c.xiaoyouxi_http_req("post", "/gameapi/v2/transfer_player_fund", &params)
				if err == nil {
					if jdata == nil {
						server.Db().CallProcedure("x_api_transfer_update", id, 3)
						return errors.New("请求失败,请稍后再试")
					} else {
						server.Db().CallProcedure("x_api_transfer_update", id, 2)
						return nil
					}
				}
			}
		}
	} else if platform == TRANSFER_PLATFORM_LIVE_WM {
		data := gin.H{
			"user":      fmt.Sprintf("%d", UserId),
			"timestamp": time.Now().Unix(),
		}
		jdata, err := c.wm_http_post("GetBalance", data)
		if err != nil {
			return err
		}
		if jdata != nil {
			balance := abugo.GetFloat64FromInterface((*jdata)["result"])
			if balance <= 0 {
				return nil
			}
			data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			errcode := abugo.GetInt64FromInterface((*data)["errcode"])
			if errcode == 10 {
				return errors.New("操作频繁,请稍后再试")
			}
			if errcode == 3 {
				return nil // 余额不足,不用做任何事情
			}
			if errcode == 2 { //有未完成订单,先清除我未完成订单
				id := abugo.GetInt64FromInterface((*data)["Id"])
				dir := abugo.GetInt64FromInterface((*data)["Dir"])
				amount := abugo.GetFloat64FromInterface((*jdata)["Amount"])
				err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
				if err != nil {
					logs.Error(err)
					return err
				}
				data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
				if err != nil {
					logs.Error(err)
					return err
				}
				errcode := abugo.GetInt64FromInterface((*data)["errcode"])
				if errcode == 10 {
					return errors.New("操作频繁,请稍后再试")
				}
				if errcode != 0 {
					return errors.New(fmt.Sprint("x_api_transfer_create 失败2:", errcode))
				}
			}
			if (*data)["Id"] != nil && (*data)["Amount"] != nil {
				id := abugo.GetInt64FromInterface((*data)["Id"])
				amount := abugo.GetFloat64FromInterface((*data)["Amount"])
				data := gin.H{
					"user":      fmt.Sprintf("%d", UserId),
					"money":     -amount,
					"order":     fmt.Sprintf("%d", id),
					"timestamp": time.Now().Unix(),
				}
				jdata, err := c.wm_http_post("ChangeBalance", data)
				if err == nil {
					if jdata == nil {
						server.Db().CallProcedure("x_api_transfer_update", id, 3)
						return errors.New("请求失败,请稍后再试")
					}
					server.Db().CallProcedure("x_api_transfer_update", id, 2)
				}
			}
		}
	} else if platform == TRANSFER_PLATFORM_WICKETS {
		// 获取用户余额
		balance, err := c.wickets_get_balance(UserId)
		if err != nil {
			return err
		}
		if balance <= 0 {
			return nil
		}

		// 创建转入订单
		data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
		if err != nil {
			logs.Error(err)
			return err
		}

		// 处理订单状态
		errcode := abugo.GetInt64FromInterface((*data)["errcode"])
		if errcode == 10 {
			return errors.New("操作频繁,请稍后再试")
		}
		if errcode == 3 {
			return nil // 余额不足,不用做任何事情
		}
		if errcode == 2 { //有未完成订单,先清除我未完成订单
			id := abugo.GetInt64FromInterface((*data)["Id"])
			dir := abugo.GetInt64FromInterface((*data)["Dir"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])
			err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			errcode := abugo.GetInt64FromInterface((*data)["errcode"])
			if errcode == 10 {
				return errors.New("操作频繁,请稍后再试")
			}
			if errcode != 0 {
				return errors.New(fmt.Sprint("x_api_transfer_create 失败2:", errcode))
			}
		}

		// 检查订单ID和金额是否存在
		if (*data)["Id"] != nil && (*data)["Amount"] != nil {
			// 获取订单ID和金额
			id := abugo.GetInt64FromInterface((*data)["Id"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])

			// 调用ThirdControllerWickets中的转入方法
			status, err := c.wickets_transfer_withdraw(UserId, amount, fmt.Sprint(id))
			if err != nil {
				logs.Error(fmt.Sprintf("Wickets TransferWithdraw 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))

				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return err
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试: %v", err)
				}
			}
			// 成功时更新订单状态
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		}
	} else if platform == TRANSFER_PLATFORM_WALI {
		// 获取用户余额
		balance, err := c.WaliClient.GetBalance(UserId)
		if err != nil {
			return err
		}
		if balance <= 0 {
			return nil
		}

		// 创建转入订单
		data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
		if err != nil {
			logs.Error(err)
			return err
		}

		// 处理订单状态
		errcode := abugo.GetInt64FromInterface((*data)["errcode"])
		if errcode == 10 {
			return errors.New("操作频繁,请稍后再试")
		}
		if errcode == 3 {
			return nil // 余额不足,不用做任何事情
		}
		if errcode == 2 { //有未完成订单,先清除我未完成订单
			id := abugo.GetInt64FromInterface((*data)["Id"])
			dir := abugo.GetInt64FromInterface((*data)["Dir"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])
			err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			errcode := abugo.GetInt64FromInterface((*data)["errcode"])
			if errcode == 10 {
				return errors.New("操作频繁,请稍后再试")
			}
			if errcode != 0 {
				return errors.New(fmt.Sprint("x_api_transfer_create 失败2:", errcode))
			}
		}

		// 检查订单ID和金额是否存在
		if (*data)["Id"] != nil && (*data)["Amount"] != nil {
			// 获取订单ID和金额
			id := abugo.GetInt64FromInterface((*data)["Id"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])

			// 调用ThirdControllerWali中的转入方法
			status, err := c.WaliClient.TransferWithdraw(UserId, amount, fmt.Sprint(id))
			if err != nil {
				// 记录错误
				logs.Error(fmt.Sprintf("Wali TransferWithdraw 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))
				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return errors.New("请求失败,请稍后再试")
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试")
				}
			}
			// 成功时更新订单状态
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		}
	} else if platform == TRANSFER_PLATFORM_TGFUN {
		// 获取用户余额
		balance, err := c.TGFUNClient.GetBalance(UserId)
		if err != nil {
			return err
		}
		if balance <= 0 {
			return nil
		}

		// 创建转入订单
		data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
		if err != nil {
			logs.Error(err)
			return err
		}

		// 处理订单状态
		errcode := abugo.GetInt64FromInterface((*data)["errcode"])
		if errcode == 10 {
			return errors.New("操作频繁,请稍后再试")
		}
		if errcode == 3 {
			return nil // 余额不足,不用做任何事情
		}
		if errcode == 2 { //有未完成订单,先清除我未完成订单
			id := abugo.GetInt64FromInterface((*data)["Id"])
			dir := abugo.GetInt64FromInterface((*data)["Dir"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])
			err = c.transfer_check(platform, UserId, int(id), int(dir), amount, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			data, err = server.Db().CallProcedure("x_api_transfer_create", UserId, balance, TRANSFER_DIR_IN, platform, Symbol)
			if err != nil {
				logs.Error(err)
				return err
			}
			errcode := abugo.GetInt64FromInterface((*data)["errcode"])
			if errcode == 10 {
				return errors.New("操作频繁,请稍后再试")
			}
			if errcode != 0 {
				return errors.New(fmt.Sprint("x_api_transfer_create 失败2:", errcode))
			}
		}

		// 检查订单ID和金额是否存在
		if (*data)["Id"] != nil && (*data)["Amount"] != nil {
			// 获取订单ID和金额
			id := abugo.GetInt64FromInterface((*data)["Id"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])

			// 调用TGFUNClient中的转入方法
			status, err := c.TGFUNClient.TransferWithdraw(UserId, amount, fmt.Sprint(id))
			if err != nil {
				// 记录错误
				logs.Error(fmt.Sprintf("TGFUN TransferWithdraw 失败 - 用户ID: %d, 订单号: %d, 错误: %v, 状态码: %d",
					UserId, id, err, status))

				// 根据状态码决定操作
				if status == 2 {
					// 订单失败
					server.Db().CallProcedure("x_api_transfer_update", id, 3)
					return errors.New("请求失败,请稍后再试")
				} else if status == 3 {
					// 订单处理中，不更新状态，等待后续检查
					return fmt.Errorf("订单处理中，请稍后再试")
				} else {
					// 网络错误或其它，不更新状态，等待后续检查
					return fmt.Errorf("网络错误，请稍后再试")
				}
			}
			// 成功时更新订单状态
			if status == 1 {
				server.Db().CallProcedure("x_api_transfer_update", id, 2)
			}
		}
	}

	return nil
}

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 刷新余额
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
func (c *ThirdController) sync_amount(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	errcode := 0

	if lck != nil {
		errcode = 1000001
		ctx.RespErr(errors.New("操作频繁,请稍后再试"), &errcode)
		return
	}
	err := c.sync_amountex(token.UserId, 0)
	if err != nil {
		if err.Error() == "服务器繁忙" {
			errcode = 1000001
		}
		ctx.RespErr(err, &errcode)
		return
	}
	data, _ := server.Db().Query("select Amount from x_user where UserId = ?", []interface{}{token.UserId})

	//var user struct {
	//	Amount      float64 `gorm:"column:Amount"`      // 当前USDT余额
	//	BonusAmount float64 `gorm:"column:BonusAmount"` // 当前Bonus余额
	//}
	//err = server.Db().GormDao().Table("x_user").
	//	Select("Amount, BonusAmount").
	//	Where("UserId = ?", token.UserId).
	//	Scan(&user).Error
	//if err != nil {
	//	logs.Error("查询玩家余额发生错误,userId=", token.UserId, err)
	//}

	//手动释放锁
	rediskey2 := fmt.Sprintf("%v:%v:third_login_lock2_%v", server.Project(), server.Module(), token.UserId)
	server.Redis().Del(rediskey2)

	ctx.RespOK(data)
}

func (c *ThirdController) sync_amountex(UserId int, _ int) error {
	defer func() {
		recover()
	}()

	{
		//加锁
		rediskey := fmt.Sprintf("%v:%v:third_login_lock2_%v", server.Project(), server.Module(), UserId)
		lck := server.Redis().SetNxString(rediskey, "1", 5*60)
		if lck != nil {

			return errors.New("服务器繁忙")
		}
	}

	{
		data, err := server.Db().CallProcedure("x_api_transfer_create", UserId, 0, TRANSFER_DIR_IN, 0, "")
		if err != nil {
			return err
		}
		dberrcode := abugo.GetInt64FromInterface((*data)["errcode"])
		if dberrcode == 10 {
			return errors.New("操作频繁,请稍后再试")
		}
		if dberrcode == 2 {
			id := abugo.GetInt64FromInterface((*data)["Id"])
			platform := abugo.GetInt64FromInterface((*data)["Platform"])
			dir := abugo.GetInt64FromInterface((*data)["Dir"])
			amount := abugo.GetFloat64FromInterface((*data)["Amount"])
			symbol := abugo.InterfaceToString((*data)["Symbol"])
			err = c.transfer_check(int(platform), UserId, int(id), int(dir), amount, symbol)
			if err != nil {
				return err
			}
		}
	}

	data, _ := server.Db().Query("SELECT * FROM x_amount_sync WHERE UserId = ? ORDER BY Id DESC limit 1", []interface{}{UserId})
	if data == nil {
		return nil
	}
	if len(*data) == 0 {
		return nil
	}

	lastplatform := abugo.InterfaceToInt((*data)[0]["Platform"])
	lastsymbol := abugo.InterfaceToString((*data)[0]["Symbol"])

	c.third_transfer_in(int(lastplatform), UserId, lastsymbol)
	return nil
}

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 哈希彩票
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
func (c *ThirdController) lottery_game(ctx *abugo.AbuHttpContent) {
	ctx.RespOK(c.lotterygames)
}

func (c *ThirdController) lottery_sign(reqStr string) (string, string) {
	EncryptPassword := "LXDCKEY1"
	EncryptionOffset := "LXDCKEY2"
	key := []byte(EncryptPassword) // des加密密码
	iv := []byte(EncryptionOffset) // des加密偏移量
	out, _ := abugo.DesCbcEncrypt([]byte(reqStr), key, iv)
	deByte := abugo.Base64Encode(out)
	encryptedreqdata := string(deByte)                   // des加密后的密文
	encryptedreqdata = url.QueryEscape(encryptedreqdata) // url编码后的密文
	hashmd5 := md5.Sum([]byte(reqStr + c.lottery_key))
	hashedkey := hex.EncodeToString(hashmd5[:])
	return encryptedreqdata, hashedkey
}

func (c *ThirdController) lottery_http_get(path string, reqstr string, other string) (*map[string]interface{}, error) {
	param, sign := c.lottery_sign(reqstr)
	url := fmt.Sprintf("%s%s?param=%s&key=%s%s", c.lottery_url, path, param, sign, other)
	resp, err := req.Get(url)
	if err != nil {
		logs.Error("lottery_http_get request error:", path, reqstr, err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("lottery_http_get body error:", err)
		return nil, err
	}
	// logs.Debug("lottery_http_get:", url, "|", reqstr, "|", string(body))
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("lottery_http_get json error:", err)
		return nil, err
	}
	return &jdata, nil
}

func (c *ThirdController) lottery_login(ctx *abugo.AbuHttpContent) {
	if len(c.lottery_url) == 0 {
		return
	}

	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"` //语言代码
	}

	errcode := 0
	reqdata := RequestData{}
	ctx.RequestData(&reqdata)

	token := server.GetToken(ctx)

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)
	c.sync_amountex(token.UserId, TRANSFER_PLATFORM_HASH_LOTTERY)
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, nil)
		data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
		if data == nil {
			ctx.RespErrString(true, &errcode, "进入失败")
			return
		}
		istest := abugo.GetInt64FromInterface((*data)["IsTest"])
		if istest == 1 {
			ctx.RespErrString(true, &errcode, "该账号禁止进入")
			return
		}
	}

	open := server.GetConfigInt(token.SellerId, 0, "LotteryOpen")
	if open != 1 {
		ctx.RespErrString(true, &errcode, "暂未开放,敬请期待")
		return
	}
	{
		page := 1
		if reqdata.GameId > 0 {
			page = 2
		}
		reqstr := fmt.Sprintf(`{"Merchantkey": "%s","Account": "%d","NickName": "%d","AgentType": "0","Platform": "mobile"}`, c.lottery_key, token.UserId, token.UserId)
		jdata, err := c.lottery_http_get("/Account/GetLogin", reqstr, fmt.Sprintf("&gameId=%d&page=%d&languageCode=%s", reqdata.GameId, page, reqdata.LangCode))
		if jdata == nil || err != nil {
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
		if (*jdata)["Success"].(bool) == true {
			err := c.third_transfer_out(TRANSFER_PLATFORM_HASH_LOTTERY, token.UserId, "")
			if err != nil {
				logs.Error(err)
				ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
				return
			}
			rurl := (*jdata)["Data"].(map[string]interface{})["Url"]
			rurl = fmt.Sprint(rurl, "&t=", rand.Intn(100000))
			logs.Debug("lottery_login:", token.UserId, rurl)
			ctx.RespOK(rurl)
		} else {
			logs.Error(jdata)
		}
	}
}

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 老板娘小游戏
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
func (c *ThirdController) xiaoyouxi_sign(params req.Param) string {
	var (
		keys sort.StringSlice
		n    int = -1
	)
	for key := range params {
		keys = append(keys, key)
		n++
	}
	sort.Sort(keys)
	reqStr := ``
	for _, key := range keys {
		if _, ok := params[key].(float64); ok {
			v := params[key].(float64)
			s := fmt.Sprintf(`%f`, v)
			for strings.HasSuffix(s, "0") {
				s = strings.TrimSuffix(s, "0")
			}
			for strings.HasSuffix(s, ".") {
				s = strings.TrimSuffix(s, ".")
			}
			reqStr += s
		} else if _, ok := params[key].(float32); ok {
			v := float64(params[key].(float32))
			s := fmt.Sprintf(`%f`, v)
			for strings.HasSuffix(s, "0") {
				s = strings.TrimSuffix(s, "0")
			}
			for strings.HasSuffix(s, ".") {
				s = strings.TrimSuffix(s, ".")
			}
			reqStr += s
		} else {
			s := fmt.Sprintf(`%v`, params[key])
			reqStr += s
		}

	}
	reqStr += c.xiaoyouxi_sign_key
	h := sha1.New()
	h.Write([]byte(reqStr))
	bs := h.Sum(nil)
	return fmt.Sprintf("%x", bs)
}

func (c *ThirdController) xiaoyouxi_http_req(method string, path string, params *req.Param) (*map[string]interface{}, error) {
	(*params)["sign"] = c.xiaoyouxi_sign(*params)
	var resp *req.Resp
	var err error
	if method == "post" {
		resp, err = req.Post(c.xiaoyouxi_url+path, req.BodyJSON(params))
	}
	if method == "get" {
		path += "?"
		for k, v := range *params {
			path += fmt.Sprintf("%v=%v&", k, v)
		}
		resp, err = req.Get(c.xiaoyouxi_url+path, req.BodyJSON(params))
	}
	reqstr, _ := json.Marshal(params)
	if err != nil {
		logs.Error("xiaoyouxi_http_req request error:", string(reqstr), err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("xiaoyouxi_http_req body error:", string(reqstr), err)
		return nil, err
	}
	// 修改后
	bodyStr := string(body)
	if len(bodyStr) > 1000 {
		bodyStr = bodyStr[:1000] + "... (已截断，总长度:" + strconv.Itoa(len(bodyStr)) + ")"
	}
	logs.Debug("xiaoyouxi_http_req:", c.xiaoyouxi_url+path, "|", string(reqstr), "|", bodyStr)

	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error(err, bodyStr)
		return nil, err
	}
	_, ok := jdata["success"]
	if !ok {
		logs.Debug("xiaoyouxi_http_req:", "解析失败")
		return nil, errors.New("解析失败")
	}
	success, ok := jdata["success"].(bool)
	if !ok {
		return nil, errors.New("解析失败")
	}
	if !success {
		if strings.Index(string(body), "Duplicate Username") >= 0 {
			return nil, nil
		}
		logs.Debug("xiaoyouxi_http_req:", c.xiaoyouxi_url+path, "|", string(reqstr), "|", bodyStr)
		return nil, nil
	}
	return &jdata, nil
}

func (c *ThirdController) xiaoyouxi_login(ctx *abugo.AbuHttpContent) {
	if len(c.xiaoyouxi_url) == 0 {
		return
	}
	type RequestData struct {
		LangCode string `validate:"required"`
		GameCode string `validate:"required"`
	}
	errcode := 0
	reqData := RequestData{}
	err := ctx.RequestData(&reqData)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	token := server.GetToken(ctx)

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	c.sync_amountex(token.UserId, TRANSFER_PLATFORM_LAOBANNIANG)

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, nil)
		data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
		if data == nil {
			ctx.RespErrString(true, &errcode, "进入失败")
			return
		}
		istest := abugo.GetInt64FromInterface((*data)["IsTest"])
		if istest == 1 {
			ctx.RespErrString(true, &errcode, "该账号禁止进入")
			return
		}
	}

	returl := ""
	open := server.GetConfigInt(token.SellerId, 0, "XiaoYouXiOpen")
	if open != 1 {
		logs.Info("小游戏未未开启:", "open=", open, "token.SellerId=", token.SellerId)
		ctx.RespErrString(true, &errcode, "暂未开放,敬请期待")
		return
	}
	{
		params := make(req.Param)
		params["auth_token"] = c.xiaoyouxi_token
		params["merchant_code"] = c.xiaoyouxi_merchant_code
		params["username"] = fmt.Sprint(token.UserId)
		c.xiaoyouxi_http_req("post", "/gameapi/v2/create_player", &params)
	}
	{
		params := make(req.Param)
		params["auth_token"] = c.xiaoyouxi_token
		params["merchant_code"] = c.xiaoyouxi_merchant_code
		params["username"] = fmt.Sprint(token.UserId)
		params["game_code"] = reqData.GameCode
		params["language"] = reqData.LangCode
		data, _ := c.xiaoyouxi_http_req("get", "/gameapi/v2/chain/query_game_launcher", &params)
		if data == nil {
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
		returl = (*data)["detail"].(map[string]interface{})["game_url"].(string)
	}
	err = c.third_transfer_out(TRANSFER_PLATFORM_LAOBANNIANG, token.UserId, "")
	if err != nil {
		logs.Error(err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	ctx.Put("url", returl)
	ctx.RespOK()
}

// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// wm视讯
// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

func (c *ThirdController) wm_http_post(cmd string, reqdata map[string]interface{}) (*map[string]interface{}, error) {
	url := c.wm_url + fmt.Sprintf("/api/public/Gateway.php?cmd=%s&vendorId=%s&signature=%s", cmd, c.wm_vendor, c.wm_signature)
	for k, v := range reqdata {
		url += fmt.Sprintf("&%s=%v", k, v)
	}
	resp, err := req.Post(url)
	if err != nil {
		logs.Error("wm_http_post request error:", url, err)
		return nil, err
	}
	defer resp.Response().Body.Close()
	body, err := io.ReadAll(resp.Response().Body)
	if err != nil {
		logs.Error("wm_http_post body error:", url, err)
		return nil, err
	}
	logs.Debug("wm_http_post:", url, "|", string(body))
	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("wm_http_post json.Unmarshal ", body, " error:", url, err)
		return nil, err
	}
	if _, ok := jdata["errorCode"]; !ok {
		logs.Error("wm_http_post errorCode not exist")
		return nil, errors.New("失败")
	}
	code, ok := jdata["errorCode"].(float64)
	msg, ok := jdata["errorMessage"].(string)
	if !ok || int(code) != 0 {
		if int(code) != 107 { //107表示查询成功，但是没有数据
			logs.Error("wm 返回错误", " cmd=", cmd, " errorCode=", code, "  errorMessage=", msg)
			return nil, nil
		}
		jdata["result"] = make([]interface{}, 0)
	}
	return &jdata, nil
}

func (c *ThirdController) mw_login(ctx *abugo.AbuHttpContent) {

	modeType := map[string]string{
		"101": "onlybac",    // 百家乐
		"102": "onlydgtg",   // 龙虎
		"103": "onlyrou",    // 轮盘
		"104": "onlysicbo",  // 骰宝
		"105": "onlyniuniu", // 牛牛
		"107": "onlyfantan", // 番摊
		"108": "onlysedie",  // 色碟
	}

	type RequestData struct {
		Lang   int
		GameId string
		Voice  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		return
	}
	token := server.GetToken(ctx)

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}

	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, nil)
		data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
		if data == nil {
			ctx.RespErrString(true, &errcode, "进入失败")
			return
		}
		istest := abugo.GetInt64FromInterface((*data)["IsTest"])
		if istest == 1 {
			ctx.RespErrString(true, &errcode, "该账号禁止进入")
			return
		}
	}

	c.sync_amountex(token.UserId, TRANSFER_PLATFORM_LIVE_WM)

	password := ""
	{
		data := []byte(fmt.Sprintf("GHD8eWWx%d", token.UserId))
		hash := md5.Sum(data)
		password = hex.EncodeToString(hash[:])
	}
	{
		data := gin.H{
			"user":     fmt.Sprintf("%d", token.UserId),
			"password": password,
			"username": fmt.Sprintf("%d", token.UserId),
		}
		c.wm_http_post("MemberRegister", data)
	}
	{
		data := gin.H{
			"user":      fmt.Sprintf("%d", token.UserId),
			"password":  password,
			"voice":     reqdata.Voice,
			"mode":      modeType[reqdata.GameId],
			"lang":      reqdata.Lang,
			"timestamp": time.Now().Unix(),
		}
		result, _ := c.wm_http_post("SigninGame", data)
		err = c.third_transfer_out(TRANSFER_PLATFORM_LIVE_WM, token.UserId, "")
		if err != nil {
			logs.Error("wm进入失败", err)
			ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
			return
		}
		ctx.Put("url", (*result)["result"])
		ctx.RespOK()
	}
}

// thirdCallbackSecurityMiddleware 三方游戏回调安全验证中间件
// 验证请求域名和IP白名单
func (c *ThirdController) thirdCallbackSecurityMiddleware(ctx *gin.Context) {
	path := ctx.Request.URL.Path

	// 只对 /api/third/**/** 格式的三方回调路径进行安全验证
	// /api/third/** 格式的是前端调用接口，不需要验证
	if !strings.HasPrefix(path, "/api/third/") {
		ctx.Next()
		return
	}

	// 检查是否为 /api/third/**/** 格式（三方回调）
	// 移除 /api/third/ 前缀后，检查是否包含至少一个 /
	pathAfterPrefix := strings.TrimPrefix(path, "/api/third/")
	if !strings.Contains(pathAfterPrefix, "/") {
		ctx.Next()
		return
	}

	// 例外路径检查 - 这些路径不需要安全验证
	exceptionPaths := []string{
		"/api/third/fb/get_hot_events",
		"/api/third/cbk/get_hot_events",
		"/api/third/qqpoker/get_hot_events",
		"/api/third/kaiyuan/login",
	}

	for _, exceptionPath := range exceptionPaths {
		if path == exceptionPath {
			ctx.Next()
			return
		}
	}

	// 1. 获取安全配置
	configStr := server.GetConfigString(1, 0, "IPBlack")
	if configStr == "" {
		logs.Error("三方回调安全验证失败: 未配置安全验证")
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "Server configuration error",
		})
		ctx.Abort()
		return
	}

	// 2. 解析安全配置
	type SecurityConfig struct {
		HostEnabled bool                `json:"hostEnabled"` // 域名验证开关
		IPEnabled   bool                `json:"ipEnabled"`   // IP验证开关
		Hosts       []string            `json:"hosts"`       // 允许的域名列表
		Vendors     map[string][]string `json:"vendors"`     // 厂商IP分组
	}

	var config SecurityConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		logs.Error("三方回调安全验证失败: 配置格式错误 - %v", err)
		ctx.JSON(500, gin.H{
			"code": 500,
			"msg":  "Configuration format error",
		})
		ctx.Abort()
		return
	}

	// 3. 验证请求域名（根据hostEnabled开关）
	host := ctx.Request.Host
	// 去掉端口号，只保留主机名
	if strings.Contains(host, ":") {
		hostParts := strings.Split(host, ":")
		if len(hostParts) > 0 {
			host = hostParts[0]
		}
	}

	if config.HostEnabled {
		if len(config.Hosts) > 0 {
			isHostAllowed := false

			for _, allowedHost := range config.Hosts {
				allowedHost = strings.TrimSpace(allowedHost)
				// 同样去掉配置中可能存在的端口号
				if strings.Contains(allowedHost, ":") {
					hostParts := strings.Split(allowedHost, ":")
					if len(hostParts) > 0 {
						allowedHost = hostParts[0]
					}
				}
				if host == allowedHost {
					isHostAllowed = true
					break
				}
			}

			if !isHostAllowed {
				logs.Error("三方回调安全验证失败: 非法域名访问 - 请求域名: %s, 允许域名: %v", host, config.Hosts, " 请求路径=", ctx.Request.URL.Path)
				ctx.JSON(403, gin.H{
					"code": 403,
					"msg":  "Forbidden: Invalid host",
				})
				ctx.Abort()
				return
			}
			//logs.Info("三方回调安全验证: 域名验证通过 - host=%s", host)
		} else {
			logs.Warn("三方回调安全验证: 域名验证开关已启用但未配置域名白名单，跳过域名验证")
		}
	} else {
		logs.Info("三方回调安全验证: 域名验证开关已关闭，跳过域名验证")
	}

	// 5. 获取客户端真实IP
	clientIP := ctx.ClientIP()

	// 尝试从各种header获取真实IP
	if forwardedIP := ctx.GetHeader("X-Forwarded-For"); forwardedIP != "" {
		// X-Forwarded-For可能包含多个IP，用逗号分隔，第一个是客户端真实IP
		ips := strings.Split(forwardedIP, ",")
		if len(ips) > 0 {
			clientIP = strings.TrimSpace(ips[0])
		}
	} else if realIP := ctx.GetHeader("X-Real-IP"); realIP != "" {
		clientIP = realIP
	}

	//logs.Info("三方回调请求 - 客户端IP: %s, 请求路径: %s", clientIP, ctx.Request.URL.Path)

	// 6. 验证IP白名单（根据ipEnabled开关）
	if config.IPEnabled {
		//logs.Info("三方回调安全验证: IP验证开关已开启")
		if len(config.Vendors) > 0 {
			// 收集所有厂商的IP到一个列表中
			var allIPs []string
			for vendor, ips := range config.Vendors {
				logs.Debug("三方回调安全验证: 收集厂商IP - vendor=%s, ips=%v", vendor, ips)
				allIPs = append(allIPs, ips...)
			}

			if len(allIPs) > 0 {
				isIPAllowed := false

				for _, allowedIP := range allIPs {
					allowedIP = strings.TrimSpace(allowedIP)
					if allowedIP == "" {
						continue
					}

					// 支持IP段匹配 (如: ***********/24)
					if strings.Contains(allowedIP, "/") {
						// CIDR格式IP段验证
						_, ipNet, err := net.ParseCIDR(allowedIP)
						if err != nil {
							logs.Error("三方回调安全验证: 解析IP段失败 - ip=%s, 错误: %v", allowedIP, err)
							continue
						}
						if ipNet.Contains(net.ParseIP(clientIP)) {
							isIPAllowed = true
							break
						}
					} else {
						// 单个IP验证
						if clientIP == allowedIP {
							isIPAllowed = true
							break
						}
					}
				}

				// IP白名单验证失败
				if !isIPAllowed {
					logs.Error("三方回调安全验证失败: IP不在白名单内 - clientIP=%s, allIPs=%v", clientIP, allIPs)
					ctx.JSON(403, gin.H{
						"code": 403,
						"msg":  "IP not allowed",
					})
					ctx.Abort()
					return
				}
				logs.Info("三方回调安全验证: IP验证通过 - clientIP=%s", clientIP)
			} else {
				logs.Warn("三方回调安全验证: IP验证开关已启用但厂商IP配置为空，跳过IP验证")
			}
		} else {
			logs.Warn("三方回调安全验证: IP验证开关已启用但未配置厂商IP白名单，跳过IP验证")
		}
	} else {
		//logs.Info("三方回调安全验证: IP验证开关已关闭，跳过IP验证")
	}

	//logs.Info("三方回调安全验证通过 - host=%s, clientIP=%s, path=%s, hostEnabled=%t, ipEnabled=%t", host, clientIP, ctx.Request.URL.Path, config.HostEnabled, config.IPEnabled)

	// 验证通过，继续处理请求
	ctx.Next()
}
