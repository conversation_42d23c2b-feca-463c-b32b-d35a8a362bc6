// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXGameList(db *gorm.DB, opts ...gen.DOOption) xGameList {
	_xGameList := xGameList{}

	_xGameList.xGameListDo.UseDB(db, opts...)
	_xGameList.xGameListDo.UseModel(&model.XGameList{})

	tableName := _xGameList.xGameListDo.TableName()
	_xGameList.ALL = field.NewAsterisk(tableName)
	_xGameList.ID = field.NewInt32(tableName, "Id")
	_xGameList.Brand = field.NewString(tableName, "Brand")
	_xGameList.GameID = field.NewString(tableName, "GameId")
	_xGameList.Name = field.NewString(tableName, "Name")
	_xGameList.EName = field.NewString(tableName, "EName")
	_xGameList.Sort = field.NewInt32(tableName, "Sort")
	_xGameList.UserCount = field.NewInt32(tableName, "UserCount")
	_xGameList.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xGameList.Rtp = field.NewFloat64(tableName, "Rtp")
	_xGameList.IsNew = field.NewInt32(tableName, "IsNew")
	_xGameList.IsHot = field.NewInt32(tableName, "IsHot")
	_xGameList.State = field.NewInt32(tableName, "State")
	_xGameList.OpenState = field.NewInt32(tableName, "OpenState")
	_xGameList.LiuSui = field.NewInt32(tableName, "LiuSui")
	_xGameList.Icon = field.NewString(tableName, "Icon")
	_xGameList.EIcon = field.NewString(tableName, "EIcon")
	_xGameList.GameType = field.NewInt32(tableName, "GameType")
	_xGameList.IsRecom = field.NewInt32(tableName, "IsRecom")
	_xGameList.HubType = field.NewInt32(tableName, "HubType")
	_xGameList.GameSubType = field.NewString(tableName, "GameSubType")
	_xGameList.ThirdGameType = field.NewString(tableName, "ThirdGameType")
	_xGameList.CountryList = field.NewString(tableName, "CountryList")
	_xGameList.OnlineMin = field.NewInt32(tableName, "OnlineMin")
	_xGameList.OnlineMax = field.NewInt32(tableName, "OnlineMax")
	_xGameList.SpecialBrand = field.NewString(tableName, "SpecialBrand")
	_xGameList.SpecailDisplay = field.NewInt32(tableName, "SpecailDisplay")

	_xGameList.fillFieldMap()

	return _xGameList
}

type xGameList struct {
	xGameListDo xGameListDo

	ALL            field.Asterisk
	ID             field.Int32   // Id
	Brand          field.String  // 品牌
	GameID         field.String  // 游戏Id
	Name           field.String  // 中文名
	EName          field.String  // 英文名
	Sort           field.Int32   // 排序,数字越大越靠前
	UserCount      field.Int32   // 投注人数
	BetAmount      field.Float64 // 投注金额
	Rtp            field.Float64 // rtp回报率
	IsNew          field.Int32   // 新游戏 1是,2不是
	IsHot          field.Int32   // 热门游戏 1是,2不是
	State          field.Int32   // 状态 1启用,2禁用
	OpenState      field.Int32   // 状态 1启用,2禁用
	LiuSui         field.Int32   // 1算流水,2不算流水
	Icon           field.String
	EIcon          field.String
	GameType       field.Int32  // 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
	IsRecom        field.Int32  // 是否是推荐
	HubType        field.Int32  // 聚合类型 0非聚合 1hub88 4gfg聚合
	GameSubType    field.String // SLOT(电子) BAC(百家乐) DT(龙虎) SHB(骰宝) ROU(轮盘) NN(牛牛) ZJH(炸金花) BJ(21点) SG(三公) BF(斗牛) CBAC(包桌百家乐) LBAC(咪牌百家乐) TEX(德州) FT(番摊) SD(色碟) PJ(牌九) CP(彩票) AB(安达巴哈)
	ThirdGameType  field.String // 三方类别
	CountryList    field.String // 支持的地区（二位字母国家代码，英文逗号分隔）
	OnlineMin      field.Int32  // 最小在玩人数
	OnlineMax      field.Int32  // 最大在玩人数
	SpecialBrand   field.String // 对应特殊厂商
	SpecailDisplay field.Int32  // 特殊游戏是否显示 1：显示，2:不显示

	fieldMap map[string]field.Expr
}

func (x xGameList) Table(newTableName string) *xGameList {
	x.xGameListDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xGameList) As(alias string) *xGameList {
	x.xGameListDo.DO = *(x.xGameListDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xGameList) updateTableName(table string) *xGameList {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.Brand = field.NewString(table, "Brand")
	x.GameID = field.NewString(table, "GameId")
	x.Name = field.NewString(table, "Name")
	x.EName = field.NewString(table, "EName")
	x.Sort = field.NewInt32(table, "Sort")
	x.UserCount = field.NewInt32(table, "UserCount")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.Rtp = field.NewFloat64(table, "Rtp")
	x.IsNew = field.NewInt32(table, "IsNew")
	x.IsHot = field.NewInt32(table, "IsHot")
	x.State = field.NewInt32(table, "State")
	x.OpenState = field.NewInt32(table, "OpenState")
	x.LiuSui = field.NewInt32(table, "LiuSui")
	x.Icon = field.NewString(table, "Icon")
	x.EIcon = field.NewString(table, "EIcon")
	x.GameType = field.NewInt32(table, "GameType")
	x.IsRecom = field.NewInt32(table, "IsRecom")
	x.HubType = field.NewInt32(table, "HubType")
	x.GameSubType = field.NewString(table, "GameSubType")
	x.ThirdGameType = field.NewString(table, "ThirdGameType")
	x.CountryList = field.NewString(table, "CountryList")
	x.OnlineMin = field.NewInt32(table, "OnlineMin")
	x.OnlineMax = field.NewInt32(table, "OnlineMax")
	x.SpecialBrand = field.NewString(table, "SpecialBrand")
	x.SpecailDisplay = field.NewInt32(table, "SpecailDisplay")

	x.fillFieldMap()

	return x
}

func (x *xGameList) WithContext(ctx context.Context) *xGameListDo {
	return x.xGameListDo.WithContext(ctx)
}

func (x xGameList) TableName() string { return x.xGameListDo.TableName() }

func (x xGameList) Alias() string { return x.xGameListDo.Alias() }

func (x xGameList) Columns(cols ...field.Expr) gen.Columns { return x.xGameListDo.Columns(cols...) }

func (x *xGameList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xGameList) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 26)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["Name"] = x.Name
	x.fieldMap["EName"] = x.EName
	x.fieldMap["Sort"] = x.Sort
	x.fieldMap["UserCount"] = x.UserCount
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["Rtp"] = x.Rtp
	x.fieldMap["IsNew"] = x.IsNew
	x.fieldMap["IsHot"] = x.IsHot
	x.fieldMap["State"] = x.State
	x.fieldMap["OpenState"] = x.OpenState
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["Icon"] = x.Icon
	x.fieldMap["EIcon"] = x.EIcon
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["IsRecom"] = x.IsRecom
	x.fieldMap["HubType"] = x.HubType
	x.fieldMap["GameSubType"] = x.GameSubType
	x.fieldMap["ThirdGameType"] = x.ThirdGameType
	x.fieldMap["CountryList"] = x.CountryList
	x.fieldMap["OnlineMin"] = x.OnlineMin
	x.fieldMap["OnlineMax"] = x.OnlineMax
	x.fieldMap["SpecialBrand"] = x.SpecialBrand
	x.fieldMap["SpecailDisplay"] = x.SpecailDisplay
}

func (x xGameList) clone(db *gorm.DB) xGameList {
	x.xGameListDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xGameList) replaceDB(db *gorm.DB) xGameList {
	x.xGameListDo.ReplaceDB(db)
	return x
}

type xGameListDo struct{ gen.DO }

func (x xGameListDo) Debug() *xGameListDo {
	return x.withDO(x.DO.Debug())
}

func (x xGameListDo) WithContext(ctx context.Context) *xGameListDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xGameListDo) ReadDB() *xGameListDo {
	return x.Clauses(dbresolver.Read)
}

func (x xGameListDo) WriteDB() *xGameListDo {
	return x.Clauses(dbresolver.Write)
}

func (x xGameListDo) Session(config *gorm.Session) *xGameListDo {
	return x.withDO(x.DO.Session(config))
}

func (x xGameListDo) Clauses(conds ...clause.Expression) *xGameListDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xGameListDo) Returning(value interface{}, columns ...string) *xGameListDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xGameListDo) Not(conds ...gen.Condition) *xGameListDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xGameListDo) Or(conds ...gen.Condition) *xGameListDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xGameListDo) Select(conds ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xGameListDo) Where(conds ...gen.Condition) *xGameListDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xGameListDo) Order(conds ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xGameListDo) Distinct(cols ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xGameListDo) Omit(cols ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xGameListDo) Join(table schema.Tabler, on ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xGameListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xGameListDo) RightJoin(table schema.Tabler, on ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xGameListDo) Group(cols ...field.Expr) *xGameListDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xGameListDo) Having(conds ...gen.Condition) *xGameListDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xGameListDo) Limit(limit int) *xGameListDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xGameListDo) Offset(offset int) *xGameListDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xGameListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xGameListDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xGameListDo) Unscoped() *xGameListDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xGameListDo) Create(values ...*model.XGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xGameListDo) CreateInBatches(values []*model.XGameList, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xGameListDo) Save(values ...*model.XGameList) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xGameListDo) First() (*model.XGameList, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameList), nil
	}
}

func (x xGameListDo) Take() (*model.XGameList, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameList), nil
	}
}

func (x xGameListDo) Last() (*model.XGameList, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameList), nil
	}
}

func (x xGameListDo) Find() ([]*model.XGameList, error) {
	result, err := x.DO.Find()
	return result.([]*model.XGameList), err
}

func (x xGameListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XGameList, err error) {
	buf := make([]*model.XGameList, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xGameListDo) FindInBatches(result *[]*model.XGameList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xGameListDo) Attrs(attrs ...field.AssignExpr) *xGameListDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xGameListDo) Assign(attrs ...field.AssignExpr) *xGameListDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xGameListDo) Joins(fields ...field.RelationField) *xGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xGameListDo) Preload(fields ...field.RelationField) *xGameListDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xGameListDo) FirstOrInit() (*model.XGameList, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameList), nil
	}
}

func (x xGameListDo) FirstOrCreate() (*model.XGameList, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XGameList), nil
	}
}

func (x xGameListDo) FindByPage(offset int, limit int) (result []*model.XGameList, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xGameListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xGameListDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xGameListDo) Delete(models ...*model.XGameList) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xGameListDo) withDO(do gen.Dao) *xGameListDo {
	x.DO = *do.(*gen.DO)
	return x
}
