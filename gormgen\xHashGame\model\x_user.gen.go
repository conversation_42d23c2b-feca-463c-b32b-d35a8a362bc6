// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUser = "x_user"

// XUser 用户表
type XUser struct {
	ID                       int32     `gorm:"column:Id;not null;comment:id" json:"Id"`                                                                                           // id
	UserID                   int32     `gorm:"column:UserId;primaryKey;comment:玩家" json:"UserId"`                                                                                 // 玩家
	SellerID                 int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                                                                       // 运营商
	ChannelID                int32     `gorm:"column:ChannelId;default:1;comment:渠道商" json:"ChannelId"`                                                                           // 渠道商
	Account                  string    `gorm:"column:Account;comment:账号" json:"Account"`                                                                                          // 账号
	Password                 string    `gorm:"column:Password;comment:登录密码" json:"Password"`                                                                                      // 登录密码
	PasswordV2               string    `gorm:"column:PasswordV2;comment:登录密码" json:"PasswordV2"`                                                                                  // 登录密码
	NickName                 string    `gorm:"column:NickName;comment:昵称" json:"NickName"`                                                                                        // 昵称
	Token                    string    `gorm:"column:Token;comment:登录token" json:"Token"`                                                                                         // 登录token
	Agents                   string    `gorm:"column:Agents;comment:json数组,所有上级id,下标0是直属代理,越往后,代理等级越高" json:"Agents"`                                                             // json数组,所有上级id,下标0是直属代理,越往后,代理等级越高
	TopAgentID               int32     `gorm:"column:TopAgentId;comment:顶级代理id" json:"TopAgentId"`                                                                                // 顶级代理id
	AgentID                  int32     `gorm:"column:AgentId;comment:直属代理" json:"AgentId"`                                                                                        // 直属代理
	RegisterIP               string    `gorm:"column:RegisterIp;comment:注册ip" json:"RegisterIp"`                                                                                  // 注册ip
	RegisterRegion           string    `gorm:"column:RegisterRegion;comment:注册区域" json:"RegisterRegion"`                                                                          // 注册区域
	RegisterTime             time.Time `gorm:"column:RegisterTime;default:CURRENT_TIMESTAMP;comment:注册时间" json:"RegisterTime"`                                                    // 注册时间
	Address                  string    `gorm:"column:Address;comment:地址" json:"Address"`                                                                                         // 地址
	Email                    string    `gorm:"column:Email;comment:注册email地址" json:"Email"`                                                                                       // 注册email地址
	PhoneNum                 string    `gorm:"column:PhoneNum;comment:注册手机号码" json:"PhoneNum"`                                                                                    // 注册手机号码
	BindEmail                string    `gorm:"column:BindEmail;comment:绑定email地址" json:"BindEmail"`                                                                               // 绑定email地址
	BindPhoneNum             string    `gorm:"column:BindPhoneNum;comment:绑定手机号码" json:"BindPhoneNum"`                                                                            // 绑定手机号码
	PwdVerificationType      int32     `gorm:"column:PwdVerificationType;not null;comment:设置资金密码验证类型:1--邮箱 2 --手机号 3 -- (或关系) 两者满其一 4 -- (与关系)邮箱+手机号" json:"PwdVerificationType"` // 设置资金密码验证类型:1--邮箱 2 --手机号 3 -- (或关系) 两者满其一 4 -- (与关系)邮箱+手机号
	IsTopAgent               int32     `gorm:"column:IsTopAgent;default:2;comment:是否是顶级代理 1是 2不是" json:"IsTopAgent"`                                                              // 是否是顶级代理 1是 2不是
	BetTrx                   float64   `gorm:"column:BetTrx;default:0.000000;comment:Trx下注" json:"BetTrx"`                                                                        // Trx下注
	RewardTrx                float64   `gorm:"column:RewardTrx;default:0.000000;comment:trx返奖" json:"RewardTrx"`                                                                  // trx返奖
	LiuSuiTrx                float64   `gorm:"column:LiuSuiTrx;default:0.000000;comment:trx流水" json:"LiuSuiTrx"`                                                                  // trx流水
	BetUsdt                  float64   `gorm:"column:BetUsdt;default:0.000000;comment:usdt下注" json:"BetUsdt"`                                                                     // usdt下注
	RewardUsdt               float64   `gorm:"column:RewardUsdt;default:0.000000;comment:usdt返奖" json:"RewardUsdt"`                                                               // usdt返奖
	LiuSuiUsdt               float64   `gorm:"column:LiuSuiUsdt;default:0.000000;comment:usdt流水" json:"LiuSuiUsdt"`                                                               // usdt流水
	IsAgent                  int32     `gorm:"column:IsAgent;default:2;comment:是否是代理 1是,2不是" json:"IsAgent"`                                                                      // 是否是代理 1是,2不是
	LoginTime                time.Time `gorm:"column:LoginTime;comment:登录时间" json:"LoginTime"`                                                                                    // 登录时间
	LoginIP                  string    `gorm:"column:LoginIp;comment:登录ip" json:"LoginIp"`                                                                                        // 登录ip
	State                    int32     `gorm:"column:State;default:1;comment:状态 1启用 2禁用 3冻结余额" json:"State"`                                                                      // 状态 1启用 2禁用 3冻结余额
	FineLiuSuiTrx            float64   `gorm:"column:FineLiuSuiTrx;default:0.000000;comment:扣除流水trx" json:"FineLiuSuiTrx"`                                                        // 扣除流水trx
	FineLiuSuiUsdt           float64   `gorm:"column:FineLiuSuiUsdt;default:0.000000;comment:扣除流水Usdt" json:"FineLiuSuiUsdt"`                                                     // 扣除流水Usdt
	LastGameInfo             string    `gorm:"column:LastGameInfo;comment:最后玩的游戏" json:"LastGameInfo"`                                                                            // 最后玩的游戏
	FenCheng                 string    `gorm:"column:FenCheng;comment:分成比例" json:"FenCheng"`                                                                                      // 分成比例
	AgentCode                string    `gorm:"column:AgentCode;comment:注册邀请码" json:"AgentCode"`                                                                                   // 注册邀请码
	TgChatID                 int64     `gorm:"column:TgChatId;comment:电报机器人chatid" json:"TgChatId"`                                                                               // 电报机器人chatid
	TgUserName               string    `gorm:"column:TgUserName;comment:电报机器人用户名" json:"TgUserName"`                                                                              // 电报机器人用户名
	VerifyState              int32     `gorm:"column:VerifyState;default:1;comment:地址是否已验证" json:"VerifyState"`                                                                   // 地址是否已验证
	VerifyAmount             int32     `gorm:"column:VerifyAmount;comment:地址验证转账金额" json:"VerifyAmount"`                                                                          // 地址验证转账金额
	VerifyTime               time.Time `gorm:"column:VerifyTime;comment:地址验证时间" json:"VerifyTime"`                                                                                // 地址验证时间
	Amount                   float64   `gorm:"column:Amount;default:0.000000;comment:账户余额usdt" json:"Amount"`                                                                     // 账户余额usdt
	BonusAmount              float64   `gorm:"column:BonusAmount;default:0.000000;comment:Bonus钱包usdt" json:"BonusAmount"`                                                        // Bonus钱包usdt
	BankerAmount             float64   `gorm:"column:BankerAmount;not null;default:0.000000;comment:上庄余额" json:"BankerAmount"`                                                    // 上庄余额
	LockedAmount             float64   `gorm:"column:LockedAmount;default:0.000000;comment:锁定账户余额usdt" json:"LockedAmount"`                                                       // 锁定账户余额usdt
	LastGameInfo6            string    `gorm:"column:LastGameInfo6;comment:最后一局快三" json:"LastGameInfo6"`                                                                          // 最后一局快三
	LastGameInfo7            string    `gorm:"column:LastGameInfo7;comment:最后一局pk10" json:"LastGameInfo7"`                                                                        // 最后一局pk10
	GameFee                  string    `gorm:"column:GameFee;comment:个人游戏费率" json:"GameFee"`                                                                                      // 个人游戏费率
	VipAmount                float64   `gorm:"column:VipAmount;comment:vip累计金额" json:"VipAmount"`                                                                                 // vip累计金额
	WinAudit                 string    `gorm:"column:WinAudit;comment:连赢审核记录" json:"WinAudit"`                                                                                    // 连赢审核记录
	MaxBet                   string    `gorm:"column:MaxBet;comment:单笔最大下注记录" json:"MaxBet"`                                                                                      // 单笔最大下注记录
	RechargeAddressTron      string    `gorm:"column:RechargeAddressTron;comment:充值地址" json:"RechargeAddressTron"`                                                                // 充值地址
	WalletPassword           string    `gorm:"column:WalletPassword;comment:提现密码" json:"WalletPassword"`                                                                          // 提现密码
	MaxBetTime               time.Time `gorm:"column:MaxBetTime;default:CURRENT_TIMESTAMP;comment:降赔重置时间" json:"MaxBetTime"`                                                      // 降赔重置时间
	JpType                   int32     `gorm:"column:JpType;comment:降赔类型" json:"JpType"`                                                                                          // 降赔类型
	LastGameInfoEx           string    `gorm:"column:LastGameInfoEx;comment:最后玩的游戏" json:"LastGameInfoEx"`                                                                        // 最后玩的游戏
	WithdrawLiuSui           float64   `gorm:"column:WithdrawLiuSui;default:0.000000;comment:提现流水" json:"WithdrawLiuSui"`                                                         // 提现流水
	CaijinLiuSui             float64   `gorm:"column:CaijinLiuSui;default:0.000000;comment:彩金提现流水" json:"CaijinLiuSui"`                                                           // 彩金提现流水
	TotalLiuSui              float64   `gorm:"column:TotalLiuSui;default:0.000000;comment:当前流水" json:"TotalLiuSui"`                                                               // 当前流水
	RechargeAddressEth       string    `gorm:"column:RechargeAddressEth;comment:充值地址" json:"RechargeAddressEth"`                                                                  // 充值地址
	HeadID                   string    `gorm:"column:HeadId;comment:头像id" json:"HeadId"`                                                                                          // 头像id
	Gender                   string    `gorm:"column:Gender;comment:性别" json:"Gender"`                                                                                            // 性别
	DeliveryAddress          string    `gorm:"column:DeliveryAddress;comment:收货地址" json:"DeliveryAddress"`                                                                        // 收货地址
	Birthday                 time.Time `gorm:"column:Birthday;comment:生日" json:"Birthday"`                                                                                        // 生日
	RealName                 string    `gorm:"column:RealName;comment:真实姓名" json:"RealName"`                                                                                      // 真实姓名
	IsTest                   int32     `gorm:"column:IsTest;default:2;comment:是否是测试账号,1是,2不是" json:"IsTest"`                                                                      // 是否是测试账号,1是,2不是
	UpdatePasswordTime       time.Time `gorm:"column:UpdatePasswordTime;comment:最后一次改密码时间" json:"UpdatePasswordTime"`                                                             // 最后一次改密码时间
	UpdateWalletPasswordTime time.Time `gorm:"column:UpdateWalletPasswordTime;default:CURRENT_TIMESTAMP;comment:最后一次改资金密码时间" json:"UpdateWalletPasswordTime"`                     // 最后一次改资金密码时间
	AuditAmount              float64   `gorm:"column:AuditAmount;default:-1.000000;comment:审核金额,返奖超过此金额需要审核" json:"AuditAmount"`                                                  // 审核金额,返奖超过此金额需要审核
	WinJiangPeiMax           string    `gorm:"column:WinJiangPeiMax;comment:降赔最大金额" json:"WinJiangPeiMax"`                                                                        // 降赔最大金额
	IsPanda                  int32     `gorm:"column:IsPanda;default:2;comment:是否是量化用户 1是,2不是" json:"IsPanda"`                                                                    // 是否是量化用户 1是,2不是
	BlackMaker               string    `gorm:"column:BlackMaker;comment:区块黑名单" json:"BlackMaker"`                                                                                 // 区块黑名单
	TgName                   string    `gorm:"column:TgName;comment:注册tg" json:"TgName"`                                                                                          // 注册tg
	CSGroup                  string    `gorm:"column:CSGroup;comment:客服团队" json:"CSGroup"`                                                                                        // 客服团队
	CSID                     string    `gorm:"column:CSId;comment:客服工号" json:"CSId"`                                                                                              // 客服工号
	RegGift                  int32     `gorm:"column:RegGift;comment:【废弃】体验金状态 1无体验金,2可以领取体验金,3可以领取trx体验金,4已经领取trx体验金,5可以领取u体验金,6已经领取u体验金" json:"RegGift"`                        // 【废弃】体验金状态 1无体验金,2可以领取体验金,3可以领取trx体验金,4已经领取trx体验金,5可以领取u体验金,6已经领取u体验金
	IgnoreWinJiangPei        int32     `gorm:"column:IgnoreWinJiangPei;default:2;comment:忽略盈利降赔,1是,2否" json:"IgnoreWinJiangPei"`                                                  // 忽略盈利降赔,1是,2否
	BetCount                 int32     `gorm:"column:BetCount;comment:投注次数" json:"BetCount"`                                                                                      // 投注次数
	BetDays                  int32     `gorm:"column:BetDays;comment:下注天数" json:"BetDays"`                                                                                        // 下注天数
	FirstBetTime             time.Time `gorm:"column:FirstBetTime;comment:首次投注时间" json:"FirstBetTime"`                                                                            // 首次投注时间
	LastBetTime              time.Time `gorm:"column:LastBetTime;comment:最后投注时间" json:"LastBetTime"`                                                                              // 最后投注时间
	FirstRechargeTime        time.Time `gorm:"column:FirstRechargeTime;comment:首次充值时间" json:"FirstRechargeTime"`                                                                  // 首次充值时间
	LastRechargeTime         time.Time `gorm:"column:LastRechargeTime;comment:最后充值时间" json:"LastRechargeTime"`                                                                    // 最后充值时间
	FirstWithdrawTime        time.Time `gorm:"column:FirstWithdrawTime;comment:首次提款申请时间" json:"FirstWithdrawTime"`                                                                // 首次提款申请时间
	LastWithdrawTime         time.Time `gorm:"column:LastWithdrawTime;comment:最后提款申请时间" json:"LastWithdrawTime"`                                                                  // 最后提款申请时间
	FirstWithdrawFinishTime  time.Time `gorm:"column:FirstWithdrawFinishTime;comment:首次提款完成时间" json:"FirstWithdrawFinishTime"`                                                    // 首次提款完成时间
	LastWithdrawFinishTime   time.Time `gorm:"column:LastWithdrawFinishTime;comment:最后提款完成时间" json:"LastWithdrawFinishTime"`                                                      // 最后提款完成时间
	WithdrawCount            int32     `gorm:"column:WithdrawCount;comment:提款申请数" json:"WithdrawCount"`                                                                           // 提款申请数
	WithdrawSuccessCount     int32     `gorm:"column:WithdrawSuccessCount;comment:提款成功数" json:"WithdrawSuccessCount"`                                                             // 提款成功数
	RegURL                   string    `gorm:"column:RegUrl;comment:注册域名" json:"RegUrl"`                                                                                          // 注册域名
	Memo                     string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                                                                                // 备注
	KeFuTgName               string    `gorm:"column:KeFuTgName;comment:客服tg" json:"KeFuTgName"`                                                                                  // 客服tg
	Tag                      string    `gorm:"column:Tag;comment:标签" json:"Tag"`                                                                                                  // 标签
	CaiJingTrx               float64   `gorm:"column:CaiJingTrx;comment:累计彩金trx" json:"CaiJingTrx"`                                                                               // 累计彩金trx
	CaiJingUsdt              float64   `gorm:"column:CaiJingUsdt;comment:累计彩金usdt" json:"CaiJingUsdt"`                                                                            // 累计彩金usdt
	RechargeAmount           float64   `gorm:"column:RechargeAmount;comment:累计充值" json:"RechargeAmount"`                                                                          // 累计充值
	WithdrawAmount           float64   `gorm:"column:WithdrawAmount;comment:累计提款" json:"WithdrawAmount"`                                                                          // 累计提款
	CSBindTime               time.Time `gorm:"column:CSBindTime;comment:客服绑定时间" json:"CSBindTime"`                                                                                // 客服绑定时间
	ThirdID                  string    `gorm:"column:ThirdId;comment:三方id" json:"ThirdId"`                                                                                        // 三方id
	SpecialAgent             int32     `gorm:"column:SpecialAgent;default:2;comment:是否是独立代理 1 是,2不是" json:"SpecialAgent"`                                                         // 是否是独立代理 1 是,2不是
	LastAddAmount            float64   `gorm:"column:LastAddAmount;comment:最后一次后台增值金额" json:"LastAddAmount"`                                                                      // 最后一次后台增值金额
	AuditAmountUsdt          float64   `gorm:"column:AuditAmountUsdt;default:-1.000000;comment:审核金额,返奖超过此金额需要审核" json:"AuditAmountUsdt"`                                          // 审核金额,返奖超过此金额需要审核
	AuditAmountTrx           float64   `gorm:"column:AuditAmountTrx;default:-1.000000;comment:审核金额,返奖超过此金额需要审核" json:"AuditAmountTrx"`                                            // 审核金额,返奖超过此金额需要审核
	RegDeviceID              string    `gorm:"column:RegDeviceId;comment:注册设备Id" json:"RegDeviceId"`                                                                              // 注册设备Id
	LoginDeviceID            string    `gorm:"column:LoginDeviceId;comment:登录设备Id" json:"LoginDeviceId"`                                                                          // 登录设备Id
	RegDeviceType            string    `gorm:"column:RegDeviceType;comment:注册设备类型  ios android windows" json:"RegDeviceType"`                                                     // 注册设备类型  ios android windows
	LoginDeviceType          string    `gorm:"column:LoginDeviceType;comment:登录设备类型 ios android windows" json:"LoginDeviceType"`                                                  // 登录设备类型 ios android windows
	GameLimit                string    `gorm:"column:GameLimit;comment:游戏限额" json:"GameLimit"`                                                                                    // 游戏限额
	WithwardNeedLiuSui       int32     `gorm:"column:WithwardNeedLiuSui;default:1;comment:提现是否需要流水,1是,2否" json:"WithwardNeedLiuSui"`                                              // 提现是否需要流水,1是,2否
	RegLang                  string    `gorm:"column:RegLang;comment:注册语言" json:"RegLang"`                                                                                        // 注册语言
	LoginLang                string    `gorm:"column:LoginLang;comment:登录语言" json:"LoginLang"`                                                                                    // 登录语言
	AgentType                int32     `gorm:"column:AgentType;default:1;comment:代理类型 1无代理类型,2无限代,3三代理" json:"AgentType"`                                                         // 代理类型 1无代理类型,2无限代,3三代理
	AgentConfigID            int32     `gorm:"column:AgentConfigId;comment:代理方案id" json:"AgentConfigId"`                                                                          // 代理方案id
	AgentNickName            string    `gorm:"column:AgentNickName;comment:代理昵称" json:"AgentNickName"`                                                                            // 代理昵称
	AgentUseID               int32     `gorm:"column:AgentUseId;comment:佣金方案id" json:"AgentUseId"`                                                                                // 佣金方案id
	CxdID                    string    `gorm:"column:CxdId" json:"CxdId"`
	IsoCountry               string    `gorm:"column:IsoCountry" json:"IsoCountry"`
	AccountType              int32     `gorm:"column:AccountType;comment:1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram" json:"AccountType"`                                      // 1:账号, 2:手机号, 3:Email, 4:Google, 5:Telegram
	TgRobotToken             string    `gorm:"column:TgRobotToken;comment:tg机器人token" json:"TgRobotToken"`                                                                    // tg机器人token
	TgRobotGiftStatus        int32     `gorm:"column:TgRobotGiftStatus;comment:【废弃】tg机器人账户体验金状态(0无体验金,1可以领取u体验金,2已经领取u体验金,3可以领取trx体验金,4已经领取trx体验金)" json:"TgRobotGiftStatus"` // 【废弃】tg机器人账户体验金状态(0无体验金,1可以领取u体验金,2已经领取u体验金,3可以领取trx体验金,4已经领取trx体验金)
	TgBindLastSendTime       time.Time `gorm:"column:TgBindLastSendTime;comment:tg机器人最后发送绑定指引消息的时间" json:"TgBindLastSendTime"`                                                // tg机器人最后发送绑定指引消息的时间
	UsdtGiftStatus           int32     `gorm:"column:UsdtGiftStatus;comment:USDT体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取 3:可领取 4:已领取)" json:"UsdtGiftStatus"`                       // USDT体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取 3:可领取 4:已领取)
	TrxGiftStatus            int32     `gorm:"column:TrxGiftStatus;comment:TRX体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取)" json:"TrxGiftStatus"`                                      // TRX体验金状态(-1:拒绝领取, 0:无体验金, 1:可领取 2:已领取)
	FirstSignTime            time.Time `gorm:"column:FirstSignTime;comment:首次签到时间" json:"FirstSignTime"`                                                                      // 首次签到时间
	LastSignTime             time.Time `gorm:"column:LastSignTime;comment:最后签到时间" json:"LastSignTime"`                                                                        // 最后签到时间
	InviteRewardUsers        int32     `gorm:"column:InviteRewardUsers;comment:邀请好友返奖人数" json:"InviteRewardUsers"`                                                            // 邀请好友返奖人数
	IsInviteReward           int32     `gorm:"column:IsInviteReward;default:2;comment:是否邀请好友返奖 1已返奖励 2未返奖励" json:"IsInviteReward"`                                            // 是否邀请好友返奖 1已返奖励 2未返奖励
	IsInResourceDb           int32     `gorm:"column:IsInResourceDb;comment:是否在库(0:否 1:是)" json:"IsInResourceDb"`                                                             // 是否在库(0:否 1:是)
	Kwai                     string    `gorm:"column:Kwai;comment:快手clickId" json:"Kwai"`                                                                                     // 快手clickId
	Bigo                     string    `gorm:"column:Bigo;comment:bigo clickId" json:"Bigo"`                                                                                  // bigo clickId
	Brand                    int32     `gorm:"column:Brand;default:1;comment:2 ut" json:"Brand"`                                                                              // 2 ut
	Fbc                      string    `gorm:"column:Fbc;comment:fb归因上报" json:"Fbc"`                                                                                          // fb归因上报
	AgentShortURL            string    `gorm:"column:AgentShortUrl;comment:代理短连接" json:"AgentShortUrl"`                                                                       // 代理短连接
	IsRechargeActive         bool      `gorm:"column:IsRechargeActive;comment:是否参与首充复充活动(true:参与 false：不参与)" json:"IsRechargeActive"`                                         // 是否参与首充复充活动(true:参与 false：不参与)
}

// TableName XUser's table name
func (*XUser) TableName() string {
	return TableNameXUser
}
