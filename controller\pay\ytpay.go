package paycontroller

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

var Ytpay = new(ytpay)

type ytpay struct {
	Base
}

// 初始化设置HTTP回调接口端点
func (c *ytpay) Init() {
	server.Http().PostNoAuth("/api/ytpay/recharge/callback", c.rechargeCallback)
	server.Http().PostNoAuth("/api/ytpay/withdraw/callback", c.withdrawCallback)
}

// 充值
func (c *ytpay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		ctx.RespErr(errors.New("payment method not found"), &errcode)
		return
	}

	// 验证币种是否匹配（使用数据库中配置的币种）
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	var cfg map[string]any
	json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg)

	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		ctx.RespErr(errors.New("user not found"), &errcode)
		return
	}

	var rate, amount float64 // 汇率和真实金额
	if user.SellerID == 26 {
		rate = 0                     // 运营商ID为26时，汇率设为0
		amount = float64(req.Amount) // 运营商ID为26时，不进行汇率转换
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID) // 获取汇率
		if err != nil {
			ctx.RespErr(errors.New("获取汇率失败"), &errcode) // 获取汇率失败，返回错误
			return
		}
		amount = float64(req.Amount) / rate // 运营商ID不为26时，进行汇率转换
	}

	// 构建 PayData
	payData := map[string]interface{}{
		"PayId":            req.MethodId,
		"Brand":            payMethod.Brand,
		"Name":             payMethod.Name,
		"IsRechargeActive": req.IsRechargeActive, // 添加充值活动标识
	}
	payDataBytes, _ := json.Marshal(payData)

	tx := server.DaoxHashGame().Begin()
	rechargeOrder := &model.XRecharge{
		SellerID:     user.SellerID,
		ChannelID:    user.ChannelID,
		UserID:       user.UserID,
		Symbol:       req.Symbol,
		PayID:        int32(req.MethodId),
		PayType:      20, // YTPay 的唯一 ID
		Amount:       req.Amount,
		RealAmount:   amount,
		TransferRate: rate,
		State:        3,
		CSGroup:      user.CSGroup,
		CSID:         user.CSID,
		SpecialAgent: int32(specialAgent),
		TopAgentID:   user.TopAgentID,
		PayData:      string(payDataBytes), // 添加 PayData 字段
	}

	err = tx.XRecharge.WithContext(ctx.Gin()).Omit(tx.XRecharge.PayTime, tx.XRecharge.TxID, tx.XRecharge.ToAddress, tx.XRecharge.OrderID).Create(rechargeOrder)
	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("创建订单失败"), &errcode)
		return
	}

	// 准备 YTPay API 的参数
	params := xgo.H{
		"merchantNo": cfg["mch_no"],
		"merchantSn": fmt.Sprintf("%d", rechargeOrder.ID),
		"amount":     fmt.Sprintf("%d", int(req.Amount)),
		"currency":   "INR",
		"notifyUrl":  fmt.Sprintf("%s/api/ytpay/recharge/callback", cfg["cburl"]),
		"returnUrl":  fmt.Sprintf("%s", cfg["cburl"]),
		"time":       fmt.Sprintf("%d", time.Now().Unix()),
	}

	// 生成签名
	signStr := c.generateYTPaySign(params, cfg["key"].(string))
	params["sign"] = signStr

	// 构建 form-data
	formData := url.Values{}
	for k, v := range params {
		formData.Set(k, fmt.Sprint(v))
	}

	// 发送请求
	resp, err := c.post(cfg["url"].(string)+"/api/Pay/order", map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}, []byte(formData.Encode()))
	logs.Info("YTPay response:", string(resp.Body()))

	if err != nil {
		tx.Rollback()
		ctx.RespErr(errors.New("发送支付请求失败"), &errcode)
		return
	}

	// YTPay 的响应结构
	type YTPayResponse struct {
		Code int             `json:"code"`
		Msg  string          `json:"msg"`
		Time string          `json:"time"`
		Data json.RawMessage `json:"data"` // 使用 RawMessage 处理不同类型
	}

	type YTPayResponseData struct {
		PayUrl     string  `json:"payUrl"`     // 支付 URL
		MerchantNo string  `json:"merchantNo"` // 商户 ID
		MerchantSn string  `json:"merchantSn"` // 商户订单 ID
		Sn         string  `json:"sn"`         // YTPay 订单 ID
		Amount     float64 `json:"amount"`     // 订单金额
		Currency   string  `json:"currency"`   // 货币 (INR)
		Status     int     `json:"status"`     // 订单状态
		Vps        string  `json:"vps"`        // VPS 信息
	}

	var response YTPayResponse
	if err = json.Unmarshal(resp.Body(), &response); err != nil {
		tx.Rollback()
		logs.Error("ytpay: 解析响应失败", err)
		ctx.RespErr(errors.New("解析响应失败"), &errcode)
		return
	}

	if response.Code != 200 {
		tx.Rollback()
		logs.Error("ytpay: 响应码不为200", response.Msg)
		ctx.RespErr(errors.New(response.Msg), &errcode)
		return
	}

	var responseData YTPayResponseData
	if err = json.Unmarshal(response.Data, &responseData); err != nil {
		tx.Rollback()
		logs.Error("ytpay: 解析响应数据失败", err)
		ctx.RespErr(errors.New("解析响应数据失败"), &errcode)
		return
	}

	tx.Commit()
	// 更新三方订单
	if err = c.updateThirdOrder(rechargeOrder.ID, responseData.Sn); err != nil {
		logs.Error("ytpay: 更新三方订单失败", err)
		ctx.Gin().String(400, "更新三方订单失败")
		return
	}

	ctx.RespOK(xgo.H{
		"payurl": responseData.PayUrl,
	})
}

// 生成 YTPay 签名
func (c *ytpay) generateYTPaySign(params xgo.H, key string) string {
	// 排序键
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signParts []string
	for _, k := range keys {
		signParts = append(signParts, fmt.Sprintf("%s=%v", k, params[k]))
	}
	signStr := strings.Join(signParts, "&") + "&key=" + key

	// 返回 MD5 哈希，大写
	return strings.ToUpper(c.md5(signStr))
}

// rechargeCallback 充值回调
func (c *ytpay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 记录原始请求数据用于调试
	logs.Info("充值回调原始请求头:", ctx.Gin().Request.Header)

	type YTPayCallback struct {
		MerchantNo  string  `json:"merchantNo"`
		Sn          string  `json:"sn"`
		MerchantSn  string  `json:"merchantSn"`
		OrderStatus int     `json:"orderStatus"`
		PayTime     int64   `json:"payTime"`
		CreateTime  int64   `json:"createTime"`
		Money       float64 `json:"money"`
		Tax         float64 `json:"tax"`
		Remark      string  `json:"remark"`
		Sign        string  `json:"sign"`
		Utr         string  `json:"utr"`
	}

	var callback YTPayCallback
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("ytpay:解析数据失败", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 填充结构体数据
	callback.MerchantNo = ctx.Gin().PostForm("merchantNo")
	callback.Sn = ctx.Gin().PostForm("sn")
	callback.MerchantSn = ctx.Gin().PostForm("merchantSn")
	callback.OrderStatus, _ = strconv.Atoi(ctx.Gin().PostForm("orderStatus"))
	callback.PayTime, _ = strconv.ParseInt(ctx.Gin().PostForm("payTime"), 10, 64)
	callback.CreateTime, _ = strconv.ParseInt(ctx.Gin().PostForm("createTime"), 10, 64)
	callback.Money, _ = strconv.ParseFloat(ctx.Gin().PostForm("money"), 64)
	callback.Tax, _ = strconv.ParseFloat(ctx.Gin().PostForm("tax"), 64)
	callback.Remark = ctx.Gin().PostForm("remark")
	callback.Sign = ctx.Gin().PostForm("sign")
	callback.Utr = ctx.Gin().PostForm("utr")
	// 记录解析后的回调数据
	callbackJSON, err := json.Marshal(callback)
	if err != nil {
		logs.Error("序列化回调数据失败:", err)
	} else {
		logs.Info("ytpay: 充值回调解析数据", string(callbackJSON))
	}
	logs.Info("充值回调订单 ID:", callback.MerchantSn)
	// 从 merchantSn 获取订单 ID
	orderId, _ := strconv.Atoi(callback.MerchantSn)
	if err != nil {
		logs.Error("ytpay: 用户不存在", err)
		ctx.Gin().String(400, "用户不存在")
		return
	}
	order, err := c.getRechargeOrder(int(orderId))
	if err != nil {
		logs.Error("ytpay: 订单不存在", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 记录订单详情
	logs.Info("充值订单详情 - ID:", orderId, "金额:", order.Amount)

	// 检查订单状态
	if callback.OrderStatus != 1 {
		logs.Warn("ytpay: 订单状态不成功. 状态:", callback.OrderStatus, "订单ID:", orderId)
		ctx.Gin().String(200, "订单状态不成功")
		return
	}

	// 检查金额
	if math.Abs(order.Amount-callback.Money) > 0.01 {
		logs.Info("ytpay: 金额不匹配. 预期:", order.Amount, "收到:", callback.Money)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 处理成功充值
	c.rechargeCallbackHandel(int(order.UserID), int(order.ID), 5)
	ctx.Gin().String(200, "success")
}

// withdrawCallback 提现下发回调
func (c *ytpay) withdrawCallback(ctx *abugo.AbuHttpContent) {
	// 记录原始请求数据用于调试
	logs.Info("提现回调原始请求数据:", ctx.Gin().Request.PostForm)
	logs.Info("提现回调原始请求头:", ctx.Gin().Request.Header)

	type YTPayWithdrawCallback struct {
		MerchantNo  string  `json:"merchantNo"`
		Sn          string  `json:"sn"`
		MerchantSn  string  `json:"merchantSn"`
		OrderStatus int     `json:"orderStatus"`
		PayTime     int64   `json:"payTime"`
		CreateTime  int64   `json:"createTime"`
		Money       float64 `json:"money"`
		Tax         float64 `json:"tax"`
		Remark      string  `json:"remark"`
		Sign        string  `json:"sign"`
		Utr         string  `json:"utr"`
	}

	var callback YTPayWithdrawCallback
	if err := ctx.Gin().Request.ParseForm(); err != nil {
		logs.Error("ytpay: 解析数据失败", err)
		ctx.Gin().String(400, "解析数据失败")
		return
	}

	// 填充结构体数据
	callback.MerchantNo = ctx.Gin().PostForm("merchantNo")
	callback.Sn = ctx.Gin().PostForm("sn")
	callback.MerchantSn = ctx.Gin().PostForm("merchantSn")
	callback.OrderStatus, _ = strconv.Atoi(ctx.Gin().PostForm("orderStatus"))
	callback.PayTime, _ = strconv.ParseInt(ctx.Gin().PostForm("payTime"), 10, 64)
	callback.CreateTime, _ = strconv.ParseInt(ctx.Gin().PostForm("createTime"), 10, 64)
	callback.Money, _ = strconv.ParseFloat(ctx.Gin().PostForm("money"), 64)
	callback.Tax, _ = strconv.ParseFloat(ctx.Gin().PostForm("tax"), 64)
	callback.Remark = ctx.Gin().PostForm("remark")
	callback.Sign = ctx.Gin().PostForm("sign")
	callback.Utr = ctx.Gin().PostForm("utr")

	// 记录解析后的回调数据
	logs.Info("提现回调解析数据:", callback)

	// 从 merchantSn 获取订单 ID
	orderId, err := strconv.Atoi(callback.MerchantSn)
	if err != nil {
		logs.Error("ytpay: 解析 merchantSn 失败", err)
		ctx.Gin().String(400, "解析 merchantSn 失败")
		return
	}

	order, err := c.getWithdrawOrder(orderId)
	if err != nil {
		logs.Error("ytpay: 订单不存在", err)
		ctx.Gin().String(400, "订单不存在")
		return
	}

	// 记录订单详情
	logs.Info("提现订单详情 - ID:", orderId, "金额:", order.RealAmount)

	// 检查金额
	if math.Abs(order.RealAmount-callback.Money) > 0.01 {
		c.withdrawCallbackHandel(int(order.ID), 7)
		logs.Info("ytpay: 金额不匹配. 预期:", order.RealAmount, "收到:", callback.Money)
		ctx.Gin().String(400, "金额不匹配")
		return
	}

	// 根据回调状态更新订单状态
	var newState int
	switch callback.OrderStatus {
	case 2: // 提现成功 (状态 2 = 成功)
		newState = 6
	case -1, 3: // 提现失败 (状态 -1 = 系统错误, 3 = 失败)
		newState = 7
	case 1: // 处理中 (状态 1 = 处理中)
		ctx.Gin().String(200, "订单处理中")
		return
	case 4: // 取消 (状态 4 = 撤销)
		newState = 7
	default:
		ctx.Gin().String(200, "未知状态")
		return
	}

	// 记录金额验证
	logs.Info("ytpay 金额验证 - 预期:", order.RealAmount, "收到:", callback.Money)

	// 记录状态转换
	logs.Info("ytpay 状态转换 - 订单ID:", orderId, "YTPay 状态:", callback.OrderStatus, "新状态:", newState)

	c.withdrawCallbackHandel(int(order.ID), newState)

	ctx.Gin().String(200, "success")
}
