// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXCommissionAudit(db *gorm.DB, opts ...gen.DOOption) xCommissionAudit {
	_xCommissionAudit := xCommissionAudit{}

	_xCommissionAudit.xCommissionAuditDo.UseDB(db, opts...)
	_xCommissionAudit.xCommissionAuditDo.UseModel(&model.XCommissionAudit{})

	tableName := _xCommissionAudit.xCommissionAuditDo.TableName()
	_xCommissionAudit.ALL = field.NewAsterisk(tableName)
	_xCommissionAudit.ID = field.NewInt32(tableName, "Id")
	_xCommissionAudit.UserID = field.NewInt32(tableName, "UserId")
	_xCommissionAudit.SellerID = field.NewInt32(tableName, "SellerId")
	_xCommissionAudit.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xCommissionAudit.State = field.NewInt32(tableName, "State")
	_xCommissionAudit.Symbol = field.NewString(tableName, "Symbol")
	_xCommissionAudit.Amount = field.NewFloat64(tableName, "Amount")
	_xCommissionAudit.CreateTime = field.NewTime(tableName, "CreateTime")
	_xCommissionAudit.Memo = field.NewString(tableName, "Memo")
	_xCommissionAudit.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xCommissionAudit.AuditTime = field.NewTime(tableName, "AuditTime")
	_xCommissionAudit.SendAccount = field.NewString(tableName, "SendAccount")
	_xCommissionAudit.SendTime = field.NewTime(tableName, "SendTime")
	_xCommissionAudit.Address = field.NewString(tableName, "Address")
	_xCommissionAudit.StartDate = field.NewTime(tableName, "StartDate")
	_xCommissionAudit.EndDate = field.NewTime(tableName, "EndDate")
	_xCommissionAudit.TrxPrice = field.NewFloat64(tableName, "TrxPrice")
	_xCommissionAudit.FinalAmount = field.NewFloat64(tableName, "FinalAmount")
	_xCommissionAudit.AgentMode = field.NewInt32(tableName, "AgentMode")
	_xCommissionAudit.GetType = field.NewInt32(tableName, "GetType")
	_xCommissionAudit.GetAmountType = field.NewInt32(tableName, "GetAmountType")
	_xCommissionAudit.RollingTimes = field.NewFloat64(tableName, "RollingTimes")

	_xCommissionAudit.fillFieldMap()

	return _xCommissionAudit
}

type xCommissionAudit struct {
	xCommissionAuditDo xCommissionAuditDo

	ALL           field.Asterisk
	ID            field.Int32 // id
	UserID        field.Int32 // 代理id
	SellerID      field.Int32 // 运营商
	ChannelID     field.Int32
	State         field.Int32   // 状态 1待审核 2 审核拒绝 3 审核通过 4已发放
	Symbol        field.String  // 币种
	Amount        field.Float64 // 申请金额
	CreateTime    field.Time    // 申请时间
	Memo          field.String  // 备注
	AuditAccount  field.String  // 审核人
	AuditTime     field.Time    // 审核时间
	SendAccount   field.String  // 发放人
	SendTime      field.Time    // 发放时间
	Address       field.String  // 代理钱包地址
	StartDate     field.Time    // 佣金产生开始时间
	EndDate       field.Time    // 佣金产生结束时间
	TrxPrice      field.Float64 // trx价格
	FinalAmount   field.Float64 // 最终金额
	AgentMode     field.Int32   // 1-无限代 2-三级代
	GetType       field.Int32   // 发放方式 1人工发放 2自动发放
	GetAmountType field.Int32   // 发放钱包 1真金钱包 2bonus钱包
	RollingTimes  field.Float64 // 打码倍数

	fieldMap map[string]field.Expr
}

func (x xCommissionAudit) Table(newTableName string) *xCommissionAudit {
	x.xCommissionAuditDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xCommissionAudit) As(alias string) *xCommissionAudit {
	x.xCommissionAuditDo.DO = *(x.xCommissionAuditDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xCommissionAudit) updateTableName(table string) *xCommissionAudit {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.UserID = field.NewInt32(table, "UserId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.State = field.NewInt32(table, "State")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.Memo = field.NewString(table, "Memo")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AuditTime = field.NewTime(table, "AuditTime")
	x.SendAccount = field.NewString(table, "SendAccount")
	x.SendTime = field.NewTime(table, "SendTime")
	x.Address = field.NewString(table, "Address")
	x.StartDate = field.NewTime(table, "StartDate")
	x.EndDate = field.NewTime(table, "EndDate")
	x.TrxPrice = field.NewFloat64(table, "TrxPrice")
	x.FinalAmount = field.NewFloat64(table, "FinalAmount")
	x.AgentMode = field.NewInt32(table, "AgentMode")
	x.GetType = field.NewInt32(table, "GetType")
	x.GetAmountType = field.NewInt32(table, "GetAmountType")
	x.RollingTimes = field.NewFloat64(table, "RollingTimes")

	x.fillFieldMap()

	return x
}

func (x *xCommissionAudit) WithContext(ctx context.Context) *xCommissionAuditDo {
	return x.xCommissionAuditDo.WithContext(ctx)
}

func (x xCommissionAudit) TableName() string { return x.xCommissionAuditDo.TableName() }

func (x xCommissionAudit) Alias() string { return x.xCommissionAuditDo.Alias() }

func (x xCommissionAudit) Columns(cols ...field.Expr) gen.Columns {
	return x.xCommissionAuditDo.Columns(cols...)
}

func (x *xCommissionAudit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xCommissionAudit) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 22)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["State"] = x.State
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AuditTime"] = x.AuditTime
	x.fieldMap["SendAccount"] = x.SendAccount
	x.fieldMap["SendTime"] = x.SendTime
	x.fieldMap["Address"] = x.Address
	x.fieldMap["StartDate"] = x.StartDate
	x.fieldMap["EndDate"] = x.EndDate
	x.fieldMap["TrxPrice"] = x.TrxPrice
	x.fieldMap["FinalAmount"] = x.FinalAmount
	x.fieldMap["AgentMode"] = x.AgentMode
	x.fieldMap["GetType"] = x.GetType
	x.fieldMap["GetAmountType"] = x.GetAmountType
	x.fieldMap["RollingTimes"] = x.RollingTimes
}

func (x xCommissionAudit) clone(db *gorm.DB) xCommissionAudit {
	x.xCommissionAuditDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xCommissionAudit) replaceDB(db *gorm.DB) xCommissionAudit {
	x.xCommissionAuditDo.ReplaceDB(db)
	return x
}

type xCommissionAuditDo struct{ gen.DO }

func (x xCommissionAuditDo) Debug() *xCommissionAuditDo {
	return x.withDO(x.DO.Debug())
}

func (x xCommissionAuditDo) WithContext(ctx context.Context) *xCommissionAuditDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xCommissionAuditDo) ReadDB() *xCommissionAuditDo {
	return x.Clauses(dbresolver.Read)
}

func (x xCommissionAuditDo) WriteDB() *xCommissionAuditDo {
	return x.Clauses(dbresolver.Write)
}

func (x xCommissionAuditDo) Session(config *gorm.Session) *xCommissionAuditDo {
	return x.withDO(x.DO.Session(config))
}

func (x xCommissionAuditDo) Clauses(conds ...clause.Expression) *xCommissionAuditDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xCommissionAuditDo) Returning(value interface{}, columns ...string) *xCommissionAuditDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xCommissionAuditDo) Not(conds ...gen.Condition) *xCommissionAuditDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xCommissionAuditDo) Or(conds ...gen.Condition) *xCommissionAuditDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xCommissionAuditDo) Select(conds ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xCommissionAuditDo) Where(conds ...gen.Condition) *xCommissionAuditDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xCommissionAuditDo) Order(conds ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xCommissionAuditDo) Distinct(cols ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xCommissionAuditDo) Omit(cols ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xCommissionAuditDo) Join(table schema.Tabler, on ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xCommissionAuditDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xCommissionAuditDo) RightJoin(table schema.Tabler, on ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xCommissionAuditDo) Group(cols ...field.Expr) *xCommissionAuditDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xCommissionAuditDo) Having(conds ...gen.Condition) *xCommissionAuditDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xCommissionAuditDo) Limit(limit int) *xCommissionAuditDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xCommissionAuditDo) Offset(offset int) *xCommissionAuditDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xCommissionAuditDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xCommissionAuditDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xCommissionAuditDo) Unscoped() *xCommissionAuditDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xCommissionAuditDo) Create(values ...*model.XCommissionAudit) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xCommissionAuditDo) CreateInBatches(values []*model.XCommissionAudit, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xCommissionAuditDo) Save(values ...*model.XCommissionAudit) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xCommissionAuditDo) First() (*model.XCommissionAudit, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCommissionAudit), nil
	}
}

func (x xCommissionAuditDo) Take() (*model.XCommissionAudit, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCommissionAudit), nil
	}
}

func (x xCommissionAuditDo) Last() (*model.XCommissionAudit, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCommissionAudit), nil
	}
}

func (x xCommissionAuditDo) Find() ([]*model.XCommissionAudit, error) {
	result, err := x.DO.Find()
	return result.([]*model.XCommissionAudit), err
}

func (x xCommissionAuditDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XCommissionAudit, err error) {
	buf := make([]*model.XCommissionAudit, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xCommissionAuditDo) FindInBatches(result *[]*model.XCommissionAudit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xCommissionAuditDo) Attrs(attrs ...field.AssignExpr) *xCommissionAuditDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xCommissionAuditDo) Assign(attrs ...field.AssignExpr) *xCommissionAuditDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xCommissionAuditDo) Joins(fields ...field.RelationField) *xCommissionAuditDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xCommissionAuditDo) Preload(fields ...field.RelationField) *xCommissionAuditDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xCommissionAuditDo) FirstOrInit() (*model.XCommissionAudit, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCommissionAudit), nil
	}
}

func (x xCommissionAuditDo) FirstOrCreate() (*model.XCommissionAudit, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XCommissionAudit), nil
	}
}

func (x xCommissionAuditDo) FindByPage(offset int, limit int) (result []*model.XCommissionAudit, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xCommissionAuditDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xCommissionAuditDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xCommissionAuditDo) Delete(models ...*model.XCommissionAudit) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xCommissionAuditDo) withDO(do gen.Dao) *xCommissionAuditDo {
	x.DO = *do.(*gen.DO)
	return x
}
