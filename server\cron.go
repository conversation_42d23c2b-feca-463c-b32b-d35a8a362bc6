package server

import (
	"github.com/beego/beego/logs"
	"github.com/robfig/cron/v3"
	"sync"
)

var (
	cronInstance *cron.Cron
	cronOnce     sync.Once
)

// getCronInstance 获取定时任务实例（单例模式）
func getCronInstance() *cron.Cron {
	cronOnce.Do(func() {
		// 创建一个支持秒级别的定时任务调度器
		cronInstance = cron.New(cron.WithSeconds())
		// 启动定时任务
		cronInstance.Start()
		logs.Info("定时任务调度器已启动")
	})
	return cronInstance
}

// RegisterCronJob 注册定时任务
// spec 为 cron 表达式，例如 "0 */5 * * * *" 表示每5分钟执行一次
// jobFunc 为要执行的函数
func RegisterCronJob(spec string, jobFunc func()) (cron.EntryID, error) {
	c := getCronInstance()
	id, err := c.AddFunc(spec, jobFunc)
	if err != nil {
		logs.Error("注册定时任务失败: %v", err)
		return id, err
	}
	logs.Info("成功注册定时任务: %s, ID: %d", spec, id)
	return id, nil
}

// RemoveCronJob 移除定时任务
func RemoveCronJob(id cron.EntryID) {
	c := getCronInstance()
	c.Remove(id)
	logs.Info("已移除定时任务: ID: %d", id)
}

// GetCronEntries 获取所有定时任务
func GetCronEntries() []cron.Entry {
	c := getCronInstance()
	return c.Entries()
}

// init 初始化定时任务模块
func init() {
	// 确保定时任务调度器在服务启动时就被初始化
	getCronInstance()
}
