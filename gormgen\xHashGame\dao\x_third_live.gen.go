// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXThirdLive(db *gorm.DB, opts ...gen.DOOption) xThirdLive {
	_xThirdLive := xThirdLive{}

	_xThirdLive.xThirdLiveDo.UseDB(db, opts...)
	_xThirdLive.xThirdLiveDo.UseModel(&model.XThirdLive{})

	tableName := _xThirdLive.xThirdLiveDo.TableName()
	_xThirdLive.ALL = field.NewAsterisk(tableName)
	_xThirdLive.ID = field.NewInt64(tableName, "Id")
	_xThirdLive.SellerID = field.NewInt32(tableName, "SellerId")
	_xThirdLive.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xThirdLive.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xThirdLive.UserID = field.NewInt32(tableName, "UserId")
	_xThirdLive.Brand = field.NewString(tableName, "Brand")
	_xThirdLive.ThirdID = field.NewString(tableName, "ThirdId")
	_xThirdLive.ThirdRefID = field.NewString(tableName, "ThirdRefId")
	_xThirdLive.GameID = field.NewString(tableName, "GameId")
	_xThirdLive.GameName = field.NewString(tableName, "GameName")
	_xThirdLive.BetAmount = field.NewFloat64(tableName, "BetAmount")
	_xThirdLive.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xThirdLive.ValidBet = field.NewFloat64(tableName, "ValidBet")
	_xThirdLive.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xThirdLive.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xThirdLive.ThirdTime = field.NewTime(tableName, "ThirdTime")
	_xThirdLive.Currency = field.NewString(tableName, "Currency")
	_xThirdLive.RawData = field.NewString(tableName, "RawData")
	_xThirdLive.State = field.NewInt32(tableName, "State")
	_xThirdLive.Fee = field.NewFloat64(tableName, "Fee")
	_xThirdLive.DataState = field.NewInt32(tableName, "DataState")
	_xThirdLive.CreateTime = field.NewTime(tableName, "CreateTime")
	_xThirdLive.CSGroup = field.NewString(tableName, "CSGroup")
	_xThirdLive.CSID = field.NewString(tableName, "CSId")
	_xThirdLive.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xThirdLive.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xThirdLive.BetCtx = field.NewString(tableName, "BetCtx")
	_xThirdLive.GameRst = field.NewString(tableName, "GameRst")
	_xThirdLive.BetCtxType = field.NewInt32(tableName, "BetCtxType")
	_xThirdLive.IP = field.NewString(tableName, "Ip")
	_xThirdLive.Lang = field.NewString(tableName, "Lang")
	_xThirdLive.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xThirdLive.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xThirdLive.BetType = field.NewInt32(tableName, "BetType")
	_xThirdLive.BonusBetAmount = field.NewFloat64(tableName, "BonusBetAmount")
	_xThirdLive.BonusWinAmount = field.NewFloat64(tableName, "BonusWinAmount")

	_xThirdLive.fillFieldMap()

	return _xThirdLive
}

type xThirdLive struct {
	xThirdLiveDo xThirdLiveDo

	ALL            field.Asterisk
	ID             field.Int64   // 自增Id
	SellerID       field.Int32   // 运营商
	ChannelID      field.Int32   // 渠道
	BetChannelID   field.Int32   // 下注渠道Id
	UserID         field.Int32   // 玩家id
	Brand          field.String  // 第三方品牌
	ThirdID        field.String  // 第三方订单号
	ThirdRefID     field.String  // 三方备用注单号
	GameID         field.String  // 游戏ID
	GameName       field.String  // 游戏名称
	BetAmount      field.Float64 // 下注金额
	WinAmount      field.Float64 // 派奖金额
	ValidBet       field.Float64 // 有效投注
	FirstLiuSui    field.Float64 // 扣减前有效投注
	ValidBetAmount field.Float64 // 有效投注
	ThirdTime      field.Time    // 第三方游戏时间
	Currency       field.String  // 币种
	RawData        field.String  // 第三方原始数据
	State          field.Int32
	Fee            field.Float64
	DataState      field.Int32 // 数据状态
	CreateTime     field.Time  // 同步时间
	CSGroup        field.String
	CSID           field.String
	TopAgentID     field.Int32
	SpecialAgent   field.Int32
	BetCtx         field.String  // 下注内容
	GameRst        field.String  // 开奖结果
	BetCtxType     field.Int32   // 1-默认类型 2-链接类型 3-json
	IP             field.String  // 登录ip
	Lang           field.String  // 登录语言
	BlackUserType  field.Int32   // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID    field.Int32   // 用户Id
	BetType        field.Int32   // 下注类型：0=纯真金 1=混合下注 2=彩金
	BonusBetAmount field.Float64 // Bonus币下注金额
	BonusWinAmount field.Float64 // Bonus币派彩金额

	fieldMap map[string]field.Expr
}

func (x xThirdLive) Table(newTableName string) *xThirdLive {
	x.xThirdLiveDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xThirdLive) As(alias string) *xThirdLive {
	x.xThirdLiveDo.DO = *(x.xThirdLiveDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xThirdLive) updateTableName(table string) *xThirdLive {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.Brand = field.NewString(table, "Brand")
	x.ThirdID = field.NewString(table, "ThirdId")
	x.ThirdRefID = field.NewString(table, "ThirdRefId")
	x.GameID = field.NewString(table, "GameId")
	x.GameName = field.NewString(table, "GameName")
	x.BetAmount = field.NewFloat64(table, "BetAmount")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.ValidBet = field.NewFloat64(table, "ValidBet")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.ThirdTime = field.NewTime(table, "ThirdTime")
	x.Currency = field.NewString(table, "Currency")
	x.RawData = field.NewString(table, "RawData")
	x.State = field.NewInt32(table, "State")
	x.Fee = field.NewFloat64(table, "Fee")
	x.DataState = field.NewInt32(table, "DataState")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.BetCtx = field.NewString(table, "BetCtx")
	x.GameRst = field.NewString(table, "GameRst")
	x.BetCtxType = field.NewInt32(table, "BetCtxType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.BetType = field.NewInt32(table, "BetType")
	x.BonusBetAmount = field.NewFloat64(table, "BonusBetAmount")
	x.BonusWinAmount = field.NewFloat64(table, "BonusWinAmount")

	x.fillFieldMap()

	return x
}

func (x *xThirdLive) WithContext(ctx context.Context) *xThirdLiveDo {
	return x.xThirdLiveDo.WithContext(ctx)
}

func (x xThirdLive) TableName() string { return x.xThirdLiveDo.TableName() }

func (x xThirdLive) Alias() string { return x.xThirdLiveDo.Alias() }

func (x xThirdLive) Columns(cols ...field.Expr) gen.Columns { return x.xThirdLiveDo.Columns(cols...) }

func (x *xThirdLive) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xThirdLive) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["Brand"] = x.Brand
	x.fieldMap["ThirdId"] = x.ThirdID
	x.fieldMap["ThirdRefId"] = x.ThirdRefID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["GameName"] = x.GameName
	x.fieldMap["BetAmount"] = x.BetAmount
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["ValidBet"] = x.ValidBet
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["ThirdTime"] = x.ThirdTime
	x.fieldMap["Currency"] = x.Currency
	x.fieldMap["RawData"] = x.RawData
	x.fieldMap["State"] = x.State
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["BetCtx"] = x.BetCtx
	x.fieldMap["GameRst"] = x.GameRst
	x.fieldMap["BetCtxType"] = x.BetCtxType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["BetType"] = x.BetType
	x.fieldMap["BonusBetAmount"] = x.BonusBetAmount
	x.fieldMap["BonusWinAmount"] = x.BonusWinAmount
}

func (x xThirdLive) clone(db *gorm.DB) xThirdLive {
	x.xThirdLiveDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xThirdLive) replaceDB(db *gorm.DB) xThirdLive {
	x.xThirdLiveDo.ReplaceDB(db)
	return x
}

type xThirdLiveDo struct{ gen.DO }

func (x xThirdLiveDo) Debug() *xThirdLiveDo {
	return x.withDO(x.DO.Debug())
}

func (x xThirdLiveDo) WithContext(ctx context.Context) *xThirdLiveDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xThirdLiveDo) ReadDB() *xThirdLiveDo {
	return x.Clauses(dbresolver.Read)
}

func (x xThirdLiveDo) WriteDB() *xThirdLiveDo {
	return x.Clauses(dbresolver.Write)
}

func (x xThirdLiveDo) Session(config *gorm.Session) *xThirdLiveDo {
	return x.withDO(x.DO.Session(config))
}

func (x xThirdLiveDo) Clauses(conds ...clause.Expression) *xThirdLiveDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xThirdLiveDo) Returning(value interface{}, columns ...string) *xThirdLiveDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xThirdLiveDo) Not(conds ...gen.Condition) *xThirdLiveDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xThirdLiveDo) Or(conds ...gen.Condition) *xThirdLiveDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xThirdLiveDo) Select(conds ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xThirdLiveDo) Where(conds ...gen.Condition) *xThirdLiveDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xThirdLiveDo) Order(conds ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xThirdLiveDo) Distinct(cols ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xThirdLiveDo) Omit(cols ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xThirdLiveDo) Join(table schema.Tabler, on ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xThirdLiveDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xThirdLiveDo) RightJoin(table schema.Tabler, on ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xThirdLiveDo) Group(cols ...field.Expr) *xThirdLiveDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xThirdLiveDo) Having(conds ...gen.Condition) *xThirdLiveDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xThirdLiveDo) Limit(limit int) *xThirdLiveDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xThirdLiveDo) Offset(offset int) *xThirdLiveDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xThirdLiveDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xThirdLiveDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xThirdLiveDo) Unscoped() *xThirdLiveDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xThirdLiveDo) Create(values ...*model.XThirdLive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xThirdLiveDo) CreateInBatches(values []*model.XThirdLive, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xThirdLiveDo) Save(values ...*model.XThirdLive) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xThirdLiveDo) First() (*model.XThirdLive, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLive), nil
	}
}

func (x xThirdLiveDo) Take() (*model.XThirdLive, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLive), nil
	}
}

func (x xThirdLiveDo) Last() (*model.XThirdLive, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLive), nil
	}
}

func (x xThirdLiveDo) Find() ([]*model.XThirdLive, error) {
	result, err := x.DO.Find()
	return result.([]*model.XThirdLive), err
}

func (x xThirdLiveDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XThirdLive, err error) {
	buf := make([]*model.XThirdLive, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xThirdLiveDo) FindInBatches(result *[]*model.XThirdLive, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xThirdLiveDo) Attrs(attrs ...field.AssignExpr) *xThirdLiveDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xThirdLiveDo) Assign(attrs ...field.AssignExpr) *xThirdLiveDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xThirdLiveDo) Joins(fields ...field.RelationField) *xThirdLiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xThirdLiveDo) Preload(fields ...field.RelationField) *xThirdLiveDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xThirdLiveDo) FirstOrInit() (*model.XThirdLive, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLive), nil
	}
}

func (x xThirdLiveDo) FirstOrCreate() (*model.XThirdLive, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XThirdLive), nil
	}
}

func (x xThirdLiveDo) FindByPage(offset int, limit int) (result []*model.XThirdLive, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xThirdLiveDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xThirdLiveDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xThirdLiveDo) Delete(models ...*model.XThirdLive) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xThirdLiveDo) withDO(do gen.Dao) *xThirdLiveDo {
	x.DO = *do.(*gen.DO)
	return x
}
