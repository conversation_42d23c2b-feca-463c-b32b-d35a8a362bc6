// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXActiveRewardAudit = "x_active_reward_audit"

// XActiveRewardAudit 活动发放审核表
type XActiveRewardAudit struct {
	ID                int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"`                                                 // id
	SellerID          int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                                                                  // 运营商
	ChannelID         int32     `gorm:"column:ChannelId;comment:渠道" json:"ChannelId"`                                                                 // 渠道
	UserID            int32     `gorm:"column:UserId;comment:玩家ID" json:"UserId"`                                                                     // 玩家ID
	ActiveDefineID    int32     `gorm:"column:ActiveDefineId;comment:x_active_define.Id" json:"ActiveDefineId"`                                       // x_active_define.Id
	ActiveID          int32     `gorm:"column:ActiveId;comment:活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎" json:"ActiveId"`          // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	ActiveLevel       int32     `gorm:"column:ActiveLevel;comment:活动等级  当ActiveId=8的时候 ActiveLevel表示游戏的种类1哈希 2原创 3电子 4棋牌 5真人 6运动" json:"ActiveLevel"` // 活动等级  当ActiveId=8的时候 ActiveLevel表示游戏的种类1哈希 2原创 3电子 4棋牌 5真人 6运动
	ActiveMemo        string    `gorm:"column:ActiveMemo;comment:活动额外说明" json:"ActiveMemo"`                                                           // 活动额外说明
	RecordDate        time.Time `gorm:"column:RecordDate;comment:活动时间" json:"RecordDate"`                                                             // 活动时间
	Amount            float64   `gorm:"column:Amount;default:0.000000;comment:活动送金" json:"Amount"`                                                    // 活动送金
	MinLiuShui        float64   `gorm:"column:MinLiuShui;default:0.000000;comment:提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比" json:"MinLiuShui"`                  // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	AuditState        int32     `gorm:"column:AuditState;comment:审核状态 1待审核,2审核拒绝,3审核通过,4自动通过" json:"AuditState"`                                      // 审核状态 1待审核,2审核拒绝,3审核通过,4自动通过
	AuditAccount      string    `gorm:"column:AuditAccount;comment:审核账号" json:"AuditAccount"`                                                         // 审核账号
	AuditTime         time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                                               // 审核时间
	AuditMemo         string    `gorm:"column:AuditMemo;comment:审核备注" json:"AuditMemo"`                                                               // 审核备注
	CreateTime        time.Time `gorm:"column:CreateTime;comment:创建时间" json:"CreateTime"`                                                             // 创建时间
	OrderID           int32     `gorm:"column:OrderId;comment:活动1注单Id" json:"OrderId"`                                                                // 活动1注单Id
	AmountTrx         float64   `gorm:"column:AmountTrx;default:0.000000;comment:活动1 送金的trx" json:"AmountTrx"`                                        // 活动1 送金的trx
	GasFee            float64   `gorm:"column:GasFee;default:0.000000;comment:活动1 派奖上链gas费" json:"GasFee"`                                            // 活动1 派奖上链gas费
	TotalRecharge     float64   `gorm:"column:TotalRecharge;default:0.000000;comment:活动2 6 当日充值金额" json:"TotalRecharge"`                              // 活动2 6 当日充值金额
	LiuShui           float64   `gorm:"column:LiuShui;default:0.000000;comment:流水有效投注" json:"LiuShui"`                                                // 流水有效投注
	Address           string    `gorm:"column:Address;comment:活动1 玩家地址" json:"Address"`                                                               // 活动1 玩家地址
	TxID              string    `gorm:"column:TxId;comment:活动1 交易哈希" json:"TxId"`                                                                     // 活动1 交易哈希
	NetWinLoss        float64   `gorm:"column:NetWinLoss;default:0.000000;comment:活动6 当日净亏损" json:"NetWinLoss"`                                       // 活动6 当日净亏损
	Config            string    `gorm:"column:Config;comment:配置项" json:"Config"`                                                                      // 配置项
	BaseConfig        string    `gorm:"column:BaseConfig;comment:基础配置" json:"BaseConfig"`                                                             // 基础配置
	FirstRecharge     float64   `gorm:"column:FirstRecharge;default:0.000000;comment:首充金额" json:"FirstRecharge"`                                      // 首充金额
	FirstSignTime     time.Time `gorm:"column:FirstSignTime;comment:首次签到时间" json:"FirstSignTime"`                                                     // 首次签到时间
	LastSignTime      time.Time `gorm:"column:LastSignTime;comment:最后签到时间" json:"LastSignTime"`                                                       // 最后签到时间
	InviteRewardType  int32     `gorm:"column:InviteRewardType;comment:邀请好友奖励分类 0其他 1单人奖励 2额外奖励" json:"InviteRewardType"`                             // 邀请好友奖励分类 0其他 1单人奖励 2额外奖励
	ChildUserID       int32     `gorm:"column:ChildUserId;comment:下级UserId" json:"ChildUserId"`                                                       // 下级UserId
	InviteRewardUsers int32     `gorm:"column:InviteRewardUsers;comment:邀请好友额外奖励用户数量" json:"InviteRewardUsers"`                                       // 邀请好友额外奖励用户数量
	GameType          string    `gorm:"column:GameType;comment:游戏分类" json:"GameType"`                                                                 // 游戏分类
	UserIP            string    `gorm:"column:UserIP;comment:用户IP" json:"UserIP"`                                                                     // 用户IP
	DeviceID          string    `gorm:"column:DeviceID;comment:设备 ID" json:"DeviceID"`                                                                // 设备 ID
	RedeemCode        string    `gorm:"column:RedeemCode;comment:本次使用的兑换码" json:"RedeemCode"`                                                         // 本次使用的兑换码
}

// TableName XActiveRewardAudit's table name
func (*XActiveRewardAudit) TableName() string {
	return TableNameXActiveRewardAudit
}
