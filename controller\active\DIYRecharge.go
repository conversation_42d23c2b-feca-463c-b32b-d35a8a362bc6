package active

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/beego/beego/logs"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"strings"
	"time"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
)

type DIYRechargeActive struct {
	model.XActiveDefine
	BaseConfig   DIYRechargeActiveBaseConfig
	RewardConfig DIYRechargeActiveRewardConfig
}

// 自定义充值活动-基础配置
type DIYRechargeActiveBaseConfig struct {
	/*// 置顶活动图
	TopImages []struct {
		LanguageTag  string // 语言标签(ISO 639-1 和 RFC 4646 标准) 例：zh,en,zh-hant,th,ko,tr,vi,pt,hi,ja,ru,km,uk
		LanguageName string // 语言名称 例：中文,英文,繁体中文,泰语,韩语,土耳其语,越南语,葡萄牙语,印地语,日语,俄语,高棉语(柬埔寨),乌克兰语
		ImageUrl     string // 图片链接
	}
	// 活动图
	Images []struct {
		LanguageTag  string // 语言标签(ISO 639-1 和 RFC 4646 标准) 例：zh,en,zh-hant,th,ko,tr,vi,pt,hi,ja,ru,km,uk
		LanguageName string // 语言名称 例：中文,英文,繁体中文,泰语,韩语,土耳其语,越南语,葡萄牙语,印地语,日语,俄语,高棉语(柬埔寨),乌克兰语
		ImageUrl     string // 图片链接
	}*/
	// 参与方式与条件
	Join struct {
		Type         int      // 参与方式: 1手动 2自动
		RegisterDays int      // 注册天数
		IsBindEmail  bool     // 是否需要绑定邮箱
		IsDuringReg  bool     // 是否在活动时间内注册
		IsIPLimit    bool     // 是否限制某些IP不能参加
		BlackIPList  string   // 限制的IP列表
		GameType     []string // 参与活动的场馆
		VIPLevels    []int32  // 参与活动的VIP等级
	}
	// 奖励发放方式与条件
	Reward struct {
		SendType  int32 // 1人工发放 2自动发放
		IsIPLimit bool  // 同IP每日只能领取一次
	}
	RechargeCalcType   int   // 充值计算方式:  1每日单笔 2每日累计 3活动期内单笔 4活动期内累计
	CalcType           int   // 计算方式: 1单次 2累计
	UnitType           int   // 计算单位: 1金额 2次数
	CycleType          int   // 计算周期: 0活动期内 1每日 2每周 3每月
	TotalNumberOfCycle int64 // 总可参与周期数
}

// 自定义充值活动-奖励方案配置
type DIYRechargeActiveRewardConfig struct {
	PlanList []DIYRechargeActivePlan
}

type DIYRechargeActivePlan struct {
	IsEnable       bool // 是否启用
	PlanType       int  // 方案类型: 1(方案1)按充值金额匹配 2(方案2)按第几次充值和充值金额匹配
	EffectiveHours int  // 活动有效期（小时）
	// 奖励档位
	RewardList []DIYRechargeActiveReward
}

type DIYRechargeActiveReward struct {
	NumberOfTimes int64 // 可参与次数
	// 奖励发放条件
	RewardCondition struct {
		RechargeNumber    int             // 充值次数
		MinRechargeAmount decimal.Decimal // 最低充值金额
		MaxRechargeAmount decimal.Decimal // 最高充值金额
		LiushuiMultiple   int64           // 充值流水倍数
	}
	// 奖励内容
	Reward struct {
		//Symbol    string          // 币种 目前只有USDT
		Amount    decimal.Decimal // 奖励金额
		IsPercent bool            // 奖励金额百分号
		MaxAmount decimal.Decimal // 最高奖励金额
	}
	// 奖励提现条件
	WithdrawCondition struct {
		Type                    int    // 1充值流水 2奖励流水 3充值流水+奖励流水
		Multiple                string // 流水倍数
		RechargeLiushuiMultiple int    // 充值流水倍数
		RewardLiushuiMultiple   int    // 奖励流水倍数
	}
}

func NewDIYRechargeActiveEntity(tb *model.XActiveDefine) (entity *DIYRechargeActive) {
	entity = &DIYRechargeActive{
		XActiveDefine: *tb,
	}
	_ = json.Unmarshal([]byte(tb.BaseConfig), &entity.BaseConfig)
	_ = json.Unmarshal([]byte(tb.Config), &entity.RewardConfig)
	return entity
}

func (a *DIYRechargeActive) GetEnablePlan() (plan *DIYRechargeActivePlan) {
	for _, p := range a.RewardConfig.PlanList {
		if p.IsEnable {
			plan = &p
			break
		}
	}
	return plan
}

func (a *DIYRechargeActive) GetRewardConfig(level int) (reward *DIYRechargeActiveReward) {
	if level < 0 {
		return nil
	}
	plan := a.GetEnablePlan()
	if plan == nil {
		return nil
	}
	if level >= len(plan.RewardList) {
		return nil
	}
	reward = &plan.RewardList[level]
	return reward
}

// 检查活动是否开启
func (a *DIYRechargeActive) IsEnable() (ok bool, err error) {
	now := time.Now()
	if a.EffectStartTime > 0 && time.UnixMilli(a.EffectStartTime).After(now) {
		return false, errors.New("活动未开始")
	}
	if a.EffectEndTime > 0 && time.UnixMilli(a.EffectEndTime).Before(now) {
		return false, errors.New("活动已结束")
	}
	if a.State != 1 {
		return false, errors.New("活动未开启")
	}
	return true, nil
}

// 检查用户是否可参与活动
func (a *DIYRechargeActive) CheckUserCanJoin(user *model.XUser) (ok bool, err error) {
	if ok, err = a.IsEnable(); !ok {
		return false, err
	}
	if user.RegisterTime.Add(time.Duration(a.BaseConfig.Join.RegisterDays) * time.Hour * 24).Before(time.Now()) {
		return false, errors.New("注册时间不满足要求")
	}
	startTime := time.UnixMilli(a.EffectStartTime)
	endTime := time.UnixMilli(a.EffectEndTime)
	if a.BaseConfig.Join.IsDuringReg && (user.RegisterTime.Before(startTime) || (a.EffectEndTime > 0 && user.RegisterTime.After(endTime))) {
		return false, errors.New("注册时间不满足要求")
	}
	if a.BaseConfig.Join.IsBindEmail && user.Email == "" {
		return false, errors.New("未绑定邮箱")
	}
	if a.BaseConfig.Join.IsIPLimit {
		idx := strings.Index(a.BaseConfig.Join.BlackIPList, user.LoginIP)
		if idx != -1 {
			return false, errors.New("用户IP不可参与活动")
		}
	}
	var vipLv int32
	xVipInfo := server.DaoxHashGame().XVipInfo
	vip, err := xVipInfo.WithContext(context.Background()).Where(xVipInfo.UserID.Eq(user.UserID)).First()
	if err == nil {
		vipLv = vip.VipLevel
	}
	var vipok bool
	for _, lv := range a.BaseConfig.Join.VIPLevels {
		if lv == vipLv {
			vipok = true
		}
	}
	if !vipok {
		return false, errors.New("VIP等级不满足要求")
	}

	return true, nil
}

// 检查用户是否已加入活动
func (a *DIYRechargeActive) CheckUserIsJoined(user *model.XUser) bool {
	if a.BaseConfig.Join.Type != 1 {
		return true
	}
	xUserActive := server.DaoxHashGame().XUserActive
	count, _ := xUserActive.WithContext(context.Background()).
		Where(xUserActive.UserID.Eq(user.UserID)).
		Where(xUserActive.ActiveDefineID.Eq(a.ID)).
		Count()
	return count > 0
}

// 活动达成数据
type CompletionData struct {
	Level             int             // 奖励档位下标
	NumberOfTimes     int64           // 可参与次数
	CompleteTimes     int64           // 已完成次数
	RewardNeedLiushui decimal.Decimal // 领奖所需流水
	CompleteLiushui   decimal.Decimal // 已完成完成领奖所需流水
}

// 用户手动参加活动
func (a *DIYRechargeActive) UserJoin(user *model.XUser) (err error) {
	if ok, err := a.CheckUserCanJoin(user); !ok {
		return err
	}
	if a.BaseConfig.Join.Type != 1 {
		return errors.New("无需手动参加")
	}
	xUserActive := server.DaoxHashGame().XUserActive
	count, _ := xUserActive.WithContext(context.Background()).
		Where(xUserActive.UserID.Eq(user.UserID)).
		Where(xUserActive.ActiveDefineID.Eq(a.ID)).
		Count()
	if count > 0 {
		return errors.New("您已参加过该活动，无需重复参加")
	}
	userActiveData := &model.XUserActive{
		UserID:         user.UserID,
		ActiveDefineID: a.ID,
		JoinTime:       time.Now(),
	}
	var completionDataList []CompletionData
	plan := a.GetEnablePlan()
	if plan == nil {
		return errors.New("奖励方案无效")
	}
	for i, v := range plan.RewardList {
		completionDataList = append(completionDataList, CompletionData{
			Level:         i,
			NumberOfTimes: v.NumberOfTimes,
		})
	}
	completionData, _ := json.Marshal(completionDataList)
	userActiveData.CompletionData = string(completionData)
	err = xUserActive.WithContext(context.Background()).Create(userActiveData)
	return err
}

type UserActiveData struct {
	*model.XUserActive
	CompletionDataList []CompletionData
}

// 活动用户数据
func (a *DIYRechargeActive) UserActiveData(userId int32) (data *UserActiveData, err error) {
	xUserActive := server.DaoxHashGame().XUserActive
	tb, err := xUserActive.WithContext(context.Background()).
		Where(xUserActive.UserID.Eq(userId)).
		Where(xUserActive.ActiveDefineID.Eq(a.ID)).
		First()
	if err != nil {
		return nil, err
	}
	data = &UserActiveData{
		XUserActive: tb,
	}
	err = json.Unmarshal([]byte(tb.CompletionData), &data.CompletionDataList)
	if err != nil {
		return nil, err
	}
	// todo: 查询 已完成次数 和 已完成完成领奖所需流水

	return data, err
}

func (d *UserActiveData) UpdateCompletionData() (err error) {
	completionData, _ := json.Marshal(d.CompletionDataList)
	d.CompletionData = string(completionData)
	xUserActive := server.DaoxHashGame().XUserActive
	_, err = xUserActive.WithContext(context.Background()).
		Where(xUserActive.UserID.Eq(d.UserID)).
		Where(xUserActive.ActiveDefineID.Eq(d.ActiveDefineID)).
		Update(xUserActive.CompletionData, d.CompletionData)
	return err
}

func (a *DIYRechargeActive) GetRewardCfgByRecharge(amount decimal.Decimal, count int) (rewardCfg *DIYRechargeActiveReward, rewardIdx int) {
	plan := a.GetEnablePlan()
	if plan == nil {
		return nil, 0
	}
	for i, v := range plan.RewardList {
		if plan.PlanType == 1 {
			if (amount.GreaterThanOrEqual(v.RewardCondition.MinRechargeAmount) &&
				v.RewardCondition.MaxRechargeAmount.LessThanOrEqual(decimal.Zero)) ||
				(amount.GreaterThanOrEqual(v.RewardCondition.MinRechargeAmount) &&
					amount.LessThanOrEqual(v.RewardCondition.MaxRechargeAmount)) {
				rewardCfg = &v
				rewardIdx = i
				break
			}
		} else if plan.PlanType == 2 {
			if i == count-1 {
				if (amount.GreaterThanOrEqual(v.RewardCondition.MinRechargeAmount) &&
					v.RewardCondition.MaxRechargeAmount.LessThanOrEqual(decimal.Zero)) ||
					(amount.GreaterThanOrEqual(v.RewardCondition.MinRechargeAmount) &&
						amount.LessThanOrEqual(v.RewardCondition.MaxRechargeAmount)) {
					rewardCfg = &v
					rewardIdx = i
					break
				}
			}
		}
	}
	return rewardCfg, rewardIdx
}

// 用户充值
// amount: 充值金额
// count:  第几次充值
func (a *DIYRechargeActive) UserRecharge(userId int32, amount decimal.Decimal, count int) (err error) {
	rewardCfg, rewardIdx := a.GetRewardCfgByRecharge(amount, count)
	if rewardCfg == nil {
		return nil
	}
	uad, err := a.UserActiveData(userId)
	if err != nil {
		return err
	}
	if rewardIdx < len(uad.CompletionDataList) {
		uad.CompletionDataList[rewardIdx].RewardNeedLiushui = uad.CompletionDataList[rewardIdx].RewardNeedLiushui.Add(
			amount.Mul(decimal.NewFromInt(rewardCfg.RewardCondition.LiushuiMultiple)),
		)
		err = uad.UpdateCompletionData()
	}
	return err
}

// 用户领取奖励
func (a *DIYRechargeActive) UserTakeReward(user *model.XUser, level int) (err error) {
	if !a.CheckUserIsJoined(user) {
		return errors.New("未参加活动")
	}
	plan := a.GetEnablePlan()
	if plan == nil {
		return errors.New("无效操作!")
	}
	rewardCfg := a.GetRewardConfig(level)
	if rewardCfg == nil {
		return errors.New("无效操作!")
	}

	//isAutoSend := a.BaseConfig.Reward.SendType == 2
	xActiveRewardAudit := server.DaoxHashGame().XActiveRewardAudit
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	if a.BaseConfig.Reward.IsIPLimit {
		count, _ := xActiveRewardAudit.WithContext(context.Background()).
			Where(xActiveRewardAudit.ActiveDefineID.Eq(a.ID)).
			Where(xActiveRewardAudit.RecordDate.Eq(today)).
			Where(xActiveRewardAudit.UserIP.Eq(user.LoginIP)).
			Count()
		if count > 0 {
			logs.Error("IP重复无法领取! UserID:%d, IP:%s, ActiveId:%d, x_active_define.Id:%d, Level:%d",
				user.UserID, user.LoginIP, a.ActiveID, a.ID, level)
			return errors.New("无法领取，请联系客服!")
		}
	}

	// 总可参与天数限制
	if a.BaseConfig.CycleType == 1 && a.BaseConfig.TotalNumberOfCycle > 0 {
		count, _ := xActiveRewardAudit.WithContext(context.Background()).
			Where(xActiveRewardAudit.UserID.Eq(user.UserID)).
			Where(xActiveRewardAudit.ActiveID.Eq(a.ActiveID)).
			Where(xActiveRewardAudit.ActiveDefineID.Eq(a.ID)).
			Group(xActiveRewardAudit.RecordDate).
			Count()
		if count >= a.BaseConfig.TotalNumberOfCycle {
			return errors.New("可参与天数达到上限")
		}
	}

	if rewardCfg.NumberOfTimes > 0 {
		db := xActiveRewardAudit.WithContext(context.Background()).
			Where(xActiveRewardAudit.UserID.Eq(user.UserID)).
			Where(xActiveRewardAudit.ActiveID.Eq(a.ActiveID)).
			Where(xActiveRewardAudit.ActiveLevel.Eq(int32(level))).
			Where(xActiveRewardAudit.ActiveDefineID.Eq(a.ID))
		if a.BaseConfig.CycleType == 1 {
			db = db.Where(xActiveRewardAudit.RecordDate.Eq(today))
		}
		count, _ := db.Count()
		if count >= rewardCfg.NumberOfTimes {
			return errors.New("可领取数达到上限")
		}
	}

	startTime := time.UnixMilli(a.EffectStartTime)
	endTime := time.UnixMilli(a.EffectEndTime)

	xRecharge := server.DaoxHashGame().XRecharge
	rechargeDb := xRecharge.WithContext(context.Background()).
		Where(xRecharge.State.Eq(5))
	if a.BaseConfig.CycleType == 0 {
		rechargeDb = rechargeDb.Where(xRecharge.PayTime.Gte(startTime))
		if a.EffectEndTime > 0 {
			rechargeDb = rechargeDb.Where(xRecharge.PayTime.Lte(endTime))
		}
	} else if a.BaseConfig.CycleType == 1 {
		rechargeDb = rechargeDb.Where(xRecharge.PayTime.DateFormat("%Y-%m-%d").Eq(today.Format(time.DateOnly)))
	} else {
		return errors.New("CycleType 配置错误")
	}
	rechargeList, err := rechargeDb.Find()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("UserTakeReward err:", err)
		return err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New("不满足领取条件")
	}

	if plan.PlanType == 1 {
		var totalAmount decimal.Decimal
		for _, v := range rechargeList {
			totalAmount = decimal.NewFromFloat(v.RealAmount).Add(totalAmount)
		}
		if totalAmount.LessThan(rewardCfg.RewardCondition.MinRechargeAmount) {
			return errors.New("充值金额不满足领取条件")
		}
		if rewardCfg.RewardCondition.MaxRechargeAmount.GreaterThan(decimal.Zero) &&
			totalAmount.GreaterThan(rewardCfg.RewardCondition.MaxRechargeAmount) {
			return errors.New("充值金额不满足领取条件")
		}
	} else if plan.PlanType == 2 {

	} else {
		return errors.New("PlanType 配置错误")
	}
	// todo: 未完成

	return err
}

// 用户奖励提现
