// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: MIT-0
// snippet-start:[ses.go.send_message]
package xemail

// snippet-start:[ses.go.send_message.imports]
import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ses"
	"github.com/aws/aws-sdk-go/service/ses/sesiface"
	"github.com/spf13/viper"
)

// snippet-end:[ses.go.send_message.imports]

// snippet-start:[ses.go.send_message.consts]
const (
	// accessKeyID     = "********************"
	// secretAccessKey = "V32EnPtLCbZ/bQkxTkcNuEfXMas825myzsz6Z79e"

	// Subject is the subject line for the email
	Subject = "Amazon SES Test (AWS SDK for Go)"

	// HTMLBody is the HTML body for the email
	HTMLBody = "<h1>Amazon SES Test Email (AWS SDK for Go)</h1><p>This email was sent with " +
		"<a href='https://aws.amazon.com/ses/'>Amazon SES</a> using the " +
		"<a href='https://aws.amazon.com/sdk-for-go/'>AWS SDK for Go</a>.</p>"

	// TextBody is the email body for recipients with non-HTML email clients
	TextBody = "This email was sent with Amazon SES using the AWS SDK for Go."

	// CharSet is the character encoding for the email
	CharSet = "UTF-8"
)

// snippet-end:[ses.go.send_message.consts]

// SendMsg sends an email message to an Amazon SES recipient
// Inputs:
//
//	svc is the Amazon SES service client
//	sender is the email address in the From field
//	recipient is the email address in the To field
//
// Output:
//
//	If success, nil
//	Otherwise, an error from the call to SendEmail
func sendMsg(svc sesiface.SESAPI, sender, recipient, subject, body string) error {
	// snippet-start:[ses.go.send_message.call]
	_, err := svc.SendEmail(&ses.SendEmailInput{
		Destination: &ses.Destination{
			CcAddresses: []*string{},
			ToAddresses: []*string{
				&recipient,
			},
		},
		Message: &ses.Message{
			Body: &ses.Body{
				Html: &ses.Content{
					Charset: aws.String(CharSet),
					Data:    aws.String(body),
				},
			},
			Subject: &ses.Content{
				Charset: aws.String(CharSet),
				Data:    aws.String(subject),
			},
		},
		Source: &sender,
	})

	return err
}

func SendEmail(seller, code, lang, toAddress string) error {
	sender := "<EMAIL>"
	recipient := toAddress
	subject := ""
	body := ""
	accessKeyID := viper.GetString("aws.email.access_key_id")
	secretAccessKey := viper.GetString("aws.email.secret_access_key")

	if lang == "zh" {
		subject = fmt.Sprintf("邮箱接码")

		body = fmt.Sprintf(`
			<br>尊敬的用户 <br><br>
			您的邮箱验证码为： %s <br><br><br>
			请勿向任何人包括客服提供验证码
		`, code)
	} else {
		subject = fmt.Sprintf("email code")

		body = fmt.Sprintf(`
			<br>Dear user <br><br>
			Your email verification code is  %s <br><br><br>
			Do not provide the verification code to any people
		`, code)
	}

	//
	sess := session.Must(session.NewSessionWithOptions(session.Options{
		Config: aws.Config{
			Region:      aws.String("ap-southeast-1"),
			Credentials: credentials.NewStaticCredentials(accessKeyID, secretAccessKey, ""),
		},
	}))

	svc := ses.New(sess)

	err := sendMsg(svc, sender, recipient, subject, body)
	if err != nil {
		return err
	}

	return nil
}

func SendEmailCommon(toAddress, subject, body string) error {
	sender := "<EMAIL>"
	recipient := toAddress
	accessKeyID := viper.GetString("aws.email.access_key_id")
	secretAccessKey := viper.GetString("aws.email.secret_access_key")
	sess := session.Must(session.NewSessionWithOptions(session.Options{
		Config: aws.Config{
			Region:      aws.String("ap-southeast-1"),
			Credentials: credentials.NewStaticCredentials(accessKeyID, secretAccessKey, ""),
		},
	}))
	svc := ses.New(sess)
	err := sendMsg(svc, sender, recipient, subject, body)
	if err != nil {
		return err
	}
	return nil
}
