// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXRobotPushMsgConfig(db *gorm.DB, opts ...gen.DOOption) xRobotPushMsgConfig {
	_xRobotPushMsgConfig := xRobotPushMsgConfig{}

	_xRobotPushMsgConfig.xRobotPushMsgConfigDo.UseDB(db, opts...)
	_xRobotPushMsgConfig.xRobotPushMsgConfigDo.UseModel(&model.XRobotPushMsgConfig{})

	tableName := _xRobotPushMsgConfig.xRobotPushMsgConfigDo.TableName()
	_xRobotPushMsgConfig.ALL = field.NewAsterisk(tableName)
	_xRobotPushMsgConfig.ID = field.NewInt64(tableName, "id")
	_xRobotPushMsgConfig.Name = field.NewString(tableName, "name")
	_xRobotPushMsgConfig.Remark = field.NewString(tableName, "remark")
	_xRobotPushMsgConfig.CreateTime = field.NewTime(tableName, "create_time")
	_xRobotPushMsgConfig.UpdateTime = field.NewTime(tableName, "update_time")

	_xRobotPushMsgConfig.fillFieldMap()

	return _xRobotPushMsgConfig
}

// xRobotPushMsgConfig 用户标签
type xRobotPushMsgConfig struct {
	xRobotPushMsgConfigDo xRobotPushMsgConfigDo

	ALL        field.Asterisk
	ID         field.Int64  // id
	Name       field.String // 名称
	Remark     field.String // 备注
	CreateTime field.Time   // 创建日期
	UpdateTime field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (x xRobotPushMsgConfig) Table(newTableName string) *xRobotPushMsgConfig {
	x.xRobotPushMsgConfigDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xRobotPushMsgConfig) As(alias string) *xRobotPushMsgConfig {
	x.xRobotPushMsgConfigDo.DO = *(x.xRobotPushMsgConfigDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xRobotPushMsgConfig) updateTableName(table string) *xRobotPushMsgConfig {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt64(table, "id")
	x.Name = field.NewString(table, "name")
	x.Remark = field.NewString(table, "remark")
	x.CreateTime = field.NewTime(table, "create_time")
	x.UpdateTime = field.NewTime(table, "update_time")

	x.fillFieldMap()

	return x
}

func (x *xRobotPushMsgConfig) WithContext(ctx context.Context) *xRobotPushMsgConfigDo {
	return x.xRobotPushMsgConfigDo.WithContext(ctx)
}

func (x xRobotPushMsgConfig) TableName() string { return x.xRobotPushMsgConfigDo.TableName() }

func (x xRobotPushMsgConfig) Alias() string { return x.xRobotPushMsgConfigDo.Alias() }

func (x xRobotPushMsgConfig) Columns(cols ...field.Expr) gen.Columns {
	return x.xRobotPushMsgConfigDo.Columns(cols...)
}

func (x *xRobotPushMsgConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xRobotPushMsgConfig) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 5)
	x.fieldMap["id"] = x.ID
	x.fieldMap["name"] = x.Name
	x.fieldMap["remark"] = x.Remark
	x.fieldMap["create_time"] = x.CreateTime
	x.fieldMap["update_time"] = x.UpdateTime
}

func (x xRobotPushMsgConfig) clone(db *gorm.DB) xRobotPushMsgConfig {
	x.xRobotPushMsgConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xRobotPushMsgConfig) replaceDB(db *gorm.DB) xRobotPushMsgConfig {
	x.xRobotPushMsgConfigDo.ReplaceDB(db)
	return x
}

type xRobotPushMsgConfigDo struct{ gen.DO }

func (x xRobotPushMsgConfigDo) Debug() *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Debug())
}

func (x xRobotPushMsgConfigDo) WithContext(ctx context.Context) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xRobotPushMsgConfigDo) ReadDB() *xRobotPushMsgConfigDo {
	return x.Clauses(dbresolver.Read)
}

func (x xRobotPushMsgConfigDo) WriteDB() *xRobotPushMsgConfigDo {
	return x.Clauses(dbresolver.Write)
}

func (x xRobotPushMsgConfigDo) Session(config *gorm.Session) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Session(config))
}

func (x xRobotPushMsgConfigDo) Clauses(conds ...clause.Expression) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xRobotPushMsgConfigDo) Returning(value interface{}, columns ...string) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xRobotPushMsgConfigDo) Not(conds ...gen.Condition) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xRobotPushMsgConfigDo) Or(conds ...gen.Condition) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xRobotPushMsgConfigDo) Select(conds ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xRobotPushMsgConfigDo) Where(conds ...gen.Condition) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xRobotPushMsgConfigDo) Order(conds ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xRobotPushMsgConfigDo) Distinct(cols ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xRobotPushMsgConfigDo) Omit(cols ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xRobotPushMsgConfigDo) Join(table schema.Tabler, on ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xRobotPushMsgConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xRobotPushMsgConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xRobotPushMsgConfigDo) Group(cols ...field.Expr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xRobotPushMsgConfigDo) Having(conds ...gen.Condition) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xRobotPushMsgConfigDo) Limit(limit int) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xRobotPushMsgConfigDo) Offset(offset int) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xRobotPushMsgConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xRobotPushMsgConfigDo) Unscoped() *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xRobotPushMsgConfigDo) Create(values ...*model.XRobotPushMsgConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xRobotPushMsgConfigDo) CreateInBatches(values []*model.XRobotPushMsgConfig, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xRobotPushMsgConfigDo) Save(values ...*model.XRobotPushMsgConfig) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xRobotPushMsgConfigDo) First() (*model.XRobotPushMsgConfig, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushMsgConfig), nil
	}
}

func (x xRobotPushMsgConfigDo) Take() (*model.XRobotPushMsgConfig, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushMsgConfig), nil
	}
}

func (x xRobotPushMsgConfigDo) Last() (*model.XRobotPushMsgConfig, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushMsgConfig), nil
	}
}

func (x xRobotPushMsgConfigDo) Find() ([]*model.XRobotPushMsgConfig, error) {
	result, err := x.DO.Find()
	return result.([]*model.XRobotPushMsgConfig), err
}

func (x xRobotPushMsgConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XRobotPushMsgConfig, err error) {
	buf := make([]*model.XRobotPushMsgConfig, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xRobotPushMsgConfigDo) FindInBatches(result *[]*model.XRobotPushMsgConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xRobotPushMsgConfigDo) Attrs(attrs ...field.AssignExpr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xRobotPushMsgConfigDo) Assign(attrs ...field.AssignExpr) *xRobotPushMsgConfigDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xRobotPushMsgConfigDo) Joins(fields ...field.RelationField) *xRobotPushMsgConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xRobotPushMsgConfigDo) Preload(fields ...field.RelationField) *xRobotPushMsgConfigDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xRobotPushMsgConfigDo) FirstOrInit() (*model.XRobotPushMsgConfig, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushMsgConfig), nil
	}
}

func (x xRobotPushMsgConfigDo) FirstOrCreate() (*model.XRobotPushMsgConfig, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XRobotPushMsgConfig), nil
	}
}

func (x xRobotPushMsgConfigDo) FindByPage(offset int, limit int) (result []*model.XRobotPushMsgConfig, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xRobotPushMsgConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xRobotPushMsgConfigDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xRobotPushMsgConfigDo) Delete(models ...*model.XRobotPushMsgConfig) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xRobotPushMsgConfigDo) withDO(do gen.Dao) *xRobotPushMsgConfigDo {
	x.DO = *do.(*gen.DO)
	return x
}
