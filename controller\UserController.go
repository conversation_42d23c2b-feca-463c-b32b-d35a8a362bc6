package controller

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	mrand "math/rand"
	"net"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/analysis/bigo"
	"xserver/analysis/facebook/convertApi"
	"xserver/analysis/kwai"
	"xserver/analysis/tiktok"
	"xserver/captcha"
	"xserver/controller/active"
	"xserver/controller/common"
	"xserver/controller/customer"
	"xserver/controller/datapush"
	"xserver/controller/msg"
	"xserver/controller/robot"
	"xserver/controller/userbrand"
	"xserver/gormgen/xHashGame/dao"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/tronscango"
	"xserver/utils/invaildAddress"

	"xserver/model"
	"xserver/server"
	"xserver/stat"

	"xserver/tronapi"
	"xserver/utils"

	"github.com/spf13/cast"
	"google.golang.org/api/idtoken"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"xserver/controller/sms"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/juju/ratelimit"
	"github.com/shopspring/decimal"
	"github.com/spf13/viper"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zhms/xgo/xgo"
)

var languageRegionMap = map[string]string{
	"ar-IL":   "阿拉伯语（以色列)",
	"ar-EG":   "阿拉伯语（埃及)",
	"zh-CN":   "中文简体",
	"zh-hans": "中文简体",
	"zh-TW":   "中文繁体",
	"zh-HK":   "中文（香港)",
	"nl-NL":   "荷兰语",
	"nl-BE":   "荷兰语（比利时)",
	"en-US":   "英语（美国)",
	"en-AU":   "英语（澳大利亚)",
	"en-CA":   "英语（加拿大)",
	"en-IN":   "英语（印度)",
	"en-IE":   "英语（爱尔兰)",
	"en-NZ":   "英语（新西兰)",
	"en-SG":   "英语（新加波)",
	"en-ZA":   "英语（南非)",
	"en-GB":   "英语（英国)",
	"fr-FR":   "法语",
	"fr-BE":   "法语（比利时)",
	"fr-CA":   "法语（加拿大)",
	"fr-CH":   "法（瑞士)",
	"de-DE":   "德语",
	"de-LI":   "德语（列支敦斯登)",
	"de-AT":   "德语（奥地利)",
	"de-CH":   "德语（瑞士)",
	"it-IT":   "意大利语",
	"it-CH":   "意大利语（瑞士)",
	"pt-BR":   "葡萄牙语（巴西)",
	"pt-PT":   "葡萄牙语",
	"es-ES":   "西班牙语",
	"es-US":   "西班牙语（美国)",
	"bn-BD":   "孟加拉语",
	"bn-IN":   "孟加拉语（印度)",
	"hr-HR":   "克罗地亚语",
	"cs-CZ":   "捷克语",
	"da-DK":   "丹麦语",
	"el-GR":   "希腊语",
	"he-IL":   "希伯来语（以色列)",
	"iw-IL":   "希伯来语（以色列)",
	"hi-IN":   "印度语",
	"hu-HU":   "匈牙利语",
	"in-ID":   "印度尼西亚语",
	"ja-JP":   "日语",
	"ko-KR":   "韩语（朝鲜语）",
	"ms-MY":   "马来语",
	"fa-IR":   "波斯语",
	"pl-PL":   "波兰语",
	"ro-RO":   "罗马尼亚语",
	"ru-RU":   "俄罗斯语",
	"sr-RS":   "塞尔维亚语",
	"sv-SE":   "瑞典语",
	"th-TH":   "泰语",
	"tr-TR":   "土耳其语",
	"ur-PK":   "乌尔都语",
	"vi-VN":   "越南语",
	"ca-ES":   "加泰隆语（西班牙)",
	"lv-LV":   "拉脱维亚语",
	"lt-LT":   "立陶宛语",
	"nb-NO":   "挪威语",
	"sk-SK":   "斯洛伐克语",
	"sl-SI":   "斯洛文尼亚语",
	"bg-BG":   "保加利亚语",
	"uk-UA":   "乌克兰语",
	"tl-PH":   "菲律宾语",
	"fi-FI":   "芬兰语",
	"af-ZA":   "南非语",
	"rm-CH":   "罗曼什语（瑞士)",
	"my-ZG":   "缅甸语（民间)",
	"my-MM":   "缅甸语（官方)",
	"km-KH":   "柬埔寨语",
	"am-ET":   "阿姆哈拉语（埃塞俄比亚)",
	"be-BY":   "白俄罗斯语",
	"et-EE":   "爱沙尼亚语",
	"sw-TZ":   "斯瓦希里语（坦桑尼亚)",
	"zu-ZA":   "祖鲁语（南非)",
	"az-AZ":   "阿塞拜疆语",
	"hy-AM":   "亚美尼亚语（亚美尼亚)",
	"ka-GE":   "格鲁吉亚语（格鲁吉亚)",
	"lo-LA":   "老挝语（老挝)",
	"mn-MN":   "蒙古语",
	"ne-NP":   "尼泊尔语",
	"kk-KZ":   "哈萨克语",
	"gl-rES":  "加利西亚语",
	"is-rIS":  "冰岛语",
	"kn-rIN":  "坎纳达语",
	"ky-rKG":  "吉尔吉斯语",
	"ml-rIN":  "马拉亚拉姆语",
	"mr-rIN":  "马拉提语/马拉地语",
	"ta-rIN":  "泰米尔语",
	"mk-rMK":  "马其顿语",
	"te-rIN":  "泰卢固语",
	"uz-rUZ":  "乌兹别克语",
	"eu-rES":  "巴斯克语",
	"si-LK":   "僧加罗语（斯里兰卡)",
	"ar":      "阿拉伯语",
	"zh":      "中文",
	"nl":      "荷兰语",
	"en":      "英语",
	"fr":      "法语",
	"de":      "德语",
	"it":      "意大利语",
	"pt":      "葡萄牙语",
	"es":      "西班牙语",
	"bn":      "孟加拉语",
	"hr":      "克罗地亚语",
	"cs":      "捷克语",
	"da":      "丹麦语",
	"el":      "希腊语",
	"he":      "希伯来语",
	"hi":      "印度语",
	"hu":      "匈牙利语",
	"in":      "印度尼西亚语",
	"ja":      "日语",
	"ko":      "韩语",
	"ms":      "马来语",
	"fa":      "波斯语",
	"pl":      "波兰语",
	"ro":      "罗马尼亚语",
	"ru":      "俄罗斯语",
	"sr":      "塞尔维亚语",
	"sv":      "瑞典语",
	"th":      "泰语",
	"tr":      "土耳其语",
	"ur":      "乌尔都语",
	"vi":      "越南语",
	"ca":      "加泰隆语",
	"lv":      "拉脱维亚语",
	"lt":      "立陶宛语",
	"nb":      "挪威语",
	"sk":      "斯洛伐克语",
	"sl":      "斯洛文尼亚语",
	"bg":      "保加利亚语",
	"uk":      "乌克兰语",
	"tl":      "菲律宾语",
	"fi":      "芬兰语",
	"af":      "南非语",
	"rm":      "罗曼什语",
	"my":      "缅甸语",
	"km":      "柬埔寨语",
	"am":      "阿姆哈拉语",
	"be":      "白俄罗斯语",
	"et":      "爱沙尼亚语",
	"sw":      "斯瓦希里语",
	"zu":      "祖鲁语",
	"az":      "阿塞拜疆语",
	"hy":      "亚美尼亚语",
	"ka":      "格鲁吉亚语",
	"lo":      "老挝语",
	"mn":      "蒙古语",
	"ne":      "尼泊尔语",
	"kk":      "哈萨克语",
	"gl":      "加利西亚语",
	"is":      "冰岛语",
	"kn":      "坎纳达语",
	"ky":      "吉尔吉斯语",
	"ml":      "马拉亚拉姆语",
	"mr":      "马拉提语",
	"ta":      "泰米尔语",
	"mk":      "马其顿语",
	"te":      "泰卢固语",
	"uz":      "乌兹别克语",
	"eu":      "巴斯克语",
	"si":      "僧加罗语",
}

func getLanguage(tag string) string {
	var str string
	str = languageRegionMap[tag]
	if str == "" {
		tag2, _, found := strings.Cut(tag, "-")
		if found {
			str = languageRegionMap[tag2]
		}
	}
	if str == "" {
		return tag
	}
	return str
}

var tgurl string
var tronscangoapi string

type UserController struct {
	*customer.BaseController
	Sms *sms.SmsController
}

var LoginRateLimiter = ratelimit.NewBucketWithRate(500, 500)

func (c *UserController) Init() {
	// 初始化 BaseController
	baseController := customer.NewBaseController()
	if baseController == nil {
		logs.Error("UserController 初始化失败: BaseController 创建失败")
	} else {
		logs.Info("UserController 成功初始化 BaseController")
		c.BaseController = baseController
	}

	c.Sms = new(sms.SmsController)
	tgurl = viper.GetString("lucky_admin")
	tronscangoapi = viper.GetString("tronscangoapi")
	server.Http().PostNoAuth("/api/user/third_register", c.third_register)
	server.Http().PostNoAuth("/api/user/third_account", c.third_account)
	server.Http().PostNoAuth("/api/user/register", c.register)
	server.Http().PostNoAuth("/api/user/login", c.login_verifycode)
	server.Http().PostNoAuth("/api/user/get_tg_bot_id", c.tgBotId)
	server.Http().PostNoAuth("/api/user/logout", c.logout)
	server.Http().PostNoAuth("/api/user/customservice", c.customservice)
	server.Http().PostNoAuth("/api/user/ChatRoom/GetChatRoomUserInfo", c.get_amount)
	server.Http().PostNoAuth("/api/user/checkUserInResourceDb", c.checkUserInResourceDb)
	server.Http().PostNoAuth("/api/user/updateTiyanjinStatusInResourceDb", c.updateTiyanjinStatusInResourceDb)
	server.Http().PostNoAuth("/api/user/checkAddress", c.checkAddress)
	server.Http().PostNoAuth("/api/user/totalLiushui", c.totalLiushui)

	server.Http().Post("/api/user/info", c.info)

	server.Http().Post("/api/user/get_recharge_address", c.get_recharge_address)
	server.Http().Post("/api/user/get_recharge_address_v2", c.get_recharge_address_v2)
	server.Http().Post("/api/user/wallet", c.get_user_wallet)

	server.Http().Post("/api/user/bonus_infos", c.bonus_infos)

	server.Http().Post("/api/user/address_verify", c.address_verify)
	server.Http().Post("/api/user/withdraw", c.withdraw)
	server.Http().Post("/api/user/withdraw_v2", c.withdraw_v2)
	server.Http().Post("/api/user/rechargelist", c.rechargelist)
	server.Http().Post("/api/user/withdrawlist", c.withdrawlist)
	server.Http().Post("/api/user/bind_address", c.bind_address)
	server.Http().Post("/api/user/recharge_address", c.recharge_address)
	server.Http().Post("/api/user/game_peilv", c.game_peilv)
	server.Http().Post("/api/user/get_withdraw_address", c.get_withdraw_address)
	server.Http().Post("/api/user/add_withdraw_address", c.add_withdraw_address)
	server.Http().Post("/api/user/get_withdraw_config", c.get_withdraw_config)
	server.Http().Post("/api/user/get_withdraw_limit_info", c.get_withdraw_limit_info)
	server.Http().Post("/api/user/get_wallet_address", c.get_wallet_address)
	server.Http().Post("/api/user/add_wallet_address", c.add_wallet_address)
	server.Http().Post("/api/user/delete_wallet_address", c.delete_wallet_address)
	server.Http().Post("/api/user/get_user_all_address", c.get_user_all_address)

	server.Http().Post("/api/user/set_wallet_password", c.set_wallet_password)
	server.Http().Post("/api/user/set_wallet_passwordV2", c.set_wallet_password2)
	server.Http().Post("/api/user/set_login_password", c.set_login_password)
	server.Http().PostNoAuth("/api/user/set_login_password_v2", c.set_login_password_v2)

	server.Http().PostNoAuth("/api/user/password_reset_money", c.password_reset_money)
	server.Http().PostNoAuth("/api/user/password_reset_token", c.password_reset_token)
	server.Http().PostNoAuth("/api/user/password_reset", c.password_reset)

	server.Http().Post("/api/user/set_info", c.set_info)
	server.Http().Post("/api/user/get_bank", c.get_bank)
	server.Http().Post("/api/user/add_bank", c.add_bank)
	server.Http().Post("/api/user/del_bank", c.del_bank)

	server.Http().Post("/api/user/bind_phone", c.bind_phone)
	server.Http().Post("/api/user/bind_phoneV2", c.bind_phone2)
	server.Http().Post("/api/user/bind_email", c.bind_email)
	server.Http().Post("/api/user/bind_emailV2", c.bind_email2)
	server.Http().Post("/api/user/get_balance", c.get_balance)
	server.Http().Post("/api/user/tiyanjing", c.tiyanjing)
	server.Http().Post("/api/user/recharge_maidian", c.recharge_maidian)
	server.Http().Post("/api/user/add_cart", c.add_cart)
	server.Http().Post("/api/user/update_game_chip", c.update_game_chip)
	server.Http().Post("/api/user/tg_sendBind", c.tg_sendBind)
	server.Http().PostByNoAuth("/api/user/check_user_email", c.checkUserEmail)
	server.Http().PostByNoAuth("/api/user/verify_user_email", c.verifyUserEmail)
	server.Http().PostByNoAuth("/api/user/verify_user_phone", c.verifyUserPhone)
	server.Http().PostByNoAuth("/api/user/retrieve_password", c.retrievePassword)

	server.Http().Post("/api/user/get_recharge_address_injection", c.get_recharge_address_injection)
	server.Http().Post("/api/user/get_shangzhuang_balance", c.get_shangzhuang_balance)
	server.Http().Post("/api/user/concat_cs", c.concat_cs)
	server.Http().Post("/api/user/first_charge_click", c.first_charge_click)

	server.Http().Post("/api/user/attr_reporting", c.attrReporting)    // 归因上报
	server.Http().Post("/api/user/reward_trial_fee", c.rewardTrialFee) // 普通用户领取体验经
	server.Http().PostNoAuth("/api/user/403/customservice", c.customservice403)
	server.Http().PostNoAuth("/api/get_idToken", c.getIdToken)
	server.Http().Post("/api/user/robot_mission_reward", c.robot_mession_reward) // 机器任务领取奖金

	server.Http().Post("/api/user/game-save", c.gameSave)      // 保存玩家游戏
	server.Http().Post("/api/user/recent-games", c.recentGame) // 玩家游戏列表
}

// 集成telegram登录时获取 bot id
func (c *UserController) tgBotId(ctx *abugo.AbuHttpContent) {
	errorCode := 0
	requestData := struct {
		Host string
	}{}

	err := ctx.RequestData(&requestData)
	if ctx.RespErr(err, &errorCode) {
		return
	}

	host := ctx.Host()
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if requestData.Host != "" {
		host = requestData.Host
	}
	//logs.Info(host)
	//ChannelId, SellerId := server.GetChannel(ctx, host)
	//tgRobotGuideTb := server.DaoxHashGame().XTgRobotGuide
	//tgRobotGuideDb := server.DaoxHashGame().XTgRobotGuide.WithContext(context.Background())
	//tgRobotGuide, err := tgRobotGuideDb.Where(tgRobotGuideTb.SellerID.Eq(int32(SellerId))).Where(tgRobotGuideTb.ChannelID.Eq(int32(ChannelId))).Where(tgRobotGuideTb.TgRobotType.Eq(10000)).First()
	// 暂不区分机器人类型，直接登录
	//tgRobotGuide, err := tgRobotGuideDb.Where(tgRobotGuideTb.SellerID.Eq(int32(SellerId))).Where(tgRobotGuideTb.ChannelID.Eq(int32(ChannelId))).First()

	xChannelHost := server.DaoxHashGame().XChannelHost
	channelHost, err := xChannelHost.WithContext(context.TODO()).Where(xChannelHost.Host.Eq(host)).First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errorCode, "未找到机器人")
		} else {
			logs.Error("UserController tgBotId err", err)
			ctx.RespErrString(true, &errorCode, "系统错误,请稍后再试")
		}
		return
	}

	type TgAuthRobot struct {
		AuthRobotName     string
		AuthRobotToken    string
		TgRobotGuideToken string
	}

	var tgAuthRobot TgAuthRobot
	robotId := ""
	if channelHost.TgAuthRobot != "" {
		json.Unmarshal([]byte(channelHost.TgAuthRobot), &tgAuthRobot)
		token := strings.Split(tgAuthRobot.AuthRobotToken, ":")

		if len(token) > 0 {
			robotId = token[0]
		}
	}

	ctx.Put("TgRobotId", robotId)
	ctx.RespOK()
}

func (c *UserController) third_account(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		ThirdId      string `validate:"required"`
		ThirdAccount string `validate:"required"`
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ThirdAccount := fmt.Sprintf("%s_%s", reqdata.ThirdId, reqdata.ThirdAccount)
	data, err := server.XDb().Table("x_user").Where("ThirdId = ?", ThirdAccount).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if data == nil {
		ctx.RespErrString(true, &errcode, "账号不存在")
		return
	}
	ctx.RespOK(data.Map())
}

func (c *UserController) third_register(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		ThirdId      string `validate:"required"`
		Account      string `validate:"required"` //账号
		Password     string `validate:"required"` //密码
		ThirdAccount string `validate:"required"`
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ThirdAccount := fmt.Sprintf("%s_%s", reqdata.ThirdId, reqdata.ThirdAccount)
	account := fmt.Sprintf("%s_%s", reqdata.ThirdId, reqdata.Account)
	data, err := server.XDb().Table("x_user").Where("ThirdId = ?", ThirdAccount).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if data != nil {
		ctx.RespErrString(true, &errcode, "三方账号已注册")
		return
	}
	reqdata.Password = utils.Md5V(reqdata.Password)
	extra := map[string]interface{}{
		"Ip":           ctx.GetIp(),
		"AgentCode":    reqdata.ThirdId,
		"Address":      "",
		"ChannelId":    2,
		"PhoneNum":     "",
		"VerifyCode":   "",
		"TgName":       "",
		"RegGift":      1,
		"CSGroup":      "",
		"Host":         "",
		"ThirdAccount": ThirdAccount,
	}
	strextra, _ := json.Marshal(&extra)
	result, err := server.XDb().CallProcedure("x_api_user_register", account, 1, reqdata.Password, "", string(strextra))
	if ctx.RespErr(err, &errcode) {
		return
	}
	dberrcode := result.Int("errcode")
	if dberrcode == 1062 {
		ctx.RespErrString(true, &errcode, "账号已存在")
		return
	}
	if dberrcode != 0 {
		if ctx.RespErrString(true, &errcode, result.String("errmsg")) {
			return
		}
	}

	c.PushCustomEvent(fmt.Sprint(reqdata.ThirdId), "third_register", map[string]interface{}{
		"account":      reqdata.Account,
		"ThirdAccount": reqdata.ThirdAccount,
		"status":       "successful",
		"remark":       "third_register",
		"action":       "third_register",
	})
	ctx.RespOK(result.Map())
}

type RegisterReq struct {
	SellerId     int    `validate:"required"` //运营商
	Account      string `validate:"required"` //账号
	Password     string //密码(md5)
	PasswordV2   string //密码(RSA)
	AccountType  int    // 1:账号, 2:手机号, 3:Email, 4:Google, 5:TG机器人自动注册, 8:WhatsApp, 9:Line, 10:Instagram, 11:X(Twitter), 12:Facebook
	Address      string
	AgentCode    string
	PhoneNum     string //`validate:"required"`
	Email        string //`validate:"required"`
	VerifyCode   string //`validate:"required"` //验证码
	Validate     string `validate:"required"`
	TgName       string
	DeviceId     string
	DeviceType   string
	Host         string
	Lang         string
	Wallet       string // 钱包地址
	TgRobotToken string
	TgChatId     int64
	Cxd          string
	ThirdIp      string
	Kwai         string
	Bigo         string
	Fbc          string
	Fbp          string
	Source       string // 注册来源
	Brand        int    // 注册来自哪里  2 ut
	NickName     string // 昵称
}
type RegisterRes map[string]any

func register(ctx *abugo.AbuHttpContent, reqdata RegisterReq) (res RegisterRes, err error) {
	if len(reqdata.TgName) >= 32 {
		err = errors.New("Telegram超过长度限制")
		return nil, err
	}

	if reqdata.AccountType == 1 {
		if !utils.VerifyUSERNAME(reqdata.Account) {
			err = errors.New("账号错误")
			return nil, err
		}
		// 注册添加限制111
		username := strings.ToLower(reqdata.Account)
		if strings.HasPrefix(username, "tmpautotg-") || strings.HasPrefix(username, "tg") || strings.HasPrefix(username, "magic") {
			err = errors.New("账号已经存在")
			return nil, err
		}
	} else if reqdata.AccountType == 3 { //邮箱注册
		// 创建验证器并自定义配置
		verifier := utils.NewVerimailVerifier()

		isValid, err := verifier.Verify(reqdata.Email)
		if err != nil {
			logs.Error("邮箱验证失败:", err)
			err = errors.New("请输入有效的邮箱地址")
			return nil, err
		}
		if !isValid {
			err = errors.New("请输入有效的邮箱地址")
			return nil, err
		}

	} else if reqdata.AccountType == 5 && reqdata.TgRobotToken == "" {
		err = errors.New("TgRobotToken不能为空")
		return nil, err
	}

	reqdata.Password = utils.Md5V(reqdata.Password)
	if reqdata.PasswordV2 != "" {
		reqdata.PasswordV2, err = utils.ConvertUserPassword(reqdata.PasswordV2)
		if err != nil {
			logs.Error("register ConvertUserPassword err:", err)
		}
	}

	reqdata.Address = ""
	host := ctx.Host()
	if len(reqdata.Host) > 3 {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	ChannelId, SellerId := server.GetChannel(ctx, host)

	verifycfg := server.GetConfigString(SellerId, 0, "YiDunVerify")
	if verifycfg == "1" {
		verifier, _ := captcha.New("0fedaaa12f614c70bd2811b367f210e7", "f3f3749c1394c14d6ecd8e85c04bddbd", "33a0f2e37841e610831d879137242f01")
		user := reqdata.Account
		verifyResult, err := verifier.Verify(reqdata.Validate, user)
		if err != nil {
			err = errors.New("系统错误,请稍后再试")
			return nil, err
		}
		if !verifyResult.Result {
			err = errors.New("验证码不正确")
			return nil, err
		}
	}
	isHost := false
	channeldata, err := server.XDb().Table("x_channel_host").Where("Host = ?", host).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logs.Info("UserController register: Host not found", host)
			return nil, errors.New("无效的主机") // "Недопустимый хост"
		}
		logs.Error("UserController register database error:", err)
		return nil, errors.New("系统错误,请稍后再试") // "Системная ошибка, пожалуйста, попробуйте позже"
	}

	if channeldata != nil {
		isHost = true
		AgentCode := channeldata.String("AgentCode")
		if reqdata.AgentCode == "" && AgentCode != "" {
			reqdata.AgentCode = AgentCode
		}
	}

	iagent, err := server.XDb().Table("x_agent_independence").Select("AgentCode,ChannelId").Where("Host = ? and State = 1", host).First()
	if err != nil {
		logs.Error("UserController register err2:", err)
		err = errors.New("系统错误,请稍后再试")
		return nil, err
	}
	// 推广链接
	if len(reqdata.AgentCode) > 0 {
		promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
		indepenAgent, err := server.XDb().Table("x_agent_independence").Select("AgentCode,ChannelId").Where("PromotionHost = ? and State = 1", promotionHost).First()
		if err == nil && indepenAgent != nil {
			iagent = indepenAgent
		}
	}
	SpecialAgent := 2
	if iagent != nil {
		isHost = true
		if reqdata.AgentCode == "" {
			reqdata.AgentCode = iagent.String("AgentCode")
		}
		ChannelId = iagent.Int("ChannelId")
		SpecialAgent = 1
	} else {
		if reqdata.AgentCode != "" {
			agentcode, _ := server.XDb().Table("x_agent_code").Where("AgentCode = ?", reqdata.AgentCode).First()
			if agentcode != nil {
				uagent, _ := server.XDb().Table("x_agent_independence").Where("UserId = ?", agentcode.Int("UserId")).First()
				if uagent != nil {
					err = errors.New("邀请码不存在")
					return nil, err
				}
			}
		}
	}
	if !isHost {
		err = errors.New("域名未配置")
		return nil, err
	}

	reqdata.TgName = strings.Replace(reqdata.TgName, "@", "", -1)
	UsdtGiftStatus := 0
	TrxGiftStatus := 0
	isTiyanjinEnable := false
	var tgRobotType int32
	var tgBotId int64
	daoBot := server.DaoxHashGame().XRobotConfig
	xTgbot, _ := daoBot.WithContext(ctx.Gin()).Where(daoBot.Token.Eq(reqdata.TgRobotToken)).First()

	oldDaoBot := server.DaoxHashGame().XTgRobotGuide
	xOldTgbot, _ := oldDaoBot.WithContext(ctx.Gin()).Where(oldDaoBot.TgRobotToken.Eq(reqdata.TgRobotToken)).First()

	if xTgbot != nil {
		isTiyanjinEnable = xTgbot.IsOpenGift == 1
		tgBotId = xTgbot.ID
		tgRobotType = xTgbot.RobotType
	}

	if xOldTgbot != nil {
		isTiyanjinEnable = xOldTgbot.IsTiyanjin == 1
		tgBotId = xOldTgbot.ID
		tgRobotType = xOldTgbot.TgRobotType
	}

	var exist, isEffective bool
	var IsInResourceDb int
	if reqdata.TgName != "" && reqdata.TgChatId != 0 {
		if reqdata.AccountType == 5 && isTiyanjinEnable && tgRobotType == 1 {
			result1, err := checkUserInResourceDb(reqdata.TgName, reqdata.TgChatId)
			if err == nil {
				exist = result1.Id > 0
				if exist {
					IsInResourceDb = 1
					isEffective = true
				}
			}
		}
		// 处理英文机器人
		if reqdata.AccountType == 5 && isTiyanjinEnable && tgRobotType == 2 {
			IsInResourceDb = 1
			UsdtGiftStatus = 1
			isEffective = true
		}

	}

	// 判断域名是否开启了体验金领取
	isTyjEnable := 2
	if channeldata != nil {
		isTyjEnable = channeldata.Int("IsTyjEnable")
	}

	if isTyjEnable == 1 {
		UsdtGiftStatus = 1
		// 判断ip和设备
	}

	if reqdata.AccountType == 5 {
		// 判断tgChatId和sellerId库里是否存在
		userDao := server.DaoxHashGame().XUser
		userDb := userDao.WithContext(ctx.Gin())
		user, _ := userDb.Where(userDao.TgChatID.Eq(reqdata.TgChatId)).Where(userDao.SellerID.Eq(int32(SellerId))).Where(userDao.State.Eq(1)).First()
		if user != nil {
			return nil, errors.New("该tg账号已存在")
		}

		threading.GoSafe(func() {
			stat.TgRobotStat.AddLogAction(context.Background(), int32(SellerId), int32(ChannelId), tgBotId, reqdata.TgChatId, stat.ActionType_Start, "", exist, time.Time{})
		})
	}

	// 根据域名查询客服组
	csgroup := ""
	where := abugo.AbuDbWhere{}
	where.Add("and", "Host", "=", host, nil)
	csgroupdata, err := server.XDb().Table("x_kefu_host").Where("Host = ?", host).First()
	if err != nil {
		logs.Error("UserController register err3:", err)
		err = errors.New("系统错误,请稍后再试")
		return nil, err
	}
	if csgroupdata != nil {
		csgroup = csgroupdata.String("GroupName")
	}

	// 获取IsoCountray
	IsoCountry := ""
	if reqdata.Cxd != "" {
		IsoCountry = utils.GetIsoCountryByIp(reqdata.ThirdIp)
	}

	extra := map[string]interface{}{
		"Ip":             reqdata.ThirdIp,
		"AgentCode":      reqdata.AgentCode,
		"Address":        reqdata.Address,
		"ChannelId":      ChannelId,
		"PhoneNum":       reqdata.PhoneNum,
		"Email":          reqdata.Email,
		"VerifyCode":     reqdata.VerifyCode,
		"TgName":         reqdata.TgName,
		"CSGroup":        csgroup,
		"Host":           host,
		"SpecialAgent":   SpecialAgent,
		"DeviceId":       reqdata.DeviceId,
		"DeviceType":     reqdata.DeviceType,
		"Lang":           getLanguage(reqdata.Lang),
		"Wallet":         reqdata.Wallet,
		"AccountType":    reqdata.AccountType,
		"TgRobotToken":   reqdata.TgRobotToken,
		"TgChatId":       reqdata.TgChatId,
		"UsdtGiftStatus": UsdtGiftStatus,
		"TrxGiftStatus":  TrxGiftStatus,
		"IsInResourceDb": IsInResourceDb,
		"CxdId":          reqdata.Cxd,
		"Kwai":           reqdata.Kwai,
		"Bigo":           reqdata.Bigo,
		"IsoCountry":     IsoCountry,
		"PasswordV2":     reqdata.PasswordV2,
		"Brand":          reqdata.Brand,
		"NickName":       reqdata.NickName,
	}
	strextra, _ := json.Marshal(&extra)
	logs.Debug("register host:", reqdata.Lang, host, reqdata.Account, string(strextra))
	pdata := []interface{}{reqdata.Account, SellerId, reqdata.Password, reqdata.VerifyCode, string(strextra)}
	result, err := server.XDb().CallProcedure("x_api_user_register", pdata...)
	if err != nil {
		logs.Error("UserController register err4:", err)
		err = errors.New("系统错误,请稍后再试")
		return nil, err
	}

	dberrcode := result.Int("errcode")
	if dberrcode == 1062 {
		err = errors.New("账号已经存在")
		return nil, err
	}
	if dberrcode != 0 {
		logs.Error("UserController register err5:", result.String("errmsg"))
		err = errors.New(result.String("errmsg"))
		return nil, err
	}
	if reqdata.Validate != "" {
		server.XRedis().Set(fmt.Sprintf("captcha:%v", reqdata.Validate), 60, 1)
	}
	res = *result.Map()
	res["exist"] = exist
	res["isEffective"] = isEffective

	// FB注册事件上报
	userId := result.Int32("UserId")
	if userId != 0 {
		xUser := server.DaoxHashGame().XUser
		ua := ctx.Gin().GetHeader("User-Agent")
		if cli := convertApi.GetClient(host); cli != nil {
			threading.GoSafe(func() {
				logs.Error("xxxxxxxxxx", xUser.Fbc)
				xUser.WithContext(context.TODO()).Where(xUser.UserID.Eq(userId)).Updates(model2.XUser{
					Fbc: reqdata.Fbc,
				})
				xUserMore := server.DaoxHashGame().XUserMore
				userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(userId)).First()
				if userMoreInfo != nil {
					_, err = xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(userId)).Update(xUserMore.Fbp, reqdata.Fbp)
				} else {
					xUserMore.WithContext(ctx.Gin()).Create(&model2.XUserMore{
						UserID: userId,
						Fbp:    reqdata.Fbp,
					})

				}
				//必须 有公告接入 发起上报
				if reqdata.Fbc != "" {
					//	logs.Error("xxxxxxxxxx", reqdata.Fbp)
					cli.CompleteRegistration(userId, reqdata.PhoneNum, reqdata.Email, reqdata.ThirdIp, ua, reqdata.Fbc, reqdata.Fbp)
				}
			})
		}
		if cli := kwai.GetClient(host); cli != nil {
			threading.GoSafe(func() {
				cli.CompleteRegistration(reqdata.Kwai, int(userId))
			})
		}
		if cli := tiktok.GetClient(host); cli != nil {
			threading.GoSafe(func() {
				cli.CompleteRegistration(strconv.Itoa(int(userId)),
					tiktok.User{
						Phone: reqdata.PhoneNum,
						Email: reqdata.Email,
						Ip:    reqdata.ThirdIp,
					})
			})
		}
		if cli := bigo.GetClient(host); cli != nil {
			threading.GoSafe(func() {
				xUser.WithContext(context.TODO()).Where(xUser.UserID.Eq(userId)).Update(xUser.Bigo, reqdata.Bigo)
				cli.CompleteRegistration(reqdata.Bigo)
			})
		}
	}
	if reqdata.AccountType == 5 {
		threading.GoSafe(func() {
			var tgBotId int64 = 0
			daoBot := server.DaoxHashGame().XRobotConfig
			xTgbot, _ := daoBot.WithContext(context.Background()).Where(daoBot.Token.Eq(reqdata.TgRobotToken)).First()

			oldDaoBot := server.DaoxHashGame().XTgRobotGuide
			xOldTgbot, _ := oldDaoBot.WithContext(context.Background()).Where(oldDaoBot.TgRobotToken.Eq(reqdata.TgRobotToken)).First()

			if xTgbot != nil {
				tgBotId = xTgbot.ID
			}
			if xOldTgbot != nil {
				tgBotId = xOldTgbot.ID
			}
			memo := fmt.Sprintf("userId:%d", res["UserId"])
			stat.TgRobotStat.AddLogAction(context.Background(), int32(SellerId), int32(ChannelId), tgBotId, reqdata.TgChatId, stat.ActionType_Register, memo, exist, time.Time{})
		})
	}

	// 注册成功后处理注册赠送活动
	go func() {
		if err := active.ProcessRegisterGift(int32(userId), reqdata.ThirdIp); err != nil {
			logs.Error("ProcessRegisterGift error: ", err)
		}
	}()

	return res, nil
}

type GetAccountResult struct {
	Id                        int    `json:"fId"`                        // 账号id >0在库
	Account                   string `json:"fAccount"`                   // 账号
	RemarkStatus              int    `json:"fRemarkStatus"`              // 备注状态（0-无；1-已领取；2-黑名单；3-刷子；4-进粉；5-新增；6-充值投注；7-有效充值；8-市场部测试）
	UserId                    string `json:"fUserId"`                    // 账号飞机UID
	IsAvailableReceive        bool   `json:"fIsAvailableReceive"`        // 是否可以领取
	IsMarketingDepartmentTest bool   `json:"fIsMarketingDepartmentTest"` // 是否是市场部测试
	SourceType                int    `json:"fSourceType"`                // 来源（0-人工上传，1-拉手推送，2-裂变机器人）
}

// 检查用户是否存在资源部数据库且有资格领取体验金
func checkUserInResourceDb(TgName string, TgUserId int64) (result *GetAccountResult, err error) {
	header := req.Header{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	resp, err := req.Post(tgurl+"/api/Hash/GetAccount", header, strings.NewReader(fmt.Sprintf("account=%s&uid=%d", TgName, TgUserId)))
	logs.Debug(tgurl+"/api/Hash/GetAccount:", TgName)
	if err != nil {
		logs.Debug("/api/Hash/GetAccount error:", err.Error())
		return nil, err
	}
	body, err := io.ReadAll(resp.Response().Body)
	defer resp.Response().Body.Close()
	if err != nil {
		return nil, err
	}
	logs.Debug("/api/Hash/GetAccount:", string(body))
	resData := struct {
		Code    int               `json:"code"` // 响应码（0-为成功，其它为失败）
		Message string            `json:"message"`
		Result  *GetAccountResult `json:"result,omitempty"`
	}{}
	err = json.Unmarshal(body, &resData)
	if err != nil {
		return nil, err
	}
	if resData.Code != 0 {
		return nil, errors.New(resData.Message)
	}
	if resData.Result == nil {
		return new(GetAccountResult), nil
	}
	return resData.Result, nil
}

// 更新体验金领取状态
func updateTiyanjinStatusInResourceDb(TgName string, TgUserId int64, isDebug bool) (err error) {
	//if isDebug {
	//	return nil
	//}
	if TgName == "" && TgUserId == 0 {
		return errors.New("参数错误")
	}
	header := req.Header{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	resp, err := req.Post(tgurl+"/api/Hash/UpdateRemark", header, strings.NewReader(fmt.Sprintf("account=%s&uid=%d", TgName, TgUserId)))
	logs.Debug("/api/Hash/UpdateRemark:", TgName)
	if err != nil {
		logs.Debug("/api/Hash/UpdateRemark error:", err.Error())
		return err
	}
	body, err := io.ReadAll(resp.Response().Body)
	defer resp.Response().Body.Close()
	if err != nil {
		return err
	}
	logs.Debug("/api/Hash/UpdateRemark:", string(body))
	jdata := struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Result  interface{} `json:"result,omitempty"`
	}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		return err
	}
	if jdata.Code != 0 && jdata.Code != 4 {
		return errors.New(jdata.Message)
	}
	return nil
}

func (c *UserController) checkUserInResourceDb(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Account  string
		TgName   string
		TgUserId int64
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Account != "" {
		userDao := server.DaoxHashGame().XUser
		db := userDao.WithContext(ctx.Gin())
		user, err := db.Where(userDao.Account.Eq(reqdata.Account)).First()
		if ctx.RespErr(err, &errcode) {
			return
		}
		reqdata.TgName = user.TgName
		reqdata.TgUserId = user.TgChatID
	}

	result, err := checkUserInResourceDb(reqdata.TgName, reqdata.TgUserId)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK(result)
}

func (c *UserController) updateTiyanjinStatusInResourceDb(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		TgName   string
		TgUserId int64
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if reqdata.TgName == "" && reqdata.TgUserId == 0 {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}
	err = updateTiyanjinStatusInResourceDb(reqdata.TgName, reqdata.TgUserId, false)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

func (c *UserController) register(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RegisterReq{Brand: 1}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if len(reqdata.ThirdIp) < 7 {
		reqdata.ThirdIp = ctx.GetIp()
	}

	lockkey := fmt.Sprintf("registerlock:%s", reqdata.Account)
	if !server.XRedis().GetLock(lockkey, 5) {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请求稍后再试")
		return
	}
	lockkey2 := fmt.Sprintf("registerlock:%s", reqdata.ThirdIp)
	if !server.XRedis().GetLock(lockkey2, 5) {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请求稍后再试")
		return
	}

	defer func() {
		server.XRedis().ReleaseLock(lockkey)
		server.XRedis().ReleaseLock(lockkey2)
	}()

	res, err := register(ctx, reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 添加邮箱验证状态信息
	if reqdata.Email != "" || reqdata.AccountType == 3 {
		// 将邮箱验证状态添加到响应中
		res["email_verified"] = true
		res["email"] = reqdata.Email
	}

	host := ctx.Host()
	if len(reqdata.Host) > 3 {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]
	if robot.IfOpenDomainAutoRegister(ctx.Gin(), host, robot.DomainName) {
		userID := cast.ToInt64(res["UserId"])
		robot.UpdateUserPasswordFirstTime(ctx.Gin(), userID)
	}
	// 向ThinkingData推送注册成功事件
	threading.GoSafe(func() {
		userId := res["UserId"]
		// 获取用户信息用于事件上报
		userDao := server.DaoxHashGame().XUser
		userDb := userDao.WithContext(ctx.Gin())
		user, err := userDb.Where(userDao.UserID.Eq(int32(abugo.GetInt64FromInterface(userId)))).First()
		if err != nil {
			logs.Error("获取用户信息失败，无法发送注册事件到ThinkingData:", err, "用户ID:", userId)
			return
		}

		// 确定注册类型
		registerType := c.getLoginTypeString(reqdata.AccountType)

		// 获取客户端IP
		clientIP := reqdata.ThirdIp
		if clientIP == "" {
			clientIP = "unknown"
		}

		// 发送注册事件到ThinkingData
		err = datapush.SendRegisterEvent(user, registerType, clientIP, reqdata.Email, reqdata.PhoneNum)
		if err != nil {
			logs.Error("发送注册事件到ThinkingData失败:", err, "用户ID:", userId)
		}
	})
	// channelId, _ := server.GetChannel(ctx, reqdata.Host)

	//直播注册新增20倍流水体验金
	// if reqdata.Source == "live" && channelId == 52 {
	// 	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
	// 		activeAddUseBalancerInfo := struct {
	// 			UserId            int32
	// 			ActiveName        string
	// 			RealAmount        float64
	// 			WithdrawLiuSuiAdd float64
	// 			BalanceCReason    int
	// 		}{
	// 			UserId:            int32(abugo.GetInt64FromInterface(res["UserId"])),
	// 			ActiveName:        "直播体验金",
	// 			RealAmount:        10,
	// 			WithdrawLiuSuiAdd: 200,
	// 			BalanceCReason:    201,
	// 		}
	// 		// 更新用户流水和账变记录
	// 		err := ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)

	// 		if err != nil {
	// 			return err
	// 		}

	// 		return nil
	// 	})

	// 	if err != nil {
	// 		ctx.RespErrString(true, &errcode, err.Error())
	// 		return
	// 	}

	// }

	ctx.RespOK(res)
}

func (c *UserController) login_verifycode(ctx *abugo.AbuHttpContent) {
	defer recover()

	type RequestData struct {
		SellerId    int    //运营商
		Account     string //账号
		Password    string //密码
		VerifyCode  string //验证码
		AccountType int    // 1:账号, 2:手机号, 3:Email, 4:Google, 5:TG机器人自动注册, 8:WhatsApp, 9:Line, 10:Instagram, 11:X(Twitter), 12:Facebook
		Validate    string
		DeviceId    string
		DeviceType  string
		Host        string
		IdToken     string
		Cxd         string
		ThirdIp     string // 登录IP
		Lang        string
		AgentCode   string
		Brand       int    // 2 ut
		IsUt        int    // 1 ut 0 非ut
		Email       string // google登录的时候有
		NickName    string // google登录的时候有
		Fbc         string
		Fbp         string
		Kwai        string
		Bigo        string
	}
	errcode := 0
	reqdata := RequestData{Brand: 1}
	if LoginRateLimiter.TakeAvailable(1) == 0 {
		ctx.RespErrString(true, &errcode, "请求过于频繁，请稍后再试")
		return
	}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if (reqdata.AccountType == 1 || reqdata.AccountType == 2 || reqdata.AccountType == 3) && (reqdata.Account == "" || reqdata.Password == "") {
		ctx.RespErrString(true, &errcode, "账号密码不能为空！")
		return
	}
	if reqdata.AccountType == 4 && reqdata.IdToken == "" {
		ctx.RespErrString(true, &errcode, "登录失败")

		return
	}
	if reqdata.AccountType == 5 && (reqdata.Account == "" || reqdata.Password == "") && reqdata.IdToken == "" {
		ctx.RespErrString(true, &errcode, "登录失败")
		return
	}

	if len(reqdata.ThirdIp) < 7 {
		reqdata.ThirdIp = ctx.GetIp()
	}

	host := ctx.Host()
	if reqdata.Host != "" {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	ChannelId, SellerId := server.GetChannel(ctx, host)

	seller, _ := server.DaoxHashGame().XSeller.WithContext(nil).Where(server.DaoxHashGame().XSeller.SellerID.Eq(int32(SellerId))).First()
	if seller == nil {
		ctx.RespErrString(true, &errcode, "运营商不存在")
		return
	}

	type AuthThird struct {
		GoogleAppid    string `json:"GoogleAppid"`
		WhatsAppAppId  string `json:"WhatsAppAppId"`
		LineAppId      string `json:"LineAppId"`
		InstagramAppId string `json:"InstagramAppId"`
		XAppId         string `json:"XAppId"`
		FacebookAppId  string `json:"FacebookAppId"`
	}

	var authThird AuthThird
	json.Unmarshal([]byte(seller.ThirdAuth), &authThird)

	key := utils.Md5V(fmt.Sprintf("%d%s%s%s", SellerId, reqdata.Account, reqdata.IdToken, host))
	rkey := fmt.Sprintf("%v:%v:user_login:%v", server.Project(), server.Module(), key)
	loc := server.Redis().SetNxString(rkey, "1", 5)
	if loc != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rkey)
	//不存在的域名不允许登录
	if ChannelId == 1 && SellerId == 1 {
		isHost := false
		channeldata, err := server.XDb().Table("x_channel_host").Where("Host = ?", host).First()
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				logs.Info("UserController login_verifycode: Host not found", host)
				ctx.RespErrString(true, &errcode, "无效的主机") // "Недопустимый хост"
				return
			}
			logs.Error("UserController login_verifycode database error:", err)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试") // "Системная ошибка, пожалуйста, попробуйте позже"
			return
		}
		if channeldata != nil {
			isHost = true
		}
		iagent, err := server.XDb().Table("x_agent_independence").Select("AgentCode,ChannelId").Where("Host = ? and State = 1", host).First()
		if err != nil {
			logs.Error("UserController login_verifycode", err)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		}
		// 推广链接
		if len(reqdata.AgentCode) > 0 {
			promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
			indepenAgent, err := server.XDb().Table("x_agent_independence").Select("AgentCode,ChannelId").Where("PromotionHost = ? and State = 1", promotionHost).First()
			if err == nil && indepenAgent != nil {
				iagent = indepenAgent
			}
		}
		if iagent != nil {
			isHost = true
		}
		if !isHost {
			ctx.RespErrString(true, &errcode, "域名未配置")
			return
		}
	}

	// {
	// 	rkey := fmt.Sprintf("%v:%v:user_login:%v", server.Project(), server.Module(), reqdata.Password)
	// 	loc := server.Redis().SetNxString(rkey, "1", 5)
	// 	if ctx.RespErrString(loc != nil, &errcode, "操作频繁,请稍后再试") {
	// 		return
	// 	}
	// }

	// {
	// 	rkey := fmt.Sprintf("%v:%v:user_login:%v", server.Project(), server.Module(), ctx.GetIp())
	// 	loc := server.Redis().SetNxString(rkey, "1", 5)
	// 	if ctx.RespErrString(loc != nil, &errcode, "操作频繁,请稍后再试") {
	// 		return
	// 	}
	// }

	reqdata.Password = utils.Md5V(reqdata.Password)

	// 是否开启图形验证码 1开启,2关闭
	verifycfg := server.GetConfigString(SellerId, 0, "YiDunVerify")
	if verifycfg == "1" {
		rediskey := fmt.Sprintf("%s:%s:captcha:%v", server.Project(), server.Module(), reqdata.Validate)
		redisdata := server.Redis().Get(rediskey)
		if redisdata == nil {
			verifier, err := captcha.New("0fedaaa12f614c70bd2811b367f210e7", "f3f3749c1394c14d6ecd8e85c04bddbd", "33a0f2e37841e610831d879137242f01")
			user := reqdata.Account
			verifyResult, err := verifier.Verify(reqdata.Validate, user)
			if err != nil {
				logs.Error("UserController login", err)
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
				return
			}
			if !verifyResult.Result {
				ctx.RespErrString(true, &errcode, "验证码不正确")
				return
			}
			server.Redis().Del(rediskey)
		}
	}

	var user *model2.XUser
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	if (reqdata.AccountType == 5 || reqdata.AccountType == 7) && reqdata.IdToken != "" {
		logs.Info("TG授权登录用户idToken", reqdata.IdToken)
		// Telegram登录
		data, err := base64.StdEncoding.DecodeString(reqdata.IdToken)
		if err != nil {
			logs.Error("UserController login", err)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			c.PushLoginEvent(false, user)
			return
		}
		queryStr, err := url.QueryUnescape(string(data))
		if err != nil {
			logs.Error("UserController login", err)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			c.PushLoginEvent(false, user)
			return
		}
		params, _ := url.ParseQuery(queryStr)
		// 打印params
		logs.Info("TG授权登录用户", params)
		/*if !server.Debug() {
			auth_date := params.Get("auth_date")
			sec, _ := strconv.ParseInt(auth_date, 10, 64)
			authTime := time.Unix(sec, 0)
			if time.Now().After(authTime.Add(time.Hour * 24)) {
				ctx.RespErrString(true, &errcode, "授权已过期,请重新登录")
				return
			}
		}*/
		if reqdata.AccountType == 5 && !checkTelegramAuthorization(ctx, queryStr) {
			ctx.RespErrString(true, &errcode, "登录失败")
			c.PushLoginEvent(false, user)
			return
		}

		chatId := params.Get("id")
		tgUsername := params.Get("username")
		firstname := params.Get("first_name")
		lastname := params.Get("last_name")
		account := fmt.Sprintf("tg%s%d", chatId, ChannelId)
		nickName := fmt.Sprintf("%s%s", firstname, lastname)
		logs.Info("TG授权登录用户", account)
		tgChatId, _ := strconv.ParseInt(chatId, 10, 64)
		user, err = userDb.Where(userDao.TgChatID.Eq(tgChatId)).Where(userDao.SellerID.Eq(int32(SellerId))).Where(userDao.State.Eq(1)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				tgRobotToken := ""

				if reqdata.AccountType == 5 {
					daobot := server.DaoxHashGame().XRobotConfig
					oldDaobot := server.DaoxHashGame().XTgRobotGuide
					dbbot := daobot.WithContext(ctx.Gin())
					dbOldDaoBot := oldDaobot.WithContext(ctx.Gin())

					xTgbot, _ := dbbot.Where(daobot.GameURL.Like("%" + host + "%")).First()
					xOldTgbot, _ := dbOldDaoBot.Where(oldDaobot.GameURL.Like("%" + host + "%")).First()
					if xTgbot != nil {
						tgRobotToken = xTgbot.Token
					}

					if xOldTgbot != nil {
						tgRobotToken = xOldTgbot.TgRobotToken
					}
				}

				if reqdata.AccountType == 7 {
					type TgAuthRobot struct {
						AuthRobotName     string
						AuthRobotToken    string
						TgRobotGuideToken string
					}
					var tgAuthRobot TgAuthRobot
					xChannelHost := server.DaoxHashGame().XChannelHost
					channelHost, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
					if channelHost != nil && channelHost.TgAuthRobot != "" {
						err := json.Unmarshal([]byte(channelHost.TgAuthRobot), &tgAuthRobot)
						if err != nil {
							tgRobotToken = ""
						}
						if tgAuthRobot.TgRobotGuideToken != "" {
							tgRobotToken = tgAuthRobot.TgRobotGuideToken
						}
					}
				}

				//if tgRobotToken == "" {
				//	ctx.RespErrString(true, &errcode, "系统未绑定接待机器人")
				//	return
				//}

				pwd := xgo.RandomString(8)
				pwd1 := utils.Md5V(pwd)
				pwd2, _ := utils.RsaEncryptUserPassword(pwd)
				tgUserId, _ := strconv.ParseInt(chatId, 10, 64)
				_, err = register(ctx, RegisterReq{Account: account, Password: pwd1, PasswordV2: pwd2, AccountType: reqdata.AccountType, SellerId: SellerId,
					Validate: "123", TgName: tgUsername, Host: host, TgRobotToken: tgRobotToken, TgChatId: tgUserId, ThirdIp: reqdata.ThirdIp,
					DeviceId: reqdata.DeviceId, DeviceType: reqdata.DeviceType, Lang: reqdata.Lang, Brand: reqdata.Brand, NickName: nickName})
				if ctx.RespErr(err, &errcode) {
					return
				}
				user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
				if ctx.RespErr(err, &errcode) {
					return
				}
			} else {
				logs.Error("UserController login", err)
				errmsg := "系统错误,请稍后再试"
				if c.BaseController != nil {
					c.PushLoginEvent(false, user)
				} else {
					logs.Error("登录失败，但 CustomerIO 客户端未初始化，无法推送登录失败事件")
				}
				ctx.RespErrString(true, &errcode, errmsg)
				return
			}
		}

		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 4 {
		// 谷歌账号登录
		aud := authThird.GoogleAppid
		// 校验IdToken
		payload, err := idtoken.Validate(ctx.Gin(), reqdata.IdToken, aud)
		logs.Info("谷歌账号登录用户payload", payload)
		logs.Info("谷歌账号登录用户payload2", payload.Claims)
		if err != nil {
			logs.Error("UserController login", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
			return
		}
		email := ""
		nickName := ""

		if reqdata.Email != "" {
			email = reqdata.Email
		} else {
			email = payload.Claims["email"].(string)
		}

		if reqdata.NickName != "" {
			nickName = reqdata.NickName
		}

		if _, ok := payload.Claims["name"].(string); ok {
			nickName = payload.Claims["name"].(string)
		}

		account := fmt.Sprintf("G-%s", payload.Subject)
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(err, &errcode)
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 3 {
		// 邮箱登录
		user, err = userDb.Where(userDao.Email.Eq(reqdata.Account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.AccountType == 2 {
		// 手机登录
		user, err = userDb.Where(userDao.PhoneNum.Eq(reqdata.Account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(err, &errcode)
			return
		}
	} else if reqdata.AccountType == 8 {
		// WhatsApp登录
		if reqdata.IdToken == "" {
			ctx.RespErrString(true, &errcode, "登录失败")
			return
		}

		// 验证WhatsApp访问令牌
		whatsappUserId, email, nickName, err := c.validateWhatsAppToken(reqdata.IdToken, authThird.WhatsAppAppId)
		if err != nil {
			logs.Error("WhatsApp登录验证失败:", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "WhatsApp登录验证失败")
			return
		}

		// 使用WhatsApp用户ID作为账号
		account := fmt.Sprintf("wa_%s", whatsappUserId)

		// 查找用户
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 9 {
		// Line登录
		if reqdata.IdToken == "" {
			ctx.RespErrString(true, &errcode, "登录失败")
			return
		}

		// 验证Line访问令牌
		lineUserId, email, nickName, err := c.validateLineToken(reqdata.IdToken, authThird.LineAppId)
		if err != nil {
			logs.Error("Line登录验证失败:", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "Line登录验证失败")
			return
		}

		// 使用Line用户ID作为账号
		account := fmt.Sprintf("line_%s", lineUserId)

		// 查找用户
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 10 {
		// Instagram登录
		if reqdata.IdToken == "" {
			ctx.RespErrString(true, &errcode, "登录失败")
			return
		}

		// 验证Instagram访问令牌
		instagramUserId, email, nickName, err := c.validateInstagramToken(reqdata.IdToken, authThird.InstagramAppId)
		if err != nil {
			logs.Error("Instagram登录验证失败:", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "Instagram登录验证失败")
			return
		}

		// 使用Instagram用户ID作为账号
		account := fmt.Sprintf("ig_%s", instagramUserId)

		// 查找用户
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 11 {
		// X(Twitter)登录
		if reqdata.IdToken == "" {
			ctx.RespErrString(true, &errcode, "登录失败")
			return
		}

		// 验证X(Twitter)访问令牌
		twitterUserId, email, nickName, err := c.validateTwitterToken(reqdata.IdToken, authThird.XAppId)
		if err != nil {
			logs.Error("X(Twitter)登录验证失败:", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "X(Twitter)登录验证失败")
			return
		}

		// 使用Twitter用户ID作为账号
		account := fmt.Sprintf("x_%s", twitterUserId)

		// 查找用户
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else if reqdata.AccountType == 12 {
		// Facebook登录
		if reqdata.IdToken == "" {
			ctx.RespErrString(true, &errcode, "登录失败")
			return
		}

		// 验证Facebook访问令牌
		facebookUserId, email, nickName, err := c.validateFacebookToken(reqdata.IdToken, authThird.FacebookAppId)
		if err != nil {
			logs.Error("Facebook登录验证失败:", err)
			c.PushLoginEvent(false, user)
			ctx.RespErrString(true, &errcode, "Facebook登录验证失败")
			return
		}

		// 使用Facebook用户ID作为账号
		account := fmt.Sprintf("fb_%s", facebookUserId)

		// 查找用户
		user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 账号不存在：注册新账号
			pwd := xgo.RandomString(32)
			registerReq := RegisterReq{
				Account:     account,
				Password:    pwd,
				Email:       email,
				AccountType: reqdata.AccountType,
				SellerId:    SellerId,
				Validate:    reqdata.Validate,
				ThirdIp:     reqdata.ThirdIp,
				DeviceId:    reqdata.DeviceId,
				DeviceType:  reqdata.DeviceType,
				Lang:        reqdata.Lang,
				Brand:       reqdata.Brand,
				NickName:    nickName,
				Fbc:         reqdata.Fbc,
				Fbp:         reqdata.Fbp,
				Kwai:        reqdata.Kwai,
				Bigo:        reqdata.Bigo,
			}

			_, err = register(ctx, registerReq)
			if err != nil {
				ctx.RespErrString(true, &errcode, err.Error())
				return
			}
			user, err = userDb.Where(userDao.Account.Eq(account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				ctx.RespErr(err, &errcode)
				return
			}
		}
		reqdata.Account = user.Account
		reqdata.Password = user.Password
	} else {
		// 账号密码登录
		user, err = userDb.Where(userDao.Account.Eq(reqdata.Account)).Where(userDao.SellerID.Eq(int32(SellerId))).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespErr(err, &errcode)
			return
		}
	}
	if user == nil {
		ctx.RespErrString(true, &errcode, "账号不存在")
		return
	}
	// 特殊处理，UT用户不能登录站点
	if user != nil && reqdata.IsUt != 1 && (ChannelId == 51 || ChannelId == 81) {
		ctx.RespErrString(true, &errcode, "UT注册用户不能从站点登录")
		return
	}

	// 首次登录判断体验金领取条件
	// if user.AccountType == 5 && user.LoginIP == "" && user.UsdtGiftStatus == 0 && user.IsInResourceDb == 1 {
	// 	XTgRobotGuide := server.DaoxHashGame().XTgRobotGuide
	// 	tgRobotGuide, err := XTgRobotGuide.WithContext(ctx.Gin()).Where(XTgRobotGuide.TgRobotToken.Eq(user.TgRobotToken)).First()
	// 	if err != nil {
	// 		ctx.RespErrString(true, &errcode, "登录失败")
	// 		// c.PushLoginCustomerIO(1, user)
	// 		return
	// 	}
	// 	if tgRobotGuide.TgRobotType == 1 {
	// 		// if 开启IP限制 且 用户IP不在白名单
	// 		if tgRobotGuide.IsIPRestriction == 1 && strings.Index(tgRobotGuide.IPWhitelist, reqdata.ThirdIp) == -1 {
	// 			LoginIPCount, _ := userDao.WithContext(ctx.Gin()).Where(userDao.LoginIP.Eq(reqdata.ThirdIp)).Count()
	// 			if LoginIPCount+1 >= 3 { // 大于3个及以上不可领取
	// 				user.UsdtGiftStatus = -1
	// 			}
	// 		}
	// 		// if开启设备ID限制
	// 		if tgRobotGuide.IsDeviceRestriction == 1 {
	// 			DeviceIDCount, _ := userDao.WithContext(ctx.Gin()).Where(userDao.LoginDeviceID.Eq(reqdata.DeviceId)).Count()
	// 			if DeviceIDCount+1 >= 3 { // 大于3个及以上不可领取
	// 				user.UsdtGiftStatus = -1
	// 			}
	// 		}
	// 		if user.UsdtGiftStatus == 0 {
	// 			user.UsdtGiftStatus = 1
	// 		}
	// 		_, _ = userDao.WithContext(ctx.Gin()).Where(userDao.UserID.Eq(user.UserID)).
	// 			Update(userDao.UsdtGiftStatus, user.UsdtGiftStatus)
	// 	}
	// }

	if (ChannelId == 19 || user.ChannelID == 19) && strings.Contains(xgo.Env(), "prd") && user.ChannelID != int32(ChannelId) { // 19 正式服测试渠道id
		ctx.RespErrString(true, &errcode, "测试域名不能跨渠道登录")
		return
	}

	if user.CxdID == "" && reqdata.Cxd != "" {
		isoCountry := utils.GetIsoCountryByIp(user.RegisterIP)
		logs.Info("login set cxdid:", reqdata.Account, reqdata.Cxd, isoCountry)
		server.XDb().Table("x_user").Where("Account = ?", reqdata.Account).Update(map[string]interface{}{
			"CxdId":      reqdata.Cxd,
			"IsoCountry": isoCountry,
		})
	}

	extra := map[string]interface{}{"Ip": reqdata.ThirdIp, "ChannelId": ChannelId, "AccountType": reqdata.AccountType,
		"DeviceId": reqdata.DeviceId, "DeviceType": reqdata.DeviceType, "Lang": getLanguage(reqdata.Lang)}
	strextra, _ := json.Marshal(&extra)
	logs.Debug("login host:", user.UserID, host, string(strextra))
	pdata := []interface{}{reqdata.Account, SellerId, reqdata.Password, reqdata.VerifyCode, string(strextra)}

	presult, err := server.Db().CallProcedure("x_api_user_login_verifycode", pdata...)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	result := *presult
	tokendata := server.TokenData{}
	tokendata.UserId = int(abugo.GetInt64FromInterface(result["UserId"]))
	tokendata.SellerId = int(abugo.GetInt64FromInterface(result["SellerId"]))
	tokendata.ChannelId = int(user.ChannelID)
	tokendata.Address = abugo.GetStringFromInterface(result["Address"])
	tokendata.VerifyState = int(abugo.GetInt64FromInterface(result["VerifyState"]))
	tokendata.VerifyAmount = int(abugo.GetInt64FromInterface(result["VerifyAmount"]))
	tokendata.Account = abugo.GetStringFromInterface(result["Account"])
	tokendata.AccountType = int(abugo.GetInt64FromInterface(result["AccountType"]))

	tokendata.Host = host
	if result["OldToken"] != nil {
		server.Http().DelToken(abugo.GetStringFromInterface(result["OldToken"]))
	}
	server.Http().SetToken(abugo.GetStringFromInterface(result["NewToken"]), tokendata)
	result["Token"] = result["NewToken"]
	result["VerifyAddress"] = server.GetConfigString(tokendata.SellerId, 0, "VerifyAddress")
	result["BscVerifyAddress"] = server.GetConfigString(tokendata.SellerId, 0, "BscVerifyAddress")
	delete(result, "NewToken")
	delete(result, "OldToken")
	result["HeadId"] = user.HeadID
	result["NickName"] = user.NickName
	result["AlreadyFirstRecharge"] = !user.FirstRechargeTime.IsZero()

	// 检查注册赠送活动是否开启
	isRegisterBonusReceived, RegisterGiftUnlocked := active.CheckRegisterGiftStatus(user, result)
	result["IsRegisterBonusReceived"] = isRegisterBonusReceived
	result["RegisterGiftUnlocked"] = RegisterGiftUnlocked

	//登录成功，推送消息到 ThinkingData
	threading.GoSafe(func() {
		// 确定登录类型
		loginType := c.getLoginTypeString(reqdata.AccountType)

		// 获取客户端IP
		clientIP := reqdata.ThirdIp
		if clientIP == "" {
			clientIP = "unknown"
		}

		// 使用 ThinkingData 发送登录事件
		err := datapush.SendLoginEvent(user, loginType, clientIP)
		if err != nil {
			logs.Error("发送登录事件到ThinkingData失败:", err, "用户ID:", user.UserID)
		}
	})

	// 检查设备是否变动，如果变动则发送站内信通知
	if user != nil && reqdata.DeviceId != "" && user.LoginDeviceID != "" &&
		reqdata.DeviceId != user.LoginDeviceID && user.LoginDeviceID != "null" {
		// 设备变动，发送站内信通知
		threading.GoSafe(func() {
			// 创建消息发送服务实例
			messageService := msg.NewSendMessageAPIService()

			// 准备变量
			variables := map[string]interface{}{
				"{变动时间-全格式}": time.Now().Format("2006-01-02 15:04:05"),
			}

			// 发送登录设备变动通知，消息类型 "9" 表示登录设备变动通知
			err := messageService.SendMessage("9", int(user.UserID), variables)
			if err != nil {
				logs.Error("发送登录设备变动通知失败: %v", err)
			} else {
				logs.Info("已发送登录设备变动通知给用户 %d", user.UserID)
			}
		})
	}

	ctx.RespOK(result)

	if user.AccountType == 5 {
		threading.GoSafe(func() {
			tgRobotId, _ := GetTgRobotByToken(user.TgRobotToken)
			stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgRobotId, user.TgChatID, stat.ActionType_Login, "", false, time.Time{})
		})
	}
}

func (c *UserController) logout(ctx *abugo.AbuHttpContent) {
	reqdata := struct {
		Token string
	}{}
	ctx.RequestData(&reqdata)
	server.Http().DelToken(reqdata.Token)
	server.Http().DelToken(ctx.Token)
	ctx.RespOK()
}

func (c *UserController) customservice403(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId  int `validate:"required"` //运营商
		LangId    int
		Host      string
		AgentCode string
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	lang := server.LangMap[reqdata.LangId]
	if reqdata.LangId == 1 {
		lang = ""
	}

	host := ctx.Host()
	if reqdata.Host != "" {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	ChannelId, SellerId := server.GetChannel(ctx, host)
	//rediskey := fmt.Sprintf("%s:%s:customservice:%d:%d:%d", server.Project(), server.Module(), SellerId, ChannelId, reqdata.LangId)
	//redisdata := server.Redis().Get(rediskey)

	//if redisdata != nil {
	//	jdata := make(map[string]interface{})
	//	json.Unmarshal(redisdata.([]byte), &jdata)
	//	ctx.RespOK(jdata)
	//} else {
	data := map[string]interface{}{}

	data["CustomService"] = server.GetConfigString(SellerId, ChannelId, "CustomService"+lang)
	data["SellerId"] = SellerId

	xChannelHostDao := server.DaoxHashGame().XChannelHost
	xChannelHostDb := xChannelHostDao.WithContext(nil)
	channelHostInfos, _ := xChannelHostDb.Where(xChannelHostDao.Host.Eq(host)).First()

	// 如果域名开启了客服链接开关
	if channelHostInfos != nil && channelHostInfos.CustomServiceState == 1 && channelHostInfos.CustomService != "" {
		type CustomInfo struct {
			CustomService string
		}

		var customData map[string]CustomInfo

		// 解析 JSON 数据
		json.Unmarshal([]byte(channelHostInfos.CustomService), &customData)
		if info, exists := customData[strconv.Itoa(reqdata.LangId)]; exists {

			var backupCustomInfo CustomInfo
			if reqdata.LangId > 2 {
				backupCustomInfo = customData["2"]
			}

			if reqdata.LangId == 13 {
				backupCustomInfo = customData["1"]
			}

			if info.CustomService == "" {
				info = backupCustomInfo
			}

			data = map[string]interface{}{
				"CustomService": info.CustomService,
				"SellerId":      SellerId,
			}
		}

	} else {
		iagent, _ := server.Db().Query("select * FROM x_agent_independence where `Host` = ? limit 1", []interface{}{host})
		// 推广链接
		if len(reqdata.AgentCode) > 0 {
			promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
			indepenAgent, err := server.Db().Query("select * FROM x_agent_independence where `PromotionHost` = ? limit 1", []interface{}{promotionHost})
			if err == nil && indepenAgent != nil {
				iagent = indepenAgent
			}
		}
		if len(*iagent) > 0 {
			if abugo.InterfaceToInt((*iagent)[0]["IsSelfCustomService"]) == 1 {
				ics := map[string]interface{}{}
				json.Unmarshal([]byte(abugo.GetStringFromInterface((*iagent)[0]["CustomeService"])), &ics)
				var backupCustomInfo model.CustomInfo
				if reqdata.LangId > 2 {
					enLang := server.LangMap[2]
					backupCustomInfo.CustomService = xgo.ToString(ics["CustomeService"+enLang])
				}

				if reqdata.LangId == 13 {
					backupCustomInfo.CustomService = xgo.ToString(ics["CustomeService"])
				}
				var info model.CustomInfo
				info.CustomService = xgo.ToString(ics["CustomeService"+lang])

				if info.CustomService == "" {
					info = backupCustomInfo
				}

				data["CustomService"] = info.CustomService
				data["SellerId"] = SellerId
			}
			if abugo.InterfaceToInt((*iagent)[0]["IsSelfTgBot"]) == 1 {
				tginfo := map[string]interface{}{}
				err := json.Unmarshal([]byte(abugo.InterfaceToString((*iagent)[0]["TgInfo"])), &tginfo)
				if err == nil {
					data["TgBot"] = xgo.ToString(tginfo["TgAddress"])
				}
			}
		}
	}

	ctx.RespOK(data)
	//	server.Redis().SetEx(rediskey, 10, data)
	//}
}

// 在线客服    CustomService
// tg         CustomTelegram
// whatsapp   CustomWhatapp
// 官方频道    CustomTelegramGroup

func (c *UserController) customservice(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		SellerId  int `validate:"required"` //运营商
		LangId    int
		Host      string
		AgentCode string
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	lang := server.LangMap[reqdata.LangId]
	if reqdata.LangId == 1 {
		lang = ""
	}

	host := ctx.Host()
	if reqdata.Host != "" {
		host = reqdata.Host
	}
	host = strings.Replace(host, "www.", "", -1)
	host = strings.Split(host, ":")[0]

	tgs := map[string]string{}
	wss := map[string]string{}

	ChannelId, SellerId := server.GetChannel(ctx, host)
	//rediskey := fmt.Sprintf("%s:%s:customservice:%d:%d:%d", server.Project(), server.Module(), SellerId, ChannelId, reqdata.LangId)
	//redisdata := server.Redis().Get(rediskey)

	//if redisdata != nil {
	//	jdata := make(map[string]interface{})
	//	json.Unmarshal(redisdata.([]byte), &jdata)
	//	ctx.RespOK(jdata)
	//} else {
	data := map[string]interface{}{}

	data["CustomTelegram"] = server.GetConfigString(SellerId, ChannelId, "CustomTelegram"+lang)
	data["CustomWhatapp"] = server.GetConfigString(SellerId, ChannelId, "CustomWhatapp"+lang)
	data["CustomTelegramGroup"] = server.GetConfigString(SellerId, ChannelId, "CustomTelegramGroup"+lang)
	data["CustomService"] = server.GetConfigString(SellerId, ChannelId, "CustomService"+lang)
	data["CustomSkype"] = server.GetConfigString(SellerId, ChannelId, "CustomSkype"+lang)
	data["CustomSafew"] = server.GetConfigString(SellerId, ChannelId, "CustomSafew"+lang)

	xChannelHostDao := server.DaoxHashGame().XChannelHost
	xChannelHostDb := xChannelHostDao.WithContext(nil)
	channelHostInfos, _ := xChannelHostDb.Where(xChannelHostDao.Host.Eq(host)).First()

	// 如果域名开启了客服链接开关
	if channelHostInfos != nil && channelHostInfos.CustomServiceState == 1 && channelHostInfos.CustomService != "" {
		type CustomInfo struct {
			CustomService       string
			CustomSkype         string
			CustomTelegram      string
			CustomTelegramGroup string
			CustomWhatapp       string
			CustomSafew         string
		}

		var customData map[string]CustomInfo

		// 解析 JSON 数据
		json.Unmarshal([]byte(channelHostInfos.CustomService), &customData)
		if info, exists := customData[strconv.Itoa(reqdata.LangId)]; exists {

			var backupCustomInfo CustomInfo
			if reqdata.LangId > 2 {
				backupCustomInfo = customData["2"]
			}

			if reqdata.LangId == 13 {
				backupCustomInfo = customData["1"]
			}

			if info.CustomTelegram == "" && info.CustomWhatapp == "" && info.CustomTelegramGroup == "" && info.CustomService == "" && info.CustomSkype == "" && info.CustomSafew == "" {
				info = backupCustomInfo
			}

			data = map[string]interface{}{
				"CustomTelegram":      info.CustomTelegram,
				"CustomWhatapp":       info.CustomWhatapp,
				"CustomTelegramGroup": info.CustomTelegramGroup,
				"CustomService":       info.CustomService,
				"CustomSkype":         info.CustomSkype,
				"CustomSafew":         info.CustomSafew,
			}
		}

	} else {
		iagent, _ := server.Db().Query("select * FROM x_agent_independence where `Host` = ? limit 1", []interface{}{host})
		// 推广链接
		if len(reqdata.AgentCode) > 0 {
			promotionHost := fmt.Sprintf(utils.TopAgentPromotionHost, host, reqdata.AgentCode)
			indepenAgent, err := server.Db().Query("select * FROM x_agent_independence where `PromotionHost` = ? limit 1", []interface{}{promotionHost})
			if err == nil && indepenAgent != nil {
				iagent = indepenAgent
			}
		}
		if len(*iagent) > 0 {
			if abugo.InterfaceToInt((*iagent)[0]["IsSelfCustomService"]) == 1 {
				ics := map[string]interface{}{}
				json.Unmarshal([]byte(abugo.GetStringFromInterface((*iagent)[0]["CustomeService"])), &ics)
				var backupCustomInfo model.CustomInfo
				if reqdata.LangId > 2 {
					enLang := server.LangMap[2]
					backupCustomInfo.CustomTelegram = xgo.ToString(ics["CustomTelegram"+enLang])
					backupCustomInfo.CustomWhatapp = xgo.ToString(ics["CustomWhatapp"+enLang])
					backupCustomInfo.CustomTelegramGroup = xgo.ToString(ics["CustomTelegramGroup"+enLang])
					backupCustomInfo.CustomService = xgo.ToString(ics["CustomeService"+enLang])
					backupCustomInfo.CustomSkype = xgo.ToString(ics["CustomSkype"+enLang])
					backupCustomInfo.CustomSafew = xgo.ToString(ics["CustomSafew"+enLang])
				}

				if reqdata.LangId == 13 {
					backupCustomInfo.CustomTelegram = xgo.ToString(ics["CustomTelegram"])
					backupCustomInfo.CustomWhatapp = xgo.ToString(ics["CustomWhatapp"])
					backupCustomInfo.CustomTelegramGroup = xgo.ToString(ics["CustomTelegramGroup"])
					backupCustomInfo.CustomService = xgo.ToString(ics["CustomeService"])
					backupCustomInfo.CustomSkype = xgo.ToString(ics["CustomSkype"])
					backupCustomInfo.CustomSafew = xgo.ToString(ics["CustomSafew"])
				}
				var info model.CustomInfo
				info.CustomTelegram = xgo.ToString(ics["CustomTelegram"+lang])
				info.CustomWhatapp = xgo.ToString(ics["CustomWhatapp"+lang])
				info.CustomTelegramGroup = xgo.ToString(ics["CustomTelegramGroup"+lang])
				info.CustomService = xgo.ToString(ics["CustomeService"+lang])
				info.CustomSkype = xgo.ToString(ics["CustomSkype"+lang])
				info.CustomSafew = xgo.ToString(ics["CustomSafew"+lang])

				if info.CustomTelegram == "" && info.CustomWhatapp == "" && info.CustomTelegramGroup == "" && info.CustomService == "" && info.CustomSkype == "" && info.CustomSafew == "" {
					info = backupCustomInfo
				}

				data["CustomTelegram"] = info.CustomTelegram
				data["CustomWhatapp"] = info.CustomWhatapp
				data["CustomTelegramGroup"] = info.CustomTelegramGroup
				data["CustomService"] = info.CustomService
				data["CustomSkype"] = info.CustomSkype
				data["CustomSafew"] = info.CustomSafew
			}
			if abugo.InterfaceToInt((*iagent)[0]["IsSelfTgBot"]) == 1 {
				tginfo := map[string]interface{}{}
				err := json.Unmarshal([]byte(abugo.InterfaceToString((*iagent)[0]["TgInfo"])), &tginfo)
				if err == nil {
					data["TgBot"] = xgo.ToString(tginfo["TgAddress"])
				}
			}
		}
	}

	data["TgBot"] = server.GetConfigString(SellerId, ChannelId, "TgBot")
	data["TgTutorials"] = server.GetConfigString(SellerId, ChannelId, "TgTutorials")

	if tv, ok := tgs[host]; ok {
		data["CustomTelegram"] = tv
	}
	if wv, ok := wss[host]; ok {
		data["CustomWhatapp"] = wv
	}
	data = c.UpdateCustomServiceConfig(reqdata.LangId, SellerId, data)
	ctx.RespOK(data)
	//	server.Redis().SetEx(rediskey, 10, data)
	//}
}

func ExtractDomain(rawURL string) (string, error) {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", err
	}

	// 获取 Host 部分，
	return parsedURL.Host, nil
}

// 更新客服链接配置: 1.如果繁体客服链接为空，则使用中文的链接 2.其他语言的客服链接为空，则使用英文的链接
// 1-中文， 2-英文 13-繁体
func (this *UserController) UpdateCustomServiceConfig(langId, sellerId int, data map[string]interface{}) map[string]interface{} {
	if this.CheckCustomServiceConfig(data) {
		return data
	}
	// `{"1":{"1":"http://www.baidu.com","2":"http://www.360.com","3":"","4":"","5":""},"2":{"1":"http://www.tudou.com","2":"http://www.bilibili.com","3":"","4":"","5":""},"ai":"0"}`
	strLang := strconv.Itoa(langId)
	sellerInfo, err := server.XDb().Table("x_seller").Where("State = 1 AND SellerId = ?", sellerId).First()
	if err != nil || sellerInfo == nil {
		return data
	}
	if len(sellerInfo.RawData) <= 0 {
		return data
	}
	strCustomCfg := sellerInfo.String("CustomerLinks")
	if len(strCustomCfg) <= 0 {
		return data
	}
	customCfgData := make(map[string]interface{})
	err = json.Unmarshal([]byte(strCustomCfg), &customCfgData)
	if err != nil {
		return data
	}
	langZhMap := this.GetCustomConfigByLang("1", customCfgData)
	langEnMap := this.GetCustomConfigByLang("2", customCfgData)
	langDataMap := this.GetCustomConfigByLang(strLang, customCfgData)
	// 获取语言对应的 map
	for k, _ := range langEnMap {
		item, _ := data[k].(string)
		// 如果域名当前语言的配置为空，则使用运营商语言对应的配置
		if len(item) <= 0 {
			// 如果运营商当前语言的配置为空，则使替换对应的配置
			if val, ok := langDataMap[k]; ok {
				if strVal, isStr := val.(string); isStr && strVal == "" {
					// 值是空字符串
					switch langId {
					case 13: // 如果是繁体，则使用中文的配置
						data[k] = langZhMap[k]
					default:
						data[k] = langEnMap[k]
					}
				} else {
					// 值存在且不是空字符串
					data[k] = langDataMap[k]
				}
			} else {
				// 键不存在或值不是字符串
				switch langId {
				case 13: // 如果是繁体，则使用中文的配置
					data[k] = langZhMap[k]
				default:
					data[k] = langEnMap[k]
				}
			}
		}
	}
	return data
}

// 获取语言对应的配置数据 map
func (this *UserController) GetCustomConfigByLang(lang string, data map[string]interface{}) map[string]interface{} {
	langMap := make(map[string]interface{})
	// 获取语言对应的 map
	if innerMap, ok := data[lang].(map[string]interface{}); ok {
		langMap["CustomTelegram"] = innerMap["3"]
		langMap["CustomWhatapp"] = innerMap["5"]
		langMap["CustomTelegramGroup"] = innerMap["4"]
		langMap["CustomService"] = innerMap["1"]
		langMap["CustomSkype"] = innerMap["2"]
		langMap["CustomSafew"] = innerMap["6"] // 新增safew客服映射
	}
	return langMap
}

// 检测客服链接配置是否完整
func (this *UserController) CheckCustomServiceConfig(data map[string]interface{}) bool {
	item, _ := data["CustomTelegram"].(string)
	if len(item) <= 0 {
		return false
	}
	item, _ = data["CustomWhatapp"].(string)
	if len(item) <= 0 {
		return false
	}
	item, _ = data["CustomTelegramGroup"].(string)
	if len(item) <= 0 {
		return false
	}
	item, _ = data["CustomService"].(string)
	if len(item) <= 0 {
		return false
	}
	item, _ = data["CustomSkype"].(string)
	if len(item) <= 0 {
		return false
	}
	item, _ = data["CustomSafew"].(string)
	if len(item) <= 0 {
		return false
	}
	return true
}

func getNotActiveUserBetCount(userId int32) (betCount int64) {
	ctx := context.Background()
	xUserWallet := server.DaoxHashGame().XUserWallet
	xOrder := server.DaoxHashGame().XOrder
	tbWallet, err := xUserWallet.WithContext(ctx).Where(xUserWallet.UserID.Eq(userId)).Order(xUserWallet.ID.Desc()).First()
	if err != nil {
		logs.Error("getNotActiveUserBetCount err:", err)
		return 0
	}
	betCount, err = xOrder.WithContext(ctx).Where(xOrder.FromAddress.Eq(tbWallet.Address)).Count()
	if err != nil {
		logs.Error("getNotActiveUserBetCount err:", err)
		return 0
	}
	return betCount
}

func getUserBetCount(userId int32) (betCount int64) {
	var err error
	ctx := context.Background()
	xOrder := server.DaoxHashGame().XOrder
	betCount, err = xOrder.WithContext(ctx).Where(xOrder.UserID.Eq(userId)).Count()
	if err != nil {
		logs.Error("getUserBetCount err:", err)
		return 0
	}
	return betCount
}

func (c *UserController) info(ctx *abugo.AbuHttpContent) {
	var errcode int
	reqdata := struct {
		DeviceId   string
		DeviceType string
		Lang       string
		Host       string
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	daoUser := server.DaoxHashGame().XUser
	db := daoUser.WithContext(ctx.Gin())
	user, err := db.Where(daoUser.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		logs.Error(err)
		return
	}
	token.VerifyState = int(user.VerifyState)

	logs.Info("use_info %d %+v", user.UserID, reqdata)

	host, _, _ := net.SplitHostPort(ctx.Host())
	host = strings.Replace(host, "www.", "", -1)
	if reqdata.Host != "" {
		host = reqdata.Host
	}

	var tgRobotType int32 // 0-不是从tg进入 1-引客机器人 2-英文接待机器人
	// var IsAotoTyj2 int32
	var KefuTgUsername string
	var tgBotId int64
	var RobotKefuPop bool
	var RobotKefuPopText string
	var RobotKefuLink string
	var tgTyjAmount int32
	var tgTyjCurrency string
	ifCanRewardGift := 0
	// var totalLiushui decimal.Decimal
	// enRobotTaskTargetNum := server.GetConfigInt(int(user.SellerID), 0, "EnRobotTaskTargetNum") // 英文接待机器人第二笔体验金需满足游戏局数
	// enRobotTaskReward := server.GetConfigFloat(int(user.SellerID), 0, "EnRobotTaskReward")     // 英文接待机器人第二笔体验金金额
	// EnRobotTaskLiushui := server.GetConfigFloat(int(user.SellerID), 0, "EnRobotTaskLiushui")   // 英文接待机器人第二笔体验金需满足流水
	// enRobotTyjUsdt := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtEnRobot")     // 英文接待机器人体验金金额
	// EnRobotKefuPopLiushui := server.GetConfigInt(int(user.SellerID), 0, "EnRobotKefuPopLiushui") // 英文接待机器人满足流水弹出客服弹窗
	if user.AccountType == 5 && user.TgRobotToken != "" {
		// 获取账号的tg属性
		tgRobotGuideDao := server.DaoxHashGame().XRobotConfig
		oldTgRobotGuideDao := server.DaoxHashGame().XTgRobotGuide
		tgRobotGuide, _ := tgRobotGuideDao.WithContext(ctx.Gin()).Where(tgRobotGuideDao.Token.Eq(user.TgRobotToken)).First()
		oldTgRobotGuide, _ := oldTgRobotGuideDao.WithContext(ctx.Gin()).Where(oldTgRobotGuideDao.TgRobotToken.Eq(user.TgRobotToken)).First()

		if err != nil {
			logs.Error("获取机器人属性错误", err.Error())
		} else {
			if tgRobotGuide != nil {
				tgRobotType = tgRobotGuide.RobotType
				tgBotId = tgRobotGuide.ID
				tgTyjAmount = tgRobotGuide.GiftAmount
				if user.IsInResourceDb == 1 {
					tgTyjAmount = tgRobotGuide.GiftAmountIndb
				}
				tgTyjCurrency = tgRobotGuide.GiftCurrency
			}

			if oldTgRobotGuide != nil {
				var totalLiushui decimal.Decimal
				var IsAotoTyj2 int32
				enRobotTaskTargetNum := server.GetConfigInt(int(user.SellerID), 0, "EnRobotTaskTargetNum") // 英文接待机器人第二笔体验金需满足游戏局数
				enRobotTaskReward := server.GetConfigFloat(int(user.SellerID), 0, "EnRobotTaskReward")     // 英文接待机器人第二笔体验金金额
				EnRobotTaskLiushui := server.GetConfigFloat(int(user.SellerID), 0, "EnRobotTaskLiushui")   // 英文接待机器人第二笔体验金需满足流水
				enRobotTyjUsdt := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtEnRobot")     // 英文接待机器人体验金金额
				// EnRobotKefuPopLiushui := server.GetConfigInt(int(user.SellerID), 0, "EnRobotKefuPopLiushui") // 英文接待机器人满足流水弹出客服弹窗

				tgRobotType = oldTgRobotGuide.TgRobotType
				tgBotId = oldTgRobotGuide.ID
				IsAotoTyj2 = oldTgRobotGuide.IsAotoTyj2
				KefuTgUsername = strings.ReplaceAll(oldTgRobotGuide.KefuTgUserName, "@", "https://t.me/")
				RobotKefuPopText = oldTgRobotGuide.H5KefuPopText
				RobotKefuLink = oldTgRobotGuide.H5KefuLink

				if tgRobotType == 2 {
					// 不在库用户完成红包任务要变成在库
					if user.UsdtGiftStatus == 0 {
						// result, err := checkUserInResourceDb(user.TgName, user.TgChatID)
						// if err == nil && result.Id > 0 && result.IsAvailableReceive && (result.SourceType == 2 || result.SourceType == 0) {
						// 	server.Db().Conn().Exec("update x_user set IsInResourceDb = 1, UsdtGiftStatus = 1 where UserId = ?", token.UserId)
						// }
						server.Db().Conn().Exec("update x_user set IsInResourceDb = 1, UsdtGiftStatus = 1 where UserId = ?", token.UserId)
					}
					totalLiushui = decimal.NewFromFloat(user.TotalLiuSui)
					// 完成指定局数哈希游戏 且 完成指定流水 可以再领一次
					if user.UsdtGiftStatus == 2 && getUserBetCount(user.UserID) >= enRobotTaskTargetNum && EnRobotTaskLiushui > 0 && totalLiushui.GreaterThan(decimal.NewFromFloat(EnRobotTaskLiushui)) {
						server.Db().Conn().Exec("update x_user set UsdtGiftStatus = 3 where UserId = ?", token.UserId)
					}
					// // 完成流水或余额低于1U 弹窗联系客服
					// if totalLiushui.GreaterThanOrEqual(decimal.NewFromInt(EnRobotKefuPopLiushui)) || user.Amount < 1 {
					// 	RobotKefuPop = true
					// }
					user.IsInResourceDb = 1
				}
				ctx.Put("IsAotoTyj2", IsAotoTyj2)
				ctx.Put("EnRobotTaskTargetNum", enRobotTaskTargetNum)
				ctx.Put("EnRobotTaskReward", enRobotTaskReward)
				ctx.Put("EnRobotTaskLiushui", EnRobotTaskLiushui)
				ctx.Put("TiYanJingUsdtEnRobot", enRobotTyjUsdt)
			}

		}
	}
	// ctx.Put("IsAotoTyj2", IsAotoTyj2)
	ctx.Put("TgTyjAmount", tgTyjAmount)
	ctx.Put("TgTyjCurrency", tgTyjCurrency)
	ctx.Put("TgRobotType", tgRobotType)

	// ctx.Put("EnRobotTaskTargetNum", enRobotTaskTargetNum)
	// ctx.Put("EnRobotTaskReward", enRobotTaskReward)
	// ctx.Put("EnRobotTaskLiushui", EnRobotTaskLiushui)
	// ctx.Put("TiYanJingUsdtEnRobot", enRobotTyjUsdt)

	// if tgRobotType == 1 {
	// 	// 已领取trx体验金且投注次数大于等于12次，可以领取u体验金
	// 	if user.UsdtGiftStatus == 0 && user.TrxGiftStatus == 2 && (user.BetCount >= 12 ||
	// 		user.AccountType == 5 && tgRobotType == 1 && user.Address == strconv.Itoa(int(user.UserID)) && getNotActiveUserBetCount(user.UserID) >= 12) {
	// 		user.UsdtGiftStatus = 1
	// 		server.Db().Conn().Exec("update x_user set UsdtGiftStatus = 1 where UserId = ?", token.UserId)
	// 	}
	// }

	xChannelHost := server.DaoxHashGame().XChannelHost
	channelHost, _ := xChannelHost.WithContext(nil).Where(xChannelHost.Host.Eq(host)).First()
	isTyjEnable := 2
	if channelHost != nil {
		isTyjEnable = int(channelHost.IsTyjEnable)
	}

	// 非tg机器人注册体验金状态
	if isTyjEnable == 1 && user.AccountType != 5 {
		RegisterIPCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegisterIP.Eq(user.RegisterIP)).Count()
		RegDeviceIDConut, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegDeviceID.Eq(user.RegDeviceID)).Count()

		if channelHost.GiftIPLimit == 1 {
			if RegisterIPCount > 1 {
				user.UsdtGiftStatus = -1
			}
		}

		if channelHost.GiftDeviceIDLimit == 1 {
			if RegDeviceIDConut > 1 {
				user.UsdtGiftStatus = -1
			}
		}

		if user.UsdtGiftStatus == 2 || user.UsdtGiftStatus == 4 {
			user.UsdtGiftStatus = -1
		}
	}

	ctx.Put("IsTyjEnable", isTyjEnable)
	ctx.Put("if_can_reward_gift", ifCanRewardGift)
	var RechargeAmount float64
	xUserRechargeWithard := server.DaoxHashGame().XUserRechargeWithard
	tbUserRechargeWithard, _ := xUserRechargeWithard.WithContext(ctx.Gin()).Where(xUserRechargeWithard.UserID.Eq(user.UserID)).First()
	if tbUserRechargeWithard != nil {
		RechargeAmount = tbUserRechargeWithard.RechargeAmount
	}
	ctx.Put("RechargeAmount", RechargeAmount)
	ctx.Put("TotalLiushui", user.TotalLiuSui)
	ctx.Put("WithdrawLiuSui", user.WithdrawLiuSui)
	ctx.Put("RobotKefuPop", RobotKefuPop)
	ctx.Put("RobotKefuPopText", RobotKefuPopText)
	ctx.Put("RobotKefuLink", RobotKefuLink)
	ctx.Put("IsInResourceDb", user.IsInResourceDb)
	ctx.Put("AlreadyFirstRecharge", !user.FirstRechargeTime.IsZero()) // 是否充过首充

	// 已激活地址
	if user.Address != "" && user.Address != strconv.Itoa(int(user.UserID)) && (tgRobotType == 1 || tgRobotType == 2) {
		daoWallet := server.DaoxHashGame().XUserWallet
		dbWallet := daoWallet.WithContext(ctx.Gin())
		xUserWallet, err := dbWallet.Where(daoWallet.UserID.Eq(user.UserID)).Where(daoWallet.Address.Eq(user.Address)).First()
		if err == nil {
			if xUserWallet.IsTgSend == 0 {
				// 给用户TG发送激活成功消息
				threading.GoSafe(func() {
					tgSendBindSuccess(ctx.Gin(), user.UserID, user.Address)
					dbWallet.Where(daoWallet.UserID.Eq(user.UserID)).Where(daoWallet.Address.Eq(user.Address)).Update(daoWallet.IsTgSend, 1)
				})
			}
			if xUserWallet.IsStat == 0 {
				threading.GoSafe(func() {
					// TG统计 用户激活地址
					err := stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgBotId, user.TgChatID, stat.ActionType_Activation, user.Address, false, time.Time{})
					if err == nil {
						dbWallet.Where(daoWallet.UserID.Eq(user.UserID)).Where(daoWallet.Address.Eq(user.Address)).Update(daoWallet.IsStat, 1)
					}
				})
			}
		}
	}

	var TrxTyjState int32
	daoTyj := server.DaoxHashGame().XTiyanjing
	xTyj, err := daoTyj.WithContext(ctx.Gin()).Where(daoTyj.UserID.Eq(user.UserID)).Where(daoTyj.Symbol.Eq("trx")).First()
	if err == nil {
		TrxTyjState = xTyj.State
	}
	// 优先级: 玩家列表 > 渠道域名配置 > 语言管理配置 > 提现限制配置
	// 提现限制配置(1:全局 2:VIP 3:代理 4:个人)优先级: 代理配置 > VIP设置 > 全局设置

	// 玩家列表配置的
	pwdVerificationType := user.PwdVerificationType
	if pwdVerificationType == 0 {
		// 渠道域名配置的
		daoUrl := server.DaoxHashGame().XChannelHost
		urlData, err1 := daoUrl.WithContext(ctx.Gin()).Where(daoUrl.ChannelID.Eq(user.ChannelID)).Where(daoUrl.Host.Eq(token.Host)).First()
		if err1 == nil {
			pwdVerificationType = urlData.PwdVerificationType
		}
		// 语言管理配置的
		if pwdVerificationType == 0 {
			tmLang, _ := strconv.Atoi(reqdata.Lang)

			daoLang := server.DaoxHashGame().XLangList
			langData, err1 := daoLang.WithContext(ctx.Gin()).
				Where(daoLang.ID.Eq(int32(tmLang))).First()
			if err1 == nil {
				pwdVerificationType = langData.PwdVerificationType
			}
		}
		// 提现限制配置的
		if pwdVerificationType == 0 {
			// 提现限制代理配置的
			daoWithdraw := server.DaoxHashGame().XWithdrawLimitConfig
			withdraw2, err2 := daoWithdraw.WithContext(ctx.Gin()).Where(daoWithdraw.Type.Eq(3)).Where(daoWithdraw.ChannelID.Eq(user.ChannelID)).Where(daoWithdraw.SellerID.Eq(int32(token.SellerId))).First()
			if err2 == nil {
				pwdVerificationType = withdraw2.PwdVerificationType
			}
			// 提现限制VIP配置的
			if pwdVerificationType == 0 {
				vipLevel := int32(0)
				daoVip := server.DaoxHashGame().XVipInfo

				vipInfo, _ := daoVip.WithContext(ctx.Gin()).
					Where(daoVip.UserID.Eq(user.UserID)).First()
				if vipInfo != nil {
					vipLevel = vipInfo.VipLevel
				}

				withdraw1, err2 := daoWithdraw.WithContext(ctx.Gin()).
					Where(daoWithdraw.Type.Eq(2)).
					Where(daoWithdraw.VipLevel.Eq(vipLevel)).
					Where(daoWithdraw.ChannelID.Eq(user.ChannelID)).
					Where(daoWithdraw.SellerID.Eq(int32(token.SellerId))).First()
				if err2 == nil {
					pwdVerificationType = withdraw1.PwdVerificationType
				}
			}
			// 提现限制全局配置的
			if pwdVerificationType == 0 {
				withdraw3, err2 := daoWithdraw.WithContext(ctx.Gin()).Where(daoWithdraw.Type.Eq(1)).Where(daoWithdraw.ChannelID.Eq(user.ChannelID)).Where(daoWithdraw.SellerID.Eq(int32(token.SellerId))).First()
				if err2 == nil {
					pwdVerificationType = withdraw3.PwdVerificationType
				}
			}
		}
	}
	// 最终如果没有就默认为邮箱验证方式
	if pwdVerificationType == 0 {
		pwdVerificationType = 1
	}
	ctx.Put("Amount", user.Amount)
	ctx.Put("LockedAmount", user.LockedAmount)
	ctx.Put("UserId", user.UserID)
	ctx.Put("SellerId", user.SellerID)
	ctx.Put("Address", user.Address)
	ctx.Put("Account", user.Account)
	ctx.Put("AccountType", user.AccountType)
	ctx.Put("TgName", user.TgName)
	ctx.Put("VerifyState", user.VerifyState)
	ctx.Put("VerifyAmount", user.VerifyAmount)
	ctx.Put("ChannelId", user.ChannelID)
	ctx.Put("PhoneNum", user.PhoneNum)
	ctx.Put("Email", user.Email)
	ctx.Put("BindPhoneNum", user.BindPhoneNum)
	ctx.Put("BindEmail", user.BindEmail)
	ctx.Put("PwdVerificationType", pwdVerificationType)
	ctx.Put("RegisterTime", user.RegisterTime.Format("2006-01-02 15:04:05"))
	ctx.Put("HeadId", user.HeadID)
	ctx.Put("Gender", user.Gender)
	ctx.Put("DeliveryAddress", user.DeliveryAddress)
	ctx.Put("State", user.State)

	if user.Birthday.Format("2006-01-02") == "0001-01-01" {
		ctx.Put("Birthday", "")
	} else {
		ctx.Put("Birthday", user.Birthday.Format("2006-01-02"))
	}

	ctx.Put("RealName", user.RealName)
	ctx.Put("NickName", user.NickName)
	ctx.Put("UpdatePasswordTime", user.UpdatePasswordTime)
	ctx.Put("UpdateWalletPasswordTime", user.UpdateWalletPasswordTime)
	logs.Info("用户AccountType", user.AccountType)
	logs.Info("用户TgRobotType", tgRobotType)
	logs.Info("用户UserId", user.UserID)
	logs.Info("用户UsdtGiftStatus", user.UsdtGiftStatus)
	logs.Info("用户TgTyjCurrency", tgTyjCurrency)
	logs.Info("用户TgTyjAmount", tgTyjAmount)
	ctx.Put("UsdtGiftStatus", user.UsdtGiftStatus)
	ctx.Put("TrxGiftStatus", user.TrxGiftStatus)
	ctx.Put("TrxTyjState", TrxTyjState)
	ctx.Put("KefuTgUsername", KefuTgUsername)
	if len(user.WalletPassword) > 0 {
		ctx.Put("WalletPassword", 1)
	} else {
		ctx.Put("WalletPassword", 2)
	}

	// 检查用户是否已领取注册奖金
	var isRegisterBonusReceived bool
	if user.UsdtGiftStatus == 2 {
		isRegisterBonusReceived = true
	}
	ctx.Put("IsRegisterBonusReceived", isRegisterBonusReceived)

	RechargeAddressTron := ""
	if user.RechargeAddressTron != "" {
		RechargeAddressTron = user.RechargeAddressTron
	}
	if len(RechargeAddressTron) == 0 {
		for {
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 1 limit 1"
			server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressTron)
			if len(RechargeAddressTron) == 0 {
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 1"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressTron)
			if r == nil {
				logs.Error("RechargeAddressTron 数据库执行结果为空")
				break
			}
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "update x_user set RechargeAddressTron = ? where UserId = ?"
				server.Db().Conn().Exec(sql, RechargeAddressTron, token.UserId)
				break
			}
		}
	}

	RechargeAddressEth := ""
	if user.RechargeAddressEth != "" {
		RechargeAddressEth = user.RechargeAddressEth
	}

	if len(RechargeAddressEth) == 0 {
		for {
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 2 limit 1"
			server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressEth)
			if len(RechargeAddressEth) == 0 {
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 2"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressEth)
			if r == nil {
				logs.Error("RechargeAddressEth  数据库执行结果为空")
				break
			}
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "update x_user set RechargeAddressEth = ? where UserId = ?"
				server.Db().Conn().Exec(sql, RechargeAddressEth, token.UserId)
				break
			}
		}
	}
	ctx.Put("RechargeAddressTron", RechargeAddressTron)
	ctx.Put("RechargeAddressEth", RechargeAddressEth)
	topagentid := user.TopAgentID
	var isNeedActive int32 = 1
	if topagentid > 0 {
		iagent, _ := server.Db().Query("select * FROM x_agent_independence where `UserId` = ? limit 1", []interface{}{topagentid})
		if len(*iagent) > 0 {
			ctx.Put("Host", (*iagent)[0]["Host"])
		}
		// 提现是否需要激活
		daoAgent := server.DaoxHashGame().XAgent
		Agent, err := daoAgent.WithContext(ctx.Gin()).Select(daoAgent.IsNeedActive).Where(daoAgent.UserID.Eq(user.TopAgentID)).First()
		if err == nil {
			isNeedActive = Agent.IsNeedActive
		}
	} else {
		// 提现是否需要激活
		daoAgent := server.DaoxHashGame().XAgent
		Agent, err := daoAgent.WithContext(ctx.Gin()).Select(daoAgent.IsNeedActive).Where(daoAgent.UserID.Eq(user.UserID)).First()
		if err == nil {
			isNeedActive = Agent.IsNeedActive
		}
	}

	if isNeedActive == 1 {
		if len(host) > 0 {
			tb := server.DaoxHashGame().XChannelHost
			db := tb.WithContext(context.Background())
			c, err := db.Where(tb.Host.Eq(host)).Select(tb.WithdrawNeedActive).First()
			if err == nil {
				isNeedActive = c.WithdrawNeedActive
			}
		}
	}
	ctx.Put("WithdrawalVerify", isNeedActive)

	// 查看是否可领取新机器人礼金
	var robotRewardUsdtState int32
	var robotRewardAmount int32
	xRobotMissionRewardDao := server.DaoxHashGame().XRobotMissionReward
	xRobotMissionRewardInfos, err := xRobotMissionRewardDao.WithContext(nil).
		Where(xRobotMissionRewardDao.UserID.Eq(int64(token.UserId))).
		Where(xRobotMissionRewardDao.Currency.Eq("usdt")).
		First()

	if xRobotMissionRewardInfos != nil {
		robotRewardUsdtState = xRobotMissionRewardInfos.Stat
		robotRewardAmount = xRobotMissionRewardInfos.Amount
	}

	ctx.Put("RobotRewardUsdtState", robotRewardUsdtState)
	ctx.Put("RobotRewardAmount", robotRewardAmount)

	// 获取当前时间戳
	currentTime := time.Now().Unix()
	// 获取最后一条领取体验金记录
	tiyanjingRecord, err := server.DaoxHashGame().XTiyanjing.WithContext(nil).
		Where(server.DaoxHashGame().XTiyanjing.UserID.Eq(int32(token.UserId))).
		Where(server.DaoxHashGame().XTiyanjing.State.Eq(7)).
		Order(server.DaoxHashGame().XTiyanjing.ID.Desc()).
		First()

	receivedStatus := 0
	if tiyanjingRecord != nil {
		// 计算距离领取体验金记录的时间差
		timeDiff := currentTime - tiyanjingRecord.CreateTime.Unix()
		// 如果时间差小于等于1天，则设置为已领取
		if timeDiff <= 86400 {
			receivedStatus = 1
		} else {
			receivedStatus = 2
		}
		ctx.Put("TgTyjCurrency", tiyanjingRecord.Symbol)
		ctx.Put("TgTyjAmount", tiyanjingRecord.Amount)
	}
	// 检查用户是否是新手保护用户
	userProtection, err := userbrand.UserProtectionSvc.CheckUserProtectionStatus(server.Db().GormDao(), token.UserId)
	if err != nil {
		logs.Error("检查用户保护状态失败 userId=", token.UserId, " err=", err.Error())

	} else {
		// 记录新手保护用户登录日志
		if userProtection.IsProtected {
			logs.Info("新手保护用户登录 userId=", token.UserId,
				" tiYanJingTotal=", userProtection.TiYanJingTotal,
				" allowedGameBrand=", userProtection.AllowedGameBrand, " userProtection=", userProtection)
		}
	}
	ctx.Put("UserProtection", userProtection)
	ctx.Put("ReceivedStatus", receivedStatus)

	ctx.RespOK()
}

func (c *UserController) get_recharge_address(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := struct {
		SellerId int `validate:"required"`
		Chain    string
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if reqdata.Chain == "trx" || reqdata.Chain == "eth" || reqdata.Chain == "bsc" || reqdata.Chain == "polygon" {
		presult, _ := server.Db().Query(`select RechargeAddressTron, RechargeAddressEth from x_user where UserId = ?`, []interface{}{token.UserId})
		if reqdata.Chain == "trx" {
			RechargeAddressTron := ""
			if (*presult)[0]["RechargeAddressTron"] != nil {
				RechargeAddressTron = (*presult)[0]["RechargeAddressTron"].(string)
			}
			if len(RechargeAddressTron) == 0 {
				for {
					sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 1 limit 1"
					server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressTron)
					if len(RechargeAddressTron) == 0 {
						break
					}
					sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 1"
					r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressTron)
					RowsAffected, _ := r.RowsAffected()
					if RowsAffected == 1 {
						sql = "update x_user set RechargeAddressTron = ? where UserId = ?"
						server.Db().Conn().Exec(sql, RechargeAddressTron, token.UserId)
						break
					}
				}
			}
			ctx.RespOK(RechargeAddressTron)
		} else if reqdata.Chain == "eth" || reqdata.Chain == "bsc" || reqdata.Chain == "polygon" {
			RechargeAddressEth := ""
			if (*presult)[0]["RechargeAddressEth"] != nil {
				RechargeAddressEth = (*presult)[0]["RechargeAddressEth"].(string)
			}

			if len(RechargeAddressEth) == 0 {
				for {
					sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 2 limit 1"
					server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressEth)
					if len(RechargeAddressEth) == 0 {
						break
					}
					sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 2"
					r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressEth)
					RowsAffected, _ := r.RowsAffected()
					if RowsAffected == 1 {
						sql = "update x_user set RechargeAddressEth = ? where UserId = ?"
						server.Db().Conn().Exec(sql, RechargeAddressEth, token.UserId)
						break
					}
				}
			}
			ctx.RespOK(RechargeAddressEth)
		}
	} else {
		chainmap := map[string]int{
			"btc":  3,
			"doge": 4,
			"xrp":  5,
			"ltc":  6,
			"eos":  7,
			"sol":  8,
			"sui":  9,
			"bch":  10,
			"ton":  11,
		}
		chainid, ok := chainmap[reqdata.Chain]
		if !ok {
			ctx.RespErrString(true, &errcode, "该链不支持充值")
			return
		}
		for {
			presult, _ := server.Db().Query(`select Address from x_user_recharge_address where Chain = ? and UserId = ? and OrderType = ?`, []interface{}{reqdata.Chain, token.UserId, utils.RechargeType})
			if len(*presult) > 0 {
				a := abugo.InterfaceToString((*presult)[0]["Address"])
				if reqdata.Chain == "eos" {
					if xgo.Env() == "dev" {
						ctx.RespOK("test_dev," + a)
					} else {
						ctx.RespOK("test_prd," + a)
					}
				} else if reqdata.Chain == "xrp" {
					if xgo.Env() == "dev" {
						ctx.RespOK("test_dev," + a)
					} else {
						ctx.RespOK("test_prd," + a)
					}
				} else if reqdata.Chain == "ton" {
					// 获取冷钱包地址
					financeSymbolTb := server.DaoxHashGame().XFinanceSymbol
					financeSymbolDb := server.DaoxHashGame().XFinanceSymbol.WithContext(context.Background())
					symbol, err := financeSymbolDb.Select(financeSymbolTb.ColdWallet).
						Where(financeSymbolTb.SellerID.Eq(int32(token.SellerId))).
						Where(financeSymbolTb.Symbol.Eq(strings.ToUpper(reqdata.Chain))).First()
					if err != nil {
						ctx.RespErrString(true, &errcode, "获取地址失败")
						break
					}
					ctx.RespOK(symbol.ColdWallet + "," + a)
				} else {
					ctx.RespOK(a)
				}
				break
			}
			var Address string
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = ? limit 1"
			server.Db().QueryScan(sql, []interface{}{token.SellerId, chainid}, &Address)
			if len(Address) == 0 {
				ctx.RespErrString(true, &errcode, "获取地址失败")
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = ?"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, Address, chainid)
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "insert into x_user_recharge_address(UserId,Chain,Address) values(?,?,?)"
				server.Db().Conn().Exec(sql, token.UserId, reqdata.Chain, Address)
			}
		}
	}
}

func (c *UserController) get_recharge_address_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := struct {
		SellerId int `validate:"required"`
		Chain    string
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if reqdata.Chain == "trx" || reqdata.Chain == "eth" || reqdata.Chain == "bsc" || reqdata.Chain == "polygon" {
		presult, _ := server.Db().Query(`select RechargeAddressTron, RechargeAddressEth from x_user where UserId = ?`, []interface{}{token.UserId})
		if reqdata.Chain == "trx" {
			RechargeAddressTron := ""
			if (*presult)[0]["RechargeAddressTron"] != nil {
				RechargeAddressTron = (*presult)[0]["RechargeAddressTron"].(string)
			}
			if len(RechargeAddressTron) == 0 {
				for {
					sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 1 limit 1"
					server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressTron)
					if len(RechargeAddressTron) == 0 {
						break
					}
					sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 1"
					r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressTron)
					RowsAffected, _ := r.RowsAffected()
					if RowsAffected == 1 {
						sql = "update x_user set RechargeAddressTron = ? where UserId = ?"
						server.Db().Conn().Exec(sql, RechargeAddressTron, token.UserId)
						break
					}
				}
			}
			ctx.Put("address", RechargeAddressTron)
			ctx.Put("memo", "")
			ctx.RespOK()
		} else if reqdata.Chain == "eth" || reqdata.Chain == "bsc" || reqdata.Chain == "polygon" {
			RechargeAddressEth := ""
			if (*presult)[0]["RechargeAddressEth"] != nil {
				RechargeAddressEth = (*presult)[0]["RechargeAddressEth"].(string)
			}

			if len(RechargeAddressEth) == 0 {
				for {
					sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 2 limit 1"
					server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressEth)
					if len(RechargeAddressEth) == 0 {
						break
					}
					sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 2"
					r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressEth)
					RowsAffected, _ := r.RowsAffected()
					if RowsAffected == 1 {
						sql = "update x_user set RechargeAddressEth = ? where UserId = ?"
						server.Db().Conn().Exec(sql, RechargeAddressEth, token.UserId)
						break
					}
				}
			}
			ctx.Put("address", RechargeAddressEth)
			ctx.Put("memo", "")
			ctx.RespOK()
		}
	} else {
		chainmap := map[string]int{
			"btc":  3,
			"doge": 4,
			"xrp":  5,
			"ltc":  6,
			"eos":  7,
			"sol":  8,
			"sui":  9,
			"bch":  10,
			"ton":  11,
		}
		chainid, ok := chainmap[reqdata.Chain]
		if !ok {
			ctx.RespErrString(true, &errcode, "该链不支持充值")
			return
		}
		for {
			presult, _ := server.Db().Query(`select Address from x_user_recharge_address where Chain = ? and UserId = ? and OrderType = ?`, []interface{}{reqdata.Chain, token.UserId, utils.RechargeType})
			if len(*presult) > 0 {
				a := abugo.InterfaceToString((*presult)[0]["Address"])
				if reqdata.Chain == "eos" {
					if xgo.Env() == "dev" {
						ctx.Put("address", "test_dev,"+a)
						ctx.Put("memo", "")
						ctx.RespOK()
					} else {
						ctx.Put("address", "test_prd,"+a)
						ctx.Put("memo", "")
						ctx.RespOK()
					}
				} else if reqdata.Chain == "xrp" {
					if xgo.Env() == "dev" {
						ctx.Put("address", "test_dev,"+a)
						ctx.Put("memo", "")
						ctx.RespOK()
					} else {
						ctx.Put("address", "test_prd,"+a)
						ctx.Put("memo", "")
						ctx.RespOK()
					}
				} else if reqdata.Chain == "ton" {
					// 获取冷钱包地址
					financeSymbolTb := server.DaoxHashGame().XFinanceSymbol
					financeSymbolDb := server.DaoxHashGame().XFinanceSymbol.WithContext(context.Background())
					symbol, err := financeSymbolDb.Select(financeSymbolTb.ColdWallet).
						Where(financeSymbolTb.SellerID.Eq(int32(token.SellerId))).
						Where(financeSymbolTb.Symbol.Eq(strings.ToUpper(reqdata.Chain))).First()
					if err != nil {
						ctx.RespErrString(true, &errcode, "获取地址失败")
						break
					}
					ctx.Put("address", symbol.ColdWallet)
					ctx.Put("memo", a)
					ctx.RespOK()
				} else {
					ctx.Put("address", a)
					ctx.Put("memo", "")
					ctx.RespOK()
				}
				break
			}
			var Address string
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = ? limit 1"
			logs.Info("get_recharge_address_v2 sql:", sql, token.SellerId, chainid)
			server.Db().QueryScan(sql, []interface{}{token.SellerId, chainid}, &Address)
			if len(Address) == 0 {
				ctx.RespErrString(true, &errcode, "获取地址失败")
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = ?"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, Address, chainid)
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "insert into x_user_recharge_address(UserId,Chain,Address) values(?,?,?)"
				server.Db().Conn().Exec(sql, token.UserId, reqdata.Chain, Address)
			}
		}
	}
}

func (c *UserController) get_recharge_address_injection(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := struct {
		SellerId int `validate:"required"`
		Chain    string
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	chainmap := map[string]int{
		"trx":     1,
		"eth":     2,
		"bsc":     2,
		"polygon": 2,
		"btc":     3,
		"doge":    4,
		"xrp":     5,
		"ltc":     6,
		"eos":     7,
		"sol":     8,
		"sui":     9,
		"bch":     10,
		"ton":     11,
	}
	chainid, ok := chainmap[reqdata.Chain]
	if !ok {
		ctx.RespErrString(true, &errcode, "该链不支持充值")
		return
	}
	for {
		presult, _ := server.Db().Query(`select Address from x_user_recharge_address where Chain = ? and UserId = ? and OrderType = ?`, []interface{}{reqdata.Chain, token.UserId, utils.RechargeShangZhuangType})
		if len(*presult) > 0 {
			a := abugo.InterfaceToString((*presult)[0]["Address"])
			if reqdata.Chain == "eos" {
				if xgo.Env() == "dev" {
					ctx.Put("address", "test_dev,"+a)
					ctx.Put("memo", "")
					ctx.RespOK()
				} else {
					ctx.Put("address", "test_prd,"+a)
					ctx.Put("memo", "")
					ctx.RespOK()
				}
			} else if reqdata.Chain == "xrp" {
				if xgo.Env() == "dev" {
					ctx.Put("address", "test_dev,"+a)
					ctx.Put("memo", "")
					ctx.RespOK()
				} else {
					ctx.Put("address", "test_prd,"+a)
					ctx.Put("memo", "")
					ctx.RespOK()
				}
			} else if reqdata.Chain == "ton" {
				// 获取冷钱包地址
				financeSymbolTb := server.DaoxHashGame().XFinanceSymbol
				financeSymbolDb := server.DaoxHashGame().XFinanceSymbol.WithContext(context.Background())
				symbol, err := financeSymbolDb.Select(financeSymbolTb.ColdWallet).
					Where(financeSymbolTb.SellerID.Eq(int32(token.SellerId))).
					Where(financeSymbolTb.Symbol.Eq(strings.ToUpper(reqdata.Chain))).First()
				if err != nil {
					ctx.RespErrString(true, &errcode, "获取地址失败")
					break
				}
				ctx.Put("address", symbol.ColdWallet)
				ctx.Put("memo", a)
				ctx.RespOK()
			} else {
				ctx.Put("address", a)
				ctx.Put("memo", "")
				ctx.RespOK()
			}
			break
		}
		var Address string
		sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = ? limit 1"
		server.Db().QueryScan(sql, []interface{}{token.SellerId, chainid}, &Address)
		if len(Address) == 0 {
			ctx.RespErrString(true, &errcode, "获取地址失败")
			break
		}
		sql = "update x_address_pool set UserId = ?, OrderType = ? where Address = ? and UserId is null and AddType = ?"
		r, _ := server.Db().Conn().Exec(sql, token.UserId, utils.RechargeShangZhuangType, Address, chainid)
		RowsAffected, _ := r.RowsAffected()
		if RowsAffected == 1 {
			sql = "insert into x_user_recharge_address(UserId,Chain,Address,OrderType) values(?,?,?,?)"
			server.Db().Conn().Exec(sql, token.UserId, reqdata.Chain, Address, utils.RechargeShangZhuangType)
		}
	}
}

func (c *UserController) address_verify(ctx *abugo.AbuHttpContent) {
	defer recover()
	token := server.GetToken(ctx)
	if token.ChannelId == 0 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, 0)
		userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
		if userdata != nil {
			token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
		}
	}
	where := abugo.AbuDbWhere{}
	where.Add("And", "SellerId", "=", token.SellerId, 0)
	where.Add("And", "ChannelId", "=", token.ChannelId, 0)
	where.Add("And", "AddrType", "=", 2, 0)
	addressdata, _ := server.Db().Table("x_recharge_address").Select("Address").Where(where).GetOne()
	if addressdata != nil {
		ctx.Put("Address", abugo.GetStringFromInterface((*addressdata)["Address"]))
	}
	where = abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	verifydata, _ := server.Db().Table("x_address_verify").Select("Amount,TIME_TO_SEC(TIMEDIFF(date_add(CreateTime,interval 30 minute),now())) as LeftTime").Where(where).GetOne()
	if verifydata == nil {
		for {
			Amount := math.Floor(mrand.Float64() * 1000000)
			_, err := server.Db().Conn().Exec("INSERT INTO x_address_verify(UserId,Amount) VALUES(?,?)", token.UserId, Amount)
			if err == nil {
				ctx.Put("Amount", Amount)
				ctx.Put("LeftTime", 1800)
				break
			}
			fmt.Println(Amount)
		}
	} else {
		ctx.Put("Amount", abugo.GetInt64FromInterface((*verifydata)["Amount"]))
		ctx.Put("LeftTime", abugo.GetInt64FromInterface((*verifydata)["LeftTime"]))
	}
	ctx.RespOK()
}

func (c *UserController) delete_address(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Address string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	server.Db().Conn().Exec("delete from x_verified_address where userid = ? and address = ?", token.UserId, reqdata.Address)
	ctx.RespOK()
}

func (c *UserController) withdraw(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Net      string  `validate:"required"`
		Address  string  `validate:"required"`
		Amount   float64 `validate:"required"`
		Password string
		Symbol   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if reqdata.Amount < 0 {
		return
	}

	reqdata.Password = utils.Md5V(reqdata.Password)
	token := server.GetToken(ctx)

	rkey := fmt.Sprintf("%v:%v:user_withdraw:%v", server.Project(), server.Module(), token.UserId)
	loc := server.Redis().SetNxString(rkey, "1", 10)
	if ctx.RespErrString(loc != nil, &errcode, "操作频繁,请稍后再试") {
		return
	}
	if reqdata.Symbol == "" {
		reqdata.Symbol = "usdt"
	}
	//data, err := server.Db().CallProcedure("x_admin_withdraw", token.UserId, reqdata.Net, reqdata.Address, reqdata.Amount, reqdata.Password, reqdata.Symbol)
	data, err := server.Db().CallProcedure("x_admin_withdraw", token.UserId, reqdata.Net, reqdata.Address, reqdata.Amount, reqdata.Password)
	if err != nil {
		logs.Debug("x_admin_withdraw error:", err.Error())
	}
	dberr := abugo.GetDbError(data)
	if dberr.ErrCode != 200 {
		if ctx.RespErrString(true, &errcode, dberr.ErrMsg) {
			return
		}
	}
	if (*data)["Id"] != nil {
		env := "测试服,"
		if !server.Debug() {
			env = "正式服,"
		}
		msg := fmt.Sprintf("%v新的提现订单,请立即审核\n编号: %v\n金额: %v\n时间: %v", env, (*data)["Id"], (*data)["Amount"], (*data)["NowTime"])
		req.Post(viper.GetString("tgbotapi")+"/sendmsg", msg)
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}

func (c *UserController) withdraw_v2(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Net      string  `validate:"required"`
		Address  string  `validate:"required"`
		Amount   float64 `validate:"required"`
		Password string
		Symbol   string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if reqdata.Amount < 0 {
		return
	}

	if reqdata.Symbol == "" {
		reqdata.Symbol = "usdt"
	}

	if reqdata.Net == "tron" {
		if len(reqdata.Address) != 34 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		if strings.Index(reqdata.Address, "T") != 0 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
	}

	if reqdata.Net == "eth" {
		if len(reqdata.Address) != 42 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		if strings.Index(reqdata.Address, "0x") != 0 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
	}

	if reqdata.Net == "btc" && !invaildAddress.InvalidBTCAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "ton" && !invaildAddress.InvalidTONAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "sol" && !invaildAddress.InvalidSolanaAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "bsc" && !invaildAddress.InvalidBSCAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	reqdata.Password = utils.Md5V(reqdata.Password)

	// 检查用户余额是否被冻结
	frozen, message := common.CheckUserFrozen(token.UserId)
	if frozen {
		logs.Error("提现失败  玩家账号余额被冻结 userId=", token.UserId, " message=", message)
		//errcode = 201
		ctx.RespErrString(true, &errcode, "当前账号暂时无法提款，请联系客服")
		return
	}

	// 检查用户是否参与活动以及活动流水是否达到要求
	activityCheck, err := active.CheckActivityLiuSui(ctx, token.UserId)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	// 检查用户是否达到提现条件兑换码活动
	isWidthdrawable, withdrawMessage, _ := active.IsWidthdrawableActive(token.UserId, utils.RedeemCodeGift_1003)
	if !isWidthdrawable {
		ctx.RespErr(errors.New(withdrawMessage), &errcode)
		return
	}
	// 检查用户是否满足活动流水要求
	if !activityCheck.Pass {
		ctx.RespErr(errors.New(activityCheck.Message), &errcode)
		return
	}
	// 检查用户是否满足首充活动流水要求
	isPass, message, diff := active.CheckActiveWithdrawable(int32(token.UserId), utils.FirstDepositGift)
	if !isPass {
		logs.Info("首充活动流水检查失败: 用户ID=%d, 错误信息=%s, 流水差额=%s", token.UserId, message, diff.StringFixed(2))
		ctx.RespErr(errors.New(message), &errcode)
		return
	}

	// 检查用户是否满足复充活动流水要求
	isPass, message, diff = active.CheckActiveWithdrawable(int32(token.UserId), utils.MultipleDepositGift)
	if !isPass {
		logs.Info("复充活动流水检查失败: 用户ID=%d, 错误信息=%s, 流水差额=%s", token.UserId, message, diff.StringFixed(2))
		ctx.RespErr(errors.New(message), &errcode)
		return
	}

	// 检查用户是否设置了提现密码
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	userTb := server.DaoxHashGame().XUser
	user, err := userDb.Select(userTb.WalletPassword).Where(userTb.UserID.Eq(int32(token.UserId))).Take()
	if err != nil {
		logs.Error("withdraw_v2 查询用户提现密码失败: UserId=%d, err=%v", token.UserId, err)
		ctx.RespErr(err, &errcode)
		return
	}
	if user.WalletPassword == "" {
		ctx.RespErr(errors.New("请先设置提现密码"), &errcode)
		return
	}

	// 验证提现密码
	if user.WalletPassword != reqdata.Password {
		ctx.RespErr(errors.New("提现密码不正确"), &errcode)
		return
	}

	rkey := fmt.Sprintf("%v:%v:user_withdraw:%v", server.Project(), server.Module(), token.UserId)
	loc := server.Redis().SetNxString(rkey, "1", 10)
	if ctx.RespErrString(loc != nil, &errcode, "操作频繁,请稍后再试") {
		return
	}

	ChannelId, _ := server.GetChannel(ctx, ctx.Host())

	data, err := server.Db().CallProcedure("x_admin_withdraw_v3", token.UserId, ChannelId, reqdata.Net, reqdata.Address, reqdata.Amount, reqdata.Password, reqdata.Symbol)
	if err != nil {
		logs.Debug("x_admin_withdraw error:", err.Error())
	}
	dberr := abugo.GetDbError(data)
	if dberr.ErrCode != 200 {
		if ctx.RespErrString(true, &errcode, dberr.ErrMsg) {
			return
		}
	}
	if (*data)["Id"] != nil {
		env := "测试服,"
		if !server.Debug() {
			env = "正式服,"
		}
		msg := fmt.Sprintf("%v新的提现订单,请立即审核\n编号: %v\n金额: %v\n时间: %v", env, (*data)["Id"], (*data)["Amount"], (*data)["NowTime"])
		tgApiUrl := viper.GetString("tgbotapi") + "/sendmsg"
		logs.Info("withdraw_v2 tgbotapi url: %v msg: %v", tgApiUrl, msg)
		_, err := req.Post(tgApiUrl, msg)
		if err != nil {
			logs.Error(tgApiUrl, "err: ", err)
		}
		// 上报提现创建事件到 ThinkingData
		datapush.SendWithdrawCreateEvent(token, reqdata, *data)
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}

func (c *UserController) withdrawlist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page     int
		PageSize int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	total, result := server.Db().Table("x_withdraw").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", *result)
	ctx.RespOK()
}

func (c *UserController) rechargelist(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		Page      int
		PageSize  int
		StartTime int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	if reqdata.StartTime > 0 {
		where.Add("and", "CreateTime", ">", carbon.CreateFromTimestamp(int64(reqdata.StartTime)).String(), "")
	}
	total, result := server.Db().Table("x_recharge").Where(where).OrderBy("id desc").PageData(reqdata.Page, reqdata.PageSize)
	ctx.Put("total", total)
	ctx.Put("data", *result)
	ctx.RespOK()
}

func (c *UserController) bind_address(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, 0)
	result, _ := server.Db().Table("x_verified_address").Where(where).GetList()
	ctx.Put("data", *result)
	ctx.RespOK()
}

func (c *UserController) recharge_address(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	if token.ChannelId == 0 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, 0)
		userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
		if userdata != nil {
			token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
		}
	}
	presult, _ := server.Db().Query("select * from x_recharge_address where sellerid = ? and ChannelId = ? and addrtype = 1", []interface{}{token.SellerId, token.ChannelId})
	ctx.Put("Address", abugo.GetStringFromInterface((*presult)[0]["Address"]))
	ctx.RespOK()
}

func (c *UserController) game_peilv(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	presult, _ := server.Db().Query("select GameFee from x_user where UserId = ?", []interface{}{token.UserId})
	ctx.Put("GameFee", abugo.GetStringFromInterface((*presult)[0]["GameFee"]))
	ctx.RespOK()
}

func (c *UserController) get_amount(ctx *abugo.AbuHttpContent) {
	SessionId := ctx.Query("SessionId")
	if len(SessionId) > 0 {
		UserId := abugo.GetInt64FromInterface(SessionId)
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", UserId, 0)
		data, _ := server.Db().Table("x_user").Select("Amount").Where(where).GetOne()
		amount := 0.0
		if data != nil {
			amount = abugo.GetFloat64FromInterface((*data)["Amount"])
		}
		ctx.RespJson(gin.H{"code": 200, "message": "成功", "balanceInfo": gin.H{
			"balance":     amount,
			"dayRecharge": 0,
			"dayBetting":  0,
		}})
	}
}

func (c *UserController) get_withdraw_address(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	presult, _ := server.Db().Query("select * from x_withdraw_address where UserId = ? order by CreateTime desc", []interface{}{token.UserId})
	ctx.RespOK(presult)
}

func (c *UserController) get_user_all_address(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	presult, _ := server.Db().Query("select * from (SELECT Address,UserId,State,Net,Symbol FROM `x_withdraw_address` where userId=? UNION select Address,UserId,state,Net,'usdt' as Symbol from x_user_wallet where State=2 and userId=?) a GROUP BY a.Address", []interface{}{token.UserId, token.UserId})
	for i, _ := range *presult {
		if (*presult)[i]["Net"] != "tron" && (*presult)[i]["Net"] != "bsc" {
			(*presult)[i]["State"] = 3
		}
	}

	ctx.RespOK(presult)
}

func (c *UserController) add_withdraw_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Net     string `validate:"required"`
		Address string `validate:"required"`
		Symbol  string
		Memo    string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if reqdata.Symbol == "" {
		reqdata.Symbol = "usdt"
	}

	if reqdata.Net == "tron" {
		if len(reqdata.Address) != 34 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		if strings.Index(reqdata.Address, "T") != 0 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
	}

	if reqdata.Net == "eth" {
		if len(reqdata.Address) != 42 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		if strings.Index(reqdata.Address, "0x") != 0 {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
	}

	if reqdata.Net == "btc" && !invaildAddress.InvalidBTCAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "sol" && !invaildAddress.InvalidSolanaAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "ton" && !invaildAddress.InvalidTONAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	if reqdata.Net == "bsc" && !invaildAddress.InvalidBSCAddress(reqdata.Address) {
		ctx.RespErrString(true, &errcode, "地址不正确")
		return
	}

	gameDao := server.DaoxHashGame().XGame
	count, _ := gameDao.WithContext(context.Background()).Where(gameDao.Address.Eq(reqdata.Address)).Count()
	if count > 0 {
		ctx.RespErrString(true, &errcode, "地址已存在,请更换地址")
		return
	}

	token := server.GetToken(ctx)
	_, err = server.Db().Conn().Exec("insert into x_withdraw_address(Net,UserId,Address,Memo,Symbol)values(?,?,?,?,?)", reqdata.Net, token.UserId, reqdata.Address, reqdata.Memo, reqdata.Symbol)
	if err != nil {
		fmt.Println(err)
		if strings.Index(err.Error(), "Duplicate entry") > 0 {
			ctx.RespErrString(err != nil, &errcode, "添加失败,该地址已添加")
			return
		}
		logs.Debug("add_withdraw_address:", err)
		ctx.RespErrString(true, &errcode, "添加失败,请稍后再试")
		return
	}

	if reqdata.Net == "tron" {
		body := struct {
			Address string `json:"address"`
		}{
			Address: reqdata.Address,
		}
		logs.Info("url: ", tronscangoapi+"/api/Address_Insert")
		logs.Info("data: ", req.BodyJSON(body))
		res, err := req.Post(tronscangoapi+"/api/Address_Insert", req.BodyJSON(body))
		if err != nil {
			logs.Error("tronscangoapi 添加目标地址失败：address: %v err : %v", reqdata.Address, err)
		}
		logs.Info("tronscangoapi res: %v", res)
	}

	ctx.RespOK()
}

func getWithdrawLimitConfig(SellerId, ChannelId, userId int32) (config *model2.XWithdrawLimitConfig, err error) {
	ctx := context.Background()
	if err != nil {
		return nil, err
	}
	// 配置优先级 个人>代理>VIP>全局
	tb := server.DaoxHashGame().XWithdrawLimitConfig
	// 个人
	config, err = tb.WithContext(ctx).Where(tb.SellerID.Eq(SellerId)).Where(tb.ChannelID.Eq(ChannelId)).
		Where(tb.Status.Eq(1)).Where(tb.Type.Eq(4)).Where(tb.UserID.Eq(userId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err2:", err)
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	// 代理
	var agentIds []int32
	xAgentChild := server.DaoxHashGame().XAgentChild
	_ = xAgentChild.WithContext(ctx).Where(xAgentChild.Child.Eq(userId)).Order(xAgentChild.ChildLevel).Pluck(xAgentChild.UserID, &agentIds)
	agentIds = append([]int32{userId}, agentIds...)
	var agentIdList []string
	for _, v := range agentIds {
		agentIdList = append(agentIdList, strconv.Itoa(int(v)))
	}
	agentIdsStr := strings.Join(agentIdList, ",")
	sql := "SELECT * FROM x_withdraw_limit_config WHERE SellerId=? AND ChannelId=? AND `Status`=1 AND `Type`=3 AND UserId IN (?) ORDER BY FIELD(UserId, %s) LIMIT 1"
	sql = fmt.Sprintf(sql, agentIdsStr)
	err = server.Db().GormDao().Raw(sql, SellerId, ChannelId, agentIds).Scan(&config).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err4:", err)
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	// VIP
	var userVipLv int32
	xVipInfo := server.DaoxHashGame().XVipInfo
	vipData, _ := xVipInfo.WithContext(ctx).Where(xVipInfo.UserID.Eq(userId)).First()
	if vipData != nil {
		userVipLv = vipData.VipLevel
	}
	config, err = tb.WithContext(ctx).Where(tb.SellerID.Eq(SellerId)).Where(tb.ChannelID.Eq(ChannelId)).
		Where(tb.Status.Eq(1)).Where(tb.Type.Eq(2)).Where(tb.VipLevel.Eq(userVipLv)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err4:", err)
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	// 渠道
	config, err = tb.WithContext(ctx).Where(tb.SellerID.Eq(SellerId)).Where(tb.ChannelID.Eq(ChannelId)).
		Where(tb.Status.Eq(1)).Where(tb.Type.Eq(1)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err5:", err)
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	// 运营商
	config, err = tb.WithContext(ctx).Where(tb.SellerID.Eq(SellerId)).Where(tb.ChannelID.Eq(0)).
		Where(tb.Type.Eq(1)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err6:", err)
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	// 全部运营商和全部渠道
	config, err = tb.WithContext(ctx).Where(tb.SellerID.Eq(0)).Where(tb.ChannelID.Eq(0)).
		Where(tb.Type.Eq(1)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("getWithdrawLimitConfig err7:", err)
		return nil, err
	}
	return config, err
}

func getTodayWithdrawData(userId int32) (amount float64, count int32) {
	now := time.Now()
	tomorrow := now.Add(time.Hour * 24)
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, now.Location())
	xWithdraw := server.DaoxHashGame().XWithdraw
	db := xWithdraw.WithContext(context.Background())
	// if symbol != "" {
	// 	db = db.Where(xWithdraw.Symbol.Eq(symbol))
	// }
	withdrawList, err := db.Where(xWithdraw.UserID.Eq(userId)).
		Where(xWithdraw.CreateTime.Between(startTime, endTime)).
		Where(xWithdraw.State.In(0, 2, 4, 5, 6)).
		Find()
	if err == nil {
		for _, v := range withdrawList {
			amount += v.RealAmount
		}
		count = int32(len(withdrawList))
	}
	return amount, count
}

func (c *UserController) get_withdraw_config(ctx *abugo.AbuHttpContent) {
	var err error
	errcode := 0
	token := server.GetToken(ctx)
	userId := int32(token.UserId)

	var config *model2.XWithdrawLimitConfig
	config, err = getWithdrawLimitConfig(int32(token.SellerId), int32(token.ChannelId), userId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.Error("get_withdraw_config err:", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if config == nil {
		config = &model2.XWithdrawLimitConfig{
			MaxAmountPerDay: -1.0,
			MaxCountPerDay:  -1,
		}
	}
	ctx.Put("MaxAmountPerDay", config.MaxAmountPerDay)
	ctx.Put("MaxCountPerDay", config.MaxCountPerDay)
	ctx.Put("FeeFreeQuotaPerDay", config.FeeFreeQuotaPerDay)
	ctx.Put("FeeFreeCountPerDay", config.FeeFreeCountPerDay)
	ctx.Put("FeePercent", config.FeePercent)
	ctx.Put("MaxAmountEveryTime", config.MaxAmountEveryTime)

	todayWithdrawAmount, todayWithdrawCount := getTodayWithdrawData(userId)
	ctx.Put("TodayWithdrawAmount", todayWithdrawAmount)
	ctx.Put("TodayWithdrawCount", todayWithdrawCount)
	ctx.RespOK()
}

// get_withdraw_limit_info 获取用户提现限制信息
func (c *UserController) get_withdraw_limit_info(ctx *abugo.AbuHttpContent) {
	errcode := 0

	// 获取用户token
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}

	// 获取用户提现限制信息
	limitInfo, err := userbrand.UserProtectionSvc.GetWithdrawLimitInfo(server.Db().GormDao(), token.UserId)
	if err != nil {
		logs.Error("获取用户提现限制信息失败 userId=", token.UserId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误，请稍后再试")
		return
	}

	// 记录日志
	logs.Info("获取用户提现限制信息 userId=", token.UserId,
		" isProtected=", limitInfo.IsProtected,
		" isLimited=", limitInfo.IsWithdrawLimited,
		" maxAmount=", limitInfo.MaxWithdrawAmount)

	// 返回结果
	ctx.RespOK(limitInfo)
}

func (c *UserController) delete_wallet_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Net     string `validate:"required"`
		Address string `validate:"required"`
		// Type    int    `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	go func() {
		server.Db().Query("delete from x_withdraw_address where UserId = ? and Net = ? and address = ? and State = 2", []interface{}{token.UserId, reqdata.Net, reqdata.Address})

	}()

	go func() {
		server.Db().Query("delete from x_user_wallet where UserId = ? and address = ? and State = 2", []interface{}{token.UserId, reqdata.Address})
	}()

	ctx.RespOK()
}

func (c *UserController) get_wallet_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Net string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	if reqdata.Net == "" {
		reqdata.Net = "tron"
	}

	token := server.GetToken(ctx)
	presult, _ := server.Db().Query("select Address,State,VerifyAmount from x_user_wallet where UserId = ? and Net = ? order by CreateTime desc", []interface{}{token.UserId, reqdata.Net})
	ctx.RespOK(presult)
}

func (c *UserController) add_wallet_address(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address string
		Host    string
		Net     string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)

	xUser := server.DaoxHashGame().XUser
	user, err := xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if user.AccountType == 5 {
		var count int64
		// statAddrDao := server.DaoxHashGame().XUserStatAddress
		// count, _ = statAddrDao.WithContext(ctx.Gin()).Where(statAddrDao.FromAddress.Eq(reqdata.Address)).Count()
		// if count > 0 {
		// 	ctx.RespErrString(true, &errcode, "地址已存在,请更换地址")
		// 	return
		// }
		walletDao := server.DaoxHashGame().XUserWallet
		count, _ = walletDao.WithContext(ctx.Gin()).Where(walletDao.Address.Eq(reqdata.Address)).Count()
		if count > 0 {
			ctx.RespErrString(true, &errcode, "地址已存在,请更换地址")
			return
		}
		withdrawDao := server.DaoxHashGame().XWithdrawAddress
		count, _ = withdrawDao.WithContext(ctx.Gin()).Where(withdrawDao.Address.Eq(reqdata.Address)).Where(withdrawDao.UserID.Neq(int32(token.UserId))).Count()
		if count > 0 {
			ctx.RespErrString(true, &errcode, "地址已存在,请更换地址")
			return
		}
	}

	gameDao := server.DaoxHashGame().XGame
	count, _ := gameDao.WithContext(context.Background()).Where(gameDao.Address.Eq(reqdata.Address)).Count()
	if count > 0 {
		ctx.RespErrString(true, &errcode, "地址已存在,请更换地址")
		return
	}

	sql := "select count(id) as cnt from x_user_wallet where SellerId = ? and Address = ?"
	result, _ := server.Db().Query(sql, []interface{}{token.SellerId, reqdata.Address})
	cnt := abugo.GetInt64FromInterface((*result)[0]["cnt"])
	if cnt > 0 {
		ctx.RespErrString(true, &errcode, "地址已绑定")
		return
	}
	var amount int
	if reqdata.Net == "tron" || reqdata.Net == "" {
		reqdata.Net = "tron"
		// 验证地址
		if !invaildAddress.InvalidTronAddress(reqdata.Address) {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		amount = 88800
	}

	if reqdata.Net == "bsc" {
		// 验证地址
		if !invaildAddress.InvalidBSCAddress(reqdata.Address) {
			ctx.RespErrString(true, &errcode, "地址不正确")
			return
		}
		amount = 10000
	}

	// 特殊处理
	if token.SellerId == 2 {
		token.SellerId = 4
	}

	server.Db().Query("insert into x_user_wallet(SellerId,ChannelId,UserId,Address,VerifyAmount,Net)values(?,?,?,?,?,?)", []interface{}{token.SellerId, token.ChannelId, token.UserId, reqdata.Address, amount, reqdata.Net})

	ctx.RespOK()
}

func (c *UserController) set_wallet_password(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RealName    string
		OldPassword string
		NewPassword string
		Email       string `validate:"required"`
		VerifyCode  string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	reqdata.OldPassword = utils.Md5V(reqdata.OldPassword)
	reqdata.NewPassword = utils.Md5V(reqdata.NewPassword)

	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")
	data, _ := server.Db().Table("x_user").Select("Password,WalletPassword,Email").Where(where).GetOne()

	Password := abugo.GetStringFromInterface((*data)["Password"])
	WalletPassword := ""
	if reqdata.NewPassword == Password {
		ctx.RespErrString(true, &errcode, "提现密码不可与登录密码相同")
		return
	}
	WalletPassword = abugo.GetStringFromInterface((*data)["WalletPassword"])
	if len(WalletPassword) > 0 && WalletPassword != reqdata.OldPassword {
		ctx.RespErrString(true, &errcode, "密码不正确")
		return
	}
	if len(WalletPassword) == 0 && Password != reqdata.OldPassword {
		ctx.RespErrString(true, &errcode, "密码不正确")
		return
	}

	email := abugo.GetStringFromInterface((*data)["Email"])
	if len(email) < 3 {
		err := c.bind_Email(ctx, &BindEmailRequestData{
			Email:      reqdata.Email,
			VerifyCode: reqdata.VerifyCode,
		})
		if ctx.RespErr(err, &errcode) {
			return
		}
	} else {
		daoV := server.DaoxHashGame().XVerify
		dbV := daoV.WithContext(ctx.Gin())
		r, _ := dbV.Where(daoV.Account.Eq(email)).
			Where(daoV.UseType.Eq(3)).Where(daoV.VerifyCode.Eq(reqdata.VerifyCode)).Delete()
		logs.Info("pp delete ", email, reqdata.VerifyCode)
		if r.RowsAffected == 0 {
			ctx.RespErrString(true, &errcode, "验证码不正确")
			return
		}
	}

	if len(WalletPassword) > 0 {
		server.Db().Conn().Exec("update x_user set WalletPassword = ?,UpdateWalletPasswordTime = now() where UserId = ?", reqdata.NewPassword, token.UserId)
	} else {
		server.Db().Conn().Exec("update x_user set WalletPassword = ?,RealName = ?,UpdateWalletPasswordTime = now() where UserId = ?", reqdata.NewPassword, reqdata.RealName, token.UserId)
	}

	//创建消息发送服务实例
	messageService := msg.NewSendMessageAPIService()
	// 修改资金密码通知
	e := messageService.SendMessage(msg.MsgTypeFundPasswordChanged, token.UserId, nil)
	if e != nil {
		logs.Error("发送站内短信失败: ", " userId=", token.UserId, " err=", e)
	}

	ctx.RespOK()
}

func (c *UserController) set_wallet_password2(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RealName           string
		OldPassword        string
		NewPassword        string
		Email              string
		VerifyCode         string
		VerifyType         int `validate:"required"` // 1:邮箱 2:手机 3:邮箱和手机
		PhoneNum           string
		PhoneNumVerifyCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	reqdata.OldPassword = utils.Md5V(reqdata.OldPassword)
	reqdata.NewPassword = utils.Md5V(reqdata.NewPassword)

	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")
	data, _ := server.Db().Table("x_user").Select("Password,WalletPassword,BindEmail,BindPhoneNum").Where(where).GetOne()

	Password := abugo.GetStringFromInterface((*data)["Password"])
	WalletPassword := ""
	if reqdata.NewPassword == Password {
		ctx.RespErrString(true, &errcode, "提现密码不可与登录密码相同")
		return
	}
	WalletPassword = abugo.GetStringFromInterface((*data)["WalletPassword"])
	if len(WalletPassword) > 0 && WalletPassword != reqdata.OldPassword {
		ctx.RespErrString(true, &errcode, "密码不正确")
		return
	}
	if len(WalletPassword) == 0 && Password != reqdata.OldPassword {
		ctx.RespErrString(true, &errcode, "密码不正确")
		return
	}

	if reqdata.VerifyType <= 0 || reqdata.VerifyType > 3 {
		ctx.RespErrString(true, &errcode, "验证类型错误")
		return
	}

	if reqdata.VerifyType == 1 || reqdata.VerifyType == 3 {
		email := abugo.GetStringFromInterface((*data)["BindEmail"])
		if len(email) < 3 {
			err := c.bind_Email(ctx, &BindEmailRequestData{
				Email:      reqdata.Email,
				VerifyCode: reqdata.VerifyCode,
			})
			if ctx.RespErr(err, &errcode) {
				return
			}
		} else {
			daoV := server.DaoxHashGame().XVerify
			dbV := daoV.WithContext(ctx.Gin())
			r, _ := dbV.Where(daoV.Account.Eq(email)).
				Where(daoV.UseType.Eq(3)).Where(daoV.VerifyCode.Eq(reqdata.VerifyCode)).Delete()
			logs.Info("set_wallet_password delete ", email, reqdata.VerifyCode)
			if r.RowsAffected == 0 {
				ctx.RespErrString(true, &errcode, "邮箱验证码不正确")
				return
			}
		}
	}

	if reqdata.VerifyType == 2 || reqdata.VerifyType == 3 {
		phoneNum := abugo.GetStringFromInterface((*data)["BindPhoneNum"])
		if len(phoneNum) < 3 {
			ctx.RespErrString(true, &errcode, "绑定手机号错误")
			return
		}
		if !c.Sms.VerifyCode(phoneNum, reqdata.PhoneNumVerifyCode) {
			ctx.RespErrString(true, &errcode, "手机验证码不正确")
			return
		}
	}

	if len(WalletPassword) > 0 {
		server.Db().Conn().Exec("update x_user set WalletPassword = ?,UpdateWalletPasswordTime = now() where UserId = ?", reqdata.NewPassword, token.UserId)
	} else {
		server.Db().Conn().Exec("update x_user set WalletPassword = ?,RealName = ?,UpdateWalletPasswordTime = now() where UserId = ?", reqdata.NewPassword, reqdata.RealName, token.UserId)
	}

	//创建消息发送服务实例
	messageService := msg.NewSendMessageAPIService()
	// 修改资金密码通知
	e := messageService.SendMessage(msg.MsgTypeFundPasswordChanged, token.UserId, nil)
	if e != nil {
		logs.Error("发送站内短信失败: ", " userId=", token.UserId, " err=", e)
	}

	ctx.RespOK()
}

func (c *UserController) password_reset_money(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := model.ResetMoneyReq{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	user := model.UserInfo{}
	noHave := server.Db().Gorm().Table(utils.TableUser).
		Where("Account = ?", reqdata.Account).
		First(&user).RecordNotFound()

	//如果没有计数器则加一，大于3次时返回系统异常，防止获取账号信息
	if noHave {
		ctx.RespErr(errors.New("没有该用户"), &errcode)
		return
	}
	switch reqdata.ResetType {
	case utils.SendBySms:
		if user.PhoneNum == "" {
			ctx.RespErr(errors.New("账号没有绑定手机无法重置密码"), &errcode)
			return
		}
		ctx.RespOK()
		//server.BuKa().BuKaSendSms([]string{user.PhoneNum}, "")
		break
	case utils.SendByEmail:
		if user.Email == "" {
			ctx.RespErr(errors.New("账号没有绑定邮箱无法重置密码"), &errcode)
			return
		}
		ctx.RespOK()
		//server.BuKa().BuKaSendCodeEmail([]string{user.Email})
		break
	case utils.SendByTransfer:
		if user.RechargeAddressTron == "" {
			ctx.RespErr(errors.New("账号没有绑定地址无法重置密码"), &errcode)
			return
		}
		if user.BetCount <= 0 {
			ctx.RespErr(errors.New("账号绑定地址无法达到转账重置密码要求"), &errcode)
			return
		}
		//获取随机传递的余额
		err, num := utils.RandomByFloat(utils.ResetPasswordMin, utils.ResetPasswordMax)
		randMoney := decimal.NewFromFloat(num).RoundUp(utils.CurrencyPrecision)
		err = utils.SetStringEx(server.Redis().Redispool(), utils.ResetMoneyKey(ctx.GetIp()), utils.RedisDefaultTimeOut, randMoney.String())
		if err != nil {
			errcode = utils.RedisErrSet
			ctx.RespErr(err, &errcode)
			return
		}

		result, _ := randMoney.Float64()
		ctx.RespOK(model.ResetMoneyRes{
			ResetType:       reqdata.ResetType,
			TransferMoney:   result,
			TransferAddress: user.RechargeAddressTron,
		})
		break
	default:
		ctx.RespErr(errors.New("重置类型错误"), &errcode)
		break
	}
}

func (c *UserController) password_reset(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := model.ResetPasswordReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	//通过account获取redis中的token
	token, err := utils.GetString(server.Redis().Redispool(), "")
	if err != nil {
		ctx.RespErr(errors.New("token已失效，请重新获取token"), &errcode)
		return
	}
	if token != reqdata.Token {
		ctx.RespErr(errors.New("token错误"), &errcode)
		return
	}
	userInfo := model.UserInfo{}
	server.Db().Gorm().Table(utils.TableUser).
		Where("Account = ?", reqdata.Account).
		First(&userInfo)
	if userInfo.Account != reqdata.Account {
		ctx.RespErr(errors.New("账号错误"), &errcode)
		return
	}
	if reqdata.Password != reqdata.RepeatPassword {
		ctx.RespErr(errors.New("两次密码不一致"), &errcode)
		return
	}

	reqdata.Password = utils.Md5V(reqdata.Password)

	server.Db().Gorm().Table(utils.TableUser).Where("Id = ?", userInfo.Id).
		Update(utils.NewAnyMap().AppendKStr("Password", reqdata.Password).GetStrMap())
	//重置密码，推送消息到CustomerIo客服系统
	c.PushCustomEvent(fmt.Sprint(userInfo.UserId), "password_recovery", map[string]interface{}{
		"account_id": userInfo.UserId,
		"email":      userInfo.Email,
		"phone":      userInfo.PhoneNum,
		"type":       "faild_password_recovery",
	})
	ctx.RespOK()
}

func (c *UserController) password_reset_token(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	reqdata := model.PasswordTokenReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	user := model.UserInfo{}
	noHave := server.Db().Gorm().Table(utils.TableUser).
		Where("Account = ?", reqdata.Account).
		First(&user).RecordNotFound()
	if noHave {

		ctx.RespErr(errors.New("没有该用户"), &errcode)
		return
	}

	//如果没有计数器则加一，大于3次时返回系统异常，防止获取账号信息
	count, err := utils.IncrByEx(server.Redis().Redispool(), fmt.Sprintf("%s", utils.RedisKPTokenIncr), 1, utils.ResetTokenTimeOut)
	if count > 3 {
		ctx.RespErr(errors.New("多次重试"), &errcode)
		return
	}

	err, num := utils.RandomByInt(utils.ResetTokenMin, utils.ResetTokenMax)
	switch reqdata.ResetType {
	case utils.SendBySms:
		if user.PhoneNum == "" {
			ctx.RespErr(errors.New("账号没有绑定手机无法重置密码"), &errcode)
			return
		}
		if user.PhoneNum != reqdata.Phone {
			ctx.RespErr(errors.New("请输入已绑定的手机号"), &errcode)
			return
		}
		utils.SetStringEx(server.Redis().Redispool(), "", utils.ResetTokenTimeOut, fmt.Sprintf("%d", num))
		server.BuKa().BuKaSendSms([]string{user.PhoneNum}, "")
		break
	case utils.SendByEmail:
		if user.Email == "" {
			ctx.RespErr(errors.New("账号没有绑定邮箱无法重置密码"), &errcode)
			return
		}
		if user.Email != reqdata.Email {
			ctx.RespErr(errors.New("请输入已绑定的邮箱"), &errcode)
			return
		}
		utils.SetStringEx(server.Redis().Redispool(), "", utils.ResetTokenTimeOut, fmt.Sprintf("%d", num))
		server.BuKa().BuKaSendCodeEmail([]string{user.Email}, num, utils.ResetTokenTimeOutMinute)
		break
	case utils.SendByTransfer:
		if user.RechargeAddressTron == "" {
			ctx.RespErr(errors.New("账号没有绑定地址无法重置密码"), &errcode)
			return
		}
		if user.RechargeAddressEth != reqdata.TransferAddress {
			ctx.RespErr(errors.New("获取的地址与账号绑定地址不一致"), &errcode)
			return
		}
		if user.BetCount <= 0 {
			ctx.RespErr(errors.New("账号绑定地址无法达到转账重置密码要求"), &errcode)
			return
		}

		utils.SetStringEx(server.Redis().Redispool(), "", utils.ResetTokenTimeOut, fmt.Sprintf("%d", num))
		ctx.RespOK(model.ResetMoneyRes{
			ResetType: reqdata.ResetType,
			//TransferMoney:   result,
			TransferAddress: user.RechargeAddressTron,
		})
		break
	default:
		break
	}
}

func (c *UserController) set_login_password(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		OldPassword   string
		NewPassword   string
		NewPasswordV2 string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	host, _, _ := net.SplitHostPort(ctx.Host())
	host = strings.Replace(host, "www.", "", -1)

	reqdata.OldPassword = utils.Md5V(reqdata.OldPassword)
	reqdata.NewPassword = utils.Md5V(reqdata.NewPassword)

	var PasswordV2 string
	PasswordV2, err = utils.ConvertUserPassword(reqdata.NewPasswordV2)
	if err != nil {
		logs.Error("set_login_password ConvertUserPassword err:", err)
	}

	token := server.GetToken(ctx)
	userdata, err := server.XDb().Table("x_user").Where("UserId = ?", token.UserId).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	Password := userdata.String("Password")
	UpdatePasswordTime := userdata.String("UpdatePasswordTime")
	AccountType := userdata.Int("AccountType")
	// 谷歌账号首次不校验旧密码
	if !((AccountType == 4 || AccountType == 5 || AccountType == 7 ||
		robot.IfOpenDomainAutoRegister(ctx.Gin(), host, robot.DomainName)) &&
		UpdatePasswordTime == "") { // 是否开启 自动注册
		if Password != reqdata.OldPassword {
			ctx.RespErrString(true, &errcode, "原登录密码不正确")
			return
		}
	}
	_, err = server.Db().Conn().Exec("update x_user set Password = ?, PasswordV2 = ?,UpdatePasswordTime = now() where UserId = ?", reqdata.NewPassword, PasswordV2, token.UserId)
	//修改登录密码，推送消息到CustomerIo客服系统
	if err == nil {
		c.PushCustomEvent(fmt.Sprint(token.UserId), "change_password", map[string]interface{}{
			"account_id": token.UserId,             // 用户账号ID
			"email":      userdata.String("Email"), // 用户邮箱
			"phone":      userdata.String("PhoneNum"),
			"type":       "change_password",
		})

		//创建消息发送服务实例
		messageService := msg.NewSendMessageAPIService()
		// 修改资金密码通知
		e := messageService.SendMessage(msg.MsgTypeLoginPasswordChanged, token.UserId, nil)
		if e != nil {
			logs.Error("发送站内短信失败: ", " userId=", token.UserId, " err=", e)
		}
	}
	ctx.RespOK()
}

func (c *UserController) set_info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		HeadId          string
		Gender          string
		DeliveryAddress string
		Birthday        string
		NickName        string
		TgName          string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	data := make(map[string]interface{})
	isdata := false
	daoUser := server.DaoxHashGame().XUser
	if reqdata.HeadId != "" {
		data["HeadId"] = reqdata.HeadId
		isdata = true
	}
	if reqdata.Gender != "" {
		data["Gender"] = reqdata.Gender
		isdata = true
	}
	if reqdata.DeliveryAddress != "" {
		data["DeliveryAddress"] = reqdata.DeliveryAddress
		isdata = true
	}
	if reqdata.Birthday != "" {
		data["Birthday"] = reqdata.Birthday
		isdata = true
	}
	if reqdata.NickName != "" {
		data["NickName"] = reqdata.NickName
		isdata = true
	}
	if reqdata.TgName != "" {
		reqdata.TgName = strings.Replace(reqdata.TgName, "@", "", -1)
		data["TgName"] = reqdata.TgName
		// 判断是否有相同tgname
		user, _ := daoUser.WithContext(ctx.Gin()).Where(daoUser.TgName.Eq(reqdata.TgName)).First()
		if user != nil {
			ctx.RespErrString(true, &errcode, "该TgName已存在")
			return
		}
		isdata = true
	}
	if !isdata {
		ctx.RespOK()
		return
	}

	dbUser := daoUser.WithContext(ctx.Gin())
	_, err = dbUser.Where(daoUser.UserID.Eq(int32(token.UserId))).Updates(data)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *UserController) get_bank(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, "")
	data, _ := server.Db().Table("x_user_bank").Where(where).GetList()
	ctx.RespOK(data)
}

func (c *UserController) add_bank(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RealName   string `validate:"required"`
		BankName   string `validate:"required"`
		BankNumber string `validate:"required"`
		KaiHuHang  string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	data := abugo.ObjectToMap(reqdata)
	(*data)["SellerId"] = token.SellerId
	(*data)["ChannelId"] = token.ChannelId
	(*data)["UserId"] = token.UserId
	server.Db().Table("x_user_bank").Insert(*data)
	ctx.RespOK()
}

func (c *UserController) del_bank(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Id int
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	server.Db().Conn().Exec("delete from x_user_bank where Id = ? and UserId = ?", reqdata.Id, token.UserId)
	ctx.RespOK()
}

func (c *UserController) bind_phone(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		PhoneNum   string
		VerifyCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if token.ChannelId == 0 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, 0)
		userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
		if userdata != nil {
			token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
		}
	}
	r, _ := server.Db().Conn().Exec("delete from x_verify where SellerId = ? and ChannelId = ? and Account = ? and UseType = 1 and VerifyCode = ?", token.SellerId, 1, reqdata.PhoneNum, reqdata.VerifyCode)
	rows, _ := r.RowsAffected()
	if rows > 0 {
		userDao := server.DaoxHashGame().XUser
		userDb := userDao.WithContext(ctx.Gin())

		userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
		if err != nil {
			ctx.RespErrString(true, &errcode, "账号不存在")
			return
		}
		if len(userData.BindPhoneNum) > 2 {
			ctx.RespErrString(true, &errcode, "您已绑定过手机号")
			return
		}
		_, err = userDb.Where(userDao.PhoneNum.Eq(reqdata.PhoneNum)).First()
		if err == nil {
			ctx.RespErrString(true, &errcode, "该手机号已占用")
			return
		}
		_, err = userDb.Where(userDao.BindPhoneNum.Eq(reqdata.PhoneNum)).First()
		if err == nil {
			ctx.RespErrString(true, &errcode, "该手机号已占用")
			return
		}
		server.Db().Conn().Exec("update x_user set BindPhoneNum = ? where UserId = ?", reqdata.PhoneNum, token.UserId)

		// 发送手机绑定事件到ThinkingData
		threading.GoSafe(func() {
			// 使用用户登录IP
			clientIP := userData.LoginIP
			if clientIP == "" {
				clientIP = "unknown"
			}

			// 发送手机绑定事件到ThinkingData
			err := datapush.SendPhoneBindEvent(userData, clientIP, reqdata.PhoneNum)
			if err != nil {
				logs.Error("发送手机绑定事件到ThinkingData失败:", err, "用户ID:", token.UserId)
			}
		})

		ctx.RespOK()
	} else {
		ctx.RespErrString(true, &errcode, "验证码不正确")
		return
	}
}

func (c *UserController) bind_phone2(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		PhoneNum   string
		VerifyCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if token.ChannelId == 0 {
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", token.UserId, 0)
		userdata, _ := server.Db().Table("x_user").Select("ChannelId").Where(where).GetOne()
		if userdata != nil {
			token.ChannelId = int(abugo.GetInt64FromInterface((*userdata)["ChannelId"]))
		}
	}

	if !c.Sms.VerifyCode(reqdata.PhoneNum, reqdata.VerifyCode) {
		logs.Error("校验手机验证错误: PhoneNum=%s, VerifyCode=%s", reqdata.PhoneNum, reqdata.VerifyCode)
		ctx.RespErrString(true, &errcode, "验证码不正确")
		return
	}
	logs.Debug("校验手机验证成功: PhoneNum=%s, VerifyCode=%s", reqdata.PhoneNum, reqdata.VerifyCode)
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		ctx.RespErrString(true, &errcode, "账号不存在")
		return
	}
	if len(userData.BindPhoneNum) > 2 {
		ctx.RespErrString(true, &errcode, "您已绑定过手机号")
		return
	}
	tmInfo, err1 := userDb.Where(userDao.PhoneNum.Eq(reqdata.PhoneNum)).First()
	if err1 == nil && tmInfo != nil && tmInfo.UserID != userData.UserID {
		ctx.RespErrString(true, &errcode, "该手机号已占用")
		return
	}
	_, err = userDb.Where(userDao.BindPhoneNum.Eq(reqdata.PhoneNum)).First()
	if err == nil {
		ctx.RespErrString(true, &errcode, "该手机号已占用")
		return
	}
	server.Db().Conn().Exec("update x_user set BindPhoneNum = ? where UserId = ?", reqdata.PhoneNum, token.UserId)
	if userData.PhoneNum == "" {
		server.Db().Conn().Exec("update x_user set PhoneNum = ? where UserId = ?", reqdata.PhoneNum, token.UserId)
	}
	// 发送手机绑定事件到ThinkingData
	threading.GoSafe(func() {
		// 使用用户登录IP
		clientIP := userData.LoginIP
		if clientIP == "" {
			clientIP = "unknown"
		}

		// 发送手机绑定事件到ThinkingData
		err := datapush.SendPhoneBindEvent(userData, clientIP, reqdata.PhoneNum)
		if err != nil {
			logs.Error("发送手机绑定事件到ThinkingData失败:", err, "用户ID:", token.UserId)
		}
	})
	ctx.RespOK()
}

type BindEmailRequestData struct {
	Email      string `validate:"required"`
	VerifyCode string `validate:"required"`
}

func (c *UserController) bind_email(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := BindEmailRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	err = c.bind_Email(ctx, &reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *UserController) bind_Email(ctx *abugo.AbuHttpContent, reqdata *BindEmailRequestData) error {
	token := server.GetToken(ctx)
	//_, SellerId := server.GetChannel(ctx, "")

	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		return err
	}
	if len(userData.Email) > 2 {
		return fmt.Errorf("您已绑定过邮箱")
	}
	_, err = userDb.Where(userDao.Email.Eq(reqdata.Email)).First()
	if err == nil {
		return fmt.Errorf("邮箱已被占用")
	}
	daoV := server.DaoxHashGame().XVerify
	dbV := daoV.WithContext(ctx.Gin())
	r, _ := dbV.Where(daoV.Account.Eq(reqdata.Email)).
		Where(daoV.UseType.Eq(3)).Where(daoV.VerifyCode.Eq(reqdata.VerifyCode)).Delete()
	if r.RowsAffected == 0 {
		return fmt.Errorf("验证码不正确")
	}
	logs.Info("bind_email delete ", reqdata.Email, reqdata.VerifyCode)
	_, err = userDb.Where(userDao.UserID.Eq(int32(token.UserId))).Update(userDao.Email, reqdata.Email)
	if err != nil {
		logs.Error(err)
		return err
	}

	c.PushCustomEvent(fmt.Sprint(userData.UserID), "bind_email", map[string]interface{}{
		"account_id": userData.UserID,
		"email":      reqdata.Email,
		"phone":      userData.PhoneNum,
		"type":       "bind_email",
	})

	return nil
}

func (c *UserController) bind_email2(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := BindEmailRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	err = c.bind_Email2(ctx, &reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ctx.RespOK()
}

func (c *UserController) bind_Email2(ctx *abugo.AbuHttpContent, reqdata *BindEmailRequestData) error {
	token := server.GetToken(ctx)
	//_, SellerId := server.GetChannel(ctx, "")

	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		return err
	}
	if len(userData.BindEmail) > 2 {
		return fmt.Errorf("您已绑定过邮箱")
	}
	tmInfo, err := userDb.Where(userDao.Email.Eq(reqdata.Email)).First()
	if err == nil && tmInfo != nil && tmInfo.UserID != userData.UserID && tmInfo.SellerID != userData.SellerID {
		return fmt.Errorf("邮箱已被占用")
	}
	_, err = userDb.Where(userDao.BindEmail.Eq(reqdata.Email)).First()
	if err == nil {
		return fmt.Errorf("邮箱已被占用")
	}
	daoV := server.DaoxHashGame().XVerify
	dbV := daoV.WithContext(ctx.Gin())
	r, _ := dbV.Where(daoV.Account.Eq(reqdata.Email)).
		Where(daoV.UseType.Eq(3)).Where(daoV.VerifyCode.Eq(reqdata.VerifyCode)).Delete()
	if r.RowsAffected == 0 {
		return fmt.Errorf("验证码不正确")
	}
	logs.Info("bind_email delete ", reqdata.Email, reqdata.VerifyCode)

	// 如果用户没有注册邮箱，同时更新Email字段
	if userData.Email == "" {
		_, err = userDb.Where(userDao.UserID.Eq(int32(token.UserId))).Update(userDao.Email, reqdata.Email)
		if err != nil {
			logs.Error("更新Email字段失败:", err)
			return err
		}
	}

	// 更新BindEmail字段
	_, err = userDb.Where(userDao.UserID.Eq(int32(token.UserId))).Update(userDao.BindEmail, reqdata.Email)
	if err != nil {
		logs.Error(err)
		return err
	}
	c.PushCustomEvent(fmt.Sprint(userData.UserID), "bind_email", map[string]interface{}{
		"account_id": userData.UserID,
		"email":      reqdata.Email,
		"phone":      userData.PhoneNum,
		"type":       "bind_email",
	})
	//
	host := userData.RegURL
	if robot.IfOpenDomainAutoRegister(ctx.Gin(), host, robot.DomainName) {
		robot.SendNoticeToUserByEmail(reqdata.Email, "zh", userData.Account, host)
		logs.Info("域名自动注册发送邮件 %s bind_email send  ", userData.UserID, reqdata.Email, reqdata.VerifyCode)

	}
	return nil
}

func (c *UserController) get_balance(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	var user struct {
		Amount      float64 `gorm:"column:Amount"`      // 当前USDT余额
		BonusAmount float64 `gorm:"column:BonusAmount"` // 当前Bonus余额
	}
	errcode := 0
	err := server.Db().GormDao().Table("x_user").
		Select("Amount, BonusAmount").
		Where("UserId = ?", token.UserId).
		Scan(&user).Error
	if err != nil {
		if ctx.RespErr(err, &errcode) {
			logs.Error("查询玩家余额发生错误,userId=", token.UserId, err)
			return
		}
	}
	ctx.RespOK(user)
}

func GetTgRobotByToken(token string) (tgRobotId int64, bot *model2.XRobotConfig) {
	if token != "" {
		daoTgBot := server.DaoxHashGame().XRobotConfig
		bot, _ := daoTgBot.WithContext(context.Background()).Where(daoTgBot.Token.Eq(token)).First()

		oldDaoTgBot := server.DaoxHashGame().XTgRobotGuide
		oldBot, _ := oldDaoTgBot.WithContext(context.Background()).Where(oldDaoTgBot.TgRobotToken.Eq(token)).First()

		if bot != nil {
			tgRobotId = bot.ID
		}

		if oldBot != nil {
			tgRobotId = oldBot.ID
		}

		return tgRobotId, nil
	}
	return 0, nil
}

// 创建被拒绝的trx体验金申请单
func createTrxTyjRejectOrder(user *model2.XUser, memo string, amount float64) (err error) {
	// amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingTrx")
	return createTyjOrder(user, memo, "trx", 2, amount)
}

// 创建被拒绝的usdt体验金申请单
func createUsdtTyjRejectOrder(user *model2.XUser, memo string, amount float64) (err error) {
	return createTyjOrder(user, memo, "usdt", 2, amount)
}

// 发放USDT体验金(不需要人工审核)
func giveUSDTTyj(user *model2.XUser, amount float64) (err error) {
	return createTyjOrder(user, "系统自动发放", "usdt", 5, amount)
}

func createTyjOrder(user *model2.XUser, memo string, symbol string, state int32, amount float64) (err error) {
	if amount == 0 {
		return
	}
	ctx := context.Background()
	err = server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		RegisterIPCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.RegisterIP.Eq(user.RegisterIP)).Count()
		LoginIPCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.LoginIP.Eq(user.LoginIP)).Count()
		PwdCount, _ := tx.XUser.WithContext(ctx).Where(tx.XUser.Password.Eq(user.Password)).Count()
		TiYanJingUsdtLiushuiBeishu := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtLiushuiBeishu")

		tbTiyanjing := &model2.XTiyanjing{
			SellerID:     user.SellerID,
			ChannelID:    user.ChannelID,
			UserID:       user.UserID,
			State:        state, // 状态 1待审核,2审核拒绝,3审核通过,4拒绝发放,5已发放,6正在出款,7出款完成
			Symbol:       symbol,
			Amount:       amount,
			Memo:         memo,
			Account:      user.Account,
			TgName:       user.TgName,
			RegisterTime: user.RegisterTime,
			LoginTime:    user.LoginTime,
			CSGroup:      user.CSGroup,
			CSID:         user.CSID,
			IPCount:      int32(RegisterIPCount),
			LoginIPCount: int32(LoginIPCount),
			PwdCount:     int32(PwdCount),
		}
		err := tx.XTiyanjing.WithContext(ctx).Select(
			tx.XTiyanjing.SellerID, tx.XTiyanjing.ChannelID, tx.XTiyanjing.UserID, tx.XTiyanjing.State, tx.XTiyanjing.Symbol,
			tx.XTiyanjing.Amount, tx.XTiyanjing.Memo, tx.XTiyanjing.Account, tx.XTiyanjing.TgName, tx.XTiyanjing.RegisterTime,
			tx.XTiyanjing.LoginTime, tx.XTiyanjing.CSGroup, tx.XTiyanjing.CSID, tx.XTiyanjing.IPCount, tx.XTiyanjing.LoginIPCount,
			tx.XTiyanjing.PwdCount,
		).Create(tbTiyanjing)
		if err != nil {
			return err
		}
		if state == 2 && symbol == "trx" {
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).
				Where(tx.XUser.TrxGiftStatus.Eq(1)).
				Update(tx.XUser.TrxGiftStatus, -1)
			if err != nil {
				return err
			}
		} else if state == 2 && symbol == "usdt" {
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).
				Where(tx.XUser.UsdtGiftStatus.Eq(1)).
				Update(tx.XUser.UsdtGiftStatus, -1)
			if err != nil {
				return err
			}
		} else if state == 5 && symbol == "usdt" {
			_, err = tx.XTiyanjing.WithContext(ctx).Where(tx.XTiyanjing.UserID.Eq(user.UserID), tx.XTiyanjing.Symbol.Eq(symbol)).
				Update(tx.XTiyanjing.SendTime, time.Now())
			if err != nil {
				return err
			}
			err = tx.XTiyanjinex.WithContext(ctx).Create(&model2.XTiyanjinex{ID: tbTiyanjing.ID, State: state})
			if err != nil {
				return err
			}
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).Update(tx.XUser.UsdtGiftStatus, tx.XUser.UsdtGiftStatus.Add(1))
			if err != nil {
				return err
			}
			// 加提现流水
			_, err = tx.XUser.WithContext(ctx).Where(tx.XUser.UserID.Eq(user.UserID)).Update(tx.XUser.WithdrawLiuSui, tx.XUser.WithdrawLiuSui.Add(TiYanJingUsdtLiushuiBeishu*amount))
			if err != nil {
				return err
			}

			if user.AccountType == 5 {
				// 语音播报
				body := struct {
					SellerId    int32           `validate:"required"`
					Symbol      string          `validate:"required"`
					UserId      int32           `validate:"required"`
					KefuAccount string          `validate:"required"`
					Amount      decimal.Decimal `validate:"required"`
				}{
					SellerId:    user.SellerID,
					Symbol:      symbol,
					UserId:      user.UserID,
					KefuAccount: user.CSID,
					Amount:      decimal.NewFromFloat(amount),
				}
				adminapi := viper.GetString("adminapi")
				adminapi_readonly := viper.GetString("adminapi_readonly")
				baseurl := "/api/TyjBroadcast/broadcast"
				resp, err := req.Post(fmt.Sprintf("%s%s", adminapi, baseurl), req.BodyJSON(body))
				if err != nil {
					logs.Error("语音播报 err:", err)
				} else {
					logs.Debug("语音播报 resp:", resp.String())
				}
				_, err = req.Post(fmt.Sprintf("%s%s", adminapi_readonly, baseurl), req.BodyJSON(body))
				if err != nil {
					logs.Error("语音播报 err:", err)
				}

				// tg群组汇报
				go tgSendHuibao(user.UserID, 2, time.Now(), amount, symbol)
			}

		}
		return nil
	})
	return err
}

func (c *UserController) rewardTrialFee(ctx *abugo.AbuHttpContent) {
	errcode := 0
	req := struct {
		Host string
	}{}

	if err := ctx.RequestData(&req); ctx.RespErr(err, &errcode) {
		return
	}

	host, _, _ := net.SplitHostPort(ctx.Host())
	host = strings.Replace(host, "www.", "", -1)
	if req.Host != "" {
		host = req.Host
	}

	token := server.GetToken(ctx)

	rediskey := fmt.Sprintf("%v:%v:tiyanjing_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 10)
	if lck != nil {
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}

	user, err := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		//return errors.New("未找到该用户")
		ctx.RespErrString(true, &errcode, "未找到该用户")
		return
	}

	channelHost, err := server.DaoxHashGame().XChannelHost.WithContext(nil).Where(server.DaoxHashGame().XChannelHost.Host.Eq(host)).First()
	if err != nil {
		//return errors.New("该域名渠道不存在")
		ctx.RespErrString(true, &errcode, "该域名渠道不存在")
		return
	}

	isTyjEnable := 2
	if channelHost != nil {
		isTyjEnable = int(channelHost.IsTyjEnable)
	}

	if isTyjEnable == 2 {
		ctx.RespErrString(true, &errcode, "该渠道无法领取体验金")
		return
	}

	// 判断用户的体验金状态
	if user.UsdtGiftStatus != 1 && user.UsdtGiftStatus != 3 {

		ctx.RespErrString(true, &errcode, "不符合领取条件")
		return
	}

	amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdt")
	DeviceIDCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegDeviceID.Eq(user.RegDeviceID)).Count()
	IPCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegisterIP.Eq(user.RegisterIP)).Count()

	if channelHost.GiftDeviceIDLimit == 1 {
		if DeviceIDCount > 1 {
			ctx.RespErrCodeString(true, 10000, "不符合领取条件")
			err = createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
				return
			}
			return
		}
	}

	if channelHost.GiftIPLimit == 1 {
		if IPCount > 1 {
			ctx.RespErrCodeString(true, 10000, "不符合领取条件")
			err = createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
				return
			}
			return
		}
	}

	// 特殊处理渠道 100106
	// if user.ChannelID != 100106 {
	// 	// 设备判断

	// 	if DeviceIDCount > 1 {
	// 		ctx.RespErrCodeString(true, 10000, "不符合领取条件")
	// 		err = createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
	// 		if err != nil {
	// 			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
	// 			return
	// 		}
	// 		return
	// 	}

	// 	// 重复ip判断

	// 	if IPCount > 2 {
	// 		ctx.RespErrCodeString(true, 10000, "不符合领取条件")
	// 		err = createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
	// 		if err != nil {
	// 			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
	// 			return
	// 		}
	// 		return
	// 	}
	// } else {
	// 	// 设备判断
	// 	DeviceIDCount, _ := server.DaoxHashGame().XUser.WithContext(nil).Where(server.DaoxHashGame().XUser.RegDeviceID.Eq(user.RegDeviceID)).Count()
	// 	if DeviceIDCount > 1 {
	// 		ctx.RespErrCodeString(true, 10000, "不符合领取条件")
	// 		err = createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
	// 		if err != nil {
	// 			ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
	// 			return
	// 		}
	// 		return
	// 	}
	// }

	//// 检查地址
	//ok, err := checkAddressRelation(user.Address)
	//if err != nil {
	//	ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
	//	return
	//}
	//
	//if !ok {
	//	ctx.RespErrCodeString(true, 10000, "不符合领取条件")
	//	err = createUsdtTyjRejectOrder(user, "地址关联,拒绝发放", amount)
	//	if err != nil {
	//		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
	//		return
	//	}
	//	return
	//}

	err = giveUSDTTyj(user, amount)
	if err != nil {
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}

	ctx.RespOK()
}

func (c *UserController) tiyanjing(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	errcode := 0

	rediskey := fmt.Sprintf("%v:%v:tiyanjing_%v", server.Project(), server.Module(), token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 10)
	if lck != nil {
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}

	userDao := server.DaoxHashGame().XUser
	user, err := userDao.WithContext(ctx.Gin()).Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
		return
	}

	if user.AccountType != 5 || (user.TrxGiftStatus != 1 && user.UsdtGiftStatus != 1 && user.UsdtGiftStatus != 3) {
		// 不符合领取条件
		ctx.RespErrString(true, &errcode, "")
		return
	}

	// 如果账号类型是tg引入，而tgtoken又是空的，说明系统错误
	if user.TgRobotToken == "" {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
		return
	}
	// 获取账号的tg属性
	tgRobotGuideDao := server.DaoxHashGame().XRobotConfig
	tgRobotGuide, _ := tgRobotGuideDao.WithContext(ctx.Gin()).Where(tgRobotGuideDao.Token.Eq(user.TgRobotToken)).First()

	oldTgRobotGuideDao := server.DaoxHashGame().XTgRobotGuide
	oldTgRobotGuide, _ := oldTgRobotGuideDao.WithContext(ctx.Gin()).Where(oldTgRobotGuideDao.TgRobotToken.Eq(user.TgRobotToken)).First()

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
		return
	}

	if user.TrxGiftStatus == 1 {

		// amount := server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingTrx")
		var amount float64
		// // 获取体验金金额并检查领取条件
		// if tgRobotGuide != nil {
		// 	amount = float64(tgRobotGuide.GiftAmount)
		// 	// 检查领取条件
		// 	if err := checkTyjRestrictions(ctx, user, tgRobotGuide, amount, &errcode); err != nil {
		// 		ctx.RespErrCodeString(true, 10000, err.Error())
		// 		return
		// 	}
		// }
		if oldTgRobotGuide != nil {
			amount = server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingTrx")
			result, err := checkUserInResourceDb(user.TgUserName, user.TgChatID)
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
				return
			}
			if result.Id == 0 {
				ctx.RespErrCodeString(true, 10000, "不符合领取条件")
				err = createTrxTyjRejectOrder(user, "不在库", amount)
				if err != nil {
					ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
					return
				}
				return
			}
			if !result.IsAvailableReceive {
				ctx.RespErrCodeString(true, 10000, "不符合领取条件")
				err = createTrxTyjRejectOrder(user, "无领取资格", amount)
				if err != nil {
					ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
					return
				}
				return
			}

			// 检查领取条件
			if err := checkTyjRestrictions(ctx, user, oldTgRobotGuide, amount, "trx", &errcode); err != nil {
				ctx.RespErrCodeString(true, 10000, err.Error())
				return
			}

			// 申请体验金（需要人工审核）
			presult, err := server.Db().CallProcedure("x_api_tiyanjing_apply", token.UserId, amount)
			if ctx.RespErr(err, &errcode) {
				return
			}
			if ctx.RespProcedureErr(presult) {
				return
			}
		}

	}

	if user.UsdtGiftStatus == 1 || user.UsdtGiftStatus == 3 {
		var amount float64
		if tgRobotGuide != nil {
			amount = float64(tgRobotGuide.GiftAmount)
			if user.IsInResourceDb == 1 {
				amount = float64(tgRobotGuide.GiftAmountIndb)
			}

			// 检查领取条件
			if err := checkTyjRestrictions(ctx, user, tgRobotGuide, amount, "usdt", &errcode); err != nil {
				ctx.RespErrCodeString(true, 10000, err.Error())
				return
			}

			// 自动发放
			err = giveUSDTTyj(user, amount)
			if err != nil {
				ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
				return
			}
			memo := fmt.Sprintf("account:%s,amount:%d", user.Account, tgRobotGuide.GiftAmount)
			if tgRobotGuide.GiftCurrency == "usdt" {
				go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgRobotGuide.ID, user.TgChatID, stat.ActionType_GetUSDT, memo, false, time.Time{})
			} else {
				go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgRobotGuide.ID, user.TgChatID, stat.ActionType_GetUSDT, memo, false, time.Time{})
			}
		}

		if oldTgRobotGuide != nil {
			if oldTgRobotGuide.TgRobotType == 1 {
				amount = server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdt")
			}

			if oldTgRobotGuide.TgRobotType == 2 {
				amount = server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtEnRobot")
			}

			if oldTgRobotGuide.TgRobotType == 1 && user.UsdtGiftStatus == 3 {
				amount = server.GetConfigFloat(int(user.SellerID), 0, "TiYanJingUsdtSecond")
			}

			if oldTgRobotGuide.TgRobotType == 2 {
				// 开启IP限制 且 用户IP不在白名单
				if oldTgRobotGuide.IsIPRestriction == 1 && strings.Index(oldTgRobotGuide.IPWhitelist, user.LoginIP) == -1 {
					LoginIPCount, _ := userDao.WithContext(ctx.Gin()).Where(userDao.LoginIP.Eq(user.LoginIP)).Count()
					if LoginIPCount > 1 {
						ctx.RespErrCodeString(true, 10000, "不符合领取条件")
						err = createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
						if err != nil {
							ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
							return
						}
						memo := fmt.Sprintf("account:%s,amount:%f,ip:%s", user.Account, amount, user.LoginIP)
						go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, oldTgRobotGuide.ID, user.TgChatID, stat.ActionType_FailWithIP, memo, false, time.Time{})
						return
					}
				}
				// if开启设备ID限制
				if oldTgRobotGuide.IsDeviceRestriction == 1 {
					DeviceIDCount, _ := userDao.WithContext(ctx.Gin()).Where(userDao.LoginDeviceID.Eq(user.LoginDeviceID)).Count()
					if DeviceIDCount > 1 {
						ctx.RespErrCodeString(true, 10000, "不符合领取条件")
						err = createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
						if err != nil {
							ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
							return
						}
						memo := fmt.Sprintf("account:%s,amount:%f,deviceId:%s", user.Account, amount, user.LoginDeviceID)
						go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, oldTgRobotGuide.ID, user.TgChatID, stat.ActionType_FailWithDeviceId, memo, false, time.Time{})
						return
					}
				}
				// if开启关联地址限制
				if oldTgRobotGuide.IsWalletRestriction == 1 {
					ok, err := checkAddressRelation(user.Address)
					if err != nil {
						ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
						return
					}
					if !ok {
						ctx.RespErrCodeString(true, 10000, "不符合领取条件")
						err = createUsdtTyjRejectOrder(user, "地址关联,拒绝发放", amount)
						if err != nil {
							ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
							return
						}
						memo := fmt.Sprintf("account:%s,amount:%f,address:%s", user.Account, amount, user.Address)
						go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, oldTgRobotGuide.ID, user.TgChatID, stat.ActionType_FailWithAddress, memo, false, time.Time{})
						return
					}
				}

			}

			if oldTgRobotGuide.TgRobotType == 1 || (oldTgRobotGuide.TgRobotType == 2 && user.UsdtGiftStatus == 1) ||
				(oldTgRobotGuide.TgRobotType == 2 && user.UsdtGiftStatus == 3 && oldTgRobotGuide.IsAotoTyj2 == 1) {
				// 自动发放
				err = giveUSDTTyj(user, amount)
				if err != nil {
					ctx.RespErrString(true, &errcode, "系统错误,请稍后再试.")
					return
				}
			}

			if oldTgRobotGuide.TgRobotType == 2 && user.UsdtGiftStatus == 3 && oldTgRobotGuide.IsAotoTyj2 != 1 {
				amount = server.GetConfigFloat(int(user.SellerID), 0, "EnRobotTaskReward")
				// 申请体验金（需要人工审核）
				presult, err := server.Db().CallProcedure("x_api_tiyanjing_apply", token.UserId, amount)
				if ctx.RespErr(err, &errcode) {
					return
				}
				if ctx.RespProcedureErr(presult) {
					return
				}
			}

			if user.TrxGiftStatus == 1 {
				giftTrx := server.GetConfigFloat(token.SellerId, 0, "TiYanJingTrx")
				memo := fmt.Sprintf("account:%s,amount:%f", user.Account, giftTrx)
				go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, oldTgRobotGuide.ID, user.TgChatID, stat.ActionType_GetTRX, memo, false, time.Time{})
			} else if user.UsdtGiftStatus == 1 || user.UsdtGiftStatus == 3 {
				tmpConfigName := "TiYanJingUsdtError"
				if oldTgRobotGuide.TgRobotType == 1 {
					tmpConfigName = "TiYanJingUsdt"
				} else if oldTgRobotGuide.TgRobotType == 2 {
					tmpConfigName = "TiYanJingUsdtEnRobot"
				}
				giftUsdt := server.GetConfigFloat(token.SellerId, 0, tmpConfigName)
				memo := fmt.Sprintf("account:%s,amount:%f", user.Account, giftUsdt)
				go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, oldTgRobotGuide.ID, user.TgChatID, stat.ActionType_GetUSDT, memo, false, time.Time{})
			}
		}
	}

	threading.GoSafe(func() {
		err = updateTiyanjinStatusInResourceDb(user.TgUserName, user.TgChatID, server.Debug())
		if err != nil {
			logs.Error("体验金领取状态回写失败：", err)
		}
	})
	ctx.RespOK()
}

func (c *UserController) robot_mession_reward(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	userId := int32(token.UserId)
	errcode := 0
	err := server.DaoxHashGame().Transaction(func(tx *dao.Query) error {
		xRobotMissionRewardDao := server.DaoxHashGame().XRobotMissionReward
		robotMissionRewardInfos, err := xRobotMissionRewardDao.WithContext(nil).
			Clauses(clause.Locking{Strength: "UPDATE"}).
			Where(xRobotMissionRewardDao.UserID.Eq(int64(userId))).
			Where(xRobotMissionRewardDao.Currency.Eq("usdt")).
			Where(xRobotMissionRewardDao.Stat.Eq(1)).
			First()

		if robotMissionRewardInfos != nil {
			activeAddUseBalancerInfo := struct {
				UserId            int32
				ActiveName        string
				RealAmount        float64
				WithdrawLiuSuiAdd float64
				BalanceCReason    int
			}{
				UserId:            userId,
				ActiveName:        "机器人任务奖励领取",
				RealAmount:        float64(robotMissionRewardInfos.Amount),
				WithdrawLiuSuiAdd: 0,
				BalanceCReason:    201,
			}

			err = ActiveAddUseBalancer(tx, activeAddUseBalancerInfo)

			// 更新状态
			_, err = xRobotMissionRewardDao.WithContext(nil).
				Where(xRobotMissionRewardDao.UserID.Eq(int64(userId))).
				Where(xRobotMissionRewardDao.Currency.Eq("usdt")).
				Where(xRobotMissionRewardDao.Stat.Eq(1)).
				Updates(model2.XRobotMissionReward{
					Amount: 0,
					Stat:   2,
				})

			if err != nil {
				return err
			}
		}

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK()

}

func (c *UserController) recharge_maidian(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	userId := int32(token.UserId)

	errcode := 0
	var resData = struct {
		Id         int32           `gorm:"column:Id"`
		RealAmount decimal.Decimal `gorm:"column:RealAmount"`
		OrderId    string          `gorm:"column:OrderId"`
		Count      int64
	}{}

	rcDao := server.DaoxHashGame().XRecharge
	rcDb := rcDao.WithContext(ctx.Gin())
	xRecharge, err := rcDb.Where(rcDao.UserID.Eq(userId)).Where(rcDao.MaidianState.Eq(0)).Where(rcDao.State.Eq(5)).Order(rcDao.ID.Desc()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctx.RespOK()
			return
		}
		ctx.RespErr(err, &errcode)
		return
	}
	resData.Id = xRecharge.ID
	resData.RealAmount = decimal.NewFromFloat(xRecharge.RealAmount)
	resData.OrderId = xRecharge.OrderID
	orderId := strconv.Itoa(int(xRecharge.ID))
	logs.Info("recharge_maidian orderId:", orderId)

	count, _ := rcDb.Where(rcDao.UserID.Eq(userId)).Count()
	resData.Count = count

	// 首充上报
	if xRecharge.IsFirst == 1 {
		userDao := server.DaoxHashGame().XUser
		udb := userDao.WithContext(ctx.Gin())
		user, err := udb.Where(userDao.UserID.Eq(userId)).First()
		if err != nil {
			logs.Error("首充上报获取用户信息失败:", err)
			// 继续执行，不要因为上报失败而影响用户体验
		} else {
			ua := ctx.Gin().GetHeader("User-Agent")
			threading.GoSafe(func() {
				if cli := convertApi.GetClient(token.Host); cli != nil {
					xUserMore := server.DaoxHashGame().XUserMore
					userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).First()
					// if user.Fbc != "" {
					logs.Info("首充上报 fbc:", user.Fbc, " fbp:", userMoreInfo.Fbp, "事件ID", orderId)
					cli.FirstRecharge(orderId, user.PhoneNum, user.Email, ctx.GetIp(), ua, xRecharge.RealAmount, user.Fbc, userMoreInfo.Fbp, int(userId))
					// }
				}
			})

			threading.GoSafe(func() {
				if cli := kwai.GetClient(token.Host); cli != nil {
					cli.FirstRecharge(user.Kwai, xRecharge.RealAmount)
				}
			})

			threading.GoSafe(func() {
				if cli := tiktok.GetClient(token.Host); cli != nil {
					cli.FirstRecharge(orderId, tiktok.User{
						Phone: user.PhoneNum,
						Email: user.Email,
						Ip:    ctx.GetIp(),
					}, xRecharge.RealAmount)
				}
			})
			// Bigo上报
			threading.GoSafe(func() {
				if cli := kwai.GetClient(token.Host); cli != nil {
					cli.FirstRecharge(user.Bigo, xRecharge.RealAmount)
				}
			})
		}
	}

	threading.GoSafe(func() {
		userDao := server.DaoxHashGame().XUser
		udb := userDao.WithContext(ctx.Gin())
		user, err := udb.Where(userDao.UserID.Eq(userId)).First()
		if err != nil {
			return
		}
		ua := ctx.Gin().GetHeader("User-Agent")
		// FB充值事件上报
		threading.GoSafe(func() {
			if cli := convertApi.GetClient(token.Host); cli != nil {
				xUserMore := server.DaoxHashGame().XUserMore
				userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).First()

				if user.Fbc != "" {
					logs.Info("充值上报 fbc:", user.Fbc, " fbp:", userMoreInfo.Fbp, "事件ID", orderId)
					cli.Purchase(orderId, user.PhoneNum, user.Email, ctx.GetIp(), ua, xRecharge.RealAmount, user.Fbc, userMoreInfo.Fbp, int(userId))
				}
			}
		})
		// 快手上报
		threading.GoSafe(func() {
			if cli := kwai.GetClient(token.Host); cli != nil {
				cli.Purchase(user.Kwai, xRecharge.RealAmount)
			}
		})
		threading.GoSafe(func() {
			if cli := tiktok.GetClient(token.Host); cli != nil {
				cli.Purchase(orderId, tiktok.User{
					Phone: user.PhoneNum,
					Email: user.Email,
					Ip:    ctx.GetIp(),
				}, xRecharge.RealAmount)
			}
		})
		// Bigo上报
		threading.GoSafe(func() {
			if cli := kwai.GetClient(token.Host); cli != nil {
				cli.Purchase(user.Bigo, xRecharge.RealAmount)
			}
		})
	})

	// tg群组汇报
	threading.GoSafe(func() {
		tgSendHuibao(userId, 1, xRecharge.CreateTime, xRecharge.RealAmount, xRecharge.Symbol)
	})

	_, err = rcDb.Where(rcDao.UserID.Eq(userId)).Where(rcDao.ID.Eq(xRecharge.ID)).Update(rcDao.MaidianState, 1)

	ctx.RespOK(resData)
}

func (c *UserController) add_cart(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	userId := int32(token.UserId)
	userDao := server.DaoxHashGame().XUser
	udb := userDao.WithContext(ctx.Gin())
	user, err := udb.Where(userDao.UserID.Eq(userId)).First()
	if err != nil {
		return
	}
	ua := ctx.Gin().GetHeader("User-Agent")

	threading.GoSafe(func() {
		if cli := convertApi.GetClient(token.Host); cli != nil {
			xUserMore := server.DaoxHashGame().XUserMore
			userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).First()
			if user.Fbc != "" {
				cli.AddToCart(userId, user.PhoneNum, user.Email, ctx.GetIp(), ua, user.Fbc, userMoreInfo.Fbp)
			}
		}
	})

	threading.GoSafe(func() {
		if cli := kwai.GetClient(token.Host); cli != nil {
			cli.AddToCart(user.Kwai, int(user.UserID))
		}
	})

	threading.GoSafe(func() {
		if cli := tiktok.GetClient(token.Host); cli != nil {
			cli.AddToCart(strconv.Itoa(int(userId)), tiktok.User{})
		}
	})

	//threading.GoSafe(func() {
	//	if cli := twitter.GetClient(token.Host); cli != nil {
	//		cli.AddToCart(strconv.Itoa(int(userId)))
	//	}
	//})

	threading.GoSafe(func() {
		if cli := bigo.GetClient(token.Host); cli != nil {
			cli.AddToCart(user.Bigo)
		}
	})

	ctx.RespOK()
}

func (c *UserController) update_game_chip(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		GameChip string `validate:"required"` //筹码
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	isJSON := json.Valid([]byte(reqdata.GameChip))
	if !isJSON {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}
	token := server.GetToken(ctx)
	type Chip struct {
		Chip   int  `json:"chip"`
		Select int8 `json:"select"`
	}
	var chipDatas []Chip
	err = json.Unmarshal([]byte(reqdata.GameChip), &chipDatas)
	if ctx.RespErr(err, &errcode) {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}
	chipData, _ := json.Marshal(chipDatas)
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.AgentID, userTb.TopAgentID).Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}
	gameData := &model2.XUserSetting{
		UserID:     int32(token.UserId),
		SellerID:   int32(token.SellerId),
		ChannelID:  int32(token.ChannelId),
		TopAgentID: user.TopAgentID,
		AgentID:    user.AgentID,
		ChipList:   string(chipData),
		Memo:       "游戏筹码",
	}
	userSettingTb := server.DaoxHashGame().XUserSetting
	userSettingDb := server.DaoxHashGame().XUserSetting.WithContext(context.Background())
	err = userSettingDb.Where(userSettingTb.UserID.Eq(int32(token.UserId))).Save(gameData)
	if ctx.RespErr(err, &errcode) {
		ctx.RespErrString(true, &errcode, "修改筹码失败")
		return
	}
	ctx.RespOK()
}

// 通过tg机器人发送绑定钱包消息
func (c *UserController) tg_sendBind(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	errcode := 0

	sendReq := struct {
		UserId int
	}{
		UserId: token.UserId,
	}
	tgservice := viper.GetString("tgservice")
	_, err := req.Post(tgservice+"/api/sendBind", req.BodyJSON(sendReq))
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK()
}

// 通过tg机器人发送绑定成功的消息
func tgSendBindSuccess(ctx context.Context, UserId int32, Address string) {
	userDao := server.DaoxHashGame().XUser
	db := userDao.WithContext(ctx)
	user, err := db.Where(userDao.UserID.Eq(UserId)).First()
	if err != nil {
		logs.Error(err)
		return
	}

	sendReq := struct {
		SellerId  int32
		ChannelId int32
		TgChatId  int64
		Address   string
	}{
		SellerId:  user.SellerID,
		ChannelId: user.ChannelID,
		TgChatId:  user.TgChatID,
		Address:   Address,
	}
	tgservice := viper.GetString("tgservice")
	_, err = req.Post(tgservice+"/api/npjwfsqvocgetmft", req.BodyJSON(sendReq))
	if err != nil {
		logs.Error(err)
	}
	return
}

// 通过tg机器人发送群组汇报消息
func tgSendHuibao(UserId, Type int32, Time time.Time, Amount float64, Symbol string) {
	sendReq := struct {
		UserId int32 `validate:"required"`
		Type   int32 `validate:"required,gt=0"` // 1:充值 2:领体验金
		Time   string
		Amount decimal.Decimal
		Symbol string
	}{
		UserId: UserId,
		Type:   Type,
		Time:   Time.Format(time.DateTime),
		Amount: decimal.NewFromFloat(Amount),
		Symbol: Symbol,
	}
	tgservice := viper.GetString("tgservice")
	_, err := req.Post(tgservice+"/api/sendHuibao", req.BodyJSON(sendReq))
	if err != nil {
		logs.Error("tg群组汇报发送失败：", err)
	}
	return
}

// check telegram authorization on golang
//php version: https://gist.github.com/anonymous/6516521b1fb3b464534fbc30ea3573c2#file-check_authorization-php
//usage:
/*
func TestTg(t *testing.T) {
	data := "id=1263310&first_name=Vadim&last_name=Kulibaba&username=recoilme&photo_url=https://t.me/i/userpic/320/recoilme.jpg&auth_date=1518535618&hash=1d7069137bf517a63261ee156919a057dca93a416118eebfd0d8f5697442cdce"
	token := "YOUR:TOKEN"
	if !checkTelegramAuthorization(data, token) {
		t.Fail()
	}
}*/
func checkTelegramAuthorization(ctx *abugo.AbuHttpContent, data string) (ok bool) {
	params, _ := url.ParseQuery(data)
	strs := []string{}
	for k, v := range params {
		if k == "hash" {
			continue
		}
		strs = append(strs, k+"="+v[0])
	}
	sort.Strings(strs)

	hash := params.Get("hash")
	botId, _ := strconv.ParseInt(params.Get("bot_id"), 10, 64)

	daoBot := server.DaoxHashGame().XRobotConfig
	dbBot := daoBot.WithContext(ctx.Gin())
	bot, _ := dbBot.Where(daoBot.ID.Eq(botId)).First()

	oldDaoBot := server.DaoxHashGame().XTgRobotGuide
	dbOldDaoBot := oldDaoBot.WithContext(ctx.Gin())
	oldBot, _ := dbOldDaoBot.Where(oldDaoBot.ID.Eq(botId)).First()

	if bot == nil && oldBot == nil {
		return false
	}

	logs.Info("strs", strs)
	var imploded = ""
	for _, s := range strs {
		if imploded != "" {
			imploded += "\n"
		}
		imploded += s
	}
	sha256hash := sha256.New()
	if bot != nil {
		io.WriteString(sha256hash, bot.Token)
	} else {
		io.WriteString(sha256hash, oldBot.TgRobotToken)
	}

	hmachash := hmac.New(sha256.New, sha256hash.Sum(nil))
	io.WriteString(hmachash, imploded)
	ss := hex.EncodeToString(hmachash.Sum(nil))
	logs.Info("ss", ss)
	return hash == ss
}

func MakeIdToken(botId int64, botToken string, chatId int64, firstName, lastName, userName string) string {
	strs := make([]string, 0)
	strs = append(strs, fmt.Sprintf("id=%d", chatId))
	strs = append(strs, fmt.Sprintf("first_name=%s", firstName))
	strs = append(strs, fmt.Sprintf("last_name=%s", lastName))
	strs = append(strs, fmt.Sprintf("username=%s", userName))
	strs = append(strs, fmt.Sprintf("auth_date=%d", time.Now().Unix()))
	strs = append(strs, fmt.Sprintf("bot_id=%d", botId))

	sort.Strings(strs)

	var imploded = ""
	for _, s := range strs {
		if imploded != "" {
			imploded += "\n"
		}
		imploded += s
	}

	sha256hash := sha256.New()
	io.WriteString(sha256hash, botToken)
	hmachash := hmac.New(sha256.New, sha256hash.Sum(nil))
	io.WriteString(hmachash, imploded)
	hash := hex.EncodeToString(hmachash.Sum(nil))

	strs = append(strs, fmt.Sprintf("hash=%s", hash))
	paramStr := strings.Join(strs, "&")

	idToken := base64.StdEncoding.EncodeToString([]byte(url.QueryEscape(paramStr)))
	return idToken
}

func (c *UserController) checkUserEmail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Host     string `validate:"required"`
		SellerId int    `validate:"required"`
		Account  string `validate:"required"` //账号
	}{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ChannelId, SellerId := server.GetChannel(ctx, reqdata.Host)
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.Email, userTb.ChannelID).Where(userTb.SellerID.Eq(int32(SellerId))).Where(userTb.Email.Eq(reqdata.Account)).First()
	if err != nil {
		logs.Error("UserController checkUserEmail", err)
		errmsg := "系统错误,请稍后再试"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			//errmsg = "账号不存在"
			errmsg = "您未绑定过任何验证方式,请联系客服处理"
		}
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	if user.ChannelID != int32(ChannelId) {
		ctx.RespErrString(true, &errcode, "渠道不正确")
		return
	}
	if user.Email == "" {
		errmsg := "您未绑定过任何验证方式,请联系客服处理"
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}
	ctx.Put("Email", user.Email)
	ctx.RespOK()
}

func (c *UserController) verifyUserEmail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := BindEmailRequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	if len(userData.BindEmail) > 2 {
		if ctx.RespErr(fmt.Errorf("您已绑定过邮箱"), &errcode) {
			return
		}
	}
	tmInfo, err := userDb.Where(userDao.Email.Eq(reqdata.Email)).First()
	if err == nil && tmInfo.UserID != int32(token.UserId) && tmInfo.SellerID != int32(token.SellerId) {
		if ctx.RespErr(fmt.Errorf("邮箱已被占用"), &errcode) {
			return
		}
	}
	_, err = userDb.Where(userDao.BindEmail.Eq(reqdata.Email)).First()
	if err == nil {
		if ctx.RespErr(fmt.Errorf("邮箱已被占用"), &errcode) {
			return
		}
	}
	daoV := server.DaoxHashGame().XVerify
	dbV := daoV.WithContext(ctx.Gin())
	r, _ := dbV.Where(daoV.Account.Eq(reqdata.Email)).
		Where(daoV.UseType.Eq(3)).Where(daoV.VerifyCode.Eq(reqdata.VerifyCode)).Delete()
	if r.RowsAffected == 0 {
		if ctx.RespErr(fmt.Errorf("验证码不正确"), &errcode) {
			return
		}
	}
	logs.Info("verifyUserEmail delete ", reqdata.Email, reqdata.VerifyCode)
	_, err = userDb.Where(userDao.UserID.Eq(int32(token.UserId))).Update(userDao.BindEmail, reqdata.Email)
	if userData.Email == "" {
		_, err = userDb.Where(userDao.UserID.Eq(int32(token.UserId))).Update(userDao.Email, reqdata.Email)
	}
	if err != nil {
		logs.Error(err)
		if ctx.RespErr(err, &errcode) {
			return
		}
	}

	c.PushCustomEvent(fmt.Sprint(userData.UserID), "bind_email", map[string]interface{}{
		"account_id": userData.UserID,
		"email":      reqdata.Email,
		"phone":      userData.PhoneNum,
		"type":       "bind_email",
	})
}
func (c *UserController) verifyUserPhone(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		PhoneNum   string
		VerifyCode string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if !c.Sms.VerifyCode(reqdata.PhoneNum, reqdata.VerifyCode) {
		logs.Error("校验手机验证错误: PhoneNum=%s, VerifyCode=%s", reqdata.PhoneNum, reqdata.VerifyCode)
		ctx.RespErrString(true, &errcode, "验证码不正确")
		return
	}
	logs.Debug("校验手机验证成功: PhoneNum=%s, VerifyCode=%s", reqdata.PhoneNum, reqdata.VerifyCode)
	userDao := server.DaoxHashGame().XUser
	userDb := userDao.WithContext(ctx.Gin())

	userData, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		ctx.RespErrString(true, &errcode, "账号不存在")
		return
	}
	if len(userData.BindPhoneNum) > 2 {
		ctx.RespErrString(true, &errcode, "您已绑定过手机号")
		return
	}
	tmInfo, err := userDb.Where(userDao.PhoneNum.Eq(reqdata.PhoneNum)).First()
	if err == nil && tmInfo.SellerID != int32(token.SellerId) && tmInfo.UserID != int32(token.UserId) {
		ctx.RespErrString(true, &errcode, "该手机号已占用")
		return
	}
	_, err = userDb.Where(userDao.BindPhoneNum.Eq(reqdata.PhoneNum)).First()
	if err == nil {
		ctx.RespErrString(true, &errcode, "该手机号已占用")
		return
	}
	server.Db().Conn().Exec("update x_user set BindPhoneNum = ? where UserId = ?", reqdata.PhoneNum, token.UserId)
	if userData.PhoneNum == "" {
		server.Db().Conn().Exec("update x_user set PhoneNum = ? where UserId = ?", reqdata.PhoneNum, token.UserId)
	}
	ctx.RespOK()
}

// 找回密码
func (c *UserController) retrievePassword(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId   int    `validate:"required"`
		Email      string `validate:"required"`
		VerifyCode string `validate:"required"`
		Host       string `validate:"required"`
		Password   string `validate:"required"` //密码(md5)
		PasswordV2 string `validate:"required"` //密码(RSA)
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, reqdata.Host)

	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.UserID).Where(userTb.Email.Eq(reqdata.Email)).First()
	if err != nil {
		logs.Error("UserController retrievePassword", err)
		errmsg := "系统错误,请稍后再试"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			//errmsg = "账号不存在"
			errmsg = "您未绑定过任何验证方式,请联系客服处理"
		}
		ctx.RespErrString(true, &errcode, errmsg)
		return
	}

	r, _ := server.Db().Conn().Exec("delete from x_verify where SellerId = ? and ChannelId = ? and Account = ? and UseType = ? and VerifyCode = ?", SellerId, 1, reqdata.Email, 2, reqdata.VerifyCode)
	rows, _ := r.RowsAffected()
	logs.Info("retrievePassword delete ", reqdata.Email, reqdata.VerifyCode)
	if rows == 0 {
		ctx.RespErrString(true, &errcode, "验证码不正确")
		return
	}

	reqdata.Password = utils.Md5V(reqdata.Password)
	var PasswordV2 string
	PasswordV2, err = utils.ConvertUserPassword(reqdata.PasswordV2)
	if err != nil {
		logs.Error("retrievePassword ConvertUserPassword err:", err)
	}

	data := map[string]interface{}{
		"Password":           reqdata.Password,
		"PasswordV2":         PasswordV2,
		"UpdatePasswordTime": time.Now(),
	}
	row, err := userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(data)
	if err != nil || row.RowsAffected == 0 {
		logs.Error("retrievePassword Update", err)
		ctx.RespErrString(true, &errcode, "重制密码失败")
		return
	}

	c.PushCustomEvent(fmt.Sprint(user.UserID), "password_recovery", map[string]interface{}{
		"account_id": user.UserID,
		"email":      user.Email,
		"phone":      user.PhoneNum,
		"type":       "faild_password_recovery",
	})

	ctx.RespOK()
}

func (c *UserController) checkAddress(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Address   string
		SellerId  int32
		ChannelId int32
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	ok := checkAddress(ctx.Gin(), []string{reqdata.Address})
	ctx.RespOK(ok)
}

func (c *UserController) totalLiushui(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		UserId int32
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	total := GetUserTotalLiushui(reqdata.UserId)
	ctx.Put("TotalLiushui", total)
	ctx.RespOK()
}

func (c *UserController) get_user_wallet(ctx *abugo.AbuHttpContent) {
	defer recover()
	token := server.GetToken(ctx)
	presult, _ := server.Db().Query(`select RechargeAddressTron, RechargeAddressEth from x_user where UserId = ?`, []interface{}{token.UserId})

	RechargeAddressTron := ""
	RechargeAddressEth := ""
	if (*presult)[0]["RechargeAddressTron"] != nil {
		RechargeAddressTron = (*presult)[0]["RechargeAddressTron"].(string)
	}
	if (*presult)[0]["RechargeAddressEth"] != nil {
		RechargeAddressEth = (*presult)[0]["RechargeAddressEth"].(string)
	}
	if len(RechargeAddressTron) == 0 {
		for {
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 1 limit 1"
			server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressTron)
			if len(RechargeAddressTron) == 0 {
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 1"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressTron)
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "update x_user set RechargeAddressTron = ? where UserId = ?"
				server.Db().Conn().Exec(sql, RechargeAddressTron, token.UserId)
				break
			}
		}
	}
	if len(RechargeAddressEth) == 0 {
		for {
			sql := "select Address from x_address_pool where SellerId = ? and UserId is null and AddType = 2 limit 1"
			server.Db().QueryScan(sql, []interface{}{token.SellerId}, &RechargeAddressEth)
			if len(RechargeAddressEth) == 0 {
				break
			}
			sql = "update x_address_pool set UserId = ? where Address = ? and UserId is null and AddType = 2"
			r, _ := server.Db().Conn().Exec(sql, token.UserId, RechargeAddressEth)
			RowsAffected, _ := r.RowsAffected()
			if RowsAffected == 1 {
				sql = "update x_user set RechargeAddressEth = ? where UserId = ?"
				server.Db().Conn().Exec(sql, RechargeAddressEth, token.UserId)
				break
			}
		}
	}

	// 获取用户余额
	balance := server.GetBalance(token.UserId)

	data := map[string]interface{}{
		"tron":    RechargeAddressTron,
		"eth":     RechargeAddressEth,
		"balance": balance,
	}

	ctx.RespOK(data)
}

func (c *UserController) bonus_infos(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	tb := server.DaoxHashGame().XBonusTaskUser
	db := tb.WithContext(context.Background())
	total := decimal.NewFromFloat(0)
	finds, err := db.Where(tb.UserID.Eq(int32(token.UserId))).Where(tb.State.In(1, 3)).Find()
	if err != nil {
		ctx.Put("total", total.InexactFloat64())
		ctx.RespOK()
		return
	}
	for _, v := range finds {
		total = total.Add(decimal.NewFromFloat(v.Bonus))
	}
	ctx.Put("total", total.InexactFloat64())
	ctx.Put("data", finds)
	ctx.RespOK()
}

func (c *UserController) set_login_password_v2(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		OldPassword   string `validate:"required"`
		NewPassword   string `validate:"required"`
		NewPasswordV2 string `validate:"required"`
		Account       string `validate:"required"`
	}
	//获取请求参数
	reqdata := RequestData{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 对密码进行md5
	reqdata.NewPassword = utils.Md5V(reqdata.NewPassword)
	reqdata.OldPassword = utils.Md5V(reqdata.OldPassword)

	var PasswordV2 string
	PasswordV2, err = utils.ConvertUserPassword(reqdata.NewPasswordV2)
	if err != nil {
		logs.Error("set_login_password_v2 ConvertUserPassword err:", err)
	}

	//获取用户信息
	userDao := server.DaoxHashGame().XUser
	user, err := userDao.WithContext(ctx.Gin()).Where(userDao.Account.Eq(reqdata.Account)).First()
	if ctx.RespErr(err, &errcode) {
		return
	}
	// 验证旧密码
	if user.Password != reqdata.OldPassword {
		ctx.RespErrString(true, &errcode, "原登录密码不正确")
		return
	}
	// 更新密码
	data := map[string]interface{}{
		"Password":           reqdata.NewPassword,
		"PasswordV2":         PasswordV2,
		"UpdatePasswordTime": time.Now(),
	}
	_, err = userDao.WithContext(ctx.Gin()).Where(userDao.Account.Eq(reqdata.Account)).Updates(data)
	if ctx.RespErr(err, &errcode) {
		return
	}
	if err == nil {
		c.PushCustomEvent(fmt.Sprint(user.UserID), "change_password", map[string]interface{}{
			"userid": user.UserID, // 用户ID
			"status": "successful",
			"remark": "change_password",
			"email":  user.Email,
			"phone":  user.PhoneNum,
			"type":   "change_password",
		})
	}
	ctx.RespOK()
}

func checkAddress(ctx context.Context, address []string) bool {
	// 检查是否符合体验金发放条件 满足1或2即可
	var ok1, ok2 bool
	// 1. 钱包创建时间超过2个月 && 钱包余额大于300U && 有活跃交易记录，每周1条转账记录 && 交易量大于500U
	//ok1 = checkAddressTronData(address)
	// 2. 检查的钱包地址不在数据库中
	if !ok1 {
		statAddrDao := server.DaoxHashGame().XUserStatAddress
		_, err := statAddrDao.WithContext(ctx).
			//Where(statAddrDao.ChannelID.Eq(int32(reqdata.ChannelId))).
			Where(statAddrDao.FromAddress.In(address...)).
			First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		walletDao := server.DaoxHashGame().XUserWallet
		walletDb := walletDao.WithContext(ctx)
		walletDb1 := walletDb.Where(walletDao.Address.In(address...))
		_, err = walletDb1.First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		withdrawDao := server.DaoxHashGame().XWithdrawAddress
		_, err = withdrawDao.WithContext(ctx).
			Where(withdrawDao.Address.In(address...)).
			First()
		if err == nil || !errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		ok2 = true
	}
	return ok1 || ok2
}

func checkAddressTronData(address string) bool {
	// 钱包创建时间超过2个月 && 钱包余额大于300U && 有活跃交易记录，每周1条转账记录 && 交易量大于500U
	tron := tronapi.Client()
	creatAt, err := tron.GetCreatTime(address)
	if err != nil || !creatAt.Before(time.Now().Add(-time.Hour*24*30*2)) {
		return false
	}
	balance, err := tron.GetTotalBalanceInUSD(address)
	if err != nil || !balance.GreaterThan(decimal.NewFromInt(300)) {
		return false
	}
	totalTransfer := decimal.NewFromInt(0)
	lastTime := time.UnixMilli(0)
	rsp, err := tron.Transfers(0, 5000, address)
	if err != nil {
		return false
	}
	if rsp.Total == 0 {
		return false
	}
	for _, v := range rsp.TokenTransfers {
		blockTime := time.UnixMilli(v.BlockTs)
		y1, w1 := blockTime.ISOWeek()
		if !lastTime.IsZero() {
			y2, w2 := lastTime.Add(-time.Hour * 24 * 7).ISOWeek()
			y := y2 - y1
			w := w2 - w1
			if y > 0 || (y == 0 && w > 0) {
				return false
			}
		}
		lastTime = blockTime
		if v.TokenInfo.TokenType == "trc20" && v.TokenInfo.TokenName == "Tether USD" {
			quantity, _ := strconv.ParseInt(v.Quant, 10, 64)
			totalTransfer = totalTransfer.Add(decimal.New(quantity, -v.TokenInfo.TokenDecimal))
		}
	}
	if !totalTransfer.GreaterThan(decimal.NewFromInt(500)) {
		return false
	}

	return true
}

// 检查地址关联
// returns: 通过:true 不通过:false
func checkAddressRelation(address string) (bool, error) {
	reqData := tronscango.AddressTransferGetListReq{AddressList: address}
	list, err := tronscango.GetClient().AddressTransfer_GetList(reqData)
	if err != nil {
		logs.Error("AddressTransfer_GetList err:", err)
		return false, err
	}
	var addrList []string
	for _, v := range list {
		addrList = append(addrList, v.Address)
	}
	return checkAddress(context.Background(), addrList), nil
}

// checkTyjRestrictions 检查体验金领取条件
// 包括IP限制、设备ID限制和钱包地址限制
func checkTyjRestrictions(ctx *abugo.AbuHttpContent, user *model2.XUser, robotGuide interface{}, amount float64, symbol string, errcode *int) error {
	// 根据传入的robotGuide类型获取相关属性
	var isIPRestriction, isDeviceRestriction, isWalletRestriction int32
	var ipWhitelist string
	var robotId int64

	// 判断robotGuide的类型并获取相应的属性
	switch guide := robotGuide.(type) {
	case *model2.XTgRobotGuide:
		isIPRestriction = guide.IsIPRestriction
		isDeviceRestriction = guide.IsDeviceRestriction
		isWalletRestriction = guide.IsWalletRestriction
		ipWhitelist = guide.IPWhitelist
		robotId = guide.ID
	case *model2.XRobotConfig:
		isIPRestriction = guide.IsIPRestriction
		isDeviceRestriction = guide.IsDeviceRestriction
		isWalletRestriction = guide.IsWalletRestriction
		ipWhitelist = guide.IPWhitelist
		robotId = guide.ID
	default:
		return errors.New("不支持的机器人类型")
	}

	// 检查IP限制
	if isIPRestriction == 1 && strings.Index(ipWhitelist, user.LoginIP) == -1 {
		userXUser := server.DaoxHashGame().XUser
		LoginIPCount, _ := userXUser.WithContext(ctx.Gin()).Where(userXUser.LoginIP.Eq(user.LoginIP)).Count()
		if LoginIPCount > 1 {
			ctx.RespErrCodeString(true, 10000, "不符合领取条件")
			if symbol == "trx" {
				err := createTrxTyjRejectOrder(user, "IP重复,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			} else {
				err := createUsdtTyjRejectOrder(user, "IP重复,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			}

			memo := fmt.Sprintf("account:%s,amount:%f,ip:%s", user.Account, amount, user.LoginIP)
			go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, robotId, user.TgChatID, stat.ActionType_FailWithIP, memo, false, time.Time{})
			return errors.New("IP限制")
		}
	}

	// 检查设备ID限制
	if isDeviceRestriction == 1 {
		userXUser := server.DaoxHashGame().XUser
		DeviceIDCount, _ := userXUser.WithContext(ctx.Gin()).Where(userXUser.LoginDeviceID.Eq(user.LoginDeviceID)).Count()
		if DeviceIDCount > 1 {
			ctx.RespErrCodeString(true, 10000, "不符合领取条件")
			if symbol == "trx" {
				err := createTrxTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			} else {
				err := createUsdtTyjRejectOrder(user, "设备ID重复,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			}

			memo := fmt.Sprintf("account:%s,amount:%f,deviceId:%s", user.Account, amount, user.LoginDeviceID)
			go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, robotId, user.TgChatID, stat.ActionType_FailWithDeviceId, memo, false, time.Time{})
			return errors.New("设备ID限制")
		}
	}

	// 检查钱包地址限制
	if isWalletRestriction == 1 {
		ok, err := checkAddressRelation(user.Address)
		if err != nil {
			ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
			return err
		}
		if !ok {
			ctx.RespErrCodeString(true, 10000, "不符合领取条件")

			if symbol == "trx" {
				err := createTrxTyjRejectOrder(user, "地址关联,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			} else {
				err := createUsdtTyjRejectOrder(user, "地址关联,拒绝发放", amount)
				if err != nil {
					ctx.RespErrString(true, errcode, "系统错误,请稍后再试.")
					return err
				}
			}

			memo := fmt.Sprintf("account:%s,amount:%f,address:%s", user.Account, amount, user.Address)
			go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, robotId, user.TgChatID, stat.ActionType_FailWithAddress, memo, false, time.Time{})
			return errors.New("钱包地址限制")
		}
	}

	return nil
}

func (c *UserController) get_shangzhuang_balance(ctx *abugo.AbuHttpContent) {
	defer recover()
	errcode := 0
	token := server.GetToken(ctx)
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())
	user, err := userDb.Select(userTb.BankerAmount).
		Where(userTb.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("get_shangzhuang_balance user err", err)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	ctx.Put("BankerAmount", user.BankerAmount)
	ctx.RespOK()
}

// 联系客服点击埋点
func (c *UserController) concat_cs(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	xUser := server.DaoxHashGame().XUser
	user, err := xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("concat_cs err:", err)
		return
	}
	tgBotId, _ := GetTgRobotByToken(user.TgRobotToken)
	go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgBotId, user.TgChatID, stat.ActionType_ConcatCs, "", user.IsInResourceDb == 1, time.Time{})
	ctx.RespOK()
}

// 首充按钮点击埋点
func (c *UserController) first_charge_click(ctx *abugo.AbuHttpContent) {
	token := server.GetToken(ctx)
	xUser := server.DaoxHashGame().XUser
	user, err := xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).First()
	if err != nil {
		logs.Error("first_charge_click err:", err)
		return
	}
	tgBotId, _ := GetTgRobotByToken(user.TgRobotToken)
	go stat.TgRobotStat.AddLogAction(context.Background(), user.SellerID, user.ChannelID, tgBotId, user.TgChatID, stat.ActionType_FirstchargeClick, "", user.IsInResourceDb == 1, time.Time{})
	ctx.RespOK()
}

func (c *UserController) attrReporting(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Kwai string
		Bigo string
		Fbc  string
		Fbp  string
	}
	reqdata := RequestData{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	xUser := server.DaoxHashGame().XUser
	user, _ := xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).First()
	if user == nil {
		ctx.RespErrString(true, &errcode, "用户不存在")
		return
	}

	if reqdata.Kwai != "" {
		_, err = xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).Update(xUser.Kwai, reqdata.Kwai)
	}

	if reqdata.Bigo != "" {
		_, err = xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).Update(xUser.Bigo, reqdata.Bigo)
	}

	if reqdata.Fbc != "" {
		_, err = xUser.WithContext(ctx.Gin()).Where(xUser.UserID.Eq(int32(token.UserId))).Update(xUser.Fbc, reqdata.Fbc)
	}

	if reqdata.Fbp != "" {
		xUserMore := server.DaoxHashGame().XUserMore
		userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).First()
		if userMoreInfo != nil {
			_, err = xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).Update(xUserMore.Fbp, reqdata.Fbp)
		} else {
			xUserMore.WithContext(ctx.Gin()).Create(&model2.XUserMore{
				UserID: int32(token.UserId),
				Fbp:    reqdata.Fbp,
			})

		}
	}

	if err != nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	ctx.RespOK()

}

// 获取用户总流水
func GetUserTotalLiushui(userId int32) (total decimal.Decimal) {
	var liuSui active.FirstDepositGiftLiuSui
	userDaillyTb := server.DaoxHashGame().XUserDailly
	userDaillyDb := server.DaoxHashGame().XUserDailly.WithContext(context.Background())
	err := userDaillyDb.Select(userDaillyTb.LiuSuiUsdt.Sum().As(userDaillyTb.LiuSuiUsdt.ColumnName().String()),
		userDaillyTb.LiuSuiLottery.Sum().As(userDaillyTb.LiuSuiLottery.ColumnName().String()),
		userDaillyTb.LiuSuiQiPai.Sum().As(userDaillyTb.LiuSuiQiPai.ColumnName().String()),
		userDaillyTb.LiuSuiDianZhi.Sum().As(userDaillyTb.LiuSuiDianZhi.ColumnName().String()),
		userDaillyTb.LiuSuiXiaoYouXi.Sum().As(userDaillyTb.LiuSuiXiaoYouXi.ColumnName().String()),
		userDaillyTb.LiuSuiLive.Sum().As(userDaillyTb.LiuSuiLive.ColumnName().String()),
		userDaillyTb.LiuSuiSport.Sum().As(userDaillyTb.LiuSuiSport.ColumnName().String()),
		//userDaillyTb.LiuSuiTexas.Sum().As(userDaillyTb.LiuSuiTexas.ColumnName().String()),
	).Where(userDaillyTb.UserID.Eq(userId)).Scan(&liuSui)
	if err != nil {
		logs.Error("GetUserTotalLiushui err:", err)
		return total
	}
	total = liuSui.LiuSuiUsdt.Add(liuSui.LiuSuiLottery).
		Add(liuSui.LiuSuiQiPai).
		Add(liuSui.LiuSuiDianZhi).
		Add(liuSui.LiuSuiXiaoYouXi).
		Add(liuSui.LiuSuiLive).
		Add(liuSui.LiuSuiSport)
	//Add(liuSui.LiuSuiTexas)
	return total
}

type ActiveAddUseBalancerInfo struct {
	UserId            int32
	ActiveName        string  // 活动备注
	RealAmount        float64 // 金额
	WithdrawLiuSuiAdd float64 // 流水
	BalanceCReason    int     // 帐变类型
}

func ActiveAddUseBalancer(tx *dao.Query, dataInfo ActiveAddUseBalancerInfo) error {
	defer recover()
	if tx == nil {
		tx = server.DaoxHashGame()
	}
	date := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	rerr := tx.Transaction(func(tx *dao.Query) error {
		userTb := server.DaoxHashGame().XUser
		userFindDb := server.DaoxHashGame().XUser.WithContext(nil)
		user, err := userFindDb.Where(userTb.UserID.Eq(dataInfo.UserId)).First()
		if err != nil {
			logs.Error("ActiveAddUseBalancer find user err", err)
			return err
		}
		// 玩家日统计
		userDaillyTb := tx.XUserDailly
		userDaillyDb := tx.XUserDailly.WithContext(nil)
		_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				userDailly := &model2.XUserDailly{
					SellerID:   user.SellerID,
					ChannelID:  user.ChannelID,
					UserID:     user.UserID,
					RecordDate: date,
				}
				err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
				if err != nil {
					logs.Error("ActiveAddUseBalancer Create userDailly err", err)
					return err
				}
			} else {
				logs.Error(err)
				return err
			}
		}
		userDb := tx.XUser.WithContext(nil)
		if user.WithdrawLiuSui > user.TotalLiuSui {
			_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
				"WithdrawLiuSui": gorm.Expr("WithdrawLiuSui + ?", dataInfo.WithdrawLiuSuiAdd),
			})
		} else {
			_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
				"WithdrawLiuSui": dataInfo.WithdrawLiuSuiAdd,
				"TotalLiuSui":    0,
			})
		}
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates user err", err)
			return err
		}

		amountChangeLog := &model2.XAmountChangeLog{
			UserID:       user.UserID,
			BeforeAmount: user.Amount,
			Amount:       dataInfo.RealAmount,
			AfterAmount:  user.Amount + dataInfo.RealAmount,
			Reason:       int32(dataInfo.BalanceCReason),
			Memo:         dataInfo.ActiveName,
			SellerID:     user.SellerID,
			ChannelID:    user.ChannelID,
		}
		amountChangeLogDB := tx.XAmountChangeLog.WithContext(nil)
		err = amountChangeLogDB.Create(amountChangeLog)
		if err != nil {
			logs.Error("ActiveAddUseBalancer Create amountChangeLog err", err)
			return err
		}

		_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).Updates(map[string]any{
			"TotalCaiJin": gorm.Expr("TotalCaiJin + ?", dataInfo.RealAmount),
		})
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates userDailly err", err)
			return err
		}
		vipInfoTb := tx.XVipInfo
		vipInfoDb := tx.XVipInfo.WithContext(nil)
		_, err = vipInfoDb.Where(vipInfoTb.UserID.Eq(user.UserID)).Updates(map[string]any{
			"CaiJin": gorm.Expr("CaiJin + ?", dataInfo.RealAmount),
		})
		if err != nil {
			logs.Error("ActiveAddUseBalancer Updates vipInfo err", err)
			return err
		}
		if dataInfo.RealAmount > 0 {
			caijingDetailDb := tx.XCaijingDetail.WithContext(nil)
			caijingDetail := &model2.XCaijingDetail{
				UserID:     user.UserID,
				SType:      dataInfo.ActiveName,
				Symbol:     "usdt",
				Amount:     dataInfo.RealAmount,
				CSGroup:    user.CSGroup,
				CSID:       user.CSID,
				TopAgentID: user.TopAgentID,
			}
			err = caijingDetailDb.Create(caijingDetail)
			if err != nil {
				logs.Error("ActiveAddUseBalancer Create caijingDetail err", err)
				return err
			}
		}
		return nil
	})
	return rerr
}

func (c *UserController) getIdToken(ctx *abugo.AbuHttpContent) {
	// Implement the logic for getIdToken here
	type RequestData struct {
		Id        int64
		FirstName string
		LastName  string
		UserName  string
		BotId     string
	}
	reqdata := RequestData{}
	errcode := 0
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	bot := server.DaoxHashGame().XRobotConfig
	botDb := bot.WithContext(ctx.Gin())
	botInfo, _ := botDb.Where(bot.Token.Like("%" + reqdata.BotId + "%")).First()

	oldBot := server.DaoxHashGame().XTgRobotGuide
	oldBotDb := oldBot.WithContext(ctx.Gin())
	oldBotInfo, _ := oldBotDb.Where(oldBot.TgRobotToken.Like("%" + reqdata.BotId + "%")).First()

	if botInfo == nil && oldBotInfo == nil {
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	if botInfo != nil {
		idToken := MakeIdToken(botInfo.ID, botInfo.Token, reqdata.Id, reqdata.FirstName, reqdata.LastName, reqdata.UserName)
		// logs.Info("getIdToken idToken", idToken)
		ctx.RespOK(idToken)
		return
	}

	if oldBotInfo != nil {
		idToken := MakeIdToken(oldBotInfo.ID, oldBotInfo.TgRobotToken, reqdata.Id, reqdata.FirstName, reqdata.LastName, reqdata.UserName)
		// logs.Info("getIdToken idToken", idToken)
		ctx.RespOK(idToken)
		return
	}
}

// validateWhatsAppToken 验证WhatsApp访问令牌并获取用户信息
func (c *UserController) validateWhatsAppToken(accessToken, appId string) (userId, email, nickName string, err error) {
	// WhatsApp Business API 验证访问令牌
	// 首先验证令牌有效性
	verifyURL := fmt.Sprintf("https://graph.facebook.com/v18.0/me?access_token=%s", accessToken)

	resp, err := http.Get(verifyURL)
	if err != nil {
		return "", "", "", fmt.Errorf("验证WhatsApp令牌失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("WhatsApp令牌验证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取WhatsApp响应失败: %v", err)
	}

	var tokenInfo struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	err = json.Unmarshal(body, &tokenInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析WhatsApp响应失败: %v", err)
	}

	// 获取用户详细信息
	userURL := fmt.Sprintf("https://graph.facebook.com/v18.0/%s?fields=id,name,email&access_token=%s", tokenInfo.ID, accessToken)

	userResp, err := http.Get(userURL)
	if err != nil {
		return "", "", "", fmt.Errorf("获取WhatsApp用户信息失败: %v", err)
	}
	defer userResp.Body.Close()

	if userResp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("获取WhatsApp用户信息失败，状态码: %d", userResp.StatusCode)
	}

	userBody, err := io.ReadAll(userResp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取WhatsApp用户信息失败: %v", err)
	}

	var userInfo struct {
		ID    string `json:"id"`
		Name  string `json:"name"`
		Email string `json:"email"`
	}

	err = json.Unmarshal(userBody, &userInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析WhatsApp用户信息失败: %v", err)
	}

	return userInfo.ID, userInfo.Email, userInfo.Name, nil
}

// validateLineToken 验证Line访问令牌并获取用户信息
func (c *UserController) validateLineToken(accessToken, channelId string) (userId, email, nickName string, err error) {
	// Line Login API 验证访问令牌
	verifyURL := "https://api.line.me/oauth2/v2.1/verify"

	// 创建POST请求验证令牌
	data := url.Values{}
	data.Set("access_token", accessToken)

	resp, err := http.PostForm(verifyURL, data)
	if err != nil {
		return "", "", "", fmt.Errorf("验证Line令牌失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("Line令牌验证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Line响应失败: %v", err)
	}

	var tokenInfo struct {
		Scope     string `json:"scope"`
		ClientId  string `json:"client_id"`
		ExpiresIn int    `json:"expires_in"`
	}

	err = json.Unmarshal(body, &tokenInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Line令牌响应失败: %v", err)
	}

	// 验证客户端ID是否匹配
	if tokenInfo.ClientId != channelId {
		return "", "", "", fmt.Errorf("Line客户端ID不匹配")
	}

	// 获取用户信息
	userURL := "https://api.line.me/v2/profile"

	req, err := http.NewRequest("GET", userURL, nil)
	if err != nil {
		return "", "", "", fmt.Errorf("创建Line用户信息请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{}
	userResp, err := client.Do(req)
	if err != nil {
		return "", "", "", fmt.Errorf("获取Line用户信息失败: %v", err)
	}
	defer userResp.Body.Close()

	if userResp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("获取Line用户信息失败，状态码: %d", userResp.StatusCode)
	}

	userBody, err := io.ReadAll(userResp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Line用户信息失败: %v", err)
	}

	var userInfo struct {
		UserId      string `json:"userId"`
		DisplayName string `json:"displayName"`
		PictureUrl  string `json:"pictureUrl"`
	}

	err = json.Unmarshal(userBody, &userInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Line用户信息失败: %v", err)
	}

	// Line API 不直接提供邮箱，使用用户ID作为邮箱的一部分
	email = fmt.Sprintf("%<EMAIL>", userInfo.UserId)

	return userInfo.UserId, email, userInfo.DisplayName, nil
}

// validateFacebookToken 验证Facebook访问令牌并获取用户信息
func (c *UserController) validateFacebookToken(accessToken, appId string) (userId, email, nickName string, err error) {
	// Facebook Graph API 验证访问令牌
	verifyURL := fmt.Sprintf("https://graph.facebook.com/me?access_token=%s&fields=id,name,email", accessToken)

	resp, err := http.Get(verifyURL)
	if err != nil {
		return "", "", "", fmt.Errorf("验证Facebook令牌失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("Facebook令牌验证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Facebook响应失败: %v", err)
	}

	var userInfo struct {
		ID    string `json:"id"`
		Name  string `json:"name"`
		Email string `json:"email"`
	}

	err = json.Unmarshal(body, &userInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Facebook用户信息失败: %v", err)
	}

	// 验证令牌是否属于正确的应用
	tokenURL := fmt.Sprintf("https://graph.facebook.com/app?access_token=%s", accessToken)

	tokenResp, err := http.Get(tokenURL)
	if err != nil {
		return "", "", "", fmt.Errorf("验证Facebook应用失败: %v", err)
	}
	defer tokenResp.Body.Close()

	if tokenResp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("验证Facebook应用失败，状态码: %d", tokenResp.StatusCode)
	}

	tokenBody, err := io.ReadAll(tokenResp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Facebook应用信息失败: %v", err)
	}

	var appInfo struct {
		ID string `json:"id"`
	}

	err = json.Unmarshal(tokenBody, &appInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Facebook应用信息失败: %v", err)
	}

	// 验证应用ID是否匹配
	if appInfo.ID != appId {
		return "", "", "", fmt.Errorf("Facebook应用ID不匹配")
	}

	// 如果没有邮箱，使用用户ID生成一个
	if userInfo.Email == "" {
		userInfo.Email = fmt.Sprintf("%<EMAIL>", userInfo.ID)
	}

	return userInfo.ID, userInfo.Email, userInfo.Name, nil
}

// validateInstagramToken 验证Instagram访问令牌并获取用户信息
func (c *UserController) validateInstagramToken(accessToken, appId string) (userId, email, nickName string, err error) {
	// Instagram Basic Display API 获取用户信息
	userURL := fmt.Sprintf("https://graph.instagram.com/me?fields=id,username&access_token=%s", accessToken)

	resp, err := http.Get(userURL)
	if err != nil {
		return "", "", "", fmt.Errorf("验证Instagram令牌失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("Instagram令牌验证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Instagram响应失败: %v", err)
	}

	var userInfo struct {
		ID       string `json:"id"`
		Username string `json:"username"`
	}

	err = json.Unmarshal(body, &userInfo)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Instagram用户信息失败: %v", err)
	}

	// 验证令牌有效性 - 获取令牌信息
	tokenURL := fmt.Sprintf("https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=%s&access_token=%s", appId, accessToken)

	tokenResp, err := http.Get(tokenURL)
	if err != nil {
		// 如果无法验证令牌，但用户信息获取成功，则继续
		logs.Error("Instagram令牌验证警告:", err)
	} else {
		defer tokenResp.Body.Close()
		if tokenResp.StatusCode != 200 {
			logs.Error("Instagram令牌验证警告，状态码:", tokenResp.StatusCode)
		}
	}

	// Instagram API 不直接提供邮箱，使用用户名生成邮箱
	email = fmt.Sprintf("%<EMAIL>", userInfo.Username)

	return userInfo.ID, email, userInfo.Username, nil
}

// validateTwitterToken 验证X(Twitter)访问令牌并获取用户信息
func (c *UserController) validateTwitterToken(accessToken, appId string) (userId, email, nickName string, err error) {
	// Twitter API v2 获取用户信息
	userURL := "https://api.twitter.com/2/users/me?user.fields=id,name,username,email"

	req, err := http.NewRequest("GET", userURL, nil)
	if err != nil {
		return "", "", "", fmt.Errorf("创建Twitter用户信息请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", "", fmt.Errorf("验证Twitter令牌失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", "", "", fmt.Errorf("Twitter令牌验证失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", "", fmt.Errorf("读取Twitter响应失败: %v", err)
	}

	var response struct {
		Data struct {
			ID       string `json:"id"`
			Name     string `json:"name"`
			Username string `json:"username"`
			Email    string `json:"email"`
		} `json:"data"`
	}

	err = json.Unmarshal(body, &response)
	if err != nil {
		return "", "", "", fmt.Errorf("解析Twitter用户信息失败: %v", err)
	}

	userInfo := response.Data

	// 如果没有邮箱，使用用户名生成邮箱
	if userInfo.Email == "" {
		userInfo.Email = fmt.Sprintf("%<EMAIL>", userInfo.Username)
	}

	return userInfo.ID, userInfo.Email, userInfo.Name, nil
}

// getLoginTypeString 根据AccountType获取登录类型字符串
func (c *UserController) getLoginTypeString(accountType int) string {
	switch accountType {
	case 1:
		return "account_login" // 账号登录
	case 2:
		return "phone_login" // 手机号登录
	case 3:
		return "email_login" // 邮箱登录
	case 4:
		return "google_login" // Google登录
	case 5:
		return "telegram_login" // TG机器人自动注册
	case 8:
		return "whatsapp_login" // WhatsApp登录
	case 9:
		return "line_login" // Line登录
	case 10:
		return "instagram_login" // Instagram登录
	case 11:
		return "twitter_login" // X(Twitter)登录
	case 12:
		return "facebook_login" // Facebook登录
	default:
		return "unknown_login" // 未知登录方式
	}
}

// gameSave 保存玩家游戏
func (c *UserController) gameSave(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Brand  string `json:"Brand" validate:"required"`  // 游戏品牌
		GameId string `json:"GameId" validate:"required"` // 游戏ID
	}

	errCode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errCode) {
		return
	}

	// 获取用户token
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespNoAuth(2, "未登录或登录已过期")
		return
	}

	// 检查是否已存在该游戏记录
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	where.Add("and", "Brand", "=", reqdata.Brand, nil)
	where.Add("and", "GameId", "=", reqdata.GameId, nil)

	existingData, _ := server.Db().Table("x_user_games").Where(where).GetOne()

	if existingData != nil {
		// 如果记录已存在，更新UpdateTime
		updateData := map[string]interface{}{
			"UpdateTime": time.Now(),
		}
		_, err = server.Db().Table("x_user_games").Where(where).Update(updateData)
		if ctx.RespErr(err, &errCode) {
			return
		}
	} else {
		// 如果记录不存在，插入新记录
		insertData := map[string]interface{}{
			"UserId":     token.UserId,
			"Brand":      reqdata.Brand,
			"GameId":     reqdata.GameId,
			"CreateTime": time.Now(),
			"UpdateTime": time.Now(),
		}
		_, err = server.Db().Table("x_user_games").Insert(insertData)
		if ctx.RespErr(err, &errCode) {
			return
		}
	}

	go func() {
		// 这里插入一条下注IP到x_user_more表中，如果已存在则更新ip
		xUserMore := server.DaoxHashGame().XUserMore
		userMoreInfo, _ := xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).First()
		if userMoreInfo != nil {
			_, err = xUserMore.WithContext(ctx.Gin()).Where(xUserMore.UserID.Eq(int32(token.UserId))).Update(xUserMore.BetIP, ctx.GetIp())
		} else {
			xUserMore.WithContext(ctx.Gin()).Create(&model2.XUserMore{
				UserID: int32(token.UserId),
				BetIP:  ctx.GetIp(),
			})
		}
	}()

	ctx.RespOK("保存成功")
}

// recentGame 玩家游戏列表
func (c *UserController) recentGame(ctx *abugo.AbuHttpContent) {
	errCode := 0

	// 获取用户token
	token := server.GetToken(ctx)
	if token == nil {
		ctx.RespNoAuth(2, "未登录或登录已过期")
		return
	}

	// 获取SellerId和ChannelId
	host := ctx.Host()
	_, sellerId := server.GetChannel(ctx, host)

	// 查询用户最近30个游戏记录
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)

	userGames, err := server.Db().Table("x_user_games").Where(where).OrderBy("UpdateTime desc").Limit(30).GetList()
	if ctx.RespErr(err, &errCode) {
		return
	}

	if userGames == nil || len(*userGames) == 0 {
		ctx.RespOK([]interface{}{})
		return
	}

	// 获取在线人数配置
	var setOnlineCntData SetOnlineCntData
	// 从redis hash list中获取配置数据
	redisData := server.CRedis().HGet("CONFIG", "SET_ONLINE_CNT")
	if redisData != nil {
		// redisData 是 []uint8 类型，将其转换为字符串
		dataBytes := redisData.([]uint8)
		// 将字节数据转为字符串
		dataStr := string(dataBytes)

		// 反序列化为 setOnlineCntData
		if err := json.Unmarshal([]byte(dataStr), &setOnlineCntData); err != nil {
			fmt.Printf("JSON 反序列化失败: %v\n", err)
		} else {
			fmt.Printf("在线人数配置: Display=%d, DefaultMin=%d, DefaultMax=%d\n",
				setOnlineCntData.Display, setOnlineCntData.DefaultMin, setOnlineCntData.DefaultMax)
			fmt.Printf("配置数据项数量: %d\n", len(setOnlineCntData.Data))
			for i, item := range setOnlineCntData.Data {
				fmt.Printf("配置项%d: GameType=%d, Brand=%s, Multiple=%f\n", i, item.GameType, item.Brand, item.Multiple)
			}
		}
	}

	// 获取时间范围用于查询在线人数（扩大到最近10分钟）
	now := time.Now()
	startTime := now.Add(-10 * time.Minute).Format("2006-01-02 15:04:05")
	endTime := now.Format("2006-01-02 15:04:05")

	// 构建返回数据
	var result []map[string]interface{}

	for _, game := range *userGames {
		gameData := map[string]interface{}{
			"Brand":      abugo.GetStringFromInterface(game["Brand"]),
			"GameId":     abugo.GetStringFromInterface(game["GameId"]),
			"UpdateTime": abugo.GetStringFromInterface(game["UpdateTime"]),
		}

		brand := abugo.GetStringFromInterface(game["Brand"])
		gameId := abugo.GetStringFromInterface(game["GameId"])

		// 初始化在线人数相关字段
		gameData["OnlineCnt"] = 0
		gameData["ShowOnline"] = 0

		// 如果Brand不是Hash，则从x_game_list获取游戏详情
		if brand != "Hash" {
			gameWhere := abugo.AbuDbWhere{}
			gameWhere.Add("and", "Brand", "like", brand+"%", nil)
			gameWhere.Add("and", "GameId", "=", gameId, nil)

			gameDetail, _ := server.Db().Table("x_game_list").Where(gameWhere).GetOne()
			if gameDetail != nil {
				gameData["Name"] = abugo.GetStringFromInterface((*gameDetail)["Name"])
				gameData["EName"] = abugo.GetStringFromInterface((*gameDetail)["EName"])
				gameData["Icon"] = abugo.GetStringFromInterface((*gameDetail)["Icon"])
				gameData["EIcon"] = abugo.GetStringFromInterface((*gameDetail)["EIcon"])
				gameData["GameType"] = abugo.GetInt64FromInterface((*gameDetail)["GameType"])
				gameData["IsNew"] = abugo.GetInt64FromInterface((*gameDetail)["IsNew"])
				gameData["IsHot"] = abugo.GetInt64FromInterface((*gameDetail)["IsHot"])
				gameData["OnlineMin"] = abugo.GetInt64FromInterface((*gameDetail)["OnlineMin"])
				gameData["OnlineMax"] = abugo.GetInt64FromInterface((*gameDetail)["OnlineMax"])
				gameData["Brand"] = abugo.GetStringFromInterface((*gameDetail)["Brand"])

				// 处理图片URL
				if gameData["Icon"] != nil && gameData["Icon"] != "" {
					gameData["Icon"] = buildImageUrl(abugo.GetStringFromInterface(gameData["Icon"]), sellerId)
				}
				if gameData["EIcon"] != nil && gameData["EIcon"] != "" {
					gameData["EIcon"] = buildImageUrl(abugo.GetStringFromInterface(gameData["EIcon"]), sellerId)
				}

				// 计算在线人数
				if setOnlineCntData.Display == 1 {
					gameData["ShowOnline"] = 1
					fmt.Printf("开始计算第三方游戏在线人数: Brand=%s, GameId=%s, GameType=%v\n", brand, gameId, gameData["GameType"])
					onlineCnt := calculateOnlineCount(brand, gameId, gameData, setOnlineCntData, startTime, endTime)
					fmt.Printf("第三方游戏在线人数计算结果: %d\n", onlineCnt)
					gameData["OnlineCnt"] = onlineCnt
				}
			}
		} else {
			// Hash游戏也计算在线人数
			if setOnlineCntData.Display == 1 {
				gameData["ShowOnline"] = 1
				fmt.Printf("开始计算Hash游戏在线人数: GameId=%s\n", gameId)
				onlineCnt := calculateHashOnlineCount(gameId, setOnlineCntData, startTime, endTime)
				fmt.Printf("Hash游戏在线人数计算结果: %d\n", onlineCnt)
				gameData["OnlineCnt"] = onlineCnt
			}
		}

		result = append(result, gameData)
	}

	ctx.RespOK(result)
}

// buildImageUrl 构建图片URL，如果已经是完整URL则直接返回，否则拼接服务器地址
func buildImageUrl(imageUrl string, sellerId int) string {
	if imageUrl == "" {
		return ""
	}

	// 如果已经是完整的HTTP URL，直接返回
	if strings.HasPrefix(imageUrl, "http://") || strings.HasPrefix(imageUrl, "https://") {
		return imageUrl
	}

	// 否则拼接服务器地址
	return server.GetImageUrl(sellerId) + imageUrl
}

// SetOnlineCntData 在线人数配置结构体
type SetOnlineCntData struct {
	Display    int32 `json:"display"`    // 在玩人数是否显示
	DefaultMin int32 `json:"defaultMin"` // 为0时默认最小值
	DefaultMax int32 `json:"defaultMax"` // 为0时默认最大值
	Data       []struct {
		GameType int     `json:"gameType"`
		Brand    string  `json:"brand"`
		Multiple float32 `json:"multiple"`
	} `json:"data"`
}

// calculateOnlineCount 计算第三方游戏的在线人数
func calculateOnlineCount(brand, gameId string, gameData map[string]interface{}, setOnlineCntData SetOnlineCntData, startTime, endTime string) int {
	config := setOnlineCntData
	fmt.Printf("第三方游戏配置: Display=%d, DefaultMin=%d, DefaultMax=%d\n", config.Display, config.DefaultMin, config.DefaultMax)

	// 查询实际投注人数
	type Record struct {
		Cnt int `gorm:"column:cnt"`
	}
	record := Record{}

	gameType := abugo.GetInt64FromInterface(gameData["GameType"])

	// 根据游戏类型确定查询的表
	var tableName string
	switch gameType {
	case 1: // 电子游戏
		tableName = "x_third_dianzhi"
	case 2: // 棋牌游戏
		tableName = "x_third_qipai"
	case 3: // 趣味游戏
		tableName = "x_third_quwei"
	case 4: // 彩票游戏
		tableName = "x_third_lottery"
	case 5: // 真人游戏
		tableName = "x_third_live"
	case 6: // 体育游戏
		tableName = "x_third_sport"
	case 7: // 德州游戏
		tableName = "x_third_texas"
	default:
		// 默认使用电子游戏表
		tableName = "x_third_dianzhi"
	}

	sql := fmt.Sprintf(`SELECT COUNT(DISTINCT(UserId)) as cnt FROM %s WHERE Brand='%s' AND GameId='%s' AND CreateTime BETWEEN '%s' AND '%s'`,
		tableName, brand, gameId, startTime, endTime)

	// 添加调试信息
	fmt.Printf("在线人数查询SQL: %s\n", sql)

	server.Db().Gorm().Raw(sql).First(&record)
	betCnt := record.Cnt

	// 添加调试信息
	fmt.Printf("查询结果: 表=%s, 品牌=%s, 游戏=%s, 投注人数=%d\n", tableName, brand, gameId, betCnt)

	// 检查配置匹配（无论投注人数是否为0）
	fmt.Printf("开始检查配置匹配: GameType=%d, Brand=%s\n", gameType, brand)
	for i, datum := range config.Data {
		fmt.Printf("配置项%d: GameType=%d, Brand=%s, Multiple=%f\n", i, datum.GameType, datum.Brand, datum.Multiple)
		if int32(gameType) == int32(datum.GameType) && brand == datum.Brand {
			fmt.Printf("找到匹配配置: GameType=%d, Brand=%s, Multiple=%f, 投注人数=%d\n", datum.GameType, datum.Brand, datum.Multiple, betCnt)
			if betCnt > 0 && datum.Multiple > 0 {
				result := int(math.Ceil(float64(float32(betCnt) * datum.Multiple)))
				fmt.Printf("使用倍数计算: %d * %f = %d\n", betCnt, datum.Multiple, result)
				return result
			}
			if betCnt > 0 && datum.Multiple == 0 {
				fmt.Printf("使用实际投注人数: %d\n", betCnt)
				return betCnt
			}
			// 如果投注人数为0，继续执行随机值逻辑
			fmt.Printf("投注人数为0，将使用随机值\n")
			break
		}
	}

	// 如果没有投注人数，使用随机值
	fmt.Printf("开始生成随机在线人数\n")
	defaultMin := config.DefaultMin
	defaultMax := config.DefaultMax
	fmt.Printf("默认范围: Min=%d, Max=%d\n", defaultMin, defaultMax)

	if onlineMin := abugo.GetInt64FromInterface(gameData["OnlineMin"]); onlineMin > 0 {
		defaultMin = int32(onlineMin)
		fmt.Printf("使用游戏配置最小值: %d\n", defaultMin)
	}
	if onlineMax := abugo.GetInt64FromInterface(gameData["OnlineMax"]); onlineMax > 0 {
		defaultMax = int32(onlineMax)
		fmt.Printf("使用游戏配置最大值: %d\n", defaultMax)
	}

	if defaultMax <= defaultMin {
		fmt.Printf("最大值<=最小值，直接返回最小值: %d\n", defaultMin)
		return int(defaultMin)
	}

	mrand.Seed(time.Now().UnixNano())
	result := mrand.Intn(int(defaultMax-defaultMin+1)) + int(defaultMin)
	fmt.Printf("生成随机在线人数: %d (范围: %d-%d)\n", result, defaultMin, defaultMax)
	return result
}

// calculateHashOnlineCount 计算Hash游戏的在线人数
func calculateHashOnlineCount(gameId string, setOnlineCntData SetOnlineCntData, startTime, endTime string) int {
	config := setOnlineCntData
	fmt.Printf("Hash游戏配置: Display=%d, DefaultMin=%d, DefaultMax=%d\n", config.Display, config.DefaultMin, config.DefaultMax)

	// 查询Hash游戏投注人数
	type Record struct {
		Cnt int `gorm:"column:cnt"`
	}
	record := Record{}

	sql := fmt.Sprintf(`SELECT COUNT(DISTINCT(UserId)) as cnt FROM x_order WHERE GameId='%s' AND CreateTime BETWEEN '%s' AND '%s'`,
		gameId, startTime, endTime)

	// 添加调试信息
	fmt.Printf("Hash游戏在线人数查询SQL: %s\n", sql)

	server.Db().Gorm().Raw(sql).First(&record)
	betCnt := record.Cnt

	// 添加调试信息
	fmt.Printf("Hash游戏查询结果: 游戏=%s, 投注人数=%d\n", gameId, betCnt)

	// 检查Hash游戏配置匹配
	fmt.Printf("Hash游戏开始检查配置匹配\n")
	for i, datum := range config.Data {
		fmt.Printf("Hash配置项%d: GameType=%d, Brand=%s, Multiple=%f\n", i, datum.GameType, datum.Brand, datum.Multiple)
		if 0 == datum.GameType && "hashOrigin" == datum.Brand {
			fmt.Printf("找到Hash游戏匹配配置: Multiple=%f, 投注人数=%d\n", datum.Multiple, betCnt)
			if betCnt > 0 && datum.Multiple > 0 {
				result := int(math.Ceil(float64(float32(betCnt) * datum.Multiple)))
				fmt.Printf("Hash游戏使用倍数计算: %d * %f = %d\n", betCnt, datum.Multiple, result)
				return result
			}
			if betCnt > 0 && datum.Multiple == 0 {
				fmt.Printf("Hash游戏使用实际投注人数: %d\n", betCnt)
				return betCnt
			}
			// 如果投注人数为0，继续执行随机值逻辑
			fmt.Printf("Hash游戏投注人数为0，将使用随机值\n")
			break
		}
	}

	// 如果没有投注人数，使用随机值
	fmt.Printf("Hash游戏开始生成随机在线人数\n")
	defaultMin := config.DefaultMin
	defaultMax := config.DefaultMax
	fmt.Printf("Hash游戏默认范围: Min=%d, Max=%d\n", defaultMin, defaultMax)

	if defaultMax <= defaultMin {
		fmt.Printf("Hash游戏最大值<=最小值，直接返回最小值: %d\n", defaultMin)
		return int(defaultMin)
	}

	mrand.Seed(time.Now().UnixNano())
	result := mrand.Intn(int(defaultMax-defaultMin+1)) + int(defaultMin)
	fmt.Printf("Hash游戏生成随机在线人数: %d (范围: %d-%d)\n", result, defaultMin, defaultMax)
	return result
}
