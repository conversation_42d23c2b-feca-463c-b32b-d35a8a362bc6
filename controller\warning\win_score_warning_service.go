package warning

import (
	"github.com/beego/beego/logs"
	"strconv"
	"time"

	"xserver/abugo"
	"xserver/server"
)

// WinScoreWarningService 赢分预警服务
type WinScoreWarningService struct{}

// NewWinScoreWarningService 创建赢分预警服务实例
func NewWinScoreWarningService() *WinScoreWarningService {
	return &WinScoreWarningService{}
}

// WarningData 预警数据结构
type WarningData struct {
	SellerId  int     `json:"seller_id"`  // 运营商ID
	ChannelId int     `json:"channel_id"` // 渠道ID
	UserId    int64   `json:"user_id"`    // 用户ID
	GameType  int     `json:"game_type"`  // 游戏类型
	GameId    string  `json:"game_id"`    // 游戏ID
	GameName  string  `json:"game_name"`  // 游戏名称
	Brand     string  `json:"brand"`      // 游戏品牌
	ThirdId   string  `json:"third_id"`   // 第三方订单号
	BetAmount float64 `json:"bet_amount"` // 投注金额
	WinAmount float64 `json:"win_amount"` // 派彩金额
	Symbol    string  `json:"symbol"`     // 币种
	ThirdTime string  `json:"third_time"` // 游戏时间
}

// CheckAndInsertWarning 检查并插入预警数据
func (s *WinScoreWarningService) CheckAndInsertWarning(data WarningData) error {
	// 计算赢分（派彩金额 - 投注金额）
	winScore := data.WinAmount - data.BetAmount
	// 获取该运营商的预警阈值
	usdtThreshold, trxThreshold := GetWarningThresholds(data.SellerId)

	logs.Info("检查预警阈值",
		"sellerId", data.SellerId,
		"userId", data.UserId,
		"betAmount", data.BetAmount,
		"winAmount", data.WinAmount,
		"winScore", winScore,
		"symbol", data.Symbol,
		"usdtThreshold", usdtThreshold,
		"trxThreshold", trxThreshold)

	// 检查是否触发预警
	shouldAlert := false
	if data.Symbol == "usdt" && usdtThreshold > 0 && winScore >= usdtThreshold {
		shouldAlert = true
	} else if data.Symbol == "trx" && trxThreshold > 0 && winScore >= trxThreshold {
		shouldAlert = true
	}

	if !shouldAlert {
		logs.Debug("未触发预警条件", " userId=", data.UserId, " winScore=", winScore,
			" sellerId=", data.SellerId, " usdt阈值=", usdtThreshold, " trx阈值=", trxThreshold)
		return nil
	}

	// 使用GormDao操作数据库
	db := server.Db().GormDao()

	// 构建预警数据记录
	warningRecord := map[string]interface{}{
		"SellerId":  data.SellerId,
		"ChannelId": data.ChannelId,
		"UserId":    data.UserId,
		"GameType":  data.GameType,
		"GameId":    data.GameId,
		"GameName":  data.GameName,
		"Brand":     data.Brand,
		"ThirdId":   data.ThirdId,
		"BetAmount": data.BetAmount,
		"WinAmount": data.WinAmount,
		"WinScore":  winScore,
		"Symbol":    data.Symbol,
		"ThirdTime": data.ThirdTime,
	}

	// 插入预警数据
	err := db.Table("x_win_score_warning").Create(warningRecord).Error

	if err != nil {
		logs.Error("插入预警数据失败", "error", err)
		return err
	}

	logs.Info("成功插入预警数据",
		"sellerId", data.SellerId,
		"userId", data.UserId,
		"winScore", winScore,
		"symbol", data.Symbol)

	// 增加Redis中的预警计数
	err = IncrementWarningCount(data.SellerId)
	if err != nil {
		logs.Error("增加预警计数失败", "sellerId", data.SellerId, "error", err)
		// 不返回错误，避免影响主流程
	}

	return nil
}

// Redis Key常量
const (
	WinScoreWarningCountKey = "win_score_warning_count_"
)

// 配置Key常量
const (
	ConfigKeyWinScoreWarningUsdt = "WinScoreWarningUsdt"
	ConfigKeyWinScoreWarningTrx  = "WinScoreWarningTrx"
)

// GetWarningCount 获取运营商预警数量
func GetWarningCount(sellerId int) (int, error) {
	key := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	rValueInterface := server.CRedis().Get(key)
	if rValueInterface == nil {
		return 0, nil
	}
	// 将[]uint8转换为字符串，再转换为int
	dataBytes := rValueInterface.([]uint8)
	dataStr := string(dataBytes)
	rValue := int(abugo.GetInt64FromInterface(dataStr))
	return rValue, nil
}

// GetAllWarningCount 获取所有运营商预警数量总和
func GetAllWarningCount() (int, error) {
	key := WinScoreWarningCountKey + "all"
	rValueInterface := server.CRedis().Get(key)
	if rValueInterface == nil {
		return 0, nil
	}
	// 将[]uint8转换为字符串，再转换为int
	dataBytes := rValueInterface.([]uint8)
	dataStr := string(dataBytes)
	rValue := int(abugo.GetInt64FromInterface(dataStr))
	return rValue, nil
}

// IncrementWarningCount 增加运营商预警数量
func IncrementWarningCount(sellerId int) error {
	// 增加指定运营商的预警数量
	sellerKey := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	currentCount, _ := GetWarningCount(sellerId)
	newCount := currentCount + 1
	err := server.CRedis().SetString(sellerKey, strconv.Itoa(newCount))
	if err != nil {
		logs.Error("增加运营商预警数量失败", "sellerId", sellerId, "error", err)
		return err
	}

	// 增加总预警数量
	allKey := WinScoreWarningCountKey + "all"
	allCurrentCount, _ := GetAllWarningCount()
	allNewCount := allCurrentCount + 1
	err = server.CRedis().SetString(allKey, strconv.Itoa(allNewCount))
	if err != nil {
		logs.Error("增加总预警数量失败", "error", err)
		return err
	}

	logs.Info("预警数量已增加", "sellerId", sellerId, "newCount", newCount, "allNewCount", allNewCount)
	return nil
}

// ClearWarningCount 清除运营商预警数量
func ClearWarningCount(sellerId int) error {
	key := WinScoreWarningCountKey + strconv.Itoa(sellerId)
	err := server.CRedis().Del(key)
	if err != nil {
		logs.Error("清除运营商预警数量失败", "sellerId", sellerId, "error", err)
	}
	return err
}

// ClearAllWarningCount 清除所有预警数量
func ClearAllWarningCount() error {
	allKey := WinScoreWarningCountKey + "all"
	err := server.CRedis().Del(allKey)
	if err != nil {
		logs.Error("清除所有预警数量失败", "error", err)
	}
	return err
}

// GetWarningThresholds 获取运营商的预警阈值配置
func GetWarningThresholds(sellerId int) (usdtThreshold, trxThreshold float64) {
	usdtThreshold = abugo.GetFloat64FromInterface(server.GetConfigString(sellerId, 0, ConfigKeyWinScoreWarningUsdt))
	trxThreshold = abugo.GetFloat64FromInterface(server.GetConfigString(sellerId, 0, ConfigKeyWinScoreWarningTrx))
	return usdtThreshold, trxThreshold
}

// PushWinWarning 插入预警数据
func PushWinWarning(gameType int, thirdId string, sellerId int, symbol string, userId int64, gameId string, gameName string, betAmount float64, winAmount float64) error {
	// 创建预警服务实例
	warningService := NewWinScoreWarningService()
	// 构建预警数据
	warningData := WarningData{
		SellerId: sellerId,
		//ChannelId: 0, // 默认渠道，可根据需要调整
		UserId:   userId,
		GameType: gameType,
		GameId:   gameId,
		GameName: gameName,
		//Brand:     brand,
		ThirdId:   thirdId, // 第三方订单号，可根据需要传入
		BetAmount: betAmount,
		WinAmount: winAmount,
		Symbol:    symbol, // 默认币种，可根据需要调整
		ThirdTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 异步检查并插入预警数据
	go func() {
		err := warningService.CheckAndInsertWarning(warningData)
		if err != nil {
			logs.Error("游戏派奖预警检查失败",
				"gameType", gameType,
				"sellerId", sellerId,
				"userId", userId,
				"gameId", gameId,
				"betAmount", betAmount,
				"winAmount", winAmount,
				"error", err)
		}
	}()

	return nil
}
