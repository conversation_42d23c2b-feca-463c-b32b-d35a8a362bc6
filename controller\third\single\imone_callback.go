package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// TransactionData 交易数据结构
type TransactionData struct {
	PlayerId         string  `json:"PlayerId"`           // 玩家账号
	ProviderPlayerId string  `json:"ProviderPlayerId"`   // 供应商玩家账号
	Provider         string  `json:"Provider"`           // 游戏供应商代码
	GameId           string  `json:"GameId"`             // 游戏代码
	GameName         string  `json:"GameName,omitempty"` // 游戏名称
	RoundId          string  `json:"RoundId,omitempty"`  // 游戏局号
	GameNo           string  `json:"GameNo,omitempty"`   // 彩期
	BetId            string  `json:"BetId,omitempty"`    // 注单代码
	TransactionId    string  `json:"TransactionId"`      // IM交易代码
	ActionId         int     `json:"ActionId,omitempty"` // 动作编号
	Type             string  `json:"Type"`               // 交易类别
	Currency         string  `json:"Currency"`           // 币种
	Amount           float64 `json:"Amount"`             // 投注金额
	BetOn            string  `json:"BetOn,omitempty"`    // 投注类别
	BetType          string  `json:"BetType,omitempty"`  // 投注内容
	Platform         string  `json:"Platform"`           // 投注平台
	Tray             string  `json:"Tray,omitempty"`     // 投注盘口
	Product          int     `json:"Product,omitempty"`  // 产品类型
	TimeStamp        string  `json:"TimeStamp"`          // 时间戳
	BetDate          string  `json:"BetDate,omitempty"`  // 投注日期
}

// FreeRoundResultData 免费回合结果数据结构
type FreeRoundResultData struct {
	Code                  int     `json:"Code"`
	Message               string  `json:"Message"`
	OperatorTransactionId string  `json:"OperatorTransactionId,omitempty"`
	TransactionId         string  `json:"TransactionId,omitempty"`
	Balance               float64 `json:"Balance,omitempty"`
}

// ResultData 结果数据结构
type ResultData struct {
	Code                  int     `json:"Code"`                            // 子级代码
	Message               string  `json:"Message"`                         // 响应信息
	OperatorTransactionId string  `json:"OperatorTransactionId,omitempty"` // 运营商交易代码
	TransactionId         string  `json:"TransactionId"`                   // IM交易代码
	Balance               float64 `json:"Balance"`                         // 余额
}

// GetBalance 查询余额回调 - URL: /GetBalance
func (l *IMOneSingleService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ProductWallet string `json:"ProductWallet" validate:"required"` // 产品钱包
		PlayerId      string `json:"PlayerId" validate:"required"`      // 玩家账号
		Currency      string `json:"Currency" validate:"required"`      // 币种
	}

	type ResponseData struct {
		Code     int    `json:"Code"`               // 响应代码
		Message  string `json:"Message"`            // 响应信息
		PlayerID string `json:"PlayerID,omitempty"` // 玩家账号
		Currency string `json:"Currency,omitempty"` // 币种
		Balance  string `json:"Balance,omitempty"`  // 余额（最多4位小数）
	}

	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("IMOne GetBalance 请求参数错误 err=", err.Error())
		respData := ResponseData{
			Code:    612,
			Message: "Invalid Argument.",
		}
		ctx.RespJson(respData)
		return
	}

	// 解析用户ID
	userId := base.ParseUserIdFromPlayerName(reqdata.PlayerId)
	if userId == 0 {
		logs.Error("IMOne GetBalance 解析用户ID失败 PlayerId=", reqdata.PlayerId)
		respData := ResponseData{
			Code:    504,
			Message: "Player does not exist.",
		}
		ctx.RespJson(respData)
		return
	}

	// 查询用户余额
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("IMOne GetBalance 用户不存在 userId=", userId)
			respData := ResponseData{
				Code:    504,
				Message: "Player does not exist.",
			}
			ctx.RespJson(respData)
			return
		}
		logs.Error("IMOne GetBalance 查询用户余额失败 userId=", userId, " err=", err.Error())
		respData := ResponseData{
			Code:    999,
			Message: "System has failed to process your request.",
		}
		ctx.RespJson(respData)
		return
	}

	// 格式化余额为最多4位小数
	balanceStr := fmt.Sprintf("%.4f", userBalance.Amount)

	logs.Info("IMOne GetBalance 成功 userId=", userId, " balance=", balanceStr)

	respData := ResponseData{
		Code:     0,
		Message:  "Successful.",
		PlayerID: reqdata.PlayerId,
		Currency: reqdata.Currency,
		Balance:  balanceStr,
	}
	ctx.RespJson(respData)
}

// PlaceBet 下注回调 - URL: /PlaceBet
func (l *IMOneSingleService) PlaceBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ProductWallet string            `json:"ProductWallet" validate:"required"` // 产品钱包
		SessionToken  string            `json:"SessionToken" validate:"required"`  // 会话
		Transactions  []TransactionData `json:"Transactions" validate:"required"`  // 交易列表
	}

	type ResponseData struct {
		Code    int          `json:"Code"`    // 母级代码
		Message string       `json:"Message"` // 响应信息
		Results []ResultData `json:"Results"` // 结果列表
	}

	reqdata := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	logs.Debug("IMOne PlaceBet receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqdata)
	if err != nil {
		logs.Error("IMOne PlaceBet 请求参数错误 err=", err.Error())
		respData := ResponseData{
			Code:    612,
			Message: "Invalid Argument.",
		}
		ctx.RespJson(respData)
		return
	}

	// 先验证所有交易，确保都能通过验证再开始事务
	var validatedTransactions []TransactionData
	var userIds []int

	// 第一阶段：验证所有交易
	for _, transaction := range reqdata.Transactions {
		// 解析用户ID
		userId := base.ParseUserIdFromPlayerName(transaction.PlayerId)
		if userId == 0 {
			logs.Error("IMOne PlaceBet 解析用户ID失败 PlayerId=", transaction.PlayerId)
			respData := ResponseData{
				Code:    504,
				Message: "Player does not exist.",
			}
			ctx.RespJson(respData)
			return
		}

		// 验证会话 - 只有 IMESports 需要验证 SessionToken
		if reqdata.ProductWallet == "IMESports" {
			if reqdata.SessionToken == "" {
				logs.Error("IMOne PlaceBet IMESports 缺少 SessionToken PlayerId=", transaction.PlayerId)
				respData := ResponseData{
					Code:    531,
					Message: "Invalid Token.",
				}
				ctx.RespJson(respData)
				return
			}

			_, err := l.ValidateSessionToken(reqdata.SessionToken, transaction.PlayerId, transaction.GameId)
			if err != nil {
				logs.Error("IMOne PlaceBet 会话验证失败 sessionToken=", reqdata.SessionToken, " PlayerId=", transaction.PlayerId, " err=", err.Error())
				respData := ResponseData{
					Code:    531,
					Message: "Invalid Token.",
				}
				ctx.RespJson(respData)
				return
			}
		}

		validatedTransactions = append(validatedTransactions, transaction)
		userIds = append(userIds, userId)
	}

	// 第二阶段：在单个事务中处理所有交易
	results := make([]ResultData, len(validatedTransactions))

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		for i, transaction := range validatedTransactions {
			userId := userIds[i]

			result := l.processPlaceBetTransactionInTx(tx, transaction, userId, reqdata.ProductWallet)
			results[i] = result

			// 如果任何一笔交易失败，整个事务回滚
			if result.Code != 0 {
				logs.Error("IMOne PlaceBet 交易失败，回滚所有交易 TransactionId=", transaction.TransactionId, " Code=", result.Code)
				return fmt.Errorf("transaction failed: %s", result.Message)
			}
		}
		return nil
	})

	if err != nil {
		logs.Error("IMOne PlaceBet 事务失败 err=", err.Error())
		respData := ResponseData{
			Code:    999,
			Message: "System has failed to process your request.",
		}
		ctx.RespJson(respData)
		return
	}

	// 事务成功后的后续处理（会话刷新、推送通知等）
	if reqdata.ProductWallet == "IMESports" && reqdata.SessionToken != "" {
		err = l.RefreshSessionToken(reqdata.SessionToken, 86400) // 24小时
		if err != nil {
			logs.Error("IMOne PlaceBet 刷新会话失败 sessionToken=", reqdata.SessionToken, " err=", err.Error())
		}
	}

	// 发送推送通知
	for i, transaction := range validatedTransactions {
		userId := userIds[i]
		// 推送下注事件
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, transaction.GameName, l.brandName, transaction.Amount, transaction.Currency, l.brandName, transaction.BetId, 6) // 2 表示免费回合
		}

		// 发送余额变动通知
		go func(notifyUserId int, transactionId string) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][IMOne] PlaceBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][IMOne] PlaceBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId, transaction.TransactionId)
	}

	respData := ResponseData{
		Code:    0,
		Message: "Successful.",
		Results: results,
	}
	ctx.RespJson(respData)
}

// processPlaceBetTransactionInTx 在事务中处理单个下注交易
func (l *IMOneSingleService) processPlaceBetTransactionInTx(tx *daogorm.DB, transaction TransactionData, userId int, productWallet string) ResultData {
	var finalBalance float64

	// 查询用户余额并锁定
	userBalance := thirdGameModel.UserBalance{}
	err := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
		Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("IMOne PlaceBet 用户不存在 userId=", userId)
			return ResultData{
				Code:          504,
				Message:       "Player does not exist.",
				TransactionId: transaction.TransactionId,
			}
		}
		logs.Error("IMOne PlaceBet 查询用户余额失败 userId=", userId, " err=", err.Error())
		return ResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 检查余额是否足够
	if userBalance.Amount < transaction.Amount {
		logs.Error("IMOne PlaceBet 余额不足 userId=", userId, " balance=", userBalance.Amount, " amount=", transaction.Amount)
		return ResultData{
			Code:          510,
			Message:       "Insufficient amount.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 检查交易是否已存在（幂等性）
	existingOrder := thirdGameModel.ThirdSportOrder{}
	err = tx.Table("x_third_sport_pre_order").Where("ThirdId = ? AND Brand = ?", transaction.BetId, l.brandName).First(&existingOrder).Error
	if err == nil {
		// 交易已存在，返回现有余额
		finalBalance = userBalance.Amount
		logs.Info("IMOne PlaceBet 交易已存在 TransactionId=", transaction.TransactionId)
		return ResultData{
			Code:                  0,
			Message:               "Successful.",
			OperatorTransactionId: fmt.Sprintf("op_%s", transaction.TransactionId),
			TransactionId:         transaction.TransactionId,
			Balance:               finalBalance,
		}
	} else if !errors.Is(err, daogorm.ErrRecordNotFound) {
		logs.Error("IMOne PlaceBet 查询交易失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
		return ResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 扣除余额
	err = tx.Table("x_user").Where("UserId = ?", userId).Update("Amount", userBalance.Amount-transaction.Amount).Error
	if err != nil {
		logs.Error("IMOne PlaceBet 扣除余额失败 userId=", userId, " err=", err.Error())
		return ResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	finalBalance = userBalance.Amount - transaction.Amount

	// 创建订单记录
	nowTime := time.Now()
	betTime := utils.ParseTime(transaction.TimeStamp)
	order := thirdGameModel.ThirdSportOrder{
		UserId:       userId,
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		BetChannelId: userBalance.ChannelId,
		ThirdId:      transaction.BetId,
		GameId:       transaction.GameId,
		GameName:     transaction.GameName,
		BetAmount:    transaction.Amount,
		BetTime:      betTime,
		BetLocalTime: utils.ParseTimeToLocal(transaction.TimeStamp),
		WinAmount:    0,
		Currency:     transaction.Currency,
		DataState:    -1, // 未结算
		RawData:      l.formatTransactionContext(transaction),
		ThirdTime:    utils.GetCurrentTime(),
		CreateTime:   nowTime.Format("2006-01-02 15:04:05"),
		Brand:        l.brandName,
	}

	err = tx.Table("x_third_sport_pre_order").Create(&order).Error
	if err != nil {
		logs.Error("IMOne PlaceBet 创建订单失败 BetId=", transaction.BetId, " err=", err.Error())
		return ResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 创建账变记录
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userId,
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		Amount:       -transaction.Amount,
		BeforeAmount: userBalance.Amount,
		AfterAmount:  finalBalance,
		Reason:       utils.BalanceCReasonIMOneBet, // IMOne下注
		CreateTime:   nowTime.Format("2006-01-02 15:04:05"),
		Memo:         fmt.Sprintf("imone下注: %s", transaction.BetId),
	}

	err = tx.Table("x_amount_change_log").Create(&amountLog).Error
	if err != nil {
		logs.Error("IMOne PlaceBet 创建账变记录失败 BetId=", transaction.BetId, " err=", err.Error())
		return ResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	logs.Info("IMOne PlaceBet 交易成功 userId=", userId, " TransactionId=", transaction.TransactionId, " balance=", finalBalance)

	return ResultData{
		Code:                  0,
		Message:               "Successful.",
		OperatorTransactionId: fmt.Sprintf("op_%s", transaction.TransactionId),
		TransactionId:         transaction.TransactionId,
		Balance:               finalBalance,
	}
}

// formatTransactionContext 格式化交易上下文
func (l *IMOneSingleService) formatTransactionContext(transaction TransactionData) string {
	data, _ := json.Marshal(transaction)
	return string(data)
}

// SettleBet 结算回调 - URL: /SettleBet （实现在 imone_settle.go 中）
// Refund 退款回调 - URL: /Refund （实现在 imone_settle.go 中）

// FreeRoundPlaceBet 免费回合下注回调 - URL: /FreeRoundPlaceBet
func (l *IMOneSingleService) FreeRoundPlaceBet(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ProductWallet string            `json:"ProductWallet" validate:"required"` // 产品钱包
		Transactions  []TransactionData `json:"Transactions" validate:"required"`  // 交易数组
		SessionToken  string            `json:"SessionToken,omitempty"`            // 会话令牌
	}

	type ResponseData struct {
		Code    int                   `json:"Code"`
		Message string                `json:"Message"`
		Results []FreeRoundResultData `json:"Results,omitempty"`
	}

	if l.Debug {
		logs.Info("IMOne FreeRoundPlaceBet 接收到请求")
	}

	// 解析请求
	reqdata := RequestData{}
	err := ctx.Gin().ShouldBind(&reqdata)
	if err != nil {
		logs.Error("IMOne FreeRoundPlaceBet 解析请求数据失败 err=", err.Error())
		respData := ResponseData{
			Code:    999,
			Message: "System has failed to process your request.",
		}
		ctx.RespJson(respData)
		return
	}

	if l.Debug {
		logs.Info("IMOne FreeRoundPlaceBet 请求数据:", fmt.Sprintf("%+v", reqdata))
	}

	// 验证必填字段
	if len(reqdata.Transactions) == 0 {
		logs.Error("IMOne FreeRoundPlaceBet 缺少交易数据")
		respData := ResponseData{
			Code:    400,
			Message: "Invalid request data.",
		}
		ctx.RespJson(respData)
		return
	}

	// 先验证所有交易，确保都能通过验证再开始事务
	var validatedTransactions []TransactionData
	var userIds []int

	// 第一阶段：验证所有交易
	for _, transaction := range reqdata.Transactions {
		// 解析用户ID
		userId := base.ParseUserIdFromPlayerName(transaction.PlayerId)
		if userId == 0 {
			logs.Error("IMOne FreeRoundPlaceBet 解析用户ID失败 PlayerId=", transaction.PlayerId)
			respData := ResponseData{
				Code:    504,
				Message: "Player does not exist.",
			}
			ctx.RespJson(respData)
			return
		}

		// 验证会话 - 只有 IMESports 需要验证 SessionToken
		if reqdata.ProductWallet == "IMESports" {
			if reqdata.SessionToken == "" {
				logs.Error("IMOne FreeRoundPlaceBet IMESports 缺少 SessionToken PlayerId=", transaction.PlayerId)
				respData := ResponseData{
					Code:    531,
					Message: "Invalid Token.",
				}
				ctx.RespJson(respData)
				return
			}

			_, err := l.ValidateSessionToken(reqdata.SessionToken, transaction.PlayerId, transaction.GameId)
			if err != nil {
				logs.Error("IMOne FreeRoundPlaceBet 会话验证失败 sessionToken=", reqdata.SessionToken, " PlayerId=", transaction.PlayerId, " err=", err.Error())
				respData := ResponseData{
					Code:    531,
					Message: "Invalid Token.",
				}
				ctx.RespJson(respData)
				return
			}
		}

		validatedTransactions = append(validatedTransactions, transaction)
		userIds = append(userIds, userId)
	}

	// 第二阶段：在单个事务中处理所有免费回合交易
	results := make([]FreeRoundResultData, len(validatedTransactions))

	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		for i, transaction := range validatedTransactions {
			userId := userIds[i]

			result := l.processFreeRoundPlaceBetTransactionInTx(tx, transaction, userId, reqdata.ProductWallet)
			results[i] = result

			// 如果任何一笔交易失败，整个事务回滚
			if result.Code != 0 {
				logs.Error("IMOne FreeRoundPlaceBet 交易失败，回滚所有交易 TransactionId=", transaction.TransactionId, " Code=", result.Code)
				return fmt.Errorf("transaction failed: %s", result.Message)
			}
		}
		return nil
	})

	if err != nil {
		logs.Error("IMOne FreeRoundPlaceBet 事务失败 err=", err.Error())
		respData := ResponseData{
			Code:    999,
			Message: "System has failed to process your request.",
		}
		ctx.RespJson(respData)
		return
	}

	// 事务成功后的后续处理（会话刷新、推送通知等）
	if reqdata.ProductWallet == "IMESports" && reqdata.SessionToken != "" {
		err = l.RefreshSessionToken(reqdata.SessionToken, 86400) // 24小时
		if err != nil {
			logs.Error("IMOne FreeRoundPlaceBet 刷新会话失败 sessionToken=", reqdata.SessionToken, " err=", err.Error())
		}
	}

	// 发送推送通知
	for i, transaction := range validatedTransactions {
		userId := userIds[i]
		// 推送免费回合下注事件
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, transaction.GameName, l.brandName, transaction.Amount, transaction.Currency, l.brandName, transaction.BetId, 6) // 2 表示免费回合
		}

		// 发送余额变动通知（虽然免费回合不扣款，但可能需要刷新界面）
		go func(notifyUserId int, transactionId string) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][IMOne] FreeRoundPlaceBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][IMOne] FreeRoundPlaceBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId, transaction.TransactionId)
	}

	respData := ResponseData{
		Code:    0,
		Message: "Successful.",
		Results: results,
	}
	ctx.RespJson(respData)
}

// FreeRoundWinResult 免费回合结果回调 - URL: /FreeRoundWinResult
func (l *IMOneSingleService) FreeRoundWinResult(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		Results []struct {
			Code    int    `json:"Code"`
			Message string `json:"Message"`
		} `json:"Results"`
	}

	// 免费回合结果处理
	respData := ResponseData{
		Results: []struct {
			Code    int    `json:"Code"`
			Message string `json:"Message"`
		}{
			{
				Code:    0,
				Message: "Successful",
			},
		},
	}
	ctx.RespJson(respData)
}

// FreezeWithdrawal 冻结/解冻提款回调 - URL: /FreezeWithdrawal
func (l *IMOneSingleService) FreezeWithdrawal(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		Code    int    `json:"Code"`
		Message string `json:"Message"`
	}

	// 始终返回成功
	respData := ResponseData{
		Code:    0,
		Message: "Successful",
	}
	ctx.RespJson(respData)
}

// GetApproval 索取批准回调 - URL: /GetApproval
func (l *IMOneSingleService) GetApproval(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		ProductWallet string  `json:"ProductWallet" validate:"required"` // 产品钱包
		PlayerId      string  `json:"PlayerId" validate:"required"`      // 玩家账号
		Currency      string  `json:"Currency" validate:"required"`      // 币种
		BetAmount     float64 `json:"BetAmount" validate:"required"`     // 投注金额
		SessionToken  string  `json:"SessionToken" validate:"required"`  // 会话
	}

	type ResponseData struct {
		Code     int    `json:"Code"`               // 响应代码
		Message  string `json:"Message"`            // 响应信息
		PlayerID string `json:"PlayerID,omitempty"` // 玩家账号
		Currency string `json:"Currency,omitempty"` // 币种
		Balance  string `json:"Balance,omitempty"`  // 余额
	}

	reqdata := RequestData{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	logs.Debug("IMOne GetApproval receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqdata)
	if err != nil {
		logs.Error("IMOne GetApproval 请求参数错误 err=", err.Error())
		respData := ResponseData{
			Code:    612,
			Message: "Invalid Argument.",
		}
		ctx.RespJson(respData)
		return
	}

	// 1. 验证产品钱包 - 只适用于IM Esports (401)
	if reqdata.ProductWallet != "IMESports" {
		logs.Error("IMOne GetApproval 无效的产品钱包 ProductWallet=", reqdata.ProductWallet)
		respData := ResponseData{
			Code:    508,
			Message: "Invalid product wallet.",
		}
		ctx.RespJson(respData)
		return
	}

	// 2. 验证投注金额格式
	if reqdata.BetAmount <= 0 {
		logs.Error("IMOne GetApproval 无效的投注金额 BetAmount=", reqdata.BetAmount)
		respData := ResponseData{
			Code:    519,
			Message: "Invalid amount format.",
		}
		ctx.RespJson(respData)
		return
	}

	// 3. 解析用户ID
	userId := base.ParseUserIdFromPlayerName(reqdata.PlayerId)
	if userId == 0 {
		logs.Error("IMOne GetApproval 解析用户ID失败 PlayerId=", reqdata.PlayerId)
		respData := ResponseData{
			Code:    504,
			Message: "Player does not exist.",
		}
		ctx.RespJson(respData)
		return
	}

	// 4. 验证会话
	sessionData, err := l.ValidateSessionToken(reqdata.SessionToken, reqdata.PlayerId, "")
	if err != nil {
		// 如果直接验证失败，尝试通过玩家ID查找可用的会话
		logs.Warn("IMOne GetApproval 直接会话验证失败，尝试查找玩家会话 sessionToken=", reqdata.SessionToken, " PlayerId=", reqdata.PlayerId)

		// 尝试查找该玩家的任何有效会话
		if alternativeSessionData := l.findPlayerActiveSession(reqdata.PlayerId); alternativeSessionData != nil {
			sessionData = alternativeSessionData
			logs.Info("IMOne GetApproval 找到玩家的有效会话 PlayerId=", reqdata.PlayerId, " 使用会话=", sessionData.PlayerId)
		} else {
			logs.Error("IMOne GetApproval 会话验证失败 sessionToken=", reqdata.SessionToken, " PlayerId=", reqdata.PlayerId, " err=", err.Error())
			respData := ResponseData{
				Code:    531,
				Message: "Invalid Token.",
			}
			ctx.RespJson(respData)
			return
		}
	}

	// 验证会话中的产品钱包是否匹配
	if sessionData.ProductWallet != "" && sessionData.ProductWallet != reqdata.ProductWallet {
		logs.Error("IMOne GetApproval 产品钱包不匹配 sessionProductWallet=", sessionData.ProductWallet, " requestProductWallet=", reqdata.ProductWallet)
		respData := ResponseData{
			Code:    531,
			Message: "Invalid Token.",
		}
		ctx.RespJson(respData)
		return
	}

	// 5. 查询用户余额和状态
	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Select("UserId,Amount,State").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("IMOne GetApproval 用户不存在 userId=", userId)
			respData := ResponseData{
				Code:    504,
				Message: "Player does not exist.",
			}
			ctx.RespJson(respData)
			return
		}
		logs.Error("IMOne GetApproval 查询用户余额失败 userId=", userId, " err=", err.Error())
		respData := ResponseData{
			Code:    999,
			Message: "System unable to process your request.",
		}
		ctx.RespJson(respData)
		return
	}

	// 6. 验证玩家状态
	if userBalance.State != 1 {
		logs.Error("IMOne GetApproval 玩家状态无效 userId=", userId, " state=", userBalance.State)
		respData := ResponseData{
			Code:    542,
			Message: "Player is inactive.",
		}
		ctx.RespJson(respData)
		return
	}

	// 7. 验证币种
	if reqdata.Currency != l.currency {
		logs.Error("IMOne GetApproval 无效的币种 currency=", reqdata.Currency, " expected=", l.currency)
		respData := ResponseData{
			Code:    507,
			Message: "Invalid currency.",
		}
		ctx.RespJson(respData)
		return
	}

	// 8. 验证钱包余额
	if userBalance.Amount < reqdata.BetAmount {
		logs.Error("IMOne GetApproval 余额不足 userId=", userId, " balance=", userBalance.Amount, " betAmount=", reqdata.BetAmount)
		respData := ResponseData{
			Code:    512,
			Message: "Insufficient amount.",
		}
		ctx.RespJson(respData)
		return
	}

	// 9. 刷新会话
	err = l.RefreshSessionToken(reqdata.SessionToken, 86400) // 24小时
	if err != nil {
		logs.Error("IMOne GetApproval 刷新会话失败 sessionToken=", reqdata.SessionToken, " err=", err.Error())
	}

	// 格式化余额
	balanceStr := fmt.Sprintf("%.4f", userBalance.Amount)

	logs.Info("IMOne GetApproval 成功 userId=", userId, " balance=", balanceStr)

	respData := ResponseData{
		Code:     0,
		Message:  "Successful.",
		PlayerID: reqdata.PlayerId,
		Currency: reqdata.Currency,
		Balance:  balanceStr,
	}
	ctx.RespJson(respData)
}

// processFreeRoundPlaceBetTransactionInTx 在事务中处理单个免费回合下注交易
func (l *IMOneSingleService) processFreeRoundPlaceBetTransactionInTx(tx *daogorm.DB, transaction TransactionData, userId int, productWallet string) FreeRoundResultData {
	var currentBalance float64

	// 查询用户信息（不需要锁定，因为不扣款）
	userBalance := thirdGameModel.UserBalance{}
	err := tx.Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("IMOne FreeRoundPlaceBet 用户不存在 userId=", userId)
			return FreeRoundResultData{
				Code:          504,
				Message:       "Player does not exist.",
				TransactionId: transaction.TransactionId,
			}
		}
		logs.Error("IMOne FreeRoundPlaceBet 查询用户信息失败 userId=", userId, " err=", err.Error())
		return FreeRoundResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	currentBalance = userBalance.Amount

	// 检查交易是否已存在（幂等性）
	existingOrder := thirdGameModel.ThirdOrder{}
	err = tx.Table("x_third_sport").Where("ThirdId = ? AND Brand = ?", transaction.BetId, l.brandName).First(&existingOrder).Error
	if err == nil {
		// 交易已存在，返回现有余额
		logs.Info("IMOne FreeRoundPlaceBet 交易已存在 TransactionId=", transaction.TransactionId)
		return FreeRoundResultData{
			Code:                  0,
			Message:               "Successful.",
			OperatorTransactionId: fmt.Sprintf("op_%s", transaction.TransactionId),
			TransactionId:         transaction.TransactionId,
			Balance:               currentBalance,
		}
	} else if !errors.Is(err, daogorm.ErrRecordNotFound) {
		logs.Error("IMOne FreeRoundPlaceBet 查询交易失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
		return FreeRoundResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 创建免费回合订单记录（不扣款，但需要记录）
	nowTime := time.Now()
	order := thirdGameModel.ThirdOrder{
		UserId:     userId,
		SellerId:   userBalance.SellerId,
		ChannelId:  userBalance.ChannelId,
		ThirdId:    transaction.BetId,
		GameName:   transaction.GameName,
		BetAmount:  transaction.Amount,
		WinAmount:  0,
		Currency:   transaction.Currency,
		DataState:  -1, // 未结算
		RawData:    l.formatTransactionContext(transaction),
		CreateTime: nowTime.Format("2006-01-02 15:04:05"),
		Brand:      l.brandName,
	}

	err = tx.Table("x_third_sport").Create(&order).Error
	if err != nil {
		logs.Error("IMOne FreeRoundPlaceBet 创建订单失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
		return FreeRoundResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	// 创建账变记录（免费回合，金额为0）
	amountLog := thirdGameModel.AmountChangeLog{
		UserId:       userId,
		SellerId:     userBalance.SellerId,
		ChannelId:    userBalance.ChannelId,
		Amount:       0, // 免费回合不扣款
		BeforeAmount: currentBalance,
		AfterAmount:  currentBalance,                     // 余额不变
		Reason:       utils.BalanceCReasonIMOneFreeRound, // IMOne免费回合
		CreateTime:   nowTime.Format("2006-01-02 15:04:05"),
		Memo:         fmt.Sprintf("IMOne免费回合下注: %s", transaction.TransactionId),
	}

	err = tx.Table("x_amount_change_log").Create(&amountLog).Error
	if err != nil {
		logs.Error("IMOne FreeRoundPlaceBet 创建账变记录失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
		return FreeRoundResultData{
			Code:          999,
			Message:       "System has failed to process your request.",
			TransactionId: transaction.TransactionId,
		}
	}

	logs.Info("IMOne FreeRoundPlaceBet 交易成功 userId=", userId, " TransactionId=", transaction.TransactionId, " balance=", currentBalance)

	return FreeRoundResultData{
		Code:                  0,
		Message:               "Successful.",
		OperatorTransactionId: fmt.Sprintf("op_%s", transaction.TransactionId),
		TransactionId:         transaction.TransactionId,
		Balance:               currentBalance,
	}
}
