package controller

import (
	"encoding/json"
	"fmt"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
)

type RankController struct {
}

func (c *RankController) Init() {
	gropu := server.Http().NewGroup("/api/rank")
	{
		gropu.PostNoAuth("/data", c.data)
		gropu.PostNoAuth("/v2/data", c.newData)
		gropu.PostNoAuth("/v3/data", c.newDataV3)
		gropu.PostNoAuth("/now", c.now)
	}
}

func (c *RankController) now(ctx *abugo.AbuHttpContent) {

}

func (c *RankController) data(ctx *abugo.AbuHttpContent) {
	defer recover()
	type RequestData struct {
		SellerId int `validate:"required"` //运营商
		RankType int `validate:"required"` //1 中奖日排行,2 中奖周排行,3 中奖月排行,4 实时排行,11 下注日排行,12 下注周排行,13 下注月排行
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	_, SellerId := server.GetChannel(ctx, "")
	{
		rediskey := fmt.Sprintf("%s:%s:rank_trx:%d:%d", server.Project(), server.Module(), SellerId, reqdata.RankType)
		redisdata := server.Redis().Get(rediskey)
		if redisdata != nil {
			jdata := []interface{}{}
			json.Unmarshal(redisdata.([]byte), &jdata)
			ctx.Put("trx", jdata)
		} else {
			where := abugo.AbuDbWhere{}
			// where.Add("and", "SellerId", "=", SellerId, 1)
			where.Add("and", "SellerId", "in", "(1, 2, 4)", "")
			where.Add("and", "RankType", "=", reqdata.RankType, 0)
			where.Add("and", "Symbol", "=", "trx", "")
			presult, err := server.Db().Table("x_rank").Where(where).OrderBy("`rank` asc").Limit(20).GetList()
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.Put("trx", *presult)
			if len(*presult) > 0 {
				server.Redis().SetEx(rediskey, 10, *presult)
			}
		}
	}
	{
		rediskey := fmt.Sprintf("%s:%s:rank_usdt:%d:%d", server.Project(), server.Module(), SellerId, reqdata.RankType)
		redisdata := server.Redis().Get(rediskey)
		if redisdata != nil {
			jdata := []interface{}{}
			json.Unmarshal(redisdata.([]byte), &jdata)
			ctx.Put("usdt", jdata)
		} else {
			where := abugo.AbuDbWhere{}
			// where.Add("and", "SellerId", "=", SellerId, 1)
			where.Add("and", "SellerId", "in", "(1, 2, 4)", "")
			where.Add("and", "RankType", "=", reqdata.RankType, 0)
			where.Add("and", "Symbol", "=", "usdt", "")
			presult, err := server.Db().Table("x_rank").Where(where).OrderBy("`rank` asc").Limit(20).GetList()
			if ctx.RespErr(err, &errcode) {
				return
			}
			ctx.Put("usdt", *presult)
			if len(*presult) > 0 {
				server.Redis().SetEx(rediskey, 10, *presult)
			}
		}
	}
	ctx.RespOK()
}

/**
 * 新排行榜
 */
func (c *RankController) newData(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SellerId int32  `validate:"required"` // 运营商
		GameType int32  `validate:"required"` // 1哈希游戏 100三方游戏
		RankType int32  `validate:"required"` // 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行
		Symbol   string `validate:"required"` // 币种
	}

	type result struct {
		model.XRankNew
		HeadId     int64 `json:"HeadId"`
		Icon       string
		GameName   string
		GameEnName string
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	_, sellerId := server.GetChannel(ctx, "")

	gameDao := server.DaoxHashGame().XGameList
	rankDao := server.DaoxHashGame().XRankNew
	userDao := server.DaoxHashGame().XUser
	rankDb := rankDao.WithContext(nil)

	query := rankDb.
		Select(rankDao.ALL, userDao.HeadID.As("HeadId"), gameDao.Icon, gameDao.Name.As("GameName"), gameDao.EName.As("GameEnName")).
		LeftJoin(userDao, userDao.UserID.EqCol(rankDao.UserID)).
		LeftJoin(gameDao, gameDao.GameID.EqCol(rankDao.GameID), gameDao.Brand.EqCol(rankDao.Brand)).
		Where(rankDao.GameType.Eq(reqdata.GameType)).
		Where(rankDao.Symbol.Eq(reqdata.Symbol)).
		Where(rankDao.RankType.Eq(reqdata.RankType)).
		Where(rankDao.SellerID.Eq(int32(sellerId)))

	if reqdata.RankType != 18 {
		query.Order(rankDao.Rank.Asc())
	}

	var data []result

	_ = query.Scan(&data)

	if data == nil {
		ctx.RespOK([]result{})
		return
	}

	for i, _ := range data {
		if len(data[i].UserID) > 10 {
			data[i].UserID = hideMiddle(data[i].UserID, 4)
		} else {
			data[i].UserID = hideMiddle(data[i].UserID, 2)
		}

		if data[i].Icon != "" {
			// 获取SellerId
			sellerId := 0
			host := ctx.Host()
			_, sellerId = server.GetChannel(ctx, host)
			data[i].Icon = server.GetImageUrl(sellerId) + data[i].Icon
		}

	}

	ctx.RespOK(data)
}

func (c *RankController) newDataV3(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		RankType int32 `validate:"required"` // 15下注日排行New 16下注周排行New 17下注月排行New 18实时投注排行
		Host     string
	}

	errCode := 0
	req := RequestData{}
	if err := ctx.RequestData(&req); ctx.RespErr(err, &errCode) {
		return
	}

	// sellerId := 20
	_, sellerId := server.GetChannel(ctx, req.Host)

	type OnlineData struct {
		Icon         string  `gorm:"column:Icon"`
		EIcon        string  `gorm:"column:EIcon"`
		Name         string  `gorm:"column:Name"`
		EName        string  `gorm:"column:EName"`
		UserId       string  `gorm:"column:UserId"`
		RewardRate   float64 `gorm:"column:RewardRate"`
		Amount       float64 `gorm:"column:Amount"`
		RewardAmount float64 `gorm:"column:RewardAmount"`
		Symbol       string  `gorm:"column:Symbol"`
		GameType     int     `gorm:"column:GameType"`
		GameId       string  `gorm:"column:GameId"`
		RewardTime   string  `gorm:"column:RewardTime"`
		VipLevel     int     `gorm:"column:VipLevel"`
	}

	type OtherData struct {
		Rank       int     `gorm:"column:Rank"`
		UserId     string  `gorm:"column:UserId"`
		Amount     float64 `gorm:"column:Amount"`
		Symbol     string  `gorm:"column:Symbol"`
		GameType   int     `gorm:"column:GameType"`
		GameId     string  `gorm:"column:GameId"`
		RewardTime string  `gorm:"column:RewardTime"`
		VipLevel   int     `gorm:"column:VipLevel"`
	}

	gameDao := server.DaoxHashGame().XGameList
	XRankDatum := server.DaoxHashGame().XRankDatum

	query := XRankDatum.WithContext(nil).
		Where(XRankDatum.SellerID.Eq(int32(sellerId))).
		Where(XRankDatum.RankType.Eq(req.RankType))

	if req.RankType == 18 {
		var onlineData []OnlineData
		err := query.
			LeftJoin(gameDao, gameDao.GameID.EqCol(XRankDatum.GameID), gameDao.Brand.EqCol(XRankDatum.Brand)).
			Select(XRankDatum.UserID, XRankDatum.RewardRate, XRankDatum.Amount, XRankDatum.RewardAmount, XRankDatum.Symbol, XRankDatum.GameType, XRankDatum.GameID, XRankDatum.VipLevel, XRankDatum.RewardTime, gameDao.Icon, gameDao.EIcon, gameDao.Name, gameDao.EName).
			Scan(&onlineData)

		if err != nil {
			logs.Error("rank获取数据失败", err)
			ctx.RespErrString(true, &errCode, "获取数据失败")
			return
		}

		if len(onlineData) == 0 {
			ctx.RespOK([]OnlineData{})
			return
		}

		// Calculate RewardAmount - Amount for each record
		for i := range onlineData {
			onlineData[i].RewardAmount = onlineData[i].RewardAmount - onlineData[i].Amount
		}

		ctx.RespOK(onlineData)
		return
	} else {
		var otherData []OtherData
		err := query.
			Select(XRankDatum.Rank, XRankDatum.UserID, XRankDatum.Amount, XRankDatum.Symbol, XRankDatum.GameType, XRankDatum.GameID, XRankDatum.VipLevel, XRankDatum.RewardTime).
			Order(XRankDatum.Rank.Asc()).
			Scan(&otherData)

		if err != nil {
			logs.Error("rank获取数据失败", err)
			ctx.RespErrString(true, &errCode, "获取数据失败")
			return
		}

		if len(otherData) == 0 {
			ctx.RespOK([]OtherData{})
			return
		}

		ctx.RespOK(otherData)
		return
	}
}

func hideMiddle(s string, num int) string {
	// 获取字符串长度
	length := len(s)

	// 如果字符串长度小于等于8，无法隐藏中间部分，直接返回原字符串
	if length <= num*2 {
		return s
	}

	// 获取前后各4位
	prefix := s[:num]
	suffix := s[length-num:]

	// 计算中间需要隐藏的字符数量
	middleLength := 4

	// 用'*'替换中间的字符
	middle := strings.Repeat("*", middleLength)

	// 返回处理后的字符串
	return prefix + middle + suffix
}
