package active

import (
	"context"
	"encoding/json"
	"errors"
	"time"
	"xserver/abugo"
	"xserver/controller/datapush"
	"xserver/gormgen/xHashGame/dao"
	model2 "xserver/gormgen/xHashGame/model"
	"xserver/model"

	"xserver/server"
	"xserver/utils"

	"gorm.io/gorm"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
	"github.com/zeromicro/go-zero/core/threading"
)

// 获取活动定义
func GetActiveDefine(sellerId, channelId, activeId int32) (model.ActiveDefine, error) {
	// 使用 gen 方式查询
	activeDefineTb := server.DaoxHashGame().XActiveDefine
	activeDefineDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	// 执行查询
	aDefine, err := activeDefineDb.Where(activeDefineTb.SellerID.Eq(sellerId)).
		Where(activeDefineTb.ChannelID.Eq(channelId)).
		Where(activeDefineTb.ActiveID.Eq(activeId)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Info("GetActiveDefine 未找到活动配置: SellerId=%d, ChannelId=%d, ActiveId=%d",
				sellerId, channelId, activeId)
		} else {
			logs.Error("GetActiveDefine 查询错误: SellerId=%d, ChannelId=%d, ActiveId=%d, Error=%v",
				sellerId, channelId, activeId, err)
		}
		return model.ActiveDefine{}, err
	}

	logs.Info("GetActiveDefine 查询成功: SellerId=%d, ChannelId=%d, ActiveId=%d, Title=%s, State=%d",
		sellerId, channelId, activeId, aDefine.Title, aDefine.State)

	// 将 gen 模型转换为原始模型
	result := model.ActiveDefine{
		Id:              int(aDefine.ID),
		SellerId:        int(aDefine.SellerID),
		ChannelId:       int(aDefine.ChannelID),
		ActiveId:        int(aDefine.ActiveID),
		Memo:            aDefine.Memo,
		AuditType:       int(aDefine.AuditType),
		State:           int(aDefine.State),
		Sort:            int(aDefine.Sort),
		EffectStartTime: aDefine.EffectStartTime,
		EffectEndTime:   aDefine.EffectEndTime,
		Title:           aDefine.Title,
		TitleImg:        aDefine.TitleImg,
		MinLiuShui:      decimal.NewFromFloat(aDefine.MinLiuShui),
		ExtReward:       decimal.NewFromFloat(aDefine.ExtReward),
		MinDeposit:      decimal.NewFromFloat(aDefine.MinDeposit),
		MaxReward:       decimal.NewFromFloat(aDefine.MaxReward),
		ValidRecharge:   decimal.NewFromFloat(aDefine.ValidRecharge),
		ValidLiuShui:    decimal.NewFromFloat(aDefine.ValidLiuShui),
		TrxPrice:        decimal.NewFromFloat(aDefine.TrxPrice),
		Config:          aDefine.Config,
		BaseConfig:      aDefine.BaseConfig,
		GameType:        aDefine.GameType,
	}

	return result, nil
}

func VerifyActiveDefine(ADefine model.ActiveDefine, tn time.Time) (bool, error) {
	//筛选出符合条件的次日奖励活动项
	if ADefine.Id == 0 {
		return false, errors.New(utils.ActiveRNotDefine)
	}
	//校验状态
	if ADefine.State != utils.ActiveStateOpen {
		return false, errors.New(utils.ActiveRTimeExpire)
	}
	//校验时间
	if ADefine.EffectStartTime != 0 &&
		ADefine.EffectEndTime != 0 {
		LTime, RTime := time.UnixMilli(ADefine.EffectStartTime), time.UnixMilli(ADefine.EffectEndTime)
		if !utils.IsTimeBetween(tn, LTime, RTime) {
			return false, errors.New(utils.ActiveRTimeExpire)
		}
	}
	return true, nil
}

func VerifyActiveInfo(AInfo model.ActiveInfo) error {
	if AInfo.Id == 0 || AInfo.Level == 0 {
		return errors.New(utils.ActiveRLevelErr)
	}
	return nil
}

func SaveActiveData(tx *dao.Query, user *model2.XUser, dataInfo SaveActiveDataInfo) error {
	defer recover()
	if tx == nil {
		tx = server.DaoxHashGame()
	}
	now := carbon.Parse(carbon.Now().String()).StdTime()
	date := carbon.Parse(carbon.Now().StartOfDay().String()).StdTime()
	minLiuShui, _ := dataInfo.MinLiuShui.Float64()
	if dataInfo.AuditType == 1 { // 需要审核
		rerr := tx.Transaction(func(tx *dao.Query) error {
			// 玩家日统计
			userDaillyTb := tx.XUserDailly
			userDaillyDb := tx.XUserDailly.WithContext(context.Background())
			_, err := userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					userDailly := &model2.XUserDailly{
						SellerID:   dataInfo.SellerID,
						ChannelID:  dataInfo.ChannelID,
						UserID:     user.UserID,
						RecordDate: date,
					}
					err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
					if err != nil {
						logs.Error(err)
						return err
					}
				} else {
					logs.Error(err)
					return err
				}
			}
			data := &model2.XActiveRewardAudit{
				SellerID:          dataInfo.SellerID,
				ChannelID:         dataInfo.ChannelID,
				UserID:            user.UserID,
				ActiveID:          int32(dataInfo.ActiveId),
				ActiveMemo:        dataInfo.ActiveMemo,
				ActiveLevel:       int32(dataInfo.Level),
				RecordDate:        now,
				Amount:            dataInfo.RealAmount,
				AuditState:        1,
				CreateTime:        now,
				LiuShui:           dataInfo.TotalLiushui,
				Config:            string(dataInfo.ConfigStr),
				FirstRecharge:     dataInfo.FirstRecharge,
				BaseConfig:        string(dataInfo.BastConfigStr),
				TotalRecharge:     dataInfo.TotalRecharge,
				InviteRewardType:  dataInfo.InviteRewardType,
				ChildUserID:       dataInfo.ChildUserID,
				InviteRewardUsers: dataInfo.InviteRewardUsers,
				OrderID:           dataInfo.OrderId,
				GameType:          dataInfo.GameType,
				MinLiuShui:        minLiuShui,
				UserIP:            dataInfo.UserIP,
				DeviceID:          dataInfo.DeviceID,
				RedeemCode:        dataInfo.RedeemCode,
			}
			// 签到时间
			if dataInfo.FirstSignTime != nil {
				data.FirstSignTime = *dataInfo.FirstSignTime
			}
			if dataInfo.LastSignTime != nil {
				data.LastSignTime = *dataInfo.LastSignTime
			}
			activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
			activeRewardAuditDb := tx.XActiveRewardAudit.WithContext(context.Background())
			if dataInfo.FirstSignTime == nil && dataInfo.LastSignTime == nil {
				activeRewardAuditDb = activeRewardAuditDb.Omit(activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime)
			}
			err = activeRewardAuditDb.Select(activeRewardAuditTb.SellerID, activeRewardAuditTb.ChannelID,
				activeRewardAuditTb.UserID, activeRewardAuditTb.ActiveID, activeRewardAuditTb.ActiveMemo,
				activeRewardAuditTb.ActiveLevel, activeRewardAuditTb.RecordDate, activeRewardAuditTb.Amount,
				activeRewardAuditTb.AuditState, activeRewardAuditTb.CreateTime, activeRewardAuditTb.LiuShui,
				activeRewardAuditTb.Config, activeRewardAuditTb.FirstRecharge, activeRewardAuditTb.BaseConfig,
				activeRewardAuditTb.TotalRecharge, activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime,
				activeRewardAuditTb.InviteRewardType, activeRewardAuditTb.ChildUserID, activeRewardAuditTb.InviteRewardUsers,
				activeRewardAuditTb.OrderID, activeRewardAuditTb.GameType, activeRewardAuditTb.MinLiuShui,
				activeRewardAuditTb.UserIP, activeRewardAuditTb.DeviceID, activeRewardAuditTb.RedeemCode,
			).Create(data)
			if err != nil {
				logs.Error(err)
				return err
			}

			// 发送活动奖励事件到ThinkingData（需要审核的活动）
			threading.GoSafe(func() {
				clientIP := dataInfo.UserIP
				if clientIP == "" {
					clientIP = user.LoginIP
					if clientIP == "" {
						clientIP = "unknown"
					}
				}

				err := datapush.SendActivityRewardEvent(user, dataInfo.ActiveId, dataInfo.ActiveName, dataInfo.RealAmount, clientIP)
				if err != nil {
					logs.Error("发送活动奖励事件到ThinkingData失败:", err, "用户ID:", user.UserID, "活动ID:", dataInfo.ActiveId)
				}
			})

			return nil
		})
		if rerr != nil {
			return rerr
		}
	} else {
		rerr := tx.Transaction(func(tx *dao.Query) error {
			// 玩家日统计
			userDaillyTb := tx.XUserDailly
			userDaillyDb := tx.XUserDailly.WithContext(context.Background())
			_, err := userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).First()
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					userDailly := &model2.XUserDailly{
						SellerID:   dataInfo.SellerID,
						ChannelID:  dataInfo.ChannelID,
						UserID:     user.UserID,
						RecordDate: date,
					}
					err = userDaillyDb.Select(userDaillyTb.SellerID, userDaillyTb.ChannelID, userDaillyTb.UserID, userDaillyTb.RecordDate).Create(userDailly)
					if err != nil {
						logs.Error(err)
						return err
					}
				} else {
					logs.Error(err)
					return err
				}
			}
			reward := &model2.XActiveReward{
				SellerID:   dataInfo.SellerID,
				ChannelID:  dataInfo.ChannelID,
				UserID:     user.UserID,
				State:      4,
				ActiveID:   10000 + int32(dataInfo.ActiveId),
				ActiveName: dataInfo.ActiveName,
				Amount:     dataInfo.RealAmount,
				AuditTime:  now,
				CreateTime: now,
			}
			activeRewardDb := tx.XActiveReward.WithContext(context.Background())
			err = activeRewardDb.Create(reward)
			if err != nil {
				logs.Error(err)
				return err
			}
			audit := &model2.XActiveRewardAudit{
				SellerID:          dataInfo.SellerID,
				ChannelID:         dataInfo.ChannelID,
				UserID:            user.UserID,
				ActiveID:          int32(dataInfo.ActiveId),
				ActiveMemo:        dataInfo.ActiveMemo,
				ActiveLevel:       int32(dataInfo.Level),
				RecordDate:        now,
				Amount:            dataInfo.RealAmount,
				AuditState:        4,
				CreateTime:        now,
				AuditTime:         now,
				LiuShui:           dataInfo.TotalLiushui,
				Config:            string(dataInfo.ConfigStr),
				FirstRecharge:     dataInfo.FirstRecharge,
				BaseConfig:        string(dataInfo.BastConfigStr),
				TotalRecharge:     dataInfo.TotalRecharge,
				InviteRewardType:  dataInfo.InviteRewardType,
				ChildUserID:       dataInfo.ChildUserID,
				InviteRewardUsers: dataInfo.InviteRewardUsers,
				OrderID:           dataInfo.OrderId,
				GameType:          dataInfo.GameType,
				MinLiuShui:        minLiuShui,
				UserIP:            dataInfo.UserIP,
				DeviceID:          dataInfo.DeviceID,
				RedeemCode:        dataInfo.RedeemCode,
			}
			// 签到时间
			if dataInfo.FirstSignTime != nil {
				audit.FirstSignTime = *dataInfo.FirstSignTime
			}
			if dataInfo.LastSignTime != nil {
				audit.LastSignTime = *dataInfo.LastSignTime
			}
			activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
			activeRewardAuditDb := tx.XActiveRewardAudit.WithContext(context.Background())
			if dataInfo.FirstSignTime == nil && dataInfo.LastSignTime == nil {
				activeRewardAuditDb = activeRewardAuditDb.Omit(activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime)
			}
			err = activeRewardAuditDb.Select(activeRewardAuditTb.SellerID, activeRewardAuditTb.ChannelID,
				activeRewardAuditTb.UserID, activeRewardAuditTb.ActiveID, activeRewardAuditTb.ActiveMemo,
				activeRewardAuditTb.ActiveLevel, activeRewardAuditTb.RecordDate, activeRewardAuditTb.Amount,
				activeRewardAuditTb.AuditState, activeRewardAuditTb.CreateTime, activeRewardAuditTb.AuditTime, activeRewardAuditTb.LiuShui,
				activeRewardAuditTb.Config, activeRewardAuditTb.FirstRecharge, activeRewardAuditTb.BaseConfig,
				activeRewardAuditTb.TotalRecharge, activeRewardAuditTb.FirstSignTime, activeRewardAuditTb.LastSignTime,
				activeRewardAuditTb.InviteRewardType, activeRewardAuditTb.ChildUserID, activeRewardAuditTb.InviteRewardUsers,
				activeRewardAuditTb.OrderID, activeRewardAuditTb.GameType, activeRewardAuditTb.MinLiuShui,
				activeRewardAuditTb.UserIP, activeRewardAuditTb.DeviceID, activeRewardAuditTb.RedeemCode,
			).Create(audit)
			if err != nil {
				logs.Error(err)
				return err
			}
			userTb := server.DaoxHashGame().XUser
			userDb := tx.XUser.WithContext(context.Background())
			if user.WithdrawLiuSui > user.TotalLiuSui {
				_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
					"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
					"WithdrawLiuSui": gorm.Expr("WithdrawLiuSui + ?", dataInfo.WithdrawLiuSuiAdd),
				})
			} else {
				_, err = userDb.Where(userTb.UserID.Eq(user.UserID)).Updates(map[string]any{
					"Amount":         gorm.Expr("Amount + ?", dataInfo.RealAmount),
					"WithdrawLiuSui": dataInfo.WithdrawLiuSuiAdd,
					"TotalLiuSui":    0,
				})
			}
			if err != nil {
				logs.Error(err)
				return err
			}

			amountChangeLog := &model2.XAmountChangeLog{
				UserID:       user.UserID,
				BeforeAmount: user.Amount,
				Amount:       dataInfo.RealAmount,
				AfterAmount:  user.Amount + dataInfo.RealAmount,
				Reason:       int32(dataInfo.BalanceCReason),
				Memo:         dataInfo.ActiveName,
				SellerID:     dataInfo.SellerID,
				ChannelID:    dataInfo.ChannelID,
			}
			amountChangeLogDB := tx.XAmountChangeLog.WithContext(context.Background())
			err = amountChangeLogDB.Create(amountChangeLog)
			if err != nil {
				logs.Error(err)
				return err
			}

			_, err = userDaillyDb.Where(userDaillyTb.UserID.Eq(user.UserID)).Where(userDaillyTb.RecordDate.Eq(date)).Updates(map[string]any{
				"TotalCaiJin": gorm.Expr("TotalCaiJin + ?", dataInfo.RealAmount),
			})

			vipInfoTb := tx.XVipInfo
			vipInfoDb := tx.XVipInfo.WithContext(context.Background())
			_, err = vipInfoDb.Where(vipInfoTb.UserID.Eq(user.UserID)).Updates(map[string]any{
				"CaiJin": gorm.Expr("CaiJin + ?", dataInfo.RealAmount),
			})
			if dataInfo.RealAmount > 0 {
				caijingDetailDb := tx.XCaijingDetail.WithContext(context.Background())
				caijingDetail := &model2.XCaijingDetail{
					UserID:     user.UserID,
					SType:      dataInfo.ActiveName,
					Symbol:     "usdt",
					Amount:     dataInfo.RealAmount,
					CSGroup:    user.CSGroup,
					CSID:       user.CSID,
					TopAgentID: user.TopAgentID,
					MinLiuShui: minLiuShui,
				}
				err = caijingDetailDb.Create(caijingDetail)
				if err != nil {
					logs.Error(err)
					return err
				}
			}
			// 彩金统计
			raw := server.Db().Gorm().Raw("CALL SystemManage_x_user_reward_commission_Stat(?,?,?,?,?,?,?,?)", now.String(), user.UserID,
				dataInfo.SellerID, dataInfo.ChannelID, user.TopAgentID, 1, 1, dataInfo.RealAmount)
			err = raw.Error
			if err != nil {
				logs.Error("CALL SystemManage_x_user_reward_commission_Stat err :", err)
				logs.Error("CALL SystemManage_x_user_reward_commission_Stat parameter :", now.String(), user.UserID,
					dataInfo.SellerID, dataInfo.ChannelID, user.TopAgentID, 1, 1, dataInfo.RealAmount)
				return err
			}

			// 发送活动奖励事件到ThinkingData（不需要审核的活动）
			threading.GoSafe(func() {
				clientIP := dataInfo.UserIP
				if clientIP == "" {
					clientIP = user.LoginIP
					if clientIP == "" {
						clientIP = "unknown"
					}
				}

				err := datapush.SendActivityRewardEvent(user, dataInfo.ActiveId, dataInfo.ActiveName, dataInfo.RealAmount, clientIP)
				if err != nil {
					logs.Error("发送活动奖励事件到ThinkingData失败:", err, "用户ID:", user.UserID, "活动ID:", dataInfo.ActiveId)
				}
			})

			return nil
		})
		if rerr != nil {
			return rerr
		}
	}
	return nil
}

// 计算用户总流水
// userId: 用户ID
// startTime: 开始时间
// endTime: 结束时间
// minBetAmount: 单笔投注最小限制，为0表示不限制
// maxBetAmount: 单笔投注最大限制，为0表示不限制
// isExcludeBetLimit: 是否排除超出投注限制的流水
// sellerId: 运营商ID
// channelId: 渠道ID
// 返回: 总流水, 错误信息
func CalculateUserTotalFlow(user *model2.XUser, startTime time.Time, endTime time.Time, minBetAmount, maxBetAmount decimal.Decimal, isExcludeBetLimit bool) (decimal.Decimal, error) {
	// 初始化总流水为0
	total := decimal.NewFromFloat(0)

	// 定义流水汇总结构体
	type BetAmountSum struct {
		TotalBetAmount decimal.Decimal
	}

	// 1. 查询 x_order 表中的 amount 字段
	orderTb := server.DaoxHashGame().XOrder
	orderDb := server.DaoxHashGame().XOrder.WithContext(context.Background())

	type OrderAmountSum struct {
		TotalAmount decimal.Decimal
	}
	var orderAmountSum OrderAmountSum

	var err error

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		orderQuery := orderDb.Where(orderTb.UserID.Eq(user.UserID)).
			Where(orderTb.SellerID.Eq(user.SellerID)).
			Where(orderTb.ChannelID.Eq(user.ChannelID)).
			Where(orderTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			orderQuery = orderQuery.Where(orderTb.ValidBetAmount.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			orderQuery = orderQuery.Where(orderTb.ValidBetAmount.Lte(maxBetFloat))
		}

		// 执行查询
		err = orderQuery.Select(orderTb.ValidBetAmount.Sum().As("TotalAmount")).
			Scan(&orderAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_order amount with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = orderDb.Select(orderTb.ValidBetAmount.Sum().As("TotalAmount")).
			Where(orderTb.UserID.Eq(user.UserID)).
			Where(orderTb.SellerID.Eq(user.SellerID)).
			Where(orderTb.ChannelID.Eq(user.ChannelID)).
			Where(orderTb.CreateTime.Between(startTime, endTime)).
			Scan(&orderAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_order amount err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_order 表的流水
	total = total.Add(orderAmountSum.TotalAmount)

	// 2. 查询 x_third_dianzhi 表中的 ValidBet 字段（有效流水）
	thirdDianzhiTb := server.DaoxHashGame().XThirdDianzhi
	thirdDianzhiDb := server.DaoxHashGame().XThirdDianzhi.WithContext(context.Background())

	var dianzhiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		dianzhiQuery := thirdDianzhiDb.Where(thirdDianzhiTb.UserID.Eq(user.UserID)).
			Where(thirdDianzhiTb.SellerID.Eq(user.SellerID)).
			Where(thirdDianzhiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdDianzhiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			dianzhiQuery = dianzhiQuery.Where(thirdDianzhiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			dianzhiQuery = dianzhiQuery.Where(thirdDianzhiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = dianzhiQuery.Select(thirdDianzhiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&dianzhiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_dianzhi ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdDianzhiDb.Select(thirdDianzhiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdDianzhiTb.UserID.Eq(user.UserID)).
			Where(thirdDianzhiTb.SellerID.Eq(user.SellerID)).
			Where(thirdDianzhiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdDianzhiTb.CreateTime.Between(startTime, endTime)).
			Scan(&dianzhiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_dianzhi ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_dianzhi 表的流水
	total = total.Add(dianzhiBetAmountSum.TotalBetAmount)

	// 3. 查询 x_third_live 表中的 ValidBet 字段（有效流水）
	thirdLiveTb := server.DaoxHashGame().XThirdLive
	thirdLiveDb := server.DaoxHashGame().XThirdLive.WithContext(context.Background())

	var liveBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		liveQuery := thirdLiveDb.Where(thirdLiveTb.UserID.Eq(user.UserID)).
			Where(thirdLiveTb.SellerID.Eq(user.SellerID)).
			Where(thirdLiveTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLiveTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			liveQuery = liveQuery.Where(thirdLiveTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			liveQuery = liveQuery.Where(thirdLiveTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = liveQuery.Select(thirdLiveTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&liveBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_live ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdLiveDb.Select(thirdLiveTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdLiveTb.UserID.Eq(user.UserID)).
			Where(thirdLiveTb.SellerID.Eq(user.SellerID)).
			Where(thirdLiveTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLiveTb.CreateTime.Between(startTime, endTime)).
			Scan(&liveBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_live ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_live 表的流水
	total = total.Add(liveBetAmountSum.TotalBetAmount)

	// 4. 查询 x_third_lottery 表中的 ValidBet 字段（有效流水）
	thirdLotteryTb := server.DaoxHashGame().XThirdLottery
	thirdLotteryDb := server.DaoxHashGame().XThirdLottery.WithContext(context.Background())

	var lotteryBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		lotteryQuery := thirdLotteryDb.Where(thirdLotteryTb.UserID.Eq(user.UserID)).
			Where(thirdLotteryTb.SellerID.Eq(user.SellerID)).
			Where(thirdLotteryTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLotteryTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			lotteryQuery = lotteryQuery.Where(thirdLotteryTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			lotteryQuery = lotteryQuery.Where(thirdLotteryTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = lotteryQuery.Select(thirdLotteryTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&lotteryBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_lottery ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdLotteryDb.Select(thirdLotteryTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdLotteryTb.UserID.Eq(user.UserID)).
			Where(thirdLotteryTb.SellerID.Eq(user.SellerID)).
			Where(thirdLotteryTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdLotteryTb.CreateTime.Between(startTime, endTime)).
			Scan(&lotteryBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_lottery ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_lottery 表的流水
	total = total.Add(lotteryBetAmountSum.TotalBetAmount)

	// 5. 查询 x_third_qipai 表中的 ValidBet 字段（有效流水）
	thirdQipaiTb := server.DaoxHashGame().XThirdQipai
	thirdQipaiDb := server.DaoxHashGame().XThirdQipai.WithContext(context.Background())

	var qipaiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		qipaiQuery := thirdQipaiDb.Where(thirdQipaiTb.UserID.Eq(user.UserID)).
			Where(thirdQipaiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQipaiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQipaiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			qipaiQuery = qipaiQuery.Where(thirdQipaiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			qipaiQuery = qipaiQuery.Where(thirdQipaiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = qipaiQuery.Select(thirdQipaiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&qipaiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_qipai ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdQipaiDb.Select(thirdQipaiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdQipaiTb.UserID.Eq(user.UserID)).
			Where(thirdQipaiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQipaiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQipaiTb.CreateTime.Between(startTime, endTime)).
			Scan(&qipaiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_qipai ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_qipai 表的流水
	total = total.Add(qipaiBetAmountSum.TotalBetAmount)

	// 6. 查询 x_third_quwei 表中的 ValidBet 字段（有效流水）
	thirdQuweiTb := server.DaoxHashGame().XThirdQuwei
	thirdQuweiDb := server.DaoxHashGame().XThirdQuwei.WithContext(context.Background())

	var quweiBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		quweiQuery := thirdQuweiDb.Where(thirdQuweiTb.UserID.Eq(user.UserID)).
			Where(thirdQuweiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQuweiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQuweiTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			quweiQuery = quweiQuery.Where(thirdQuweiTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			quweiQuery = quweiQuery.Where(thirdQuweiTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = quweiQuery.Select(thirdQuweiTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&quweiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_quwei ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdQuweiDb.Select(thirdQuweiTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdQuweiTb.UserID.Eq(user.UserID)).
			Where(thirdQuweiTb.SellerID.Eq(user.SellerID)).
			Where(thirdQuweiTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdQuweiTb.CreateTime.Between(startTime, endTime)).
			Scan(&quweiBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_quwei ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_quwei 表的流水
	total = total.Add(quweiBetAmountSum.TotalBetAmount)

	// 7. 查询 x_third_sport 表中的 ValidBet 字段（有效流水）
	thirdSportTb := server.DaoxHashGame().XThirdSport
	thirdSportDb := server.DaoxHashGame().XThirdSport.WithContext(context.Background())

	var sportBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		sportQuery := thirdSportDb.Where(thirdSportTb.UserID.Eq(user.UserID)).
			Where(thirdSportTb.SellerID.Eq(user.SellerID)).
			Where(thirdSportTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdSportTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			sportQuery = sportQuery.Where(thirdSportTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			sportQuery = sportQuery.Where(thirdSportTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = sportQuery.Select(thirdSportTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&sportBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_sport ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdSportDb.Select(thirdSportTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdSportTb.UserID.Eq(user.UserID)).
			Where(thirdSportTb.SellerID.Eq(user.SellerID)).
			Where(thirdSportTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdSportTb.CreateTime.Between(startTime, endTime)).
			Scan(&sportBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_sport ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_sport 表的流水
	total = total.Add(sportBetAmountSum.TotalBetAmount)

	// 8. 查询 x_third_texas 表中的 ValidBet 字段（有效流水）
	thirdTexasTb := server.DaoxHashGame().XThirdTexa
	thirdTexasDb := server.DaoxHashGame().XThirdTexa.WithContext(context.Background())

	var texasBetAmountSum BetAmountSum

	// 如果需要排除超出投注限制的流水
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		// 构建查询条件
		texasQuery := thirdTexasDb.Where(thirdTexasTb.UserID.Eq(user.UserID)).
			Where(thirdTexasTb.SellerID.Eq(user.SellerID)).
			Where(thirdTexasTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdTexasTb.CreateTime.Between(startTime, endTime))

		// 添加最小投注限制
		if !minBetAmount.IsZero() {
			minBetFloat, _ := minBetAmount.Float64()
			texasQuery = texasQuery.Where(thirdTexasTb.ValidBet.Gte(minBetFloat))
		}

		// 添加最大投注限制
		if !maxBetAmount.IsZero() {
			maxBetFloat, _ := maxBetAmount.Float64()
			texasQuery = texasQuery.Where(thirdTexasTb.ValidBet.Lte(maxBetFloat))
		}

		// 执行查询
		err = texasQuery.Select(thirdTexasTb.ValidBet.Sum().As("TotalBetAmount")).
			Scan(&texasBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_texas ValidBet with bet limits err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	} else {
		// 不需要排除，查询所有流水
		err = thirdTexasDb.Select(thirdTexasTb.ValidBet.Sum().As("TotalBetAmount")).
			Where(thirdTexasTb.UserID.Eq(user.UserID)).
			Where(thirdTexasTb.SellerID.Eq(user.SellerID)).
			Where(thirdTexasTb.ChannelID.Eq(user.ChannelID)).
			Where(thirdTexasTb.CreateTime.Between(startTime, endTime)).
			Scan(&texasBetAmountSum)

		if err != nil {
			logs.Error("CalculateUserTotalFlow query x_third_texas ValidBet err", err)
			return decimal.Zero, errors.New("系统错误,请稍后再试")
		}
	}

	// 累加 x_third_texas 表的流水
	total = total.Add(texasBetAmountSum.TotalBetAmount)

	// 9. 查询 x_user_reduce 表中的 RealReduceLiuShui 字段（减免流水）
	userReduceTb := server.DaoxHashGame().XUserReduce
	userReduceDb := server.DaoxHashGame().XUserReduce.WithContext(context.Background())

	type ReduceLiuShuiSum struct {
		TotalReduceLiuShui decimal.Decimal
	}
	var reduceLiuShuiSum ReduceLiuShuiSum

	// 查询用户在指定时间范围内的减免流水总和
	err = userReduceDb.Select(userReduceTb.RealReduceLiuShui.Sum().As("TotalReduceLiuShui")).
		Where(userReduceTb.UserID.Eq(user.UserID)).
		Where(userReduceTb.CreateTime.Between(startTime, endTime)).
		Scan(&reduceLiuShuiSum)

	if err != nil {
		logs.Error("CalculateUserTotalFlow query x_user_reduce RealReduceLiuShui err", err)
		// 查询出错不影响其他流水计算，继续处理
	} else {
		// 累加减免流水
		total = total.Add(reduceLiuShuiSum.TotalReduceLiuShui)
	}

	// 记录日志，方便调试
	if isExcludeBetLimit && (!minBetAmount.IsZero() || !maxBetAmount.IsZero()) {
		logs.Info("用户活动期间总流水(已排除超出投注限制的流水) : 用户ID=%v, 最小投注限制=%v, 最大投注限制=%v, x_order=%v, x_third_dianzhi=%v, x_third_live=%v, x_third_lottery=%v, x_third_qipai=%v, x_third_quwei=%v, x_third_sport=%v, x_third_texas=%v, x_user_reduce=%v, total=%v",
			user.UserID,
			minBetAmount,
			maxBetAmount,
			orderAmountSum.TotalAmount,
			dianzhiBetAmountSum.TotalBetAmount,
			liveBetAmountSum.TotalBetAmount,
			lotteryBetAmountSum.TotalBetAmount,
			qipaiBetAmountSum.TotalBetAmount,
			quweiBetAmountSum.TotalBetAmount,
			sportBetAmountSum.TotalBetAmount,
			texasBetAmountSum.TotalBetAmount,
			reduceLiuShuiSum.TotalReduceLiuShui,
			total)
	} else {
		logs.Info("用户活动期间总流水 : 用户ID=%v, x_order=%v, x_third_dianzhi=%v, x_third_live=%v, x_third_lottery=%v, x_third_qipai=%v, x_third_quwei=%v, x_third_sport=%v, x_third_texas=%v, x_user_reduce=%v, total=%v",
			user.UserID,
			orderAmountSum.TotalAmount,
			dianzhiBetAmountSum.TotalBetAmount,
			liveBetAmountSum.TotalBetAmount,
			lotteryBetAmountSum.TotalBetAmount,
			qipaiBetAmountSum.TotalBetAmount,
			quweiBetAmountSum.TotalBetAmount,
			sportBetAmountSum.TotalBetAmount,
			texasBetAmountSum.TotalBetAmount,
			reduceLiuShuiSum.TotalReduceLiuShui,
			total)
	}

	return total, nil
}

// CheckActiveWithdrawable 检查用户是否满足活动流水要求，以确定是否可以提现
// 参数:
//   - userId: 用户ID
//   - activeId: 活动ID
//
// 返回:
//   - bool: 是否可以提现 (true: 可以, false: 不可以)
//   - string: 错误信息 (如果不可以提现)
//   - decimal.Decimal: 流水差额 (如果流水不足，返回还需要的流水金额；如果流水充足，返回0)
func CheckActiveWithdrawable(userId int32, activeId int32) (bool, string, decimal.Decimal) {
	logs.Info("CheckActiveWithdrawable 开始检查: 用户ID=%v, 活动ID=%v, 提现金额=%v", userId, activeId)
	// 获取用户信息
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	user, err := userDb.Where(userTb.UserID.Eq(userId)).First()
	if err != nil {
		logs.Error("CheckActiveWithdrawable 步骤4错误: 查询用户信息错误: %v", err)
		return false, "查询用户信息错误", decimal.Zero // 查询出错，阻止提现
	}
	// 1. 获取活动配置
	activeTb := server.DaoxHashGame().XActiveDefine
	activeDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	activeDefine, err := activeDb.Where(activeTb.ActiveID.Eq(activeId)).
		Where(activeTb.SellerID.Eq(user.SellerID)).
		Where(activeTb.ChannelID.Eq(user.ChannelID)).
		Where(activeTb.State.Eq(1)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 活动不存在，不阻止提现
			logs.Info("CheckActiveWithdrawable 步骤1通过: 活动ID=%v 不存在，不阻止提现", activeId)
			return true, "success", decimal.Zero
		}
		logs.Error("CheckActiveWithdrawable 步骤1错误: 获取活动配置错误: %v", err)
		return true, "success", decimal.Zero // 查询出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤1完成: 找到活动ID=%v, 活动标题=%v", activeId, activeDefine.Title)

	// 2. 查询用户是否参与了该活动，并且不是被拒绝的记录

	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())

	userActivity, err := activeRewardAuditDb.
		Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
		Where(activeRewardAuditTb.ActiveID.Eq(activeId)).
		Where(activeRewardAuditTb.SellerID.Eq(user.SellerID)).
		Where(activeRewardAuditTb.ChannelID.Eq(user.ChannelID)).
		Where(activeRewardAuditTb.AuditState.Neq(utils.ActiveAwardAuditStateRefuse)). // 排除被拒绝的记录
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户未参与活动或者所有记录都被拒绝，直接返回通过
			logs.Info("CheckActiveWithdrawable 步骤2通过: 用户ID=%v 未参与活动ID=%v 或所有记录都被拒绝，不阻止提现", userId, activeId)
			return true, "success", decimal.Zero
		}
		logs.Error("CheckActiveWithdrawable 步骤2错误: 查询用户活动记录错误: %v", err)
		return true, "success", decimal.Zero // 查询出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤2完成: 用户ID=%v 参与了活动ID=%v, 活动记录ID=%v, 审核状态=%v", userId, activeId, userActivity.ID, userActivity.AuditState)

	// 3. 获取活动流水要求
	requiredLiuShui := userActivity.MinLiuShui
	logs.Info("CheckActiveWithdrawable 步骤3: 活动流水要求=%v", requiredLiuShui)

	// 如果没有要求流水，直接通过
	if requiredLiuShui <= 0 {
		logs.Info("CheckActiveWithdrawable 步骤3通过: 活动没有流水要求(requiredLiuShui=%v)，不阻止提现", requiredLiuShui)
		return true, "success", decimal.Zero
	}

	logs.Info("CheckActiveWithdrawable 步骤4完成: 用户ID=%v, 注册时间=%v, 当前总流水=%v, 提现流水要求=%v",
		userId, user.RegisterTime, user.TotalLiuSui, user.WithdrawLiuSui)

	// 5. 解析活动配置，获取投注限制参数
	var minBetAmount, maxBetAmount decimal.Decimal
	var isExcludeBetLimit bool

	// 根据活动ID判断活动类型，解析不同的配置
	if activeId == utils.FirstDepositGift || activeId == utils.MultipleDepositGift { // 首充复充活动
		var baseData DepositBaseConfig
		err = json.Unmarshal([]byte(activeDefine.BaseConfig), &baseData)
		if err != nil {
			logs.Error("CheckActiveWithdrawable 步骤5错误: 解析活动配置错误: %v", err)
		} else {
			minBetAmount = baseData.MinBetAmount
			maxBetAmount = baseData.MaxBetAmount
			isExcludeBetLimit = !minBetAmount.IsZero() || !maxBetAmount.IsZero()
			logs.Info("CheckActiveWithdrawable 步骤5完成: 活动ID=%v, 最小投注限制=%v, 最大投注限制=%v",
				activeId, minBetAmount, maxBetAmount)
		}
	}

	// 6. 计算用户在活动期间的总流水
	var startTime, endTime time.Time

	// 对于复充活动，流水计算从当天开始
	if activeId == utils.MultipleDepositGift {
		// 复充活动的流水计算从当天开始
		today := time.Now().Format("2006-01-02")
		startTime, _ = time.Parse("2006-01-02", today)
		endTime = time.Now()
		logs.Info("CheckActiveWithdrawable 步骤6: 复充活动流水计算从当天开始: 开始时间=%v, 结束时间=%v", startTime, endTime)
	} else {
		// 其他活动使用原有逻辑
		// 如果活动有明确的开始和结束时间，使用活动时间范围
		if activeDefine.EffectStartTime > 0 {
			startTime = time.UnixMilli(activeDefine.EffectStartTime)
		} else {
			// 如果活动没有明确的开始时间，使用用户注册时间
			startTime = user.RegisterTime
		}

		if activeDefine.EffectEndTime > 0 {
			endTime = time.UnixMilli(activeDefine.EffectEndTime)
			// 如果当前时间早于活动结束时间，使用当前时间
			if time.Now().Before(endTime) {
				endTime = time.Now()
			}
		} else {
			// 如果活动没有明确的结束时间，使用当前时间
			endTime = time.Now()
		}
		logs.Info("CheckActiveWithdrawable 步骤6: 其他活动流水计算时间范围: 开始时间=%v, 结束时间=%v", startTime, endTime)
	}

	// 使用封装的函数计算用户总流水
	total, err := CalculateUserTotalFlow(user, startTime, endTime, minBetAmount, maxBetAmount, isExcludeBetLimit)
	if err != nil {
		logs.Error("CheckActiveWithdrawable 步骤6错误: 计算用户总流水错误: %v", err)
		return true, "success", decimal.Zero // 计算出错，不阻止提现
	}
	logs.Info("CheckActiveWithdrawable 步骤6完成: 计算得到用户总流水=%v", total)

	// 7. 检查用户总流水是否达到要求
	requiredLiushuiDecimal := decimal.NewFromFloat(requiredLiuShui)
	isEnough := total.GreaterThanOrEqual(requiredLiushuiDecimal)

	// 记录日志，方便调试
	logs.Info("CheckActiveWithdrawable 步骤7: 用户提现条件检查: 用户ID=%v, 活动ID=%v, 当前总流水=%s, 需要完成的总流水=%v, 是否满足条件=%v",
		userId, activeId, total.StringFixed(2), requiredLiuShui, isEnough)

	if !isEnough {
		// 计算差额并保留小数点后2位
		diff := requiredLiushuiDecimal.Sub(total)
		// 使用Round(2)进行四舍五入，然后使用StringFixed(2)格式化为2位小数
		roundedDiff := diff.Round(2)
		logs.Info("CheckActiveWithdrawable 步骤7结果: 流水不足! 需要流水=%s, 当前流水=%s, 差额=%s, 四舍五入后=%s",
			requiredLiushuiDecimal.StringFixed(2), total.StringFixed(2), diff.StringFixed(2), roundedDiff.StringFixed(2))
		return false, "流水不足，还需要" + roundedDiff.StringFixed(2), roundedDiff
	}

	logs.Info("CheckActiveWithdrawable 步骤7结果: 流水充足! 用户ID=%v, 活动ID=%v 通过检查", userId, activeId)
	return true, "success", decimal.Zero
}

// AutoGiveDepositGift 自动赠送充值活动奖励
// 参数:
//   - userId: 用户ID
//   - rechargeId: 充值订单ID
//
// 返回:
//   - error: 处理错误信息
//
// 功能:
//   - 根据充值记录自动判断并发放首充或复充活动奖励
//   - 支持PayData中的IsRechargeActive字段控制活动开关
//   - 首充时调用FirstDepositGift函数处理首充活动
//   - 复充时调用MultipleDepositGift函数处理复充活动
func AutoGiveDepositGift(userId int32, rechargeId int32) error {
	logs.Info("AutoGiveDepositGift 开始处理充值活动奖励: userId=%v, rechargeId=%v", userId, rechargeId)
	defer recover() // 捕获可能的panic异常

	// 充值表相关
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := server.DaoxHashGame().XRecharge.WithContext(context.Background())

	// 查询充值记录，确保是成功的充值
	recharge, err := rechargeDb.Where(rechargeTb.ID.Eq(rechargeId)).
		Where(rechargeTb.State.Eq(5)).Where(rechargeTb.UserID.Eq(userId)).First()
	if err != nil {
		logs.Error("AutoGiveDepositGift 获取充值记录错误: userId=%v, rechargeId=%v, err=%v", userId, rechargeId, err)
		return err
	}

	logs.Info("AutoGiveDepositGift 充值记录查询成功: userId=%v, rechargeId=%v, isFirst=%v, amount=%v",
		userId, rechargeId, recharge.IsFirst, recharge.RealAmount)
	// 解析PayData字段
	paydata := map[string]interface{}{}
	if recharge.PayData != "" {
		if err := json.Unmarshal([]byte(recharge.PayData), &paydata); err != nil {
			logs.Error("AutoGiveDepositGift 解析PayData错误: userId=%v, rechargeId=%v, err=%v", userId, rechargeId, err)
			// PayData解析失败不影响主流程，继续处理
		}
	}

	// 获取充值活动标识，默认为false
	isRechargeActive := true
	if val, exists := paydata["IsRechargeActive"]; exists {
		if boolVal, ok := val.(bool); ok {
			isRechargeActive = boolVal
		}
	}

	logs.Info("AutoGiveDepositGift PayData解析结果: userId=%v, rechargeId=%v, isRechargeActive=%v",
		userId, rechargeId, isRechargeActive)
	// 处理首充活动
	if recharge.IsFirst == 1 {
		logs.Info("AutoGiveDepositGift 检测到首充记录: userId=%v, orderId=%v", userId, rechargeId)
		// 检查首充活动是否开启
		if isRechargeActive {
			// 尝试处理首充活动，按优先级顺序：普通首充 -> Magic首充
			processed := false

			// 1. 先尝试普通首充活动
			firstActive, err := GetActiveDefine(recharge.SellerID, recharge.ChannelID, utils.FirstDepositGift)
			if err == nil && firstActive.State == 1 {
				logs.Info("AutoGiveDepositGift 找到开启的普通首充活动: userId=%v, orderId=%v, activeId=%d", userId, rechargeId, utils.FirstDepositGift)
				processed = processFirstDepositActivity(userId, rechargeId, recharge.RealAmount, firstActive, utils.FirstDepositGift)
			} else {
				logs.Info("AutoGiveDepositGift 普通首充活动未开启或获取失败: userId=%v, orderId=%v, err=%v", userId, rechargeId, err)
			}

			// 2. 如果普通首充活动未处理成功，尝试Magic首充活动
			if !processed {
				magicActive, err := GetActiveDefine(recharge.SellerID, recharge.ChannelID, utils.MagicFirstDepositGift)
				if err == nil && magicActive.State == 1 {
					logs.Info("AutoGiveDepositGift 找到开启的Magic首充活动: userId=%v, orderId=%v, activeId=%d", userId, rechargeId, utils.MagicFirstDepositGift)
					processed = processFirstDepositActivity(userId, rechargeId, recharge.RealAmount, magicActive, utils.MagicFirstDepositGift)
				} else {
					logs.Info("AutoGiveDepositGift Magic首充活动未开启或获取失败: userId=%v, orderId=%v, err=%v", userId, rechargeId, err)
				}
			}

			if !processed {
				logs.Info("AutoGiveDepositGift 没有可用的首充活动: userId=%v, orderId=%v", userId, rechargeId)
			}
		} else {
			logs.Info("AutoGiveDepositGift 首充活动未开启，跳过首充奖励: userId=%v, orderId=%v", userId, rechargeId)
		}

	}

	// 处理复充活动
	if recharge.IsFirst == 2 {
		logs.Info("AutoGiveDepositGift 检测到复充记录: userId=%v, orderId=%v", userId, rechargeId)

		// 检查复充活动是否开启
		if isRechargeActive {
			// 获取复充活动配置
			multipleActive, err := GetActiveDefine(recharge.SellerID, recharge.ChannelID, utils.MultipleDepositGift)
			if err != nil {
				logs.Error("AutoGiveDepositGift 获取复充活动配置错误: userId=%v, orderId=%v, err=%v", userId, rechargeId, err)
				// 配置获取失败不影响其他流程，继续处理
			} else if multipleActive.State != 1 {
				logs.Info("AutoGiveDepositGift 复充活动未开启: userId=%v, orderId=%v", userId, rechargeId)
			} else {
				// 解析活动档位配置
				var configData []DepositConfig
				err = json.Unmarshal([]byte(multipleActive.Config), &configData)
				if err != nil {
					logs.Error("AutoGiveDepositGift 解析复充活动档位配置错误: userId=%v, orderId=%v, err=%v", userId, rechargeId, err)
					// 配置解析失败不影响其他流程，继续处理
				} else {
					// 根据充值金额匹配合适的档位
					level := getMatchingLevel(recharge.RealAmount, configData)
					if level > 0 {
						// 调用复充活动处理函数
						errCode, err := MultipleDepositGift(userId, multipleActive.ActiveId, int(level), "auto")
						if err != nil {
							logs.Error("AutoGiveDepositGift 复充活动处理失败: userId=%v, orderId=%v, level=%v, errCode=%v, err=%v",
								userId, rechargeId, level, errCode, err)
							// 复充活动失败不影响其他流程
						} else {
							logs.Info("AutoGiveDepositGift 复充活动处理成功: userId=%v, orderId=%v, level=%v", userId, rechargeId, level)
						}
					} else {
						logs.Info("AutoGiveDepositGift 充值金额不满足复充活动要求: userId=%v, orderId=%v, amount=%v",
							userId, rechargeId, recharge.RealAmount)
					}
				}
			}
		} else {
			logs.Info("AutoGiveDepositGift 复充活动未开启，跳过复充奖励: userId=%v, orderId=%v", userId, rechargeId)
		}
	}

	logs.Info("AutoGiveDepositGift 充值活动奖励处理完成: userId=%v, orderId=%v, isFirst=%v",
		userId, rechargeId, recharge.IsFirst)

	return nil
}

// getMatchingLevel 根据充值金额匹配合适的活动档位
// 参数:
//   - rechargeAmount: 充值金额
//   - configData: 活动档位配置数组
//
// 返回:
//   - int32: 匹配的档位ID，0表示没有匹配的档位
//
// 逻辑:
//   - 遍历所有档位配置，找到充值金额满足的最高档位
//   - 档位按ID从小到大排序，充值金额要求也是递增的
//   - 返回满足条件的最高档位ID
func getMatchingLevel(rechargeAmount float64, configData []DepositConfig) int32 {
	rechargeDecimal := decimal.NewFromFloat(rechargeAmount)
	var matchedLevel int32 = 0

	logs.Info("getMatchingLevel 开始档位匹配: 充值金额=%v, 档位数量=%d", rechargeAmount, len(configData))

	// 遍历所有档位配置，找到满足条件的最高档位
	for _, config := range configData {
		logs.Info("getMatchingLevel 检查档位%d: 最低充值=%s, 赠送比例=%s%%, 赠送上限=%s",
			config.ID, config.FirstChargeUstdLimit.StringFixed(2),
			config.GiveProportion.StringFixed(2), config.GiveLimit.StringFixed(2))

		// 检查充值金额是否满足当前档位的最低要求
		if rechargeDecimal.GreaterThanOrEqual(config.FirstChargeUstdLimit) {
			logs.Info("getMatchingLevel 档位%d满足条件: 充值金额%s >= 最低要求%s",
				config.ID, rechargeDecimal.StringFixed(2), config.FirstChargeUstdLimit.StringFixed(2))

			// 如果满足条件，且档位ID更高，则更新匹配的档位
			if config.ID > matchedLevel {
				logs.Info("getMatchingLevel 更新匹配档位: 从%d更新为%d", matchedLevel, config.ID)
				matchedLevel = config.ID
			}
		} else {
			logs.Info("getMatchingLevel 档位%d不满足条件: 充值金额%s < 最低要求%s",
				config.ID, rechargeDecimal.StringFixed(2), config.FirstChargeUstdLimit.StringFixed(2))
		}
	}

	logs.Info("getMatchingLevel 档位匹配结果: 充值金额=%v, 匹配档位=%v", rechargeAmount, matchedLevel)
	return matchedLevel
}

// CheckAllActiveWithdrawable 检查用户所有活动的提现流水要求
// 参数:
//   - userId: 用户ID
//
// 返回:
//   - error: 如果有活动流水不足，返回错误信息；否则返回nil
func CheckAllActiveWithdrawable(userId int) error {
	// 检查用户参与兑换码活动是否达到提现条件
	isWidthdrawable, withdrawMessage, _ := IsWidthdrawableActive(userId, utils.RedeemCodeGift_1003)
	if !isWidthdrawable {
		logs.Error("兑换码活动流水不足,无法提款! 详情: %s", withdrawMessage)
		return errors.New(withdrawMessage)
	}

	// 检查用户是否满足首充活动流水要求
	isPass, message, diff := CheckActiveWithdrawable(int32(userId), utils.FirstDepositGift)
	logs.Info("CheckActiveWithdrawable 检查首充活动流水结果: %v, %v, 差额=%s", isPass, message, diff.StringFixed(2))
	if !isPass {
		logs.Error("首充活动流水不足,无法提款! 还需要流水: %s", diff.StringFixed(2))
		return errors.New(message)
	}

	// 检查用户是否满足Magic首充活动流水要求
	isPass, message, diff = CheckActiveWithdrawable(int32(userId), utils.MagicFirstDepositGift)
	logs.Info("CheckActiveWithdrawable 检查Magic首充活动流水结果: %v, %v, 差额=%s", isPass, message, diff.StringFixed(2))
	if !isPass {
		logs.Error("Magic首充活动流水不足,无法提款! 还需要流水: %s", diff.StringFixed(2))
		return errors.New(message)
	}

	// 检查用户是否满足复充活动流水要求
	isPass, message, diff = CheckActiveWithdrawable(int32(userId), utils.MultipleDepositGift)
	logs.Info("CheckActiveWithdrawable 检查复充活动流水结果: %v, %v, 差额=%s", isPass, message, diff.StringFixed(2))
	if !isPass {
		logs.Error("复充活动流水不足,无法提款! 还需要流水: %s", diff.StringFixed(2))
		return errors.New(message)
	}

	logs.Info("CheckAllActiveWithdrawable 所有活动流水检查通过: userId=%d", userId)
	return nil
}

// GetActivityConfigs 获取活动配置信息
// 参数:
//   - sellerId: 商户ID
//   - channelId: 渠道ID
//   - rechargeWithward: 是否充值（1=充值，其他=非充值）
//   - userId: 用户ID
//
// 返回值: 首充配置数组, 复充配置数组
func GetActivityConfigs(sellerId, channelId, rechargeWithward, userId int) ([]DepositConfig, []DepositConfig) {
	var fconfig, mconfig []DepositConfig

	// 只在充值时处理活动信息
	if rechargeWithward != 1 {
		return fconfig, mconfig
	}

	logs.Info("GetActivityConfigs 开始获取活动配置: UserId=%d, SellerId=%d, ChannelId=%d", userId, sellerId, channelId)

	// 查询充值赠送活动配置
	first, firstErr := GetActiveDefine(int32(sellerId), int32(channelId), utils.FirstDepositGift)
	magic, magicErr := GetActiveDefine(int32(sellerId), int32(channelId), utils.MagicFirstDepositGift)
	multiple, multipleErr := GetActiveDefine(int32(sellerId), int32(channelId), utils.MultipleDepositGift)

	// 检查用户充值状态
	hasFirstDeposit := CheckUserFirstDeposit(userId)
	hasFirstDepositActive := CheckUserFirstDepositActive(userId)

	logs.Info("GetActivityConfigs 用户充值状态检查: UserId=%d, 有首充记录=%v, 参与过首充活动=%v",
		userId, hasFirstDeposit, hasFirstDepositActive)

	// 处理首充活动配置
	// 规则：如果用户没有首充，也没有参与首充活动，返回首充活动的奖励配置
	var shouldShowFirstDeposit bool = false
	if firstErr != nil {
		logs.Info("查询首充赠送活动失败: SellerId=%d, ChannelId=%d, Error=%v", sellerId, channelId, firstErr)
	} else if first.State == utils.ActiveStateOpen { // 活动状态为开启
		logs.Info("首充赠送活动已开启: 活动ID=%d, 标题=%s", first.ActiveId, first.Title)

		// 判断是否应该显示首充活动配置
		if !hasFirstDeposit && !hasFirstDepositActive {
			shouldShowFirstDeposit = true
			logs.Info("用户符合首充活动显示条件: UserId=%d, 无首充记录且未参与首充活动", userId)
		} else {
			logs.Info("用户不符合首充活动显示条件: UserId=%d, 有首充记录=%v, 参与过首充活动=%v",
				userId, hasFirstDeposit, hasFirstDepositActive)
		}

		// 只有符合显示条件时才解析和返回首充活动配置
		if shouldShowFirstDeposit && first.Config != "" {
			err := json.Unmarshal([]byte(first.Config), &fconfig)
			if err != nil {
				logs.Error("解析首充赠送活动Config配置失败: %v", err)
				logs.Info("原始首充Config配置内容: %s", first.Config)
			} else {
				logs.Info("首充赠送活动Config配置解析成功，档位数量: %d", len(fconfig))
			}
		}
	} else {
		logs.Info("首充赠送活动未开启: 活动状态=%d", first.State)
	}

	// 处理Magic首充活动配置
	// 规则：如果普通首充活动未开启或用户不符合条件，且Magic首充活动开启，则返回Magic首充活动配置
	if !shouldShowFirstDeposit {
		if magicErr != nil {
			logs.Info("查询Magic首充赠送活动失败: SellerId=%d, ChannelId=%d, Error=%v", sellerId, channelId, magicErr)
		} else if magic.State == utils.ActiveStateOpen { // Magic首充活动状态为开启
			logs.Info("Magic首充赠送活动已开启: 活动ID=%d, 标题=%s", magic.ActiveId, magic.Title)

			// 判断是否应该显示Magic首充活动配置
			if !hasFirstDeposit && !hasFirstDepositActive {
				shouldShowFirstDeposit = true
				logs.Info("用户符合Magic首充活动显示条件: UserId=%d, 无首充记录且未参与首充活动", userId)

				// 解析和返回Magic首充活动配置
				if magic.Config != "" {
					err := json.Unmarshal([]byte(magic.Config), &fconfig)
					if err != nil {
						logs.Error("解析Magic首充赠送活动Config配置失败: %v", err)
						logs.Info("原始Magic首充Config配置内容: %s", magic.Config)
					} else {
						logs.Info("Magic首充赠送活动Config配置解析成功，档位数量: %d", len(fconfig))
					}
				}
			} else {
				logs.Info("用户不符合Magic首充活动显示条件: UserId=%d, 有首充记录=%v, 参与过首充活动=%v",
					userId, hasFirstDeposit, hasFirstDepositActive)
			}
		} else {
			logs.Info("Magic首充赠送活动未开启: 活动状态=%d", magic.State)
		}
	}

	// 处理复充活动配置
	// 规则：如果有首充记录，没有参与当天的复充活动，返回复充活动的奖励配置
	var shouldShowMultipleDeposit bool = false
	if multipleErr != nil {
		logs.Info("查询复充赠送活动失败: SellerId=%d, ChannelId=%d, Error=%v", sellerId, channelId, multipleErr)
	} else if multiple.State == utils.ActiveStateOpen { // 复充活动状态为开启
		logs.Info("复充赠送活动已开启: 活动ID=%d, 标题=%s", multiple.ActiveId, multiple.Title)

		// 判断是否应该显示复充活动配置
		if hasFirstDeposit {
			// 检查用户是否满足复充活动基本资格
			eligibilityResult := CheckMultipleDepositEnable(int32(userId), true)
			if eligibilityResult.IsEligible {
				shouldShowMultipleDeposit = true
				logs.Info("用户符合复充活动显示条件: UserId=%d, 有首充记录且满足复充活动基本资格", userId)
			} else {
				logs.Info("用户不符合复充活动显示条件: UserId=%d, 原因=%s", userId, eligibilityResult.ErrorMsg)
			}
		} else {
			logs.Info("用户不符合复充活动显示条件: UserId=%d, 无首充记录", userId)
		}

		// 只有符合显示条件时才解析和返回复充活动配置
		if shouldShowMultipleDeposit && multiple.Config != "" {
			err := json.Unmarshal([]byte(multiple.Config), &mconfig)
			if err != nil {
				logs.Error("解析复充赠送活动Config配置失败: %v", err)
				logs.Info("原始复充Config配置内容: %s", multiple.Config)
			} else {
				logs.Info("复充赠送活动Config配置解析成功，档位数量: %d", len(mconfig))
			}
		}
	} else {
		logs.Info("复充赠送活动未开启: 活动状态=%d", multiple.State)
	}

	logs.Info("GetActivityConfigs 活动配置获取完成: UserId=%d, 返回首充配置数量=%d, 返回复充配置数量=%d",
		userId, len(fconfig), len(mconfig))

	return fconfig, mconfig
}

// FilterActivityConfig 过滤活动配置，如果GiveAmount为0返回GiveProportion，反之返回GiveAmount
// 参数:
//   - configs: 活动配置数组
//
// 返回:
//   - []map[string]interface{}: 过滤后的配置数组
func FilterActivityConfig(configs []DepositConfig) []map[string]interface{} {
	filtered := make([]map[string]interface{}, len(configs))

	for i, config := range configs {
		item := map[string]interface{}{
			"Id":                   config.ID,
			"FirstChargeUstdLimit": config.FirstChargeUstdLimit.String(),
			"LiushuiMultiple":      config.LiushuiMultiple.String(),
			"BonusMultiple":        config.BonusMultiple.String(),
			"GiveLimit":            config.GiveLimit.String(),
		}

		if config.GiveAmount > 0 {
			// 如果有固定金额，只返回GiveAmount字段
			item["GiveAmount"] = config.GiveAmount
		} else {
			// 如果没有固定金额，只返回GiveProportion字段
			item["GiveProportion"] = config.GiveProportion.String()
		}

		filtered[i] = item
	}

	return filtered
}

// processFirstDepositActivity 处理首充活动的辅助函数
// 参数:
//   - userId: 用户ID
//   - rechargeId: 充值记录ID
//   - rechargeAmount: 充值金额
//   - activeDefine: 活动定义
//   - activeId: 活动ID
//
// 返回:
//   - bool: 是否处理成功
func processFirstDepositActivity(userId int32, rechargeId int32, rechargeAmount float64, activeDefine model.ActiveDefine, activeId int32) bool {
	// 解析活动档位配置
	var configData []DepositConfig
	err := json.Unmarshal([]byte(activeDefine.Config), &configData)
	if err != nil {
		logs.Error("processFirstDepositActivity 解析活动档位配置错误: userId=%v, orderId=%v, activeId=%d, err=%v",
			userId, rechargeId, activeId, err)
		return false
	}

	// 根据充值金额匹配合适的档位
	level := getMatchingLevel(rechargeAmount, configData)
	if level <= 0 {
		logs.Info("processFirstDepositActivity 充值金额不满足活动要求: userId=%v, orderId=%v, activeId=%d, amount=%v",
			userId, rechargeId, activeId, rechargeAmount)
		return false
	}

	// 调用首充活动处理函数
	errCode, err := FirstDepositGift(userId, level, activeId, "auto")
	if err != nil {
		logs.Error("processFirstDepositActivity 活动处理失败: userId=%v, orderId=%v, activeId=%d, level=%v, errCode=%v, err=%v",
			userId, rechargeId, activeId, level, errCode, err)
		return false
	}

	logs.Info("processFirstDepositActivity 活动处理成功: userId=%v, orderId=%v, activeId=%d, level=%v",
		userId, rechargeId, activeId, level)
	return true
}

// FindActiveByRedeemCode 根据兑换码查询对应的活动信息
// 参数:
//   - redeemCode: 兑换码
//   - ctx: HTTP请求上下文，用于获取运营商和渠道信息
//
// 返回:
//   - model.ActiveDefine: 活动定义信息
//   - error: 错误信息
func FindActiveByRedeemCode(redeemCode string, ctx *abugo.AbuHttpContent) (model.ActiveDefine, error) {
	if redeemCode == "" {
		return model.ActiveDefine{}, errors.New("兑换码不能为空")
	}

	// 获取运营商和渠道信息
	host := ctx.Host()
	channelId, sellerId := server.GetChannel(ctx, host)

	// 查询指定兑换码活动类型的活动定义
	activeTb := server.DaoxHashGame().XActiveDefine
	activeDb := server.DaoxHashGame().XActiveDefine.WithContext(context.Background())

	// 查询指定的兑换码活动类型配置
	// 兑换码活动ID列表：1003, 1015, 1016, 1017
	redeemCodeActiveIds := []int32{utils.RedeemCodeGift_1003, utils.RedeemCodeGift_1015, utils.RedeemCodeGift_1016, utils.RedeemCodeGift_1017}
	activeDefines, err := activeDb.
		Where(activeTb.SellerID.Eq(int32(sellerId))).
		Where(activeTb.ChannelID.Eq(int32(channelId))).
		Where(activeTb.ActiveID.In(redeemCodeActiveIds...)). // 只查询兑换码活动类型
		Where(activeTb.State.Eq(1)). // 状态为开启
		Find()

	if err != nil {
		logs.Error("FindActiveByRedeemCode 查询活动定义失败: %v", err)
		return model.ActiveDefine{}, errors.New("查询活动配置失败")
	}

	// 遍历所有活动，查找包含该兑换码的活动
	for _, activeDefine := range activeDefines {
		// 检查活动是否在有效期内
		currentTime := time.Now().Unix()
		startTimeInSeconds := activeDefine.EffectStartTime / 1000
		endTimeInSeconds := activeDefine.EffectEndTime / 1000

		// 如果配置了开始时间，检查活动是否已开始
		if startTimeInSeconds > 0 && startTimeInSeconds > currentTime {
			continue // 活动未开始，跳过
		}

		// 如果配置了结束时间，检查活动是否已结束
		if endTimeInSeconds > 0 && endTimeInSeconds < currentTime {
			continue // 活动已结束，跳过
		}

		// 解析活动配置，查找兑换码
		if activeDefine.Config == "" {
			continue // 配置为空，跳过
		}

		// 尝试解析为兑换码配置
		var config RedeemCodeConfig
		err = json.Unmarshal([]byte(activeDefine.Config), &config)
		if err != nil {
			// 解析失败，可能不是兑换码活动，跳过
			continue
		}

		// 查找匹配的兑换码
		for _, codeItem := range config.RedeemCodes {
			if codeItem.RedeemCode == redeemCode {
				logs.Info("FindActiveByRedeemCode 找到匹配的兑换码: %s, 活动ID: %d, 活动名称: %s",
					redeemCode, activeDefine.ActiveID, activeDefine.Title)

				// 将 gen 模型转换为原始模型
				return model.ActiveDefine{
					Id:              int(activeDefine.ID),
					SellerId:        int(activeDefine.SellerID),
					ChannelId:       int(activeDefine.ChannelID),
					ActiveId:        int(activeDefine.ActiveID),
					Memo:            activeDefine.Memo,
					AuditType:       int(activeDefine.AuditType),
					State:           int(activeDefine.State),
					Sort:            int(activeDefine.Sort),
					EffectStartTime: activeDefine.EffectStartTime,
					EffectEndTime:   activeDefine.EffectEndTime,
					Title:           activeDefine.Title,
					TitleImg:        activeDefine.TitleImg,
					GameType:        activeDefine.GameType,
					MinLiuShui:      decimal.NewFromFloat(activeDefine.MinLiuShui),
					ExtReward:       decimal.NewFromFloat(activeDefine.ExtReward),
					MinDeposit:      decimal.NewFromFloat(activeDefine.MinDeposit),
					MaxReward:       decimal.NewFromFloat(activeDefine.MaxReward),
					ValidRecharge:   decimal.NewFromFloat(activeDefine.ValidRecharge),
					ValidLiuShui:    decimal.NewFromFloat(activeDefine.ValidLiuShui),
					TrxPrice:        decimal.NewFromFloat(activeDefine.TrxPrice),
					Config:          activeDefine.Config,
					BaseConfig:      activeDefine.BaseConfig,
				}, nil
			}
		}
	}

	logs.Error("FindActiveByRedeemCode 未找到匹配的兑换码: %s", redeemCode)
	return model.ActiveDefine{}, errors.New("无效的兑换码")
}
