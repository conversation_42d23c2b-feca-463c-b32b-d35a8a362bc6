// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXAgentCommissionUserDate(db *gorm.DB, opts ...gen.DOOption) xAgentCommissionUserDate {
	_xAgentCommissionUserDate := xAgentCommissionUserDate{}

	_xAgentCommissionUserDate.xAgentCommissionUserDateDo.UseDB(db, opts...)
	_xAgentCommissionUserDate.xAgentCommissionUserDateDo.UseModel(&model.XAgentCommissionUserDate{})

	tableName := _xAgentCommissionUserDate.xAgentCommissionUserDateDo.TableName()
	_xAgentCommissionUserDate.ALL = field.NewAsterisk(tableName)
	_xAgentCommissionUserDate.StatDate = field.NewTime(tableName, "StatDate")
	_xAgentCommissionUserDate.AgentID = field.NewInt32(tableName, "AgentId")
	_xAgentCommissionUserDate.UserID = field.NewInt32(tableName, "UserId")
	_xAgentCommissionUserDate.ParentAgentID = field.NewInt32(tableName, "ParentAgentId")
	_xAgentCommissionUserDate.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xAgentCommissionUserDate.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xAgentCommissionUserDate.SellerID = field.NewInt32(tableName, "SellerId")
	_xAgentCommissionUserDate.SchemeID = field.NewInt32(tableName, "SchemeId")
	_xAgentCommissionUserDate.TeamLevel = field.NewInt32(tableName, "TeamLevel")
	_xAgentCommissionUserDate.AgentLevel = field.NewInt32(tableName, "AgentLevel")
	_xAgentCommissionUserDate.BetHaXi = field.NewFloat64(tableName, "BetHaXi")
	_xAgentCommissionUserDate.BetHaXiRoulette = field.NewFloat64(tableName, "BetHaXiRoulette")
	_xAgentCommissionUserDate.BetTranferUsdtHaXi = field.NewFloat64(tableName, "BetTranferUsdtHaXi")
	_xAgentCommissionUserDate.BetTranferUsdtHaXiRoulette = field.NewFloat64(tableName, "BetTranferUsdtHaXiRoulette")
	_xAgentCommissionUserDate.BetTranferTrxHaXi = field.NewFloat64(tableName, "BetTranferTrxHaXi")
	_xAgentCommissionUserDate.BetTranferTrxHaXiRoulette = field.NewFloat64(tableName, "BetTranferTrxHaXiRoulette")
	_xAgentCommissionUserDate.BetLottery = field.NewFloat64(tableName, "BetLottery")
	_xAgentCommissionUserDate.BetLowLottery = field.NewFloat64(tableName, "BetLowLottery")
	_xAgentCommissionUserDate.BetQiPai = field.NewFloat64(tableName, "BetQiPai")
	_xAgentCommissionUserDate.BetDianZhi = field.NewFloat64(tableName, "BetDianZhi")
	_xAgentCommissionUserDate.BetXiaoYouXi = field.NewFloat64(tableName, "BetXiaoYouXi")
	_xAgentCommissionUserDate.BetLive = field.NewFloat64(tableName, "BetLive")
	_xAgentCommissionUserDate.BetSport = field.NewFloat64(tableName, "BetSport")
	_xAgentCommissionUserDate.BetTexas = field.NewFloat64(tableName, "BetTexas")
	_xAgentCommissionUserDate.WinHaXi = field.NewFloat64(tableName, "WinHaXi")
	_xAgentCommissionUserDate.WinHaXiRoulette = field.NewFloat64(tableName, "WinHaXiRoulette")
	_xAgentCommissionUserDate.WinTranferUsdtHaXi = field.NewFloat64(tableName, "WinTranferUsdtHaXi")
	_xAgentCommissionUserDate.WinTranferUsdtHaXiRoulette = field.NewFloat64(tableName, "WinTranferUsdtHaXiRoulette")
	_xAgentCommissionUserDate.WinTranferTrxHaXi = field.NewFloat64(tableName, "WinTranferTrxHaXi")
	_xAgentCommissionUserDate.WinTranferTrxHaXiRoulette = field.NewFloat64(tableName, "WinTranferTrxHaXiRoulette")
	_xAgentCommissionUserDate.WinLottery = field.NewFloat64(tableName, "WinLottery")
	_xAgentCommissionUserDate.WinLowLottery = field.NewFloat64(tableName, "WinLowLottery")
	_xAgentCommissionUserDate.WinQiPai = field.NewFloat64(tableName, "WinQiPai")
	_xAgentCommissionUserDate.WinDianZhi = field.NewFloat64(tableName, "WinDianZhi")
	_xAgentCommissionUserDate.WinXiaoYouXi = field.NewFloat64(tableName, "WinXiaoYouXi")
	_xAgentCommissionUserDate.WinLive = field.NewFloat64(tableName, "WinLive")
	_xAgentCommissionUserDate.WinSport = field.NewFloat64(tableName, "WinSport")
	_xAgentCommissionUserDate.WinTexas = field.NewFloat64(tableName, "WinTexas")
	_xAgentCommissionUserDate.LiuShuiHaXi = field.NewFloat64(tableName, "LiuShuiHaXi")
	_xAgentCommissionUserDate.LiuShuiHaXiRoulette = field.NewFloat64(tableName, "LiuShuiHaXiRoulette")
	_xAgentCommissionUserDate.LiuShuiTranferUsdtHaXi = field.NewFloat64(tableName, "LiuShuiTranferUsdtHaXi")
	_xAgentCommissionUserDate.LiuShuiTranferUsdtHaXiRoulette = field.NewFloat64(tableName, "LiuShuiTranferUsdtHaXiRoulette")
	_xAgentCommissionUserDate.LiuShuiTranferTrxHaXi = field.NewFloat64(tableName, "LiuShuiTranferTrxHaXi")
	_xAgentCommissionUserDate.LiuShuiTranferTrxHaXiRoulette = field.NewFloat64(tableName, "LiuShuiTranferTrxHaXiRoulette")
	_xAgentCommissionUserDate.LiuShuiLottery = field.NewFloat64(tableName, "LiuShuiLottery")
	_xAgentCommissionUserDate.LiuShuiLowLottery = field.NewFloat64(tableName, "LiuShuiLowLottery")
	_xAgentCommissionUserDate.LiuShuiQiPai = field.NewFloat64(tableName, "LiuShuiQiPai")
	_xAgentCommissionUserDate.LiuShuiDianZhi = field.NewFloat64(tableName, "LiuShuiDianZhi")
	_xAgentCommissionUserDate.LiuShuiXiaoYouXi = field.NewFloat64(tableName, "LiuShuiXiaoYouXi")
	_xAgentCommissionUserDate.LiuShuiLive = field.NewFloat64(tableName, "LiuShuiLive")
	_xAgentCommissionUserDate.LiuShuiSport = field.NewFloat64(tableName, "LiuShuiSport")
	_xAgentCommissionUserDate.LiuShuiTexas = field.NewFloat64(tableName, "LiuShuiTexas")
	_xAgentCommissionUserDate.RechargeCount = field.NewInt32(tableName, "RechargeCount")
	_xAgentCommissionUserDate.RechargeAmount = field.NewFloat64(tableName, "RechargeAmount")
	_xAgentCommissionUserDate.FirstRechargeAmount = field.NewFloat64(tableName, "FirstRechargeAmount")
	_xAgentCommissionUserDate.WithdrawCount = field.NewInt32(tableName, "WithdrawCount")
	_xAgentCommissionUserDate.WithdrawAmount = field.NewFloat64(tableName, "WithdrawAmount")
	_xAgentCommissionUserDate.ActivityRewardCount = field.NewInt32(tableName, "ActivityRewardCount")
	_xAgentCommissionUserDate.ActivityRewardAmount = field.NewFloat64(tableName, "ActivityRewardAmount")
	_xAgentCommissionUserDate.VipRewardCount = field.NewInt32(tableName, "VipRewardCount")
	_xAgentCommissionUserDate.VipRewardAmount = field.NewFloat64(tableName, "VipRewardAmount")
	_xAgentCommissionUserDate.ManRewardCount = field.NewInt32(tableName, "ManRewardCount")
	_xAgentCommissionUserDate.ManRewardAmount = field.NewFloat64(tableName, "ManRewardAmount")
	_xAgentCommissionUserDate.GetCommissionCount = field.NewInt32(tableName, "GetCommissionCount")
	_xAgentCommissionUserDate.GetCommissionAmount = field.NewFloat64(tableName, "GetCommissionAmount")
	_xAgentCommissionUserDate.RewardHaXi = field.NewFloat64(tableName, "RewardHaXi")
	_xAgentCommissionUserDate.RewardHaXiRoulette = field.NewFloat64(tableName, "RewardHaXiRoulette")
	_xAgentCommissionUserDate.RewardLottery = field.NewFloat64(tableName, "RewardLottery")
	_xAgentCommissionUserDate.RewardLowLottery = field.NewFloat64(tableName, "RewardLowLottery")
	_xAgentCommissionUserDate.RewardQiPai = field.NewFloat64(tableName, "RewardQiPai")
	_xAgentCommissionUserDate.RewardDianZhi = field.NewFloat64(tableName, "RewardDianZhi")
	_xAgentCommissionUserDate.RewardXiaoYouXi = field.NewFloat64(tableName, "RewardXiaoYouXi")
	_xAgentCommissionUserDate.RewardLive = field.NewFloat64(tableName, "RewardLive")
	_xAgentCommissionUserDate.RewardSport = field.NewFloat64(tableName, "RewardSport")
	_xAgentCommissionUserDate.RewardTexas = field.NewFloat64(tableName, "RewardTexas")
	_xAgentCommissionUserDate.TrxRate = field.NewFloat64(tableName, "TrxRate")
	_xAgentCommissionUserDate.CommissionAmount = field.NewFloat64(tableName, "CommissionAmount")
	_xAgentCommissionUserDate.CommissionTrxAmount = field.NewFloat64(tableName, "CommissionTrxAmount")
	_xAgentCommissionUserDate.IsVaild = field.NewInt32(tableName, "IsVaild")
	_xAgentCommissionUserDate.IsRealVaild = field.NewInt32(tableName, "IsRealVaild")
	_xAgentCommissionUserDate.CreateTime = field.NewTime(tableName, "CreateTime")
	_xAgentCommissionUserDate.UpdateTime = field.NewTime(tableName, "UpdateTime")

	_xAgentCommissionUserDate.fillFieldMap()

	return _xAgentCommissionUserDate
}

// xAgentCommissionUserDate 三级返佣下级数据(按日期统计)
type xAgentCommissionUserDate struct {
	xAgentCommissionUserDateDo xAgentCommissionUserDateDo

	ALL                            field.Asterisk
	StatDate                       field.Time    // 日期
	AgentID                        field.Int32   // 代理Id
	UserID                         field.Int32   // 玩家
	ParentAgentID                  field.Int32   // 上级代理Id
	TopAgentID                     field.Int32   // 顶级代理Id
	ChannelID                      field.Int32   // 渠道Id
	SellerID                       field.Int32   // 运营商Id
	SchemeID                       field.Int32   // 方案Id
	TeamLevel                      field.Int32   // 团队等级
	AgentLevel                     field.Int32   // 代理等级
	BetHaXi                        field.Float64 // 哈希余额投注额
	BetHaXiRoulette                field.Float64 // 哈希轮盘余额投注额
	BetTranferUsdtHaXi             field.Float64 // 哈希转账Usdt投注额
	BetTranferUsdtHaXiRoulette     field.Float64 // 哈希轮盘转账Usdt投注额
	BetTranferTrxHaXi              field.Float64 // 哈希转账Trx投注额
	BetTranferTrxHaXiRoulette      field.Float64 // 哈希轮盘转账Trx投注额
	BetLottery                     field.Float64 // 彩票投注额
	BetLowLottery                  field.Float64 // 低频彩投注额
	BetQiPai                       field.Float64 // 棋牌投注额
	BetDianZhi                     field.Float64 // 电子投注额
	BetXiaoYouXi                   field.Float64 // 小游戏投注额
	BetLive                        field.Float64 // 真人投注额
	BetSport                       field.Float64 // 体育投注额
	BetTexas                       field.Float64 // 德州投注额
	WinHaXi                        field.Float64 // 哈希余额派奖额
	WinHaXiRoulette                field.Float64 // 哈希轮盘余额派奖额
	WinTranferUsdtHaXi             field.Float64 // 哈希转账Usdt派奖额
	WinTranferUsdtHaXiRoulette     field.Float64 // 哈希轮盘转账Usdt派奖额
	WinTranferTrxHaXi              field.Float64 // 哈希转账Trx派奖额
	WinTranferTrxHaXiRoulette      field.Float64 // 哈希轮盘转账Trx派奖额
	WinLottery                     field.Float64 // 彩票派奖额
	WinLowLottery                  field.Float64 // 低频彩派奖额
	WinQiPai                       field.Float64 // 棋牌派奖额
	WinDianZhi                     field.Float64 // 电子派奖额
	WinXiaoYouXi                   field.Float64 // 小游戏派奖额
	WinLive                        field.Float64 // 真人派奖额
	WinSport                       field.Float64 // 体育派奖额
	WinTexas                       field.Float64 // 德州派奖额
	LiuShuiHaXi                    field.Float64 // 哈希余额流水
	LiuShuiHaXiRoulette            field.Float64 // 哈希轮盘余额流水
	LiuShuiTranferUsdtHaXi         field.Float64 // 哈希转账Usdt流水
	LiuShuiTranferUsdtHaXiRoulette field.Float64 // 哈希轮盘转账Usdt流水
	LiuShuiTranferTrxHaXi          field.Float64 // 哈希转账Trx流水
	LiuShuiTranferTrxHaXiRoulette  field.Float64 // 哈希轮盘转账Trx流水
	LiuShuiLottery                 field.Float64 // 彩票流水
	LiuShuiLowLottery              field.Float64 // 低频彩流水
	LiuShuiQiPai                   field.Float64 // 棋牌流水
	LiuShuiDianZhi                 field.Float64 // 电子流水
	LiuShuiXiaoYouXi               field.Float64 // 小游戏流水
	LiuShuiLive                    field.Float64 // 真人流水
	LiuShuiSport                   field.Float64 // 体育流水
	LiuShuiTexas                   field.Float64 // 德州流水
	RechargeCount                  field.Int32   // 充值笔数
	RechargeAmount                 field.Float64 // 充值金额
	FirstRechargeAmount            field.Float64 // 首充金额
	WithdrawCount                  field.Int32   // 提款笔数
	WithdrawAmount                 field.Float64 // 提现金额
	ActivityRewardCount            field.Int32   // 活动彩金笔数
	ActivityRewardAmount           field.Float64 // 活动彩金金额
	VipRewardCount                 field.Int32   // Vip活动彩金笔数
	VipRewardAmount                field.Float64 // Vip活动彩金金额
	ManRewardCount                 field.Int32   // 人工彩金笔数
	ManRewardAmount                field.Float64 // 人工彩金金额
	GetCommissionCount             field.Int32   // 领取佣金笔数
	GetCommissionAmount            field.Float64 // 领取佣金金额
	RewardHaXi                     field.Float64 // 哈希返佣(百分比)
	RewardHaXiRoulette             field.Float64 // 哈希轮盘返佣(百分比)
	RewardLottery                  field.Float64 // 彩票返佣(百分比)
	RewardLowLottery               field.Float64 // 低频彩返佣(百分比)
	RewardQiPai                    field.Float64 // 棋牌返佣(百分比)
	RewardDianZhi                  field.Float64 // 电子返佣(百分比)
	RewardXiaoYouXi                field.Float64 // 小游戏返佣(百分比)
	RewardLive                     field.Float64 // 真人返佣(百分比)
	RewardSport                    field.Float64 // 体育返佣(百分比)
	RewardTexas                    field.Float64 // 德州返佣(百分比)
	TrxRate                        field.Float64 // Trx汇率
	CommissionAmount               field.Float64 // 结算佣金Usdt
	CommissionTrxAmount            field.Float64 // 结算佣金Trx
	IsVaild                        field.Int32   // 是否有效玩家：1有效 2无效
	IsRealVaild                    field.Int32   // 是否真实有效玩家：1有效 2无效
	CreateTime                     field.Time    // 创建时间
	UpdateTime                     field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (x xAgentCommissionUserDate) Table(newTableName string) *xAgentCommissionUserDate {
	x.xAgentCommissionUserDateDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xAgentCommissionUserDate) As(alias string) *xAgentCommissionUserDate {
	x.xAgentCommissionUserDateDo.DO = *(x.xAgentCommissionUserDateDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xAgentCommissionUserDate) updateTableName(table string) *xAgentCommissionUserDate {
	x.ALL = field.NewAsterisk(table)
	x.StatDate = field.NewTime(table, "StatDate")
	x.AgentID = field.NewInt32(table, "AgentId")
	x.UserID = field.NewInt32(table, "UserId")
	x.ParentAgentID = field.NewInt32(table, "ParentAgentId")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.SchemeID = field.NewInt32(table, "SchemeId")
	x.TeamLevel = field.NewInt32(table, "TeamLevel")
	x.AgentLevel = field.NewInt32(table, "AgentLevel")
	x.BetHaXi = field.NewFloat64(table, "BetHaXi")
	x.BetHaXiRoulette = field.NewFloat64(table, "BetHaXiRoulette")
	x.BetTranferUsdtHaXi = field.NewFloat64(table, "BetTranferUsdtHaXi")
	x.BetTranferUsdtHaXiRoulette = field.NewFloat64(table, "BetTranferUsdtHaXiRoulette")
	x.BetTranferTrxHaXi = field.NewFloat64(table, "BetTranferTrxHaXi")
	x.BetTranferTrxHaXiRoulette = field.NewFloat64(table, "BetTranferTrxHaXiRoulette")
	x.BetLottery = field.NewFloat64(table, "BetLottery")
	x.BetLowLottery = field.NewFloat64(table, "BetLowLottery")
	x.BetQiPai = field.NewFloat64(table, "BetQiPai")
	x.BetDianZhi = field.NewFloat64(table, "BetDianZhi")
	x.BetXiaoYouXi = field.NewFloat64(table, "BetXiaoYouXi")
	x.BetLive = field.NewFloat64(table, "BetLive")
	x.BetSport = field.NewFloat64(table, "BetSport")
	x.BetTexas = field.NewFloat64(table, "BetTexas")
	x.WinHaXi = field.NewFloat64(table, "WinHaXi")
	x.WinHaXiRoulette = field.NewFloat64(table, "WinHaXiRoulette")
	x.WinTranferUsdtHaXi = field.NewFloat64(table, "WinTranferUsdtHaXi")
	x.WinTranferUsdtHaXiRoulette = field.NewFloat64(table, "WinTranferUsdtHaXiRoulette")
	x.WinTranferTrxHaXi = field.NewFloat64(table, "WinTranferTrxHaXi")
	x.WinTranferTrxHaXiRoulette = field.NewFloat64(table, "WinTranferTrxHaXiRoulette")
	x.WinLottery = field.NewFloat64(table, "WinLottery")
	x.WinLowLottery = field.NewFloat64(table, "WinLowLottery")
	x.WinQiPai = field.NewFloat64(table, "WinQiPai")
	x.WinDianZhi = field.NewFloat64(table, "WinDianZhi")
	x.WinXiaoYouXi = field.NewFloat64(table, "WinXiaoYouXi")
	x.WinLive = field.NewFloat64(table, "WinLive")
	x.WinSport = field.NewFloat64(table, "WinSport")
	x.WinTexas = field.NewFloat64(table, "WinTexas")
	x.LiuShuiHaXi = field.NewFloat64(table, "LiuShuiHaXi")
	x.LiuShuiHaXiRoulette = field.NewFloat64(table, "LiuShuiHaXiRoulette")
	x.LiuShuiTranferUsdtHaXi = field.NewFloat64(table, "LiuShuiTranferUsdtHaXi")
	x.LiuShuiTranferUsdtHaXiRoulette = field.NewFloat64(table, "LiuShuiTranferUsdtHaXiRoulette")
	x.LiuShuiTranferTrxHaXi = field.NewFloat64(table, "LiuShuiTranferTrxHaXi")
	x.LiuShuiTranferTrxHaXiRoulette = field.NewFloat64(table, "LiuShuiTranferTrxHaXiRoulette")
	x.LiuShuiLottery = field.NewFloat64(table, "LiuShuiLottery")
	x.LiuShuiLowLottery = field.NewFloat64(table, "LiuShuiLowLottery")
	x.LiuShuiQiPai = field.NewFloat64(table, "LiuShuiQiPai")
	x.LiuShuiDianZhi = field.NewFloat64(table, "LiuShuiDianZhi")
	x.LiuShuiXiaoYouXi = field.NewFloat64(table, "LiuShuiXiaoYouXi")
	x.LiuShuiLive = field.NewFloat64(table, "LiuShuiLive")
	x.LiuShuiSport = field.NewFloat64(table, "LiuShuiSport")
	x.LiuShuiTexas = field.NewFloat64(table, "LiuShuiTexas")
	x.RechargeCount = field.NewInt32(table, "RechargeCount")
	x.RechargeAmount = field.NewFloat64(table, "RechargeAmount")
	x.FirstRechargeAmount = field.NewFloat64(table, "FirstRechargeAmount")
	x.WithdrawCount = field.NewInt32(table, "WithdrawCount")
	x.WithdrawAmount = field.NewFloat64(table, "WithdrawAmount")
	x.ActivityRewardCount = field.NewInt32(table, "ActivityRewardCount")
	x.ActivityRewardAmount = field.NewFloat64(table, "ActivityRewardAmount")
	x.VipRewardCount = field.NewInt32(table, "VipRewardCount")
	x.VipRewardAmount = field.NewFloat64(table, "VipRewardAmount")
	x.ManRewardCount = field.NewInt32(table, "ManRewardCount")
	x.ManRewardAmount = field.NewFloat64(table, "ManRewardAmount")
	x.GetCommissionCount = field.NewInt32(table, "GetCommissionCount")
	x.GetCommissionAmount = field.NewFloat64(table, "GetCommissionAmount")
	x.RewardHaXi = field.NewFloat64(table, "RewardHaXi")
	x.RewardHaXiRoulette = field.NewFloat64(table, "RewardHaXiRoulette")
	x.RewardLottery = field.NewFloat64(table, "RewardLottery")
	x.RewardLowLottery = field.NewFloat64(table, "RewardLowLottery")
	x.RewardQiPai = field.NewFloat64(table, "RewardQiPai")
	x.RewardDianZhi = field.NewFloat64(table, "RewardDianZhi")
	x.RewardXiaoYouXi = field.NewFloat64(table, "RewardXiaoYouXi")
	x.RewardLive = field.NewFloat64(table, "RewardLive")
	x.RewardSport = field.NewFloat64(table, "RewardSport")
	x.RewardTexas = field.NewFloat64(table, "RewardTexas")
	x.TrxRate = field.NewFloat64(table, "TrxRate")
	x.CommissionAmount = field.NewFloat64(table, "CommissionAmount")
	x.CommissionTrxAmount = field.NewFloat64(table, "CommissionTrxAmount")
	x.IsVaild = field.NewInt32(table, "IsVaild")
	x.IsRealVaild = field.NewInt32(table, "IsRealVaild")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.UpdateTime = field.NewTime(table, "UpdateTime")

	x.fillFieldMap()

	return x
}

func (x *xAgentCommissionUserDate) WithContext(ctx context.Context) *xAgentCommissionUserDateDo {
	return x.xAgentCommissionUserDateDo.WithContext(ctx)
}

func (x xAgentCommissionUserDate) TableName() string { return x.xAgentCommissionUserDateDo.TableName() }

func (x xAgentCommissionUserDate) Alias() string { return x.xAgentCommissionUserDateDo.Alias() }

func (x xAgentCommissionUserDate) Columns(cols ...field.Expr) gen.Columns {
	return x.xAgentCommissionUserDateDo.Columns(cols...)
}

func (x *xAgentCommissionUserDate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xAgentCommissionUserDate) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 82)
	x.fieldMap["StatDate"] = x.StatDate
	x.fieldMap["AgentId"] = x.AgentID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ParentAgentId"] = x.ParentAgentID
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["SchemeId"] = x.SchemeID
	x.fieldMap["TeamLevel"] = x.TeamLevel
	x.fieldMap["AgentLevel"] = x.AgentLevel
	x.fieldMap["BetHaXi"] = x.BetHaXi
	x.fieldMap["BetHaXiRoulette"] = x.BetHaXiRoulette
	x.fieldMap["BetTranferUsdtHaXi"] = x.BetTranferUsdtHaXi
	x.fieldMap["BetTranferUsdtHaXiRoulette"] = x.BetTranferUsdtHaXiRoulette
	x.fieldMap["BetTranferTrxHaXi"] = x.BetTranferTrxHaXi
	x.fieldMap["BetTranferTrxHaXiRoulette"] = x.BetTranferTrxHaXiRoulette
	x.fieldMap["BetLottery"] = x.BetLottery
	x.fieldMap["BetLowLottery"] = x.BetLowLottery
	x.fieldMap["BetQiPai"] = x.BetQiPai
	x.fieldMap["BetDianZhi"] = x.BetDianZhi
	x.fieldMap["BetXiaoYouXi"] = x.BetXiaoYouXi
	x.fieldMap["BetLive"] = x.BetLive
	x.fieldMap["BetSport"] = x.BetSport
	x.fieldMap["BetTexas"] = x.BetTexas
	x.fieldMap["WinHaXi"] = x.WinHaXi
	x.fieldMap["WinHaXiRoulette"] = x.WinHaXiRoulette
	x.fieldMap["WinTranferUsdtHaXi"] = x.WinTranferUsdtHaXi
	x.fieldMap["WinTranferUsdtHaXiRoulette"] = x.WinTranferUsdtHaXiRoulette
	x.fieldMap["WinTranferTrxHaXi"] = x.WinTranferTrxHaXi
	x.fieldMap["WinTranferTrxHaXiRoulette"] = x.WinTranferTrxHaXiRoulette
	x.fieldMap["WinLottery"] = x.WinLottery
	x.fieldMap["WinLowLottery"] = x.WinLowLottery
	x.fieldMap["WinQiPai"] = x.WinQiPai
	x.fieldMap["WinDianZhi"] = x.WinDianZhi
	x.fieldMap["WinXiaoYouXi"] = x.WinXiaoYouXi
	x.fieldMap["WinLive"] = x.WinLive
	x.fieldMap["WinSport"] = x.WinSport
	x.fieldMap["WinTexas"] = x.WinTexas
	x.fieldMap["LiuShuiHaXi"] = x.LiuShuiHaXi
	x.fieldMap["LiuShuiHaXiRoulette"] = x.LiuShuiHaXiRoulette
	x.fieldMap["LiuShuiTranferUsdtHaXi"] = x.LiuShuiTranferUsdtHaXi
	x.fieldMap["LiuShuiTranferUsdtHaXiRoulette"] = x.LiuShuiTranferUsdtHaXiRoulette
	x.fieldMap["LiuShuiTranferTrxHaXi"] = x.LiuShuiTranferTrxHaXi
	x.fieldMap["LiuShuiTranferTrxHaXiRoulette"] = x.LiuShuiTranferTrxHaXiRoulette
	x.fieldMap["LiuShuiLottery"] = x.LiuShuiLottery
	x.fieldMap["LiuShuiLowLottery"] = x.LiuShuiLowLottery
	x.fieldMap["LiuShuiQiPai"] = x.LiuShuiQiPai
	x.fieldMap["LiuShuiDianZhi"] = x.LiuShuiDianZhi
	x.fieldMap["LiuShuiXiaoYouXi"] = x.LiuShuiXiaoYouXi
	x.fieldMap["LiuShuiLive"] = x.LiuShuiLive
	x.fieldMap["LiuShuiSport"] = x.LiuShuiSport
	x.fieldMap["LiuShuiTexas"] = x.LiuShuiTexas
	x.fieldMap["RechargeCount"] = x.RechargeCount
	x.fieldMap["RechargeAmount"] = x.RechargeAmount
	x.fieldMap["FirstRechargeAmount"] = x.FirstRechargeAmount
	x.fieldMap["WithdrawCount"] = x.WithdrawCount
	x.fieldMap["WithdrawAmount"] = x.WithdrawAmount
	x.fieldMap["ActivityRewardCount"] = x.ActivityRewardCount
	x.fieldMap["ActivityRewardAmount"] = x.ActivityRewardAmount
	x.fieldMap["VipRewardCount"] = x.VipRewardCount
	x.fieldMap["VipRewardAmount"] = x.VipRewardAmount
	x.fieldMap["ManRewardCount"] = x.ManRewardCount
	x.fieldMap["ManRewardAmount"] = x.ManRewardAmount
	x.fieldMap["GetCommissionCount"] = x.GetCommissionCount
	x.fieldMap["GetCommissionAmount"] = x.GetCommissionAmount
	x.fieldMap["RewardHaXi"] = x.RewardHaXi
	x.fieldMap["RewardHaXiRoulette"] = x.RewardHaXiRoulette
	x.fieldMap["RewardLottery"] = x.RewardLottery
	x.fieldMap["RewardLowLottery"] = x.RewardLowLottery
	x.fieldMap["RewardQiPai"] = x.RewardQiPai
	x.fieldMap["RewardDianZhi"] = x.RewardDianZhi
	x.fieldMap["RewardXiaoYouXi"] = x.RewardXiaoYouXi
	x.fieldMap["RewardLive"] = x.RewardLive
	x.fieldMap["RewardSport"] = x.RewardSport
	x.fieldMap["RewardTexas"] = x.RewardTexas
	x.fieldMap["TrxRate"] = x.TrxRate
	x.fieldMap["CommissionAmount"] = x.CommissionAmount
	x.fieldMap["CommissionTrxAmount"] = x.CommissionTrxAmount
	x.fieldMap["IsVaild"] = x.IsVaild
	x.fieldMap["IsRealVaild"] = x.IsRealVaild
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["UpdateTime"] = x.UpdateTime
}

func (x xAgentCommissionUserDate) clone(db *gorm.DB) xAgentCommissionUserDate {
	x.xAgentCommissionUserDateDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xAgentCommissionUserDate) replaceDB(db *gorm.DB) xAgentCommissionUserDate {
	x.xAgentCommissionUserDateDo.ReplaceDB(db)
	return x
}

type xAgentCommissionUserDateDo struct{ gen.DO }

func (x xAgentCommissionUserDateDo) Debug() *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Debug())
}

func (x xAgentCommissionUserDateDo) WithContext(ctx context.Context) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xAgentCommissionUserDateDo) ReadDB() *xAgentCommissionUserDateDo {
	return x.Clauses(dbresolver.Read)
}

func (x xAgentCommissionUserDateDo) WriteDB() *xAgentCommissionUserDateDo {
	return x.Clauses(dbresolver.Write)
}

func (x xAgentCommissionUserDateDo) Session(config *gorm.Session) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Session(config))
}

func (x xAgentCommissionUserDateDo) Clauses(conds ...clause.Expression) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xAgentCommissionUserDateDo) Returning(value interface{}, columns ...string) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xAgentCommissionUserDateDo) Not(conds ...gen.Condition) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xAgentCommissionUserDateDo) Or(conds ...gen.Condition) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xAgentCommissionUserDateDo) Select(conds ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xAgentCommissionUserDateDo) Where(conds ...gen.Condition) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xAgentCommissionUserDateDo) Order(conds ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xAgentCommissionUserDateDo) Distinct(cols ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xAgentCommissionUserDateDo) Omit(cols ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xAgentCommissionUserDateDo) Join(table schema.Tabler, on ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xAgentCommissionUserDateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xAgentCommissionUserDateDo) RightJoin(table schema.Tabler, on ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xAgentCommissionUserDateDo) Group(cols ...field.Expr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xAgentCommissionUserDateDo) Having(conds ...gen.Condition) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xAgentCommissionUserDateDo) Limit(limit int) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xAgentCommissionUserDateDo) Offset(offset int) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xAgentCommissionUserDateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xAgentCommissionUserDateDo) Unscoped() *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xAgentCommissionUserDateDo) Create(values ...*model.XAgentCommissionUserDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xAgentCommissionUserDateDo) CreateInBatches(values []*model.XAgentCommissionUserDate, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xAgentCommissionUserDateDo) Save(values ...*model.XAgentCommissionUserDate) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xAgentCommissionUserDateDo) First() (*model.XAgentCommissionUserDate, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionUserDate), nil
	}
}

func (x xAgentCommissionUserDateDo) Take() (*model.XAgentCommissionUserDate, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionUserDate), nil
	}
}

func (x xAgentCommissionUserDateDo) Last() (*model.XAgentCommissionUserDate, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionUserDate), nil
	}
}

func (x xAgentCommissionUserDateDo) Find() ([]*model.XAgentCommissionUserDate, error) {
	result, err := x.DO.Find()
	return result.([]*model.XAgentCommissionUserDate), err
}

func (x xAgentCommissionUserDateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XAgentCommissionUserDate, err error) {
	buf := make([]*model.XAgentCommissionUserDate, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xAgentCommissionUserDateDo) FindInBatches(result *[]*model.XAgentCommissionUserDate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xAgentCommissionUserDateDo) Attrs(attrs ...field.AssignExpr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xAgentCommissionUserDateDo) Assign(attrs ...field.AssignExpr) *xAgentCommissionUserDateDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xAgentCommissionUserDateDo) Joins(fields ...field.RelationField) *xAgentCommissionUserDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xAgentCommissionUserDateDo) Preload(fields ...field.RelationField) *xAgentCommissionUserDateDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xAgentCommissionUserDateDo) FirstOrInit() (*model.XAgentCommissionUserDate, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionUserDate), nil
	}
}

func (x xAgentCommissionUserDateDo) FirstOrCreate() (*model.XAgentCommissionUserDate, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XAgentCommissionUserDate), nil
	}
}

func (x xAgentCommissionUserDateDo) FindByPage(offset int, limit int) (result []*model.XAgentCommissionUserDate, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xAgentCommissionUserDateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xAgentCommissionUserDateDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xAgentCommissionUserDateDo) Delete(models ...*model.XAgentCommissionUserDate) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xAgentCommissionUserDateDo) withDO(do gen.Dao) *xAgentCommissionUserDateDo {
	x.DO = *do.(*gen.DO)
	return x
}
