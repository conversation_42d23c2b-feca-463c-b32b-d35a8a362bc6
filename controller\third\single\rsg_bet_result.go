package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// BetResult 游戏结算 API URL BetResult
func (l *RSGSingleService) BetResult(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string  `json:"SystemCode"`    // 系統代碼(只限英數)，必填，长度2~20
		WebId         string  `json:"WebId"`         // 站台代碼(只限英數)，必填，长度3~20
		UserId        string  `json:"UserId"`        // 會員惟一識別碼(只限英數)，必填，长度3~20
		TransactionID string  `json:"TransactionID"` // 交易惟一識別碼(只限英數)，必填，长度23
		Currency      string  `json:"Currency"`      // 幣別代碼(請參照代碼表)，必填，长度2~5
		GameId        int     `json:"GameId"`        // 遊戲代碼(請參照代碼表)，必填
		SubGameType   int     `json:"SubGameType"`   // 子遊戲代碼(請參照代碼表)，必填
		SequenNumber  int64   `json:"SequenNumber"`  // 遊戲紀錄惟一編號，必填
		Amount        float64 `json:"Amount"`        // 贏分金額(小數點兩位) (範圍 0.00~9999999999.99)，必填
		PlayTime      string  `json:"PlayTime"`      // 遊戲時間(yyyy-MM-dd HH:mm:ss)，必填，长度19
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 游戏结算")
	if err != nil {
		logs.Error("RSG_single 游戏结算 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 游戏结算 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 游戏结算 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 游戏结算 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 游戏结算 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	//检查订单是否已结算
	tablePre := "x_third_dianzhi_pre_order"
	table := "x_third_dianzhi"
	thirdId := strconv.Itoa(int(reqdata.SequenNumber))
	rawData := decryptedBytes
	// 开始结算事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {

		// 查询订单是否存在
		order, exists, err := base.GetOrderForUpdate(tx, thirdId, l.brandName, tablePre)
		// 处理查询错误
		if err != nil {
			// 查询出错
			logs.Error("RSG_single 游戏结算 查询预下注订单失败 thirdId=", thirdId, " err=", err.Error(), " order=", order)
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return err
		}

		// 判断订单是否存在
		if !exists {
			logs.Error("RSG_single 游戏结算 预下注订单不存在，可能是先收到结算请求 thirdId=", thirdId, " order=", order)
			respdata.ErrorCode = RSG_Code_Seq_Not_Exist
			respdata.ErrorMessage = "订单不存在"
			return errors.New(fmt.Sprintf("订单不存在，thirdId= %s", thirdId))
		}

		if order.DataState != -1 {
			err = errors.New("订单已结算")
			logs.Error("RSG_single 派彩 订单已结算  thirdId=", thirdId, " order=", order)
			respdata.ErrorCode = RSG_Code_Seq_Settled
			respdata.ErrorMessage = "系统维护中"
			return err
		}

		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 游戏结算 获取用户余额失败 userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		}

		// 获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		// 除了体育所有三方的有效流水取不大于下注金额的输赢绝对值
		validBet := math.Abs(reqdata.Amount - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}
		// 更新注单状态
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"DataState":  1, // 已结算状态
			"ThirdTime":  utils.GetCurrentTime(),
			"ValidBet":   validBet,
			"WinAmount":  reqdata.Amount,
			"BetCtx":     string(rawData),
			"GameRst":    string(rawData),
			"BetCtxType": 3,
			"RawData":    string(rawData),
		}).Error
		if e != nil {
			logs.Error("RSG_single 游戏结算 更新订单状态失败 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		order.DataState = 1
		order.ThirdTime = utils.GetCurrentTime()
		order.ValidBet = validBet
		order.WinAmount = reqdata.Amount
		order.BetCtx = string(rawData)
		order.GameRst = string(rawData)
		order.BetCtxType = 3
		order.RawData = string(decryptedBytes)
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("RSG_single 游戏结算 创建正式表订单失败  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		// 更新用户余额（只有在有赢利时才更新）
		if reqdata.Amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", reqdata.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 派彩 加扣款失败  userId=", userId, " thirdId=", thirdId, " v=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       reqdata.Amount,
			AfterAmount:  userBalance.Amount + reqdata.Amount,
			Reason:       utils.BalanceCReasonRSGWin,
			Memo:         "RSG win,thirdId:" + thirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    ChannelId,
			CreateTime:   utils.GetCurrentTime(),
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("RSG_single 游戏结算 创建账变记录失败 userId=", userId, " reqdata.WinAmount=", reqdata.Amount, " err=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		// 设置响应数据
		respdata.Data.Balance = userBalance.Amount
		if reqdata.Amount > 0 {
			respdata.Data.Balance += reqdata.Amount
		}
		balance := math.Floor(respdata.Data.Balance*100) / 100
		respdata.Data.Balance = balance
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 游戏结算 事务处理失败 userId=", userId, " err=", err.Error())

		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
	if err != nil {
		logs.Error("RSG_single 游戏结算 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 游戏结算")
		return
	}

	// 推送派奖事件
	if l.thirdGamePush != nil && reqdata.Amount > 0 {
		//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 游戏结算 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 游戏结算 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// JackpotResult 彩池奖金结算 API URL JackpotResult
func (l *RSGSingleService) Jackpot(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode    string  `json:"SystemCode"`    // 系統代碼(只限英數)，必填，长度2~20
		WebId         string  `json:"WebId"`         // 站台代碼(只限英數)，必填，长度3~20
		UserId        string  `json:"UserId"`        // 會員惟一識別碼(只限英數)，必填，长度3~20
		TransactionID string  `json:"TransactionID"` // 交易惟一識別碼(只限英數)，必填，长度23
		Currency      string  `json:"Currency"`      // 幣別代碼(請參照代碼表)，必填，长度2~5
		GameId        int     `json:"GameId"`        // 遊戲代碼(請參照代碼表)，必填
		SubGameType   int     `json:"SubGameType"`   // 子遊戲代碼(請參照代碼表)，必填
		SequenNumber  int64   `json:"SequenNumber"`  // 遊戲紀錄惟一編號，必填
		Amount        float64 `json:"Amount"`        // 贏分金額(小數點兩位) (範圍 0.00~9999999999.99)，必填
		PlayTime      string  `json:"PlayTime"`      // 遊戲時間(yyyy-MM-dd HH:mm:ss)，必填，长度19
	}

	type ResponseData struct {
		ErrorCode    int    `json:"ErrorCode"`    // 错误代码，0为成功，其他为失败
		ErrorMessage string `json:"ErrorMessage"` // 错误信息
		Timestamp    int64  `json:"Timestamp"`    // 时间戳
		Data         struct {
			Balance float64 `json:"Balance"` // 会员当下余额
		} `json:"Data"`
	}

	respdata := ResponseData{
		ErrorCode:    RSG_Code_Success,
		ErrorMessage: "OK",
		Timestamp:    time.Now().Unix(),
	}

	// 处理加密请求
	decryptedBytes, _, _, _, err := l.ProcessEncryptedRequest(ctx, "RSG_single 彩池奖金结算")
	if err != nil {
		logs.Error("RSG_single 彩池奖金结算 处理请求失败: ", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("RSG_single 彩池奖金结算 解析请求消息体错误 err=", err.Error())
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 验证系统代码和站台代码
	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) || !strings.EqualFold(reqdata.WebId, l.webId) {
		logs.Error("RSG_single 彩池奖金结算 商户编码不正确 reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode,
			" reqdata.WebId=", reqdata.WebId, " l.webId=", l.webId)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 验证币种
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("RSG_single 彩池奖金结算 币种不正确 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.ErrorCode = RSG_Code_Illegal_Args
		respdata.ErrorMessage = "无效的参数"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 转换用户ID
	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("RSG_single 彩池奖金结算 用户ID转换错误 reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.ErrorCode = RSG_Code_Player_Not_Exist
		respdata.ErrorMessage = "此玩家帐户不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 检查彩池奖金订单是否已存在
	thirdId := strconv.Itoa(int(reqdata.SequenNumber))
	tablePre := "x_third_dianzhi_pre_order"
	table := "x_third_dianzhi"

	// 开始彩池奖金结算事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询订单是否存在
		orderPre, exists, err := base.GetOrderForUpdate(tx, thirdId, l.brandName, tablePre)
		// 处理查询错误
		if err != nil {
			// 查询出错
			logs.Error("RSG_single 彩池奖金结算 查询订单失败 thirdId=", thirdId, " err=", err.Error(), " order=", orderPre)
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return err
		}

		// 判断订单是否存在
		if exists {
			logs.Error("RSG_single 彩池奖金结算 订单已存在 thirdId=", thirdId, " order=", orderPre)
			respdata.ErrorCode = RSG_Code_Duplicate_Seq
			respdata.ErrorMessage = "订单号重复"
			return errors.New(fmt.Sprintf("订单号重复，thirdId= %s", thirdId))
		}

		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("RSG_single 彩池奖金结算 获取用户余额失败 userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.ErrorCode = RSG_Code_Player_Not_Exist
				respdata.ErrorMessage = "此玩家帐户不存在"
			} else {
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
			}
			return e
		}

		// 获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		respdata.Data.Balance = userBalance.Amount

		thirdTime := utils.GetCurrentTime()

		// 获取游戏名称
		gameName := ""
		if name, ok := l.games[reqdata.GameId]; ok {
			gameName = name
		} else {
			gameList := thirdGameModel.GameList{}
			e = tx.Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
			if e == nil {
				gameName = gameList.Name
			} else {
				gameName = l.brandName + "_" + strconv.Itoa(reqdata.GameId)
			}
		}

		gameName = gameName + "(中彩金)"
		// 创建彩池奖金订单
		rawData, _ := json.Marshal(reqdata)
		order := thirdGameModel.ThirdOrder{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       strconv.Itoa(reqdata.GameId),
			GameName:     gameName,
			BetAmount:    0,
			WinAmount:    reqdata.Amount,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(rawData),
			State:        1,
			DataState:    1, // 已派奖
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("RSG_single 彩池奖金结算 创建预设表订单失败 userId=", userId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("RSG_single 彩池奖金结算 创建正式表订单失败 userId=", userId, " thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.ErrorCode = RSG_Code_System_Busy
			respdata.ErrorMessage = "系统维护中"
			return e
		}

		// 更新用户余额
		if reqdata.Amount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", reqdata.Amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Amount != 0 {
				e = fmt.Errorf("更新条数0")
			}
			if e != nil {
				logs.Error("RSG_single 彩池奖金结算 更新用户余额失败 userId=", userId, " reqdata.JackpotAmount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}

			// 创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       reqdata.Amount,
				AfterAmount:  userBalance.Amount + reqdata.Amount,
				Reason:       utils.BalanceCReasonRSGJackpot,
				Memo:         "RSG jackpot,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("RSG_single 彩池奖金结算 创建账变记录失败 userId=", userId, " reqdata.JackpotAmount=", reqdata.Amount, " err=", e.Error())
				respdata.ErrorCode = RSG_Code_System_Busy
				respdata.ErrorMessage = "系统维护中"
				return e
			}
		}

		respdata.Data.Balance = userBalance.Amount + reqdata.Amount
		balance := math.Floor(respdata.Data.Balance*100) / 100
		respdata.Data.Balance = balance
		return nil
	})

	if err != nil {
		logs.Error("RSG_single 彩池奖金结算 事务处理失败 userId=", userId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
	if err != nil {
		logs.Error("RSG_single 彩池奖金结算 加密响应数据失败 err=", err.Error())
		respdata.ErrorCode = RSG_Code_System_Busy
		respdata.ErrorMessage = "系统维护中"
		l.ProcessEncryptedResponse(ctx, respdata, "RSG_single 彩池奖金结算")
		return
	}

	// 推送派奖事件
	if l.thirdGamePush != nil && reqdata.Amount > 0 {
		//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
		l.thirdGamePush.PushRewardEvent(1, l.brandName, thirdId)
	}

	// 发送余额变动通 知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][RSG_single] 彩池奖金结算 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][RSG_single] 彩池奖金结算 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}
