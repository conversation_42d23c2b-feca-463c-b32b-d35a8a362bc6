package controller

import (
	"context"
	"errors"
	"xserver/abugo"
	"xserver/server"
	"xserver/utils"
)

func (this *ActiveController) InitEx() {
	gropu := server.Http().NewGroup("/api/active")
	{
		// 邀请活动批量领取
		gropu.Post("/active_apply_batch", this.active_apply_recommend_friend_reward_batch)
	}

}

// active_apply_recommend_friend_reward_batch 批量处理推荐好友奖励申请
// 支持一次调用申请多个等级的推荐好友奖励
func (this *ActiveController) active_apply_recommend_friend_reward_batch(ctx *abugo.AbuHttpContent) {
	defer recover()

	// 定义请求数据结构
	type RequestData struct {
		ActiveId int   `json:"activeId" validate:"required"` // 活动ID，必填
		Levels   []int `json:"levels" validate:"required"`   // 申请的活动等级列表，必填
		IsAgent  int   `json:"isAgent"`                      // 是否为代理
	}

	errcode := 0
	reqdata := RequestData{}

	// 解析请求数据
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	// 验证活动ID是否为推荐好友奖励活动
	if reqdata.ActiveId != utils.KActiveIdRecommendFriendReward {
		ctx.RespErr(errors.New("该接口仅支持推荐好友奖励活动"), &errcode)
		return
	}

	// 验证等级列表不为空
	if len(reqdata.Levels) == 0 {
		ctx.RespErr(errors.New("levels不能为空"), &errcode)
		return
	}

	// 获取用户令牌信息
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = 77767503
	}
	// 存储处理结果
	results := make([]map[string]interface{}, 0, len(reqdata.Levels))

	// 遍历每个等级进行处理
	for _, level := range reqdata.Levels {
		if this.CheckInvitationApply(int32(level), token) {
			continue
		}
		result := map[string]interface{}{
			"level":     level,
			"status":    "success",
			"message":   "",
			"errorCode": 0,
		}

		// 调用现有的推荐好友奖励处理函数
		err, code := RecommendFriendReward(reqdata.ActiveId, level, token)
		if err != nil {
			result["status"] = "failed"
			result["message"] = err.Error()
			result["errorCode"] = code
		}

		results = append(results, result)
	}

	ctx.Put("results", results)
	ctx.RespOK()
}

// 邀请奖励:检查是否已经申请
func (this *ActiveController) CheckInvitationApply(level int32, token *server.TokenData) bool {
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, _ := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(54)).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(level)).
		Where(activeRewardAuditTb.InviteRewardType.Eq(2)).First()

	if activeRewardAudit != nil && activeRewardAudit.ID > 0 {
		return true
	}
	return false
}
