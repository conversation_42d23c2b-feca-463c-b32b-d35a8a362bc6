// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXAgentCommissionUserDate = "x_agent_commission_user_date"

// XAgentCommissionUserDate 三级返佣下级数据(按日期统计)
type XAgentCommissionUserDate struct {
	StatDate                       time.Time `gorm:"column:StatDate;primaryKey;comment:日期" json:"StatDate"`                                                                      // 日期
	AgentID                        int32     `gorm:"column:AgentId;primaryKey;comment:代理Id" json:"AgentId"`                                                                      // 代理Id
	UserID                         int32     `gorm:"column:UserId;primaryKey;comment:玩家" json:"UserId"`                                                                          // 玩家
	ParentAgentID                  int32     `gorm:"column:ParentAgentId;comment:上级代理Id" json:"ParentAgentId"`                                                                   // 上级代理Id
	TopAgentID                     int32     `gorm:"column:TopAgentId;comment:顶级代理Id" json:"TopAgentId"`                                                                         // 顶级代理Id
	ChannelID                      int32     `gorm:"column:ChannelId;comment:渠道Id" json:"ChannelId"`                                                                             // 渠道Id
	SellerID                       int32     `gorm:"column:SellerId;comment:运营商Id" json:"SellerId"`                                                                              // 运营商Id
	SchemeID                       int32     `gorm:"column:SchemeId;not null;comment:方案Id" json:"SchemeId"`                                                                      // 方案Id
	TeamLevel                      int32     `gorm:"column:TeamLevel;not null;comment:团队等级" json:"TeamLevel"`                                                                    // 团队等级
	AgentLevel                     int32     `gorm:"column:AgentLevel;not null;default:1;comment:代理等级" json:"AgentLevel"`                                                        // 代理等级
	BetHaXi                        float64   `gorm:"column:BetHaXi;not null;default:0.000000;comment:哈希余额投注额" json:"BetHaXi"`                                                    // 哈希余额投注额
	BetHaXiRoulette                float64   `gorm:"column:BetHaXiRoulette;not null;default:0.000000;comment:哈希轮盘余额投注额" json:"BetHaXiRoulette"`                                  // 哈希轮盘余额投注额
	BetTranferUsdtHaXi             float64   `gorm:"column:BetTranferUsdtHaXi;not null;default:0.000000;comment:哈希转账Usdt投注额" json:"BetTranferUsdtHaXi"`                          // 哈希转账Usdt投注额
	BetTranferUsdtHaXiRoulette     float64   `gorm:"column:BetTranferUsdtHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Usdt投注额" json:"BetTranferUsdtHaXiRoulette"`        // 哈希轮盘转账Usdt投注额
	BetTranferTrxHaXi              float64   `gorm:"column:BetTranferTrxHaXi;not null;default:0.000000;comment:哈希转账Trx投注额" json:"BetTranferTrxHaXi"`                             // 哈希转账Trx投注额
	BetTranferTrxHaXiRoulette      float64   `gorm:"column:BetTranferTrxHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Trx投注额" json:"BetTranferTrxHaXiRoulette"`           // 哈希轮盘转账Trx投注额
	BetLottery                     float64   `gorm:"column:BetLottery;not null;default:0.000000;comment:彩票投注额" json:"BetLottery"`                                                // 彩票投注额
	BetLowLottery                  float64   `gorm:"column:BetLowLottery;not null;default:0.000000;comment:低频彩投注额" json:"BetLowLottery"`                                         // 低频彩投注额
	BetQiPai                       float64   `gorm:"column:BetQiPai;not null;default:0.000000;comment:棋牌投注额" json:"BetQiPai"`                                                    // 棋牌投注额
	BetDianZhi                     float64   `gorm:"column:BetDianZhi;not null;default:0.000000;comment:电子投注额" json:"BetDianZhi"`                                                // 电子投注额
	BetXiaoYouXi                   float64   `gorm:"column:BetXiaoYouXi;not null;default:0.000000;comment:小游戏投注额" json:"BetXiaoYouXi"`                                           // 小游戏投注额
	BetLive                        float64   `gorm:"column:BetLive;not null;default:0.000000;comment:真人投注额" json:"BetLive"`                                                      // 真人投注额
	BetSport                       float64   `gorm:"column:BetSport;not null;default:0.000000;comment:体育投注额" json:"BetSport"`                                                    // 体育投注额
	BetTexas                       float64   `gorm:"column:BetTexas;not null;default:0.000000;comment:德州投注额" json:"BetTexas"`                                                    // 德州投注额
	WinHaXi                        float64   `gorm:"column:WinHaXi;not null;default:0.000000;comment:哈希余额派奖额" json:"WinHaXi"`                                                    // 哈希余额派奖额
	WinHaXiRoulette                float64   `gorm:"column:WinHaXiRoulette;not null;default:0.000000;comment:哈希轮盘余额派奖额" json:"WinHaXiRoulette"`                                  // 哈希轮盘余额派奖额
	WinTranferUsdtHaXi             float64   `gorm:"column:WinTranferUsdtHaXi;not null;default:0.000000;comment:哈希转账Usdt派奖额" json:"WinTranferUsdtHaXi"`                          // 哈希转账Usdt派奖额
	WinTranferUsdtHaXiRoulette     float64   `gorm:"column:WinTranferUsdtHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Usdt派奖额" json:"WinTranferUsdtHaXiRoulette"`        // 哈希轮盘转账Usdt派奖额
	WinTranferTrxHaXi              float64   `gorm:"column:WinTranferTrxHaXi;not null;default:0.000000;comment:哈希转账Trx派奖额" json:"WinTranferTrxHaXi"`                             // 哈希转账Trx派奖额
	WinTranferTrxHaXiRoulette      float64   `gorm:"column:WinTranferTrxHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Trx派奖额" json:"WinTranferTrxHaXiRoulette"`           // 哈希轮盘转账Trx派奖额
	WinLottery                     float64   `gorm:"column:WinLottery;not null;default:0.000000;comment:彩票派奖额" json:"WinLottery"`                                                // 彩票派奖额
	WinLowLottery                  float64   `gorm:"column:WinLowLottery;not null;default:0.000000;comment:低频彩派奖额" json:"WinLowLottery"`                                         // 低频彩派奖额
	WinQiPai                       float64   `gorm:"column:WinQiPai;not null;default:0.000000;comment:棋牌派奖额" json:"WinQiPai"`                                                    // 棋牌派奖额
	WinDianZhi                     float64   `gorm:"column:WinDianZhi;not null;default:0.000000;comment:电子派奖额" json:"WinDianZhi"`                                                // 电子派奖额
	WinXiaoYouXi                   float64   `gorm:"column:WinXiaoYouXi;not null;default:0.000000;comment:小游戏派奖额" json:"WinXiaoYouXi"`                                           // 小游戏派奖额
	WinLive                        float64   `gorm:"column:WinLive;not null;default:0.000000;comment:真人派奖额" json:"WinLive"`                                                      // 真人派奖额
	WinSport                       float64   `gorm:"column:WinSport;not null;default:0.000000;comment:体育派奖额" json:"WinSport"`                                                    // 体育派奖额
	WinTexas                       float64   `gorm:"column:WinTexas;not null;default:0.000000;comment:德州派奖额" json:"WinTexas"`                                                    // 德州派奖额
	LiuShuiHaXi                    float64   `gorm:"column:LiuShuiHaXi;not null;default:0.000000;comment:哈希余额流水" json:"LiuShuiHaXi"`                                             // 哈希余额流水
	LiuShuiHaXiRoulette            float64   `gorm:"column:LiuShuiHaXiRoulette;not null;default:0.000000;comment:哈希轮盘余额流水" json:"LiuShuiHaXiRoulette"`                           // 哈希轮盘余额流水
	LiuShuiTranferUsdtHaXi         float64   `gorm:"column:LiuShuiTranferUsdtHaXi;not null;default:0.000000;comment:哈希转账Usdt流水" json:"LiuShuiTranferUsdtHaXi"`                   // 哈希转账Usdt流水
	LiuShuiTranferUsdtHaXiRoulette float64   `gorm:"column:LiuShuiTranferUsdtHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Usdt流水" json:"LiuShuiTranferUsdtHaXiRoulette"` // 哈希轮盘转账Usdt流水
	LiuShuiTranferTrxHaXi          float64   `gorm:"column:LiuShuiTranferTrxHaXi;not null;default:0.000000;comment:哈希转账Trx流水" json:"LiuShuiTranferTrxHaXi"`                      // 哈希转账Trx流水
	LiuShuiTranferTrxHaXiRoulette  float64   `gorm:"column:LiuShuiTranferTrxHaXiRoulette;not null;default:0.000000;comment:哈希轮盘转账Trx流水" json:"LiuShuiTranferTrxHaXiRoulette"`    // 哈希轮盘转账Trx流水
	LiuShuiLottery                 float64   `gorm:"column:LiuShuiLottery;not null;default:0.000000;comment:彩票流水" json:"LiuShuiLottery"`                                         // 彩票流水
	LiuShuiLowLottery              float64   `gorm:"column:LiuShuiLowLottery;not null;default:0.000000;comment:低频彩流水" json:"LiuShuiLowLottery"`                                  // 低频彩流水
	LiuShuiQiPai                   float64   `gorm:"column:LiuShuiQiPai;not null;default:0.000000;comment:棋牌流水" json:"LiuShuiQiPai"`                                             // 棋牌流水
	LiuShuiDianZhi                 float64   `gorm:"column:LiuShuiDianZhi;not null;default:0.000000;comment:电子流水" json:"LiuShuiDianZhi"`                                         // 电子流水
	LiuShuiXiaoYouXi               float64   `gorm:"column:LiuShuiXiaoYouXi;not null;default:0.000000;comment:小游戏流水" json:"LiuShuiXiaoYouXi"`                                    // 小游戏流水
	LiuShuiLive                    float64   `gorm:"column:LiuShuiLive;not null;default:0.000000;comment:真人流水" json:"LiuShuiLive"`                                               // 真人流水
	LiuShuiSport                   float64   `gorm:"column:LiuShuiSport;not null;default:0.000000;comment:体育流水" json:"LiuShuiSport"`                                             // 体育流水
	LiuShuiTexas                   float64   `gorm:"column:LiuShuiTexas;not null;default:0.000000;comment:德州流水" json:"LiuShuiTexas"`                                             // 德州流水
	RechargeCount                  int32     `gorm:"column:RechargeCount;not null;comment:充值笔数" json:"RechargeCount"`                                                            // 充值笔数
	RechargeAmount                 float64   `gorm:"column:RechargeAmount;not null;default:0.000000;comment:充值金额" json:"RechargeAmount"`                                         // 充值金额
	FirstRechargeAmount            float64   `gorm:"column:FirstRechargeAmount;not null;default:0.000000;comment:首充金额" json:"FirstRechargeAmount"`                               // 首充金额
	WithdrawCount                  int32     `gorm:"column:WithdrawCount;not null;comment:提款笔数" json:"WithdrawCount"`                                                            // 提款笔数
	WithdrawAmount                 float64   `gorm:"column:WithdrawAmount;not null;default:0.000000;comment:提现金额" json:"WithdrawAmount"`                                         // 提现金额
	ActivityRewardCount            int32     `gorm:"column:ActivityRewardCount;not null;comment:活动彩金笔数" json:"ActivityRewardCount"`                                              // 活动彩金笔数
	ActivityRewardAmount           float64   `gorm:"column:ActivityRewardAmount;not null;default:0.000000;comment:活动彩金金额" json:"ActivityRewardAmount"`                           // 活动彩金金额
	VipRewardCount                 int32     `gorm:"column:VipRewardCount;not null;comment:Vip活动彩金笔数" json:"VipRewardCount"`                                                     // Vip活动彩金笔数
	VipRewardAmount                float64   `gorm:"column:VipRewardAmount;not null;default:0.000000;comment:Vip活动彩金金额" json:"VipRewardAmount"`                                  // Vip活动彩金金额
	ManRewardCount                 int32     `gorm:"column:ManRewardCount;not null;comment:人工彩金笔数" json:"ManRewardCount"`                                                        // 人工彩金笔数
	ManRewardAmount                float64   `gorm:"column:ManRewardAmount;not null;default:0.000000;comment:人工彩金金额" json:"ManRewardAmount"`                                     // 人工彩金金额
	GetCommissionCount             int32     `gorm:"column:GetCommissionCount;not null;comment:领取佣金笔数" json:"GetCommissionCount"`                                                // 领取佣金笔数
	GetCommissionAmount            float64   `gorm:"column:GetCommissionAmount;not null;default:0.000000;comment:领取佣金金额" json:"GetCommissionAmount"`                             // 领取佣金金额
	RewardHaXi                     float64   `gorm:"column:RewardHaXi;not null;default:0.000000;comment:哈希返佣(百分比)" json:"RewardHaXi"`                                            // 哈希返佣(百分比)
	RewardHaXiRoulette             float64   `gorm:"column:RewardHaXiRoulette;not null;default:0.000000;comment:哈希轮盘返佣(百分比)" json:"RewardHaXiRoulette"`                          // 哈希轮盘返佣(百分比)
	RewardLottery                  float64   `gorm:"column:RewardLottery;not null;default:0.000000;comment:彩票返佣(百分比)" json:"RewardLottery"`                                      // 彩票返佣(百分比)
	RewardLowLottery               float64   `gorm:"column:RewardLowLottery;not null;default:0.000000;comment:低频彩返佣(百分比)" json:"RewardLowLottery"`                               // 低频彩返佣(百分比)
	RewardQiPai                    float64   `gorm:"column:RewardQiPai;not null;default:0.000000;comment:棋牌返佣(百分比)" json:"RewardQiPai"`                                          // 棋牌返佣(百分比)
	RewardDianZhi                  float64   `gorm:"column:RewardDianZhi;not null;default:0.000000;comment:电子返佣(百分比)" json:"RewardDianZhi"`                                      // 电子返佣(百分比)
	RewardXiaoYouXi                float64   `gorm:"column:RewardXiaoYouXi;not null;default:0.000000;comment:小游戏返佣(百分比)" json:"RewardXiaoYouXi"`                                 // 小游戏返佣(百分比)
	RewardLive                     float64   `gorm:"column:RewardLive;not null;default:0.000000;comment:真人返佣(百分比)" json:"RewardLive"`                                            // 真人返佣(百分比)
	RewardSport                    float64   `gorm:"column:RewardSport;not null;default:0.000000;comment:体育返佣(百分比)" json:"RewardSport"`                                          // 体育返佣(百分比)
	RewardTexas                    float64   `gorm:"column:RewardTexas;not null;default:0.000000;comment:德州返佣(百分比)" json:"RewardTexas"`                                          // 德州返佣(百分比)
	TrxRate                        float64   `gorm:"column:TrxRate;not null;default:0.300000;comment:Trx汇率" json:"TrxRate"`                                                      // Trx汇率
	CommissionAmount               float64   `gorm:"column:CommissionAmount;not null;default:0.000000;comment:结算佣金Usdt" json:"CommissionAmount"`                                 // 结算佣金Usdt
	CommissionTrxAmount            float64   `gorm:"column:CommissionTrxAmount;not null;default:0.000000;comment:结算佣金Trx" json:"CommissionTrxAmount"`                            // 结算佣金Trx
	IsVaild                        int32     `gorm:"column:IsVaild;not null;default:2;comment:是否有效玩家：1有效 2无效" json:"IsVaild"`                                                    // 是否有效玩家：1有效 2无效
	IsRealVaild                    int32     `gorm:"column:IsRealVaild;not null;default:2;comment:是否真实有效玩家：1有效 2无效" json:"IsRealVaild"`                                          // 是否真实有效玩家：1有效 2无效
	CreateTime                     time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`                                        // 创建时间
	UpdateTime                     time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"`                                        // 更新时间
}

// TableName XAgentCommissionUserDate's table name
func (*XAgentCommissionUserDate) TableName() string {
	return TableNameXAgentCommissionUserDate
}
