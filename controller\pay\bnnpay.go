package paycontroller

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"xserver/abugo"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

// 数据脱敏工具函数
// maskKey 脱敏密钥，只显示前3位和后3位
func maskKey(key string) string {
	if len(key) <= 6 {
		return "***"
	}
	return key[:3] + "***" + key[len(key)-3:]
}

// maskIP 脱敏IP地址，隐藏最后一段
func maskIP(ip string) string {
	parts := strings.Split(ip, ".")
	if len(parts) == 4 {
		return fmt.Sprintf("%s.%s.%s.***", parts[0], parts[1], parts[2])
	}
	return "***"
}

// maskName 脱敏姓名，只显示第一个字符
func maskName(name string) string {
	if len(name) == 0 {
		return "***"
	}
	if len(name) == 1 {
		return "*"
	}
	return string([]rune(name)[0]) + strings.Repeat("*", len([]rune(name))-1)
}

// maskAmount 脱敏金额，隐藏中间部分
func maskAmount(amount string) string {
	if len(amount) <= 4 {
		return "***"
	}
	return amount[:2] + "***" + amount[len(amount)-2:]
}

// maskSign 脱敏签名，只显示前6位和后6位
func maskSign(sign string) string {
	if len(sign) <= 12 {
		return "***"
	}
	return sign[:6] + "***" + sign[len(sign)-6:]
}

// maskBankNum 脱敏银行卡号，只显示前4位和后4位
func maskBankNum(bankNum string) string {
	if len(bankNum) <= 8 {
		return "***"
	}
	return bankNum[:4] + "***" + bankNum[len(bankNum)-4:]
}

// maskAccount 脱敏用户账号
func (c *bnnpay) maskAccount(account string) string {
	if len(account) < 6 {
		return account
	}
	return account[:3] + "**" + account[len(account)-3:]
}

// Bnnpay 是bnnpay支付服务的实例
var Bnnpay = new(bnnpay)

// bnnpay 结构体定义
type bnnpay struct {
	Base
}

// BnnpayConfig 配置结构
type BnnpayConfig struct {
	URL   string `json:"url"`   // 提单网关地址
	MID   string `json:"mid"`   // 商户号
	MName string `json:"mname"` // 商户名称
	Key   string `json:"key"`   // 商户密钥
	CbUrl string `json:"cburl"` // 回调地址
}

// BnnpayRechargeRequest 充值请求参数
type BnnpayRechargeRequest struct {
	IP        string `json:"ip"`        // 用户IP
	OrderID   string `json:"orderid"`   // 订单号
	MName     string `json:"mname"`     // 商户名称
	MID       string `json:"mid"`       // 商户ID
	Money     string `json:"money"`     // 金额
	Types     string `json:"types"`     // 类型 5
	ReturnURL string `json:"returnurl"` // 回调地址
	Remark    string `json:"remark"`    // 订单备注
	Country   string `json:"country"`   // 国家
	SName     string `json:"sname"`     // 用户姓名
	Sign      string `json:"sign"`      // 签名
}

// BnnpayRechargeResponse 充值响应结构
type BnnpayRechargeResponse struct {
	Code int    `json:"code"` // 1成功，0失败
	Msg  string `json:"msg"`
	Data struct {
		OrderID       string `json:"orderid"`       // 订单号
		Money         string `json:"money"`         // 金额
		DiscountMoney int    `json:"discountmoney"` // 优惠金额
		MName         string `json:"mname"`         // 商户名称
		Types         string `json:"types"`         // 类型
		BankNum       string `json:"banknum"`       // 银行卡号
		BankType      string `json:"banktype"`      // 银行类型
		BankTypeName  string `json:"banktypename"`  // 银行名称
		Remark        string `json:"remark"`        // 备注
		BankName      string `json:"bankname"`      // 持卡人姓名
		ReturnURL     string `json:"returnurl"`     // 回调地址
		CardAddress   string `json:"cardaddress"`   // 支行
	} `json:"data"`
	Sign        string `json:"sign"`        // 签名
	PageAddress string `json:"pageaddress"` // 收银台地址
}

// BnnpayCallback 回调数据结构
type BnnpayCallback struct {
	OrderID   string `json:"orderid"`   // 订单号
	MID       string `json:"mid"`       // 商户ID
	Money     string `json:"money"`     // 金额
	Types     string `json:"types"`     // 类型
	Remark    string `json:"remark"`    // 备注
	Status    string `json:"status"`    // 状态：2成功
	Date      string `json:"date"`      // 创建时间
	Sign      string `json:"sign"`      // 签名
	RealMoney string `json:"realmoney"` // 真实金额
}

// Init 初始化函数
// 初始化服务器的HTTP接口，注册充值回调接口
func (c *bnnpay) Init() {
	// 注册充值回调接口
	server.Http().PostNoAuth("/api/bnnpay/recharge/callback", c.rechargeCallback)
}

// Recharge 充值接口
func (c *bnnpay) Recharge(ctx *abugo.AbuHttpContent, req *CreateOrderReq, specialAgent int) {
	errcode := 0

	// 1. 获取支付方式配置
	payMethod, err := c.getPayMethod(req.MethodId)
	if err != nil {
		logs.Error("bnnpay 充值失败: 支付方式不存在，MethodId=%v", req.MethodId)
		ctx.RespErr(errors.New("支付方式不存在"), &errcode)
		return
	}

	// 2. 验证币种是否匹配
	if err := c.ValidateSymbol(req.Symbol, payMethod.Symbol); err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	// 3. 解析支付配置
	var cfg BnnpayConfig
	if err := json.Unmarshal([]byte(payMethod.ExtraConfig), &cfg); err != nil {
		logs.Error("bnnpay 充值失败: 配置解析错误, err=%v", err)
		ctx.RespErr(errors.New("支付配置错误"), &errcode)
		return
	}

	// 4. 获取用户信息
	token := server.GetToken(ctx)
	user, err := c.getUser(token.UserId)
	if err != nil {
		logs.Error("bnnpay 充值失败: 用户不存在，UserId=%v", token.UserId)
		ctx.RespErr(errors.New("用户不存在"), &errcode)
		return
	}

	// 5. 获取汇率
	var rate, amount float64
	if user.SellerID == 26 {
		rate = 0
		amount = float64(req.Amount)
	} else {
		rate, err = c.getRechargeRate(req.Symbol, user.SellerID)
		if err != nil {
			logs.Error("bnnpay 充值失败: 获取汇率失败，Symbol=%v, err=%v", req.Symbol, err)
			ctx.RespErr(errors.New("获取汇率失败"), &errcode)
			return
		}
		amount = float64(req.Amount) / rate
	}

	// 6. 从数据库字段读取参数
	country := payMethod.Country // 从x_finance_method表的Country字段读取
	if country == "" {
		country = "china" // 默认值
	}

	// 从PayType字段读取渠道编号
	channelTypes := "5"
	if payMethod.PayType != "" {
		channelTypes = payMethod.PayType
	}

	// 验证必要字段
	if req.RealName == "" {
		logs.Error("bnnpay 充值失败: 用户姓名不能为空，UserId=%v", token.UserId)
		ctx.RespErr(errors.New("用户姓名不能为空"), &errcode)
		return
	}

	// 7. 创建充值订单
	c.createRechargeOrder(ctx, cfg, rate, user, specialAgent, token, req, amount, country, channelTypes)
}

// createRechargeOrder 创建充值订单
func (c *bnnpay) createRechargeOrder(ctx *abugo.AbuHttpContent, cfg BnnpayConfig, rate float64, user *model.XUser, specialAgent int, token *server.TokenData, reqdata *CreateOrderReq, amount float64, country string, channelTypes string) {
	// 执行事务
	server.XDb().Transaction(func(tx *xgo.XTx) error {
		errcode := 0

		// 1. 构建 PayData
		payData := map[string]interface{}{
			"PayId":            reqdata.MethodId,
			"MethodId":         reqdata.MethodId, // 支付方式ID
			"Brand":            "bnnpay",
			"Name":             "bnnpay",
			"IsRechargeActive": reqdata.IsRechargeActive,
			"RealName":         reqdata.RealName, // 用户提交的姓名
		}
		payDataBytes, _ := json.Marshal(payData)

		// 2. 创建订单记录
		OrderId, err := tx.Table("x_recharge").Insert(xgo.H{
			"SellerId":     user.SellerID,
			"ChannelId":    user.ChannelID,
			"UserId":       user.UserID,
			"Symbol":       reqdata.Symbol,
			"PayId":        reqdata.MethodId,
			"PayType":      29, // bnnpay的PayType
			"Amount":       reqdata.Amount,
			"RealAmount":   amount,
			"TransferRate": rate,
			"State":        3, // 处理中
			"CSGroup":      user.CSGroup,
			"CSId":         user.CSID,
			"SpecialAgent": specialAgent,
			"TopAgentId":   user.TopAgentID,
			"OrderType":    reqdata.OrderType,
			"PayData":      string(payDataBytes),
		})

		if err != nil {
			logs.Error("bnnpay 创建订单失败: %v", err)
			ctx.RespErr(errors.New("创建订单失败"), &errcode)
			return err
		}

		// 3. 构建请求参数
		request := BnnpayRechargeRequest{
			IP:        ctx.GetIp(),
			OrderID:   fmt.Sprintf("%d", OrderId),
			MName:     cfg.MName,
			MID:       cfg.MID,
			Money:     fmt.Sprintf("%.2f", float64(reqdata.Amount)),
			Types:     channelTypes,
			ReturnURL: fmt.Sprintf("%s/api/bnnpay/recharge/callback", cfg.CbUrl),
			Remark:    fmt.Sprintf("用户%s充值", c.maskAccount(user.Account)),
			Country:   country,
			SName:     reqdata.RealName,
		}

		// 4. 生成签名
		request.Sign = c.generateRechargeSign(request, cfg.Key)

		// 5. 发送充值请求
		response, err := c.sendRechargeRequest(cfg.URL+"/deposit", request)
		if err != nil {
			logs.Error("bnnpay 充值请求失败: OrderId=%v, err=%v", OrderId, err)
			// 检查是否是网络连接问题
			if strings.Contains(err.Error(), "connectex") || strings.Contains(err.Error(), "connection") {
				ctx.RespErr(errors.New("支付服务器连接失败，请稍后重试或联系客服"), &errcode)
			} else {
				ctx.RespErr(errors.New(fmt.Sprintf("充值请求失败: %v", err)), &errcode)
			}
			return err
		}

		// 6. 验证响应状态码
		if response.Code != 1 {
			logs.Error("bnnpay 充值失败: OrderId=%v, Code=%v, Msg=%v", OrderId, response.Code, response.Msg)
			ctx.RespErr(errors.New(fmt.Sprintf("充值失败: %s", response.Msg)), &errcode)
			return errors.New(response.Msg)
		}

		// 7. 保存三方订单ID
		if response.Data.OrderID != "" {
			tx.Table("x_recharge").Where("Id = ?", OrderId).Update(xgo.H{
				"ThirdId": response.Data.OrderID,
			})
		}

		// 8. 统一返回格式
		ctx.RespOK(xgo.H{
			"payurl":  response.PageAddress,
			"orderId": OrderId,
		})

		return nil // 事务提交
	})
}

// generateRechargeSign 生成充值签名
// 签名公式: md5(商户秘钥+mid+orderid+money+returnurl+remark+types+country)
func (c *bnnpay) generateRechargeSign(req BnnpayRechargeRequest, key string) string {
	signStr := key + req.MID + req.OrderID + req.Money + req.ReturnURL + req.Remark + req.Types + req.Country
	hash := md5.Sum([]byte(signStr))
	return fmt.Sprintf("%x", hash)
}

// generateCallbackSign 生成回调签名
// 签名公式: md5(商户秘钥+mid+orderid+money+remark+types+status+date)
// 注意：date只取时分秒验签，例如：2022-01-11 19:35:01 只取19:35:01
func (c *bnnpay) generateCallbackSign(callback BnnpayCallback, cfg BnnpayConfig) string {
	// 提取时分秒
	dateTime := callback.Date
	timePart := ""
	if len(dateTime) >= 19 { // 确保格式为 YYYY-MM-DD HH:MM:SS
		timePart = dateTime[11:19] // 提取 HH:MM:SS 部分
	} else {
		timePart = dateTime // 如果格式不对，使用原始值
	}

	// 回调签名验证：md5(商户秘钥+mid+orderid+money+remark+types+status+date)
	signStr := cfg.Key + cfg.MID + callback.OrderID + callback.Money + callback.Remark + callback.Types + callback.Status + timePart

	hash := md5.Sum([]byte(signStr))
	expectedSign := fmt.Sprintf("%x", hash)

	return expectedSign
}

// verifyRechargeResponseSign 验证充值响应签名
// 签名公式: md5(key+mid+orderid+money+returnurl+remark+types+banknum+bankname+banktype)
func (c *bnnpay) verifyRechargeResponseSign(response BnnpayRechargeResponse, cfg BnnpayConfig) bool {
	// 按照文档要求的完整签名验证
	signStr := cfg.Key + cfg.MID + response.Data.OrderID + response.Data.Money + response.Data.ReturnURL + response.Data.Remark + response.Data.Types + response.Data.BankNum + response.Data.BankName + response.Data.BankType

	hash := md5.Sum([]byte(signStr))
	expectedSign := fmt.Sprintf("%x", hash)

	isValid := expectedSign == response.Sign
	if !isValid {
		logs.Error("bnnpay 响应签名验证失败: OrderId=%v", response.Data.OrderID)
	}

	return isValid
}

// sendRechargeRequest 发送充值请求
func (c *bnnpay) sendRechargeRequest(apiURL string, req BnnpayRechargeRequest) (*BnnpayRechargeResponse, error) {
	// 构建POST参数
	data := url.Values{}
	data.Set("ip", req.IP)
	data.Set("orderid", req.OrderID)
	data.Set("mname", req.MName)
	data.Set("mid", req.MID)
	data.Set("money", req.Money)
	data.Set("types", req.Types)
	data.Set("returnurl", req.ReturnURL)
	data.Set("remark", req.Remark)
	data.Set("country", req.Country)
	data.Set("sname", req.SName)
	data.Set("sign", req.Sign)

	// 发送HTTP请求
	resp, err := http.PostForm(apiURL, data)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON响应
	var response BnnpayRechargeResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// rechargeCallback 充值回调处理
func (c *bnnpay) rechargeCallback(ctx *abugo.AbuHttpContent) {
	// 读取原始请求体（用于重新构造）
	body, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		logs.Error("bnnpay 回调读取请求体失败: %v", err)
		ctx.Gin().String(500, "FAIL")
		return
	}

	// 重新构造请求体
	ctx.Gin().Request.Body = io.NopCloser(strings.NewReader(string(body)))

	// 根据Content-Type选择不同的解析方式
	contentType := ctx.Gin().Request.Header.Get("Content-Type")
	if strings.Contains(contentType, "multipart/form-data") {
		// 解析multipart/form-data格式
		err := ctx.Gin().Request.ParseMultipartForm(32 << 20) // 32MB
		if err != nil {
			logs.Error("bnnpay 回调解析multipart表单失败: %v", err)
			ctx.Gin().String(500, "FAIL")
			return
		}
	} else {
		// 解析普通表单参数（POST请求）
		err := ctx.Gin().Request.ParseForm()
		if err != nil {
			logs.Error("bnnpay 回调解析表单失败: %v", err)
			ctx.Gin().String(500, "FAIL")
			return
		}
	}

	// 尝试多种方式获取参数
	getParam := func(key string) string {
		// 优先从表单获取
		if val := ctx.Gin().Request.FormValue(key); val != "" {
			return val
		}
		// 从multipart表单获取
		if ctx.Gin().Request.MultipartForm != nil && ctx.Gin().Request.MultipartForm.Value != nil {
			if vals, ok := ctx.Gin().Request.MultipartForm.Value[key]; ok && len(vals) > 0 {
				return vals[0]
			}
		}
		// 其次从URL参数获取
		if val := ctx.Gin().Request.URL.Query().Get(key); val != "" {
			return val
		}
		// 最后从Gin的参数获取
		return ctx.Gin().Query(key)
	}

	// 构建回调数据结构
	callback := BnnpayCallback{
		OrderID:   getParam("orderid"),
		MID:       getParam("mid"),
		Money:     getParam("money"),
		Types:     getParam("types"),
		Remark:    getParam("remark"),
		Status:    getParam("status"),
		Date:      getParam("date"),
		Sign:      getParam("sign"),
		RealMoney: getParam("realmoney"),
	}

	// 验证必要参数
	if callback.OrderID == "" || callback.MID == "" || callback.Money == "" || callback.Status == "" || callback.Sign == "" {
		logs.Error("bnnpay 回调参数不完整: OrderID=%v", callback.OrderID)
		ctx.Gin().String(500, "FAIL")
		return
	}

	var order *xgo.XMap
	responseReturned := false

	// 使用defer recover防止panic，确保总是返回响应
	defer func() {
		if r := recover(); r != nil {
			logs.Error("bnnpay 订单查询发生panic: %v", r)
			if !responseReturned {
				ctx.Gin().String(500, "FAIL")
			}
		}
	}()

	// 查询订单
	if orderIdInt, parseErr := strconv.ParseInt(callback.OrderID, 10, 64); parseErr == nil {
		// 使用pay24相同的查询方式，避免类型转换问题
		where := abugo.AbuDbWhere{}
		where.Add("and", "Id", "=", orderIdInt, "")
		orderdata, err := server.Db().Table("x_recharge").Where(where).GetOne()

		if err != nil {
			logs.Error("bnnpay 数据库查询错误: OrderID=%v, err=%v", orderIdInt, err)
		} else if orderdata != nil {
			orderId := xgo.ToInt((*orderdata)["Id"])
			userId := xgo.ToInt((*orderdata)["UserId"])
			state := xgo.ToInt((*orderdata)["State"])
			amount := xgo.ToFloat((*orderdata)["Amount"])
			thirdId := xgo.ToString((*orderdata)["ThirdId"])
			payId := xgo.ToInt((*orderdata)["PayId"])

			// 将结果转换为后续代码需要的格式
			order = &xgo.XMap{}
			order.Set("Id", orderId)
			order.Set("UserId", userId)
			order.Set("State", state)
			order.Set("Amount", amount)
			order.Set("ThirdId", thirdId)
			order.Set("PayId", payId)
		}
	} else {
		logs.Error("bnnpay OrderID无法转换为数字: %v, err=%v", callback.OrderID, parseErr)
	}

	if err != nil || order == nil {
		logs.Warn("bnnpay 回调: 订单不存在，OrderID=%v", callback.OrderID)
		responseReturned = true
		ctx.Gin().String(200, "ok") // 返回ok停止重试
		return
	}

	// 获取支付配置
	paymethod, err := server.XDb().Table("x_finance_method").Where("Id = ?", order.Int("PayId")).First()
	if err != nil || paymethod == nil {
		logs.Warn("bnnpay 回调: 支付方式不存在，PayId=%v", order.Int("PayId"))
		responseReturned = true
		ctx.Gin().String(200, "ok") // 返回ok停止重试
		return
	}

	var cfg BnnpayConfig
	if err := json.Unmarshal([]byte(paymethod.String("ExtraConfig")), &cfg); err != nil {
		logs.Warn("bnnpay 回调: 配置解析错误, err=%v", err)
		responseReturned = true
		ctx.Gin().String(200, "ok") // 返回ok停止重试
		return
	}

	// 验证MID是否匹配
	if callback.MID != cfg.MID {
		logs.Error("bnnpay 回调MID不匹配: OrderID=%v", callback.OrderID)
		ctx.Gin().String(500, "FAIL")
		return
	}

	// 验证签名
	expectedSign := c.generateCallbackSign(callback, cfg)
	if expectedSign != callback.Sign {
		logs.Error("bnnpay 回调签名验证失败: OrderID=%v", callback.OrderID)
		ctx.Gin().String(500, "FAIL")
		return
	}

	// 检查订单状态
	if order.Int("State") == 2 {
		responseReturned = true
		ctx.Gin().String(200, "ok")
		return
	}

	// 验证回调金额与订单金额是否一致
	callbackMoney, err := strconv.ParseFloat(callback.Money, 64)
	if err != nil {
		logs.Error("bnnpay 回调金额解析失败: OrderID=%v", callback.OrderID)
		ctx.Gin().String(500, "FAIL")
		return
	}

	// 金额验证
	orderAmount := order.Float64("Amount")
	if math.Abs(orderAmount-callbackMoney) > 0.01 {
		logs.Error("bnnpay 回调金额不匹配: OrderID=%v", callback.OrderID)
		ctx.Gin().String(500, "FAIL")
		return
	}

	// 如果存在RealMoney字段，也需要验证真实金额
	if callback.RealMoney != "" {
		realMoney, err := strconv.ParseFloat(callback.RealMoney, 64)
		if err != nil {
			logs.Error("bnnpay 回调真实金额解析失败: OrderID=%v", callback.OrderID)
			ctx.Gin().String(500, "FAIL")
			return
		}

		if math.Abs(orderAmount-realMoney) > 0.01 {
			logs.Error("bnnpay 回调真实金额不匹配: OrderID=%v", callback.OrderID)
			ctx.Gin().String(500, "FAIL")
			return
		}
	}

	// 处理回调
	if callback.Status == "2" {
		// 充值成功，调用统一的回调处理方法
		c.rechargeCallbackHandel(order.Int("UserId"), order.Int("Id"), 5)
		logs.Info("bnnpay 充值成功: OrderID=%v, UserId=%v", callback.OrderID, order.Int("UserId"))
	} else {
		// 充值失败
		c.rechargeCallbackHandel(order.Int("UserId"), order.Int("Id"), 7)
		logs.Info("bnnpay 充值失败: OrderID=%v, Status=%v", callback.OrderID, callback.Status)
	}

	responseReturned = true
	ctx.Gin().String(200, "ok")
}
