package third

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/zhms/xgo/xgo"
)

func NewJDBService(url, currency, dc, iv, key, parent string, fc func(int) error) *JDBService {
	return &JDBService{
		Url:                   url,
		Currency:              currency,
		BrandName:             "spribe",
		Dc:                    dc,
		Iv:                    iv,
		Key:                   key,
		Parent:                parent,
		OrderType:             "quwei",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

type JDBService struct {
	Url                   string
	BrandName             string
	Currency              string
	Dc                    string
	Iv                    string
	Key                   string
	Parent                string
	OrderType             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

func (e *JDBService) JDB_Login(ctx *abugo.AbuHttpContent) {
	//https://doc.jdb711.com/cn/seamless/providedFunctions/functions/action21
	type RequestData struct {
		Lang  string
		GType int //游戏类型
		MType int //机台类型:若需直接进入游戏，需同时带入 mType 及 gType
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("JDB_Login:", err)
		return
	}

	token := server.GetToken(ctx)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", token.UserId, nil)
	data, _ := server.Db().Table("x_user").Select("IsTest").Where(where).GetOne()
	if data == nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	istest := abugo.GetInt64FromInterface((*data)["IsTest"])
	if istest == 1 {
		ctx.RespErrString(true, &errcode, "该账号禁止进入")
		return
	}

	rediskey := fmt.Sprintf("%v:%v:third_login_%v", server.Project(), server.Module(), token.UserId)
	username := strconv.Itoa(token.UserId)
	lck := server.Redis().SetNxString(rediskey, "1", 5)
	if lck != nil {
		errcode = 1000001
		ctx.RespErrString(true, &errcode, "操作频繁,请稍后再试")
		return
	}
	defer server.Redis().Del(rediskey)

	// 检查游戏入口权限 gamelist表无游戏列表 所以查询不到数据
	//gameId := strconv.Itoa(token.UserId)
	//allowed, hint, err := base.BeforeEnterGameId(token.UserId, e.BrandName, gameId)
	//if err != nil {
	//	logs.Error(e.BrandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", gameId, " err=", err.Error())
	//	ctx.RespErrString(true, &errcode, "系统错误")
	//	return
	//}
	//if !allowed {
	//	// 如果不允许进入，返回提示信息
	//	ctx.RespErrString(true, &errcode, hint)
	//	return
	//}

	balance := e.UserAmount(token.UserId)
	postData := `{"action":21,"ts":` + strconv.Itoa(int(time.Now().UnixNano()/1000000)) + `,"parent":"` + e.Parent + `","uid":"` + username + `","balance":` + fmt.Sprintf("%f", balance) + `,"lang":"` + reqdata.Lang + `","windowMode":"2","gType":"` + strconv.Itoa(reqdata.GType) + `","mType":"` + strconv.Itoa(reqdata.MType) + `"}`
	d := []byte(postData)
	encbyte, _ := e.AesEncrypt([]byte(d), []byte(e.Key), []byte(e.Iv))
	payload := strings.NewReader("dc=" + string(e.Dc) + "&x=" + encbyte)
	req, _ := http.NewRequest("POST", e.Url+`/apiRequest.do`, payload)
	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("cache-control", "no-cache")
	res, _ := http.DefaultClient.Do(req)
	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	jdata := map[string]interface{}{}
	err = json.Unmarshal(body, &jdata)
	if err != nil {
		logs.Error("JDB_Login:", err, string(body))
		return
	}

	if jdata["status"] != "0000" {
		logs.Error("JDB_Login:", err, string(body))
		logs.Debug("JDB Login data", postData)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	ctx.Put("url", jdata["path"])
	ctx.RespOK()
}

func (e *JDBService) UserAmount(UserId int) float64 {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Select("Amount").Where(where).GetOne()
	balance := float64(0)
	if udata != nil {
		balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	}
	return balance
}

func (e *JDBService) AesEncrypt(plaintext []byte, key []byte, iv []byte) (string, error) {

	block, err := aes.NewCipher(key)
	if err != nil {
		fmt.Println("err=", err)
		return "", errors.New("invalid decrypt key")
	}
	blockSize := block.BlockSize()
	plaintext = e.AesZeroPadding(plaintext, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv)
	ciphertext := make([]byte, len(plaintext))
	blockMode.CryptBlocks(ciphertext, plaintext)
	encryptString := base64.RawURLEncoding.EncodeToString([]byte(ciphertext))
	return encryptString, nil
}

func (e *JDBService) AesZeroPadding(cipherText []byte, blockSize int) []byte {
	length := len(cipherText)
	if length%blockSize == 0 {
		return cipherText
	} else {
		padding := blockSize - len(cipherText)%blockSize
		paddingText := bytes.Repeat([]byte{byte(0)}, padding)
		return append(cipherText, paddingText...)
	}
}

func (e *JDBService) AesDecrypt(cipherText string, key []byte, iv []byte) (string, error) {
	decode_data, err := base64.RawURLEncoding.DecodeString(cipherText)
	if err != nil {
		return "", errors.New("invalid decrypt data")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		fmt.Println("err=", err)
		return "", errors.New("invalid decrypt key")
	}
	blockModel := cipher.NewCBCDecrypter(block, iv)
	plainText := make([]byte, len(decode_data))
	blockModel.CryptBlocks(plainText, decode_data)
	return string(bytes.TrimRight(plainText, string([]byte{0}))), nil
}

func (e *JDBService) JDB_apiRequest(ctx *abugo.AbuHttpContent) {
	x := ctx.Gin().PostForm("x")
	decryptData, _ := e.AesDecrypt(x, []byte(e.Key), []byte(e.Iv))
	jdata := map[string]interface{}{}
	err := json.Unmarshal([]byte(decryptData), &jdata)
	if err != nil {
		logs.Error("JDB_apiRequest:", err, decryptData)
		return
	}
	abugo.LogInfo("JDB_apiRequest", jdata)
	action := int64(jdata["action"].(float64))
	switch action {
	case 6:
		e.JDB_Balance(ctx, jdata)
	case 9:
		e.JDB_Bet(ctx, jdata)
	case 11:
		e.JDB_NoBet(ctx, jdata)
	case 10:
		e.JDB_End(ctx, jdata)
	default:
		ctx.Gin().JSON(200, gin.H{
			"status":   "0000",
			"err_text": "",
		})
	}
}

func (e *JDBService) JDB_Balance(ctx *abugo.AbuHttpContent, jdata map[string]interface{}) {
	userId := jdata["uid"].(string)
	u, _ := strconv.Atoi(userId)
	bal := e.UserAmount(u)

	ctx.Gin().JSON(200, gin.H{
		"status":   "0000",
		"balance":  bal,
		"err_text": "",
	})
}

func (e *JDBService) JDB_Bet(ctx *abugo.AbuHttpContent, jdata map[string]interface{}) {
	type RequestData struct {
		TransferId     int64   `validate:"required"`
		Uid            string  `validate:"required"`
		Amount         float64 `validate:"required"`
		GameRoundSeqNo string  `validate:"required"`
		MType          float64 `validate:"required"`
	}
	userId := jdata["uid"].(string)
	u, _ := strconv.Atoi(userId)
	amount := jdata["amount"].(float64)
	transferId := strconv.Itoa(int(jdata["transferId"].(float64)))
	// currency := jdata["currency"].(string)
	mType := int64(jdata["mType"].(float64))
	//
	//// 检查游戏权限 jdb gamelist 少数据
	//gameId := strconv.Itoa(int(mType))
	//if allowed, hint, err := base.BeforeEnterGameId(u, e.BrandName, gameId); err != nil {
	//	logs.Error("JDB JDB_Bet 权限检查错误 userId=", u, " gameId=", gameId, " err=", err.Error())
	//	ctx.Gin().JSON(200, gin.H{
	//		"status":   "6001",
	//		"balance":  0,
	//		"err_text": "用户不存在",
	//	})
	//	return
	//} else if !allowed {
	//	logs.Error("JDB JDB_Bet 权限被拒绝 userId=", u, " gameId=", gameId, " hint=", hint)
	//	ctx.Gin().JSON(200, gin.H{
	//		"status":   "6001",
	//		"balance":  0,
	//		"err_text": "用户不存在",
	//	})
	//	return
	//}

	bal := e.UserAmount(u)

	if amount > bal {
		ctx.Gin().JSON(200, gin.H{
			"status":   "6006",
			"balance":  bal,
			"err_text": "",
		})
		return
	}

	server.Db().Conn().Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", amount, u, amount)

	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", u, nil)
	udata, _ := server.Db().Table("x_user").Where(where).GetOne()
	balance := abugo.GetFloat64FromInterface((*udata)["Amount"])

	var gameMapping = map[string]string{
		"22001": "飞行员",
		"22002": "骰子",
		"22003": "射门",
		"22004": "狂欢节",
		"22005": "地雷",
		"22006": "高低",
		"22007": "快乐彩",
		"22008": "迷你轮盘赌",
		"22009": "热线",
	}

	ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)

	order := xgo.H{
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
		"BetChannelId": ChannelId,
		"UserId":       u,
		"Brand":        e.BrandName,
		"ThirdId":      transferId,
		"GameId":       strconv.Itoa(int(mType)),
		"GameName":     "",
		"BetAmount":    amount,
		"WinAmount":    0,
		"ValidBet":     0,
		"ThirdTime":    time.Now().Format("2006-01-02 15:04:05"),
		"Currency":     e.Currency,
		"RawData":      "",
		"DataState":    -1,
	}

	if chineseName, ok := gameMapping[strconv.Itoa(int(mType))]; ok {
		order["GameName"] = chineseName
	} else {
		order["GameName"] = ""
	}

	server.Db().Table("x_third_" + e.OrderType + "_pre_order").Insert(order)

	amountLog := xgo.H{
		"UserId":       u,
		"BeforeAmount": balance + float64(amount),
		"Amount":       0 - amount,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonJDBBet,
		"Memo":         e.BrandName + " bet,thirdId:" + transferId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)

	// 推送投注事件
	gameName := ""
	if chineseName, ok := gameMapping[strconv.Itoa(int(mType))]; ok {
		gameName = chineseName
	} else {
		gameName = e.BrandName
	}
	ctx.Gin().JSON(200, gin.H{
		"status":   "0000",
		"balance":  balance,
		"err_text": "",
	})

	if e.thirdGamePush != nil {
		e.thirdGamePush.PushBetEvent(u, gameName, e.BrandName, amount, e.Currency, e.BrandName, transferId, 3)
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][jdb] JDB_Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][jdb] JDB_Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(u)
}

func (e *JDBService) JDB_NoBet(ctx *abugo.AbuHttpContent, jdata map[string]interface{}) {
	userId := jdata["uid"].(string)
	u, _ := strconv.Atoi(userId)
	amount := jdata["amount"].(float64)
	transferId := strconv.Itoa(int(jdata["transferId"].(float64)))
	refTransferIds := jdata["refTransferIds"].([]interface{})
	balance := float64(0)
	{
		where := abugo.AbuDbWhere{}
		where.Add("and", "UserId", "=", u, nil)
		udata, _ := server.Db().Table("x_user").Where(where).GetOne()
		if udata != nil {
			balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
		} else {
			refTransferIdsStr := "["
			for _, v := range refTransferIds {
				if reflect.TypeOf(v).String() == "float64" {
					refTransferIdsStr += fmt.Sprintf("%d ", int64(v.(float64)))
				}
			}
			refTransferIdsStr += "]"
			logs.Error("[Error][jdb] JDB_NoBet 撤销下注 用户不存在 transferId=", transferId, " refTransferIds=", refTransferIdsStr)
			ctx.Gin().JSON(200, gin.H{
				"status":   "9999",
				"balance":  0,
				"err_text": "用户不存在",
			})
			return
		}
	}
	totalAmount := float64(0)

	for _, v := range refTransferIds {
		if reflect.TypeOf(v).String() == "float64" {
			refTransferId := fmt.Sprintf("%d", int64(v.(float64)))

			where := abugo.AbuDbWhere{}
			where.Add("and", "Brand", "=", e.BrandName, nil)
			where.Add("and", "ThirdId", "=", refTransferId, nil)
			where.Add("and", "UserId", "=", u, nil)
			betTran, _ := server.Db().Table("x_third_" + e.OrderType + "_pre_order").Where(where).GetOne()
			BetAmount := float64(0)
			if betTran != nil && abugo.GetInt64FromInterface((*betTran)["DataState"]) == -1 {
				BetAmount = abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
				totalAmount += BetAmount
				logs.Info("[Info][jdb] JDB_NoBet 撤销下注 订单存在 betTran=", betTran)
				server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", BetAmount, u)
				server.Db().Conn().Exec("update x_third_"+e.OrderType+"_pre_order set ValidBet = 0,DataState=-2 where ThirdId = ?", refTransferId)
			} else {
				if betTran == nil {
					logs.Error("[Error][jdb] JDB_NoBet 撤销下注 订单不存在 transferId=", transferId, " refTransferId=", refTransferId)
				} else {
					logs.Error("[Error][jdb] JDB_NoBet 撤销下注 订单已处理 betTran=", betTran, " transferId=", transferId, " refTransferId=", refTransferId)
				}
				continue
			}

			where = abugo.AbuDbWhere{}
			where.Add("and", "UserId", "=", u, nil)
			udata, _ := server.Db().Table("x_user").Where(where).GetOne()
			balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
			if betTran != nil && abugo.GetInt64FromInterface((*betTran)["DataState"]) == -1 {
				amountLog := xgo.H{
					"UserId":       u,
					"BeforeAmount": balance - BetAmount,
					"Amount":       BetAmount,
					"AfterAmount":  balance,
					"Reason":       utils.BalanceCReasonJDBCancel,
					"Memo":         e.BrandName + " cancel,thirdId:" + refTransferId,
					"SellerId":     (*udata)["SellerId"],
					"ChannelId":    (*udata)["ChannelId"],
				}
				server.Db().Table("x_amount_change_log").Insert(amountLog)
			}
		}
	}

	if amount != totalAmount {
		logs.Error("[Error][jdb] JDB_NoBet 撤销下注 金额不对 transferId=", transferId, " amount=", amount, " totalAmount=", totalAmount)
	}

	ctx.Gin().JSON(200, gin.H{
		"status":   "0000",
		"balance":  balance,
		"err_text": "",
	})
	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][jdb] JDB_NoBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][jdb] JDB_NoBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(u)
}

func (e *JDBService) JDB_End(ctx *abugo.AbuHttpContent, jdata map[string]interface{}) {
	userId := jdata["uid"].(string)
	u, _ := strconv.Atoi(userId)
	bet := jdata["bet"].(float64)
	amount := jdata["amount"].(float64)
	historyId := jdata["historyId"].(string)
	// transferId := strconv.Itoa(int(jdata["transferId"].(float64)))
	refTransferIds := jdata["refTransferIds"].([]interface{})

	if len(refTransferIds) < 1 {
		ctx.Gin().JSON(200, gin.H{
			"status":   "9999",
			"err_text": "refTransferIds EMPTY",
		})
		return
	}
	transferId := strconv.Itoa(int(refTransferIds[0].(float64)))

	where := abugo.AbuDbWhere{}
	where.Add("and", "Brand", "=", e.BrandName, nil)
	where.Add("and", "ThirdId", "=", transferId, nil)
	udata, err := server.Db().Table("x_third_" + e.OrderType + "_pre_order").Where(where).GetOne()
	if err != nil || udata == nil {
		logs.Error("JDB_End:", err, jdata)
		ctx.Gin().JSON(200, gin.H{
			"status":   "9999",
			"err_text": "refTransferIds mapping faild",
		})
		return
	}

	// 加密游戏取输赢绝对值 当输赢绝对值大于下注金额时，取下注金额
	validBet := math.Abs(bet - amount)
	if validBet > bet {
		validBet = math.Abs(bet)
	}
	server.Db().Conn().Exec("update x_user set amount = amount + ? where UserId = ?", amount, u)
	server.Db().Conn().Exec("update x_third_"+e.OrderType+"_pre_order set WinAmount = ?, ValidBet = ?, DataState = 1 where ThirdId = ?", amount, validBet, transferId)

	betTranData := *udata
	delete(betTranData, "Id")
	delete(betTranData, "CreateTime")
	betTranData["ThirdId"] = historyId
	betTranData["WinAmount"] = amount
	betTranData["ValidBet"] = validBet
	betTranData["DataState"] = 1

	server.Db().Table("x_third_" + e.OrderType).Insert(betTranData)

	where = abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", u, nil)
	udata, _ = server.Db().Table("x_user").Where(where).GetOne()
	balance := abugo.GetFloat64FromInterface((*udata)["Amount"])

	amountLog := xgo.H{
		"UserId":       u,
		"BeforeAmount": balance - amount,
		"Amount":       amount,
		"AfterAmount":  balance,
		"Reason":       utils.BalanceCReasonJDBWin,
		"Memo":         e.BrandName + " settle,thirdId:" + historyId,
		"SellerId":     (*udata)["SellerId"],
		"ChannelId":    (*udata)["ChannelId"],
	}
	server.Db().Table("x_amount_change_log").Insert(amountLog)
	ctx.Gin().JSON(200, gin.H{
		"status":   "0000",
		"balance":  balance,
		"err_text": "",
	})
	// 推送派奖事件
	if e.thirdGamePush != nil {
		//gameName := abugo.GetStringFromInterface(betTranData["GameName"])
		//e.thirdGamePush.PushRewardEvent(u, gameName, e.BrandName, bet, amount, e.Currency)
		// 推送派奖事件
		e.thirdGamePush.PushRewardEvent(3, e.BrandName, transferId)
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if e.RefreshUserAmountFunc != nil {
			tmpErr := e.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][jdb] JDB_End 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][jdb] JDB_End 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(u)
}
