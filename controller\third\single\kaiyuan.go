package single

import (
	"bytes"
	"crypto/aes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// 开元单一钱包类

type KaiyuanSingleService struct {
	url                   string
	recordUrl             string
	agentId               string
	aesKey                string
	md5Key                string
	currency              string
	brandName             string
	RefreshUserAmountFunc func(int) error
	balanceCheckInterval  time.Duration
	thirdGamePush         *base.ThirdGamePush
}

func NewKaiyuanSingleService(params map[string]string, fc func(int) error) *KaiyuanSingleService {
	service := &KaiyuanSingleService{
		url:                   params["url"],
		recordUrl:             params["record_url"],
		agentId:               params["agent_id"],
		aesKey:                params["aes_key"],
		md5Key:                params["md5_key"],
		currency:              params["currency"],
		brandName:             "kaiyuan",
		RefreshUserAmountFunc: fc,
		balanceCheckInterval:  time.Minute * 5,
		thirdGamePush:         base.NewThirdGamePush(),
	}

	// 启动定期余额检查
	go service.startBalanceChecker()
	return service
}

const cacheKeyKaiyuan = "cacheKeyKaiyuan:"

// Kaiyuan返回错误码
const (
	Kaiyuan_Code_Success                            = 0  // 0 成功 Success
	Kaiyuan_Code_Fail_Not_Enough_Balance            = 1  // 1 余额不足 Insufficient balance
	Kaiyuan_Code_Account_Does_Not_Exist             = 2  // 2 会员帐号不存在 Account does not exist
	Kaiyuan_Code_Maintains                          = 3  // 3 维护中 Maintains
	Kaiyuan_Code_Token_Valid_Error                  = 4  // 4 token 驗證错误 Token validation error
	Kaiyuan_Code_Data_Format_Error                  = 5  // 5 数据格式错误 Data format error
	Kaiyuan_Code_Decryption_Error                   = 6  // 6 解密错误 Decryption error
	Kaiyuan_Code_MD5_Key_Error                      = 7  // 7 MD5 错误 MD5 key error
	Kaiyuan_Code_Unknown_SubType_Operation          = 8  // 8 未知的操作子类型 Unknown sub-type operation
	Kaiyuan_Code_Duplication_Order                  = 9  // 9 订单编号重复 Duplicate order
	Kaiyuan_Code_Agent_Does_Not_Exist               = 10 // 10 代理不存在 Agent does not exist
	Kaiyuan_Code_Account_Under_Deposit_Or_Withdrawn = 11 // 11 账号正在上下分 Account under deposit or withdrawn
	Kaiyuan_Code_Order_Does_Not_Exist               = 12 // 12 查无订单号 Order does not exist
	Kaiyuan_Code_Internal_Server_Error              = 13 // 13 伺服器错误 Internal server error
)

// 支持的语言（中文简体和英文）
var kaiyuanSupportLang = []string{"zh_cn", "en"}

// Login 开元棋牌登陆接口
func (k *KaiyuanSingleService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   string `json:"GameId"`
		LangCode string `json:"LangCode"`
		SellerId int    `json:"SellerId"`
	}
	errcode := 0
	requestData := new(RequestData)
	err := ctx.RequestData(requestData)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	loginLang := ""
	if requestData.LangCode != "" {
		for _, v := range kaiyuanSupportLang {
			if v == requestData.LangCode {
				loginLang = requestData.LangCode
				break
			}
		}
		if loginLang == "" {
			loginLang = "zh_cn"
		}
	} else {
		loginLang = "en"
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyKaiyuan, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	// 检查游戏入口权限
	allowed, hint, err := base.BeforeEnterGameId(token.UserId, k.brandName, requestData.GameId)
	if err != nil {
		logs.Error(k.brandName, "检查游戏入口权限失败 userId=", token.UserId, " gameId=", requestData.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误")
		return
	}
	if !allowed {
		// 如果不允许进入，返回提示信息
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	userId := token.UserId
	//获取用户余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyKaiyuan)
	if err != nil {
		logs.Error("kaiyuan_single 获取玩家余额 错误  userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	loginAccount := k.userId2LoginAccount(userId)
	loginMoney := balance
	loginOrderId := fmt.Sprintf("%s%s%s", k.agentId, time.Now().Format("*****************"), loginAccount)
	loginIp := ctx.GetIp()
	loginLineCode := "0"
	loginKindId := requestData.GameId
	loginCurrency := k.currency
	srcParam := fmt.Sprintf("s=0&account=%s&money=%f&orderid=%s&ip=%s&lineCode=%s&KindID=%s&currency=%s", loginAccount, loginMoney, loginOrderId, loginIp, loginLineCode, loginKindId, loginCurrency)
	aesParam, err := k.AESEncrypt([]byte(k.aesKey), srcParam)
	if err != nil {
		logs.Error("kaiyuan_single 登录加密参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	fmt.Println("kaiyuan_single 登录加密参数", srcParam)
	urlParam := url.QueryEscape(aesParam) //url 转码

	loginTimestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	signStr := k.getSign(loginTimestamp)
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code int    `json:"code"` // 错误码 Error code
			Url  string `json:"url"`  // 游戏 URL game URL
		} `json:"d"` // 数据结果 data results
	}
	responseData := new(ResponseData)

	//agent 代理编号  timestamp 时间戳毫秒  param 参数  key md5校验码
	loginUrl := fmt.Sprintf("%s/channelHandle?agent=%s&timestamp=%s&param=%s&key=%s", k.url, k.agentId, loginTimestamp, urlParam, signStr)
	client := &http.Client{}
	req, _ := http.NewRequest(http.MethodGet, loginUrl, nil)
	res, err := client.Do(req)
	if err != nil {
		logs.Error("kaiyuan_single 登录游戏 请求错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	logs.Info("kaiyuan_single 登录游戏参数:", loginUrl)
	defer res.Body.Close()
	respBytes, err := io.ReadAll(res.Body)

	if err != nil {
		logs.Error("kaiyuan_single 登录游戏 读取响应错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	if err = json.Unmarshal(respBytes, responseData); err != nil {
		logs.Error("kaiyuan_single 登录游戏 解析响应消息体错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	if responseData.D.Code != Kaiyuan_Code_Success {
		logs.Error("kaiyuan_single 登录游戏 登录失败 错误信息==", responseData.D.Code)
		ctx.RespErrString(true, &errcode, fmt.Sprintf("%d", responseData.D.Code))
		return
	}

	resUrl := responseData.D.Url
	if loginLang != "" {
		resUrl = fmt.Sprintf("%s&lang=%s", resUrl, loginLang)
	}
	// 隐藏游戏返回按钮 &backUrl=0&jumpType=1
	resUrl = fmt.Sprintf("%s&backUrl=0&jumpType=1", resUrl)

	// 在返回游戏URL之前，确保同步用户余额
	go func(notifyUserId int) {
		// 多次尝试发送余额变动通知，确保同步成功
		maxRetries := 3
		for i := 0; i < maxRetries; i++ {
			if k.RefreshUserAmountFunc != nil {
				logs.Info("[INFO][kaiyuan_single] 登录游戏 尝试发送余额变动通知 尝试次数: %d, userId: %d", i+1, notifyUserId)

				tmpErr := k.RefreshUserAmountFunc(notifyUserId)
				if tmpErr == nil {
					logs.Info("[INFO][kaiyuan_single] 登录游戏 发送余额变动通知成功 userId: %d", notifyUserId)
					break
				}

				logs.Error("[ERROR][kaiyuan_single] 登录游戏 发送余额变动通知错误 重试次数: %d, userId: %d, err: %s",
					i+1, notifyUserId, tmpErr.Error())

				// 记录错误类型，便于后续分析
				if strings.Contains(tmpErr.Error(), "connection") {
					logs.Error("[ERROR][kaiyuan_single] 登录游戏 可能是网络连接问题 userId: %d", notifyUserId)
				} else if strings.Contains(tmpErr.Error(), "timeout") {
					logs.Error("[ERROR][kaiyuan_single] 登录游戏 可能是请求超时问题 userId: %d", notifyUserId)
				}

				// 指数退避重试策略，每次重试延迟时间增加
				retryDelay := time.Millisecond * time.Duration(100*(i+1))
				logs.Info("[INFO][kaiyuan_single] 登录游戏 将在 %v 后重试 userId: %d", retryDelay, notifyUserId)
				time.Sleep(retryDelay)
			} else {
				logs.Error("[ERROR][kaiyuan_single] 登录游戏 发送余额变动通知失败,RefreshUserAmountFunc is nil")
				break
			}
		}
	}(userId)

	ctx.RespOK(resUrl)
}

// RequestIndex 单一钱包回调入口
func (l *KaiyuanSingleService) RequestIndex(ctx *abugo.AbuHttpContent) {
	var err error
	type ResponseData struct {
		M string `json:"m"` // 主操作类型 main operation type
		S int    `json:"s"` // ⼦操作类型 suboperation type
		D struct {
			Code int `json:"code"` // 错误码 Error code
		} `json:"d"` // 数据结果 data results
	}

	responseData := ResponseData{
		M: "/channelHandle",
		S: 0,
	}

	reqUrl := ctx.Gin().Request.URL.String()
	logs.Info("kaiyuan_single 单一钱包回调入口 请求原始数据 reqUrl=", reqUrl)
	reqParamData := l.getUrlParam(ctx)
	logs.Info("kaiyuan_single 单一钱包回调入口 请求参数 reqParamData=", reqParamData)
	agentId := ""
	requestTimestamp := ""
	key := ""
	param := ""
	ok := false
	if agentId, ok = reqParamData["agent"]; !ok || agentId == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 agent是空 agentId=", agentId, " l.agentId=", l.agentId)
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if requestTimestamp, ok = reqParamData["timestamp"]; !ok || requestTimestamp == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 timestamp是空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if param, ok = reqParamData["param"]; !ok || param == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 param是空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if key, ok = reqParamData["key"]; !ok || key == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 key是空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}

	if agentId != l.agentId || agentId == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 agent不正确 agentId=", agentId, " l.agentId=", l.agentId)
		responseData.D.Code = Kaiyuan_Code_Agent_Does_Not_Exist
		ctx.RespJson(responseData)
		return
	}
	// 校验签名
	if key != l.getSign(requestTimestamp) {
		logs.Error("kaiyuan_single 单一钱包回调入口 签名校验错误 key=", key, " l.getSign=", l.getSign(requestTimestamp), " requestTimestamp=", requestTimestamp)
		responseData.D.Code = Kaiyuan_Code_Agent_Does_Not_Exist
		ctx.RespJson(responseData)
		return
	}

	dstParam, err := l.AESDecrypt([]byte(l.aesKey), param)
	if err != nil {
		logs.Error("kaiyuan_single 单一钱包回调入口 解密错误 err=", err.Error())
		responseData.D.Code = Kaiyuan_Code_Decryption_Error
		ctx.RespJson(responseData)
		return
	}

	mdata, err := l.praseParams2Map(dstParam)
	logs.Info("kaiyuan_single 单一钱包回调入口 解析参数数据 mdata=", mdata)
	if err != nil {
		logs.Error("kaiyuan_single 单一钱包回调入口 解析参数错误 err=", err.Error())
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	opType := mdata["s"]
	logs.Info("kaiyuan_single 单一钱包回调入口 操作类型 opType=", opType)
	if opType == "" {
		logs.Error("kaiyuan_single 单一钱包回调入口 操作类型为空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	logs.Info("kaiyuan_single 单一钱包回调入口 请求原始参数数据 dstParam=", dstParam)

	switch opType {
	case "1001": // 查询余额
		l.getBalance(ctx, mdata)
		return
	case "1002": // 请求下注
		l.requestBet(ctx, mdata)
		return
	case "1003": // 返还余额
		l.returnBalance(ctx, mdata)
		return
	case "1004": // 查询订单
		l.getOrderStatus(ctx, mdata)
		return
	case "1005": // 取消下注
		l.cancelBet(ctx, mdata)
		return
	default:
		logs.Error("kaiyuan_single 单一钱包回调入口 不支持的操作类型转换错误 opType=", opType)
		responseData.S, err = strconv.Atoi(opType)
		if err != nil {
			logs.Error("kaiyuan_single 单一钱包回调入口 操作类型转换错误 opType=", opType, " err=", err.Error())
		}
		responseData.D.Code = Kaiyuan_Code_Unknown_SubType_Operation
		ctx.RespJson(responseData)
		return
	}
}

// 获取玩家余额
func (l *KaiyuanSingleService) getBalance(ctx *abugo.AbuHttpContent, mdata map[string]string) {
	type RequestData struct {
		S        string `json:"s"`        // 操作子类型 1001
		Account  string `json:"account"`  // 会员帐号
		Currency string `json:"currency"` // 币种
	}
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code    int     `json:"code"`    // 错误码 Error code
			Account string  `json:"account"` // 账号
			Money   float64 `json:"money"`   // 余额
		} `json:"d"` // 数据结果 data results
	}
	responseData := ResponseData{
		S: 1001,
		M: "/channelHandle",
	}
	responseData.D.Code = Kaiyuan_Code_Success

	bodyBytes, _ := json.Marshal(mdata)
	ok := false
	requestData := RequestData{}
	if requestData.S, ok = mdata["s"]; !ok {
		logs.Error("kaiyuan_single 获取玩家余额 操作类型为空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if requestData.Account, ok = mdata["account"]; !ok {
		logs.Error("kaiyuan_single 获取玩家余额 账号为空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if requestData.Currency, ok = mdata["currency"]; !ok {
		logs.Error("kaiyuan_single 获取玩家余额 币别为空")
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}

	if requestData.S != "1001" {
		logs.Error("kaiyuan_single 获取玩家余额 操作类型不匹配 reqdata.S=", requestData.S)
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	if !strings.EqualFold(requestData.Currency, l.currency) {
		logs.Error("kaiyuan_single 获取玩家余额 币别不匹配 reqdata.Currency=", requestData.Currency, " l.currency=", l.currency)
		responseData.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(responseData)
		return
	}
	userId := l.loginAccount2UserId(requestData.Account)
	if userId <= 0 {
		logs.Error("kaiyuan_single 获取玩家余额 用户账号转换ID错误 userId=", userId, " reqdata.Account=", requestData.Account)
		responseData.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(responseData)
		return
	}

	// 获取用户余额
	userBalance := thirdGameModel.UserBalance{}
	err := server.Db().GormDao().Table("x_user").Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("kaiyuan_single 获取玩家余额 失败 userId=", userId, " err=", err.Error())
		if err == daogorm.ErrRecordNotFound {
			responseData.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		} else {
			responseData.D.Code = Kaiyuan_Code_Internal_Server_Error
		}
		ctx.RespJson(responseData)
		return
	}
	logs.Info("kaiyuan_single 获取玩家余额 成功 userId=", userId, " requestData=", string(bodyBytes), " responseData=", responseData)
	responseData.D.Account = requestData.Account
	responseData.D.Money = userBalance.Amount
	ctx.RespJson(responseData)
}

// 请求下注
func (l *KaiyuanSingleService) requestBet(ctx *abugo.AbuHttpContent, mdata map[string]string) {
	type RequestData struct {
		S        string  `json:"s"`        // 操作子类型 1002
		Account  string  `json:"account"`  // 会员帐号
		OrderId  string  `json:"orderId"`  // 订单编号
		GameNo   string  `json:"gameNo"`   // 游戏局号
		KindId   string  `json:"kindId"`   // 游戏编号
		Money    float64 `json:"money"`    // 金额
		Currency string  `json:"currency"` // 币种
	}
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code    int     `json:"code"`    // 错误码 Error code
			Account string  `json:"account"` // 账号
			Money   float64 `json:"money"`   // 余额
		} `json:"d"` // 数据结果 data results
	}
	respdata := ResponseData{
		S: 1002,
		M: "/channelHandle",
	}
	respdata.D.Code = Kaiyuan_Code_Success

	bodyBytes, _ := json.Marshal(mdata)
	var err error
	ok := false
	reqdata := RequestData{}
	if reqdata.S, ok = mdata["s"]; !ok {
		logs.Error("kaiyuan_single 请求下注 操作类型为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Account, ok = mdata["account"]; !ok {
		logs.Error("kaiyuan_single 请求下注 账号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.OrderId, ok = mdata["orderId"]; !ok {
		logs.Error("kaiyuan_single 请求下注 订单编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.GameNo, ok = mdata["gameNo"]; !ok {
		logs.Error("kaiyuan_single 请求下注 游戏局号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.KindId, ok = mdata["kindId"]; !ok {
		logs.Error("kaiyuan_single 请求下注 游戏编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if moneyStr, ok := mdata["money"]; ok {
		reqdata.Money, err = l.str2Float64(moneyStr)
		if err != nil {
			logs.Error("kaiyuan_single 请求下注 金额格式错误 moneyStr=", moneyStr)
			respdata.D.Code = Kaiyuan_Code_Data_Format_Error
			ctx.RespJson(respdata)
			return
		} else {
			if reqdata.Money < 0 {
				logs.Error("kaiyuan_single 请求下注 金额不能为负数 moneyStr=", moneyStr)
				respdata.D.Code = Kaiyuan_Code_Data_Format_Error
				ctx.RespJson(respdata)
				return
			}
		}
	} else {
		logs.Error("kaiyuan_single 请求下注 金额为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Currency, ok = mdata["currency"]; !ok {
		logs.Error("kaiyuan_single 请求下注 币别为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}

	if reqdata.S != "1002" {
		logs.Error("kaiyuan_single 请求下注 操作类型不匹配 reqdata.S=", reqdata.S)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("kaiyuan_single 请求下注 币别不匹配 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	userId := l.loginAccount2UserId(reqdata.Account)
	if userId <= 0 {
		logs.Error("kaiyuan_single 请求下注 用户账号转换ID错误 userId=", userId, " reqdata.Account=", reqdata.Account)
		respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(respdata)
		return
	}

	gameId := reqdata.KindId
	gameName := ""
	{
		gameList := thirdGameModel.GameList{}
		err = server.Db().GormDao().Table("x_game_list").Select("Brand,GameId,Name").Where("GameId = ? and Brand=?", gameId, l.brandName).First(&gameList).Error
		if err != nil {
			logs.Error("dblive_single 请求下注 获取游戏名称失败 thirdId=", reqdata.GameNo, " gameId=", gameId, " err=", err.Error())
		} else {
			gameName = gameList.Name
		}
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, gameId); err != nil {
		logs.Error("kaiyuan_single 请求下注 权限检查错误 userId=", userId, " gameId=", gameId, " err=", err.Error())
		respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(respdata)
		return
	} else if !allowed {
		logs.Error("kaiyuan_single 请求下注 权限被拒绝 userId=", userId, " gameId=", gameId, " hint=", hint)
		respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(respdata)
		return
	}

	// table := "x_third_qipai"
	tablePre := "x_third_qipai_pre_order"
	thirdId := reqdata.GameNo
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betAmount := reqdata.Money

	// 查询订单是否处理过
	{
		reqInfo := thirdGameModel.ThirdReqInfo{}
		err = server.Db().GormDao().Table("x_third_request_info").Select("Id").Where("Brand=? and ReqId=?", l.brandName, reqdata.OrderId).First(&reqInfo).Error
		if err == nil { // 已存在的订单
			if reqInfo.Code == 0 || reqInfo.Code == -2 { // 正确处理的订单 或者 撤销过的订单
				logs.Error("kaiyuan_single 请求下注 订单已处理过 reqdata.OrderId=", reqdata.OrderId)
				respdata.D.Code = Kaiyuan_Code_Duplication_Order
				ctx.RespJson(respdata)
				return
			}
		}
	}

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.OrderId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.D.Code, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if respdata.D.Code == Kaiyuan_Code_Success {
			if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
				Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
				DoUpdates: daogormclause.Assignments(map[string]interface{}{
					"ReqUrl":        thirdReqInfo.ReqUrl,
					"ReqBody":       thirdReqInfo.ReqBody,
					"RespBody":      thirdReqInfo.RespBody,
					"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
					"Code":          thirdReqInfo.Code,
				}),
			}).Create(&thirdReqInfo).Error; e != nil {
				logs.Error("dblive_single 请求下注 记录请求响应日志失败 OrderId=", reqdata.OrderId, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
			}
		} else {
			if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
				Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
				DoUpdates: daogormclause.Assignments(map[string]interface{}{
					"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
				}),
			}).Create(&thirdReqInfo).Error; e != nil {
				logs.Error("dblive_single 请求下注 记录请求响应日志失败 OrderId=", reqdata.OrderId, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
			}
		}
	}()

	// 请求下注事务处理
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("kaiyuan_single 请求下注 失败 userId=", userId, " e=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
			} else {
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			}
			return e
		}
		if userBalance.Amount < reqdata.Money {
			logs.Error("kaiyuan_single 请求下注 余额不足 userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdata.Money=", reqdata.Money)
			respdata.D.Code = Kaiyuan_Code_Fail_Not_Enough_Balance
			return errors.New("not enough balance")
		}

		betChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("kaiyuan_single 请求下注 查询订单错误 thirdId=", thirdId, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		if e != nil { // 注单不存在
			order = thirdGameModel.ThirdOrder{
				// Id: 0,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				BetChannelId: betChannelId,
				UserId:       userBalance.UserId,
				Brand:        l.brandName,
				ThirdId:      thirdId,
				GameId:       gameId,
				GameName:     gameName,
				BetAmount:    betAmount,
				WinAmount:    0,
				ValidBet:     0,
				ThirdTime:    thirdTime,
				Currency:     l.currency,
				RawData:      string(bodyBytes),
				State:        1,
				Fee:          0,
				DataState:    -1, //未开奖
				CreateTime:   thirdTime,
			}
			e = tx.Table(tablePre).Create(&order).Error
			if e != nil {
				logs.Error("kaiyuan_single 请求下注 创建订单错误 thirdId=", thirdId, " e=", e.Error())
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
				return e
			}

		} else { // 注单存在
			if order.DataState != -1 {
				logs.Error("kaiyuan_single 请求下注 订单不是未开奖状态 thirdId=", thirdId, " order.DataState=", order.DataState)
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
				return errors.New("order DataState error")
			}

			e = tx.Table(tablePre).Where("Id=? and ThirdId=? and Brand=?", order.Id, thirdId, l.brandName).Updates(map[string]interface{}{
				"BetAmount": daogorm.Expr("BetAmount + ?", betAmount),
				"RawData":   string(bodyBytes),
				"ThirdTime": thirdTime,
			}).Error
			if e != nil {
				logs.Error("kaiyuan_single 请求下注 更新订单错误 thirdId=", thirdId, " e=", e.Error())
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
				return e
			}

		}

		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, betAmount).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", betAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && betAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("kaiyuan_single 请求下注 扣款失败 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -betAmount,
			AfterAmount:  userBalance.Amount - betAmount,
			Reason:       utils.BalanceCReasonKaiYuanBet,
			Memo:         l.brandName + " bet,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("kaiyuan_single 请求下注 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		respdata.D.Money = userBalance.Amount - betAmount
		logs.Info("请求下注 扣款成功 thirdId=", thirdId, " userId=", userId, " betAmount=", betAmount, " respdata.D.Money=", respdata.D.Money)
		return nil
	})

	if err != nil {
		logs.Error("kaiyuan_single 请求下注 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 推送投注事件
		if l.thirdGamePush != nil {
			l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency, l.brandName, thirdId, 2)
		}

		// 发送余额变动通知
		go func(notifyUserId int) {
			maxRetries := 3
			for i := 0; i < maxRetries; i++ {
				if l.RefreshUserAmountFunc != nil {
					logs.Info("[INFO][kaiyuan_single] 请求下注 尝试发送余额变动通知 尝试次数: %d, userId: %d", i+1, notifyUserId)

					tmpErr := l.RefreshUserAmountFunc(notifyUserId)
					if tmpErr == nil {
						logs.Info("[INFO][kaiyuan_single] 请求下注 发送余额变动通知成功 userId: %d", notifyUserId)
						break
					}

					logs.Error("[ERROR][kaiyuan_single] 请求下注 发送余额变动通知错误 重试次数: %d, userId: %d, err: %s",
						i+1, notifyUserId, tmpErr.Error())

					// 记录错误类型，便于后续分析
					if strings.Contains(tmpErr.Error(), "connection") {
						logs.Error("[ERROR][kaiyuan_single] 请求下注 可能是网络连接问题 userId: %d", notifyUserId)
					} else if strings.Contains(tmpErr.Error(), "timeout") {
						logs.Error("[ERROR][kaiyuan_single] 请求下注 可能是请求超时问题 userId: %d", notifyUserId)
					}

					// 指数退避重试策略，每次重试延迟时间增加
					retryDelay := time.Millisecond * time.Duration(100*(i+1))
					logs.Info("[INFO][kaiyuan_single] 请求下注 将在 %v 后重试 userId: %d", retryDelay, notifyUserId)
					time.Sleep(retryDelay)
				} else {
					logs.Error("[ERROR][kaiyuan_single] 请求下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					break
				}
			}
		}(userId)
	}

	respdata.D.Account = reqdata.Account
	ctx.RespJson(respdata)
	logs.Info("kaiyuan_single 请求下注 成功 thirdId=", thirdId, " reqdata=", string(bodyBytes), " respdata=", respdata)
}

// 返还余额
func (l *KaiyuanSingleService) returnBalance(ctx *abugo.AbuHttpContent, mdata map[string]string) {
	type RequestData struct {
		S        string  `json:"s"`        // 操作子类型 1003
		Account  string  `json:"account"`  // 会员帐号
		OrderId  string  `json:"orderId"`  // 订单编号
		GameNo   string  `json:"gameNo"`   // 游戏局号
		KindId   string  `json:"kindId"`   // 游戏编号
		Money    float64 `json:"money"`    // 金额
		Currency string  `json:"currency"` // 币种
	}
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code    int     `json:"code"`    // 错误码 Error code
			Account string  `json:"account"` // 账号
			Money   float64 `json:"money"`   // 余额
		} `json:"d"` // 数据结果 data results
	}
	respdata := ResponseData{
		S: 1003,
		M: "/channelHandle",
	}
	respdata.D.Code = Kaiyuan_Code_Success

	bodyBytes, _ := json.Marshal(mdata)
	var err error
	ok := false
	reqdata := RequestData{}
	if reqdata.S, ok = mdata["s"]; !ok {
		logs.Error("kaiyuan_single 返还余额 操作类型为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Account, ok = mdata["account"]; !ok {
		logs.Error("kaiyuan_single 返还余额 账号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.OrderId, ok = mdata["orderId"]; !ok {
		logs.Error("kaiyuan_single 返还余额 订单编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.GameNo, ok = mdata["gameNo"]; !ok {
		logs.Error("kaiyuan_single 返还余额 游戏局号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.KindId, ok = mdata["kindId"]; !ok {
		logs.Error("kaiyuan_single 返还余额 游戏编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if moneyStr, ok := mdata["money"]; ok {
		reqdata.Money, err = l.str2Float64(moneyStr)
		if err != nil {
			logs.Error("kaiyuan_single 返还余额 金额格式错误 moneyStr=", moneyStr)
			respdata.D.Code = Kaiyuan_Code_Data_Format_Error
			ctx.RespJson(respdata)
			return
		} else {
			if reqdata.Money < 0 {
				logs.Error("kaiyuan_single 返还余额 金额不能为负数 moneyStr=", moneyStr)
				respdata.D.Code = Kaiyuan_Code_Data_Format_Error
				ctx.RespJson(respdata)
				return
			}
		}
	} else {
		logs.Error("kaiyuan_single 返还余额 金额为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Currency, ok = mdata["currency"]; !ok {
		logs.Error("kaiyuan_single 返还余额 币别为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}

	if reqdata.S != "1003" {
		logs.Error("kaiyuan_single 返还余额 操作类型不匹配 reqdata.S=", reqdata.S)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("kaiyuan_single 返还余额 币别不匹配 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	userId := l.loginAccount2UserId(reqdata.Account)
	if userId <= 0 {
		logs.Error("kaiyuan_single 返还余额 用户账号转换ID错误 userId=", userId, " reqdata.Account=", reqdata.Account)
		respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(respdata)
		return
	}

	// 查询订单是否处理过
	{
		reqInfo := thirdGameModel.ThirdReqInfo{}
		err = server.Db().GormDao().Table("x_third_request_info").Select("Id").Where("Brand=? and ReqId=?", l.brandName, reqdata.OrderId).First(&reqInfo).Error
		if err == nil { // 已存在的订单
			if reqInfo.Code == 0 || reqInfo.Code == -2 { // 正确处理的订单 或者 撤销过的订单
				logs.Error("kaiyuan_single 返还余额 订单已处理过 reqdata.OrderId=", reqdata.OrderId)
				respdata.D.Code = Kaiyuan_Code_Duplication_Order
				ctx.RespJson(respdata)
				return
			}
		}
	}

	// table := "x_third_qipai"
	tablePre := "x_third_qipai_pre_order"
	thirdId := reqdata.GameNo
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	winAmount := reqdata.Money

	defer func() {
		// 记录请求响应日志
		respBodyBytes, _ := json.Marshal(respdata)
		thirdReqInfo := thirdGameModel.ThirdReqInfo{
			// Id:0,
			Brand:         l.brandName,
			ReqId:         reqdata.OrderId,
			ReqUrl:        ctx.Gin().Request.URL.String(),
			ReqBody:       string(bodyBytes),
			RespBody:      string(respBodyBytes),
			RepeatReqData: "[]",
			Code:          respdata.D.Code, // 0成功 非0异常
			CreatedAt:     time.Now().Unix(),
		}
		thirdReqInfoBytes, _ := json.Marshal(thirdReqInfo)
		if respdata.D.Code == Kaiyuan_Code_Success {
			if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
				Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
				DoUpdates: daogormclause.Assignments(map[string]interface{}{
					"ReqUrl":        thirdReqInfo.ReqUrl,
					"ReqBody":       thirdReqInfo.ReqBody,
					"RespBody":      thirdReqInfo.RespBody,
					"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
					"Code":          thirdReqInfo.Code,
				}),
			}).Create(&thirdReqInfo).Error; e != nil {
				logs.Error("dblive_single 返还余额 记录请求响应日志失败 OrderId=", reqdata.OrderId, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
			}
		} else {
			if e := server.Db().GormDao().Table("x_third_request_info").Clauses(daogormclause.OnConflict{
				Columns: []daogormclause.Column{{Name: "uk_Brand_ReqId"}},
				DoUpdates: daogormclause.Assignments(map[string]interface{}{
					"RepeatReqData": daogorm.Expr("JSON_ARRAY_INSERT(x_third_request_info.RepeatReqData,'$[0]',?)", thirdReqInfoBytes),
				}),
			}).Create(&thirdReqInfo).Error; e != nil {
				logs.Error("dblive_single 返还余额 记录请求响应日志失败 OrderId=", reqdata.OrderId, " thirdReqInfo=", thirdReqInfo, " err=", e.Error())
			}
		}
	}()

	// 返还余额事务处理
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("kaiyuan_single 返还余额 失败 userId=", userId, " e=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
			} else {
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			}
			return e
		}

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			logs.Error("kaiyuan_single 返还余额 查询订单错误 thirdId=", thirdId, " e=", e.Error())
			if e != daogorm.ErrRecordNotFound {
				respdata.D.Code = Kaiyuan_Code_Order_Does_Not_Exist
			} else {
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			}
			return e
		}

		if order.DataState != -1 {
			logs.Error("kaiyuan_single 返还余额 订单不是未开奖状态 thirdId=", thirdId, " order.DataState=", order.DataState)
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return errors.New("order DataState error")
		}

		validBet := math.Abs(winAmount - order.BetAmount)
		if validBet > math.Abs(order.BetAmount) {
			validBet = math.Abs(order.BetAmount)
		}

		e = tx.Table(tablePre).Where("Id=? and ThirdId=? and Brand=?", order.Id, thirdId, l.brandName).Updates(map[string]interface{}{
			"WinAmount": winAmount,
			"ValidBet":  validBet,
			"ThirdTime": thirdTime,
			"RawData":   string(bodyBytes),
			"DataState": 3, // 3 已完成待修正状态
		}).Error
		if e != nil {
			logs.Error("kaiyuan_single 返还余额 更新注单错误 thirdId=", thirdId, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}

		// 这里不插入正式表，在worker里面修正了注单的下注额之后再插入正式表
		// order.Id = 0
		// order.WinAmount = winAmount
		// order.ValidBet = validBet
		// order.ThirdTime = thirdTime
		// order.RawData = string(bodyBytes)
		// order.DataState = 1
		// e = tx.Table(table).Create(&order).Error
		// if e != nil {
		// 	logs.Error("kaiyuan_single 返还余额 创建正式表注单错误 thirdId=", thirdId, " e=", e.Error())
		// 	respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
		// 	return e
		// }

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", winAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && winAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("kaiyuan_single 返还余额 加款失败 thirdId=", thirdId, " userId=", userId, " winAmount=", winAmount, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       winAmount,
			AfterAmount:  userBalance.Amount + winAmount,
			Reason:       utils.BalanceCReasonKaiYuanSettle,
			Memo:         l.brandName + " settle,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("kaiyuan_single 返还余额 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		respdata.D.Money = userBalance.Amount + winAmount

		return nil
	})

	if err != nil {
		logs.Error("kaiyuan_single 返还余额 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 推送派奖事件
		if l.thirdGamePush != nil {
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(2, l.brandName, thirdId)
		}

		// 发送余额变动通知，增加重试机制
		go func(notifyUserId int) {
			maxRetries := 3
			for i := 0; i < maxRetries; i++ {
				if l.RefreshUserAmountFunc != nil {
					logs.Info("[INFO][kaiyuan_single] 返还余额 尝试发送余额变动通知 尝试次数: %d, userId: %d", i+1, notifyUserId)

					tmpErr := l.RefreshUserAmountFunc(notifyUserId)
					if tmpErr == nil {
						logs.Info("[INFO][kaiyuan_single] 返还余额 发送余额变动通知成功 userId: %d", notifyUserId)
						break
					}

					logs.Error("[ERROR][kaiyuan_single] 返还余额 发送余额变动通知错误 重试次数: %d, userId: %d, err: %s",
						i+1, notifyUserId, tmpErr.Error())

					// 记录错误类型，便于后续分析
					if strings.Contains(tmpErr.Error(), "connection") {
						logs.Error("[ERROR][kaiyuan_single] 返还余额 可能是网络连接问题 userId: %d", notifyUserId)
					} else if strings.Contains(tmpErr.Error(), "timeout") {
						logs.Error("[ERROR][kaiyuan_single] 返还余额 可能是请求超时问题 userId: %d", notifyUserId)
					}

					// 指数退避重试策略，每次重试延迟时间增加
					retryDelay := time.Millisecond * time.Duration(100*(i+1))
					logs.Info("[INFO][kaiyuan_single] 返还余额 将在 %v 后重试 userId: %d", retryDelay, notifyUserId)
					time.Sleep(retryDelay)
				} else {
					logs.Error("[ERROR][kaiyuan_single] 返还余额 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					break
				}
			}

			// 当达到最大重试次数后，记录一个严重错误
			if l.RefreshUserAmountFunc != nil {
				// 最后检查一次系统和游戏余额是否一致
				gameBalance, gameErr := l.getGameBalance(notifyUserId)
				systemBalance, sysErr := l.getSystemBalance(notifyUserId)

				if gameErr != nil || sysErr != nil {
					logs.Error("[ERROR][kaiyuan_single] 返还余额 最终余额检查失败 userId: %d, gameErr: %v, sysErr: %v",
						notifyUserId, gameErr, sysErr)
				} else if math.Abs(gameBalance-systemBalance) > 0.01 {
					logs.Error("[ERROR][kaiyuan_single] 返还余额 余额不一致 userId: %d, gameBalance: %f, systemBalance: %f",
						notifyUserId, gameBalance, systemBalance)
				}
			}
		}(userId)
	}

	respdata.D.Account = reqdata.Account
	ctx.RespJson(respdata)
	logs.Info("kaiyuan_single 返还余额 成功 thirdId=", thirdId, " reqdata=", string(bodyBytes), " respdata=", respdata)
}

// 查询订单
func (l *KaiyuanSingleService) getOrderStatus(ctx *abugo.AbuHttpContent, mdata map[string]string) {
	type RequestData struct {
		S       string `json:"s"`       // 操作子类型 1004
		OrderId string `json:"orderId"` // 订单编号
	}
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code   int `json:"code"`   // 错误码 Error code
			Status int `json:"status"` // 状态(1:成功, 2:失败, 3:处理中, 4:查无订单, 5:其他错误) 当状态为 2 或 4 时，会进行重新派奬, 成功后修正订单为已处理
		} `json:"d"` // 数据结果 data results
	}
	respdata := ResponseData{
		S: 1004,
		M: "/channelHandle",
	}
	respdata.D.Code = Kaiyuan_Code_Success

	bodyBytes, _ := json.Marshal(mdata)
	var err error
	ok := false
	reqdata := RequestData{}
	if reqdata.S, ok = mdata["s"]; !ok {
		logs.Error("kaiyuan_single 查询订单 操作类型为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.OrderId, ok = mdata["orderId"]; !ok {
		logs.Error("kaiyuan_single 查询订单 订单编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}

	if reqdata.S != "1004" {
		logs.Error("kaiyuan_single 查询订单 操作类型不匹配 reqdata.S=", reqdata.S)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}

	// 查询订单是否处理过
	reqInfo := thirdGameModel.ThirdReqInfo{}
	err = server.Db().GormDao().Table("x_third_request_info").Where("Brand=? and ReqId=?", l.brandName, reqdata.OrderId).First(&reqInfo).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			respdata.D.Status = 4 // 查无订单
		} else {
			logs.Error("kaiyuan_single 查询订单 查询订单错误 reqdata.OrderId=", reqdata.OrderId, " err=", err.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
		}
	} else { // 已存在的订单
		if reqInfo.Code == 0 || reqInfo.Code == -2 {
			logs.Error("kaiyuan_single 取消下注 订单已处理过 reqdata.OrderId=", reqdata.OrderId, " Code=", reqInfo.Code)
			respdata.D.Status = 1
		} else {
			respdata.D.Status = 2
		}
	}

	logs.Info("kaiyuan_single 查询订单 成功 OrderId=", reqdata.OrderId, " reqdata=", string(bodyBytes), " respdata=", respdata)
	ctx.RespJson(respdata)
}

// 取消下注
func (l *KaiyuanSingleService) cancelBet(ctx *abugo.AbuHttpContent, mdata map[string]string) {
	type RequestData struct {
		S        string  `json:"s"`        // 操作子类型 1005
		Account  string  `json:"account"`  // 会员帐号
		OrderId  string  `json:"orderId"`  // 订单编号
		GameNo   string  `json:"gameNo"`   // 游戏局号
		KindId   string  `json:"kindId"`   // 游戏编号
		Money    float64 `json:"money"`    // 金额
		Currency string  `json:"currency"` // 币种
	}
	type ResponseData struct {
		S int    `json:"s"` // ⼦操作类型 suboperation type
		M string `json:"m"` // 主操作类型 main operation type
		D struct {
			Code   int `json:"code"`   // 错误码 Error code
			Status int `json:"status"` // 状态(1:成功, 2:失败) 已处理取消下注成功的订单与查无此订单请皆视为成功，将不再做发送
		} `json:"d"` // 数据结果 data results
	}
	respdata := ResponseData{
		S: 1005,
		M: "/channelHandle",
	}
	respdata.D.Code = Kaiyuan_Code_Success

	bodyBytes, _ := json.Marshal(mdata)
	var err error
	ok := false
	reqdata := RequestData{}
	if reqdata.S, ok = mdata["s"]; !ok {
		logs.Error("kaiyuan_single 取消下注 操作类型为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Account, ok = mdata["account"]; !ok {
		logs.Error("kaiyuan_single 取消下注 账号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.OrderId, ok = mdata["orderId"]; !ok {
		logs.Error("kaiyuan_single 取消下注 订单编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.GameNo, ok = mdata["gameNo"]; !ok {
		logs.Error("kaiyuan_single 取消下注 游戏局号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.KindId, ok = mdata["kindId"]; !ok {
		logs.Error("kaiyuan_single 取消下注 游戏编号为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if moneyStr, ok := mdata["money"]; ok {
		reqdata.Money, err = l.str2Float64(moneyStr)
		if err != nil {
			logs.Error("kaiyuan_single 取消下注 金额格式错误 moneyStr=", moneyStr)
			respdata.D.Code = Kaiyuan_Code_Data_Format_Error
			ctx.RespJson(respdata)
			return
		} else {
			if reqdata.Money < 0 {
				logs.Error("kaiyuan_single 取消下注 金额不能为负数 moneyStr=", moneyStr)
				respdata.D.Code = Kaiyuan_Code_Data_Format_Error
				ctx.RespJson(respdata)
				return
			}
		}
	} else {
		logs.Error("kaiyuan_single 取消下注 金额为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if reqdata.Currency, ok = mdata["currency"]; !ok {
		logs.Error("kaiyuan_single 取消下注 币别为空")
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}

	if reqdata.S != "1005" {
		logs.Error("kaiyuan_single 取消下注 操作类型不匹配 reqdata.S=", reqdata.S)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	if !strings.EqualFold(reqdata.Currency, l.currency) {
		logs.Error("kaiyuan_single 取消下注 币别不匹配 reqdata.Currency=", reqdata.Currency, " l.currency=", l.currency)
		respdata.D.Code = Kaiyuan_Code_Data_Format_Error
		ctx.RespJson(respdata)
		return
	}
	userId := l.loginAccount2UserId(reqdata.Account)
	if userId <= 0 {
		logs.Error("kaiyuan_single 取消下注 用户账号转换ID错误 userId=", userId, " reqdata.Account=", reqdata.Account)
		respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
		ctx.RespJson(respdata)
		return
	}

	// 查询订单是否处理过
	{
		reqInfo := thirdGameModel.ThirdReqInfo{}
		err = server.Db().GormDao().Table("x_third_request_info").Select("Id").Where("Brand=? and ReqId=?", l.brandName, reqdata.OrderId).First(&reqInfo).Error
		if err != nil {
			if err == daogorm.ErrRecordNotFound {
				logs.Info("kaiyuan_single 取消下注 成功 thirdId=", reqdata.GameNo, " orderId=", reqdata.OrderId, " reqdata=", string(bodyBytes), " respdata=", respdata)
				respdata.D.Status = 1
				ctx.RespJson(respdata)
				return
			} else {
				logs.Error("kaiyuan_single 取消下注 查询订单是否处理过错误 reqdata.OrderId=", reqdata.OrderId, " err=", err.Error())
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
				ctx.RespJson(respdata)
				return
			}
		}
		// 已存在的订单
		if reqInfo.Code != 0 { // 异常的订单 或者 已经撤销过的订单
			logs.Error("kaiyuan_single 取消下注 订单已处理过 reqdata.OrderId=", reqdata.OrderId)
			respdata.D.Status = 1
			ctx.RespJson(respdata)
			return
		}
	}

	// table := "x_third_qipai"
	tablePre := "x_third_qipai_pre_order"
	thirdId := reqdata.GameNo
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	cancelAmount := reqdata.Money

	// 取消下注事务处理
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("kaiyuan_single 取消下注 查询用户失败 userId=", userId, " e=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.D.Code = Kaiyuan_Code_Account_Does_Not_Exist
			} else {
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			}
			return e
		}

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Clauses(daogormclause.Locking{Strength: "UPDATE"}).Where("ThirdId=? and Brand=?", thirdId, l.brandName).First(&order).Error
		if e != nil {
			logs.Error("kaiyuan_single 取消下注 查询订单错误 thirdId=", thirdId, " e=", e.Error())
			if e != daogorm.ErrRecordNotFound {
				respdata.D.Status = 1 // 已处理取消下注成功的订单与查无此订单请皆视为成功，将不再做发送
				return nil
			} else {
				respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			}
			return e
		}

		if order.DataState != -1 {
			logs.Error("kaiyuan_single 取消下注 订单不是未开奖状态 thirdId=", thirdId, " order.DataState=", order.DataState)
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return errors.New("order DataState error")
		}

		// 总下注金额小于取消金额
		if order.BetAmount < cancelAmount {
			logs.Error("kaiyuan_single 取消下注 取消金额大于下注金额 thirdId=", thirdId, " order.BetAmount=", order.BetAmount, " cancelAmount=", cancelAmount)
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return errors.New("取消金额大于下注金额")
		}

		e = tx.Table(tablePre).Where("Id=? and ThirdId=? and Brand=?", order.Id, thirdId, l.brandName).Updates(map[string]interface{}{
			"BetAmount": daogorm.Expr("BetAmount - ?", cancelAmount),
			"ThirdTime": thirdTime,
			"RawData":   string(bodyBytes),
		}).Error
		if e != nil {
			logs.Error("kaiyuan_single 取消下注 更新注单错误 thirdId=", thirdId, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}

		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount + ?", cancelAmount),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && cancelAmount != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("kaiyuan_single 取消下注 加款失败 thirdId=", thirdId, " userId=", userId, " cancelAmount=", cancelAmount, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       cancelAmount,
			AfterAmount:  userBalance.Amount + cancelAmount,
			Reason:       utils.BalanceCReasonKaiYuanCancel,
			Memo:         l.brandName + " cancel,thirdId:" + thirdId + ",tid:" + reqdata.OrderId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("kaiyuan_single 取消下注 创建账变记录失败 thirdId=", thirdId, " amountLog=", amountLog, " e=", e.Error())
			respdata.D.Code = Kaiyuan_Code_Internal_Server_Error
			return e
		}
		respdata.D.Status = 1

		return nil
	})

	if err != nil {
		logs.Error("kaiyuan_single 取消下注 事务处理失败 thirdId=", thirdId, " err=", err.Error())
		ctx.RespJson(respdata)
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int) {
			maxRetries := 3
			for i := 0; i < maxRetries; i++ {
				if l.RefreshUserAmountFunc != nil {
					logs.Info("[INFO][kaiyuan_single] 取消下注 尝试发送余额变动通知 尝试次数: %d, userId: %d", i+1, notifyUserId)

					tmpErr := l.RefreshUserAmountFunc(notifyUserId)
					if tmpErr == nil {
						logs.Info("[INFO][kaiyuan_single] 取消下注 发送余额变动通知成功 userId: %d", notifyUserId)
						break
					}

					logs.Error("[ERROR][kaiyuan_single] 取消下注 发送余额变动通知错误 重试次数: %d, userId: %d, err: %s",
						i+1, notifyUserId, tmpErr.Error())

					// 记录错误类型，便于后续分析
					if strings.Contains(tmpErr.Error(), "connection") {
						logs.Error("[ERROR][kaiyuan_single] 取消下注 可能是网络连接问题 userId: %d", notifyUserId)
					} else if strings.Contains(tmpErr.Error(), "timeout") {
						logs.Error("[ERROR][kaiyuan_single] 取消下注 可能是请求超时问题 userId: %d", notifyUserId)
					}

					// 指数退避重试策略，每次重试延迟时间增加
					retryDelay := time.Millisecond * time.Duration(100*(i+1))
					logs.Info("[INFO][kaiyuan_single] 取消下注 将在 %v 后重试 userId: %d", retryDelay, notifyUserId)
					time.Sleep(retryDelay)
				} else {
					logs.Error("[ERROR][kaiyuan_single] 取消下注 发送余额变动通知失败,RefreshUserAmountFunc is nil")
					break
				}
			}
		}(userId)
	}

	ctx.RespJson(respdata)
	logs.Info("kaiyuan_single 取消下注 成功 thirdId=", thirdId, " reqdata=", string(bodyBytes), " respdata=", respdata)
	return
}

// AESEncrypt 开元棋牌 数据AES加密函数
func (l *KaiyuanSingleService) AESEncrypt(key []byte, plaintext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	plainBytes := []byte(plaintext)
	// 对于ECB模式，直接使用cipher.NewCBCEncrypter即可
	//if len(plainBytes)%aes.BlockSize != 0 {
	plainBytes = l.AESPad(plainBytes, aes.BlockSize)
	//}
	ciphertext := make([]byte, len(plainBytes))
	for start := 0; start < len(plainBytes); start += aes.BlockSize {
		block.Encrypt(ciphertext[start:start+aes.BlockSize], plainBytes[start:start+aes.BlockSize])
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// AESDecrypt 开元棋牌 数据AES解密函数
func (l *KaiyuanSingleService) AESDecrypt(key []byte, ciphertext string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext := make([]byte, len(cipherBytes))
	// 对于ECB模式，直接使用cipher.NewCBCDecrypter即可
	for start := 0; start < len(cipherBytes); start += aes.BlockSize {
		block.Decrypt(plaintext[start:start+aes.BlockSize], cipherBytes[start:start+aes.BlockSize])
	}
	return string(l.AESUnpad(plaintext)), nil
}

// AESPad 使用PKCS7填充
func (l *KaiyuanSingleService) AESPad(buf []byte, blockSize int) []byte {
	padding := blockSize - (len(buf) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(buf, padText...)
}

// AESUnpad 移除PKCS7填充
func (l *KaiyuanSingleService) AESUnpad(buf []byte) []byte {
	length := len(buf)
	if length == 0 {
		return buf
	}
	padding := int(buf[length-1])
	return buf[:length-padding]
}

// 计算签名字符串生成key 然后md5加密
func (l *KaiyuanSingleService) getSign(timestamp string) (sign string) {
	src := fmt.Sprintf("%s%s%s", l.agentId, timestamp, l.md5Key)
	sign = base.MD5(src)
	return
}

func (l *KaiyuanSingleService) getUrlParam(ctx *abugo.AbuHttpContent) (data map[string]string) {
	data = make(map[string]string, 0)
	query := ctx.Gin().Request.URL.Query()
	for k := range query {
		data[k] = ctx.Query(k)
	}
	return
}

// 解析参数
func (l *KaiyuanSingleService) praseParams2Map(udata string) (mdata map[string]string, err error) {
	mdata = make(map[string]string)
	if udata == "" {
		return
	}
	paramsArry := strings.Split(udata, "&")
	for _, v := range paramsArry {
		kv := strings.Split(v, "=")
		if len(kv) == 2 {
			mdata[strings.Trim(kv[0], " ")] = strings.Trim(kv[1], " ")
		} else {
			err = errors.New("参数错误")
			return
		}
	}
	return
}

// 获取登录账号
func (l *KaiyuanSingleService) userId2LoginAccount(userId int) (loginAccount string) {
	return fmt.Sprintf("%d", userId)
}

// 获取用户id
func (l *KaiyuanSingleService) loginAccount2UserId(loginAccount string) (userId int) {
	u, err := strconv.ParseInt(loginAccount, 10, 64)
	if err != nil {
		return
	}
	userId = int(u)
	if loginAccount != l.userId2LoginAccount(userId) {
		userId = 0
	}
	return
}

// 字符串转浮点
func (l *KaiyuanSingleService) str2Float64(str string) (v float64, err error) {
	v, _ = strconv.ParseFloat(str, 64)
	v2 := strings.Split(str, ".")
	decLen := 0
	if len(v2) == 1 {
		decLen = 0
	} else if len(v2) == 2 {
		decLen = len(v2[1])
	} else {
		err = errors.New("浮点数格式错误")
		return
	}
	vStr := fmt.Sprintf(fmt.Sprintf("%%.%df", decLen), v)
	if vStr != str {
		err = errors.New("浮点数格式错误")
	}
	return
}

// startBalanceChecker 启动定期余额检查
func (k *KaiyuanSingleService) startBalanceChecker() {
	ticker := time.NewTicker(k.balanceCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		k.checkAllActiveUsersBalance()
	}
}

// checkAllActiveUsersBalance 检查所有活跃用户的余额
func (k *KaiyuanSingleService) checkAllActiveUsersBalance() {
	// 获取最近30分钟内有游戏记录的用户
	var activeUsers []struct {
		UserId int
	}

	err := server.Db().GormDao().Table("x_third_qipai_pre_order").
		Select("DISTINCT UserId").
		Where("Brand = ? AND ThirdTime >= ?", k.brandName,
			time.Now().Add(-30*time.Minute).Format("2006-01-02 15:04:05")).
		Find(&activeUsers).Error

	if err != nil {
		logs.Error("[ERROR][kaiyuan_single] 获取活跃用户列表失败:", err)
		return
	}

	// 检查每个用户的余额
	for _, user := range activeUsers {
		go func(userId int) {
			// 获取游戏中的余额
			gameBalance, err := k.getGameBalance(userId)
			if err != nil {
				logs.Error("[ERROR][kaiyuan_single] 获取游戏余额失败 userId:", userId, " err:", err)
				return
			}

			// 获取系统中的余额
			systemBalance, err := k.getSystemBalance(userId)
			if err != nil {
				logs.Error("[ERROR][kaiyuan_single] 获取系统余额失败 userId:", userId, " err:", err)
				return
			}

			// 如果余额不一致，尝试同步
			if math.Abs(gameBalance-systemBalance) > 0.01 {
				logs.Warning("[WARN][kaiyuan_single] 用户余额不一致 userId:", userId,
					" gameBalance:", gameBalance, " systemBalance:", systemBalance)

				// 使用重试机制同步余额
				maxRetries := 3
				for i := 0; i < maxRetries; i++ {
					if k.RefreshUserAmountFunc != nil {
						if err := k.RefreshUserAmountFunc(userId); err != nil {
							logs.Error("[ERROR][kaiyuan_single] 同步用户余额失败，重试次数:", i+1,
								" userId:", userId, " err:", err)
							time.Sleep(time.Millisecond * 100)
							continue
						}
						logs.Info("[INFO][kaiyuan_single] 同步用户余额成功 userId:", userId)
						break
					} else {
						logs.Error("[ERROR][kaiyuan_single] RefreshUserAmountFunc未设置")
						break
					}
				}
			}
		}(user.UserId)
	}
}

// getGameBalance 获取用户在游戏中的余额
func (k *KaiyuanSingleService) getGameBalance(userId int) (float64, error) {
	loginAccount := k.userId2LoginAccount(userId)

	// 构造查询参数
	srcParam := fmt.Sprintf("s=1001&account=%s&currency=%s", loginAccount, k.currency)
	logs.Info("[INFO][kaiyuan_single] 获取游戏余额请求参数: %s, userId: %d", srcParam, userId)

	aesParam, err := k.AESEncrypt([]byte(k.aesKey), srcParam)
	if err != nil {
		logs.Error("[ERROR][kaiyuan_single] 加密参数错误: %v, userId: %d", err, userId)
		return 0, fmt.Errorf("加密参数错误: %v", err)
	}

	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())
	signStr := k.getSign(timestamp)

	// 构造请求URL
	reqURL := fmt.Sprintf("%s/channelHandle?agent=%s&timestamp=%s&param=%s&key=%s",
		k.url, k.agentId, timestamp, url.QueryEscape(aesParam), signStr)
	logs.Info("[INFO][kaiyuan_single] 获取游戏余额完整请求URL: %s, userId: %d", reqURL, userId)

	// 发送请求
	resp, err := http.Get(reqURL)
	if err != nil {
		logs.Error("[ERROR][kaiyuan_single] 请求游戏余额失败: %v, userId: %d", err, userId)
		return 0, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("[ERROR][kaiyuan_single] 读取响应内容失败: %v, userId: %d", err, userId)
		return 0, fmt.Errorf("读取响应内容失败: %v", err)
	}
	logs.Info("[INFO][kaiyuan_single] 获取游戏余额响应内容: %s, userId: %d", string(respBody), userId)

	// 解析响应
	var result struct {
		D struct {
			Code  int     `json:"code"`
			Money float64 `json:"money"`
		} `json:"d"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		logs.Error("[ERROR][kaiyuan_single] 解析响应失败: %v, userId: %d, resp: %s", err, userId, string(respBody))
		return 0, fmt.Errorf("解析响应失败: %v", err)
	}

	if result.D.Code != Kaiyuan_Code_Success {
		// 针对错误码8（未知操作子类型）的特殊处理
		if result.D.Code == Kaiyuan_Code_Unknown_SubType_Operation {
			logs.Error("[ERROR][kaiyuan_single] 获取游戏余额失败，操作子类型错误(错误码8)，请检查请求参数格式，userId: %d", userId)
		}
		logs.Error("[ERROR][kaiyuan_single] 获取游戏余额失败，错误码: %d, userId: %d", result.D.Code, userId)
		return 0, fmt.Errorf("获取游戏余额失败，错误码: %d", result.D.Code)
	}

	logs.Info("[INFO][kaiyuan_single] 获取游戏余额成功，余额: %f, userId: %d", result.D.Money, userId)
	return result.D.Money, nil
}

// getSystemBalance 获取用户在系统中的余额
func (k *KaiyuanSingleService) getSystemBalance(userId int) (float64, error) {
	var userBalance struct {
		Amount float64
	}

	err := server.Db().GormDao().Table("x_user").
		Select("Amount").
		Where("UserId = ?", userId).
		First(&userBalance).Error

	if err != nil {
		return 0, fmt.Errorf("查询用户余额失败: %v", err)
	}

	return userBalance.Amount, nil
}
