@echo off
chcp 65001
SET GOOS=linux
SET GOARCH=amd64
SET INPUT_PATH=clientapi.go
SET OUTPUT_NAME=clientapi

REM 使用 PowerShell 删除文件
powershell -Command "Remove-Item -Path '%OUTPUT_NAME%' -Force -ErrorAction SilentlyContinue"
git pull
REM 编译
echo 正在编译为 Linux 版本...
go build -o %OUTPUT_NAME% %INPUT_PATH%

IF %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    exit /b %ERRORLEVEL%
) ELSE (
    echo 编译成功，生成文件：%OUTPUT_NAME%
)
pause