// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXGameList = "x_game_list"

// XGameList mapped from table <x_game_list>
type XGameList struct {
	ID             int32   `gorm:"column:Id;primaryKey;autoIncrement:true;comment:Id" json:"Id"`    // Id
	Brand          string  `gorm:"column:Brand;comment:品牌" json:"Brand"`                            // 品牌
	GameID         string  `gorm:"column:GameId;comment:游戏Id" json:"GameId"`                        // 游戏Id
	Name           string  `gorm:"column:Name;comment:中文名" json:"Name"`                             // 中文名
	EName          string  `gorm:"column:EName;comment:英文名" json:"EName"`                           // 英文名
	Sort           int32   `gorm:"column:Sort;comment:排序,数字越大越靠前" json:"Sort"`                      // 排序,数字越大越靠前
	UserCount      int32   `gorm:"column:UserCount;comment:投注人数" json:"UserCount"`                  // 投注人数
	BetAmount      float64 `gorm:"column:BetAmount;default:0.000000;comment:投注金额" json:"BetAmount"` // 投注金额
	Rtp            float64 `gorm:"column:Rtp;default:0.000000;comment:rtp回报率" json:"Rtp"`           // rtp回报率
	IsNew          int32   `gorm:"column:IsNew;default:2;comment:新游戏 1是,2不是" json:"IsNew"`          // 新游戏 1是,2不是
	IsHot          int32   `gorm:"column:IsHot;default:2;comment:热门游戏 1是,2不是" json:"IsHot"`         // 热门游戏 1是,2不是
	State          int32   `gorm:"column:State;default:2;comment:状态 1启用,2禁用" json:"State"`          // 状态 1启用,2禁用
	OpenState      int32   `gorm:"column:OpenState;default:2;comment:状态 1启用,2禁用" json:"OpenState"`  // 状态 1启用,2禁用
	LiuSui         int32   `gorm:"column:LiuSui;default:1;comment:1算流水,2不算流水" json:"LiuSui"`        // 1算流水,2不算流水
	Icon           string  `gorm:"column:Icon" json:"Icon"`
	EIcon          string  `gorm:"column:EIcon" json:"EIcon"`
	GameType       int32   `gorm:"column:GameType;default:1;comment:1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育" json:"GameType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
	IsRecom        int32   `gorm:"column:IsRecom;default:2;comment:是否是推荐" json:"IsRecom"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // 是否是推荐
	HubType        int32   `gorm:"column:HubType;comment:聚合类型 0非聚合 1hub88 4gfg聚合" json:"HubType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 聚合类型 0非聚合 1hub88 4gfg聚合
	GameSubType    string  `gorm:"column:GameSubType;comment:SLOT(电子) BAC(百家乐) DT(龙虎) SHB(骰宝) ROU(轮盘) NN(牛牛) ZJH(炸金花) BJ(21点) SG(三公) BF(斗牛) CBAC(包桌百家乐) LBAC(咪牌百家乐) TEX(德州) FT(番摊) SD(色碟) PJ(牌九) CP(彩票) AB(安达巴哈)" json:"GameSubType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // SLOT(电子) BAC(百家乐) DT(龙虎) SHB(骰宝) ROU(轮盘) NN(牛牛) ZJH(炸金花) BJ(21点) SG(三公) BF(斗牛) CBAC(包桌百家乐) LBAC(咪牌百家乐) TEX(德州) FT(番摊) SD(色碟) PJ(牌九) CP(彩票) AB(安达巴哈)
	ThirdGameType  string  `gorm:"column:ThirdGameType;comment:三方类别" json:"ThirdGameType"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  // 三方类别
	CountryList    string  `gorm:"column:CountryList;default:AE,AF,AZ,BD,BH,BN,BT,CN,CY,HK,ID,IL,IN,IO,IQ,IR,JO,JP,KG,KH,KP,KR,KW,KZ,LA,LB,LK,MM,MN,MO,MV,MY,NP,OM,PH,PK,PS,QA,RU,SA,SG,SY,TH,TJ,TL,TM,TW,UZ,VN,YE,AD,AL,AM,AT,AX,BA,BE,BG,BY,CH,CZ,DE,DK,EE,ES,FI,FO,FR,GB,GE,GG,GI,GR,HR,HU,IE,IM,IS,IT,JE,LI,LT,LU,LV,MC,MD,ME,MK,MT,NL,NO,PL,PT,RO,RS,SE,SI,SJ,SK,SM,TR,UA,VA,AG,AI,AW,BB,BL,BM,BQ,BS,BZ,CA,CR,CU,CW,DM,DO,GD,GL,GP,GT,HN,HT,JM,KN,KY,LC,MF,MQ,MS,MX,NI,PA,PM,PR,SV,SX,TC,TT,US,VC,VG,VI,AR,BO,BR,CL,CO,EC,FK,GF,GS,GY,PE,PY,SR,UY,VE,AO,BF,BI,BJ,BW,CD,CF,CG,CI,CM,CV,DJ,DZ,EG,EH,ER,ET,GA,GH,GM,GN,GQ,GW,KE,KM,LR,LS,LY,MA,MG,ML,MR,MU,MW,MZ,NA,NE,NG,RE,RW,SC,SD,SH,SL,SN,SO,SS,ST,SZ,TD,TG,TN,TZ,UG,YT,ZA,ZM,ZW,AQ,BV,TF,AS,AU,CC,CK,CX,FJ,FM,GU,HM,KI,MH,MP,NC,NF,NR,NU,NZ,PF,PG,PN,PW,SB,TK,TO,TV,UM,VU,WF,WS;comment:支持的地区（二位字母国家代码，英文逗号分隔）" json:"CountryList"` // 支持的地区（二位字母国家代码，英文逗号分隔）
	OnlineMin      int32   `gorm:"column:OnlineMin;comment:最小在玩人数" json:"OnlineMin"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 最小在玩人数
	OnlineMax      int32   `gorm:"column:OnlineMax;comment:最大在玩人数" json:"OnlineMax"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 最大在玩人数
	SpecialBrand   string  `gorm:"column:SpecialBrand;comment:对应特殊厂商" json:"SpecialBrand"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  // 对应特殊厂商
	SpecailDisplay int32   `gorm:"column:SpecailDisplay;default:1;comment:特殊游戏是否显示 1：显示，2:不显示" json:"SpecailDisplay"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       // 特殊游戏是否显示 1：显示，2:不显示
}

// TableName XGameList's table name
func (*XGameList) TableName() string {
	return TableNameXGameList
}
