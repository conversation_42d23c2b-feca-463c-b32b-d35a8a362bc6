package single

import (
	"encoding/json"
	"fmt"
	"strings"
	"xserver/controller/third/single/base"
	"xserver/utils"

	"github.com/robfig/cron/v3"
)

type VGSingleConfig struct {
	ApiDomain     string `json:"url"`      // api基础接口
	Agent         string `json:"agent"`    // 代理
	ApiKey        string `json:"api_key"`  // api key
	Suffix        string `json:"suffix"`   // 玩家后缀
	Currency      string `json:"currency"` // 币别
	ThirdGamePush *base.ThirdGamePush
}

type VGGame struct {
	GameID   string `gorm:"column:GameId"` // 游戏ID
	GameName string `gorm:"column:Name"`   // 游戏名称
}
type VGSingleService struct {
	VGSingleConfig
	BrandName             string             // 平台名称
	RefreshUserAmountFunc func(int) error    // 余额更新回调
	Cronjob               *cron.Cron         // cronjob
	TableID               map[string]VGTable // 桌号列表
	Games                 []VGGame           // 游戏ID列表
}

type VGResponse struct {
	Code    int    `json:"code"`    // 错误代码
	Message string `json:"message"` // 错误信息
}
type VGTableListResponse struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    []VGTable `json:"data"`
	TraceID string    `json:"TraceId"`
}

type VGTable struct {
	TableId   string `json:"tableid"`
	Casino    string `json:"casino"`
	TableName string `json:"tablename"`
}

type VGLoginRequestData struct {
	Language    string `json:"language"` // 语言
	RedirectUrl string `json:"HomeUrl"`  // 返回商户地址
	GameId      string `json:"GameId"`   // 桌號
	// GameId      string `json:"gameId"`
	// Platform    string `json:"platform"` // 用户设备类型，表示用户访问的设备类型，可以是 Desktop、Mobile、Unknown 等，选填
}

// 注册用户
type VGSignUpRequest struct {
	Agent      string `json:"agent"`     // 代理名称
	BetLimit   string `json:"betlimit"`  // 限红
	LoginName  string `json:"loginname"` // 玩家名称+后缀(suffix)
	SignString string `json:"sign"`      // 簽名
}

func (r *VGSignUpRequest) Sign(apiKey string) {
	msg := fmt.Sprintf("%s%s%s%s", r.Agent, r.BetLimit, r.LoginName, apiKey)
	r.SignString = utils.Md5V(msg)
}

type VGSignUpResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		UID      int    `json:"uid"`
		Username string `json:"username"`
		Betlimit string `json:"betlimit"`
	} `json:"data"`
	TraceID string `json:"TraceId"`
}

// 登入游戏
type VGSignInRequest struct {
	Agent      string `json:"agent"`      // 代理名称
	Language   string `json:"language"`   // 用户语系
	LoginName  string `json:"loginname"`  // 玩家名称+后缀(suffix)
	ReturnUrl  string `json:"return_url"` // 登入失败转跳 URL
	TableId    string `json:"rid"`        // 桌號
	Token      string `json:"token"`      // 身份令牌
	SignString string `json:"sign"`       // 簽名
}

func (r *VGSignInRequest) Sign(apiKey string) {
	msg := fmt.Sprintf("%s%s%s%s%s%s%s", r.Agent, r.Language, r.LoginName, r.ReturnUrl, r.TableId, r.Token, apiKey)
	r.SignString = utils.Md5V(msg)
}

type VGSignInResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		URL string `json:"url"`
	} `json:"data"`
	TraceID string `json:"TraceId"`
}

type VGTokenData struct {
	UserId    int    `json:"user_id"`
	Account   string `json:"account"`
	CreatedAt string `json:"created_at"`
}

// 余额查询
type VGBalanceRequest struct {
	Agent      string `json:"agent"`     // 代理名称
	LoginName  string `json:"loginname"` // 玩家名称+后缀(suffix)
	Token      string `json:"token"`     // 玩家令牌
	Username   string `json:"username"`  // 玩家名称+后缀(suffix)
	SignString string `json:"sign"`      // 簽名
}

func (r *VGBalanceRequest) IsValid(apiKey string) bool {
	msg := fmt.Sprintf("%s%s%s%s%s", r.Agent, r.LoginName, r.Token, r.Username, apiKey)
	return utils.Md5V(msg) == strings.ToLower(r.SignString)
}

// 下注
type VGBetRequest struct {
	Agent      string         `json:"agent"`     // 代理名称
	Amount     json.Number    `json:"amount"`    // 投注金额
	LoginName  string         `json:"loginname"` // 玩家名称+后缀(suffix)
	RoundID    string         `json:"roundid"`   // 游戏局号
	Token      string         `json:"token"`     // 玩家令牌
	TransID    string         `json:"transid"`   // 投注号 (betid)
	SignString string         `json:"sign"`      // 簽名
	Detail     VGBetReqDetail `json:"detail"`    // 其他资讯
}

type VGBetReqDetail struct {
	BetType   string  `json:"bettype"`
	BuBalance float64 `json:"bubalance"`
	Currency  string  `json:"currency"`
	IP        string  `json:"ip"`
	MaxLose   int     `json:"maxlose"`
	MaxWin    int     `json:"maxwin"`
	Platform  string  `json:"platform"`
	ShoeId    string  `json:"shoeid"`
	ShoeRound string  `json:"shoeround"`
	TableId   string  `json:"tableid"`
	TableIdx  string  `json:"tableidx"`
	Ts        int     `json:"ts"`
}

func (r *VGBetRequest) IsValid(apiKey string) bool {
	msg := fmt.Sprintf("%s%s%s%s%s%s%s", r.Agent, r.Amount.String(), r.LoginName, r.RoundID, r.Token, r.TransID, apiKey)
	return utils.Md5V(msg) == strings.ToLower(r.SignString)
}

type VGBetResponse struct {
	Code    int     `json:"code"`    // 错误代码
	Balance float64 `json:"balance"` // 用户余额
}

// 取消投注
type VGWinBetRequest struct {
	Agent      string      `json:"agent"`     // 代理名称
	Amount     json.Number `json:"amount"`    // 投注 / 派彩金额
	LoginName  string      `json:"loginname"` // 玩家名称+后缀(suffix)
	RoundID    string      `json:"roundid"`   // 游戏局号
	TransID    string      `json:"transid"`   // 投注号 (betid)
	SignString string      `json:"sign"`      // 簽名
	Detail     VGWinDetail `json:"detail"`    // 其他资讯
}

type VGWinDetail struct {
	BetTime    string      `json:"bettime"`
	BetType    string      `json:"bettype"`
	Bp         string      `json:"bp"`
	BpWin      json.Number `json:"bp_win"`
	BTimestamp int         `json:"btimestamp"`
	Bw         string      `json:"bw"`
	BwWin      string      `json:"bw_win"`
	Currency   string      `json:"currency"`
	Detail     struct {
		B1 string `json:"b1"`
		B2 string `json:"b2"`
		B3 string `json:"b3"`
		P1 string `json:"p1"`
		P2 string `json:"p2"`
		P3 string `json:"p3"`
	} `json:"detail"`
	IP         string `json:"ip"`
	Platform   string `json:"platform"`
	Pp         string `json:"pp"`
	PpWin      string `json:"pp_win"`
	Pw         string `json:"pw"`
	PwWin      string `json:"pw_win"`
	SettleTime string `json:"settletime"`
	ShoeId     int    `json:"shoeid"`
	ShoeRound  int    `json:"shoeround"`
	TableIdx   string `json:"tableidx"`
	Tie        string `json:"tie"`
	TieWin     string `json:"tie_win"`
}

func (r *VGWinBetRequest) IsValid(apiKey string) bool {
	msg := fmt.Sprintf("%s%s%s%s%s%s", r.Agent, r.Amount.String(), r.LoginName, r.RoundID, r.TransID, apiKey)
	return utils.Md5V(msg) == strings.ToLower(r.SignString)
}

func (r *VGWinBetRequest) GameRecord2Str(userId string, status string) string {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf(`"投注号":"%s",`, r.TransID))
	sb.WriteString(fmt.Sprintf(`"玩家ID":"%s",`, userId))
	sb.WriteString(fmt.Sprintf(`"玩家名称":"%s",`, r.LoginName))
	sb.WriteString(fmt.Sprintf(`"游戏局号":"%s",`, r.RoundID))
	sb.WriteString(fmt.Sprintf(`"派彩金额":%s,`, r.Amount.String()))
	sb.WriteString(fmt.Sprintf(`"订单状态":"%s"`, status))
	if jsonString, err := json.Marshal(r.Detail); err == nil {
		sb.WriteString(fmt.Sprintf(`,"其他资讯":%s`, string(jsonString)))
	}
	sb.WriteString("}")
	return sb.String()
}

// 取消投注
type VGCancelBetRequest struct {
	Agent      string            `json:"agent"`     // 代理名称
	Amount     json.Number       `json:"amount"`    // 投注 / 派彩金额
	LoginName  string            `json:"loginname"` // 玩家名称+后缀(suffix)
	RoundID    string            `json:"roundid"`   // 游戏局号
	TransID    string            `json:"transid"`   // 投注号 (betid)
	SignString string            `json:"sign"`      // 簽名
	Detail     VGCancelBetDetail `json:"detail"`    // 其他资讯
}

type VGCancelBetDetail struct {
	Abet       string      `json:"abet"`
	Abet2      string      `json:"abet2"`
	BetTime    string      `json:"bettime"`
	BetType    string      `json:"bettype"`
	Bp         string      `json:"bp"`
	BpWin      string      `json:"bp_win"`
	BTimestamp int         `json:"btimestamp"`
	Bw         json.Number `json:"bw"`
	BwWin      json.Number `json:"bw_win"`
	Currency   string      `json:"currency"`
	Detail     any         `json:"detail"`
	Fanshui    any         `json:"fanshui"`
	ID         int         `json:"id"`
	IP         string      `json:"ip"`
	Percentage any         `json:"percentage"`
	Platform   string      `json:"platform"`
	Pp         string      `json:"pp"`
	PpWin      string      `json:"pp_win"`
	Pw         string      `json:"pw"`
	PwWin      string      `json:"pw_win"`
	SettleTime any         `json:"settletime"`
	ShoeId     int         `json:"shoeid"`
	ShoeRound  int         `json:"shoeround"`
	Status     int         `json:"status"`
	TableIdx   string      `json:"tableidx"`
	Tie        string      `json:"tie"`
	TieWin     string      `json:"tie_win"`
	Token      string      `json:"token"`
}

func (r *VGCancelBetRequest) IsValid(apiKey string) bool {
	msg := fmt.Sprintf("%s%s%s%s%s%s", r.Agent, r.Amount.String(), r.LoginName, r.RoundID, r.TransID, apiKey)
	return utils.Md5V(msg) == strings.ToLower(r.SignString)
}

func (r *VGCancelBetRequest) GameRecord2Str(userId string, isSettled bool, status string) string {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf(`"投注号":"%s",`, r.TransID))
	sb.WriteString(fmt.Sprintf(`"玩家ID":"%s",`, userId))
	sb.WriteString(fmt.Sprintf(`"玩家名称":"%s",`, r.LoginName))
	sb.WriteString(fmt.Sprintf(`"游戏局号":"%s",`, r.RoundID))
	if isSettled {
		sb.WriteString(fmt.Sprintf(`"玩家输赢":%s,`, r.Amount.String()))
	} else {
		sb.WriteString(fmt.Sprintf(`"投注金额":%s,`, r.Amount.String()))
	}
	sb.WriteString(fmt.Sprintf(`"订单状态":"%s"`, status))
	if jsonString, err := json.Marshal(r.Detail); err == nil {
		sb.WriteString(fmt.Sprintf(`,"其他资讯":%s`, string(jsonString)))
	}
	sb.WriteString("}")
	return sb.String()
}

type VGCancelBetResponse struct {
	Code    int     `json:"code"`    // 错误代码
	Balance float64 `json:"balance"` // 用户余额
}

type VGWinResponse struct {
	Code    int     `json:"code"`    // 错误代码
	Balance float64 `json:"balance"` // 用户余额
}

// 游戏结果
type VGBetResultRequest struct {
	Agent      string `json:"agent"`     // 代理名称
	StartTime  string `json:"starttime"` // UTC+8, 'YYYY-MM-DD HH:mm:ss'
	EndTime    string `json:"endtime"`   // UTC+8, 'YYYY-MM-DD HH:mm:ss'
	PageNum    int    `json:"page_num"`  // 页数
	PageSize   int    `json:"page_size"` // 每页笔数 max: 2000
	Status     int    `json:"token"`     // 1 = 未结算 4 = 已结算,若不带此参数默认将回应所有状态纪录。
	SignString string `json:"sign"`      // 簽名
}

func (r *VGBetResultRequest) Sign(apiKey string) {
	msg := fmt.Sprintf("%s%s%d%d%s%d%s", r.Agent, r.EndTime, r.PageNum, r.PageSize, r.StartTime, r.Status, apiKey)
	r.SignString = utils.Md5V(msg)
}

type VGBetResultResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	TraceID string          `json:"TraceId"`
	Data    VGBetResultData `json:"data"`
}

type VGBetResultData struct {
	CurrentPage   int               `json:"current_page"`
	CurrentCounts int               `json:"current_counts"`
	TotalPages    int               `json:"total_pages"`
	TotalCounts   int               `json:"total_counts"`
	BetDetail     []VGBetRespDetail `json:"betdetail"`
}

type VGBetRespDetail struct {
	RoundID    string      `json:"roundid"`    // 游戏局号
	BetID      string      `json:"betid"`      // 投注号
	Username   string      `json:"username"`   // 玩家名称
	ShoeID     int         `json:"shoeid"`     // 靴号
	ShoeRound  int         `json:"shoeround"`  // 靴局
	Casino     string      `json:"casino"`     // 桌别
	Valid      json.Number `json:"valid"`      // 有效投注
	Bet        json.Number `json:"bet"`        // 投注金额
	Win        json.Number `json:"win"`        // 派彩金额
	Currency   string      `json:"currency"`   // 币别
	TableIdx   string      `json:"tableidx"`   // 桌号
	BetTime    string      `json:"bettime"`    // 下注时间 (ISO 8601 标准日期时间格式)
	SettleTime string      `json:"settletime"` // 结算时间 (ISO 8601 标准日期时间格式)
	Status     int         `json:"status"`     // 此笔数据状态 1 = 未结算 4 = 已结算
	IP         string      `json:"ip"`         // 玩家 IP 地址
	BetType    string      `json:"bettype"`    // 投注类型 multi = 多台投注 single = 进桌投注
	Platform   string      `json:"platform"`   // 设备
	BetResult  struct {
		Bw     string `json:"bw"`      // 庄下注金额
		Pw     string `json:"pw"`      // 闲下注金额
		Tie    string `json:"tie"`     // 和下注金额
		Bp     string `json:"bp"`      // 庄对下注金额
		Pp     string `json:"pp"`      // 闲对下注金额
		BwWin  string `json:"bw_win"`  // 庄派彩金额
		PwWin  string `json:"pw_win"`  // 闲派彩金额
		TieWin string `json:"tie_win"` // 和派彩金额
		BpWin  string `json:"bp_win"`  // 庄对派彩金额
		PpWin  string `json:"pp_win"`  // 闲对派彩金额
	} `json:"betresult"` // 详细投注与派彩结果
	Detail struct {
		B1 string `json:"b1"` // 庄 1
		B2 string `json:"b2"` // 庄 2
		B3 string `json:"b3"` // 庄 3
		P1 string `json:"p1"` // 闲 1
		P2 string `json:"p2"` // 闲 2
		P3 string `json:"p3"` // 闲 3
	} `json:"detail"` // 开牌结果
}

// 派彩/取消投注
type VGReSettleRequest struct {
	Agent      string           `json:"agent"`     // 代理名称
	Amount     json.Number      `json:"amount"`    // 投注 / 派彩金额
	LoginName  string           `json:"loginname"` // 玩家名称+后缀(suffix)
	RoundID    string           `json:"roundid"`   // 游戏局号
	TransID    string           `json:"transid"`   // 投注号 (betid)
	SignString string           `json:"sign"`      // 簽名
	Detail     VGReSettleDetail `json:"detail"`    // 其他资讯
}

type VGReSettleDetail struct {
	BetTime    string `json:"bettime"`
	BetType    string `json:"bettype"`
	Bp         string `json:"bp"`
	BpWin      string `json:"bp_win"`
	BTimestamp int    `json:"btimestamp"`
	Bw         string `json:"bw"`
	BwWin      string `json:"bw_win"`
	Currency   string `json:"currency"`
	Detail     struct {
		B1 string `json:"b1"`
		B2 string `json:"b2"`
		B3 string `json:"b3"`
		P1 string `json:"p1"`
		P2 string `json:"p2"`
		P3 string `json:"p3"`
	} `json:"detail"`
	IP         string `json:"ip"`
	Platform   string `json:"platform"`
	Pp         string `json:"pp"`
	PpWin      string `json:"pp_win"`
	Pw         string `json:"pw"`
	PwWin      string `json:"pw_win"`
	SettleTime string `json:"settletime"`
	ShoeId     int    `json:"shoeid"`
	ShoeRound  int    `json:"shoeround"`
	TableIdx   string `json:"tableidx"`
	Tie        string `json:"tie"`
	TieWin     string `json:"tie_win"`
}

func (r *VGReSettleRequest) IsValid(apiKey string) bool {
	msg := fmt.Sprintf("%s%s%s%s%s%s", r.Agent, r.Amount.String(), r.LoginName, r.RoundID, r.TransID, apiKey)
	return utils.Md5V(msg) == strings.ToLower(r.SignString)
}
func (r *VGReSettleRequest) GameRecord2Str(userId string, status string) string {
	sb := strings.Builder{}
	sb.WriteString("{")
	sb.WriteString(fmt.Sprintf(`"投注号":"%s",`, r.TransID))
	sb.WriteString(fmt.Sprintf(`"玩家ID":"%s",`, userId))
	sb.WriteString(fmt.Sprintf(`"玩家名称":"%s",`, r.LoginName))
	sb.WriteString(fmt.Sprintf(`"游戏局号":"%s",`, r.RoundID))
	sb.WriteString(fmt.Sprintf(`"玩家输赢":%s,`, r.Amount.String()))
	sb.WriteString(fmt.Sprintf(`"订单状态":"%s"`, status))
	if jsonString, err := json.Marshal(r.Detail); err == nil {
		sb.WriteString(fmt.Sprintf(`,"其他资讯":%s`, string(jsonString)))
	}
	sb.WriteString("}")
	return sb.String()
}
