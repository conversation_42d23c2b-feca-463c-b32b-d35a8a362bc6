// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXUserMore = "x_user_more"

// XUserMore mapped from table <x_user_more>
type XUserMore struct {
	UserID            int32     `gorm:"column:UserId;primaryKey;comment:玩家" json:"UserId"`                                   // 玩家
	Fbp               string    `gorm:"column:Fbp;comment:fbp" json:"Fbp"`                                                   // fbp
	CreateTime        time.Time `gorm:"column:CreateTime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"` // 创建时间
	UpdateTime        time.Time `gorm:"column:UpdateTime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdateTime"` // 更新时间
	IsLostAllOnce     int32     `gorm:"column:IsLostAllOnce;comment:0:未输光 1:用户曾经输光一次" json:"IsLostAllOnce"`                  // 0:未输光 1:用户曾经输光一次
	BlockedGameBrands string    `gorm:"column:BlockedGameBrands;comment:禁止的游戏厂商列表，JSON格式" json:"BlockedGameBrands"`          // 禁止的游戏厂商列表，JSON格式
	BetIP             string    `gorm:"column:BetIp;comment:下注IP" json:"BetIp"`                                              // 下注IP
}

// TableName XUserMore's table name
func (*XUserMore) TableName() string {
	return TableNameXUserMore
}
