package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/controller/userbrand"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/imroc/req"
	daogorm "gorm.io/gorm"
)

const GFG_BRAND = "GFG-" // GFG聚合三方厂商以GFG-开头

type GFGJuheConfig struct {
	// 默认线路配置
	defaultConfig GFGJuheRouteConfig
	// 新手保护线路配置
	xinshouConfig         GFGJuheRouteConfig
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

// GFGJuheRouteConfig GFG聚合线路配置
type GFGJuheRouteConfig struct {
	Url      string `json:"url"`
	Key      string `json:"key"`
	Company  string `json:"company"`
	Theme    string `json:"theme"`
	Agent    string `json:"agent"`
	AppUrl   string `json:"appUrl"`
	Currency string `json:"currency"`
}

type ThirdOrderGfgJuhe struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

type AmountChangeLogGfgJuhe struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

type UserBalanceGfgJuhe struct {
	UserId    int     `json:"UserId" gorm:"column:UserId"`
	SellerId  int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId int     `json:"ChannelId" gorm:"column:ChannelId"`
	Amount    float64 `json:"Amount" gorm:"column:Amount"`
	Token     string  `json:"Token" gorm:"column:Token"`
}

type GameListGfgJuhe struct {
	Id        int    `json:"Id" gorm:"column:Id"`
	Brand     string `json:"Brand" gorm:"column:Brand"`
	GameId    string `json:"GameId" gorm:"column:GameId"`
	Name      string `json:"Name" gorm:"column:Name"`
	EName     string `json:"EName" gorm:"column:EName"`
	State     int    `json:"State" gorm:"column:State"`
	OpenState int    `json:"OpenState" gorm:"column:OpenState"`
	GameType  int    `json:"GameType" gorm:"column:GameType"`
	HubType   int    `json:"HubType" gorm:"column:HubType"`
}

func NewGFGJuheLogic(params map[string]string, fc func(int) error) *GFGJuheConfig {
	// 解析新的配置格式
	configJson := params["config"]
	if configJson == "" {
		// 兼容旧格式
		return &GFGJuheConfig{
			defaultConfig: GFGJuheRouteConfig{
				Url:      params["url"],
				Key:      params["key"],
				Company:  params["company"],
				Theme:    params["theme"],
				Agent:    params["agent"],
				AppUrl:   params["appUrl"],
				Currency: params["currency"],
			},
			xinshouConfig:         GFGJuheRouteConfig{}, // 空配置，使用默认配置
			brandName:             "gfg_juhe",
			RefreshUserAmountFunc: fc,
			thirdGamePush:         base.NewThirdGamePush(),
		}
	}

	// 解析新的JSON配置格式
	var configData struct {
		Default GFGJuheRouteConfig `json:"default"`
		Xinshou GFGJuheRouteConfig `json:"xinshou"`
	}

	if err := json.Unmarshal([]byte(configJson), &configData); err != nil {
		logs.Error("GFG聚合配置解析失败:", err, " config=", configJson)
		// 解析失败时使用默认配置
		return &GFGJuheConfig{
			defaultConfig: GFGJuheRouteConfig{
				Url:      params["url"],
				Key:      params["key"],
				Company:  params["company"],
				Theme:    params["theme"],
				Agent:    params["agent"],
				AppUrl:   params["appUrl"],
				Currency: params["currency"],
			},
			brandName:             "gfg_juhe",
			RefreshUserAmountFunc: fc,
			thirdGamePush:         base.NewThirdGamePush(),
		}
	}

	return &GFGJuheConfig{
		defaultConfig:         configData.Default,
		xinshouConfig:         configData.Xinshou,
		brandName:             "gfg_juhe",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyGFGJuhe = "cacheKeyGFGJuhe:"

// getRouteConfig 根据用户ID获取对应的线路配置
func (l *GFGJuheConfig) getRouteConfig(userId int) GFGJuheRouteConfig {
	// 检查用户是否为新手保护用户
	if userId > 0 {
		status, err := userbrand.UserProtectionSvc.CheckUserProtectionStatus(server.Db().GormDao(), userId)
		if err != nil {
			logs.Error("GFG聚合获取用户保护状态失败 userId=", userId, " err=", err.Error())
			return l.defaultConfig
		}

		// 如果是新手保护用户且有新手配置，使用新手线路
		if status.IsProtected && l.xinshouConfig.Url != "" {
			logs.Info("GFG聚合新手保护用户使用新手线路 userId=", userId)
			return l.xinshouConfig
		}
	}

	// 默认使用普通线路
	return l.defaultConfig
}

func (l *GFGJuheConfig) sign(s string, config GFGJuheRouteConfig) string {
	return base.MD5(s + config.Key)
}

func (l *GFGJuheConfig) userId2tokenByGfgJuhe(cacheKey string, userId int) string {
	token := "gfg_juhe_" + uuid.NewString()
	if err := server.Redis().SetStringEx(cacheKey+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("gfg聚合单一钱包 userId2token set redis key=", cacheKey+token, " userId=", userId, " error=", err.Error())
	}
	return token
}

func (l *GFGJuheConfig) token2UserIdByGfgJuhe(cacheKey, token string) int {
	redisdata := server.Redis().Get(cacheKey + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

// 获取用户Id
func (l *GFGJuheConfig) getUserIdByAccountId(accountId_ string) int {
	// 获取ID，下划线后面的部分
	accountId := strings.Split(accountId_, "_")[1]
	userId, _ := strconv.Atoi(accountId)
	return userId
}

// getConfigByAccountId 根据accountId获取对应的配置
func (l *GFGJuheConfig) getConfigByAccountId(accountId_ string) GFGJuheRouteConfig {
	userId := l.getUserIdByAccountId(accountId_)
	return l.getRouteConfig(userId)
}

// IsGFGJuheAccount 检查accountId是否属于GFG聚合（匹配任一线路的Agent）
func (l *GFGJuheConfig) IsGFGJuheAccount(accountId string) bool {
	if accountId == "" {
		return false
	}

	s := strings.Split(accountId, "_")
	if len(s) == 0 {
		return false
	}

	agent := s[0]

	// 检查是否匹配默认线路的Agent
	if agent == l.defaultConfig.Agent {
		return true
	}

	// 检查是否匹配新手线路的Agent（如果配置了的话）
	if l.xinshouConfig.Agent != "" && agent == l.xinshouConfig.Agent {
		return true
	}

	return false
}

// Login
func (l *GFGJuheConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"`
		ExitUrl  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyGFGJuhe, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 根据用户类型获取对应的线路配置
	routeConfig := l.getRouteConfig(token.UserId)
	logs.Info("GFG聚合登录使用线路配置 userId=", token.UserId, " url=", routeConfig.Url, " agent=", routeConfig.Agent)

	//sessionId
	sessionId := l.userId2tokenByGfgJuhe(cacheKeyGFGJuhe, token.UserId)
	//account
	account := fmt.Sprintf("%s_%d", routeConfig.Agent, token.UserId)

	gamecode := fmt.Sprintf("%d", reqdata.GameId)

	//根据游戏获取分类
	brand := GFG_BRAND // Brand前缀，匹配GFG-后跟任意字符
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", gamecode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			logs.Error("gfg聚合 Login 游戏ID获取失败,游戏不存在 userId=", token.UserId, " GameId=", gamecode, " Brand=", brand)
			return
		}
		logs.Error("gfg聚合 Login 游戏ID获取失败 userId=", token.UserId, " GameId=", gamecode, " Brand=", brand, " err=", err)
		ctx.RespErrString(true, &errcode, "游戏ID获取失败")
		return
	}
	brandName := gameList.Brand
	_ = gameList.Name     // gameName 暂时未使用
	_ = gameList.GameType // gameType 暂时未使用

	// 801=虎虎生财 802=亡灵大盗 803=麻将胡了2 804=十倍金牛
	if gamecode != "801" && gamecode != "802" && gamecode != "803" && gamecode != "804" {
		if gameList.State != 1 || gameList.OpenState != 1 {
			logs.Error("gfg聚合 Login 游戏不可用 userId=", token.UserId, " gamecode=", gamecode, " gameList=", gameList)
			ctx.RespErrString(true, &errcode, "游戏未开放")
			return
		}
	}
	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(token.UserId, brandName, gamecode); err != nil {
		logs.Error("gfg聚合 登录游戏 权限检查错误 userId=", token.UserId, " gameId=", gamecode, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("gfg聚合 登录游戏 权限被拒绝 userId=", token.UserId, " gameId=", gamecode, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	querydata := make(req.Param)
	querydata["agent"] = routeConfig.Agent
	querydata["companyKey"] = routeConfig.Company
	querydata["theme"] = routeConfig.Theme
	querydata["account"] = account
	querydata["gameId"] = reqdata.GameId
	querydata["ip"] = ctx.GetIp()
	querydata["platform"] = 2
	querydata["token"] = sessionId
	querydata["nickName"] = fmt.Sprint(token.UserId)
	querydata["timestamp"] = fmt.Sprintf("%d", time.Now().UnixMilli())
	querydata["languageType"] = reqdata.LangCode
	querydata["exitUrl"] = reqdata.ExitUrl
	querydata["appUrl"] = routeConfig.AppUrl + "/api/third/gfg_juhe"
	urlreq := routeConfig.Url + "/login"
	reqBytes, _ := json.Marshal(querydata)
	au := l.sign(string(reqBytes), routeConfig)
	header := map[string]string{
		"Authorization": au,
	}
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if code, ok := data["code"]; ok {
		if abugo.GetInt64FromInterface(code) == 0 {
			ctx.RespOK(data["data"].(map[string]interface{})["url"])
			return
		}
	}
	logs.Error("gfg聚合单一钱包 登录三方错误 msg=", data["msg"].(string))
	ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
	return
}

// GetBalance 查询会员余额 api/Balance/GetBalance
func (l *GFGJuheConfig) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AccountId string `json:"accountId"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg聚合单一钱包 GetBalance := ", string(bodyBytes))
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "参数读取参数错误",
			"timestamp": time.Now().Unix(),
		})
		logs.Error("gfg聚合单一钱包 GetBalance 错误", err.Error())
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg聚合单一钱包 GetBalance 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "JSON解析失败",
			"timestamp": time.Now().Unix(),
		})
		logs.Error("gfg聚合单一钱包 JSON解析失败", err.Error())
		return
	}

	// 获取用户Id，下划线后面的部分
	UserId := l.getUserIdByAccountId(reqdata.AccountId)
	if UserId <= 0 {
		logs.Error("gfg聚合单一钱包 无效的用户Id", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 开启余额查询事务
	userBalance := UserBalanceGfgJuhe{}
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("gfg聚合单一钱包 GetBalance 查询用户余额时，事务开启失败 userId=", UserId, " err=", tx.Error)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	// 使用 FOR UPDATE 加锁查询，防止其他事务修改这行数据
	if err := tx.Table("x_user").Where("UserId = ?", UserId).Set("gorm:query_option", "FOR UPDATE").First(&userBalance).Error; err != nil {
		tx.Rollback()
		logs.Error("gfg聚合单一钱包 Bet 查询用户余额错误 userId=", UserId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfg聚合单一钱包 Bet 用户余额为负数 userId=", UserId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
			"data":      gin.H{"Data": float64(int(userBalance.Amount*100)) / 100},
		})
		return
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logs.Error("gfg聚合单一钱包 Bet 查询用户余额错误,提交事务失败 userId=", UserId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100
	// logs.Info("gfg聚合单一钱包 GetBalance:", UserId, fmt.Sprintf("%v", balance2))
	ctx.Gin().JSON(200, gin.H{
		"code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance2},
		"msg":       "",
	})
}

func (l *GFGJuheConfig) check(authorization, bodyBytes string) bool {
	if authorization == "" {
		return false
	}

	// 尝试用默认配置验证
	if base.MD5(bodyBytes+l.defaultConfig.Key) == authorization {
		return true
	}

	// 尝试用新手保护配置验证
	if l.xinshouConfig.Key != "" && base.MD5(bodyBytes+l.xinshouConfig.Key) == authorization {
		return true
	}

	return false
}

// Bet 下注方法 api/Balance/LockBalance
func (l *GFGJuheConfig) Bet(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg聚合单一钱包 Bet bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfg聚合单一钱包 Bet 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg聚合单一钱包 Bet 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}
	type betReq struct {
		AccountId    string  `json:"accountId"`
		Money        float64 `json:"money"`
		GameId       int     `json:"gameId"`
		OrderId      string  `json:"orderId"`
		MinLockMoney float64 `json:"minLockMoney"`
		Timestamp    int64   `json:"timestamp"`
	}
	requests := betReq{}
	err = json.Unmarshal(bodyBytes, &requests)
	if err != nil {
		logs.Error("gfg聚合单一钱包 Bet json解析错误 bodyBytes=", string(bodyBytes), " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "非法参数",
		})
		return
	}

	// 获取用户Id，下划线后面的部分
	userId := l.getUserIdByAccountId(requests.AccountId)
	if userId <= 0 {
		logs.Error("gfg聚合单一钱包 无效的用户Id", requests.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("gfg聚合单一钱包 Bet 查询用户余额错误 userId=", userId, " thirdId=", requests.OrderId, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfg聚合单一钱包 Bet 用户余额为负数 userId=", userId, " thirdId=", requests.OrderId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100

	//三方来源的数据整理
	var (
		betAmount1   = requests.Money
		betAmountMin = requests.MinLockMoney
		thirdId      = requests.OrderId
		gamecode     = fmt.Sprintf("%d", requests.GameId)
		thirdTime    = time.Now().Format("2006-01-02 15:04:05")
	)

	if betAmountMin > balance2 {
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足",
		})
		return
	}
	var betAmount float64 = 0
	if betAmount1 > balance2 {
		//实际冻结额度，如果可用余额大于"申请冻结额"，则返回"申请冻结额"，如果可用余额小于"申请冻结额"大于"最低冻结金额"，则返回所有余额，否则失败
		betAmount = balance2
	} else {
		betAmount = betAmount1
	}

	//根据游戏获取分类
	brand := GFG_BRAND // Brand前缀，匹配GFG-后跟任意字符
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", gamecode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("gfg聚合 Bet 游戏ID获取失败,游戏不存在 userId=", userId, " GameId=", gamecode, " Brand=", brand)
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询游戏错误",
			})
			return
		}
		logs.Error("gfg聚合 Bet 游戏ID获取失败 userId=", userId, " GameId=", gamecode, " Brand=", brand, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询游戏错误",
		})
		return
	}
	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, brandName, gamecode); err != nil {
		logs.Error("gfg聚合 Bet 权限检查错误 userId=", userId, " gameId=", gamecode, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "玩家余额被冻结",
			"timestamp": time.Now().Unix(),
		})
		return
	} else if !allowed {
		logs.Error("gfg聚合 Bet 权限被拒绝 userId=", userId, " gameId=", gamecode, " hint=", hint)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "玩家余额被冻结",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 推送下注事件通知
	if l.thirdGamePush != nil {
		// 获取用户对应的配置
		userConfig := l.getRouteConfig(userId)
		logs.Info("gfg聚合单一钱包 Bet 下注成功 推送下注事件", gameName, " betAmount=", betAmount)
		l.thirdGamePush.PushBetEvent(userId, gameName, brandName, betAmount, userConfig.Currency, brandName, thirdId, gameType)
	}

	// 返回成功响应
	ctx.Gin().JSON(200, gin.H{
		"code":      0,
		"timestamp": time.Now().Unix(),
		"data": gin.H{
			"money":     betAmount,
			"accountId": requests.AccountId,
			"balance":   balance2,
		},
		"msg": "",
	})
}
