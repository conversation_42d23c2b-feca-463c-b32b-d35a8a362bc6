package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

// SettleBetTransaction 结算交易数据结构
type SettleBetTransaction struct {
	PlayerId         string       `json:"PlayerId"`                  // 玩家账号
	Provider         string       `json:"Provider"`                  // 游戏供应商代码
	GameId           string       `json:"GameId"`                    // 游戏代码
	GameName         string       `json:"GameName,omitempty"`        // 游戏名称
	RoundId          string       `json:"RoundId,omitempty"`         // 游戏局号
	GameNo           string       `json:"GameNo,omitempty"`          // 彩期
	BetId            string       `json:"BetId,omitempty"`           // 注单代码
	TransactionId    string       `json:"TransactionId"`             // IM交易代码
	ActionId         int          `json:"ActionId,omitempty"`        // 动作编号
	RefTransactionId []string     `json:"RefTransactionId"`          // 被结算的交易代码数组
	Type             string       `json:"Type"`                      // 交易类别
	BetType          string       `json:"BetType,omitempty"`         // 下注类别
	Currency         string       `json:"Currency"`                  // 币种
	Amount           float64      `json:"Amount"`                    // 结算金额
	WalletType       int          `json:"WalletType,omitempty"`      // 钱包类型
	ActualBetAmount  float64      `json:"ActualBetAmount,omitempty"` // 实际投注额
	ActualWinAmount  float64      `json:"ActualWinAmount,omitempty"` // 实际中奖金额
	Details          string       `json:"Details,omitempty"`         // 投注明细
	SettlementDate   string       `json:"SettlementDate,omitempty"`  // 结算时间
	Product          int          `json:"Product,omitempty"`         // 产品类型
	EventId          string       `json:"EventId,omitempty"`         // 匹配号码
	TimeStamp        string       `json:"TimeStamp"`                 // 时间戳
	SpecialGame      *SpecialGame `json:"specialGame,omitempty"`     // 特殊游戏
}

// SpecialGame 特殊游戏结构
type SpecialGame struct {
	SGtype     string `json:"Sgtype,omitempty"`     // 特殊游戏类型
	SGcount    int    `json:"Sgcount,omitempty"`    // 特殊游戏总数
	SGsequence int    `json:"Sgsequence,omitempty"` // 完成的特殊游戏计数
}

// SettleBetRequest 结算请求结构
type SettleBetRequest struct {
	ProductWallet string                 `json:"ProductWallet" validate:"required"` // 产品钱包
	Transactions  []SettleBetTransaction `json:"Transactions" validate:"required"`  // 交易列表
}

// SettleBetResult 结算结果结构
type SettleBetResult struct {
	Code                  int     `json:"Code"`                            // 子级代码
	Message               string  `json:"Message"`                         // 响应信息
	OperatorTransactionId string  `json:"OperatorTransactionId,omitempty"` // 运营商交易代码
	TransactionId         string  `json:"TransactionId"`                   // IM交易代码
	Balance               float64 `json:"Balance"`                         // 余额
}

// SettleBetResponse 结算响应结构
type SettleBetResponse struct {
	Code    int               `json:"Code"`    // 母级代码
	Message string            `json:"Message"` // 响应信息
	Results []SettleBetResult `json:"Results"` // 结果列表
}

// SettleBet 结算回调 - URL: /SettleBet
func (l *IMOneSingleService) SettleBet(ctx *abugo.AbuHttpContent) {

	reqdata := SettleBetRequest{}
	reqDataByte, err := io.ReadAll(ctx.Gin().Request.Body)

	logs.Debug("IMOne SettleBet receive:%s, ", string(reqDataByte))
	err = json.Unmarshal(reqDataByte, &reqdata)
	if err != nil {
		logs.Error("IMOne SettleBet 请求参数错误 err=", err.Error())
		respData := SettleBetResponse{
			Code:    612,
			Message: "Invalid Argument.",
		}
		ctx.RespJson(respData)
		return
	}

	// 处理每个交易
	results := make([]SettleBetResult, len(reqdata.Transactions))

	for i, transaction := range reqdata.Transactions {
		result := l.processSettleBetTransaction(transaction, reqdata.ProductWallet, reqDataByte)
		results[i] = result
	}

	respData := SettleBetResponse{
		Code:    0,
		Message: "Success",
		Results: results,
	}
	ctx.RespJson(respData)
}

// processSettleBetTransaction 处理单个结算交易
func (l *IMOneSingleService) processSettleBetTransaction(transaction SettleBetTransaction, productWallet string, reqDataByte []byte) SettleBetResult {
	// 解析用户ID
	userId := base.ParseUserIdFromPlayerName(transaction.PlayerId)
	if userId == 0 {
		logs.Error("IMOne SettleBet 解析用户ID失败 PlayerId=", transaction.PlayerId)
		return SettleBetResult{
			Code:          504,
			Message:       "Player does not exist.",
			TransactionId: transaction.TransactionId,
		}
	}

	var finalBalance float64
	thirdId := transaction.BetId          // 使用 BetId 作为主要标识
	tablePre := "x_third_sport_pre_order" // 预结算表
	table := "x_third_sport"              // 正式表
	actionId := transaction.ActionId      // 动作编号

	// 根据 ActionId 确定操作类型和最终订单状态
	var operationType string
	var finalDataState int

	switch actionId {
	// 退款相关 (金额为正，订单状态变为已撤单)
	case 2001: // DangerRefund
		operationType = "危险球退款"
		finalDataState = -2 // 已撤单
	case 2011: // LiveBallRefund
		operationType = "滚球退款"
		finalDataState = -2 // 已撤单
	case 6001, 6002, 6003, 6011, 6014: // Cancel 系列
		operationType = "比赛取消退款"
		finalDataState = -2 // 已撤单

	// BetTrade 相关
	case 3001: // BTBuyBack
		operationType = "BT回购"
		finalDataState = -2 // 已结算
	case 3002: // BTCancelBuyBack
		operationType = "BT取消回购"
		finalDataState = -2 // 回到下注状态
	case 3003: // BTRestoreBuyBack
		operationType = "BT恢复回购"
		finalDataState = -2 // 已结算
	case 3011: // LiveBallRefundRevert
		operationType = "滚球退款还原"
		finalDataState = -2 // 回到下注状态

	// 结算相关 (订单状态变为已结算)
	case 4001, 4002, 4003, 4004, 4005: // Settle 系列
		operationType = "比赛结算"
		finalDataState = 1 // 已结算

	// 撤销结算相关 (订单状态变为未结算)
	case 5001, 5002, 5003, 5004, 5005: // Unsettle 系列
		operationType = "撤销结算"
		finalDataState = 1 // 回到重新结算状态

	// 撤销取消相关 (订单状态变为未结算)
	case 7001, 7002, 7003, 7011, 7014: // Uncancel 系列
		operationType = "撤销取消"
		finalDataState = -1 // 回到下注状态

	default:
		operationType = "未知操作"
		finalDataState = 0 // 未知状态
		logs.Error("IMOne SettleBet 未知的 ActionId=", actionId, " Type=", transaction.Type)
		return SettleBetResult{
			Code:          600,
			Message:       "Type not found.",
			TransactionId: transaction.TransactionId,
		}
	}

	logs.Info("IMOne SettleBet 操作类型=", operationType, " ActionId=", actionId, " Type=", transaction.Type, " 目标状态=", finalDataState)

	// 开启事务处理结算
	err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询预结算表中的订单并锁定
		order := thirdGameModel.ThirdSportOrder{}
		err := tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if err != nil {
			logs.Error("IMOne SettleBet 订单不存在 thirdId=", thirdId, " err=", err.Error())
			if err == daogorm.ErrRecordNotFound {
				return fmt.Errorf("order not found")
			} else {
				return fmt.Errorf("query order failed")
			}
		}

		// 计算结算金额（需要在状态检查前计算，用于记录日志）
		winAmount := transaction.Amount
		//// 对于体育博彩和电子竞技，金额可以是负数
		//if productWallet == "IMSportsbook" || productWallet == "IMESports" || productWallet == "ESports" {
		//	// 支持负数结算
		//} else {
		//	// 其他产品，确保金额不为负
		//	if winAmount < 0 {
		//		winAmount = 0
		//	}
		//}

		// 检查是否为重新结算
		var isResettle bool

		logs.Info("IMOne SettleBet 订单信息 thirdId=", thirdId, " 订单状态=", order.DataState, " 结算状态=", order.ResettleState, " 操作=", operationType)

		// 根据业务流程检查订单状态合法性
		// 流程1: PlaceBet > SettleBet > Unsettle > SettleBet
		// 流程2: PlaceBet > CancelHT > UncancelHT > SettleBet
		switch {
		// 结算操作：可以从未结算状态进行结算，也可以从撤销结算后重新结算
		case actionId >= 4001 && actionId <= 4005: // Settle 系列
			if order.DataState != -1 && order.DataState != 1 {
				logs.Error("IMOne SettleBet 结算操作要求订单为未结算(-1)或已结算(1)状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for settle")
			}
			// 如果是重新结算（从已结算到结算），需要记录为重新结算
			if order.DataState == 1 {
				isResettle = true
				logs.Info("IMOne SettleBet 重新结算操作 thirdId=", thirdId)
			}

		// 撤销结算操作：只能从已结算状态撤销
		case actionId >= 5001 && actionId <= 5005: // Unsettle 系列
			if order.DataState != 1 {
				logs.Error("IMOne SettleBet 撤销结算操作要求订单为已结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for unsettle")
			}

		// 取消操作：只能从未结算状态取消
		case actionId >= 6001 && actionId <= 6014: // Cancel 系列
			if order.DataState != -1 {
				logs.Error("IMOne SettleBet 取消操作要求订单为未结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for cancel")
			}

		// 撤销取消操作：只能从已撤单状态撤销取消
		case actionId >= 7001 && actionId <= 7014: // Uncancel 系列
			if order.DataState != -2 {
				logs.Error("IMOne SettleBet 撤销取消操作要求订单为已撤单状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for uncancel")
			}

		// 退款操作：只能从未结算状态退款
		case actionId == 2001 || actionId == 2011: // DangerRefund, LiveBallRefund
			if order.DataState != -1 {
				logs.Error("IMOne SettleBet 退款操作要求订单为未结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for refund")
			}

		// BetTrade 相关操作：根据具体操作类型判断
		case actionId == 3001: // BTBuyBack - 可以从未结算状态
			if order.DataState != -1 {
				logs.Error("IMOne SettleBet BT回购要求订单为未结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for bt buyback")
			}
		case actionId == 3002: // BTCancelBuyBack - 可以从已结算状态
			if order.DataState != 1 {
				logs.Error("IMOne SettleBet BT取消回购要求订单为已结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for bt cancel buyback")
			}
		case actionId == 3003: // BTRestoreBuyBack - 可以从未结算状态
			if order.DataState != -1 {
				logs.Error("IMOne SettleBet BT恢复回购要求订单为未结算状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for bt restore buyback")
			}
		case actionId == 3011: // LiveBallRefundRevert - 可以从已撤单状态
			if order.DataState != -2 {
				logs.Error("IMOne SettleBet 滚球退款还原要求订单为已撤单状态 thirdId=", thirdId, " 当前状态=", order.DataState)
				return fmt.Errorf("order status incorrect for refund revert")
			}

		default:
			logs.Warn("IMOne SettleBet 未定义的 ActionId=", actionId, " 跳过状态检查")
		}

		// 记录金额类型（正值存入余额，负值从余额扣除）
		amountType := "无变动"
		if winAmount > 0 {
			amountType = "存入余额"
		} else if winAmount < 0 {
			amountType = "扣除余额"
		}
		logs.Info("IMOne SettleBet 金额处理=", amountType, " 金额=", winAmount, " 操作=", operationType)

		settleTime := time.Now().Format("2006-01-02 15:04:05")
		thirdTime := transaction.SettlementDate
		if thirdTime == "" {
			thirdTime = settleTime
		}

		// 如果是重新结算，记录重新结算日志
		if isResettle {
			// 重新结算增加记录日志
			// 参数说明：
			// state: 1=撤销结算, 0=重新结算
			// changeType: 0=增加资金, 1=减少资金
			// winAmount: 结算金额（正数=派奖，负数=扣款）
			state := 1 // 撤销结算状态
			resettleTime := thirdTime
			changeType := 0 // 默认增加资金
			if winAmount <= 0 {
				changeType = 1 // 减少资金
			}
			memo := "重新结算"
			e := base.AddSportResettleLog(tx, order, thirdTime, string(reqDataByte), winAmount, changeType, state, transaction.TransactionId, resettleTime, "", memo)
			if e != nil {
				logs.Error("IMOne SettleBet 增加重新结算记录失败 userId=", order.UserId, " thirdId=", order.ThirdId, " e=", e.Error())
				// 记录重新结算失败，但不中断流程，继续处理结算
				logs.Warn("IMOne SettleBet 重新结算记录失败，继续处理结算 thirdId=", thirdId)
			} else {
				logs.Info("IMOne SettleBet 重新结算记录添加成功 userId=", userId, " thirdId=", thirdId)
			}
		}

		// 根据 finalDataState 确定处理逻辑
		var updateWinAmount, updateValidBet float64

		if finalDataState == -2 || finalDataState == -1 {
			// 已撤单或回到下注状态：WinAmount=0, ValidBet=0，但仍需处理余额变动
			updateWinAmount = 0
			updateValidBet = 0
			logs.Info("IMOne SettleBet 撤单/回到下注状态 thirdId=", thirdId, " 余额变动金额=", winAmount)
		} else if finalDataState == 1 {
			// 已结算状态：需要派奖操作
			updateWinAmount = winAmount
			updateValidBet = base.GetValidBet(order.BetAmount, winAmount)
			logs.Info("IMOne SettleBet 结算状态 thirdId=", thirdId, " 余额变动金额=", winAmount)
		} else {
			// 其他状态按结算处理
			updateWinAmount = winAmount
			updateValidBet = base.GetValidBet(order.BetAmount, winAmount)
			logs.Warn("IMOne SettleBet 未知状态 finalDataState=", finalDataState, " 余额变动金额=", winAmount)
		}

		if updateWinAmount <= 0 {
			updateWinAmount = 0
			updateValidBet = 0
		}
		// 更新预结算表订单状态
		// 根据是否为重新结算设置 ResettleState
		var resettleState int
		if isResettle {
			resettleState = 1 // 重新结算
		} else {
			resettleState = 0 // 普通结算
		}
		logs.Info("IMOne SettleBet 设置ResettleState thirdId=", thirdId, " isResettle=", isResettle, " resettleState=", resettleState)

		resultTmp := tx.Table(tablePre).Where("Id=?", order.Id).Updates(map[string]interface{}{
			"WinAmount":     updateWinAmount,
			"ValidBet":      updateValidBet,
			"DataState":     finalDataState,
			"ResettleState": resettleState,
			"ThirdTime":     thirdTime,
			"SettleTime":    settleTime,
		})
		err = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			err = fmt.Errorf("update pre order affected rows 0")
		}
		if err != nil {
			logs.Error("IMOne SettleBet 更新预结算订单状态失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
			return err
		}

		// 处理正式表数据
		if finalDataState == 1 {
			// 已结算状态：创建或更新正式表
			logs.Info("IMOne SettleBet 处理正式表数据 thirdId=", thirdId)

			// 准备正式表数据
			rawData := l.formatSettleTransactionContext(transaction)

			// 先尝试更新正式表
			result := tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"DataState":     1,
				"WinAmount":     updateWinAmount,
				"ValidBet":      updateValidBet,
				"ResettleState": resettleState,
				"ThirdTime":     thirdTime,
				"RawData":       rawData,
				"SettleTime":    settleTime,
			})

			if result.Error != nil || result.RowsAffected == 0 {
				// 如果更新失败或没有记录被更新，则创建新记录
				logs.Info("IMOne SettleBet 正式表不存在，创建新记录 thirdId=", thirdId)

				// 创建正式表订单记录
				formalOrder := order // 复制预结算表数据
				formalOrder.WinAmount = updateWinAmount
				formalOrder.ValidBet = updateValidBet
				formalOrder.DataState = 1
				formalOrder.ResettleState = resettleState
				formalOrder.ThirdTime = thirdTime
				formalOrder.RawData = rawData
				formalOrder.SettleTime = &settleTime
				formalOrder.Id = 0 // 重置ID，让数据库自动生成新ID

				err = tx.Table(table).Create(&formalOrder).Error
				if err != nil {
					logs.Error("IMOne SettleBet 创建正式表订单失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
					return err
				}
				logs.Info("IMOne SettleBet 创建正式表成功 thirdId=", thirdId)
			} else {
				logs.Info("IMOne SettleBet 更新正式表成功 thirdId=", thirdId)
			}
		} else if finalDataState == -2 || finalDataState == -1 {
			// 撤单或回到下注状态：如果正式表有数据就更新，无数据则不更新
			logs.Info("IMOne SettleBet 尝试更新正式表状态 thirdId=", thirdId, " finalDataState=", finalDataState)

			result := tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
				"DataState":     finalDataState,
				"WinAmount":     0,
				"ValidBet":      0,
				"ResettleState": resettleState,
				"ThirdTime":     thirdTime,
			})

			if result.Error != nil {
				logs.Error("IMOne SettleBet 更新正式表状态失败 thirdId=", thirdId, " err=", result.Error.Error())
				return result.Error
			} else if result.RowsAffected == 0 {
				logs.Info("IMOne SettleBet 正式表无数据，跳过更新 thirdId=", thirdId)
			} else {
				logs.Info("IMOne SettleBet 更新正式表状态成功 thirdId=", thirdId, " 受影响行数=", result.RowsAffected)
			}
		}

		// 查询用户余额（无论金额是否为0都需要查询）
		userBalance := thirdGameModel.UserBalance{}
		err = tx.Table("x_user").Where("UserId=?", userId).First(&userBalance).Error
		if err != nil {
			logs.Error("IMOne SettleBet 查询用户余额失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
			return err
		}

		// 处理用户余额变动（所有状态都需要处理余额变动）
		if winAmount != 0 {
			// 检查余额是否足够（仅在扣除金额时检查）
			if winAmount < 0 && userBalance.Amount < (-winAmount) {
				logs.Error("IMOne SettleBet 用户余额不足 userId=", userId, " 当前余额=", userBalance.Amount, " 需要扣除=", -winAmount)
				return fmt.Errorf("insufficient balance")
			}

			// 更新用户余额：正数存入，负数扣除
			resultTmp := tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount+?", winAmount), // 正数增加，负数减少
			})
			err = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				err = fmt.Errorf("update user balance affected rows 0")
			}
			if err != nil {
				logs.Error("IMOne SettleBet 更新用户余额失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
				return err
			}

			finalBalance = userBalance.Amount + winAmount

			// 记录详细的余额变动信息
			balanceChangeType := "存入"
			if winAmount < 0 {
				balanceChangeType = "扣除"
			}
			logs.Info("IMOne SettleBet 余额变动 userId=", userId, " 操作=", balanceChangeType, " 金额=", winAmount, " 变动前=", userBalance.Amount, " 变动后=", finalBalance)

			// 创建账变记录
			// 根据操作类型选择正确的 Reason 类型
			var reasonType int
			var reasonDesc string
			if isResettle {
				reasonType = utils.BalanceCReasonIMOneReSettle // 重新结算
				reasonDesc = "重新结算"
			} else if actionId == 2001 || actionId == 2011 || actionId == 3001 || actionId == 3002 || actionId == 3003 || (actionId >= 6001 && actionId <= 6014) {
				reasonType = utils.BalanceCReasonIMOneRefund // 退款操作（危险球、滚球、取消）
				reasonDesc = "退款"
			} else {
				reasonType = utils.BalanceCReasonIMOneSettle // 普通结算
				reasonDesc = "普通结算"
			}
			logs.Info("IMOne SettleBet 账变类型 thirdId=", thirdId, " reasonType=", reasonType, " reasonDesc=", reasonDesc, " actionId=", actionId)

			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       winAmount, // 保持原始金额（正数或负数）
				AfterAmount:  finalBalance,
				Reason:       reasonType,
				Memo:         fmt.Sprintf("%s %s(ActionId:%d), thirdId:%s", l.brandName, operationType, actionId, thirdId),
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			err = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if err != nil {
				logs.Error("IMOne SettleBet 创建账变失败 userId=", userId, " thirdId=", thirdId, " err=", err.Error())
				return err
			}
		} else {
			// 金额为0时，余额不变
			finalBalance = userBalance.Amount
			logs.Info("IMOne SettleBet 无余额变动 userId=", userId, " 金额=0, 余额=", finalBalance)
		}

		logs.Info("IMOne SettleBet 结算成功 thirdId=", thirdId)
		return nil
	})

	if err != nil {
		errStr := err.Error()
		if strings.Contains(errStr, "order not found") {
			return SettleBetResult{
				Code:          600,
				Message:       "Order not found.",
				TransactionId: transaction.TransactionId,
			}
		} else if strings.Contains(errStr, "order cancelled") {
			return SettleBetResult{
				Code:          601,
				Message:       "Order has been cancelled.",
				TransactionId: transaction.TransactionId,
			}
		} else if strings.Contains(errStr, "order status incorrect") {
			return SettleBetResult{
				Code:          602,
				Message:       "Order status is incorrect.",
				TransactionId: transaction.TransactionId,
			}
		} else if strings.Contains(errStr, "insufficient balance") {
			return SettleBetResult{
				Code:          510,
				Message:       "Insufficient amount.",
				TransactionId: transaction.TransactionId,
			}
		} else {
			return SettleBetResult{
				Code:          999,
				Message:       "System has failed to process your request.",
				TransactionId: transaction.TransactionId,
			}
		}
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][IMOne] SettleBet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][IMOne] SettleBet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	logs.Info("IMOne SettleBet 成功 userId=", userId, " TransactionId=", transaction.TransactionId, " balance=", finalBalance)

	return SettleBetResult{
		Code:                  0,
		Message:               "Successful.",
		OperatorTransactionId: fmt.Sprintf("settle_%s", transaction.TransactionId),
		TransactionId:         transaction.TransactionId,
		Balance:               finalBalance,
	}
}

// formatSettleTransactionContext 格式化结算交易上下文
func (l *IMOneSingleService) formatSettleTransactionContext(transaction SettleBetTransaction) string {
	data, _ := json.Marshal(transaction)
	return string(data)
}

// RefundTransaction 退款交易数据结构
type RefundTransaction struct {
	TransactionId    string `json:"TransactionId"`    // IM交易代码
	RefTransactionId string `json:"RefTransactionId"` // 被退款的交易代码
	TransactionType  string `json:"TransactionType"`  // 交易类别
	PlayerId         string `json:"PlayerId"`         // 玩家账号
	ProviderPlayerId string `json:"ProviderPlayerId"` // 供应商玩家账号
	Provider         string `json:"Provider"`         // 游戏供应商代码
	TimeStamp        string `json:"TimeStamp"`        // 时间戳
}

// RefundRequest 退款请求结构
type RefundRequest struct {
	Transactions []RefundTransaction `json:"Transactions" validate:"required"` // 交易列表
}

// RefundResult 退款结果结构
type RefundResult struct {
	Code                  int     `json:"Code"`                            // 子级代码
	Message               string  `json:"Message"`                         // 响应信息
	OperatorTransactionId string  `json:"OperatorTransactionId,omitempty"` // 运营商交易代码
	TransactionId         string  `json:"TransactionId"`                   // IM交易代码
	Balance               float64 `json:"Balance"`                         // 余额
}

// RefundResponse 退款响应结构
type RefundResponse struct {
	Code    int            `json:"Code"`    // 母级代码
	Message string         `json:"Message"` // 响应信息
	Results []RefundResult `json:"Results"` // 结果列表
}

// Refund 退款回调 - URL: /Refund
func (l *IMOneSingleService) Refund(ctx *abugo.AbuHttpContent) {
	reqdata := RefundRequest{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("IMOne Refund 请求参数错误 err=", err.Error())
		respData := RefundResponse{
			Code:    612,
			Message: "Invalid Argument.",
		}
		ctx.RespJson(respData)
		return
	}

	// 处理每个交易
	results := make([]RefundResult, len(reqdata.Transactions))

	for i, transaction := range reqdata.Transactions {
		result := l.processRefundTransaction(transaction)
		results[i] = result
	}

	respData := RefundResponse{
		Code:    0,
		Message: "Success",
		Results: results,
	}
	ctx.RespJson(respData)
}

// processRefundTransaction 处理单个退款交易
func (l *IMOneSingleService) processRefundTransaction(transaction RefundTransaction) RefundResult {
	// 解析用户ID
	userId := base.ParseUserIdFromPlayerName(transaction.PlayerId)
	if userId == 0 {
		logs.Error("IMOne Refund 解析用户ID失败 PlayerId=", transaction.PlayerId)
		return RefundResult{
			Code:          504,
			Message:       "Player does not exist.",
			TransactionId: transaction.TransactionId,
		}
	}

	var finalBalance float64

	// 开启事务处理退款
	err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询用户余额并锁定
		userBalance := thirdGameModel.UserBalance{}
		err := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).
			Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if err != nil {
			if errors.Is(err, daogorm.ErrRecordNotFound) {
				logs.Error("IMOne Refund 用户不存在 userId=", userId)
				return fmt.Errorf("player does not exist")
			}
			logs.Error("IMOne Refund 查询用户余额失败 userId=", userId, " err=", err.Error())
			return fmt.Errorf("query user balance failed")
		}

		// 检查退款交易是否已存在（幂等性）
		existingOrder := thirdGameModel.ThirdOrder{}
		err = tx.Table("x_third_sport").Where("ThirdId = ? AND Brand = ?", transaction.TransactionId, l.brandName).First(&existingOrder).Error
		if err == nil {
			// 交易已存在，返回现有余额
			finalBalance = userBalance.Amount
			logs.Info("IMOne Refund 交易已存在 TransactionId=", transaction.TransactionId)
			return nil
		} else if !errors.Is(err, daogorm.ErrRecordNotFound) {
			logs.Error("IMOne Refund 查询交易失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
			return fmt.Errorf("query transaction failed")
		}

		// 查询被退款的原始订单
		var originalOrder thirdGameModel.ThirdOrder
		var refId string

		if transaction.TransactionType == "Cancel" {
			// 对于Cancel类型，RefTransactionId是RoundId
			refId = transaction.RefTransactionId
			err = tx.Table("x_third_sport").Where("GameRoundId = ? AND Brand = ? AND UserId = ?", refId, l.brandName, userId).First(&originalOrder).Error
		} else {
			// 其他类型，RefTransactionId是TransactionId
			refId = transaction.RefTransactionId
			err = tx.Table("x_third_sport").Where("ThirdId = ? AND Brand = ?", refId, l.brandName).First(&originalOrder).Error
		}

		if err != nil {
			if errors.Is(err, daogorm.ErrRecordNotFound) {
				logs.Error("IMOne Refund 原始订单不存在 RefTransactionId=", transaction.RefTransactionId)
				return fmt.Errorf("reference transaction not found")
			}
			logs.Error("IMOne Refund 查询原始订单失败 RefTransactionId=", transaction.RefTransactionId, " err=", err.Error())
			return fmt.Errorf("query reference transaction failed")
		}

		// 计算退款金额
		var refundAmount float64
		switch transaction.TransactionType {
		case "CancelWager", "Cancel":
			// 取消下注，退还下注金额
			refundAmount = originalOrder.BetAmount
		case "CancelSettlement":
			// 取消结算，扣除派彩金额
			refundAmount = -originalOrder.WinAmount
		case "CancelTips", "CancelProviderTourFee", "CancelProviderBonus", "CancelCommission":
			// 取消各种类型的交易，根据原始金额处理
			if originalOrder.WinAmount > 0 {
				refundAmount = -originalOrder.WinAmount
			} else {
				refundAmount = originalOrder.BetAmount
			}
		case "CancelFRB":
			// 取消免费回合奖金，无需实际操作
			refundAmount = 0
		default:
			refundAmount = originalOrder.BetAmount
		}

		// 更新用户余额
		finalBalance = userBalance.Amount + refundAmount
		err = tx.Table("x_user").Where("UserId = ?", userId).Update("Amount", finalBalance).Error
		if err != nil {
			logs.Error("IMOne Refund 更新余额失败 userId=", userId, " err=", err.Error())
			return fmt.Errorf("update balance failed")
		}

		// 更新原始订单状态
		err = tx.Table("x_third_sport").Where("id = ?", originalOrder.Id).
			Updates(map[string]interface{}{
				"DataState":  -2, // 已退款
				"UpdateTime": time.Now().Format("2006-01-02 15:04:05"),
			}).Error
		if err != nil {
			logs.Error("IMOne Refund 更新原始订单失败 orderId=", originalOrder.Id, " err=", err.Error())
			return fmt.Errorf("update original order failed")
		}

		// 创建退款记录
		nowTime := time.Now()
		refundOrder := thirdGameModel.ThirdOrder{
			UserId:     userId,
			SellerId:   userBalance.SellerId,
			ChannelId:  userBalance.ChannelId,
			ThirdId:    transaction.TransactionId,
			GameName:   originalOrder.GameName,
			BetAmount:  0,
			WinAmount:  refundAmount,
			Currency:   originalOrder.Currency,
			DataState:  1, // 已处理
			RawData:    l.formatRefundTransactionContext(transaction),
			CreateTime: nowTime.Format("2006-01-02 15:04:05"),
			Brand:      l.brandName,
		}

		err = tx.Table("x_third_sport").Create(&refundOrder).Error
		if err != nil {
			logs.Error("IMOne Refund 创建退款订单失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
			return fmt.Errorf("create refund order failed")
		}

		// 创建账变记录
		if refundAmount != 0 {
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				Amount:       refundAmount,
				BeforeAmount: userBalance.Amount,
				AfterAmount:  finalBalance,
				Reason:       utils.BalanceCReasonIMOneRefund, // IMOne退款
				CreateTime:   nowTime.Format("2006-01-02 15:04:05"),
				Memo:         fmt.Sprintf("IMOne退款: %s", transaction.TransactionId),
			}

			err = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if err != nil {
				logs.Error("IMOne Refund 创建账变记录失败 TransactionId=", transaction.TransactionId, " err=", err.Error())
				return fmt.Errorf("create amount log failed")
			}
		}

		return nil
	})

	if err != nil {
		errStr := err.Error()
		if strings.Contains(errStr, "player does not exist") {
			return RefundResult{
				Code:          504,
				Message:       "Player does not exist.",
				TransactionId: transaction.TransactionId,
			}
		} else if strings.Contains(errStr, "reference transaction not found") {
			return RefundResult{
				Code:          545,
				Message:       "TransactionId is not found at Operator side.",
				TransactionId: transaction.TransactionId,
			}
		} else {
			return RefundResult{
				Code:          999,
				Message:       "System has failed to process your request.",
				TransactionId: transaction.TransactionId,
			}
		}
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][IMOne] Refund 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][IMOne] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)

	logs.Info("IMOne Refund 成功 userId=", userId, " TransactionId=", transaction.TransactionId, " balance=", finalBalance)

	return RefundResult{
		Code:                  0,
		Message:               "Successful",
		OperatorTransactionId: fmt.Sprintf("refund_%s", transaction.TransactionId),
		TransactionId:         transaction.TransactionId,
		Balance:               finalBalance,
	}
}

// formatRefundTransactionContext 格式化退款交易上下文
func (l *IMOneSingleService) formatRefundTransactionContext(transaction RefundTransaction) string {
	data, _ := json.Marshal(transaction)
	return string(data)
}
