package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand/v2"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/active"
	"xserver/controller/datapush"
	"xserver/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/imroc/req"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

const (
	RedisKeyFakerUser                     = "hashgame:faker:user"
	RedisKeyFakerUserResp                 = "hashgame:faker:user:resp"
	RedisKeyFakerTotal                    = "hashgame:faker:totalCommission"
	RdsKeyLockTotalCommissionTask         = "hashgame:lock:totalCommission"
	FakerTotalCommissionValue     float64 = 726413.38
)

type FakerRedisItem struct {
	UserId  int64  `gorm:"column:UserId" json:"userId"`   // 用户uid
	Account string `gorm:"column:Account" json:"account"` // 用户名
}

type RTRewardsListReq struct {
}

type RTRewardsListItem struct {
	UserId   int64   `gorm:"column:userId" json:"userId"`     // 用户uid
	UserName string  `gorm:"column:userName" json:"userName"` // 用户名
	Currency string  `gorm:"column:currency" json:"currency"` // 币种
	Amount   float64 `gorm:"column:amount" json:"amount"`     // 余额
}

type RTRewardsListResp struct {
	TotalCommission float64              `gorm:"column:totalCommission" json:"totalCommission"` // 平台佣金奖励总获得
	List            []*RTRewardsListItem `gorm:"column:-" json:"list"`
}

// 三级代: 实时奖励
func (c *AgentController) rt_rewards(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RTRewardsListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	//token := server.GetToken(ctx)
	//if token.UserId <= 0 {
	//	errcode = 100
	//	ctx.RespErr(errors.New("noauth"), &errcode)
	//	return
	//}

	respData := c.GetFakerData()
	ctx.RespOK(respData)
}

// 从db随机获取用户名称
func (c *AgentController) GetFakerUserFromDB(count int) {
	//SELECT UserId,Account FROM x_user
	//ORDER BY RAND()
	//LIMIT 10;

	accountList := make([]*FakerRedisItem, 0)
	rawSql := `SELECT UserId,Account FROM x_user
ORDER BY RAND()
LIMIT %d`
	strSql := fmt.Sprintf(rawSql, count)
	server.Db().GormDao().Raw(strSql).Find(&accountList)
	if len(accountList) == 0 {
		return
	}
	strJsonArr := make([]string, 0)
	for _, val := range accountList {
		str, _ := json.Marshal(val)
		strJsonArr = append(strJsonArr, string(str))
	}
	ctx := context.Background()
	if len(strJsonArr) > 0 {
		server.RdbC.SAdd(ctx, RedisKeyFakerUser, strJsonArr)
		server.RdbC.Expire(ctx, RedisKeyFakerUser, time.Hour*24*3)
		server.RdbC.SetNX(ctx, RedisKeyFakerTotal, FakerTotalCommissionValue, 0)
	}

}

// 刷新faker数据
func (c *AgentController) FlashFakerData(count int64) []*RTRewardsListItem {
	ctx := context.Background()
	respDataArr := make([]*RTRewardsListItem, 0)
	fakerArr, err := server.RdbC.SRandMemberN(ctx, RedisKeyFakerUser, count).Result()
	if err != nil || len(fakerArr) <= 0 {
		logs.Debug("刷新faker数据错误", err)
		c.GetFakerUserFromDB(1000)
		fakerArr, _ = server.RdbC.SRandMemberN(ctx, RedisKeyFakerUser, count).Result()
	}
	if len(fakerArr) > 0 {

		for _, v := range fakerArr {
			faker := new(FakerRedisItem)
			err1 := json.Unmarshal([]byte(v), faker)
			if err1 != nil {
				continue
			}
			item := new(RTRewardsListItem)
			item.UserName = faker.Account
			item.UserId = faker.UserId
			if 0 == rand.IntN(3) {
				item.Currency = "trx"
			} else {
				item.Currency = "usdt"
			}
			rdv := rand.IntN(200) + 1
			amount := float64(rdv) / 100
			item.Amount = math.Round(amount*100) / 100
			respDataArr = append(respDataArr, item)
		}
		if len(respDataArr) > 0 {
			strResp, _ := json.Marshal(respDataArr)
			server.RdbC.SetEx(ctx, RedisKeyFakerUserResp, strResp, time.Minute*3)
		}
	}
	return respDataArr
}

// 获取faker数据
func (c *AgentController) GetFakerData() *RTRewardsListResp {
	ctx := context.Background()
	data := &RTRewardsListResp{}

	strData, err := server.RdbC.Get(ctx, RedisKeyFakerUserResp).Result()
	if len(strData) > 0 {
		itemArr := make([]*RTRewardsListItem, 0)
		json.Unmarshal([]byte(strData), &itemArr)
		data.List = itemArr
	} else {
		logs.Debug("获取faker数据失败：%v", err)
		data.List = c.FlashFakerData(20)
	}

	strTotal, _ := server.RdbC.Get(ctx, RedisKeyFakerTotal).Result()
	if len(strTotal) > 0 {
		data.TotalCommission = abugo.GetFloat64FromInterface(strTotal)
	}
	if data.TotalCommission <= FakerTotalCommissionValue {
		c.CheckTotalCommissionTaskJob()
	}
	return data
}

// 在初始化时注册定时任务
func (this *AgentController) RegisterStatsTaskJob() {
	// 注册每日凌晨2点执行的任务
	spec := "0 0 2 * * *" // cron 表达式，表示每天 2:00 执行一次
	id, err := server.RegisterCronJob(spec, this.CheckTotalCommissionTaskJob)
	if err != nil {
		logs.Error("实时平台总佣金数据统计任务注册失败：%v", err)
	} else {
		logs.Info("实时平台总佣金数据统计任务注册成功，任务ID：%d", id)
	}
}

func (this *AgentController) CheckTotalCommissionTaskJob() {
	// 使用 Redis 实现分布式锁
	lockKey := RdsKeyLockTotalCommissionTask // 锁的 key
	expireTime := 10 * time.Minute           // 锁的过期时间

	lockValue := fmt.Sprintf("locked_by_this_instance:%d", 1) // 锁的值（可随机生成以避免冲突）
	// 尝试获取锁
	locked, err := server.RdbC.SetNX(context.Background(), lockKey, lockValue, expireTime).Result()
	if err != nil {
		logs.Error("获取分布式锁失败：%v", err)
		return
	}

	// 如果没有获取到锁，则直接返回
	if !locked {
		logs.Info("当前实例未获取到分布式锁，跳过执行计任务")
		return
	}

	//defer func() {
	//	_, delErr := server.RdbC.Del(context.Background(), lockKey).Result()
	//	if delErr != nil {
	//		logs.Error("释放分布式锁失败：%v", delErr)
	//	}
	//}()

	//方案1：
	//
	//起始金额：100万美金
	//
	//真实每日佣金（美元）*增长系数=1万美金 ~ 5万美金
	//波动因子：0.95 ~ 1.08
	//2:又修改为==>累计已发放奖励计算公式：累计已发放奖励金额=基础金额10000 USDT+(每日佣金金额 × 1000 × 3.1415926)
	//方案二：
	//
	//
	//
	//每日增长浮动= 随机值（每日增长范围） × 随机浮动系数（0.95 ~ 1.10）
	//
	//每日最小增长：2000 USDT
	//
	//每日最大增长：8000 USDT
	//
	//每日波动系数：0.95~1.10
	//
	//
	//
	//精确到小数点后2位
	//
	//初始值：726413.38 USDT
	//
	//
	//
	//
	//
	//1年下来的值大概会在  1,420,000  USDT~ 3,940,000 USDT 之间； 可以根据公司需求随时对每日增长范围进行调整。增快或者放缓
	//

	//now := time.Now()
	ctx := context.Background()
	// 计算昨天的日期
	//yesterday := now.AddDate(0, 0, -1)
	//yesterdayString := yesterday.Format("2006-01-02")
	//amountDate := this.GetPlatTotalCommissionDate(yesterdayString)
	//incrAmount := amountDate * 1000 * 3.1415926
	//if incrAmount <= 0 {
	//	incrAmount = 1000 * 3.1415926
	//}
	rdn := rand.IntN(8000-2000) + 2000
	rdf := rand.IntN(110-95) + 95
	incrAmount := float64(rdn) * float64(rdf/100)

	incrAmount = math.Round(incrAmount*100) / 100
	server.RdbC.SetNX(ctx, RedisKeyFakerTotal, FakerTotalCommissionValue, 0)
	if incrAmount > 0 {
		server.RdbC.IncrByFloat(ctx, RedisKeyFakerTotal, incrAmount)
	}
}

type MyRewardsListReq struct {
}

type MyRewardsListResp struct {
	TotalCommission         float64 `gorm:"column:totalCommission" json:"totalCommission"`                 // 佣金奖励 总获得
	CommissionAvailable     float64 `gorm:"column:commissionAvailable" json:"commissionAvailable"`         // 佣金奖励 可用奖励
	CommissionTrx           float64 `gorm:"column:commissionTrx" json:"commissionTrx"`                     // 佣金奖励 trx
	CommissionAvailableTrx  float64 `gorm:"column:commissionAvailableTrx" json:"commissionAvailableTrx"`   // 佣金奖励 trx 可用
	CommissionUSDT          float64 `gorm:"column:commissionUSDT" json:"commissionUSDT"`                   // 佣金奖励 usdt
	CommissionAvailableUSDT float64 `gorm:"column:commissionAvailableUSDT" json:"commissionAvailableUSDT"` // 佣金奖励 usdt 可用
	TotalInvitation         float64 `gorm:"column:totalInvitation" json:"totalInvitation"`                 // 邀请奖励 总获得
	InvitationAvailable     float64 `gorm:"column:invitationAvailable" json:"invitationAvailable"`         // 邀请奖励 可用奖励
	InvitationCount         int     `gorm:"column:invitationCount" json:"invitationCount"`                 // 邀请奖励 已邀请人数
	InvitationLocked        float64 `gorm:"column:invitationLocked" json:"invitationLocked"`               // 邀请奖励 已锁定余额
}

// 我的奖励
func (c *AgentController) my_rewards(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MyRewardsListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	data := new(MyRewardsListResp)
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ******** //86621045  38976126
	}
	// 获取佣金奖励usdt
	//tdb := server.Db().GormDao().Table("x_agent_commission_data_date")
	//tdb.Where("AgentId = ?", token.UserId)
	//tdb.Where("GetStatus = 1")
	//tdb.Select("ROUND(SUM(CommissionAmount),2) AS totalCommission")
	//
	//if err := tdb.Scan(&commissionU).Error; err != nil {
	//	logs.Error("三级代:我的奖励查询错误", err)
	//	//ctx.RespErr(err, &errcode)
	//}

	currency := "usdt"
	tdb := server.Db().GormDao().Table("x_commission_audit")
	tdb.Where("UserId = ?", token.UserId)
	tdb.Where("Symbol = ?", currency)
	tdb.Where("State = 4")
	tdb.Select("ROUND(SUM(Amount),2) AS commissionUSDT")

	if err := tdb.Scan(data).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}

	// 获取佣金奖励 trx 转usdt
	currency = "trx"
	commissionT := float64(0)
	//tdbT := server.Db().GormDao().Table("x_agent_commission_data_date")
	//tdbT.Where("AgentId = ?", token.UserId)
	//tdbT.Where("CommissionTrxAmount = 1")
	//tdbT.Select("ROUND(SUM(CommissionTrxAmount * TrxRate),2) AS totalCommission")

	tdbT := server.Db().GormDao().Table("x_commission_audit")
	tdbT.Where("UserId = ?", token.UserId)
	tdbT.Where("Symbol = ?", currency)
	tdbT.Where("State = 4")
	tdbT.Select("ROUND(SUM(Amount * TrxPrice),2)")

	if err := tdbT.Scan(&commissionT).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}
	data.TotalCommission = data.CommissionUSDT + commissionT

	//tdb2 := server.Db().GormDao().Table("x_agent_commission_data_date")
	//tdb2.Where("AgentId = ?", token.UserId)
	//tdb2.Where("CommissionTrxAmount = 1")
	//tdb2.Select("ROUND(SUM(CommissionTrxAmount),2) AS commissionTrx")

	tdb2 := server.Db().GormDao().Table("x_commission_audit")
	tdb2.Where("UserId = ?", token.UserId)
	tdb2.Where("Symbol = ?", currency)
	tdb2.Where("State = 4")
	tdb2.Select("ROUND(SUM(Amount),2) AS commissionTrx")
	if err := tdb2.Scan(data).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}

	// 获取可用佣金奖励usdt
	tdbT2 := server.Db().GormDao().Table("x_agent_commission_data_date")
	tdbT2.Where("AgentId = ?", token.UserId)
	tdbT2.Where("GetStatus = 2")
	tdbT2.Select("ROUND(SUM(CommissionAmount),2) AS commissionAvailableUSDT")

	if err := tdbT2.Scan(data).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}

	// 获取可用佣金奖励trx
	tdbT3 := server.Db().GormDao().Table("x_agent_commission_data_date")
	tdbT3.Where("AgentId = ?", token.UserId)
	tdbT3.Where("GetTrxStatus = 2")
	tdbT3.Select("ROUND(SUM(CommissionTrxAmount * TrxRate),2) AS commissionAvailable",
		"ROUND(SUM(CommissionTrxAmount),2) AS commissionAvailableTrx")

	if err := tdbT3.Scan(data).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}

	// 获取总的可用佣金奖励: usdt + trx
	data.CommissionAvailable = data.CommissionAvailable + data.CommissionAvailableUSDT
	// 获取邀请奖励
	tdb3 := server.Db().GormDao().Table("x_active_reward_audit")
	tdb3.Where("UserId = ?", token.UserId)
	tdb3.Where("ActiveId = 54")
	tdb3.Where("AuditState = 3 OR AuditState = 4")
	tdb3.Select("ROUND(SUM(Amount),2) AS totalInvitation")

	if err := tdb3.Scan(data).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}

	// 获取已邀请人数
	tdb5 := server.Db().GormDao().Table("x_user")
	tdb5.Where("UserId = ?", token.UserId)
	total := int64(0)
	if err := tdb5.Select("InviteRewardUsers").Scan(&total).Error; err != nil {
		logs.Error("三级代:我的奖励查询错误", err)
		//ctx.RespErr(err, &errcode)
	}
	data.InvitationCount = int(total)

	// 获取可用邀请奖励
	//tdb4 := server.Db().GormDao().Table("x_active_reward_audit")
	//tdb4.Where("UserId = ?", token.UserId)
	//tdb4.Where("ActiveId = 54")
	//tdb4.Where("AuditState = 1")
	//tdb4.Select("ROUND(SUM(Amount),2) AS invitationAvailable")
	//
	//if err := tdb4.Scan(data).Error; err != nil {
	//	logs.Error("三级代:我的奖励查询错误", err)
	//	//ctx.RespErr(err, &errcode)
	//}
	if data.InvitationCount > 0 {
		data.InvitationAvailable = c.InvitationAvailable(data.InvitationCount, token)
	}

	ctx.RespOK(data)
}

// 获取邀请奖励的可用奖励
func (c *AgentController) InvitationAvailable(total int, token *server.TokenData) float64 {
	available := float64(0)
	err, cfgArr := c.GetInvitationConfig(token)
	if err != nil {
		logs.Error("获取邀请奖励的配置错误", err)
	}
	for _, v := range cfgArr {
		if v.TotalMax > int32(total) {
			continue
		}
		// 该等级对应的奖励是否已经领取
		if !c.CheckInvitationApply(v.ID, token) {
			tv, _ := v.AdditionalReward.Float64()
			available = available + tv
		}
	}

	return available
}

// 邀请奖励:获取活动配置
func (c *AgentController) GetInvitationConfig(token *server.TokenData) (error, []active.RecommendFriendRewardConfig) {
	now := carbon.Parse(carbon.Now().String()).StdTime()
	gdb := server.Db().Gorm()
	ADefine := ciRiTodayADefine(gdb, now, token, 54)
	// 是否活动失效
	_, err := active.VerifyActiveDefine(ADefine, now)
	if err != nil {
		return err, nil
	}
	var configData []active.RecommendFriendRewardConfig
	err = json.Unmarshal([]byte(ADefine.Config), &configData)
	if err != nil {
		return errors.New("活动配置有误"), configData
	}
	return nil, configData
}

func CiRiTodayADefine(gdb *gorm.DB, now time.Time, token *server.TokenData, ActiveId int) model.ActiveDefine {
	aDefine := model.ActiveDefine{}
	err := gdb.Table(utils.TableActiveDefine).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ?", token.SellerId, token.ChannelId, ActiveId).
		First(&aDefine).Error
	if err != nil {
		return aDefine
	}
	if utils.IsActiveTest {
		return aDefine
	}

	today := utils.FormatDate(now)
	list := []model.ActiveDefineOld{}
	err = gdb.Table(utils.TableActiveDefineOld).
		Where("SellerId = ? and ChannelId = ? and ActiveId = ? and UpdateDate in (?)",
			token.SellerId, token.ChannelId, ActiveId, []string{today}).
		Find(&list).Error
	//如果存在则使用最后两个查看是否有昨天的记录
	if err == nil {
		defineMap := make(map[string]model.ActiveDefine)
		for _, old := range list {
			defineMap[string(old.UpdateDate)] = old.ActiveDefine
		}
		if v, ok := defineMap[today]; ok {
			return v
		}
	}

	return aDefine
}

// 邀请奖励:检查是否已经申请
func (c *AgentController) CheckInvitationApply(level int32, token *server.TokenData) bool {
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := server.DaoxHashGame().XActiveRewardAudit.WithContext(context.Background())
	activeRewardAudit, _ := activeRewardAuditDb.Where(activeRewardAuditTb.ActiveID.Eq(54)).
		Where(activeRewardAuditTb.UserID.Eq(int32(token.UserId))).
		Where(activeRewardAuditTb.ActiveLevel.Eq(level)).
		Where(activeRewardAuditTb.InviteRewardType.Eq(2)).First()

	if activeRewardAudit != nil && activeRewardAudit.ID > 0 {
		return true
	}
	return false
}

type CommissionListReq struct {
	Page     int `json:"page"`     // 页码
	PageSize int `json:"pageSize"` // 页大小
}

type CommissionListResp struct {
	RecordDate *time.Time `gorm:"column:RecordDate" json:"recordDate"` // 日期
	Currency   string     `gorm:"column:currency" json:"currency"`     // 币种
	Amount     float64    `gorm:"column:Amount" json:"amount"`         // 金额
	RewardType int        `gorm:"column:Type" json:"rewardType"`       // 类型: 1.佣金 2.邀请
}

// 领取佣金明细
func (c *AgentController) receive_commission_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommissionListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = 38976126 // ********
	}
	list := make([]*CommissionListResp, 0)
	total := int64(0)
	strSql := c.GetReceiveListSql(token.UserId)
	querySql := server.Db().GormDao().Raw(strSql)

	// 扫描结果到list
	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("领取佣金明细执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	strSql = c.GetReceiveCountSql(token.UserId)
	querySql = server.Db().GormDao().Raw(strSql)

	// 扫描结果到data
	if err = querySql.Scan(&total).Error; err != nil {
		logs.Error("领取佣金明细执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 获取领取明细sql
func (c *AgentController) GetReceiveListSql(uid int) string {
	//SELECT
	//RecordDate,
	//	Currency,
	//	Amount,
	//	Type
	//FROM (
	//	SELECT
	//SendTime AS RecordDate,
	//	Symbol AS Currency,
	//	COALESCE(Amount, 0) AS Amount,
	//	1 AS Type
	//FROM x_commission_audit
	//WHERE UserId = 123
	//AND State=4
	//
	//UNION ALL
	//
	//SELECT
	//RecordDate,
	//	'trx' AS Currency,
	//	COALESCE(Amount, 0) AS Amount,
	//	2 AS Type
	//FROM x_active_reward_audit
	//WHERE UserId = 123
	//AND ActiveId=7
	//) AS combined_data
	//ORDER BY RecordDate DESC

	rawSql := `SELECT 
    RecordDate,
    Currency,
    Amount,
    Type
FROM (
    SELECT 
        SendTime AS RecordDate,
        Symbol AS Currency,
        COALESCE(Amount, 0) AS Amount,
        1 AS Type
    FROM x_commission_audit
    WHERE UserId = %d
    AND State=4
    AND AgentMode=2
) AS combined_data
ORDER BY RecordDate DESC 
`

	strSql := fmt.Sprintf(rawSql, uid)
	return strSql
}

// 获取领取明细sql count
func (c *AgentController) GetReceiveCountSql(uid int) string {
	//SELECT
	//COUNT(*) AS TotalRecords
	//FROM (
	//	SELECT
	//AuditTime AS RecordDate,
	//	COALESCE(Amount, 0) AS Amount,
	//	1 AS Type
	//FROM x_commission_audit
	//WHERE UserId = 123
	//AND State=4
	//
	//UNION ALL
	//
	//SELECT
	//AuditTime AS RecordDate,
	//	COALESCE(Amount, 0) AS Amount,
	//	2 AS Type
	//FROM x_active_reward_audit
	//WHERE UserId = 123
	//AND ActiveId=7
	//) AS combined_data;

	rawSql := `SELECT 
    COUNT(*) AS TotalRecords
FROM (
    SELECT 
        AuditTime AS RecordDate,
        COALESCE(Amount, 0) AS Amount,
        1 AS Type
    FROM x_commission_audit
    WHERE UserId = %d
     AND State=4
     AND AgentMode=2
) AS combined_data
`

	strSql := fmt.Sprintf(rawSql, uid)
	return strSql
}

type WithdrawListReq struct {
	Page     int `json:"page"`     // 页码
	PageSize int `json:"pageSize"` // 页大小
	AgentId  int `json:"agentId"`  // 代理 id
}

type WithdrawListResp struct {
	RecordDate *time.Time `gorm:"column:recordDate" json:"recordDate"` // 日期
	Currency   string     `gorm:"column:currency" json:"currency"`     // 币种
	Amount     float64    `gorm:"column:amount" json:"amount"`         // 金额
	Status     int        `gorm:"column:status" json:"status"`         // 状态
}

// 提取明细
func (c *AgentController) withdraw_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := WithdrawListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	//token := server.GetToken(ctx)
	//data, _ := server.XDb().Table("x_user_reward_commission").Select("GetCommissionAmount").Where("UserId=?", token.UserId).Find()
	//if data == nil {
	//	return
	//}
	rt := time.Now()
	list := make([]*WithdrawListResp, 0)
	for i := 0; i < 5; i++ {
		list = append(list, &WithdrawListResp{
			RecordDate: &rt,
			Currency:   "USDT",
			Amount:     float64(100*i + 100),
			Status:     1,
		})
	}
	total := int64(5)
	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

type InviteRewardReq struct {
	Page      int   `json:"page"`      // 页码
	PageSize  int   `json:"pageSize"`  // 页大小
	StartTime int64 `json:"startTime"` // 开始时间
	EndTime   int64 `json:"endTime"`   // 结束时间
}

type InviteRewardResp struct {
	UserId   int64   `gorm:"column:userId" json:"userId"`     // 用户uid
	UserName string  `gorm:"column:userName" json:"userName"` // 用户名
	VipLv    int     `gorm:"column:vipLv" json:"vipLv"`       // VIP等级
	Amount   float64 `gorm:"column:amount" json:"amount"`     // 金额
}

// 邀请奖励
func (c *AgentController) invite_reward(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := InviteRewardReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ********
	}
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	list := make([]*InviteRewardResp, 0)
	total := int64(0)
	strSql := c.GetInviteRewardSql(token.UserId, reqdata.StartTime, reqdata.EndTime, limit, offset)
	querySql := server.Db().GormDao().Raw(strSql)

	// 扫描结果到list
	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("奖励佣金执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	strSql = c.GetInviteRewardCountSql(token.UserId, reqdata.StartTime, reqdata.EndTime)
	querySql = server.Db().GormDao().Raw(strSql)

	// 扫描结果到data
	if err = querySql.Scan(&total).Error; err != nil {
		logs.Error("领取佣金明细执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 获取邀请奖励sql
func (c *AgentController) GetInviteRewardSql(uid int, startTime, endTime int64, limit, offset int) string {
	//SELECT U.UserId, U.NickName AS UserName, U.AgentId, V.VipLevel AS VipLv, A.Amount AS Amount
	//FROM x_user AS U
	//LEFT JOIN x_vip_info AS V ON  U.UserId = V.UserId
	//LEFT JOIN x_active_reward_audit AS A ON  U.UserId = A.ChildUserId
	//WHERE U.AgentId = 85331509
	//AND A.ActiveId=54
	//AND A.InviteRewardType=1;

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND U.RegisterTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND U.RegisterTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	rawSql := `SELECT U.UserId AS userId, U.NickName AS userName, V.VipLevel AS vipLv, A.Amount AS amount
FROM x_user AS U
LEFT JOIN x_vip_info AS V ON  U.UserId = V.UserId
LEFT JOIN x_active_reward_audit AS A ON  U.UserId = A.ChildUserId
WHERE U.AgentId = %d
AND A.ActiveId=54
AND (AuditState = 3 OR AuditState = 4)
%s
%s
LIMIT %d
OFFSET %d
`

	strSql := fmt.Sprintf(rawSql, uid,
		strStartTime, strEndTime, limit, offset)

	return strSql
}

// 获取邀请奖励sql
func (c *AgentController) GetInviteRewardCountSql(uid int, startTime, endTime int64) string {
	//SELECT COUNT(1) AS TotalCount
	//FROM x_user AS U
	//LEFT JOIN x_vip_info AS V ON U.UserId = V.UserId
	//LEFT JOIN x_active_reward_audit AS A ON U.UserId = A.ChildUserId
	//WHERE U.AgentId = %d
	//AND A.ActiveId = 54
	//AND A.InviteRewardType = 1;

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND U.RegisterTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND U.RegisterTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	rawSql := `SELECT COUNT(1) AS TotalCount
FROM x_user AS U
LEFT JOIN x_vip_info AS V ON U.UserId = V.UserId
LEFT JOIN x_active_reward_audit AS A ON U.UserId = A.ChildUserId
WHERE U.AgentId = %d
  AND A.ActiveId = 54
  AND (AuditState = 3 OR AuditState = 4)
  %s
  %s
`

	strSql := fmt.Sprintf(rawSql, uid,
		strStartTime, strEndTime)

	return strSql
}

type RewardListReq struct {
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 页大小
	RewardType int   `json:"rewardType"` // 类型: 1.佣金 2.邀请
	StartTime  int64 `json:"startTime"`  // 开始时间
	EndTime    int64 `json:"endTime"`    // 结束时间
}

type RewardListResp struct {
	RecordDate *time.Time `gorm:"column:recordDate" json:"recordDate"` // 日期
	Currency   string     `gorm:"column:currency" json:"currency"`     // 币种
	Amount     float64    `gorm:"column:amount" json:"amount"`         // 金额
	RewardType int        `gorm:"column:rewardType" json:"rewardType"` // 类型: 1.佣金 2.邀请
}

// 奖励明细
func (c *AgentController) reward_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := RewardListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = 38976126 //********
	}

	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize

	list := make([]*RewardListResp, 0)
	total := int64(0)
	strSql := c.GetRewardListSql(token.UserId, reqdata.RewardType,
		"", reqdata.StartTime, reqdata.EndTime, limit, offset)
	querySql := server.Db().GormDao().Raw(strSql)

	// 扫描结果到list
	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("奖励佣金明细执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	strSql = c.GetRewardCountSql(token.UserId, reqdata.RewardType, "",
		reqdata.StartTime, reqdata.EndTime)
	querySql = server.Db().GormDao().Raw(strSql)

	// 扫描结果到data
	if err = querySql.Scan(&total).Error; err != nil {
		logs.Error("领取佣金明细执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 获取奖励明细sql
func (c *AgentController) GetRewardListSql(uid, rewardType int, currency string,
	startTime, endTime int64, limit, offset int) string {
	//SELECT
	//RecordDate,
	//	Currency,
	//	Amount,
	//	Type
	//FROM (
	//	SELECT
	//SendTime AS RecordDate,
	//	Symbol AS Currency,
	//	COALESCE(Amount, 0) AS Amount,
	//	1 AS Type
	//FROM x_commission_audit
	//WHERE UserId = 123
	//AND State=4
	//
	//UNION ALL
	//
	//SELECT
	//RecordDate,
	//	'trx' AS Currency,
	//	COALESCE(Amount, 0) AS Amount,
	//	2 AS Type
	//FROM x_active_reward_audit
	//WHERE UserId = 123
	//AND ActiveId=7
	//) AS combined_data
	//ORDER BY RecordDate DESC

	// 币种
	strCurrency := ""
	if len(currency) > 0 {
		strCurrency = fmt.Sprintf(" AND Symbol = %s", currency)
	}
	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND SendTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND SendTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	//时间区间
	strStartTime2 := ""
	if startTime > 0 {
		strStartTime2 = fmt.Sprintf("AND AuditTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime2 := ""
	if endTime > 0 && endTime > startTime {
		strEndTime2 = fmt.Sprintf("AND AuditTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	strSql := ""
	switch rewardType {
	case 0:
		{
			rawSql := `SELECT 
    RecordDate,
    Currency,
    Amount,
    rewardType
FROM (
    SELECT 
        SendTime AS RecordDate,
        Symbol AS Currency,
        COALESCE(Amount, 0) AS Amount,
        1 AS rewardType
    FROM x_commission_audit
    WHERE UserId = %d
    AND State=4
    AND AgentMode=2
	%s
    %s
    %s
    
    UNION ALL
    
    SELECT 
        RecordDate,
        'usdt' AS Currency,
        COALESCE(Amount, 0) AS Amount,
        2 AS rewardType
    FROM x_active_reward_audit
    WHERE UserId = %d
    AND ActiveId=54
    AND (AuditState = 3 OR AuditState = 4)
    %s
    %s
) AS combined_data
ORDER BY RecordDate DESC 
LIMIT %d
OFFSET %d
`
			strSql = fmt.Sprintf(rawSql, uid,
				strCurrency, strStartTime, strEndTime,
				uid, strStartTime2, strEndTime2, limit, offset)

		}
	case 1:
		{
			rawSql := `SELECT
SendTime AS RecordDate,
Symbol AS Currency,
COALESCE(Amount, 0) AS Amount,
1 AS rewardType
FROM
x_commission_audit
WHERE
UserId = %d
    %s
    %s
AND State = 4
AND AgentMode=2
LIMIT %d
OFFSET %d
`

			strSql = fmt.Sprintf(rawSql, uid,
				strStartTime, strEndTime, limit, offset)

		}
	case 2:
		{
			rawSql := `SELECT
AuditTime AS RecordDate,
'usdt' AS Currency,
COALESCE(Amount, 0) AS Amount,
2 AS rewardType
FROM
x_active_reward_audit
WHERE
UserId = %d
	%s
	%s
AND ActiveId = 54
AND (AuditState = 3 OR AuditState = 4)
LIMIT %d
OFFSET %d
`

			strSql = fmt.Sprintf(rawSql, uid,
				strStartTime2, strEndTime2, limit, offset)

		}
	}

	return strSql
}

// 获取奖励明细sql count
func (c *AgentController) GetRewardCountSql(uid, rewardType int, currency string, startTime, endTime int64) string {
	//SELECT
	//COUNT(*) AS TotalRecords
	//FROM (
	//	SELECT
	//AuditTime AS RecordDate,
	//	COALESCE(Amount, 0) AS Amount,
	//	1 AS Type
	//FROM x_commission_audit
	//WHERE UserId = 123
	//AND State=4
	//
	//UNION ALL
	//
	//SELECT
	//AuditTime AS RecordDate,
	//	COALESCE(Amount, 0) AS Amount,
	//	2 AS Type
	//FROM x_active_reward_audit
	//WHERE UserId = 123
	//AND ActiveId=7
	//) AS combined_data;

	// 币种
	strCurrecy := ""
	if len(currency) > 0 {
		strCurrecy = fmt.Sprintf(" AND Symbol = %s", currency)
	}
	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND SendTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND SendTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	//时间区间
	strStartTime2 := ""
	if startTime > 0 {
		strStartTime2 = fmt.Sprintf("AND AuditTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime2 := ""
	if endTime > 0 && endTime > startTime {
		strEndTime2 = fmt.Sprintf("AND AuditTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	strSql := ""

	switch rewardType {
	case 0:
		{
			rawSql := `SELECT 
    COUNT(*) AS TotalRecords
FROM (
    SELECT 
        AuditTime AS RecordDate,
        COALESCE(Amount, 0) AS Amount,
        1 AS rewardType
    FROM x_commission_audit
    WHERE UserId = %d
        %s
        %s
        %s
     AND State=4
     AND AgentMode=2
    
    UNION ALL
    
    SELECT 
        AuditTime AS RecordDate,
        COALESCE(Amount, 0) AS Amount,
        2 AS rewardType
    FROM x_active_reward_audit
    WHERE UserId = %d
    AND ActiveId=54
    AND (AuditState = 3 OR AuditState = 4)
    %s
    %s
) AS combined_data
`
			strSql = fmt.Sprintf(rawSql, uid,
				strCurrecy, strStartTime, strEndTime,
				uid, strStartTime2, strEndTime2)

		}
	case 1:
		{
			rawSql := `SELECT
COUNT(*) AS TotalRecords
FROM
x_commission_audit
WHERE
UserId = %d
AND State = 4
AND AgentMode=2
%s
%s
`
			strSql = fmt.Sprintf(rawSql, uid,
				strStartTime, strEndTime)
		}
	case 2:
		{
			rawSql := `SELECT
COUNT(*) AS TotalRecords
FROM
x_active_reward_audit
WHERE
UserId = %d
    %s
    %s
AND ActiveId = 54
AND (AuditState = 3 OR AuditState = 4)
`
			strSql = fmt.Sprintf(rawSql, uid,
				strStartTime2, strEndTime2)

		}
	}

	return strSql
}

type InvitedUserDetailReq struct {
	UserId    int64  `json:"userId"`    // 会员用户uid
	Currency  string `json:"currency"`  // 币种
	StartTime int64  `json:"startTime"` // 开始时间
	EndTime   int64  `json:"endTime"`   // 结束时间
}

type GameAmountItem struct {
	GameType int     `gorm:"column:gameType" json:"gameType"` // 游戏类型
	Amount   float64 `gorm:"column:amount" json:"amount"`     // 金额
}

type InvitedUserDetailResp struct {
	FirstRechargeAmount float64 `gorm:"column:firstRechargeAmount" json:"firstRechargeAmount"` // 首充金额
	RechargeAmount      float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`           // 充值金额
	BetCount            int     `gorm:"column:betCount" json:"betCount"`                       // 投注笔数
	BetAmount           float64 `gorm:"column:betAmount" json:"betAmount"`                     // 投注金额
	ValidBetAmount      float64 `gorm:"column:validBetAmount" json:"validBetAmount"`           // 有效投注金额
	// 投注信息
	BetHash        float64 `gorm:"column:betHash" json:"betHash"`               // 哈希游戏
	BetHashRlt     float64 `gorm:"column:betHashRlt" json:"betHashRlt"`         // 哈希轮盘游戏
	BetDianZi      float64 `gorm:"column:betDianZi" json:"betDianZi"`           // 电子游戏
	BetLive        float64 `gorm:"column:betLive" json:"betLive"`               // 真人游戏
	BetSport       float64 `gorm:"column:betSport" json:"betSport"`             // 体育游戏
	BetXiaoYouXi   float64 `gorm:"column:betXiaoYouXi" json:"betXiaoYouXi"`     // 休闲游戏
	BetLottery     float64 `gorm:"column:betLottery" json:"betLottery"`         // 彩票游戏
	BetQiPai       float64 `gorm:"column:betQiPai" json:"betQiPai"`             // 棋牌游戏
	BetTexas       float64 `gorm:"column:betTexas" json:"betTexas"`             // 德州游戏
	TotalBetAmount float64 `gorm:"column:totalBetAmount" json:"totalBetAmount"` // 总投注金额
	// 佣金信息
	CommissionHash        float64 `gorm:"column:commissionHash" json:"commissionHash"`               // 哈希游戏
	CommissionHashRlt     float64 `gorm:"column:commissionHashRlt" json:"commissionHashRlt"`         // 哈希轮盘游戏
	CommissionDianZi      float64 `gorm:"column:commissionDianZi" json:"commissionDianZi"`           // 电子	游戏
	CommissionLive        float64 `gorm:"column:commissionLive" json:"commissionLive"`               // 真人游戏
	CommissionSport       float64 `gorm:"column:commissionSport" json:"commissionSport"`             // 体育游戏
	CommissionXiaoYouXi   float64 `gorm:"column:commissionXiaoYouXi" json:"commissionXiaoYouXi"`     // 休闲游戏
	CommissionLottery     float64 `gorm:"column:commissionLottery" json:"commissionLottery"`         // 彩票游戏
	CommissionQiPai       float64 `gorm:"column:commissionQiPai" json:"commissionQiPai"`             // 棋牌游戏
	CommissionTexas       float64 `gorm:"column:commissionTexas" json:"commissionTexas"`             // 德州游戏
	TotalCommissionAmount float64 `gorm:"column:totalCommissionAmount" json:"totalCommissionAmount"` // 总佣金金额
}

// 邀请奖励用户详情
func (c *AgentController) invited_user_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := InvitedUserDetailReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	currency := strings.ToLower(reqdata.Currency)
	data := new(InvitedUserDetailResp)
	token := server.GetToken(ctx)
	// 获取用户详情
	tdb := server.Db().GormDao().Table("x_agent_commission_user_date")
	c.GetMemberDetailWhere(int64(token.UserId), reqdata.UserId, reqdata.StartTime, reqdata.EndTime, tdb)

	switch currency {
	case "usdt":
		querySql := tdb.Select(
			"ROUND(SUM(BetTranferUsdtHaXi), 2) AS betHash",
			"ROUND(SUM(BetTranferUsdtHaXiRoulette), 2) AS betHashRlt",
			"ROUND(SUM(BetDianZhi), 2) AS betDianZi",
			"ROUND(SUM(BetLive), 2) AS betLive",
			"ROUND(SUM(BetSport), 2) AS betSport",
			//"ROUND(SUM(CASE WHEN NewBetCount > 0 THEN NewBetCount / NewBetCount ELSE 0 END), 4) AS winRate",
			"ROUND(SUM(BetXiaoYouXi), 2) AS betXiaoYouXi",
			"ROUND(SUM(BetLottery), 2) AS betLottery",
			"ROUND(SUM(BetQiPai), 2) AS betQiPai",
			"ROUND(SUM(BetTexas), 2)  AS betTexas",
			"ROUND(SUM(BetTranferUsdtHaXi * RewardHaXi), 2)  AS commissionHash",
			"ROUND(SUM(BetTranferUsdtHaXiRoulette * RewardHaXiRoulette), 2)  AS commissionHashRlt",
			"ROUND(SUM(BetDianZhi * RewardDianZhi), 2)  AS commissionDianZi",
			"ROUND(SUM(BetLive * RewardLive), 2)  AS commissionLive",
			"ROUND(SUM(BetXiaoYouXi * RewardXiaoYouXi), 2)  AS commissionXiaoYouXi",
			"ROUND(SUM(BetLottery * RewardLottery), 2)  AS commissionLottery",
			"ROUND(SUM(BetQiPai * RewardQiPai), 2)  AS commissionQiPai",
			"ROUND(SUM(BetTexas * RewardTexas), 2)  AS commissionTexas",
		)

		err = querySql.Scan(data).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	case "trx":
	}

	//tdb2 := server.Db().GormDao().Table("x_agent_data_date")

	// 合计
	data.TotalBetAmount = data.BetHash + data.BetHashRlt +
		data.BetDianZi + data.BetLive +
		data.BetSport + data.BetXiaoYouXi +
		data.BetLottery + data.BetQiPai +
		data.BetTexas
	data.TotalCommissionAmount = data.CommissionHash + data.CommissionHashRlt +
		data.CommissionDianZi + data.CommissionLive +
		data.CommissionSport + data.CommissionXiaoYouXi +
		data.CommissionLottery + data.CommissionQiPai +
		data.CommissionTexas

	ctx.RespOK(data)
}

func (this *AgentController) GetMemberDetailWhere(uid, memberUid int64, startTime, EndTime int64, db *gorm.DB) {
	db.Where("AgentId = ?", uid)
	db.Where("UserId = ?", memberUid)
	//时间区间
	if startTime > 0 {
		db.Where("StatDate >= ?", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	if EndTime > 0 && startTime > EndTime {
		db.Where("StatDate <= ?", abugo.TimeStampToLocalTime(EndTime))
	}

}

func (this *AgentController) GetMemberDetailBaseWhere(uid int64, startTime, EndTime int64, db *gorm.DB) {
	db.Where("UserId = ?", uid)
	//时间区间
	if startTime > 0 {
		db.Where("StatDate >= ?", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	if EndTime > 0 && startTime > EndTime {
		db.Where("StatDate <= ?", abugo.TimeStampToLocalTime(EndTime))
	}

}

type CommissionDetailReq struct {
	Page      int   `json:"page"`      // 页码
	PageSize  int   `json:"pageSize"`  // 页大小
	StartTime int64 `json:"startTime"` // 开始时间
	EndTime   int64 `json:"endTime"`   // 结束时间
}

type CommissionDetailResp struct {
	Currency    string  `gorm:"column:currency" json:"currency"`       // 币种
	Amount      float64 `gorm:"column:amount" json:"amount"`           // 金额
	TotalAmount float64 `gorm:"column:totalAmount" json:"totalAmount"` // 总计金额
}

// 佣金详情
func (c *AgentController) commission_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := CommissionDetailReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	//token := server.GetToken(ctx)
	//data, _ := server.XDb().Table("x_user_reward_commission").Select("GetCommissionAmount").Where("UserId=?", token.UserId).Find()
	//if data == nil {
	//	return
	//}

	list := make([]*CommissionDetailResp, 0)
	for i := 0; i < 5; i++ {
		list = append(list, &CommissionDetailResp{
			Currency:    "USDT",
			Amount:      float64(100 * (i + 2)),
			TotalAmount: float64(100 * (i + 5)),
		})
	}
	ctx.Put("data", list)
	ctx.Put("total", 5)
	ctx.RespOK()

}

// 获取平台佣金数按天
func (c *AgentController) GetPlatTotalCommissionDate(dataTime string) float64 {
	amount := float64(0)
	tdb := server.Db().GormDao().Table("x_agent_commission_data_date")
	tdb.Where("StatDate = ?", dataTime)

	tdb.Select("ROUND(SUM(CommissionAmount + CommissionTrxAmount * TrxRate),2) ")
	tdb.Scan(&amount)
	return amount
}

type MyTeamReq struct {
}

type MyTeamResp struct {
	TeamLevel           int     `gorm:"column:teamLevel" json:"teamLevel"`                     // 团体等级
	MemberCount         int     `gorm:"column:teamUsers" json:"memberCount"`                   // 团体成员数
	ValidMemberCount    int     `gorm:"column:validMemberCount" json:"validMemberCount"`       // 有效团体成员数
	RechargeMemberCount int     `gorm:"column:rechargeMemberCount" json:"rechargeMemberCount"` // 团体充值成员数
	TeamBetAmount       float64 `gorm:"column:teamBetAmount" json:"teamBetAmount"`             // 团体投注金额
	Today               int     `gorm:"column:todayTeamUsers" json:"today"`                    // 今日新增团体成员数
	Yesterday           int     `gorm:"column:yesterday" json:"yesterday"`                     // 昨日日新增团体成员数
	Month               int     `gorm:"column:month" json:"month"`                             // 本月新增团体成员数
	Agent1              int     `gorm:"column:agent1" json:"agent1"`                           // 一级代理
	Agent2              int     `gorm:"column:agent2" json:"agent2"`                           // 二级代理
	Agent3              int     `gorm:"column:agent3" json:"agent3"`                           // 三级代理
}

// 我的团队
func (c *AgentController) my_team_member(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MyTeamReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	data := &MyTeamResp{}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ******** //********
	}
	strSql := c.GetTeamMemberSql(token.UserId)
	querySql := server.Db().GormDao().Raw(strSql)

	// 扫描结果到data
	if err := querySql.Scan(data).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	// 团队有效数据: 一级是用总的，二级以及以后就要用当天的
	validStrSql := c.GetTeamValidMemberSql(token.UserId)
	validSql := server.Db().GormDao().Raw(validStrSql)

	// 扫描结果到valid
	if err := validSql.Scan(data).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}
	ctx.RespOK(data)
}

// 获取团队成员数据
func (c *AgentController) GetTeamMemberSql(uid int) string {
	//SELECT
	//count(1) AS teamUsers,
	//	IFNULL(SUM(CASE WHEN C.ChildLevel = 0 THEN 1 ELSE 0 END), 0) AS agent1,
	//	IFNULL(SUM(CASE WHEN C.ChildLevel = 1 THEN 1 ELSE 0 END), 0) AS agent2,
	//	IFNULL(SUM(CASE WHEN C.ChildLevel = 2 THEN 1 ELSE 0 END), 0) AS agent3,
	//	IFNULL(
	//		SUM(CASE WHEN U.RegisterTime >= '2025-08-02' AND U.RegisterTime < TIMESTAMPADD(DAY, 1, '2025-08-02') THEN 1 ELSE 0 END),
	//0
	//) AS todayTeamUsers,
	//	IFNULL(
	//		SUM(CASE WHEN U.RegisterTime >= TIMESTAMPADD(DAY,- 1, '2025-08-02') AND U.RegisterTime < '2025-08-02' THEN 1 ELSE 0 END),
	//0
	//) AS yesterday,
	//	IFNULL(
	//		SUM(CASE WHEN U.RegisterTime >= '2025-08-01' AND U.RegisterTime < TIMESTAMPADD(MONTH, 1, '2025-08-01') THEN 1 ELSE 0 END),
	//0
	//) AS MONTH,
	//	IFNULL(SUM(CASE WHEN R.RechargeCount>0 THEN 1 ELSE 0 END),0) AS RechargeUsers #团队充值人数
	//FROM x_agent_child AS C
	//INNER JOIN x_user AS U ON C.Child = U.UserId
	//LEFT JOIN x_user_recharge_withard AS R
	//ON C.Child=R.UserId
	//WHERE
	//C.UserId = 71263857
	//AND C.ChildLevel <= 2

	today := time.Now().Format("2006-01-02")
	firstDayOfMonth := time.Now().Format("2006-01-02")[:8] + "01" // 获取当月第一天

	rawSql := `SELECT
  count(1) AS teamUsers,
  IFNULL(SUM(CASE WHEN C.ChildLevel = 0 THEN 1 ELSE 0 END), 0) AS agent1,
  IFNULL(SUM(CASE WHEN C.ChildLevel = 1 THEN 1 ELSE 0 END), 0) AS agent2,
  IFNULL(SUM(CASE WHEN C.ChildLevel = 2 THEN 1 ELSE 0 END), 0) AS agent3,
  IFNULL(
  SUM(CASE WHEN U.RegisterTime >= '%s' AND U.RegisterTime < TIMESTAMPADD(DAY, 1, '%s') THEN 1 ELSE 0 END),
  0
  ) AS todayTeamUsers,
  IFNULL(
  SUM(CASE WHEN U.RegisterTime >= TIMESTAMPADD(DAY,- 1, '%s') AND U.RegisterTime < '%s' THEN 1 ELSE 0 END),
  0
  ) AS yesterday,
  IFNULL(
  SUM(CASE WHEN U.RegisterTime >= '%s' AND U.RegisterTime < TIMESTAMPADD(MONTH, 1, '%s') THEN 1 ELSE 0 END),
  0
  ) AS month,
  IFNULL(SUM(CASE WHEN R.RechargeCount>0 THEN 1 ELSE 0 END),0) AS rechargeMemberCount  
FROM x_agent_child AS C
INNER JOIN x_user AS U ON C.Child = U.UserId
LEFT JOIN x_user_recharge_withard AS R
  ON C.Child=R.UserId
WHERE 
C.UserId = %d
AND C.ChildLevel <= 2
    `

	strSql := fmt.Sprintf(rawSql,
		today, today,
		today, today,
		firstDayOfMonth, firstDayOfMonth,
		uid)

	return strSql
}

// 获取团队有效成员数据
// 获取团队有效成员数据
func (c *AgentController) GetTeamValidMemberSql(uid int) string {
	//SELECT
	//TotalRealValidUsers
	//FROM x_agent_commission_data_date AS D
	//WHERE D.StatDate=@NOW
	//AND D.AgentId=63926145;

	now := time.Now()
	// 计算昨天的日期
	todaySting := now.Format("2006-01-02")
	//yesterday := now.AddDate(0, 0, -1)
	//yesterdayString := yesterday.Format("2006-01-02")

	rawSql := `SELECT
  RealValidUsers AS validMemberCount,
  TeamLevel AS teamLevel,
  RealValidLiuShui AS teamBetAmount
FROM x_agent_commission_data_date AS D
WHERE D.AgentId=%d
AND D.StatDate='%s'
`
	strSql := fmt.Sprintf(rawSql, uid, todaySting)
	return strSql
}

// 获取团队当前等级
func (c *AgentController) GetTeamLevelSql(uid int) string {
	now := time.Now()
	// 计算日期
	todaySting := now.Format("2006-01-02")
	rawSql := `SELECT
  TeamLevel AS teamLevel
FROM x_agent_commission_data_date AS D
WHERE D.AgentId=%d
AND D.StatDate='%s'
`
	strSql := fmt.Sprintf(rawSql, uid, todaySting)
	return strSql
}

// 获取团队总投注数据
func (c *AgentController) GetTeamBetCountSql(uid int) string {
	//select
	//	IFNULL(SUM(BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi*TrxRate+BetTranferTrxHaXiRoulette*TrxRate+BetHaXi+BetHaXiRoulette+BetLottery+BetLowLottery+BetQiPai+BetDianZhi+BetXiaoYouXi+BetLive+BetSport+BetTexas),0) AS TeamBetAmount #总投注金额
	//
	//	from x_agent_commission_level_date AS D
	//	WHERE D.StatDate=@NOW
	//	AND D.AgentId=63926145;

	//today := time.Now().Format("2006-01-02")
	rawSql := `select 
  IFNULL(SUM(BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi*TrxRate+BetTranferTrxHaXiRoulette*TrxRate+BetHaXi+BetHaXiRoulette+BetLottery+BetLowLottery+BetQiPai+BetDianZhi+BetXiaoYouXi+BetLive+BetSport+BetTexas),0) AS teamBetAmount 
  
from x_agent_commission_level_date AS D
WHERE  D.AgentId=%d
`
	strSql := fmt.Sprintf(rawSql, uid)
	return strSql
}

type MemberListReq struct {
	Page      int   `json:"page"`      // 页码
	PageSize  int   `json:"pageSize"`  // 页大小
	AgentLv   int   `json:"agentLv"`   // 代理等级
	MemberUid int64 `json:"memberUid"` // 成员uid
	StartTime int64 `json:"startTime"` // 开始时间
	EndTime   int64 `json:"endTime"`   // 结束时间
}

type MemberListResp struct {
	UserId           int        `gorm:"column:UserId" json:"userId"`                     // 用户id
	CommissionAmount float64    `gorm:"column:CommissionAmount" json:"commissionAmount"` // 佣金
	Account          string     `gorm:"column:Account" json:"account"`                   // 账号
	VipLv            int        `gorm:"column:VipLevel" json:"vipLv"`                    // 会员等级
	InviteLink       string     `gorm:"column:AgentCode" json:"inviteLink"`              // 邀请链接
	RegTime          *time.Time `gorm:"column:RegisterTime" json:"regTime"`              // 注册时间
	AgentId          int        `gorm:"column:AgentId" json:"agentId"`                   // 上级代理id
}

// 会员列表
func (c *AgentController) member_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MemberListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ******** //********
	}

	total := 0
	list := make([]*MemberListResp, 0)
	countStrSql := c.GetTeamMemberListCountSql(token.UserId,
		int(reqdata.MemberUid), reqdata.AgentLv,
		reqdata.StartTime,
		reqdata.EndTime)
	countSql := server.Db().GormDao().Raw(countStrSql)

	// 扫描结果到total
	if err := countSql.Scan(&total).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	if total <= 0 {
		ctx.Put("data", list)
		ctx.Put("total", total)
		ctx.RespOK()
		return
	}
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	limit := reqdata.PageSize
	queryStrSql := c.GetTeamMemberListSql(token.UserId,
		int(reqdata.MemberUid), reqdata.AgentLv,
		limit,
		offset,
		reqdata.StartTime,
		reqdata.EndTime)
	querySql := server.Db().GormDao().Raw(queryStrSql)

	// 扫描结果到total
	if err := querySql.Find(&list).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("data", list)
	ctx.Put("total", total)
	ctx.RespOK()
}

// 获取团队成员列表数据
func (c *AgentController) GetTeamMemberListSql(uid, memberUid, agentLv, limit, offset int, startTime, endTime int64) string {
	//WITH CTE AS
	//(
	//	SELECT
	//UserId
	//,CommissionAmount+CommissionTrxAmount*TrxRate AS CommissionAmount
	//FROM x_agent_commission_user_date
	//WHERE AgentId=********
	//GROUP BY UserId
	//
	//)
	//
	//SELECT
	//C.Child AS UserId
	//,U.AgentId
	//,U.Account
	//,U.RegisterTime
	//,IFNULL(V.VipLevel,0) AS VipLevel
	//,E.CommissionAmount
	//,GROUP_CONCAT(D.AgentCode) AS AgentCode
	//FROM x_agent_child AS C
	//INNER JOIN x_user AS U
	//ON C.Child=U.UserId
	//LEFT JOIN x_vip_info AS V
	//ON U.UserId=V.UserId
	//LEFT JOIN CTE AS E
	//ON U.UserId=E.UserId
	//LEFT JOIN x_agent_code AS D
	//ON U.UserId=D.UserId
	//WHERE C.UserId=********
	//AND C.ChildLevel<=2
	//AND U.UserId=********
	//AND U.RegisterTime >=  '2025-07-19 00:00:00'
	//AND U.RegisterTime <=  '2025-08-19 00:00:00'
	//GROUP BY UserId
	//LIMIT 15
	//OFFSET 0;

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND U.RegisterTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND U.RegisterTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	// 会员uid
	strMemberUid := ""
	if memberUid > 0 {
		strMemberUid = fmt.Sprintf(" AND U.UserId=%d ", memberUid)
	}

	// 代理级别
	strAgentLv := ""
	if agentLv > 0 {
		strAgentLv = fmt.Sprintf(" AND C.ChildLevel = %d ", agentLv-1)
	} else {
		strAgentLv = fmt.Sprintf(" AND C.ChildLevel <= 2 ")
	}

	rawSql := `WITH CTE AS 
(
  SELECT
    UserId
    ,ROUND(SUM(CommissionAmount+CommissionTrxAmount*TrxRate),2) AS CommissionAmount
	,IsVaild
  FROM x_agent_commission_user_date
  WHERE AgentId=%d
    GROUP BY UserId

)

SELECT 
   C.Child AS UserId
  ,U.AgentId
  ,U.Account
  ,U.RegisterTime
    ,IFNULL(V.VipLevel,0) AS VipLevel
    ,E.CommissionAmount AS CommissionAmount
    ,GROUP_CONCAT(D.AgentCode) AS AgentCode
FROM x_agent_child AS C
INNER JOIN x_user AS U
  ON C.Child=U.UserId
LEFT JOIN x_vip_info AS V
  ON U.UserId=V.UserId
LEFT JOIN CTE AS E
  ON U.UserId=E.UserId
LEFT JOIN x_agent_code AS D
  ON U.UserId=D.UserId
WHERE C.UserId=%d 
AND E.IsVaild=1
%s
%s
%s
%s
GROUP BY UserId
LIMIT %d
OFFSET %d
`
	strSql := fmt.Sprintf(rawSql,
		uid, uid, strAgentLv,
		strMemberUid,
		strStartTime,
		strEndTime,
		limit,
		offset)

	return strSql
}

// 获取团队成员列表总数据
func (c *AgentController) GetTeamMemberListCountSql(uid, memberUid, agentLv int, startTime, endTime int64) string {
	//SELECT
	//COUNT(*)
	//FROM
	//x_agent_child AS C
	//INNER JOIN x_user AS U ON C.Child = U.UserId
	//WHERE
	//C.UserId = ********
	//AND C.ChildLevel <= 2
	//;

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND U.RegisterTime >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND U.RegisterTime <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	// 会员uid
	strMemberUid := ""
	if memberUid > 0 {
		strMemberUid = fmt.Sprintf(" AND U.UserId=%d ", memberUid)
	}

	// 代理级别
	strAgentLv := ""
	if agentLv > 0 {
		strAgentLv = fmt.Sprintf(" AND C.ChildLevel = %d ", agentLv-1)
	} else {
		strAgentLv = fmt.Sprintf(" AND C.ChildLevel <= 2 ")
	}

	rawSql := `SELECT 
    COUNT(*)
FROM x_agent_child AS C
INNER JOIN x_user AS U
    ON C.Child = U.UserId
LEFT JOIN (
    SELECT
        UserId,
        ROUND(SUM(CommissionAmount + CommissionTrxAmount * TrxRate), 2) AS CommissionAmount,
        IsVaild
    FROM x_agent_commission_user_date
    WHERE AgentId = %d
    GROUP BY UserId
) AS E ON U.UserId = E.UserId
WHERE C.UserId = %d 
  AND E.IsVaild = 1
 %s
 %s
 %s
 %s
`
	strSql := fmt.Sprintf(rawSql, uid,
		uid, strAgentLv,
		strMemberUid,
		strStartTime,
		strEndTime)

	return strSql
}

type MemberDetailReq struct {
	Currency  string ` json:"currency"` // 币种
	MemberUid int64  `json:"memberUid"` // 成员uid
	StartTime int64  `json:"startTime"` // 开始时间
	EndTime   int64  `json:"endTime"`   // 结束时间

}

type MemberDetailResp struct {
	FirstRechargeAmount float64 `gorm:"column:firstRechargeAmount" json:"firstRechargeAmount"` // 首充金额
	RechargeAmount      float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`           // 充值金额
	BetCount            int     `gorm:"column:betCount" json:"betCount"`                       // 投注笔数
	BetAmount           float64 `gorm:"column:betAmount" json:"betAmount"`                     // 投注金额
	ValidBetAmount      float64 `gorm:"column:validBetAmount" json:"validBetAmount"`           // 有效投注金额
	// 投注信息
	BetHash        float64 `gorm:"column:betHash" json:"betHash"`               // 哈希游戏
	BetHashRlt     float64 `gorm:"column:betHashRlt" json:"betHashRlt"`         // 哈希轮盘游戏
	BetDianZi      float64 `gorm:"column:betDianZi" json:"betDianZi"`           // 电子游戏
	BetLive        float64 `gorm:"column:betLive" json:"betLive"`               // 真人游戏
	BetSport       float64 `gorm:"column:betSport" json:"betSport"`             // 体育游戏
	BetXiaoYouXi   float64 `gorm:"column:betXiaoYouXi" json:"betXiaoYouXi"`     // 休闲游戏
	BetLottery     float64 `gorm:"column:betLottery" json:"betLottery"`         // 彩票游戏
	BetQiPai       float64 `gorm:"column:betQiPai" json:"betQiPai"`             // 棋牌游戏
	BetTexas       float64 `gorm:"column:betTexas" json:"betTexas"`             // 德州游戏
	TotalBetAmount float64 `gorm:"column:totalBetAmount" json:"totalBetAmount"` // 总投注金额
	// 佣金信息
	CommissionHash        float64 `gorm:"column:commissionHash" json:"commissionHash"`               // 哈希游戏
	CommissionHashRlt     float64 `gorm:"column:commissionHashRlt" json:"commissionHashRlt"`         // 哈希轮盘游戏
	CommissionDianZi      float64 `gorm:"column:commissionDianZi" json:"commissionDianZi"`           // 电子	游戏
	CommissionLive        float64 `gorm:"column:commissionLive" json:"commissionLive"`               // 真人游戏
	CommissionSport       float64 `gorm:"column:commissionSport" json:"commissionSport"`             // 体育游戏
	CommissionXiaoYouXi   float64 `gorm:"column:commissionXiaoYouXi" json:"commissionXiaoYouXi"`     // 休闲游戏
	CommissionLottery     float64 `gorm:"column:commissionLottery" json:"commissionLottery"`         // 彩票游戏
	CommissionQiPai       float64 `gorm:"column:commissionQiPai" json:"commissionQiPai"`             // 棋牌游戏
	CommissionTexas       float64 `gorm:"column:commissionTexas" json:"commissionTexas"`             // 德州游戏
	TotalCommissionAmount float64 `gorm:"column:totalCommissionAmount" json:"totalCommissionAmount"` // 总佣金金额
}

// 会员详情
func (c *AgentController) member_detail(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MemberDetailReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	currency := strings.ToLower(reqdata.Currency)
	data := new(MemberDetailResp)
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ********
	}
	// 获取用户详情
	tdb := server.Db().GormDao().Table("x_agent_commission_user_date")
	c.GetMemberDetailWhere(int64(token.UserId), reqdata.MemberUid, reqdata.StartTime, reqdata.EndTime, tdb)

	switch currency {
	case "usdt":
		querySql := tdb.Select(
			"ROUND(SUM(BetHaXi+BetTranferUsdtHaXi), 2) AS betHash",
			"ROUND(SUM(BetTranferUsdtHaXiRoulette), 2) AS betHashRlt",
			"ROUND(SUM(BetDianZhi), 2) AS betDianZi",
			"ROUND(SUM(BetLive), 2) AS betLive",
			"ROUND(SUM(BetSport), 2) AS betSport",
			//"ROUND(SUM(CASE WHEN NewBetCount > 0 THEN NewBetCount / NewBetCount ELSE 0 END), 4) AS winRate",
			"ROUND(SUM(BetXiaoYouXi), 2) AS betXiaoYouXi",
			"ROUND(SUM(BetLottery), 2) AS betLottery",
			"ROUND(SUM(BetQiPai), 2) AS betQiPai",
			"ROUND(SUM(BetTexas), 2)  AS betTexas",
			"ROUND(SUM((BetHaXi+BetTranferUsdtHaXi) * RewardHaXi * 0.01), 2)  AS commissionHash",
			"ROUND(SUM(BetTranferUsdtHaXiRoulette * RewardHaXiRoulette * 0.01), 2)  AS commissionHashRlt",
			"ROUND(SUM(BetDianZhi * RewardDianZhi * 0.01), 2)  AS commissionDianZi",
			"ROUND(SUM(BetLive * RewardLive * 0.01), 2)  AS commissionLive",
			"ROUND(SUM(BetXiaoYouXi * RewardXiaoYouXi * 0.01), 2)  AS commissionXiaoYouXi",
			"ROUND(SUM(BetLottery * RewardLottery * 0.01), 2)  AS commissionLottery",
			"ROUND(SUM(BetQiPai * RewardQiPai * 0.01), 2)  AS commissionQiPai",
			"ROUND(SUM(BetTexas * RewardTexas * 0.01), 2)  AS commissionTexas",
			"ROUND(SUM(FirstRechargeAmount),2)  AS firstRechargeAmount",
			"ROUND(SUM(RechargeAmount),2)  AS rechargeAmount",
			//"ROUND(SUM(RechargeAmount),2)  AS betCount",
			//"ROUND(SUM(RechargeAmount),2)  AS betAmount",
			//"ROUND(SUM(RechargeAmount),2)  AS validBetAmount",
		)

		err = querySql.Scan(data).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	case "trx":
		querySql := tdb.Select(
			"ROUND(SUM(BetTranferTrxHaXi), 2) AS betHash",
			"ROUND(SUM(BetTranferTrxHaXiRoulette), 2) AS betHashRlt",
			"ROUND(SUM(BetTranferTrxHaXi * RewardHaXi * 0.01), 2)  AS commissionHash",
			"ROUND(SUM(BetTranferTrxHaXiRoulette * RewardHaXiRoulette * 0.01), 2)  AS commissionHashRlt",
			//"ROUND(SUM(FirstRechargeAmount),2)  AS firstRechargeAmount",
			//"ROUND(SUM(RechargeAmount),2)  AS rechargeAmount",
			//"ROUND(SUM(RechargeAmount),2)  AS betCount",
			//"ROUND(SUM(RechargeAmount),2)  AS betAmount",
			//"ROUND(SUM(RechargeAmount),2)  AS validBetAmount",
		)

		err = querySql.Scan(data).Error
		if err != nil {
			ctx.RespErr(err, &errcode)
			return
		}
	}

	// 获取用户首充金额
	//firstChargeAmount := float64(0)
	//server.Db().GormDao().Table("x_ads_user_profile").Select("first_recharge_amount").
	//	Where("user_id = ?", reqdata.MemberUid).Scan(&firstChargeAmount)
	//data.FirstRechargeAmount = firstChargeAmount

	// 获取用户充值金额
	//rechargeAmount := float64(0)
	//tb2 := server.Db().GormDao().Table("x_user_recharge_withard_date")
	//tb2.Where("UserId = ?", reqdata.MemberUid)
	////时间区间
	//if reqdata.StartTime > 0 {
	//	tb2.Where("RecordDate >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
	//}
	//// 注册时间区间
	//if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
	//	tb2.Where("RecordDate <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
	//}
	//tb2.Select("ROUND(SUM(RechargeAmount), 2)").
	//	Scan(&rechargeAmount)
	//data.RechargeAmount = rechargeAmount

	// 获取用户有效投注
	validAmount := float64(0)
	tb3 := server.Db().GormDao().Table("x_agent_commission_user_date")
	tb3.Where("AgentId = ?", token.UserId)
	tb3.Where("UserId = ?", reqdata.MemberUid)
	//时间区间
	if reqdata.StartTime > 0 {
		tb3.Where("StatDate >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
	}
	// 注册时间区间
	if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
		tb3.Where("StatDate <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
	}
	switch currency {
	case "usdt":
		tb3.Select("IFNULL(ROUND(SUM(LiuShuiHaXi+LiuShuiHaXiRoulette+LiuShuiTranferUsdtHaXi+" +
			"LiuShuiTranferUsdtHaXiRoulette+LiuShuiLottery+LiuShuiLowLottery+" +
			"LiuShuiQiPai+LiuShuiDianZhi+LiuShuiXiaoYouXi+LiuShuiLive+" +
			"LiuShuiSport+LiuShuiTexas), 2), 0)").
			Scan(&validAmount)
	case "trx":
		tb3.Select("IFNULL(ROUND(SUM(LiuShuiTranferTrxHaXi+LiuShuiTranferTrxHaXiRoulette), 2),0)").
			Scan(&validAmount)
	}

	data.ValidBetAmount = validAmount
	betCount := int64(0)
	// 获取用户投注次数: haxi
	betCount = betCount + c.GetUserBetCountHash(currency, &reqdata)
	if currency == "usdt" {
		// 获取用户投注次数: qipai
		betCount = betCount + c.GetUserBetCountThird("x_third_qipai", &reqdata)
		// 获取用户投注次数: dianzhi
		betCount = betCount + c.GetUserBetCountThird("x_third_dianzhi", &reqdata)
		// 获取用户投注次数: xiaoyouxi
		betCount = betCount + c.GetUserBetCountThird("x_third_quwei", &reqdata)
		// 获取用户投注次数: live
		betCount = betCount + c.GetUserBetCountThird("x_third_live", &reqdata)
		// 获取用户投注次数: sport
		betCount = betCount + c.GetUserBetCountThird("x_third_sport", &reqdata)
		// 获取用户投注次数: lottery
		betCount = betCount + c.GetUserBetCountThird("x_third_lottery", &reqdata)
		// 获取用户投注次数: texas
		betCount = betCount + c.GetUserBetCountThird("x_third_texas", &reqdata)
	}

	data.BetCount = int(betCount)

	// 获取投注金额
	data.BetAmount = data.BetHash + data.BetHashRlt +
		data.BetDianZi + data.BetLive +
		data.BetSport + data.BetXiaoYouXi +
		data.BetLottery + data.BetQiPai +
		data.BetTexas

	// 合计
	data.TotalBetAmount = data.BetHash + data.BetHashRlt +
		data.BetDianZi + data.BetLive +
		data.BetSport + data.BetXiaoYouXi +
		data.BetLottery + data.BetQiPai +
		data.BetTexas
	data.TotalCommissionAmount = data.CommissionHash + data.CommissionHashRlt +
		data.CommissionDianZi + data.CommissionLive +
		data.CommissionSport + data.CommissionXiaoYouXi +
		data.CommissionLottery + data.CommissionQiPai +
		data.CommissionTexas

	ctx.RespOK(data)
}

// 获取用户投注次数:haxi
func (c *AgentController) GetUserBetCountHash(currency string, reqdata *MemberDetailReq) int64 {
	betCount := int64(0)
	tb := server.Db().GormDao().Table("x_order")
	tb.Where("UserId = ?", reqdata.MemberUid)
	tb.Where("Symbol = ?", currency)
	//时间区间
	if reqdata.StartTime > 0 {
		tb.Where("CreateTime >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
	}
	// 注册时间区间
	if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
		tb.Where("CreateTime <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
	}
	tb.Count(&betCount)
	return betCount
}

// 获取用户三方投注次数:third
func (c *AgentController) GetUserBetCountThird(tbName string, reqdata *MemberDetailReq) int64 {
	betCount := int64(0)
	tb := server.Db().GormDao().Table(tbName)
	tb.Where("UserId = ?", reqdata.MemberUid)
	switch tbName {
	case "x_third_sport": // 体育
		{
			// 时间区间
			if reqdata.StartTime > 0 {
				tb.Where("BetLocalTime >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
			}
			// 时间区间
			if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
				tb.Where("BetLocalTime <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
			}

		}
	case "x_third_lottery": // 彩票
		{
			// 时间区间
			if reqdata.StartTime > 0 {
				tb.Where("BetTime >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
			}
			// 时间区间
			if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
				tb.Where("BetTime <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
			}
			tb.Where("GameId in (30,44,328,667,715,253,669,717,713,711,329, 254, 670, 668, 714, 710, 716, 712,672,671,1,618,984)")
		}
	default:
		{

			// 时间区间
			if reqdata.StartTime > 0 {
				tb.Where("ThirdTime >= ?", abugo.TimeStampToLocalTime(reqdata.StartTime))
			}
			// 时间区间
			if reqdata.EndTime > 0 && reqdata.StartTime > reqdata.EndTime {
				tb.Where("ThirdTime <= ?", abugo.TimeStampToLocalTime(reqdata.EndTime))
			}
		}
	}
	tb.Count(&betCount)
	return betCount
}

type MyTeamGameReq struct {
	AgentLv   int   `json:"agentLv"`   // 代理等级
	StartTime int64 `json:"startTime"` // 开始日期
	EndTime   int64 `json:"endTime"`   // 结束日期
}

type MyTeamGameResp struct {
	RechargeCount       int     `gorm:"column:rechargeCount" json:"rechargeCount"`             // 充值人数
	FirstRechargeCount  int     `gorm:"column:firstRechargeCount" json:"firstRechargeCount"`   // 首充值人数
	Bettors             int     `gorm:"column:bettors" json:"bettors"`                         // 投注人数
	FirstRechargeAmount float64 `gorm:"column:firstRechargeAmount" json:"firstRechargeAmount"` // 首充金额
	RechargeAmount      float64 `gorm:"column:rechargeAmount" json:"rechargeAmount"`           // 充值金额
	BetAmount           float64 `gorm:"column:betAmount" json:"betAmount"`                     // 投注金额
	ValidBetAmount      float64 `gorm:"column:validBetAmount" json:"validBetAmount"`           // 有效投注金额
	ValidUsers          int     `gorm:"column:validUsers" json:"validUsers"`                   // 有效活跃人数
	CommissionAmount    float64 `gorm:"column:commissionAmount" json:"commissionAmount"`       // 佣金金额
}

// 我的团队
func (c *AgentController) my_team_game(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MyTeamGameReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ********
	}
	data := new(MyTeamGameResp)
	queryStrSql := c.GetTeamOverviewGameDataSql(int64(token.UserId),
		reqdata.StartTime,
		reqdata.EndTime)
	// 扫描结果
	querySql := server.Db().GormDao().Raw(queryStrSql)
	if err = querySql.Scan(data).Error; err != nil {
		logs.Error("执行查询SQL失败:", err)
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.RespOK(data)

}

// 获取团队游戏总揽数据
func (c *AgentController) GetTeamOverviewGameDataSql(uid, startTime, endTime int64) string {
	//select
	//	AgentId
	//	,SUM(RechargeUsers) AS RechargeUsers
	//	,SUM(FirstRechargeUsers) AS FirstRechargeUsers
	//	,SUM(BetTotalUsers) AS BetTotalUsers
	//	,ROUND(SUM(RechargeAmount),2) AS RechargeAmount
	//	,ROUND(SUM(FirstRechargeAmount),2) AS FirstRechargeAmount
	//	,ROUND(SUM(BetHaXi+BetHaXiRoulette+BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi*TrxRate+BetTranferTrxHaXiRoulette*TrxRate+BetDianZhi+BetLive+BetSport+BetXiaoYouXi+BetLottery+BetQiPai+BetTexas),2) AS BetTotal
	//	,SUM(VaildUsers) AS VaildUsers
	//	,ROUND(SUM(LiuShuiHaXi+LiuShuiHaXiRoulette+LiuShuiTranferUsdtHaXi+LiuShuiTranferUsdtHaXiRoulette+LiuShuiTranferTrxHaXi*TrxRate+LiuShuiTranferTrxHaXiRoulette*TrxRate+LiuShuiDianZhi+LiuShuiLive+LiuShuiSport+LiuShuiXiaoYouXi+LiuShuiLottery+LiuShuiQiPai+LiuShuiTexas),2) AS TotalBetLiShui
	//	,ROUND(SUM(LiuShuiHaXi*RewardHaXi/100.0+LiuShuiHaXiRoulette*RewardHaXiRoulette/100.0+LiuShuiTranferUsdtHaXi*RewardHaXi/100.0+LiuShuiTranferUsdtHaXiRoulette*RewardHaXiRoulette/100.0+LiuShuiDianZhi*RewardDianZhi/100.0+LiuShuiLive*RewardLive/100.0+LiuShuiSport*RewardSport/100.0+LiuShuiXiaoYouXi*RewardXiaoYouXi/100.0+LiuShuiQiPai*RewardQiPai/100.0+LiuShuiLottery*RewardLottery/100.0+LiuShuiTexas*RewardTexas/100.0),2) AS TotalCommission
	//	from x_agent_commission_level_date
	//	where AgentId=123 and AgentLevel=1
	//	AND StatDate='2025-07-24'
	//	group BY AgentId
	//	;

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND StatDate >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND StatDate <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}
	rawSql := `
   select 
   AgentId
  ,SUM(RechargeUsers) AS rechargeCount
  ,SUM(FirstRechargeUsers) AS firstRechargeCount
  ,SUM(BetTotalUsers) AS bettors
  ,ROUND(SUM(RechargeAmount),2) AS rechargeAmount
  ,ROUND(SUM(FirstRechargeAmount),2) AS firstRechargeAmount
  ,ROUND(SUM(BetHaXi+BetHaXiRoulette+BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette+BetTranferTrxHaXi*TrxRate+BetTranferTrxHaXiRoulette*TrxRate+BetDianZhi+BetLive+BetSport+BetXiaoYouXi+BetLottery+BetQiPai+BetTexas),2) AS betAmount
  ,SUM(VaildUsers) AS validUsers
  ,ROUND(SUM(LiuShuiHaXi+LiuShuiHaXiRoulette+LiuShuiTranferUsdtHaXi+LiuShuiTranferUsdtHaXiRoulette+LiuShuiTranferTrxHaXi*TrxRate+LiuShuiTranferTrxHaXiRoulette*TrxRate+LiuShuiDianZhi+LiuShuiLive+LiuShuiSport+LiuShuiXiaoYouXi+LiuShuiLottery+LiuShuiQiPai+LiuShuiTexas),2) AS validBetAmount
,ROUND(SUM(LiuShuiHaXi*RewardHaXi/100.0+LiuShuiHaXiRoulette*RewardHaXiRoulette/100.0+LiuShuiTranferUsdtHaXi*RewardHaXi/100.0+LiuShuiTranferUsdtHaXiRoulette*RewardHaXiRoulette/100.0+LiuShuiDianZhi*RewardDianZhi/100.0+LiuShuiLive*RewardLive/100.0+LiuShuiSport*RewardSport/100.0+LiuShuiXiaoYouXi*RewardXiaoYouXi/100.0+LiuShuiQiPai*RewardQiPai/100.0+LiuShuiLottery*RewardLottery/100.0+LiuShuiTexas*RewardTexas/100.0),2) AS commissionAmount
from x_agent_commission_level_date 
where AgentId=%d
%s
%s
group BY AgentId
 `
	strSql := fmt.Sprintf(rawSql, uid,
		strStartTime,
		strEndTime)

	return strSql
}

type GameDataListReq struct {
	AgentLv   int    `json:"agentLv"`   // 代理层级
	MemberUid int64  `json:"memberUid"` // 成员uid
	StartTime int64  `json:"startTime"` // 时间
	EndTime   int64  `json:"endTime"`   // 时间
	Currency  string ` json:"currency"` // 币种
}

type GameDataResp struct {
	// 投注人数
	BetHashNumber      int `gorm:"column:betHashNumber" json:"betHashNumber"`               // 哈希游戏
	BetDianZiNumber    int `gorm:"column:betDianZiNumber" json:"betDianZiNumber"`           // 电子游戏
	BetLiveNumber      int `gorm:"column:betLiveNumber" json:"betLiveNumber"`               // 真人游戏
	BetSportNumber     int `gorm:"column:betSportNumber" json:"betSportNumber"`             // 体育游戏
	BetXiaoYouXiNumber int `gorm:"column:betXiaoYouXiNumber" json:"betXiaoYouXiNumber"`     // 休闲游戏
	BetLotteryNumber   int `gorm:"column:betLotteryNumber" json:"betLotteryNumber"`         // 彩票游戏
	BetQiPaiNumber     int `gorm:"column:betQiPaiNumber" json:"betQiPaiNumber"`             // 棋牌游戏
	BetTexasNumber     int `gorm:"column:betTexasNumber" json:"betTexasNumber"`             // 德州游戏
	TotalBetNumber     int `gorm:"column:totalBetAmountNumber" json:"totalBetAmountNumber"` // 总投注金额
	// 投注信息
	BetHash        float64 `gorm:"column:betHash" json:"betHash"`               // 哈希游戏
	BetDianZi      float64 `gorm:"column:betDianZi" json:"betDianZi"`           // 电子游戏
	BetLive        float64 `gorm:"column:betLive" json:"betLive"`               // 真人游戏
	BetSport       float64 `gorm:"column:betSport" json:"betSport"`             // 体育游戏
	BetXiaoYouXi   float64 `gorm:"column:betXiaoYouXi" json:"betXiaoYouXi"`     // 休闲游戏
	BetLottery     float64 `gorm:"column:betLottery" json:"betLottery"`         // 彩票游戏
	BetQiPai       float64 `gorm:"column:betQiPai" json:"betQiPai"`             // 棋牌游戏
	BetTexas       float64 `gorm:"column:betTexas" json:"betTexas"`             // 德州游戏
	TotalBetAmount float64 `gorm:"column:totalBetAmount" json:"totalBetAmount"` // 总投注金额
	// 佣金信息
	CommissionHash        float64 `gorm:"column:commissionHash" json:"commissionHash"`               // 哈希游戏
	CommissionDianZi      float64 `gorm:"column:commissionDianZi" json:"commissionDianZi"`           // 电子	游戏
	CommissionLive        float64 `gorm:"column:commissionLive" json:"commissionLive"`               // 真人游戏
	CommissionSport       float64 `gorm:"column:commissionSport" json:"commissionSport"`             // 体育游戏
	CommissionXiaoYouXi   float64 `gorm:"column:commissionXiaoYouXi" json:"commissionXiaoYouXi"`     // 休闲游戏
	CommissionLottery     float64 `gorm:"column:commissionLottery" json:"commissionLottery"`         // 彩票游戏
	CommissionQiPai       float64 `gorm:"column:commissionQiPai" json:"commissionQiPai"`             // 棋牌游戏
	CommissionTexas       float64 `gorm:"column:commissionTexas" json:"commissionTexas"`             // 德州游戏
	TotalCommissionAmount float64 `gorm:"column:totalCommissionAmount" json:"totalCommissionAmount"` // 总佣金金额
}

type GameTypeData struct {
	BetUsers   int     `gorm:"column:BetUsers" json:"BetUsers"`     // 投注人数
	BetAmount  float64 `gorm:"column:BetAmount" json:"BetAmount"`   // 投注金额
	Commission float64 `gorm:"column:Commission" json:"Commission"` // 佣金
}

func (this *GameTypeData) Clean() {
	this.BetAmount = 0
	this.BetUsers = 0
	this.Commission = 0
}

// 游戏数据列表
func (c *AgentController) game_data_list(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := GameDataListReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	currency := strings.ToLower(reqdata.Currency)
	data := new(GameDataResp)
	token := server.GetToken(ctx)
	if server.Debug() {
		//token.UserId = ********
	}
	// 获取游戏数据
	if currency == "usdt" {
		gameData := new(GameTypeData)
		// 哈希游戏
		strGame := c.GetTeamGameTypeSql("hash")
		queryStrSql := c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql := server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetHashNumber = gameData.BetUsers
		data.BetHash = gameData.BetAmount
		data.CommissionHash = gameData.Commission
		gameData.Clean()

		// 电子游戏
		strGame = c.GetTeamGameTypeSql("dianzi")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetDianZiNumber = gameData.BetUsers
		data.BetDianZi = gameData.BetAmount
		data.CommissionDianZi = gameData.Commission
		gameData.Clean()

		// 真人游戏
		strGame = c.GetTeamGameTypeSql("live")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetLiveNumber = gameData.BetUsers
		data.BetLive = gameData.BetAmount
		data.CommissionLive = gameData.Commission
		gameData.Clean()

		// 体育游戏
		strGame = c.GetTeamGameTypeSql("sport")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetSportNumber = gameData.BetUsers
		data.BetSport = gameData.BetAmount
		data.CommissionLottery = gameData.Commission
		gameData.Clean()

		// 体育游戏
		strGame = c.GetTeamGameTypeSql("xiaoyouxi")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetXiaoYouXiNumber = gameData.BetUsers
		data.BetXiaoYouXi = gameData.BetAmount
		data.CommissionXiaoYouXi = gameData.Commission
		gameData.Clean()

		// 彩票游戏
		strGame = c.GetTeamGameTypeSql("lottery")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetLotteryNumber = gameData.BetUsers
		data.BetLottery = gameData.BetAmount
		data.CommissionLottery = gameData.Commission
		gameData.Clean()

		// 棋牌游戏
		strGame = c.GetTeamGameTypeSql("qipai")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetQiPaiNumber = gameData.BetUsers
		data.BetQiPai = gameData.BetAmount
		data.CommissionQiPai = gameData.Commission
		gameData.Clean()

		// 德州游戏
		strGame = c.GetTeamGameTypeSql("texas")
		queryStrSql = c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql = server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}

		data.BetTexasNumber = gameData.BetUsers
		data.BetTexas = gameData.BetAmount
		data.CommissionTexas = gameData.Commission
		gameData.Clean()
	} else {
		gameData := new(GameTypeData)
		strGame := c.GetTeamGameTrxSql()
		queryStrSql := c.GetTeamGameDataSql(strGame,
			int64(token.UserId), reqdata.MemberUid,
			int64(reqdata.AgentLv),
			reqdata.StartTime,
			reqdata.EndTime)
		// 扫描结果
		querySql := server.Db().GormDao().Raw(queryStrSql)
		if err = querySql.Scan(gameData).Error; err != nil {
			logs.Error("执行查询SQL失败:", err)
			ctx.RespErr(err, &errcode)
			return
		}
		data.BetHashNumber = gameData.BetUsers
		data.BetHash = gameData.BetAmount
		data.CommissionHash = gameData.Commission
		gameData.Clean()
	}

	// 合计
	data.TotalBetNumber = data.BetHashNumber +
		data.BetDianZiNumber + data.BetLiveNumber +
		data.BetSportNumber + data.BetXiaoYouXiNumber +
		data.BetLotteryNumber + data.BetQiPaiNumber +
		data.BetTexasNumber

	data.TotalBetAmount = data.BetHash +
		data.BetDianZi + data.BetLive +
		data.BetSport + data.BetXiaoYouXi +
		data.BetLottery + data.BetQiPai +
		data.BetTexas
	data.TotalCommissionAmount = data.CommissionHash +
		data.CommissionDianZi + data.CommissionLive +
		data.CommissionSport + data.CommissionXiaoYouXi +
		data.CommissionLottery + data.CommissionQiPai +
		data.CommissionTexas

	ctx.RespOK(data)
}

// 获取团队游戏数据
func (c *AgentController) GetTeamGameTypeSql(game string) string {
	strSql := ""
	switch game {
	case "hash":
		strSql = `
   AgentId,
   SUM(BetHaXiUsers+BetHaXiRouletteUsers+BetTranferUsdtHaXiUsers+BetTranferUsdtHaXiRouletteUsers) AS BetUsers,
   ROUND(SUM(BetHaXi+BetHaXiRoulette+BetTranferUsdtHaXi+BetTranferUsdtHaXiRoulette),2) AS BetAmount,
   ROUND(SUM(LiuShuiHaXi*RewardHaXi/100.0+LiuShuiHaXiRoulette*RewardHaXiRoulette/100.0+LiuShuiTranferUsdtHaXi*RewardHaXi/100.0+LiuShuiTranferUsdtHaXiRoulette*RewardHaXiRoulette/100.0),2) AS Commission
   `
	case "dianzi":
		strSql = `
   AgentId,
   SUM(BetDianZhiUsers) AS BetUsers,
   SUM(BetDianZhi) AS BetAmount,
   ROUND(SUM(LiuShuiDianZhi*RewardDianZhi/100.0),2) AS Commission
   `
	case "live":
		strSql = `
   AgentId,
   SUM(BetLiveUsers) AS BetUsers,
   SUM(BetLive) AS BetAmount,
   ROUND(SUM(LiuShuiLive*RewardLive/100.0),2) AS Commission
   `
	case "sport":
		strSql = `
   AgentId,
   SUM(BetSportUsers) AS BetUsers,
   SUM(BetSport) AS BetAmount,
   ROUND(SUM(LiuShuiSport*RewardSport/100.0),2) AS Commission
   `
	case "xiaoyouxi":
		strSql = `
   AgentId,
   SUM(BetXiaoYouXiUsers) AS BetUsers,
   SUM(BetXiaoYouXi) AS BetAmount,
   ROUND(SUM(LiuShuiXiaoYouXi*RewardXiaoYouXi/100.0),2) AS Commission
   `
	case "lottery":
		strSql = `
   AgentId,
   SUM(BetLotteryUsers) AS BetUsers,
   SUM(BetLottery) AS BetAmount,
   ROUND(SUM(LiuShuiLottery*RewardLottery/100.0),2) AS Commission
   `
	case "qipai":
		strSql = `
   AgentId,
   SUM(BetQiPaiUsers) AS BetUsers,
   SUM(BetQiPai) AS BetAmount,
   ROUND(SUM(LiuShuiQiPai*RewardQiPai/100.0),2) AS Commission
   `
	case "texas":
		strSql = `
   AgentId,
   SUM(BetTexasUsers) AS BetUsers,
   SUM(BetTexas) AS BetAmount,
   ROUND(SUM(LiuShuiTexas*RewardTexas/100.0),2) AS Commission
   `
	}

	return strSql
}

// 获取团队游戏数据trx
func (c *AgentController) GetTeamGameTrxSql() string {
	strSql := `
   AgentId,
   SUM(BetTranferTrxHaXiUsers+BetTranferTrxHaXiRouletteUsers) AS BetUsers,
   ROUND(SUM(BetTranferTrxHaXi+BetTranferTrxHaXiRoulette),2) AS BetAmount,
   ROUND(SUM(LiuShuiTranferTrxHaXi*RewardHaXi/100.0+LiuShuiTranferTrxHaXiRoulette*RewardHaXiRoulette/100.0),2) AS Commission
   `
	return strSql
}

// 获取团队游戏数据
func (c *AgentController) GetTeamGameDataSql(game string, uid, memberUid, agentLv, startTime, endTime int64) string {
	//select
	//	AgentId
	//	,BetLotteryUsers AS BetUsers
	//	,BetLottery AS BetAmount
	//	,LiuShuiQiPai*RewardQiPai/100.0 AS Commission
	//	from x_agent_commission_level_date
	//	where AgentId=123 and AgentLevel=1
	//	AND StatDate='2025-07-24'
	//	group BY AgentId

	//时间区间
	strStartTime := ""
	if startTime > 0 {
		strStartTime = fmt.Sprintf("AND StatDate >= '%s' ", abugo.TimeStampToLocalTime(startTime))
	}
	// 注册时间区间
	strEndTime := ""
	if endTime > 0 && endTime > startTime {
		strEndTime = fmt.Sprintf("AND StatDate <= '%s' ", abugo.TimeStampToLocalTime(endTime))
	}

	// 会员uid
	agentId := uid
	if memberUid > 0 {
		agentId = memberUid
	}

	// 代理级别 agentLv是从1开始的 1-3 级代理
	strAgentLv := ""
	if agentLv > 0 {
		strAgentLv = fmt.Sprintf(" AND AgentLevel = %d ", agentLv)
	}

	rawSql := `
   select 
   %s
from x_agent_commission_level_date 
where AgentId=%d
%s
%s
%s
group BY AgentId
 `
	strSql := fmt.Sprintf(rawSql, game,
		agentId, strAgentLv,
		strStartTime,
		strEndTime)

	return strSql
}

type MyCommissionHistoryReq struct {
	Page     int `json:"page"`     // 页码
	PageSize int `json:"pageSize"` // 页大小
}

type MyCommissionHistoryResp struct {
	RecordDate *time.Time `gorm:"column:recordDate" json:"recordDate"` // 日期
	Currency   string     `gorm:"column:currency" json:"currency"`     // 币种
	Amount     float64    `gorm:"column:amount" json:"amount"`         // 金额
}

// 佣金领取历史记录
func (c *AgentController) commission_data_history(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := MyCommissionHistoryReq{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}

	token := server.GetToken(ctx)
	list := make([]*MyCommissionHistoryResp, 0)
	if reqdata.Page <= 0 {
		reqdata.Page = 1
	}
	if reqdata.PageSize <= 0 {
		reqdata.PageSize = 10
	}
	offset := (reqdata.Page - 1) * reqdata.PageSize
	total := int64(0)
	limit := reqdata.PageSize

	tdb := server.Db().GormDao().Table("x_commission_audit")
	tdb.Where("UserId = ? ", token.UserId)
	tdb.Where("State = 4")
	tdb.Where("AgentMode = 2 ")
	tdb.Select("SendTime AS recordDate",
		"Symbol AS currency",
		"Amount AS amount")
	if err = tdb.Count(&total).Error; err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	err = tdb.Limit(limit).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	ctx.Put("total", total)
	ctx.Put("data", list)
	ctx.RespOK()
	return
}

type AgentTeamLevelConfig struct {
	SchemeId          int     `gorm:"column:SchemeId" json:"schemeId"`                   // 方案id
	TeamLevel         int     `gorm:"column:TeamLevel" json:"teamLevel"`                 // 团队等级
	TotalValidUsers   int     `gorm:"column:TotalValidUsers" json:"totalValidUsers"`     // 总有效会员数
	TotalValidLiuShui float64 `gorm:"column:TotalValidLiuShui" json:"totalValidLiuShui"` // 总有效流水
}

type AgentCommissionLevelConfig struct {
	SchemeId           int     `gorm:"column:SchemeId" json:"schemeId"`                     // 方案id
	TeamLevel          int     `gorm:"column:TeamLevel" json:"teamLevel"`                   // 团队等级
	AgentLevel         int     `gorm:"column:AgentLevel" json:"agentLevel"`                 // 代理等级
	RewardHaXi         float64 `gorm:"column:RewardHaXi" json:"rewardHaXi"`                 // 哈希返佣(百分比)
	RewardHaXiRoulette float64 `gorm:"column:RewardHaXiRoulette" json:"rewardHaXiRoulette"` // 哈希轮盘返佣(百分比)
	RewardLottery      float64 `gorm:"column:RewardLottery" json:"rewardLottery"`           // 彩票返佣(百分比)
	RewardLowLottery   float64 `gorm:"column:RewardLowLottery" json:"rewardLowLottery"`     // 低频彩返佣(百分比)
	RewardQiPai        float64 `gorm:"column:RewardQiPai" json:"rewardQiPai"`               // 棋牌返佣(百分比)
	RewardDianZhi      float64 `gorm:"column:RewardDianZhi" json:"rewardDianZhi"`           // 电子返佣(百分比)
	RewardXiaoYouXi    float64 `gorm:"column:RewardXiaoYouXi" json:"rewardXiaoYouXi"`       // 小游戏返佣(百分比)
	RewardLive         float64 `gorm:"column:RewardLive" json:"rewardLive"`                 // 真人返佣(百分比)
	RewardSport        float64 `gorm:"column:RewardSport" json:"rewardSport"`               // 体育返佣(百分比)
	RewardTexas        float64 `gorm:"column:RewardTexas" json:"rewardTexas"`               // 德州返佣(百分比)
}

type AgentConfigResp struct {
	TeamLevelConfig       []*AgentTeamLevelConfig       `gorm:"column:-" json:"teamLevelConfig"`       // 团队层级配置
	CommissionLevelConfig []*AgentCommissionLevelConfig `gorm:"column:-" json:"commissionLevelConfig"` // 佣金层级配置
}

// 三级代:获取代理配置信息
func (c *AgentController) agent_config(ctx *abugo.AbuHttpContent) {
	errcode := 0

	data := new(AgentConfigResp)
	token := server.GetToken(ctx)
	caseId, err := c.GetChannelCaseId(token.ChannelId, token.SellerId)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}

	data.TeamLevelConfig = c.GetTeamConfig(caseId)
	data.CommissionLevelConfig = c.GetCommissionConfig(caseId)
	ctx.RespOK(data)
}

// 三级代:获取渠道对应的代理方案
func (c *AgentController) GetChannelCaseId(cid, sid int) (int, error) {
	caseId := 0
	tdb := server.Db().GormDao().Table("x_channel")
	tdb.Where("ChannelId = ?", cid)
	tdb.Where("SellerId = ?", sid)
	tdb.Where("State = 1")
	tdb.Where("AgentMode = 2")

	if err := tdb.Select("AgentCaseId").Scan(&caseId).Error; err != nil {
		logs.Error("三级代:获取渠道对应的代理方案错误", err)
		return caseId, err
	}
	return caseId, nil
}

// 三级代:获取渠道代理方案对应的团队配置
func (c *AgentController) GetCommissionConfig(caseId int) []*AgentCommissionLevelConfig {
	dataList := make([]*AgentCommissionLevelConfig, 0)
	tdb := server.Db().GormDao().Table("x_agent_commission_level_define")
	tdb.Where("SchemeId = ?", caseId)

	if err := tdb.Find(&dataList).Error; err != nil {
		logs.Error("三级代:获取渠道代理方案对应的团队配置", err)
		return dataList
	}
	return dataList
}

// 三级代:获取渠道代理方案对应的佣金配置
func (c *AgentController) GetTeamConfig(caseId int) []*AgentTeamLevelConfig {
	dataList := make([]*AgentTeamLevelConfig, 0)
	tdb := server.Db().GormDao().Table("x_agent_commission_team_define")
	tdb.Where("SchemeId = ?", caseId)

	if err := tdb.Find(&dataList).Error; err != nil {
		logs.Error("三级代:获取渠道代理方案对应的佣金配置", err)
		return dataList
	}
	return dataList
}

func (c *AgentController) get_commission_new2(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Symbol string `validate:"required"`
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		return
	}
	date := abugo.GetLocalDate()
	date += " 02:00:00"

	loc, _ := time.LoadLocation("Local")
	tx, _ := time.ParseInLocation("2006-01-02 15:04:05", date, loc)
	if time.Now().Before(tx) {
		ctx.RespErrString(true, &errcode, "每日02:00:00以后才可领取")
		return
	}
	token := server.GetToken(ctx)
	presult, err := server.Db().CallProcedure("x_admin_get_commission_new", token.UserId, reqdata.Symbol)
	// 过滤
	//tmMsg := abugo.GetStringFromInterface((*presult)["errmsg"])
	//if len(tmMsg) > 0 {
	//	tmMsg = strings.ReplaceAll(tmMsg, "佣金", "")
	//	(*presult)["errmsg"] = tmMsg
	//}
	if ctx.RespErr(err, &errcode) {
		return
	}
	if ctx.RespProcedureErr(presult) {
		return
	}
	if (*presult)["Id"] != nil {
		env := "测试服,"
		if !server.Debug() {
			env = "正式服,"
		}
		msg := fmt.Sprintf(`%v新的佣金领取订单,请立即审核
编号: %v
金额: %v
时间: %v`, env, (*presult)["Id"], (*presult)["Amount"], (*presult)["NowTime"])
		req.Post(viper.GetString("tgbotapi")+"/sendmsg", msg)

		// 向数据分析平台上报佣金领取事件
		userDao := server.DaoxHashGame().XUser
		userDb := userDao.WithContext(ctx.Gin())
		user, err := userDb.Where(userDao.UserID.Eq(int32(token.UserId))).First()
		if err != nil {
			logs.Error("get_commission_new 获取用户信息失败: userID=%d, err=%v", token.UserId, err)
		} else {
			// 获取佣金金额，转换为float64
			commissionAmount := 0.0
			if amount, ok := (*presult)["Amount"]; ok {
				switch v := amount.(type) {
				case float64:
					commissionAmount = v
				case string:
					if parsed, err := strconv.ParseFloat(v, 64); err == nil {
						commissionAmount = parsed
					}
				case int:
					commissionAmount = float64(v)
				case int64:
					commissionAmount = float64(v)
				}
			}

			// 获取更准确的佣金信息
			commissionType := "代理佣金"
			if reqdata.Symbol == "trx" {
				commissionType = "TRX代理佣金"
			} else if reqdata.Symbol == "usdt" {
				commissionType = "USDT代理佣金"
			}

			// 确定佣金周期（基于当前时间判断）
			commissionPeriod := "日佣金"

			// 获取代理层级信息
			agentLevel := 1 // 默认为1级代理
			if user.IsTopAgent == 1 {
				agentLevel = 0 // 顶级代理
			}

			// 计算佣金比例（基于用户的分成比例）
			commissionRate := 0.0
			if user.FenCheng != "" {
				// 解析FenCheng JSON字符串
				var fenchengMap map[string]interface{}
				if err := json.Unmarshal([]byte(user.FenCheng), &fenchengMap); err == nil {
					// 根据币种获取对应的分成比例
					var fenchengKey string
					if reqdata.Symbol == "trx" {
						fenchengKey = "haxitrx"
					} else if reqdata.Symbol == "usdt" {
						fenchengKey = "haxiusdt"
					}

					if fenchengValue, exists := fenchengMap[fenchengKey]; exists {
						switch v := fenchengValue.(type) {
						case float64:
							commissionRate = v
						case string:
							if parsed, err := strconv.ParseFloat(v, 64); err == nil {
								commissionRate = parsed
							}
						case int:
							commissionRate = float64(v)
						}
					}
				}
			}

			// 来源用户ID设为"multiple"表示来自多个下级用户
			sourceUserID := "multiple_users"

			// 上报佣金领取事件到ThinkingData
			err = datapush.SendCommissionReceiveEvent(
				user,
				commissionType,       // 更准确的佣金类型（包含币种）
				commissionPeriod,     // 佣金周期
				sourceUserID,         // 来源用户ID
				commissionAmount,     // 佣金金额
				commissionRate,       // 实际的佣金比例
				agentLevel,           // 实际的代理层级
				ctx.Gin().ClientIP(), // 客户端IP
			)
			if err != nil {
				logs.Error("get_commission_new 上报佣金领取事件失败: userID=%d, err=%v", token.UserId, err)
			} else {
				logs.Info("get_commission_new 佣金领取事件上报成功: userID=%d, amount=%.2f, symbol=%s",
					token.UserId, commissionAmount, reqdata.Symbol)
			}
		}
	}
	ctx.RespOK()
	_, errx := req.Post(viper.GetString("adminapi") + "/api/nmxgqtpmlbrn")
	if errx != nil {
		logs.Error(viper.GetString("adminapi"), errx)
	}
}
