package single

import (
	"bytes"
	"crypto/aes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
)

type GFGLotteryGame struct {
	GameId string `gorm:"column:GameId"`
	Name   string `gorm:"column:Name"`
}

type GFGLotteryConfig struct {
	url                   string
	key                   string
	currency              string
	brandName             string
	brandNameOrder        string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
	games                 []GFGLotteryGame // 缓存的游戏列表
}

func NewGFGLotteryLogic(params map[string]string, fc func(int) error) *GFGLotteryConfig {
	brand := "gfg_hash"
	var games []GFGLotteryGame
	err := server.Db().GormDao().Table("x_game_list").Select("GameId", "Name").Where("Brand = ?", brand).Scan(&games).Error
	if err != nil {
		logs.Error("GFGLottery 获取 game list 失败 err=", err.Error())
	} else {
		logs.Info("GFGLottery 获取 game list 成功, games count=", len(games))
	}

	return &GFGLotteryConfig{
		url:                   params["url"],
		key:                   params["key"],
		currency:              params["currency"],
		brandName:             brand,
		brandNameOrder:        "gfg_hash_single_order",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
		games:                 games,
	}
}

const cacheKeyGFGHash = "cacheKeyGFGHASH:"

// getGameNameById 根据游戏ID获取游戏名称
func (l *GFGLotteryConfig) getGameNameById(gameId string) string {
	for _, game := range l.games {
		if game.GameId == gameId {
			return game.Name
		}
	}
	return ""
}

func (l *GFGLotteryConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"` //语言代码
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}

	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyGFGHash, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	open := server.GetConfigInt(token.SellerId, 0, "LotteryOpen")
	if open != 1 {
		logs.Error("gfg_hash 单一钱包 LotteryOpen 未开启 SellerId=", token.SellerId, "userId=", token.UserId)
		ctx.RespErrString(true, &errcode, "暂未开放,敬请期待")
		return
	}
	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(token.UserId, "gfg", fmt.Sprintf("%d", reqdata.GameId)); err != nil {
		logs.Error("gfg_single 登录游戏 权限检查错误 userId=", token.UserId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("gfg_single 登录游戏 权限被拒绝 userId=", token.UserId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	// 查询游戏ID是否存在
	gameList, _ := server.XDb().Table("x_game_list").Where("GameId = ? AND Brand=?", fmt.Sprintf("%d", reqdata.GameId), "gfg").First()
	if gameList == nil {
		logs.Error("gfg_hash 单一钱包 游戏不存在 GameId=", reqdata.GameId)
		ctx.RespErrString(true, &errcode, "暂未开放,敬请期待")
		return
	}
	// 判断游戏是否开放
	gameStatus := abugo.GetInt64FromInterface(gameList.RawData["State"])
	gameOpenState := abugo.GetInt64FromInterface(gameList.RawData["OpenState"])
	if gameStatus != 1 || gameOpenState != 1 {
		logs.Error("gfg_hash 单一钱包 游戏未开放 GameId=", reqdata.GameId, " State=", gameStatus, " OpenState=", gameOpenState)
		ctx.RespErrString(true, &errcode, "暂未开放,敬请期待")
		return
	}

	page := 1
	if reqdata.GameId > 0 {
		page = 2
	}
	reqstr := fmt.Sprintf(`{"Merchantkey": "%s","Account": "%d","NickName": "%d","AgentType": "0","Platform": "mobile"}`, l.key, token.UserId, token.UserId)
	param, sign := l.lottery_sign(reqstr)

	other := fmt.Sprintf("&gameId=%d&page=%d&languageCode=%s", reqdata.GameId, page, reqdata.LangCode)
	urls := fmt.Sprintf("%s%s?param=%s&key=%s%s", l.url, "/Account/GetLogin", param, sign, other)

	httpclients := httpc.DoRequest{
		UrlPath: urls,
	}
	body, err := httpclients.DoGet()
	logs.Error("gfg_hash 单一钱包  lottery_login", urls, string(body), err)
	if err != nil {
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	type res struct {
		Success bool   `json:"Success"`
		Message string `json:"Message"`
		Data    struct {
			Url string `json:"Url"`
		} `json:"Data"`
	}
	resp1 := res{}
	err = json.Unmarshal(body, &resp1)
	if err != nil {
		logs.Error("gfg_hash 单一钱包  Unmarshal", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	if !resp1.Success {
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}

	rurl := resp1.Data.Url
	rurl = fmt.Sprint(rurl, "&t=", rand.Int()%100000)

	rurl = ChangeHttpToHttps(rurl)
	logs.Debug("lottery_login:", token.UserId, rurl)
	ctx.RespOK(rurl)
}

// 获取所有玩家下注记录
func (l *GFGLotteryConfig) GetReportDetail(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		StartDate string `validate:"required"` //开始时间,格式:”2018-01-01 02:00:00”
		EndDate   string `validate:"required"` //结束时间,格式:”2018-01-02 02:00:00”
		PageIndex int    `validate:"required"` //页码，默认值1，必须大于0
		PageSize  int    `validate:"required"` //每页数据量，必须大于0并且小于等于1000
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}

	reqstr := fmt.Sprintf(`{"Merchantkey": "%s","StartDate": "%s","EndDate": "%s","PageIndex": %d,"PageSize": %d}`, l.key, reqdata.StartDate, reqdata.EndDate, reqdata.PageIndex, reqdata.PageSize)
	param, sign := l.lottery_sign(reqstr)
	urls := fmt.Sprintf("%s%s?param=%s&key=%s", l.url, "/Account/GetReportDetail", param, sign)

	httpclients := httpc.DoRequest{
		UrlPath: urls,
	}

	type messagedata struct {
		Merchantkey       string  `json:"Merchantkey"`
		OrderNumber       string  `json:"OrderNumber"`
		NumberOfPeriod    string  `json:"NumberOfPeriod"`
		GameId            int     `json:"GameId"`
		GameName          string  `json:"GameName"`
		IpAddress         string  `json:"IpAddress"`
		Times             int     `json:"Times"`
		BetNumber         int     `json:"BetNumber"`
		ODDS              float64 `json:"ODDS"`
		Content           string  `json:"Content"`
		TotalAmount       float64 `json:"TotalAmount"`
		GamePlayName      string  `json:"GamePlayName"`
		CreateTime        string  `json:"CreateTime"`
		Status            int     `json:"Status"`
		Platform          string  `json:"Platform"`
		BettingBalance    float64 `json:"BettingBalance"`
		TotalKickback     float64 `json:"TotalKickback"`
		Result            string  `json:"Result"`
		LotteryCreateTime string  `json:"LotteryCreateTime"`
		AccountName       string  `json:"AccountName"`
		SettleTime        string  `json:"SettleTime"`
		WinningStatus     int     `json:"WinningStatus"`
		IsTrack           bool    `json:"IsTrack"`
	}

	type res struct {
		Success bool   `json:"Success"`
		Message string `json:"Message"`
		Data    []*messagedata
		Total   int `json:"Total"`
	}

	body, err := httpclients.DoGet()
	logs.Debug("body:", string(body))
	resp1 := res{}
	err = json.Unmarshal(body, &resp1)
	if err != nil {
		logs.Error("gfg_hash 单一钱包  Unmarshal", err)
		ctx.RespErrString(true, &errcode, "获取用户")
		return
	}

	logs.Error("gfg_hash 单一钱包  lottery_reportDetail", urls, resp1, err)
	if err != nil {
		logs.Error("获取订单失败%s----%s", sign, reqdata)
		ctx.RespErrString(true, &errcode, "获取订单失败")
		return
	}

	ctx.RespOK(resp1)
}

func (l *GFGLotteryConfig) lottery_sign(reqStr string) (string, string) {
	EncryptPassword := "LXDCKEY1"
	EncryptionOffset := "LXDCKEY2"
	key := []byte(EncryptPassword) // des加密密码
	iv := []byte(EncryptionOffset) // des加密偏移量
	out, _ := abugo.DesCbcEncrypt([]byte(reqStr), key, iv)
	deByte := abugo.Base64Encode(out)
	encryptedreqdata := string(deByte)                   // des加密后的密文
	encryptedreqdata = url.QueryEscape(encryptedreqdata) // url编码后的密文
	hashmd5 := md5.Sum([]byte(reqStr + l.key))
	hashedkey := hex.EncodeToString(hashmd5[:])
	return encryptedreqdata, hashedkey
}
func encrypt(key string, plaintext string) (string, error) {
	upperKey := []byte(strings.ToUpper(key))
	block, err := aes.NewCipher(upperKey)
	if err != nil {
		return "", err
	}
	plainBytes := []byte(plaintext)
	// 对于ECB模式，直接使用cipher.NewCBCEncrypter即可
	//if len(plainBytes)%aes.BlockSize != 0 {
	plainBytes = pad(plainBytes, aes.BlockSize)
	//}
	ciphertext := make([]byte, len(plainBytes))
	for start := 0; start < len(plainBytes); start += aes.BlockSize {
		block.Encrypt(ciphertext[start:start+aes.BlockSize], plainBytes[start:start+aes.BlockSize])
	}
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func decrypt(key string, ciphertext string) (string, error) {
	upperKey := []byte(strings.ToUpper(key))
	block, err := aes.NewCipher(upperKey)
	if err != nil {
		return "", err
	}
	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}
	plaintext := make([]byte, len(cipherBytes))
	// 对于ECB模式，直接使用cipher.NewCBCDecrypter即可
	for start := 0; start < len(cipherBytes); start += aes.BlockSize {
		block.Decrypt(plaintext[start:start+aes.BlockSize], cipherBytes[start:start+aes.BlockSize])
	}
	return string(unpad(plaintext)), nil
}

// pad 使用PKCS7填充
func pad(buf []byte, blockSize int) []byte {
	padding := blockSize - (len(buf) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(buf, padText...)
}

// unpad 移除PKCS7填充
func unpad(buf []byte) []byte {
	length := len(buf)
	if length == 0 {
		return buf
	}
	padding := int(buf[length-1])
	return buf[:length-padding]
}

type respData struct {
	Status  bool   `json:"Status"`
	Message string `json:"Message"`
	Data    string `json:"Data"`
}

func (l *GFGLotteryConfig) GetUserBalance(ctx *abugo.AbuHttpContent) {

	respResponse := respData{}

	reqstr := ctx.Gin().PostForm("data")
	//logs.Info(l.brandName, "gfg_hash GetUserBalance =================  req:= ", reqstr)

	reqstr, err := decrypt(l.key, reqstr)
	if err != nil {
		// Логируем ошибку без раскрытия чувствительной информации
		logs.Error("gfg_hash GetUserBalance: decryption error")

		respResponse.Status = false
		respResponse.Message = "处理请求时发生错误" // "Произошла ошибка при обработке запроса"
		ctx.RespJson(respResponse)
		return
	}

	if reqstr == "" {
		respResponse.Status = false
		respResponse.Message = "参数为空"
		logs.Error("gfg_hash GetUserBalance =================  reply:= ", respResponse)
		ctx.RespJson(respResponse)
		return
	}

	type req struct {
		SerialKey string
		Account   string
	}

	reqData := req{}
	err = json.Unmarshal([]byte(reqstr), &reqData)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "json解析出错"
		logs.Error("gfg_hash GetUserBalance =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}

	userId, _ := strconv.Atoi(reqData.Account)
	_, balance, err := base.GetUserBalance(userId, cacheKeyGFGHash)
	balance = float64(int(balance*100)) / 100
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "获取玩家信息失败"
		logs.Error("gfg_hash GetUserBalance =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}

	type data struct {
		Account string
		Balance float64
	}
	resps := data{
		Account: reqData.Account,
		Balance: balance,
	}
	respsJson, _ := json.Marshal(resps)
	dataStr, err := encrypt(l.key, string(respsJson))
	logs.Error("gfg_hash GetUserBalance =================  decrypt  encrypt err", dataStr, err)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "加密错误"
		logs.Error("gfg_hash GetUserBalance =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}

	respResponse.Status = true
	respResponse.Data = dataStr
	logs.Error("gfg_hash GetUserBalance =================  reply:= ", respResponse, err)
	ctx.RespJson(respResponse)
	return

}

func (l *GFGLotteryConfig) GetAllUserBalance(ctx *abugo.AbuHttpContent) {
	//没有代理  忽略

}

type bet struct {
	ID             int64
	UserID         int64
	SerialKey      string
	Account        string
	NotifyId       int
	OrderNumber    string
	NumberOfPeriod string
	DepositFlag    bool
	Amount         float64
	GameId         int64
	GameName       string
	PeriodId       int64
	PeriodResult   string
	Sign           string
	PlayName       string
	Odds           float64
	BetContent     string
	BetNumber      int
	BetAmount      float64
	Times          int
	PlayId         int
}

type betV2 struct {
	ID             int64
	UserID         int64
	SerialKey      string
	Account        string
	NotifyId       int
	OrderNumber    string
	NumberOfPeriod string
	DepositFlag    bool
	Amount         float64
	GameId         int64
	GameName       string
	PeriodId       int64
	PeriodResult   string
	Sign           string
	PlayName       string
	Odds           float64
	ReturnMoney    float64

	BetContent string
	BetNumber  int
	BetAmount  float64
	Times      int
}

func (l *GFGLotteryConfig) GetSingleWalletQuota(ctx *abugo.AbuHttpContent) {
	respResponse := respData{}

	reqstr := ctx.Gin().PostForm("data")
	logs.Info(l.brandName, "gfg_hash GetSingleWalletQuota =================  req:= ", reqstr)

	reqstr, err := decrypt(l.key, reqstr)
	if err != nil {
		// Логируем ошибку без раскрытия чувствительной информации
		logs.Error("gfg_hash GetSingleWalletQuota: decryption error")

		respResponse.Status = false
		respResponse.Message = "处理请求时发生错误" // "Произошла ошибка при обработке запроса"
		ctx.RespJson(respResponse)
		return
	}

	if reqstr == "" {
		respResponse.Status = false
		respResponse.Message = "参数为空"
		logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse)
		ctx.RespJson(respResponse)
		return
	}

	type reqs struct {
		TotalAmount float64
		Data        []*bet
	}

	reqData := reqs{}
	err = json.Unmarshal([]byte(reqstr), &reqData)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "json解析出错"
		logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}
	returndata := reqs{}
	ntUserIds := make(map[int]struct{})

	for _, v := range reqData.Data {
		//签名 NotifyId+NumberOfPeriod+OrderNumber+DepositFlag+Amount+SerialKey
		str := strconv.Itoa(v.NotifyId) + v.NumberOfPeriod + v.OrderNumber + base.ToTitleFirst(strconv.FormatBool(v.DepositFlag)) + strconv.FormatFloat(v.Amount, 'f', 4, 64) + v.SerialKey
		sign := base.MD5(str)
		if v.Sign != sign {
			logs.Error("gfg_hash str | sign | third sign ", str, "|", sign, "|", v.Sign)
			continue
		}
		reqBytes, _ := json.Marshal(v)
		//logs.Info("gfg_hash 加减款请求中", string(reqBytes))
		userId, _ := strconv.Atoi(v.Account)
		udata, balance, err := base.GetUserBalance(userId, cacheKeyGFGHash)
		balance = float64(int(balance*100)) / 100
		if err != nil {
			logs.Error("gfg_hash Bet =================  获取用户信息 ", err)
			continue
		}

		// 获取用户渠道ID
		ChannelId := base.GetUserChannelIdFromMap(ctx, udata, userId)

		ntUserIds[userId] = struct{}{}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(abugo.GetStringFromInterface((*udata)["Token"])).Host)
		//ChannelId := base.GetUserChannelId(ctx, user)
		// 查询游戏ID是否存在
		gameList, _ := server.XDb().Table("x_game_list").Where("GameId = ? AND Brand=?", fmt.Sprintf("%d", v.GameId), "gfg").First()
		if gameList == nil {
			logs.Error("gfg_hash Bet =================  游戏ID不存在 ", v.GameId, v)
			continue
		}

		//logs.Info("gfg_hash Bet ======通知ID========= ", v.NotifyId)
		switch v.NotifyId {
		case 9:
			//下注
			//logs.Info("gfg_hash Bet =======下注====1======")
			if v.DepositFlag {
				logs.Error("gfg_hash Bet ================= v.DepositFlag  不正确", v.DepositFlag)
				continue
			}
			//三方来源的数据整理
			var (
				betAmount = v.Amount
				thirdId   = v.OrderNumber
				gamecode  = v.GameId
				gameName  = v.GameName
				thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			)
			if betAmount > balance {
				continue
			}

			//查询订单是否存在
			betTran, isExist := base.OrderIsExist(thirdId, l.brandName, "x_third_lottery_pre_order")
			if isExist {
				continue
			}
			//开启事务
			err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
				ressql, err2 := tx.Tx.Exec("update x_user set amount = amount - ? where UserId = ? and amount>= ?", betAmount, userId, betAmount)
				row, err3 := ressql.RowsAffected()
				if err2 != nil || err3 != nil || row < 1 {
					logs.Debug(l.brandName, "下注 修改x_user失败了:  id = ", thirdId, err)

					return errors.New("修改x_user失败了")
				}

				if betTran != nil {
					betId := abugo.GetInt64FromInterface((*betTran)["Id"])
					_, err = tx.Tx.Exec(`update x_third_lottery_pre_order set BetAmount = BetAmount + ? where Id = ?`, betAmount, betId)
					if err != nil {
						logs.Debug(l.brandName, "下注 修改 x_third_lottery_pre_order 修改失败了:  id = ", thirdId, err)

						return err
					}
				} else {
					// 获取六合彩游戏分类ID
					var CatId int32
					if v.GameId == 1 {
						xLiuHeCaiMapDao := server.DaoxHashGame().XLiuhecaiMap
						xLiuHeCaiMapDb := xLiuHeCaiMapDao.WithContext(nil)
						xLiuHeCaiMap, _ := xLiuHeCaiMapDb.Where(xLiuHeCaiMapDao.Sid.Eq(int32(v.PlayId))).First()
						logs.Info("六合彩PlayId:", v.PlayId)
						if xLiuHeCaiMap != nil {
							CatId = xLiuHeCaiMap.ID
						}
					}

					//获取三方游戏名称
					order := xgo.H{
						"SellerId":     (*udata)["SellerId"],
						"ChannelId":    (*udata)["ChannelId"],
						"BetChannelId": ChannelId,
						"UserId":       userId,
						"Brand":        l.brandName,
						"ThirdId":      thirdId,
						"GameId":       gamecode,
						"GameName":     gameName,
						"BetAmount":    betAmount,
						"WinAmount":    0,
						"ValidBet":     0,
						"ThirdTime":    thirdTime,
						"BetTime":      thirdTime,
						"Currency":     l.currency,
						"RawData":      string(reqBytes),
						"DataState":    -1,
						"BetCtx":       v.PlayName + " 下注内容:" + v.BetContent,
						"SubCatId":     v.PlayId,
						"CatId":        CatId,
					}

					_, err = tx.Table("x_third_lottery_pre_order").Insert(order)
					if err != nil {
						logs.Debug(l.brandName, "下注 修改 x_third_lottery_pre_order 新增失败了:  id = ", thirdId, err)

						return err
					}
				}

				amountLog := xgo.H{
					"UserId":       userId,
					"BeforeAmount": balance,
					"Amount":       0 - betAmount,
					"AfterAmount":  balance - betAmount,
					"Reason":       utils.BalanceCReasonGfGHASHBet,
					"Memo":         l.brandName + " bet,thirdId:" + thirdId,
					"SellerId":     (*udata)["SellerId"],
					"ChannelId":    (*udata)["ChannelId"],
				}
				logs.Info("下注 新增x_amount_change_log  userId = ", userId, l.brandName+" bet,thirdId:"+thirdId)
				_, err = tx.Table("x_amount_change_log").Insert(amountLog)
				if err != nil {
					logs.Debug(l.brandName, "下注 新增x_amount_change_log失败了:  id = ", thirdId, err)

					return err
				}

				return nil
			})
			if err != nil {
				continue
			}
			returndata.TotalAmount += v.Amount
			returndata.Data = append(returndata.Data, v)
			// 推送下注事件通知
			if l.thirdGamePush != nil {
				l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, l.currency, l.brandName, v.OrderNumber, 4)
			}
			break
		case 31:
			//撤回
			//三方来源的数据整理
			var (
				amount  = v.Amount
				thirdId = v.OrderNumber
			)
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			betTran, err := server.Db().Table("x_third_lottery_pre_order").Where(where).GetOne()
			if betTran == nil {
				//订单不存在
				logs.Debug("gfg_hash 订单不存在:  id = ", thirdId)
				continue
			}
			dataState := abugo.GetInt64FromInterface((*betTran)["DataState"])
			if dataState == -2 {
				//已经取消了
				logs.Debug("gfg_hash 订单已经取消了:  id = ", thirdId)
				continue
			}
			//开启事务
			err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
				//修改消了
				betId := abugo.GetInt64FromInterface((*betTran)["Id"])
				_, err = tx.Tx.Exec(`update x_third_lottery_pre_order set
		RawData = ?,
		DataState = -2
		where Id = ?`,
					string(reqBytes),
					betId,
				)
				if err != nil {
					logs.Debug("gfg_hash 订单取消 修改 x_third_lottery_pre_order 状态失败了:  id = ", thirdId, err)

					return err
				}

				ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
				row, err3 := ressql.RowsAffected()
				if err2 != nil || err3 != nil || row < 1 {
					logs.Debug("gfg_hash 订单取消 操作金币上分失败了:  id = ", thirdId, err2, err3)

					return errors.New("修改x_user失败了")
				}

				amountLog := xgo.H{
					"UserId":       userId,
					"BeforeAmount": balance,
					"Amount":       amount,
					"AfterAmount":  balance + amount,
					"Reason":       utils.BalanceCReasonGfGHASHREFUND,
					"Memo":         "gfg_hash cancel,thirdId:" + thirdId,
					"SellerId":     (*udata)["SellerId"],
					"ChannelId":    (*udata)["ChannelId"],
				}
				_, err = tx.Table("x_amount_change_log").Insert(amountLog)
				if err != nil {
					logs.Debug("gfg_hash 订单取消 操作金币日志失败了:  id = ", thirdId, err)

					return err
				}
				logs.Debug("gfg_hash 订单取消成功了:  id = ", thirdId, err)

				return nil
			})
			if err != nil {
				continue
			}
			returndata.TotalAmount += v.Amount
			returndata.Data = append(returndata.Data, v)
			break
		case 1:
			//派奖
			//三方来源的数据整理
			var (
				amount    = v.Amount
				thirdId   = v.OrderNumber
				thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			)

			//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			where.Add("and", "DataState", "=", -1, nil)
			betTran, err := server.Db().Table("x_third_lottery_pre_order").Where(where).GetOne()
			if err != nil {
				logs.Error("----数据库查询失败----")
				continue
			} else {
				if betTran == nil {
					logs.Error("gfg_hash  订单已经处理了 1 thirdId", thirdId)
				} else {
					//开启事务
					err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
						betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
						validBet := math.Abs(amount - betAmount)
						if validBet > math.Abs(betAmount) {
							validBet = math.Abs(betAmount)
						}
						//将下注订单移动至结算订单表
						//修改成已经结算了
						betId := abugo.GetInt64FromInterface((*betTran)["Id"])
						_, err = tx.Tx.Exec(`update x_third_lottery_pre_order set
			WinAmount = ?,
			RawData = ?,
			ThirdTime = ?,
			ValidBet = ?,
			GameRst = ?,
			DataState = 1
			where Id = ?`,
							amount,
							string(reqBytes),
							thirdTime,
							validBet,
							v.PeriodResult,
							betId,
						)

						if err != nil {
							logs.Error("gfg_hash  订单已经处理了  thirdId", thirdId)
							return err
						}
						//移动至统计表
						tmp := (*betTran)
						delete(tmp, "Id")
						delete(tmp, "CreateTime")
						tmp["DataState"] = 1
						tmp["WinAmount"] = amount
						tmp["RawData"] = string(reqBytes)
						tmp["ThirdTime"] = thirdTime
						tmp["ValidBet"] = validBet
						tmp["GameRst"] = v.PeriodResult

						_, err = tx.Table("x_third_lottery").Insert(tmp)
						if err != nil {
							logs.Debug("gfh_hash 结算 移动至统计表 x_third_lottery 失败了:  id = ", thirdId, err)

							return err
						}
						//处理结算
						logs.Info("------GetSingleWalletQuota----派奖-----2----", string(reqBytes))
						if amount > 0 {
							//win
							ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
							row, err3 := ressql.RowsAffected()
							if err2 != nil || err3 != nil || row < 1 {
								logs.Debug("gfg_hash 结算 处理结算 x_user 失败了:  id = ", thirdId, err2, err3)

								return errors.New("修改x_user失败了")
							}

							amountLog := xgo.H{
								"UserId":       userId,
								"BeforeAmount": balance,
								"Amount":       amount,
								"AfterAmount":  balance + amount,
								"Reason":       utils.BalanceCReasonGfGHASHSettle,
								"Memo":         "gfg_hash settle,thirdId:" + thirdId,
								"SellerId":     (*udata)["SellerId"],
								"ChannelId":    (*udata)["ChannelId"],
							}
							_, err = tx.Table("x_amount_change_log").Insert(amountLog)
							if err != nil {
								logs.Debug("gfg_hash 结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)

								return err
							}
						}
						logs.Debug("gfg_hash sw :", "結算成功", thirdId)
						return nil
					})
					// 推送派奖事件通知
					if l.thirdGamePush != nil {
						//gameName := abugo.GetStringFromInterface((*betTran)["GameName"])
						//l.thirdGamePush.PushRewardEvent(userId, gameName, l.brandName, betAmount, v.Amount, l.currency)
						//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
						l.thirdGamePush.PushRewardEvent(4, l.brandName, thirdId)
					}
				}
				returndata.TotalAmount += v.Amount
				returndata.Data = append(returndata.Data, v)
			}

			break
		case 27:
			//返点
			var (
				amount    = v.Amount
				thirdId   = v.OrderNumber
				thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			)

			//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			//where.Add("and", "DataState", "=", -10, nil)
			betTran, err := server.Db().Table("x_third_lottery").Where(where).GetOne()
			if err != nil {
				logs.Error("gfg_hash 数据库查询失败  thirdId", thirdId)
				continue
			} else {
				if betTran == nil {
					logs.Error("gfg_hash 27 订单已经处理了  thirdId", thirdId)
				} else {
					betNew := betV2{}
					json.Unmarshal([]byte(abugo.GetStringFromInterface((*betTran)["RawData"])), &betNew)
					betNew.ReturnMoney = v.Amount
					reqBytes2, _ := json.Marshal(betNew)
					//开启事务
					err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
						//处理结算
						if amount > 0 {
							betId := abugo.GetInt64FromInterface((*betTran)["Id"])
							WinAmount := abugo.GetFloat64FromInterface((*betTran)["WinAmount"]) + amount
							_, err = tx.Tx.Exec("update x_third_lottery set WinAmount = ?,RawData = ?,ThirdTime = ? where Id = ?", WinAmount, string(reqBytes2), thirdTime, betId)

							if err != nil {
								logs.Error("gfg_hash  订单已经处理了  thirdId", thirdId)
								return err
							}

							//返点
							ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
							row, err3 := ressql.RowsAffected()
							if err2 != nil || err3 != nil || row < 1 {
								logs.Debug("gfg_hash 结算 处理结算 x_user 失败了:  id = ", thirdId, err2, err3)

								return errors.New("修改x_user失败了")
							}
							amountLog := xgo.H{
								"UserId":       userId,
								"BeforeAmount": balance,
								"Amount":       amount,
								"AfterAmount":  balance + amount,
								"Reason":       utils.BalanceCReasonGfGHASHReturn,
								"Memo":         "gfg_hash settle,thirdId:" + thirdId,
								"SellerId":     (*udata)["SellerId"],
								"ChannelId":    (*udata)["ChannelId"],
							}
							_, err = tx.Table("x_amount_change_log").Insert(amountLog)
							if err != nil {
								logs.Debug("gfg_hash 返点 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)

								return err
							}
						}
						logs.Debug("gfg_hash sw :", "返点成功", thirdId)

						return nil
					})
				}
				returndata.TotalAmount += v.Amount
				returndata.Data = append(returndata.Data, v)
			}
			break
		case 43:
			// 打和返款
			// 相当于结算  把下注金额返还给用户 并且不计算流水
			var (
				amount    = v.Amount
				thirdId   = v.OrderNumber
				thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			)

			//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			where.Add("and", "DataState", "=", -1, nil)
			betTran, err := server.Db().Table("x_third_lottery_pre_order").Where(where).GetOne()
			if err != nil {
				logs.Error("----数据库查询失败----")
				continue
			} else {
				if betTran == nil {
					logs.Error("gfg_hash  订单已经处理了 1 thirdId", thirdId)
				} else {
					//开启事务
					err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
						//将下注订单移动至结算订单表
						//修改成已经结算了
						betId := abugo.GetInt64FromInterface((*betTran)["Id"])
						_, err = tx.Tx.Exec(`update x_third_lottery_pre_order set
			WinAmount = ?,
			RawData = ?,
			ThirdTime = ?,
			ValidBet = 0,
			GameRst = ?,
			DataState = 1
			where Id = ?`,
							amount,
							string(reqBytes),
							thirdTime,
							v.PeriodResult,
							betId,
						)

						if err != nil {
							logs.Error("gfg_hash  订单已经处理了  thirdId", thirdId)
							return err
						}
						//移动至统计表
						tmp := (*betTran)
						delete(tmp, "Id")
						delete(tmp, "CreateTime")
						tmp["DataState"] = 1
						tmp["WinAmount"] = amount
						tmp["RawData"] = string(reqBytes)
						tmp["ThirdTime"] = thirdTime
						tmp["ValidBet"] = 0
						tmp["GameRst"] = v.PeriodResult

						_, err = tx.Table("x_third_lottery").Insert(tmp)
						if err != nil {
							logs.Debug("gfh_hash 结算 移动至统计表 x_third_lottery 失败了:  id = ", thirdId, err)

							return err
						}
						//处理结算
						logs.Info("------GetSingleWalletQuota----打和返款-----2----", string(reqBytes))
						if amount > 0 {
							//win
							ressql, err2 := tx.Tx.Exec("update x_user set amount = amount + ? where UserId = ?", amount, userId)
							row, err3 := ressql.RowsAffected()
							if err2 != nil || err3 != nil || row < 1 {
								logs.Debug("gfg_hash 结算 处理结算 x_user 失败了:  id = ", thirdId, err2, err3)

								return errors.New("修改x_user失败了")
							}

							amountLog := xgo.H{
								"UserId":       userId,
								"BeforeAmount": balance,
								"Amount":       amount,
								"AfterAmount":  balance + amount,
								"Reason":       utils.BalanceCReasonGfGHASHSettle,
								"Memo":         "gfg_hash settle,thirdId:" + thirdId,
								"SellerId":     (*udata)["SellerId"],
								"ChannelId":    (*udata)["ChannelId"],
							}
							_, err = tx.Table("x_amount_change_log").Insert(amountLog)
							if err != nil {
								logs.Debug("gfg_hash 结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)

								return err
							}
						}
						logs.Debug("gfg_hash sw :", "結算成功", thirdId)

						return nil
					})
				}
				returndata.TotalAmount += v.Amount
				returndata.Data = append(returndata.Data, v)
			}
			break
		default:
			logs.Info("notifyId不在处中", string(reqBytes))
			break
		}
	}
	returnbody, _ := base.MyJson.Marshal(returndata)

	dataStr, err := encrypt(l.key, string(returnbody))
	// logs.Error("gfg_hash GetSingleWalletQuota =================   encrypt  err", dataStr, string(returnbody), err)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "加密错误"
		logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}

	respResponse.Status = true
	respResponse.Data = dataStr
	// logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse, err)
	ctx.RespJson(respResponse)

	// 发送余额变动通知
	for vUserId := range ntUserIds {
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][GFGLotteryConfig] GetSingleWalletQuota 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][GFGLotteryConfig] GetSingleWalletQuota 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(vUserId)
	}

	return
}

func (l *GFGLotteryConfig) PushLoseOrder(ctx *abugo.AbuHttpContent) {
	respResponse := respData{}

	reqstr := ctx.Gin().PostForm("data")
	logs.Info(l.brandName, "gfg_hash PushLoseOrder =================  req:= ", reqstr)

	reqstr, err := decrypt(l.key, reqstr)
	if err != nil {
		// Логируем ошибку без раскрытия чувствительной информации
		logs.Error("gfg_hash PushLoseOrder: decryption error")

		respResponse.Status = false
		respResponse.Message = "处理请求时发生错误" // "Произошла ошибка при обработке запроса"
		ctx.RespJson(respResponse)
		return
	}

	if reqstr == "" {
		respResponse.Status = false
		respResponse.Message = "参数为空"
		logs.Error("gfg_hash PushLoseOrder =================  reply:= ", respResponse)
		ctx.RespJson(respResponse)
		return
	}

	type reqs struct {
		TotalAmount float64
		Data        []*bet
	}

	reqData := reqs{}
	err = json.Unmarshal([]byte(reqstr), &reqData)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "json解析出错"
		logs.Error("gfg_hash PushLoseOrder =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}
	returndata := reqs{}
	for _, v := range reqData.Data {
		//签名 NotifyId+NumberOfPeriod+OrderNumber+DepositFlag+Amount+SerialKey
		str := strconv.Itoa(v.NotifyId) + v.NumberOfPeriod + v.OrderNumber + base.ToTitleFirst(strconv.FormatBool(v.DepositFlag)) + "0" + v.SerialKey
		userId, _ := strconv.Atoi(v.Account)
		udata, balance, err := base.GetUserBalance(userId, cacheKeyGFGHash)
		balance = float64(int(balance*100)) / 100
		sign := base.MD5(str)
		if v.Sign != sign {
			logs.Error("gfg_hash str | sign | third sign ", str, "|", sign, "|", v.Sign)
			continue
		}
		reqBytes, _ := json.Marshal(v)

		if err != nil {
			logs.Error("gfg_hash PushLoseOrder =================  获取用户信息 ", string(reqBytes))
			continue
		}
		if v.Amount > 0 {
			logs.Error("gfg_hash PushLoseOrder =================  数据源不正确 ", string(reqBytes))
			continue
		}

		switch v.NotifyId {
		case 1:
			//未中奖  改成结算订单
			//三方来源的数据整理

			var (
				amount    = v.Amount
				thirdId   = v.OrderNumber
				thirdTime = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
			)

			//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			where.Add("and", "DataState", "=", -1, nil)
			betTran, err := server.Db().Table("x_third_lottery_pre_order").Where(where).GetOne()
			if err != nil {
				logs.Error("数据库查询失败  thirdId", thirdId)
				continue
			} else {
				if betTran == nil {
					logs.Error("gfg_hash PushLoseOrder 1 订单已经处理了  thirdId", thirdId)
				} else {
					//开启事务
					err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
						betAmount := abugo.GetFloat64FromInterface((*betTran)["BetAmount"])
						validBet := math.Abs(amount - betAmount)
						if validBet > math.Abs(betAmount) {
							validBet = math.Abs(betAmount)
						}
						//将下注订单移动至结算订单表
						//修改成已经结算了

						betNew := bet{}
						json.Unmarshal([]byte(abugo.GetStringFromInterface((*betTran)["RawData"])), &betNew)
						v.Times = betNew.Times
						reqBytes2, _ := json.Marshal(v)

						betId := abugo.GetInt64FromInterface((*betTran)["Id"])
						_, err = tx.Tx.Exec(`update x_third_lottery_pre_order set
				WinAmount = ?,
				RawData = ?,
				ThirdTime = ?,
				ValidBet = ?,
				GameRst = ?,
				DataState = 1
				where Id = ?`,
							amount,
							string(reqBytes2),
							thirdTime,
							validBet,
							v.PeriodResult,
							betId,
						)

						if err != nil {
							logs.Error("gfg_hash  订单已经处理了  thirdId", thirdId)
							return err
						}
						//移动至统计表
						tmp := (*betTran)
						delete(tmp, "Id")
						delete(tmp, "CreateTime")
						tmp["DataState"] = 1
						tmp["WinAmount"] = amount
						tmp["RawData"] = string(reqBytes2)
						tmp["ThirdTime"] = thirdTime
						tmp["ValidBet"] = validBet
						tmp["GameRst"] = v.PeriodResult
						_, err = tx.Table("x_third_lottery").Insert(tmp)
						if err != nil {
							logs.Debug("gfh_hash 结算 移动至统计表 x_third_lottery 失败了:  id = ", thirdId, err)

							return err
						}
						logs.Debug("gfg_hash sw :", "未中奖  改成结算订单 結算成功")

						amountLog := xgo.H{
							"UserId":       userId,
							"BeforeAmount": balance,
							"Amount":       amount,
							"AfterAmount":  balance + amount,
							"Reason":       utils.BalanceCReasonGfGHASHSettle,
							"Memo":         "gfg_hash settle,thirdId:" + thirdId,
							"SellerId":     (*udata)["SellerId"],
							"ChannelId":    (*udata)["ChannelId"],
						}
						_, err = tx.Table("x_amount_change_log").Insert(amountLog)
						if err != nil {
							logs.Debug("gfg_hash 结算 处理金币变化记录 x_amount_change_log 失败了:  id = ", thirdId, err)

							return err
						}

						return nil
					})
				}
				returndata.TotalAmount += v.Amount
				returndata.Data = append(returndata.Data, v)
			}
			break
		case 27:
			//返点为0处理
			var (
				thirdId = v.OrderNumber
			)

			//判断订单是否处理过  DataState -2取消订单 -1下注订单  1结算后的订单
			where := abugo.AbuDbWhere{}
			where.Add("and", "ThirdId", "=", thirdId, nil)
			where.Add("and", "Brand", "=", "gfg_hash", nil)
			//where.Add("and", "DataState", "=", -10, nil)
			betTran, err := server.Db().Table("x_third_lottery").Where(where).GetOne()
			if err != nil {
				logs.Error("gfg_hash PushLoseOrder 27  thirdId", thirdId)
				continue
			} else {
				if betTran == nil {
					logs.Error("gfg_hash PushLoseOrder 27：订单已经处理 ")
				} else {
					betNew := betV2{}
					json.Unmarshal([]byte(abugo.GetStringFromInterface((*betTran)["RawData"])), &betNew)
					betNew.ReturnMoney = v.Amount
					reqBytes2, _ := json.Marshal(betNew)
					//开启事务
					err = server.XDb().Transaction2(func(tx *xgo.XTx) error {
						//处理结算
						betId := abugo.GetInt64FromInterface((*betTran)["Id"])
						_, err = tx.Tx.Exec("update x_third_lottery set RawData = ? where Id = ?", string(reqBytes2), betId)

						if err != nil {
							logs.Error("gfg_hash  订单已经处理了  thirdId", thirdId)
							return err
						}

						logs.Debug("gfg_hash sw :", "返点成功")

						return nil
					})
				}
				returndata.TotalAmount += v.Amount
				returndata.Data = append(returndata.Data, v)
			}
			break
		default:
			logs.Info("notifyId不在处理中", string(reqBytes))
			break
		}
	}

	returnbody, _ := base.MyJson.Marshal(returndata)

	dataStr, err := encrypt(l.key, string(returnbody))
	// logs.Error("gfg_hash GetSingleWalletQuota =================   encrypt  err", dataStr, string(returnbody), err)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "加密错误"
		// logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}
	respResponse.Status = true
	respResponse.Data = dataStr
	// logs.Error("gfg_hash GetSingleWalletQuota =================  reply:= ", respResponse, err)
	ctx.RespJson(respResponse)
	return
}

func (l *GFGLotteryConfig) CheckWalletRequestExisted(ctx *abugo.AbuHttpContent) {
	reqstr := ctx.Gin().PostForm("data")
	logs.Info(l.brandName, "gfg_hash CheckWalletRequestExisted =================  req:= ", reqstr)

	respResponse := respData{}

	reqstr, err := decrypt(l.key, reqstr)
	if err != nil {
		// Логируем ошибку без раскрытия чувствительной информации
		logs.Error("gfg_hash CheckWalletRequestExisted: decryption error")

		respResponse.Status = false
		respResponse.Message = "处理请求时发生错误" // "Произошла ошибка при обработке запроса"
		ctx.RespJson(respResponse)
		return
	}
	//查询订单是否存在
	type order struct {
		OrderId []string `json:"OrderId"` // 不需要用此字段
		Orders  []struct {
			OrderNumber string `json:"OrderNumber"` // 订单号
			Sign        string `json:"Sign"`        // 签名
		} `json:"Orders"`
	}
	req1 := order{}
	json.Unmarshal([]byte(reqstr), &req1)

	type WalletRequestExistedInfo struct {
		OrderId    string `json:"OrderId"`
		OrderExist bool   `json:"OrderExist"`
	}
	type WalletRequestExistedInfos struct {
		WalletRequestExistedInfos []*WalletRequestExistedInfo `json:"WalletRequestExistedInfos"`
	}
	Data := WalletRequestExistedInfos{}
	for _, v := range req1.Orders {
		where := abugo.AbuDbWhere{}
		where.Add("and", "ThirdId", "=", v.OrderNumber, nil)
		where.Add("and", "Brand", "=", l.brandName, nil)
		betTran, err := server.Db().Table("x_third_lottery_pre_order").Where(where).GetOne()
		if err == nil && betTran != nil {
			type TmpGfgHashBetData struct {
				Sign string `json:"Sign"`
			}
			rowData := abugo.GetStringFromInterface((*betTran)["RawData"])
			if rowData != "" {
				tmpBetData := TmpGfgHashBetData{}
				json.Unmarshal([]byte(rowData), &tmpBetData)
				if tmpBetData.Sign == v.Sign {
					Data.WalletRequestExistedInfos = append(Data.WalletRequestExistedInfos, &WalletRequestExistedInfo{
						OrderId:    v.Sign,
						OrderExist: true,
					})
				} else {
					Data.WalletRequestExistedInfos = append(Data.WalletRequestExistedInfos, &WalletRequestExistedInfo{
						OrderId:    v.Sign,
						OrderExist: false,
					})
				}
			} else {
				Data.WalletRequestExistedInfos = append(Data.WalletRequestExistedInfos, &WalletRequestExistedInfo{
					OrderId:    v.Sign,
					OrderExist: false,
				})
			}
		} else {
			Data.WalletRequestExistedInfos = append(Data.WalletRequestExistedInfos, &WalletRequestExistedInfo{
				OrderId:    v.Sign,
				OrderExist: false,
			})
		}
	}

	returnbody, _ := base.MyJson.Marshal(Data)

	dataStr, err := encrypt(l.key, string(returnbody))
	logs.Error("gfg_hash CheckWalletRequestExisted =================   encrypt  err", dataStr, string(returnbody), err)
	if err != nil {
		respResponse.Status = false
		respResponse.Message = "加密错误"
		logs.Error("gfg_hash CheckWalletRequestExisted =================  reply:= ", respResponse, err)
		ctx.RespJson(respResponse)
		return
	}

	respResponse.Status = true
	respResponse.Data = dataStr
	logs.Error("gfg_hash CheckWalletRequestExisted =================  reply:= ", respResponse, err)
	ctx.RespJson(respResponse)
	return

}

func ChangeHttpToHttps(url string) string {
	tempurl := strings.Split(url, ":")
	if len(tempurl) != 0 {
		if tempurl[0] == "http" {
			return strings.Replace(url, "http", "https", 1)
		}
		return url
	}
	return url
}

// 获取游戏期数信息
func (l *GFGLotteryConfig) GetGamePeriodData(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameIds string // 游戏ID，英文逗号隔开，可选参数
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, "参数错误")
		return
	}

	// 如果GameIds为空，设置默认值
	if reqdata.GameIds == "" {
		reqdata.GameIds = "1,711,713,985,618,30,44,715,717" // 默认查询香港六合彩,哈希3分快三,哈希3分赛车,哈希1分赛车,印度幸运10,福彩3D,排列三五,哈希1分时时彩,哈希5分时时彩
	}

	//token := server.GetToken(ctx)
	//if err, errcode = base.IsLoginByUserId(cacheKeyGFGHash, token.UserId); err != nil {
	//	ctx.RespErrString(true, &errcode, err.Error())
	//	return
	//}

	reqstr := fmt.Sprintf(`{"Merchantkey": "%s","GameIds": "%s"}`, l.key, reqdata.GameIds)
	param, sign := l.lottery_sign(reqstr)

	urls := fmt.Sprintf("%s%s?param=%s&key=%s", l.url, "/Account/GetGamePeriodData", param, sign)

	httpclients := httpc.DoRequest{
		UrlPath: urls,
	}
	body, err := httpclients.DoGet()
	//logs.Debug("gfg_hash GetGamePeriodData", urls, string(body), err)
	if err != nil {
		ctx.RespErrString(true, &errcode, "获取游戏期数失败,请稍后再试")
		return
	}

	type GamePeriodData struct {
		GameID         int    `json:"GameID"`
		GameName       string `json:"GameName"`
		GroupName      string `json:"GroupName"`
		GroupID        int    `json:"GroupID"`
		NumberofPeriod int64  `json:"NumberofPeriod"`
		LottoStartTime string `json:"LottoStartTime"`
		NextPeriod     int64  `json:"NextPeriod"`
		NextStartTime  string `json:"NextStartTime"`
		ServerTime     string `json:"ServerTime"`
	}

	type res struct {
		Success bool              `json:"Success"`
		Message string            `json:"Message"`
		Data    []*GamePeriodData `json:"Data"`
	}

	resp1 := res{}
	err = json.Unmarshal(body, &resp1)
	if err != nil {
		logs.Error("gfg_hash GetGamePeriodData Unmarshal", err)
		ctx.RespErrString(true, &errcode, "获取游戏期数失败,请稍后再试")
		return
	}

	if !resp1.Success {
		ctx.RespErrString(true, &errcode, "获取游戏期数失败,请稍后再试")
		return
	}

	// 从缓存的游戏列表中查询游戏名称
	for i := 0; i < len(resp1.Data); i++ {
		gameId := fmt.Sprintf("%d", resp1.Data[i].GameID)
		gameName := l.getGameNameById(gameId)
		if gameName != "" {
			resp1.Data[i].GameName = gameName
		}
	}

	ctx.RespOK(resp1.Data)
}
