package common

import (
	"github.com/beego/beego/logs"
	"xserver/server"
)

// CheckUserFrozen 检查用户余额是否被冻结
func CheckUserFrozen(userId int) (bool, string) {
	var userState int
	db := server.Db().GormDao()

	err := db.Table("x_user").
		Select("State").
		Where("UserId = ?", userId).
		Scan(&userState).Error

	if err != nil {
		logs.Error("查询用户状态失败  userId=", userId, " err=", err.Error())
		return true, "系统错误，请稍后再试"
	}

	// State=3 代表余额冻结
	if userState == 3 {
		logs.Info("用户余额被冻结 userId=", userId, " userState=", userState)
		return true, "当前账号暂时无法进入该游戏，请联系客服"
	}

	return false, ""
}
