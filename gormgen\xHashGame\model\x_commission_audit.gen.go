// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameXCommissionAudit = "x_commission_audit"

// XCommissionAudit mapped from table <x_commission_audit>
type XCommissionAudit struct {
	ID            int32     `gorm:"column:Id;primaryKey;autoIncrement:true;comment:id" json:"Id"` // id
	UserID        int32     `gorm:"column:UserId;not null;comment:代理id" json:"UserId"`            // 代理id
	SellerID      int32     `gorm:"column:SellerId;comment:运营商" json:"SellerId"`                  // 运营商
	ChannelID     int32     `gorm:"column:ChannelId;default:1" json:"ChannelId"`
	State         int32     `gorm:"column:State;comment:状态 1待审核 2 审核拒绝 3 审核通过 4已发放" json:"State"`                    // 状态 1待审核 2 审核拒绝 3 审核通过 4已发放
	Symbol        string    `gorm:"column:Symbol;comment:币种" json:"Symbol"`                                          // 币种
	Amount        float64   `gorm:"column:Amount;comment:申请金额" json:"Amount"`                                        // 申请金额
	CreateTime    time.Time `gorm:"column:CreateTime;comment:申请时间" json:"CreateTime"`                                // 申请时间
	Memo          string    `gorm:"column:Memo;comment:备注" json:"Memo"`                                              // 备注
	AuditAccount  string    `gorm:"column:AuditAccount;comment:审核人" json:"AuditAccount"`                             // 审核人
	AuditTime     time.Time `gorm:"column:AuditTime;comment:审核时间" json:"AuditTime"`                                  // 审核时间
	SendAccount   string    `gorm:"column:SendAccount;comment:发放人" json:"SendAccount"`                               // 发放人
	SendTime      time.Time `gorm:"column:SendTime;comment:发放时间" json:"SendTime"`                                    // 发放时间
	Address       string    `gorm:"column:Address;comment:代理钱包地址" json:"Address"`                                    // 代理钱包地址
	StartDate     time.Time `gorm:"column:StartDate;comment:佣金产生开始时间" json:"StartDate"`                              // 佣金产生开始时间
	EndDate       time.Time `gorm:"column:EndDate;comment:佣金产生结束时间" json:"EndDate"`                                  // 佣金产生结束时间
	TrxPrice      float64   `gorm:"column:TrxPrice;comment:trx价格" json:"TrxPrice"`                                   // trx价格
	FinalAmount   float64   `gorm:"column:FinalAmount;comment:最终金额" json:"FinalAmount"`                              // 最终金额
	AgentMode     int32     `gorm:"column:AgentMode;not null;default:1;comment:1-无限代 2-三级代" json:"AgentMode"`        // 1-无限代 2-三级代
	GetType       int32     `gorm:"column:GetType;default:1;comment:发放方式 1人工发放 2自动发放" json:"GetType"`                // 发放方式 1人工发放 2自动发放
	GetAmountType int32     `gorm:"column:GetAmountType;default:1;comment:发放钱包 1真金钱包 2bonus钱包" json:"GetAmountType"` // 发放钱包 1真金钱包 2bonus钱包
	RollingTimes  float64   `gorm:"column:RollingTimes;default:0.000000;comment:打码倍数" json:"RollingTimes"`           // 打码倍数
}

// TableName XCommissionAudit's table name
func (*XCommissionAudit) TableName() string {
	return TableNameXCommissionAudit
}
