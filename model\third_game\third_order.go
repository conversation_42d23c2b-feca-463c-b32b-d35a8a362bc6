package third_game

// x_third_dianzhi x_third_dianzhi_pre_order
// x_third_qipai x_third_qipai_pre_order
// x_third_quwei x_third_quwei_pre_order
// x_third_lottery x_third_lottery_pre_order
// x_third_live x_third_live_pre_order
// x_third_sport x_third_sport_pre_order
type ThirdOrder struct {
	Id           int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `json:"BetChannelId" gorm:"column:BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	ThirdRefId   *string `json:"ThirdRefId" gorm:"column:ThirdRefId"` //三方备用号
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"` //投注金额
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"` //派奖金额
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`   //有效流水
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`

	// 新增混合投注字段
	BetType int `json:"BetType" gorm:"column:BetType"` // 下注类型：为兼容原有下注 0=纯真金 1=彩金下注 2=混合下注
	//RealBetAmount  float64 `json:"RealBetAmount" gorm:"column:RealBetAmount"`   // 真金下注金额
	BonusBetAmount float64 `json:"BonusBetAmount" gorm:"column:BonusBetAmount"` // Bonus币下注金额
	//RealWinAmount  float64 `json:"RealWinAmount" gorm:"column:RealWinAmount"`   // 真金派彩金额
	BonusWinAmount float64 `json:"BonusWinAmount" gorm:"column:BonusWinAmount"` // Bonus币派彩金额

	//RealProfit  float64 `json:"RealProfit" gorm:"column:RealProfit"`   // 平台真金输赢
	//BonusProfit float64 `json:"BonusProfit" gorm:"column:BonusProfit"` // 平台Bonus币输赢
	//TotalProfit float64 `json:"TotalProfit" gorm:"column:TotalProfit"` // 平台合计输赢

	// 新增显示用计算字段（不存储到数据库，仅用于前端显示）
	RealBetAmount  float64 `json:"RealBetAmount" gorm:"-"`  // 真金下注金额（计算字段）
	RealWinAmount  float64 `json:"RealWinAmount" gorm:"-"`  // 真金派彩金额（计算字段）
	BetTypeDisplay string  `json:"BetTypeDisplay" gorm:"-"` // 投注类型显示文本

}

// CalculateDisplayFields 计算显示字段
func (t *ThirdOrder) CalculateDisplayFields() {
	// 计算真金投注金额
	t.RealBetAmount = t.BetAmount - t.BonusBetAmount

	// 计算真金派彩金额
	t.RealWinAmount = t.WinAmount - t.BonusWinAmount

	// 设置投注类型显示文本
	switch t.BetType {
	case 1:
		t.BetTypeDisplay = "真金投注"
	case 2:
		t.BetTypeDisplay = "混合投注"
	case 3:
		t.BetTypeDisplay = "Bonus币投注"
	default:
		t.BetTypeDisplay = "未知类型"
	}
}

// GetBetTypeText 获取投注类型文本
func (t *ThirdOrder) GetBetTypeText() string {
	switch t.BetType {
	case 0:
		return "真金投注"
	case 1:
		return "混合投注"
	case 2:
		return "Bonus币投注"
	default:
		return "未知类型"
	}
}

// IsRealMoneyBet 是否为真金投注
func (t *ThirdOrder) IsRealMoneyBet() bool {
	return t.BetType == 0
}

// IsBonusBet 是否为Bonus币投注
func (t *ThirdOrder) IsBonusBet() bool {
	return t.BetType == 1
}

// IsMixedBet 是否为混合投注
func (t *ThirdOrder) IsMixedBet() bool {
	return t.BetType == 2
}

// x_third_sport x_third_sport_pre_order 增加重新结算字段
type ThirdSportOrder struct {
	Id             int64   `json:"Id" gorm:"column:Id;primaryKey;autoIncrement:true"`
	SellerId       int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId      int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId   int     `json:"BetChannelId" gorm:"column:BetChannelId"` // 下注渠道Id
	UserId         int     `json:"UserId" gorm:"column:UserId"`
	Brand          string  `json:"Brand" gorm:"column:Brand"`
	ThirdId        string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId         string  `json:"GameId" gorm:"column:GameId"`
	GameName       string  `json:"GameName" gorm:"column:GameName"`
	BetAmount      float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount      float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet       float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime      string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency       string  `json:"Currency" gorm:"column:Currency"`
	RawData        string  `json:"RawData" gorm:"column:RawData"`
	State          int     `json:"State" gorm:"column:State"`
	Fee            float64 `json:"Fee" gorm:"column:Fee"`
	DataState      int     `json:"DataState" gorm:"column:DataState"`
	CreateTime     string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup        string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId           string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId     int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent   int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx         string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst        string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType     int     `json:"BetCtxType" gorm:"column:BetCtxType"`
	ResettleState  int     `json:"ResettleState" gorm:"column:ResettleState"`
	ResettleTime   *string `json:"ResettleTime" gorm:"column:ResettleTime"`
	ResettleNumber int     `json:"ResettleNumber" gorm:"column:ResettleNumber"`
	ThirdRefId     string  `json:"ThirdRefId" gorm:"column:ThirdRefId"`
	BetTime        *string `json:"BetTime" gorm:"column:BetTime"`
	SettleTime     *string `json:"SettleTime" gorm:"column:SettleTime"`
	CancelTime     *string `json:"CancelTime" gorm:"column:CancelTime"`
	BetLocalTime   *string `json:"BetLocalTime" gorm:"column:BetLocalTime"`

	// 新增混合投注字段
	BetType        int     `json:"BetType" gorm:"column:BetType"`               // 下注类型：1=纯真金 2=混合下注 3=彩金
	BonusBetAmount float64 `json:"BonusBetAmount" gorm:"column:BonusBetAmount"` // Bonus币下注金额
	BonusWinAmount float64 `json:"BonusWinAmount" gorm:"column:BonusWinAmount"` // Bonus币派彩金额

}
