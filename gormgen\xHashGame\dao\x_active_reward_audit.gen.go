// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXActiveRewardAudit(db *gorm.DB, opts ...gen.DOOption) xActiveRewardAudit {
	_xActiveRewardAudit := xActiveRewardAudit{}

	_xActiveRewardAudit.xActiveRewardAuditDo.UseDB(db, opts...)
	_xActiveRewardAudit.xActiveRewardAuditDo.UseModel(&model.XActiveRewardAudit{})

	tableName := _xActiveRewardAudit.xActiveRewardAuditDo.TableName()
	_xActiveRewardAudit.ALL = field.NewAsterisk(tableName)
	_xActiveRewardAudit.ID = field.NewInt32(tableName, "Id")
	_xActiveRewardAudit.SellerID = field.NewInt32(tableName, "SellerId")
	_xActiveRewardAudit.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xActiveRewardAudit.UserID = field.NewInt32(tableName, "UserId")
	_xActiveRewardAudit.ActiveDefineID = field.NewInt32(tableName, "ActiveDefineId")
	_xActiveRewardAudit.ActiveID = field.NewInt32(tableName, "ActiveId")
	_xActiveRewardAudit.ActiveLevel = field.NewInt32(tableName, "ActiveLevel")
	_xActiveRewardAudit.ActiveMemo = field.NewString(tableName, "ActiveMemo")
	_xActiveRewardAudit.RecordDate = field.NewTime(tableName, "RecordDate")
	_xActiveRewardAudit.Amount = field.NewFloat64(tableName, "Amount")
	_xActiveRewardAudit.MinLiuShui = field.NewFloat64(tableName, "MinLiuShui")
	_xActiveRewardAudit.AuditState = field.NewInt32(tableName, "AuditState")
	_xActiveRewardAudit.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xActiveRewardAudit.AuditTime = field.NewTime(tableName, "AuditTime")
	_xActiveRewardAudit.AuditMemo = field.NewString(tableName, "AuditMemo")
	_xActiveRewardAudit.CreateTime = field.NewTime(tableName, "CreateTime")
	_xActiveRewardAudit.OrderID = field.NewInt32(tableName, "OrderId")
	_xActiveRewardAudit.AmountTrx = field.NewFloat64(tableName, "AmountTrx")
	_xActiveRewardAudit.GasFee = field.NewFloat64(tableName, "GasFee")
	_xActiveRewardAudit.TotalRecharge = field.NewFloat64(tableName, "TotalRecharge")
	_xActiveRewardAudit.LiuShui = field.NewFloat64(tableName, "LiuShui")
	_xActiveRewardAudit.Address = field.NewString(tableName, "Address")
	_xActiveRewardAudit.TxID = field.NewString(tableName, "TxId")
	_xActiveRewardAudit.NetWinLoss = field.NewFloat64(tableName, "NetWinLoss")
	_xActiveRewardAudit.Config = field.NewString(tableName, "Config")
	_xActiveRewardAudit.BaseConfig = field.NewString(tableName, "BaseConfig")
	_xActiveRewardAudit.FirstRecharge = field.NewFloat64(tableName, "FirstRecharge")
	_xActiveRewardAudit.FirstSignTime = field.NewTime(tableName, "FirstSignTime")
	_xActiveRewardAudit.LastSignTime = field.NewTime(tableName, "LastSignTime")
	_xActiveRewardAudit.InviteRewardType = field.NewInt32(tableName, "InviteRewardType")
	_xActiveRewardAudit.ChildUserID = field.NewInt32(tableName, "ChildUserId")
	_xActiveRewardAudit.InviteRewardUsers = field.NewInt32(tableName, "InviteRewardUsers")
	_xActiveRewardAudit.GameType = field.NewString(tableName, "GameType")
	_xActiveRewardAudit.UserIP = field.NewString(tableName, "UserIP")
	_xActiveRewardAudit.DeviceID = field.NewString(tableName, "DeviceID")
	_xActiveRewardAudit.RedeemCode = field.NewString(tableName, "RedeemCode")

	_xActiveRewardAudit.fillFieldMap()

	return _xActiveRewardAudit
}

// xActiveRewardAudit 活动发放审核表
type xActiveRewardAudit struct {
	xActiveRewardAuditDo xActiveRewardAuditDo

	ALL               field.Asterisk
	ID                field.Int32   // id
	SellerID          field.Int32   // 运营商
	ChannelID         field.Int32   // 渠道
	UserID            field.Int32   // 玩家ID
	ActiveDefineID    field.Int32   // x_active_define.Id
	ActiveID          field.Int32   // 活动ID 1能量补给站 2充值任务 3哈希闯关 4棋牌闯关 5电子闯关 6救援金 7邀请好友 8VIP返水 9降龙伏虎
	ActiveLevel       field.Int32   // 活动等级  当ActiveId=8的时候 ActiveLevel表示游戏的种类1哈希 2原创 3电子 4棋牌 5真人 6运动
	ActiveMemo        field.String  // 活动额外说明
	RecordDate        field.Time    // 活动时间
	Amount            field.Float64 // 活动送金
	MinLiuShui        field.Float64 // 提现最低流水(彩金打码量)百分比 次日送领取最低打码量百分比
	AuditState        field.Int32   // 审核状态 1待审核,2审核拒绝,3审核通过,4自动通过
	AuditAccount      field.String  // 审核账号
	AuditTime         field.Time    // 审核时间
	AuditMemo         field.String  // 审核备注
	CreateTime        field.Time    // 创建时间
	OrderID           field.Int32   // 活动1注单Id
	AmountTrx         field.Float64 // 活动1 送金的trx
	GasFee            field.Float64 // 活动1 派奖上链gas费
	TotalRecharge     field.Float64 // 活动2 6 当日充值金额
	LiuShui           field.Float64 // 流水有效投注
	Address           field.String  // 活动1 玩家地址
	TxID              field.String  // 活动1 交易哈希
	NetWinLoss        field.Float64 // 活动6 当日净亏损
	Config            field.String  // 配置项
	BaseConfig        field.String  // 基础配置
	FirstRecharge     field.Float64 // 首充金额
	FirstSignTime     field.Time    // 首次签到时间
	LastSignTime      field.Time    // 最后签到时间
	InviteRewardType  field.Int32   // 邀请好友奖励分类 0其他 1单人奖励 2额外奖励
	ChildUserID       field.Int32   // 下级UserId
	InviteRewardUsers field.Int32   // 邀请好友额外奖励用户数量
	GameType          field.String  // 游戏分类
	UserIP            field.String  // 用户IP
	DeviceID          field.String  // 设备 ID
	RedeemCode        field.String  // 本次使用的兑换码

	fieldMap map[string]field.Expr
}

func (x xActiveRewardAudit) Table(newTableName string) *xActiveRewardAudit {
	x.xActiveRewardAuditDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xActiveRewardAudit) As(alias string) *xActiveRewardAudit {
	x.xActiveRewardAuditDo.DO = *(x.xActiveRewardAuditDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xActiveRewardAudit) updateTableName(table string) *xActiveRewardAudit {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.UserID = field.NewInt32(table, "UserId")
	x.ActiveDefineID = field.NewInt32(table, "ActiveDefineId")
	x.ActiveID = field.NewInt32(table, "ActiveId")
	x.ActiveLevel = field.NewInt32(table, "ActiveLevel")
	x.ActiveMemo = field.NewString(table, "ActiveMemo")
	x.RecordDate = field.NewTime(table, "RecordDate")
	x.Amount = field.NewFloat64(table, "Amount")
	x.MinLiuShui = field.NewFloat64(table, "MinLiuShui")
	x.AuditState = field.NewInt32(table, "AuditState")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AuditTime = field.NewTime(table, "AuditTime")
	x.AuditMemo = field.NewString(table, "AuditMemo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.OrderID = field.NewInt32(table, "OrderId")
	x.AmountTrx = field.NewFloat64(table, "AmountTrx")
	x.GasFee = field.NewFloat64(table, "GasFee")
	x.TotalRecharge = field.NewFloat64(table, "TotalRecharge")
	x.LiuShui = field.NewFloat64(table, "LiuShui")
	x.Address = field.NewString(table, "Address")
	x.TxID = field.NewString(table, "TxId")
	x.NetWinLoss = field.NewFloat64(table, "NetWinLoss")
	x.Config = field.NewString(table, "Config")
	x.BaseConfig = field.NewString(table, "BaseConfig")
	x.FirstRecharge = field.NewFloat64(table, "FirstRecharge")
	x.FirstSignTime = field.NewTime(table, "FirstSignTime")
	x.LastSignTime = field.NewTime(table, "LastSignTime")
	x.InviteRewardType = field.NewInt32(table, "InviteRewardType")
	x.ChildUserID = field.NewInt32(table, "ChildUserId")
	x.InviteRewardUsers = field.NewInt32(table, "InviteRewardUsers")
	x.GameType = field.NewString(table, "GameType")
	x.UserIP = field.NewString(table, "UserIP")
	x.DeviceID = field.NewString(table, "DeviceID")
	x.RedeemCode = field.NewString(table, "RedeemCode")

	x.fillFieldMap()

	return x
}

func (x *xActiveRewardAudit) WithContext(ctx context.Context) *xActiveRewardAuditDo {
	return x.xActiveRewardAuditDo.WithContext(ctx)
}

func (x xActiveRewardAudit) TableName() string { return x.xActiveRewardAuditDo.TableName() }

func (x xActiveRewardAudit) Alias() string { return x.xActiveRewardAuditDo.Alias() }

func (x xActiveRewardAudit) Columns(cols ...field.Expr) gen.Columns {
	return x.xActiveRewardAuditDo.Columns(cols...)
}

func (x *xActiveRewardAudit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xActiveRewardAudit) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 36)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["ActiveDefineId"] = x.ActiveDefineID
	x.fieldMap["ActiveId"] = x.ActiveID
	x.fieldMap["ActiveLevel"] = x.ActiveLevel
	x.fieldMap["ActiveMemo"] = x.ActiveMemo
	x.fieldMap["RecordDate"] = x.RecordDate
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["MinLiuShui"] = x.MinLiuShui
	x.fieldMap["AuditState"] = x.AuditState
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AuditTime"] = x.AuditTime
	x.fieldMap["AuditMemo"] = x.AuditMemo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["OrderId"] = x.OrderID
	x.fieldMap["AmountTrx"] = x.AmountTrx
	x.fieldMap["GasFee"] = x.GasFee
	x.fieldMap["TotalRecharge"] = x.TotalRecharge
	x.fieldMap["LiuShui"] = x.LiuShui
	x.fieldMap["Address"] = x.Address
	x.fieldMap["TxId"] = x.TxID
	x.fieldMap["NetWinLoss"] = x.NetWinLoss
	x.fieldMap["Config"] = x.Config
	x.fieldMap["BaseConfig"] = x.BaseConfig
	x.fieldMap["FirstRecharge"] = x.FirstRecharge
	x.fieldMap["FirstSignTime"] = x.FirstSignTime
	x.fieldMap["LastSignTime"] = x.LastSignTime
	x.fieldMap["InviteRewardType"] = x.InviteRewardType
	x.fieldMap["ChildUserId"] = x.ChildUserID
	x.fieldMap["InviteRewardUsers"] = x.InviteRewardUsers
	x.fieldMap["GameType"] = x.GameType
	x.fieldMap["UserIP"] = x.UserIP
	x.fieldMap["DeviceID"] = x.DeviceID
	x.fieldMap["RedeemCode"] = x.RedeemCode
}

func (x xActiveRewardAudit) clone(db *gorm.DB) xActiveRewardAudit {
	x.xActiveRewardAuditDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xActiveRewardAudit) replaceDB(db *gorm.DB) xActiveRewardAudit {
	x.xActiveRewardAuditDo.ReplaceDB(db)
	return x
}

type xActiveRewardAuditDo struct{ gen.DO }

func (x xActiveRewardAuditDo) Debug() *xActiveRewardAuditDo {
	return x.withDO(x.DO.Debug())
}

func (x xActiveRewardAuditDo) WithContext(ctx context.Context) *xActiveRewardAuditDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xActiveRewardAuditDo) ReadDB() *xActiveRewardAuditDo {
	return x.Clauses(dbresolver.Read)
}

func (x xActiveRewardAuditDo) WriteDB() *xActiveRewardAuditDo {
	return x.Clauses(dbresolver.Write)
}

func (x xActiveRewardAuditDo) Session(config *gorm.Session) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Session(config))
}

func (x xActiveRewardAuditDo) Clauses(conds ...clause.Expression) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xActiveRewardAuditDo) Returning(value interface{}, columns ...string) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xActiveRewardAuditDo) Not(conds ...gen.Condition) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xActiveRewardAuditDo) Or(conds ...gen.Condition) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xActiveRewardAuditDo) Select(conds ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xActiveRewardAuditDo) Where(conds ...gen.Condition) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xActiveRewardAuditDo) Order(conds ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xActiveRewardAuditDo) Distinct(cols ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xActiveRewardAuditDo) Omit(cols ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xActiveRewardAuditDo) Join(table schema.Tabler, on ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xActiveRewardAuditDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xActiveRewardAuditDo) RightJoin(table schema.Tabler, on ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xActiveRewardAuditDo) Group(cols ...field.Expr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xActiveRewardAuditDo) Having(conds ...gen.Condition) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xActiveRewardAuditDo) Limit(limit int) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xActiveRewardAuditDo) Offset(offset int) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xActiveRewardAuditDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xActiveRewardAuditDo) Unscoped() *xActiveRewardAuditDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xActiveRewardAuditDo) Create(values ...*model.XActiveRewardAudit) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xActiveRewardAuditDo) CreateInBatches(values []*model.XActiveRewardAudit, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xActiveRewardAuditDo) Save(values ...*model.XActiveRewardAudit) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xActiveRewardAuditDo) First() (*model.XActiveRewardAudit, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRewardAudit), nil
	}
}

func (x xActiveRewardAuditDo) Take() (*model.XActiveRewardAudit, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRewardAudit), nil
	}
}

func (x xActiveRewardAuditDo) Last() (*model.XActiveRewardAudit, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRewardAudit), nil
	}
}

func (x xActiveRewardAuditDo) Find() ([]*model.XActiveRewardAudit, error) {
	result, err := x.DO.Find()
	return result.([]*model.XActiveRewardAudit), err
}

func (x xActiveRewardAuditDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XActiveRewardAudit, err error) {
	buf := make([]*model.XActiveRewardAudit, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xActiveRewardAuditDo) FindInBatches(result *[]*model.XActiveRewardAudit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xActiveRewardAuditDo) Attrs(attrs ...field.AssignExpr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xActiveRewardAuditDo) Assign(attrs ...field.AssignExpr) *xActiveRewardAuditDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xActiveRewardAuditDo) Joins(fields ...field.RelationField) *xActiveRewardAuditDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xActiveRewardAuditDo) Preload(fields ...field.RelationField) *xActiveRewardAuditDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xActiveRewardAuditDo) FirstOrInit() (*model.XActiveRewardAudit, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRewardAudit), nil
	}
}

func (x xActiveRewardAuditDo) FirstOrCreate() (*model.XActiveRewardAudit, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XActiveRewardAudit), nil
	}
}

func (x xActiveRewardAuditDo) FindByPage(offset int, limit int) (result []*model.XActiveRewardAudit, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xActiveRewardAuditDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xActiveRewardAuditDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xActiveRewardAuditDo) Delete(models ...*model.XActiveRewardAudit) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xActiveRewardAuditDo) withDO(do gen.Dao) *xActiveRewardAuditDo {
	x.DO = *do.(*gen.DO)
	return x
}
