// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameXVipDefine = "x_vip_define"

// XVipDefine mapped from table <x_vip_define>
type XVipDefine struct {
	ID                     int32   `gorm:"column:Id;not null" json:"Id"`
	SellerID               int32   `gorm:"column:SellerId;primaryKey" json:"SellerId"`
	ChannelID              int32   `gorm:"column:ChannelId;primaryKey" json:"ChannelId"`
	VipLevel               int32   `gorm:"column:VipLevel;primaryKey;comment:vip等级" json:"VipLevel"`                                    // vip等级
	Recharge               int32   `gorm:"column:Recharge;not null;comment:累计存款" json:"Recharge"`                                       // 累计存款
	LiuSui                 int32   `gorm:"column:<PERSON><PERSON><PERSON>;not null;comment:累计流水" json:"<PERSON>Sui"`                                           // 累计流水
	KeepLiuSui             int32   `gorm:"column:KeepLiuSui;not null;comment:保级流水" json:"KeepLiuSui"`                                   // 保级流水
	State                  int32   `gorm:"column:State;comment:状态 1启用,2禁用" json:"State"`                                                // 状态 1启用,2禁用
	UpgradeReward          float64 `gorm:"column:UpgradeReward;comment:升级礼金" json:"UpgradeReward"`                                      // 升级礼金
	MonthlyReward          float64 `gorm:"column:MonthlyReward;comment:每月礼金" json:"MonthlyReward"`                                      // 每月礼金
	MonthlyLiuSui          float64 `gorm:"column:MonthlyLiuSui;default:0.000000;comment:每月礼金流水" json:"MonthlyLiuSui"`                   // 每月礼金流水
	RewardRateHaXi         float64 `gorm:"column:RewardRateHaXi;comment:哈希返点" json:"RewardRateHaXi"`                                    // 哈希返点
	RewardRateHaXiRoulette float64 `gorm:"column:RewardRateHaXiRoulette;default:0.000000;comment:哈希轮盘返点" json:"RewardRateHaXiRoulette"` // 哈希轮盘返点
	RewardRateLottery      float64 `gorm:"column:RewardRateLottery;comment:彩票返点" json:"RewardRateLottery"`                              // 彩票返点
	RewardRateLowLottery   float64 `gorm:"column:RewardRateLowLottery;default:0.000000;comment:低频彩返点" json:"RewardRateLowLottery"`      // 低频彩返点
	RewardRateQiPai        float64 `gorm:"column:RewardRateQiPai;comment:棋牌返点" json:"RewardRateQiPai"`                                  // 棋牌返点
	RewardRateDianZhi      float64 `gorm:"column:RewardRateDianZhi;comment:电子返点" json:"RewardRateDianZhi"`                              // 电子返点
	RewardRateXiaoYouXi    float64 `gorm:"column:RewardRateXiaoYouXi;comment:小游戏返点" json:"RewardRateXiaoYouXi"`                         // 小游戏返点
	RewardRateCryptoMarket float64 `gorm:"column:RewardRateCryptoMarket;default:0.000000;comment:加密市场返点" json:"RewardRateCryptoMarket"` // 加密市场返点
	RewardRateLive         float64 `gorm:"column:RewardRateLive;comment:真人返点" json:"RewardRateLive"`                                    // 真人返点
	RewardRateSport        float64 `gorm:"column:RewardRateSport;comment:体育返点" json:"RewardRateSport"`                                  // 体育返点
	RewardRateTexas        float64 `gorm:"column:RewardRateTexas;default:0.000000;comment:德州返点" json:"RewardRateTexas"`                 // 德州返点
	WeeklyReward           float64 `gorm:"column:WeeklyReward;comment:每周礼金" json:"WeeklyReward"`                                        // 每周礼金
	WeeklyLiuSui           float64 `gorm:"column:WeeklyLiuSui;default:0.000000;comment:每周礼金流水" json:"WeeklyLiuSui"`                     // 每周礼金流水
	WeeklyRechargeTimes    int32   `gorm:"column:WeeklyRechargeTimes;comment:每周充值次数" json:"WeeklyRechargeTimes"`                        // 每周充值次数
	WeeklyRechargeAmount   float64 `gorm:"column:WeeklyRechargeAmount;default:0.000000;comment:每周充值金额" json:"WeeklyRechargeAmount"`     // 每周充值金额
	MonthlyRechargeTimes   int32   `gorm:"column:MonthlyRechargeTimes;comment:每月充值次数" json:"MonthlyRechargeTimes"`                      // 每月充值次数
	MonthlyRechargeAmount  float64 `gorm:"column:MonthlyRechargeAmount;default:0.000000;comment:每月充值金额" json:"MonthlyRechargeAmount"`   // 每月充值金额
}

// TableName XVipDefine's table name
func (*XVipDefine) TableName() string {
	return TableNameXVipDefine
}
