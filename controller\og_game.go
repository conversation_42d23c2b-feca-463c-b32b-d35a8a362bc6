package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third"
	"xserver/server"

	"github.com/beego/beego/logs"

	"github.com/gin-gonic/gin"
)

func (c *ThirdController) Og_Union_Login(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	errcode := 0
	if err != nil {
		ctx.RespErrString(true, &errcode, "进入失败")
		return
	}
	// 保存请求体
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Lang   string
		GameId int64
	}
	reqdata := RequestData{}
	json.Unmarshal(bodyBytes, &reqdata)

	// 游戏分发
	switch reqdata.GameId {
	case 1:
		if c.coin2Service != nil {
			c.coin2Service.Login(ctx)
			return
		}
		break
	case 4:
		if c.jdService != nil {
			c.jdService.Login(ctx)
			return
		}

		break
	case 5:
		if c.bearcowService != nil {
			c.bearcowService.JD_Login(ctx)
			return
		}
		break
	case 2:
		if c.stargrabberService != nil {
			c.stargrabberService.JD_Login(ctx)
			return
		}

		break
	case 3:
		if c.fortuneService != nil {
			c.fortuneService.JD_Login(ctx)
			return
		}

		break
	case 28:
		if c.hashLottoryService != nil {
			c.hashLottoryService.JD_Login(ctx)
			return
		}
		break
	case 29:
		if c.tradingService != nil {
			c.tradingService.Login(ctx)
			return
		}
		break
	case 35:
		if c.updownService != nil {
			c.updownService.Login(ctx)
			return
		}
		break
	}

	ctx.RespErrString(true, &errcode, "进入失败")
	return
}

func (c *ThirdController) Og_Union_LockBalance(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	// 保存请求体
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	logs.Info("JD_Bet-ACCESS:", ctx.Gin().Request.RequestURI, string(bodyBytes))
	type RequestData struct {
		Lang      string
		GameId    int64
		Token     string `json:"token"`
		AccountId string `json:"accountId"`
	}
	reqdata := RequestData{}
	json.Unmarshal(bodyBytes, &reqdata)
	s := strings.Split(reqdata.Token, "_")
	if len(s) > 0 && s[0] == "gfg" {
		if c.GFGSingle.IsGFGAccount(reqdata.AccountId) {
			//进入了 gfg棋牌单一钱包
			logs.Info("进入了 GFG大运单一钱包下注接口:", reqdata)
			c.GFGSingle.Bet(ctx)
			return
		}
	}
	switch reqdata.GameId {
	case 1:
		if c.coin2Service != nil {
			c.coin2Service.Bet(ctx)
			return
		}
		break
	case 4:
		if c.jdService != nil {
			c.jdService.Bet(ctx)
			return
		}

		break
	case 5:
		if c.bearcowService != nil {
			c.bearcowService.JD_Bet(ctx)
			return
		}
		break
	case 2:
		if c.stargrabberService != nil {
			c.stargrabberService.JD_Bet(ctx)
			return
		}

		break
	case 3:
		if c.fortuneService != nil {
			c.fortuneService.JD_Bet(ctx)
			return
		}

		break
	case 28:
		if c.hashLottoryService != nil {
			c.hashLottoryService.JD_Bet(ctx)
			return
		}
		break
	case 29:
		if c.tradingService != nil {
			c.tradingService.Bet(ctx)
			return
		}
		break
	case 35:
		if c.updownService != nil {
			c.updownService.Bet(ctx)
			return
		}
		break
	}

	ctx.Gin().JSON(200, gin.H{
		"Code": -1,
	})
	return
}

func (c *ThirdController) Og_Union_UnLockBalance(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	// 保存请求体
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Lang      string
		GameId    int64
		Token     string `json:"token"`
		AccountId string `json:"accountId"`
	}
	reqdata := RequestData{}
	json.Unmarshal(bodyBytes, &reqdata)

	s := strings.Split(reqdata.Token, "_")
	if len(s) > 0 && s[0] == "gfg" {
		if c.GFGSingle.IsGFGAccount(reqdata.AccountId) {
			//进入了 gfg棋牌单一钱包
			logs.Info("进入了 GFG大运单一钱包结算接口:", reqdata)
			c.GFGSingle.Result(ctx)
			return
		}
	}

	logs.Info("JD_End-ACCESS:", ctx.Gin().Request.RequestURI, string(bodyBytes))

	// 游戏分发
	switch reqdata.GameId {
	case 1:
		if c.coin2Service != nil {
			c.coin2Service.End(ctx)
			return
		}
		break
	case 4:
		if c.jdService != nil {
			c.jdService.End(ctx)
			return
		}

		break
	case 5:
		if c.bearcowService != nil {
			c.bearcowService.JD_End(ctx)
			return
		}
		break
	case 2:
		if c.stargrabberService != nil {
			c.stargrabberService.JD_End(ctx)
			return
		}

		break
	case 3:
		if c.fortuneService != nil {
			c.fortuneService.JD_End(ctx)
			return
		}

		break
	case 28:
		if c.hashLottoryService != nil {
			c.hashLottoryService.JD_End(ctx)
			return
		}
		break
	case 29:
		if c.tradingService != nil {
			c.tradingService.End(ctx)
			return
		}
		break
	case 35:
		if c.updownService != nil {
			c.updownService.End(ctx)
			return
		}
		break
	}

	ctx.Gin().JSON(200, gin.H{
		"Code": -1,
	})
	return
}

func (c *ThirdController) Og_GetBalance(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}
	// 保存请求体
	ctx.Gin().Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	type RequestData struct {
		Token     string `json:"token"`
		AccountId string `json:"accountId"`
		Timestamp int64  `json:"timestamp"`
	}
	errcode := 0
	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		ctx.RespErr(err, &errcode)
		return
	}
	s := strings.Split(reqdata.Token, "_")
	if len(s) > 0 && s[0] == "gfg" {
		if c.GFGSingle.IsGFGAccount(reqdata.AccountId) {
			//进入了 gfg棋牌单一钱包
			logs.Info("进入了 GFG大运单一钱包:", reqdata)
			c.GFGSingle.GetBalance(ctx)
			return
		}
	}
	if len(s) > 0 {
		if s[0] == "updown" {
			if c.updownService != nil {
				c.updownService.GetBalance(ctx)
				return
			}
		} else if s[0] == "trading" {
			if c.tradingService != nil {
				c.tradingService.GetBalance(ctx)
				return
			}
		}
	}

	logs.Info("OG_GAME JD_GetBalance-ACCESS:", reqdata.Token)
	if reqdata.Token == "" {
		logs.Warning("OG_GAME JD_GetBalance-Warning No Token")
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}

	UserId := third.Token2UserId(reqdata.Token)
	if UserId <= 0 {
		ctx.Gin().JSON(200, gin.H{
			"Code": -1,
		})
		return
	}

	balance := c.UserAmount(UserId)
	logs.Info("OG_GAME GET Galance:", UserId, fmt.Sprintf("%v", balance))
	ctx.Gin().JSON(200, gin.H{
		"Code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance},
		"msg":       "",
	})
}

func (c *ThirdController) UserAmount(UserId int) float64 {
	where := abugo.AbuDbWhere{}
	where.Add("and", "UserId", "=", UserId, nil)
	udata, _ := server.Db().Table("x_user").Select("Amount").Where(where).GetOne()
	balance := float64(0)
	if udata != nil {
		balance = abugo.GetFloat64FromInterface((*udata)["Amount"])
	}
	return balance
}
