package single

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/zhms/xgo/xgo"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

const OpsCacheTaExpireTime = 1800 // 三十分钟缓存
// 返回给OPS系统错误码
const (
	OPS_ERR_NoContent           = "NoContent"            // 沒有內容
	OPS_ERR_InvalidArgument     = "INVALID_ARGUMENT"     // 無效的參數
	OPS_ERR_InvalidAccount      = "INVALID_ACCOUNT"      // 無效的 player 或錢包幣別或皆不可⽤
	OPS_ERR_InvalidRound        = "INVAILD_ROUND"        // 無效的 round
	OPS_ERR_MissingTicket       = "MissingTicket"        //注單不存在
	OPS_ERR_BalanceInsufficient = "BALANCE_INSUFFICIENT" //餘額不⾜
	OPS_ERR_DuplicateOperation  = "DUPLICATE_OPERATION"  //重複操作
	OPS_ERR_Failure             = "Failure"              // 異常錯誤

	OPS_BRAND = "OPS_" //开元聚合三方厂商以OPS_开头
)

var opsAccessProductList = []string{
	"3Oaks",            // 第一批上
	"Retro Gaming",     // 第一批上
	"SlotMill Games",   // 第一批上
	"Booming Games",    // 第一批上
	"BetsyGames",       // 第一批上
	"Mancala Gaming",   // 第一批上
	"Habanero",         // 第一批上
	"Play'n Go",        // 第一批上
	"OneTouch Generic", // 测试服专用
	"OneTouch",         // 第二批上
	"Live88",           // 第二批上
	"rubyplay",
}

type OpsService struct {
	Url                   string `json:"url"`
	Key                   string `json:"key"`
	Currency              string `json:"currency"`
	Lobby                 string `json:"lobby"`
	Platform              string `json:"platform"`
	Token                 string `json:"token"`
	brandName             string
	RefreshUserAmountFunc func(int) error
	mu                    sync.Mutex
	thirdGamePush         *base.ThirdGamePush
}

type ThirdOrderOps struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

type AmountChangeLogOps struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

type GameListOps struct {
	Id        int    `json:"Id" gorm:"column:Id"`
	Brand     string `json:"Brand" gorm:"column:Brand"`
	GameId    string `json:"GameId" gorm:"column:GameId"`
	Name      string `json:"Name" gorm:"column:Name"`
	EName     string `json:"EName" gorm:"column:EName"`
	State     int    `json:"State" gorm:"column:State"`
	OpenState int    `json:"OpenState" gorm:"column:OpenState"`
	GameType  int    `json:"GameType" gorm:"column:GameType"`
}

func NewOpsLogic(params map[string]string, fc func(int) error) *OpsService {
	return &OpsService{
		Url:                   params["url"],
		Key:                   params["key"],
		Currency:              params["currency"],
		Lobby:                 params["lobby"],
		Platform:              params["platform"],
		Token:                 params["token"],
		brandName:             "ops",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

// OPS三方暂未实现该接口
func (l *OpsService) GetGameList(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Line string `json:"line"` // EU 或者 ASIA
		Add  string `json:"add"`  // 1 添加数据库 非1不添加数据库
	}
	theReq := RequestData{}
	err := ctx.RequestData(&theReq)
	if err != nil {
		ctx.RespErrString(true, nil, err.Error())
		return
	}
	operatorId := 0
	baseUrl := ""
	//if theReq.Line == "EU" {
	//	operatorId, _ = strconv.Atoi(l.operatorIdEu)
	//  baseUrl = l.urlEu
	//} else {
	operatorId, _ = strconv.Atoi(l.Url) // 全部走亚洲
	baseUrl = l.Url
	//}

	type params struct {
		OperatorId int `json:"operator_id"`
	}

	errcode := 0

	req := params{OperatorId: operatorId}
	jbytes, _ := json.Marshal(req)
	signstr, err := Sign(l.Url, jbytes)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	heardmap := map[string]string{
		"X-Ops-Signature": signstr,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    baseUrl + "/operator/generic/v2/game/list",
		Param:      jbytes,
		Header:     heardmap,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	type resp struct {
		UrlThumb              string   `json:"url_thumb"`
		UrlBackground         string   `json:"url_background"`
		Product               string   `json:"product"`
		Platforms             []string `json:"platforms"`
		Name                  string   `json:"name"`
		gameCode              string   `json:"game_code"`
		FreebetSupport        bool     `json:"freebet_support"`
		DemoGameSupport       bool     `json:"demo_game_support"`
		PhoenixJackpotSupport bool     `json:"phoenix_jackpot_support"`
		Enabled               bool     `json:"enabled"`
		Category              string   `json:"category"`
		BlockedCountries      []string `json:"blocked_countries"`
		ReleaseDate           string   `json:"release_date"`
		Volatility            int      `json:"volatility"`
		Rtp                   string   `json:"rtp"`
		Paylines              int      `json:"paylines"`
		HitRatio              string   `json:"hit_ratio"`
		Certifications        []string `json:"certifications"`
		Languages             []string `json:"languages"`
		Theme                 []string `json:"theme"`
		Technology            []string `json:"technology"`
		Features              []string `json:"features"`
	}
	rest := make([]*resp, 0)
	_ = json.Unmarshal(b, &rest)
	rest2 := make([]*resp, 0)
	for _, v := range rest {
		rest2 = append(rest2, v)
		if !in_array_string(opsAccessProductList, v.Product) {
			continue
		}
		Brand := OPS_BRAND + strings.ReplaceAll(v.Product, " ", "_")
		Brand = strings.ReplaceAll(Brand, "'", "_")
		GameType := 0
		dianzi := []string{"Video Slots", "Scratch", "Unknown", "Arcade Games", "Jackpot Slots"}
		qipai := []string{"Table Games", "Blackjack", "Roulette", "Poker", "Sic Bo", "Baccarat", "Video Poker", "Other Table Games", "Dice", "Virtual Sports", "Scratch Cards"}
		quwei := []string{"Hi Lo", "Crash", "Crash Games"}
		zhenren := []string{"Live Baccarat", "Live Blackjack", "Live Roulette", "Bet On Poker", "Live Games", "Live Dealer", "Live Dice", "Dragon Tiger", "Other Live Games", "Live Poker", "Live Dragon Tiger", "Live Game Shows"}

		if in_array_string(dianzi, v.Category) {
			GameType = 1
		} else if in_array_string(qipai, v.Category) {
			GameType = 2
		} else if in_array_string(quwei, v.Category) {
			GameType = 3
		} else if in_array_string(zhenren, v.Category) {
			GameType = 5
		}

		// "3Oaks", "Retro Gaming", "SlotMill Games", "Booming Games", "BetsyGames", "Mancala Gaming", "Habanero", "Play'n Go" // 第一批上
		// "OneTouch", "Live88" // 第二批上
		if v.Product == "3Oaks" || v.Product == "Retro Gaming" || v.Product == "SlotMill Games" || v.Product == "Booming Games" || v.Product == "Mancala Gaming" || v.Product == "Habanero" || v.Product == "Play'n Go" {
			if GameType != 1 && GameType != 2 { // 第一批上 爆辣鸡块说 只上电子 棋牌 体育
				continue
			}
		} else if v.Product == "BetsyGames" { // 第一批上 // BetsyGames要上分类 体育
			if GameType != 6 {
				continue
			}
		} else if v.Product == "OneTouch" { // 第二批上 // OneTouch要上分类 棋牌 电子 真人 趣味
			if GameType != 1 && GameType != 2 && GameType != 3 && GameType != 5 {
				continue
			}
		} else if v.Product == "Live88" { // 第二批上 // Live88要上分类 真人
			if GameType != 5 {
				continue
			}
		} else {
			continue
		}

		if theReq.Add == "1" {
			gameList := GameListOps{}
			err = server.Db().GormDao().Table("x_game_list").Where("Brand=? and GameId = ?", Brand, v.gameCode).First(&gameList).Error
			if err != daogorm.ErrRecordNotFound {
				continue
			}

			//写入数据库中
			gameData := xgo.H{
				"Brand":     Brand,
				"GameId":    v.gameCode,
				"Name":      v.Name,
				"EName":     v.Name,
				"GameType":  GameType,
				"OpsType":   1, // 0非聚合类型 1ops类型
				"State":     2,
				"OpenState": 1,
			}
			server.Db().Table("x_game_list").Insert(gameData)
		}
	}
	ctx.RespJson(rest2)

}

func in_array_string1(arr []string, s string) bool {
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}

const cacheKeyOps = "cacheKeyOps:"
const cacheKeyOpsInfo = "cacheKeyOps:Info:%s"
const UNIT1 float64 = 100000

// ops登录
func (l *OpsService) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId     string `json:"GameId" validate:"required"` // 游戏code
		LangCode   string `json:"LangCode"`                   // 语言 en
		HomeUrl    string `json:"HomeUrl"`                    // 返回商户地址
		DeviceType string `json:"DeviceType"`                 // 设备类型 mobile
	}

	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("ops 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("ops Login 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyOps, userId); err != nil {
		logs.Error("ops Login 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, OPS_BRAND, reqdata.GameId); err != nil {
		logs.Error("ops 登录游戏 权限检查错误 userId=", userId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("ops 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	gameList := GameListOps{}
	brand := OPS_BRAND // Brand前缀，匹配OPS_后跟任意字符
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", reqdata.GameId).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("ops Login 查询游戏错误 userId=", token.UserId, " gameCode=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("ops Login 游戏不可用 userId=", token.UserId, " gameCode=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	//用户名
	username := fmt.Sprintf("%d", token.UserId)
	//server.Redis().SetString(fmt.Sprintf(cacheKeyOpsInfo, username))
	type thirdLogin struct {
		Game       string `json:"game"`
		Player     string `json:"player"`
		PlayerName string `json:"player_name"`
		Currency   string `json:"currency"`
		Lang       string `json:"lang"`
		Device     string `json:"device"`
		Lobby      string `json:"lobby"`
		Fun        bool   `json:"fun"`
	}

	thirdLoginReq := thirdLogin{
		Game:       reqdata.GameId,
		Player:     username,
		PlayerName: username,
		Currency:   l.Currency,
		Lang:       reqdata.LangCode,
		Device:     reqdata.DeviceType,
		Lobby:      reqdata.HomeUrl,
		Fun:        false,
	}

	reqbytes, _ := json.Marshal(thirdLoginReq)
	logs.Info("ops  请求三方登录参数:", string(reqbytes))
	var timestamp = fmt.Sprint(time.Now().Unix())
	var entry map[string]interface{}
	err = json.Unmarshal(reqbytes, &entry)
	if err != nil {
		logs.Error("ops Login 参数错误", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}
	content := ComputeSignature(l.Token, timestamp, entry)
	sign := Encrypt(content, l.Token)

	//if err != nil {
	//	logs.Error("ops  请求三方签名错误:", err.Error())
	//	ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
	//	return
	//}
	signStr := l.Key + ":" + sign
	header := map[string]string{
		"Signature": signStr,
		"Timestamp": timestamp,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    l.Url + "/seamless/game_url",
		Param:      reqbytes,
		Header:     header,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		logs.Error("ops Login 返回数据错误", token.UserId, " err=", err)
		ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
		return
	}
	// 读取响应数据
	var gameLink string
	gameLink = string(b)
	ctx.Put("url", gameLink)
	ctx.RespOK()
}

// 获取下注详情 三方接口存在问题暂未实现
func (l *OpsService) getRoundDetail(reqUser, reqUuid, reqRound string) (detailUrl string) {
	type RequestData struct {
		DateFrom int `json:"date_from"`
		DateTo   int `json:"date_to"`
		Count    int `json:"count"`
	}

	type Request struct {
		DateFrom int `json:"date_from"`
		DateTo   int `json:"date_to"`
		Count    int `json:"count"`
	}
	baseUrl := l.Url
	req := RequestData{}

	reqbytes, _ := json.Marshal(req)
	logs.Info("ops  请求三方开奖结果 参数:", string(reqbytes))

	signstr, err := Sign(l.Lobby, reqbytes)

	if err != nil {
		logs.Error("ops  请求三方开奖结果签名错误:", err.Error())
		return
	}
	heardmap := map[string]string{
		"X-Ops-Signature": signstr,
	}

	httpcclient := httpc.DoRequest{
		UrlPath:    baseUrl + "/operator/generic/v2/game/round",
		Param:      reqbytes,
		Header:     heardmap,
		PostMethod: httpc.JSON_DATA,
	}

	b, err := httpcclient.DoPostBytes()
	if err != nil {
		logs.Error("ops  请求三方开奖结果 请求错误:", err.Error())
		return
	}
	logs.Info("ops  请求三方开奖结果 响应:", string(b))

	type resp struct {
		Url string `json:"url"`
	}
	rest := resp{}
	_ = json.Unmarshal(b, &rest)
	detailUrl = rest.Url
	return
}

// Info 三方暂未实现
func (l *OpsService) Info(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		User        string `json:"user"`
		RequestUuid string `json:"request_uuid"`
	}

	type RespData struct {
		User        string `json:"user"`
		Status      string `json:"status"`
		RequestUuid string `json:"request_uuid"`
		Country     string `json:"country"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops  Info api  Body := ", string(bodyBytes))

	res := RespData{}
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("ops  Info api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		res.Status = "RS_ERROR_INVALID_PARTNER"
		logs.Error("ops  Info api  Unmarshal := ", res, err)
		ctx.RespJson(res)
		return
	}

	//验证签名
	signature := ctx.Gin().Request.Header.Get("X-Ops-Signature")
	logs.Info("ops  Info api  signature := ", signature)

	err = VerifySignature(l.brandName, bodyBytes, []byte(signature))
	if err != nil {
		res.Status = "RS_ERROR_INVALID_SIGNATURE"
		res.RequestUuid = reqdata.RequestUuid
		logs.Error("ops  Info api  reply := ", res, err)
		ctx.RespJson(res)
		return
	}

	//获取国家
	countryAny := server.Redis().Get(fmt.Sprintf(cacheKeyOpsInfo, reqdata.User))
	country := abugo.GetStringFromInterface(countryAny)
	if country == "" {
		logs.Error("ops  Info api  get country key=", fmt.Sprintf(cacheKeyOpsInfo, reqdata.User), " countryAny=", countryAny, " country=", country)
	}

	userId, _ := strconv.Atoi(reqdata.User)

	if err, _ = base.IsLoginByUserId(cacheKeyOps, userId); err != nil {
		res.Status = "RS_ERROR_INVALID_TOKEN"
		res.RequestUuid = reqdata.RequestUuid
		res.User = reqdata.User
		res.Country = country
		logs.Error("ops  Info api  reply := ", res)
		ctx.RespJson(res)
		return
	}

	res.Status = "RS_OK"
	res.RequestUuid = reqdata.RequestUuid
	res.User = reqdata.User
	res.Country = country
	logs.Error("ops  Info api  reply := ", res)
	ctx.RespJson(res)
	return

}

// ResponseErrData 发生错误时响应
type ResponseErrData struct {
	Message     string `json:"message"`
	Description string `json:"description"`
	Timestamp   int64  `json:"timestamp"`
}

// GetBalance 余额查询
func (l *OpsService) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Player   string `json:"player"`
		Currency string `json:"currency"`
	}

	type ResponseData struct {
		Balance   string `json:"balance"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops  Balance api  Body := ", string(bodyBytes))

	res := ResponseErrData{Timestamp: time.Now().Unix()}
	if err != nil {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "参数错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  Balance 参数错误,err=", err)
		return
	}

	// 检查请求参数是否与GetBalance接口的RequestData一致
	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.DisallowUnknownFields()

	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		res.Message = OPS_ERR_InvalidArgument
		if strings.Contains(err.Error(), "unknown field") {
			res.Description = "请求参数与接口定义不符"
			logs.Error("ops Balance 请求参数与接口定义不符,err=", err)
		} else {
			res.Description = "JSON解析失败"
			logs.Error("ops Balance JSON解析失败,err=", err)
		}
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	if !l.verifySignature(ctx, l.Token, bodyBytes) {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "签名错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)

	//获取余额
	_, balance, err := base.GetUserBalance(userId, cacheKeyOps)
	if err != nil {
		res.Message = OPS_ERR_InvalidAccount
		res.Description = "查询用户余额失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  Balance 查询用户余额失败,err=", err, res)
		return
	}

	resSuccess := ResponseData{Timestamp: time.Now().Unix()}
	resSuccess.Balance = strconv.FormatFloat(balance, 'f', -1, 64)
	ctx.RespJson(resSuccess)
	logs.Info("ops  Balance 查询余额成功 res=", res, err)

	return
}

// GetOrderStatus 订单状态查询
func (l *OpsService) GetOrderStatus(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		Round string `json:"round"` //下注編碼
	}

	type ResponseData struct {
		Status    string `json:"status"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops  OrderStatus api  Body := ", string(bodyBytes))

	res := ResponseErrData{Timestamp: time.Now().Unix()}
	if err != nil {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "参数错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  OrderStatus 参数错误,err=", err)
		return
	}

	// 检查请求参数是否与GetOrderStatus接口的RequestData一致
	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.DisallowUnknownFields()

	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		res.Message = OPS_ERR_InvalidArgument
		if strings.Contains(err.Error(), "unknown field") {
			res.Description = "请求参数与接口定义不符"
			logs.Error("ops OrderStatus 请求参数与接口定义不符,err=", err)
		} else {
			res.Description = "JSON解析失败"
			logs.Error("ops OrderStatus JSON解析失败,err=", err)
		}
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//验证签名
	signature := ctx.Gin().Request.Header.Get("X-Ops-Signature")
	logs.Info("ops  OrderStatus api  signature := ", signature)

	if !l.verifySignature(ctx, l.Token, bodyBytes) {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "签名错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  签名验证不通过")
		return
	}

	thirdId := reqdata.Round
	order := l.getOrder(thirdId, "x_third_dianzhi_pre_order")
	if order == nil {
		order = l.getOrder(thirdId, "x_third_qipai_pre_order")
		if order == nil {
			order = l.getOrder(thirdId, "x_third_live_pre_order")
			if order == nil {
				res.Message = OPS_ERR_InvalidRound
				res.Description = "订单不存在"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops  OrderStatus 订单不存在，thirdId=", thirdId)
				return
			}
		}
	}

	dataState := abugo.GetInt64FromInterface((*order)["DataState"])
	resSuccess := ResponseData{Timestamp: time.Now().Unix()}
	if dataState == -1 {
		resSuccess.Status = "running" //订单已确认
	} else if dataState == 1 {
		resSuccess.Status = "settled" //订单已结算
	} else if dataState == -2 {
		resSuccess.Status = "cancelled" //订单取消
	}
	ctx.RespJson(resSuccess)
	logs.Info("ops  OrderStatus 确认订单完成 res=", res, err)
	return
}

// 查询订单
func (l *OpsService) getOrder(thirdId, table string) *map[string]interface{} {
	brand := OPS_BRAND //开元聚合三方厂商以OPS_开头
	where := abugo.AbuDbWhere{}
	where.Add("and", "ThirdId", "=", thirdId, nil)
	where.Add("and", "Brand", "like", fmt.Sprintf("%s%%", brand), "")
	betTran, _ := server.Db().Table(table).Where(where).GetOne()
	if betTran == nil {
		return nil
	} else {
		return betTran
	}
}

// Win win -> 回傳失敗 -> round status: running -> retry win
func (l *OpsService) Win(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		TransactionId string `json:"transaction_id"` //交易編號。註：長度64以內
		Round         string `json:"round"`          // 下注編碼。註：長度64以內
		ParentRound   string `json:"parent_round"`   // 上游回合單號。註：長度64以內
		Game          string `json:"game"`           //遊戲代碼。註：長度64以內
		Player        string `json:"player"`         //玩家識別碼
		Currency      string `json:"currency"`       //幣別代碼
		Amount        string `json:"amount"`         //⾦額
		Session       string `json:"session"`
	}

	type ResponseData struct {
		Balance   string `json:"balance"`
		Status    string `json:"status"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops  Win api  Body := ", string(bodyBytes))

	res := ResponseErrData{Timestamp: time.Now().Unix()}     //失败响应
	resSuccess := ResponseData{Timestamp: time.Now().Unix()} //成功响应
	if err != nil {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "参数错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  Win 参数错误,err=", err)
		return
	}

	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.DisallowUnknownFields()

	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		res.Message = OPS_ERR_InvalidArgument
		if strings.Contains(err.Error(), "unknown field") {
			res.Description = "请求参数与接口定义不符"
			logs.Error("ops Win 请求参数与接口定义不符,err=", err)
		} else {
			res.Description = "JSON解析失败"
			logs.Error("ops Win JSON解析失败,err=", err)
		}
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//验证签名
	if !l.verifySignature(ctx, l.Token, bodyBytes) {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "签名错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId                 //用户ID
	cacheSignKey.Brand = l.brandName             //三方厂商名称
	cacheSignKey.ThirdId = reqdata.TransactionId //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if err != nil {
		res.Message = OPS_ERR_Failure
		res.Description = "检测重复请求出错"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测是否重复请求出错 transactionId=", reqdata.TransactionId, " err=", err)
		return
	}
	if duplicateResult != nil && err == nil {
		res.Message = OPS_ERR_DuplicateOperation
		res.Description = "重复请求"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.TransactionId, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		var resData interface{}
		resData = resSuccess
		if res.Message != "" { //保存本次响应结果，判断是错误响应还是正确响应
			resData = res
		}
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, resData, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.TransactionId, " err=", e.Error())
		}
	}()

	user, balance, err := base.GetUserById(userId)
	if err != nil {
		res.Message = OPS_ERR_InvalidAccount
		res.Description = "查询用户失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  Win 查询用户余额失败,err=", err, res)
		return
	}

	amount, _ := strconv.ParseFloat(reqdata.Amount, 64)
	if amount < 0 {
		res.Message = OPS_ERR_BalanceInsufficient
		res.Description = "金额不能小于0"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Win api 金额不能小于0")
		return
	}

	//三方来源的数据整理
	var (
		thirdId   = reqdata.Round
		gameCode  = reqdata.Game
		thirdTime = utils.GetCurrentTime()
	)

	//根据游戏获取分类
	brand := OPS_BRAND // Brand前缀，匹配OPS_后跟任意字符
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", gameCode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			res.Message = OPS_ERR_Failure
			res.Description = "游戏ID获取失败"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Refund api 游戏ID获取失败,游戏不存在 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand)
			return
		}
		res.Message = OPS_ERR_Failure
		res.Description = "游戏ID获取失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Refund api 游戏ID获取失败, thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand, " err=", err)
		return
	}
	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType

	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人

	var goldType int
	var tablePre, table string
	switch gameType {
	case 1:
		tablePre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	case 2:
		tablePre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
		// case 3:
		//     tablePre = "x_third_quwei_pre_order"
		//     table = "x_third_quwei"
		// case 4:
		//     tablePre = "x_third_lottery_pre_order"
		//     table = "x_third_lottery"
	case 5:
		tablePre = "x_third_live_pre_order"
		table = "x_third_live"
	case 6:
		tablePre = "x_third_sport_pre_order"
		table = "x_third_sport"
	default:
		res.Message = OPS_ERR_Failure
		res.Description = "游戏类型不正确"
		logs.Error("ops Win api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", res)
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	goldType = l.getOpsGoldChangeType(K_OPSCHANGETYPESETTLE, brandName)
	if goldType == 0 {
		res.Message = OPS_ERR_Failure
		res.Description = "获取账变类型错误"
		logs.Error("ops Win api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablePre=", tablePre, " reply= ", res)
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 先判断会员在判断订单
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("ops Win api 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			res.Message = OPS_ERR_InvalidAccount
			res.Description = "会员不存在"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Win api 查询用户余额失败 thirdId=", thirdId, " reply := ", res, amount)
			return e
		}

		betTran := ThirdOrderOps{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, brandName, userId).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil || betTran.Id == 0 {
			res.Message = OPS_ERR_InvalidRound
			res.Description = "订单不存在，或者select for update出错"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Win 订单不存在 thirdId=", thirdId, " e=", e)
			return e
		}

		dataState := betTran.DataState
		if dataState != -1 { // 订单已经被处理
			res.Message = OPS_ERR_Failure
			res.Description = "该订单已经被处理"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Win api 该订单已经被处理 id=", thirdId, " dataState= ", dataState)
			return errors.New(fmt.Sprintf("该订单已经处理，thirdId= %s", thirdId))
		}
		logs.Info("ops 结算订单数据: thirdId=", thirdId, " betTran=", betTran)

		//将下注订单移动至结算订单表
		//修改成已经结算了
		betId := betTran.Id
		betAmount := betTran.BetAmount
		validBet := betAmount

		winAmount := amount
		// 除了体育所有三方有效流水取不大于下注金额的输赢绝对值，体育有效流水取下注金额
		if gameType != 6 {
			validBet = math.Abs(winAmount - betAmount)
			if validBet > math.Abs(betAmount) {
				validBet = math.Abs(betAmount)
			}
		}

		betTran.WinAmount = winAmount
		betTran.ValidBet = validBet
		betTran.RawData = string(bodyBytes)
		betTran.GameId = gameCode
		betTran.GameName = gameName
		betTran.ThirdTime = thirdTime
		betTran.DataState = 1

		resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
			"WinAmount": winAmount,
			"ValidBet":  validBet,
			"RawData":   string(bodyBytes),
			"GameId":    gameCode,
			"GameName":  gameName,
			"ThirdTime": thirdTime,
			"DataState": 1,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = "修改订单错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Win api 修改订单错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " e=", e)
			return e
		}
		//移动至统计表
		betTran.Id = 0
		betTran.State = 1
		betTran.DataState = 1
		betTran.RawData = string(bodyBytes)
		betTran.CreateTime = thirdTime
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = "插入正式表记录错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Win api 插入正式表记录错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
			return e
		}

		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.Message = OPS_ERR_Failure
				res.Description = " Win api 修改用户余额错误"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops  Win api 修改用户余额错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " e=", e)
				return errors.New("修改x_user失败了")
			}
		}
		afterBalance := balance + amount
		amountLog := AmountChangeLogOps{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       amount,
			AfterAmount:  afterBalance,
			Reason:       goldType,
			Memo:         brandName + " settle,thirdId:" + thirdId,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Omit("Id").Create(&amountLog).Error
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = " Win api 插入账变记录错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Win api 插入账变记录错误 id=", thirdId, " reply= ", res, " betTran=", betTran, " table=", table, " amountLog=", amountLog, " e=", e)
			return e
		}
		resSuccess.Status = "settled"
		resSuccess.Balance = strconv.FormatFloat(afterBalance, 'f', -1, 64)
		ctx.RespJson(resSuccess)
		logs.Debug("ops Win 結算成功", res, " thirdId:", thirdId, " betTran=", betTran)
		return nil
	})
	if err == nil {
		// 推送奖励事件通知
		if l.thirdGamePush != nil {
			//l.thirdGamePush.PushRewardEvent(userId, gameName, brandName, betAmount, amount, l.Currency)
			l.thirdGamePush.PushRewardEvent(int(gameType), brandName, thirdId)
		}

		go func() {
			// 获取中将结果 三方接口存在问题暂未实现
			// time.Sleep(time.Second * 2)
			//url := l.getRoundDetail(reqdata.Player, reqdata.TransactionId, reqdata.Round)
			//if url != "" && thirdId != "" && brandName != "" {
			//	if e := server.Db().GormDao().Table(table).Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, brandName).Updates(map[string]interface{}{
			//		"BetCtx":     url,
			//		"GameRst":    url,
			//		"BetCtxType": 2,
			//	}).Error; e != nil {
			//		logs.Error("ops 异步更新开奖结果 错误 userId=", userId, "table=", table, "brand=", brandName, "gameCode=", reqdata.Game, " thirdId=", thirdId)
			//	}
			//	if e := server.Db().GormDao().Table(tablePre).Where("ThirdId=? and UserId=? and Brand=?", thirdId, userId, brandName).Updates(map[string]interface{}{
			//		"BetCtx":     url,
			//		"GameRst":    url,
			//		"BetCtxType": 2,
			//	}).Error; e != nil {
			//		logs.Error("ops 异步更新开奖结果 错误 userId=", userId, "tablePre=", tablePre, "brand=", brandName, "gameCode=", reqdata.Game, " thirdId=", thirdId)
			//	}
			//}
		}()
	}

	// 发送余额变动
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][OpsService] Win 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][OpsService] Win 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

// Refund 退款 Refund 分成已下兩種場景
// 1. bet 成功  —————-> refund 成功, status: cancelled
// 2. bet 成功 -> win 成功 -> refund 失敗, status: settled
func (l *OpsService) Refund(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		TransactionId string `json:"transaction_id"` //交易編號。註：長度64以內
		Round         string `json:"round"`          // 下注編碼。註：長度64以內
		ParentRound   string `json:"parent_round"`   // 上游回合單號。註：長度64以內
		Game          string `json:"game"`           //遊戲代碼。註：長度64以內
		Player        string `json:"player"`         //玩家識別碼
		Currency      string `json:"currency"`       //幣別代碼
		Amount        string `json:"amount"`         //下注⾦額
		Session       string `json:"session"`
	}

	type ResponseData struct {
		Balance   string `json:"balance"`
		Status    string `json:"status"`
		Timestamp int64  `json:"timestamp"`
	}

	type CacheRequest struct {
		Req RequestData     `json:"req"`
		Res ResponseErrData `json:"res"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops  Refund api Body := ", string(bodyBytes))

	res := ResponseErrData{Timestamp: time.Now().Unix()}
	resSuccess := ResponseData{Timestamp: time.Now().Unix()}

	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.DisallowUnknownFields()

	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		res.Message = OPS_ERR_InvalidArgument
		if strings.Contains(err.Error(), "unknown field") {
			res.Description = "参数无效"
			logs.Error("ops Refund 存在未定义的参数,err=", err)
		} else {
			res.Description = "JSON解析失败"
			logs.Error("ops Refund JSON解析失败,err=", err)
		}
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//验证签名
	if !l.verifySignature(ctx, l.Token, bodyBytes) {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "签名错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId                         //用户ID
	cacheSignKey.Brand = l.brandName                     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.TransactionId         //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String() //如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if err != nil {
		res.Message = OPS_ERR_Failure
		res.Description = "检测重复请求出错"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测是否重复请求出错 transactionId=", reqdata.TransactionId, " err=", err)
		return
	}
	if duplicateResult != nil && err == nil {
		res.Message = OPS_ERR_DuplicateOperation
		res.Description = "重复请求"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.TransactionId, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		var resData interface{}
		resData = resSuccess
		if res.Message != "" { //保存本次响应结果，判断是错误响应还是正确响应
			resData = res
		}
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, resData, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.TransactionId, " err=", e.Error())
		}
	}()

	//三方来源的数据整理
	var (
		thirdId  = reqdata.Round
		gameCode = reqdata.Game
	)

	//根据游戏获取分类
	brand := OPS_BRAND // Brand前缀，匹配OPS_后跟任意字符
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", gameCode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			res.Message = OPS_ERR_Failure
			res.Description = "游戏ID获取失败"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Refund api 游戏ID获取失败,游戏不存在 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand)
			return
		}
		res.Message = OPS_ERR_Failure
		res.Description = "游戏ID获取失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Refund api 游戏ID获取失败, thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand, " err=", err)
		return
	}
	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType

	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人

	var goldType int
	var tablePre, table string
	switch gameType {
	case 1:
		tablePre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
	case 2:
		tablePre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
	case 5:
		tablePre = "x_third_live_pre_order"
		table = "x_third_live"
	default:
		res.Message = OPS_ERR_Failure
		res.Description = "游戏类型不正确"
		logs.Error("ops Refund api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", res)
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	goldType = l.getOpsGoldChangeType(K_OPSCHANGETYPEROLLBACK, brandName)
	if goldType == 0 {
		res.Message = OPS_ERR_Failure
		res.Description = "获取账变类型错误"
		logs.Error("ops Refund api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablePre=", tablePre, " reply= ", res)
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 先判断用户是否正确 在判断订单是否存在
		user := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&user).Error
		if e != nil {
			logs.Error("ops Refund 取消订单 获取用户余额失败 thirdId=", thirdId, " userId=", userId, " err=", e.Error())
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				res.Message = OPS_ERR_InvalidAccount
				res.Description = "会员不存在"
				ctx.Gin().JSON(http.StatusBadRequest, res)
			} else {
				res.Message = OPS_ERR_InvalidAccount
				res.Description = "查询会员信息失败"
				ctx.Gin().JSON(http.StatusBadRequest, res)
			}
			return e
		}

		betTran := ThirdOrderOps{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil || betTran.Id == 0 {
			res.Message = OPS_ERR_InvalidRound
			res.Description = "订单不存在，或者select for update出错"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Refund 订单不存在 thirdId=", thirdId, " e=", e)
			return e
		}

		balance := user.Amount
		if betTran.DataState != -1 {
			if betTran.DataState == 1 {
				logs.Error("ops  Refund 订单已结算 thirdId=", thirdId, " reply= ", res, " tablePre=", tablePre, " betTran=", betTran)
				res.Message = OPS_ERR_Failure
				res.Description = "订单不存在，或者select for update出错"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				e = errors.New("订单已经结算")
				return e
			} else if betTran.DataState == -2 {
				resSuccess.Status = "cancelled"
				resSuccess.Balance = strconv.FormatFloat(balance, 'f', -1, 64)
				ctx.RespJson(resSuccess)
				logs.Error("ops  Refund 订单已撤销 thirdId=", thirdId, " reply= ", res, " tablePre=", tablePre, " betTran=", betTran)
				e = errors.New("订单已经撤销")
				return e
			} else {
				res.Message = OPS_ERR_Failure
				res.Description = "订单状态不对"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops  Refund 订单状态不对 thirdId=", thirdId, " reply= ", res, " tablePre=", tablePre, " betTran=", betTran)
				return errors.New("订单状态不对")
			}
		}

		betId := betTran.Id
		resultTmp := tx.Table(tablePre).Where("Id=?", betId).Updates(map[string]interface{}{
			"GameName":  gameName,
			"RawData":   daogorm.Expr("CONCAT(RawData, ?)", string(bodyBytes)),
			"DataState": -2,
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = "修改订单错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Refund 修改订单错误 thirdId=", thirdId, " reply= ", res, " tablePre=", tablePre, " betTran=", betTran, " e=", e)
			return errors.New("修改订单失败")
		}
		amount := betTran.BetAmount
		if amount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount+?", amount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.Message = OPS_ERR_Failure
				res.Description = "修改用户余额错误"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops  Refund 修改用户余额错误 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran, " e=", e)
				return errors.New("修改x_user失败了")
			}
		}
		afterAmount := balance + amount
		amountLog := AmountChangeLogOps{
			UserId:       userId,
			BeforeAmount: balance,
			Amount:       amount,
			AfterAmount:  afterAmount,
			Reason:       goldType,
			Memo:         brandName + " Refund,thirdId:" + thirdId,
			SellerId:     user.SellerId,
			ChannelId:    user.ChannelId,
			CreateTime:   time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
		}
		e = tx.Table("x_amount_change_log").Omit("Id").Create(&amountLog).Error
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = "插入账变记录错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops  Refund 插入账变记录错误 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran, " amountLog=", amountLog, " e=", e)
			return e
		}

		resSuccess.Status = "cancelled"
		resSuccess.Balance = strconv.FormatFloat(afterAmount, 'f', -1, 64)
		ctx.RespJson(resSuccess)
		logs.Info("ops  Refund 订单取消成功 thirdId=", thirdId, " reply= ", res, " table=", table, " betTran=", betTran)
		return nil
	})

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][OpsService] Refund 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][OpsService] Refund 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type RequestData struct {
	TransactionId string `json:"transaction_id"` //交易編號。註：長度64以內
	Round         string `json:"round"`          // 下注編碼。註：長度64以內
	ParentRound   string `json:"parent_round"`   // 上游回合單號。註：長度64以內
	Game          string `json:"game"`           //遊戲代碼。註：長度64以內
	Player        string `json:"player"`         //玩家識別碼
	Currency      string `json:"currency"`       //幣別代碼
	Amount        string `json:"amount"`         //下注⾦額
	Session       string `json:"session"`
}

// Bet 投注
func (l *OpsService) Bet(ctx *abugo.AbuHttpContent) {

	//l.mu.Lock()
	//defer l.mu.Unlock()

	type ResponseData struct {
		Balance   string `json:"balance"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("ops Bet api Body=", string(bodyBytes))

	//失败响应
	res := ResponseErrData{Timestamp: time.Now().Unix()}
	//成功响应
	resSuccess := ResponseData{Timestamp: time.Now().Unix()}

	if err != nil {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "参数错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  Bet 参数错误,err=", err)
		return
	}

	decoder := json.NewDecoder(bytes.NewReader(bodyBytes))
	decoder.DisallowUnknownFields()

	reqdata := RequestData{}
	if err := decoder.Decode(&reqdata); err != nil {
		res.Message = OPS_ERR_InvalidArgument
		if strings.Contains(err.Error(), "unknown field") {
			res.Description = "参数无效"
			logs.Error("ops Bet 存在未定义的参数,err=", err)
		} else {
			res.Description = "JSON解析失败"
			logs.Error("ops Bet JSON解析失败,err=", err)
		}
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//验证签名
	if !l.verifySignature(ctx, l.Token, bodyBytes) {
		res.Message = OPS_ERR_InvalidArgument
		res.Description = "签名错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops  签名验证不通过")
		return
	}

	userId_ := reqdata.Player
	userId, _ := strconv.Atoi(userId_)
	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId                 //用户ID
	cacheSignKey.Brand = l.brandName             //三方厂商名称
	cacheSignKey.ThirdId = reqdata.TransactionId //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if err != nil {
		res.Message = OPS_ERR_Failure
		res.Description = "检测重复请求出错"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测是否重复请求出错 transactionId=", reqdata.TransactionId, " err=", err)
		return
	}
	if duplicateResult != nil && err == nil {
		res.Message = OPS_ERR_DuplicateOperation
		res.Description = "重复请求"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.TransactionId, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		var resData interface{}
		resData = resSuccess
		if res.Message != "" { //保存本次响应结果，判断是错误响应还是正确响应
			resData = res
		}
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, resData, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.TransactionId, " err=", e.Error())
		}
	}()

	user, _, err := base.GetUserById(userId)
	if err != nil {
		res.Message = OPS_ERR_InvalidAccount
		res.Description = "查询用户失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Bet 查询用户失败,err=", err, res)
		return
	}

	amount, _ := strconv.ParseFloat(reqdata.Amount, 64)
	if amount < 0 {
		res.Message = OPS_ERR_BalanceInsufficient
		res.Description = "金额必须大于0"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Bet api 金额必须大于0")
		return
	}

	//三方来源的数据整理
	var (
		betAmount = amount
		thirdId   = reqdata.Round
		gameCode  = reqdata.Game
		thirdTime = utils.GetCurrentTime()
	)

	//根据游戏获取分类
	brand := OPS_BRAND // Brand前缀，匹配OPS_后跟任意字符
	var gameList thirdGameModel.GameList
	err = server.Db().GormDao().Table("x_game_list").Where("Brand LIKE ? and GameId = ?", brand+"%", gameCode).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			res.Message = OPS_ERR_Failure
			res.Description = "游戏ID获取失败"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Refund api 游戏ID获取失败,游戏不存在 thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand)
			return
		}
		res.Message = OPS_ERR_Failure
		res.Description = "游戏ID获取失败"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Refund api 游戏ID获取失败, thirdId=", thirdId, " GameId=", gameCode, " Brand=", brand, " err=", err)
		return
	}
	brandName := gameList.Brand
	gameName := gameList.Name
	gameType := gameList.GameType

	//趣味和彩票不需要对接
	//1 =dianzi  2=qipai  3=老趣味 4=彩票 5=hub真人 6=hub体育
	var goldType int
	var tablePre string
	switch gameType {
	case 1: //电子游戏
		tablePre = "x_third_dianzhi_pre_order"
	case 2: //棋牌游戏
		tablePre = "x_third_qipai_pre_order"
	case 5: //真人游戏
		tablePre = "x_third_live_pre_order"
	default:
		res.Message = OPS_ERR_Failure
		res.Description = "游戏类型不正确"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Bet api 游戏类型不正确 thirdId=", thirdId, " gameType=", gameType, " reply := ", res)
		return
	}

	goldType = l.getOpsGoldChangeType(K_OPSCHANGETYPEBET, brandName)
	if goldType == 0 {
		res.Message = OPS_ERR_Failure
		res.Description = "获取账变类型错误"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		logs.Error("ops Bet api 获取账变类型错误 thirdId=", thirdId, " brandName=", brandName, " tablePre=", tablePre, " reply= ", res)
		return
	}
	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, brandName, gameCode); err != nil {
		logs.Error("ops Bet api 权限检查错误 userId=", userId, " gameId=", gameCode, " err=", err.Error())
		res.Message = OPS_ERR_InvalidAccount
		res.Description = "玩家禁止下注"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	} else if !allowed {
		logs.Error("ops Bet api 权限被拒绝 userId=", userId, " gameId=", gameCode, " hint=", hint)
		res.Message = OPS_ERR_InvalidAccount
		res.Description = "玩家禁止下注"
		ctx.Gin().JSON(http.StatusBadRequest, res)
		return
	}

	//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
	//获取投注渠道
	ChannelId := base.GetUserChannelId(ctx, user)
	//开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 获取用户余额 行锁处理并发修改用户余额问题
		userBalance := thirdGameModel.UserBalance{}
		e := tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				logs.Error("ops Bet api 下注失败 会员不存在,thirdId=", thirdId, "userId=", userId)
			}
			res.Message = OPS_ERR_InvalidAccount
			res.Description = "会员不存在"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Bet api 查询用户余额失败 thirdId=", thirdId, " reply := ", res, betAmount)
			return e
		}

		balance := userBalance.Amount
		//判断用户余额
		if balance < 0 || balance < betAmount {
			res.Message = OPS_ERR_BalanceInsufficient
			res.Description = "用户余额不足"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Bet api 用户余额不足 thirdId=", thirdId, " reply := ", res, betAmount, balance)
			e = errors.New("玩家余额不足")
			return e
		}

		//查询订单是否存在
		betTran := ThirdOrderOps{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error

		if e == nil || betTran.Id > 0 {
			logs.Error("ops Bet 下注失败 注单已存在 thirdId=", thirdId)
			res.Message = OPS_ERR_InvalidRound
			res.Description = "订单已存在"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Bet api 订单已存在 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " betTran=", betTran)
			return errors.New(fmt.Sprintf("订单已存在，thirdId= %s", thirdId))
		}
		if !errors.Is(e, daogorm.ErrRecordNotFound) {
			res.Message = OPS_ERR_Failure
			res.Description = "查询订单错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Bet api 查询订单错误 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " e=", e)
			return e
		}

		if betAmount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ? and amount>= ?", userId, betAmount).Updates(map[string]interface{}{
				"amount": daogorm.Expr("amount-?", betAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				res.Message = OPS_ERR_Failure
				res.Description = "修改用户余额错误"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops Bet api 修改用户余额错误 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " e=", e)
				return errors.New("修改x_user失败了")
			}
		}

		afterBalance := balance - betAmount
		order := ThirdOrderOps{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        brandName,
			ThirdId:      thirdId,
			GameId:       gameCode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.Currency,
			RawData:      string(bodyBytes),
			State:        1,
			DataState:    -1,
			CreateTime:   thirdTime,
			BetCtx:       string(bodyBytes),
			BetCtxType:   1,
		}
		e = tx.Table(tablePre).Omit("Id").Create(&order).Error
		if e != nil {
			res.Message = OPS_ERR_Failure
			res.Description = "新增注单错误"
			ctx.Gin().JSON(http.StatusBadRequest, res)
			logs.Error("ops Bet api 新增注单错误 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " order=", order, " e=", e)
			return e
		}

		if betAmount > 0 {
			amountLog := AmountChangeLogOps{
				UserId:       userId,
				BeforeAmount: balance,
				Amount:       0 - betAmount,
				AfterAmount:  afterBalance,
				Reason:       goldType,
				Memo:         brandName + " bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   utils.GetCurrentTime(),
			}
			e = tx.Table("x_amount_change_log").Omit("Id").Create(&amountLog).Error
			if e != nil {
				res.Message = OPS_ERR_Failure
				res.Description = "添加账变记录错误"
				ctx.Gin().JSON(http.StatusBadRequest, res)
				logs.Error("ops Bet api 添加账变记录错误 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " order=", order, " e=", e)
				return e
			}
		}
		resSuccess.Balance = strconv.FormatFloat(afterBalance, 'f', -1, 64)
		ctx.RespJson(resSuccess)
		logs.Info("ops Bet api 下注成功 thirdId=", thirdId, " tablePre=", tablePre, " reply=", res, " order=", order)
		return nil
	})
	// 推送下注事件通知
	if l.thirdGamePush != nil {
		l.thirdGamePush.PushBetEvent(userId, gameName, brandName, betAmount, l.Currency, brandName, thirdId, int(gameType))
	}
	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][OpsService] Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][OpsService] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

func ComputeSignature(token string, timestamp string, content map[string]interface{}) []byte {
	var (
		buf      bytes.Buffer
		entrySet = make([]string, 0, len(content))
		SECRET   = []byte(token)
	)

	keys := make([]string, 0, len(content))
	for k, _ := range content {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, k := range keys {
		kv := fmt.Sprintf("%s=%v", k, content[k])
		entrySet = append(entrySet, kv)
	}
	buf.WriteString(strings.Join(entrySet, "&"))
	buf.WriteString(":")
	buf.Write(SECRET)
	buf.WriteString(timestamp)
	return buf.Bytes()
}

func Encrypt(message []byte, token string) string {
	var ALGORITHMS = sha256.New
	var SECRET = []byte(token)
	hmacSha256 := hmac.New(ALGORITHMS, SECRET)
	hmacSha256.Write(message)
	hash := hmacSha256.Sum(nil)
	// to hex
	digest := make([]byte, hex.EncodedLen(len(hash)))
	hex.Encode(digest, hash)
	// to base64url
	return base64.RawURLEncoding.EncodeToString(digest)
}

const K_OPSCHANGETYPEBET int = 1
const K_OPSCHANGETYPESETTLE int = 2
const K_OPSCHANGETYPEROLLBACK int = 3

// 1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育
// 返回账变类型
func (l *OpsService) getOpsGoldChangeType(t int, brand string) (goldType int) {
	// 定义账变类型映射
	changeTypeMap := map[string]map[int]int{
		"OPS_BGaming": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSBGamingBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSBGamingSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSBGamingRollback,
		},
		"OPS_CQ9": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSCQ9Bet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSCQ9Settle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSCQ9Rollback,
		},
		"OPS_Funky_Games": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSFunkyGamesBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSFunkyGamesSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSFunkyGamesRollback,
		},
		"OPS_PlayStar": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSPlayStarBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSPlayStarSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSPlayStarRollback,
		},
		"OPS_JDB": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSJDBBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSJDBSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSJDBRollback,
		},
		"OPS_Hacksaw": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSHacksawBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSHacksawSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSHacksawRollback,
		},
		"OPS_PlaynGo": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSPlaynGoBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSPlaynGoSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSPlaynGoRollback,
		},
		"OPS_Bng": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSBngBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSBngSettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSBngRollback,
		},
		"OPS_Rubyplay": {
			K_OPSCHANGETYPEBET:      utils.BalanceCReasonOPSrubyplayBet,
			K_OPSCHANGETYPESETTLE:   utils.BalanceCReasonOPSrubyplaySettle,
			K_OPSCHANGETYPEROLLBACK: utils.BalanceCReasonOPSrubyplayRollback,
		},
	}

	// 获取对应品牌的账变类型映射
	if brandTypes, exists := changeTypeMap[brand]; exists {
		// 获取对应操作类型的账变类型
		if changeType, ok := brandTypes[t]; ok {
			goldType = changeType
		}
	}

	return
}

// 验证签名函数
func (l *OpsService) verifySignature(ctx *abugo.AbuHttpContent, token string, bodyBytes []byte) bool {
	var entry map[string]interface{}
	err := json.Unmarshal(bodyBytes, &entry)
	if err != nil {
		logs.Error("ops  verifySignature 参数解析失败,err=", err)
		return false
	}
	//验证签名
	signature := ctx.Gin().Request.Header.Get("Signature")
	timestamp := ctx.Gin().Request.Header.Get("Timestamp")
	content := ComputeSignature(l.Token, timestamp, entry)
	sign := Encrypt(content, l.Token)
	newSignStr := l.Key + ":" + sign
	//logs.Info("ops  Balance api  signature = ", signature, "newSignStr = ", newSignStr, "timestamp = ", timestamp)
	if signature == newSignStr {
		return true
	}

	return false
}
