package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	"xserver/controller/third/single/httpc"
	"xserver/controller/userbrand"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/imroc/req"
	"github.com/shopspring/decimal"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

type GFGConfig struct {
	// 默认线路配置
	defaultConfig GFGRouteConfig
	// 新手保护线路配置
	xinshouConfig         GFGRouteConfig
	brandName             string
	RefreshUserAmountFunc func(int) error
	thirdGamePush         *base.ThirdGamePush
}

// GFGRouteConfig GFG线路配置
type GFGRouteConfig struct {
	Url      string `json:"url"`
	Key      string `json:"key"`
	Company  string `json:"company"`
	Theme    string `json:"theme"`
	Agent    string `json:"agent"`
	AppUrl   string `json:"appUrl"`
	Currency string `json:"currency"`
}

type ThirdOrderGfg struct {
	Id           int64   `json:"Id" gorm:"column:Id"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
	BetChannelId int     `gorm:"column:BetChannelId;comment:下注渠道Id" json:"BetChannelId"` // 下注渠道Id
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	Brand        string  `json:"Brand" gorm:"column:Brand"`
	ThirdId      string  `json:"ThirdId" gorm:"column:ThirdId"`
	GameId       string  `json:"GameId" gorm:"column:GameId"`
	GameName     string  `json:"GameName" gorm:"column:GameName"`
	BetAmount    float64 `json:"BetAmount" gorm:"column:BetAmount"`
	WinAmount    float64 `json:"WinAmount" gorm:"column:WinAmount"`
	ValidBet     float64 `json:"ValidBet" gorm:"column:ValidBet"`
	ThirdTime    string  `json:"ThirdTime" gorm:"column:ThirdTime"`
	Currency     string  `json:"Currency" gorm:"column:Currency"`
	RawData      string  `json:"RawData" gorm:"column:RawData"`
	State        int     `json:"State" gorm:"column:State"`
	Fee          float64 `json:"Fee" gorm:"column:Fee"`
	DataState    int     `json:"DataState" gorm:"column:DataState"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	CSGroup      string  `json:"CSGroup" gorm:"column:CSGroup"`
	CSId         string  `json:"CSId" gorm:"column:CSId"`
	TopAgentId   int     `json:"TopAgentId" gorm:"column:TopAgentId"`
	SpecialAgent int     `json:"SpecialAgent" gorm:"column:SpecialAgent"`
	BetCtx       string  `json:"BetCtx" gorm:"column:BetCtx"`
	GameRst      string  `json:"GameRst" gorm:"column:GameRst"`
	BetCtxType   int     `json:"BetCtxType" gorm:"column:BetCtxType"`
}

type AmountChangeLogGfg struct {
	Id           int     `json:"Id" gorm:"column:Id"`
	UserId       int     `json:"UserId" gorm:"column:UserId"`
	BeforeAmount float64 `json:"BeforeAmount" gorm:"column:BeforeAmount"`
	Amount       float64 `json:"Amount" gorm:"column:Amount"`
	AfterAmount  float64 `json:"AfterAmount" gorm:"column:AfterAmount"`
	Reason       int     `json:"Reason" gorm:"column:Reason"`
	CreateTime   string  `json:"CreateTime" gorm:"column:CreateTime"`
	Memo         string  `json:"Memo" gorm:"column:Memo"`
	SellerId     int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId    int     `json:"ChannelId" gorm:"column:ChannelId"`
}

type UserBalanceGfg struct {
	UserId    int     `json:"UserId" gorm:"column:UserId"`
	SellerId  int     `json:"SellerId" gorm:"column:SellerId"`
	ChannelId int     `json:"ChannelId" gorm:"column:ChannelId"`
	Amount    float64 `json:"Amount" gorm:"column:Amount"`
	Token     string  `json:"Token" gorm:"column:Token"`
}

type GameListGfg struct {
	Id        int    `json:"Id" gorm:"column:Id"`
	Brand     string `json:"Brand" gorm:"column:Brand"`
	GameId    string `json:"GameId" gorm:"column:GameId"`
	Name      string `json:"Name" gorm:"column:Name"`
	EName     string `json:"EName" gorm:"column:EName"`
	State     int    `json:"State" gorm:"column:State"`
	OpenState int    `json:"OpenState" gorm:"column:OpenState"`
	GameType  int    `json:"GameType" gorm:"column:GameType"`
	HubType   int    `json:"HubType" gorm:"column:HubType"`
}

func NewGFGLogic(params map[string]string, fc func(int) error) *GFGConfig {
	// 解析新的配置格式
	configJson := params["config"]
	if configJson == "" {
		// 兼容旧格式
		return &GFGConfig{
			defaultConfig: GFGRouteConfig{
				Url:      params["url"],
				Key:      params["key"],
				Company:  params["company"],
				Theme:    params["theme"],
				Agent:    params["agent"],
				AppUrl:   params["appUrl"],
				Currency: params["currency"],
			},
			xinshouConfig:         GFGRouteConfig{}, // 空配置，使用默认配置
			brandName:             "gfg",
			RefreshUserAmountFunc: fc,
			thirdGamePush:         base.NewThirdGamePush(),
		}
	}

	// 解析新的JSON配置格式
	var configData struct {
		Default GFGRouteConfig `json:"default"`
		Xinshou GFGRouteConfig `json:"xinshou"`
	}

	if err := json.Unmarshal([]byte(configJson), &configData); err != nil {
		logs.Error("GFG配置解析失败:", err, " config=", configJson)
		// 解析失败时使用默认配 置
		return &GFGConfig{
			defaultConfig: GFGRouteConfig{
				Url:      params["url"],
				Key:      params["key"],
				Company:  params["company"],
				Theme:    params["theme"],
				Agent:    params["agent"],
				AppUrl:   params["appUrl"],
				Currency: params["currency"],
			},
			brandName:             "gfg",
			RefreshUserAmountFunc: fc,
			thirdGamePush:         base.NewThirdGamePush(),
		}
	}

	return &GFGConfig{
		defaultConfig:         configData.Default,
		xinshouConfig:         configData.Xinshou,
		brandName:             "gfg",
		RefreshUserAmountFunc: fc,
		thirdGamePush:         base.NewThirdGamePush(),
	}
}

const cacheKeyGFG = "cacheKeyGFG:"

// getRouteConfig 根据用户ID获取对应的线路配置
func (l *GFGConfig) getRouteConfig(userId int) GFGRouteConfig {
	// 检查用户是否为新手保护用户
	if userId > 0 {
		status, err := userbrand.UserProtectionSvc.CheckUserProtectionStatus(server.Db().GormDao(), userId)
		if err != nil {
			logs.Error("GFG获取用户保护状态失败 userId=", userId, " err=", err.Error())
			return l.defaultConfig
		}

		// 如果是新手保护用户且有新手配置，使用新手线路
		if status.IsProtected && l.xinshouConfig.Url != "" {
			logs.Info("GFG新手保护用户使用新手线路 userId=", userId)
			return l.xinshouConfig
		}
	}

	// 默认使用普通线路
	return l.defaultConfig
}

func (l *GFGConfig) sign(s string, config GFGRouteConfig) string {
	return base.MD5(s + config.Key)
}

func (l *GFGConfig) userId2tokenByGfg(cacheKey string, userId int) string {
	token := "gfg_" + uuid.NewString()
	if err := server.Redis().SetStringEx(cacheKey+token, 86400, strconv.Itoa(userId)); err != nil {
		logs.Error("gfg单一钱包 userId2token set redis key=", cacheKey+token, " userId=", userId, " error=", err.Error())
	}
	return token
}

func (l *GFGConfig) token2UserIdByGfg(cacheKey, token string) int {
	redisdata := server.Redis().Get(cacheKey + token)
	if redisdata == nil {
		return -1
	}
	if uidStr, ok := redisdata.([]byte); ok {
		if uid, err := strconv.Atoi(string(uidStr)); err == nil {
			return uid
		}
	}
	return -1
}

// 获取用户Id
func (l *GFGConfig) getUserIdByAccountId(accountId_ string) int {
	// 获取ID，下划线后面的部分
	accountId := strings.Split(accountId_, "_")[1]
	userId, _ := strconv.Atoi(accountId)
	return userId
}

// getConfigByAccountId 根据accountId获取对应的配置
func (l *GFGConfig) getConfigByAccountId(accountId_ string) GFGRouteConfig {
	userId := l.getUserIdByAccountId(accountId_)
	return l.getRouteConfig(userId)
}

// IsGFGAccount 检查accountId是否属于GFG（匹配任一线路的Agent）
func (l *GFGConfig) IsGFGAccount(accountId string) bool {
	if accountId == "" {
		return false
	}

	s := strings.Split(accountId, "_")
	if len(s) == 0 {
		return false
	}

	agent := s[0]

	// 检查是否匹配默认线路的Agent
	if agent == l.defaultConfig.Agent {
		return true
	}

	// 检查是否匹配新手线路的Agent（如果配置了的话）
	if l.xinshouConfig.Agent != "" && agent == l.xinshouConfig.Agent {
		return true
	}

	return false
}

// Login
func (l *GFGConfig) Login(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		GameId   int
		LangCode string `validate:"required"`
		ExitUrl  string
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	token := server.GetToken(ctx)
	if err, errcode = base.IsLoginByUserId(cacheKeyGFG, token.UserId); err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 根据用户类型获取对应的线路配置
	routeConfig := l.getRouteConfig(token.UserId)
	logs.Info("GFG登录使用线路配置 userId=", token.UserId, " url=", routeConfig.Url, " agent=", routeConfig.Agent)

	//sessionId
	sessionId := l.userId2tokenByGfg(cacheKeyGFG, token.UserId)
	//account
	account := fmt.Sprintf("%s_%d", routeConfig.Agent, token.UserId)

	gamecode := fmt.Sprintf("%d", reqdata.GameId)
	gameList := GameListGfg{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		if err == daogorm.ErrRecordNotFound {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("gfg单一钱包 Login 查询游戏错误 userId=", token.UserId, " gamecode=", gamecode, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	// 801=虎虎生财 802=亡灵大盗 803=麻将胡了2 804=十倍金牛
	if gamecode != "801" && gamecode != "802" && gamecode != "803" && gamecode != "804" {
		if gameList.State != 1 || gameList.OpenState != 1 {
			logs.Error("gfg单一钱包 Login 游戏不 可用 userId=", token.UserId, " gamecode=", gamecode, " gameList=", gameList)
			ctx.RespErrString(true, &errcode, "游戏未开放")
			return
		}
	}
	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(token.UserId, l.brandName, gamecode); err != nil {
		logs.Error("gfg_single 登录游戏 权限检查错误 userId=", token.UserId, " gameId=", gamecode, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("gfg_single 登录游戏 权限被拒绝 userId=", token.UserId, " gameId=", gamecode, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	querydata := make(req.Param)
	querydata["agent"] = routeConfig.Agent
	querydata["companyKey"] = routeConfig.Company
	querydata["theme"] = routeConfig.Theme
	querydata["account"] = account
	querydata["gameId"] = reqdata.GameId
	querydata["ip"] = ctx.GetIp()
	querydata["platform"] = 2
	querydata["token"] = sessionId
	querydata["nickName"] = fmt.Sprint(token.UserId)
	querydata["timestamp"] = fmt.Sprintf("%d", time.Now().UnixMilli())
	querydata["languageType"] = reqdata.LangCode
	querydata["exitUrl"] = reqdata.ExitUrl
	querydata["appUrl"] = routeConfig.AppUrl + "/api/third/gfg"
	urlreq := routeConfig.Url + "/login"
	reqBytes, _ := json.Marshal(querydata)
	au := l.sign(string(reqBytes), routeConfig)
	header := map[string]string{
		"Authorization": au,
	}
	httpclient := httpc.DoRequest{
		UrlPath:    urlreq,
		Param:      reqBytes,
		PostMethod: httpc.JSON_DATA,
		Header:     header,
	}
	data, err := httpclient.DoPost()
	if err != nil {
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}
	if code, ok := data["code"]; ok {
		if abugo.GetInt64FromInterface(code) == 0 {
			ctx.RespOK(data["data"].(map[string]interface{})["url"])
			return
		}
	}
	logs.Error("gfg单一钱包  登录三方错误 msg=", data["msg"].(string))
	ctx.RespErrString(true, &errcode, "进入失败,请稍后再试")
	return
}

// GetBalance 查询会员余额 api/Balance/GetBalance
func (l *GFGConfig) GetBalance(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		AccountId string `json:"accountId"`
		Timestamp int64  `json:"timestamp"`
	}

	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg单一钱包 GetBalance := ", string(bodyBytes))
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "参数读取参数错误",
			"timestamp": time.Now().Unix(),
		})
		logs.Error("gfg单一钱包 GetBalance 错误", err.Error())
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg单一钱包 GetBalance 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}

	reqdata := RequestData{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "JSON解析失败",
			"timestamp": time.Now().Unix(),
		})
		logs.Error("gfg单一钱包 JSON解析失败", err.Error())
		return
	}

	// 获取用户Id，下划线后面的部分
	UserId := l.getUserIdByAccountId(reqdata.AccountId)
	if UserId <= 0 {
		logs.Error("gfg单一钱包 无效的用户Id", reqdata.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 开启余额查询事务
	userBalance := UserBalanceGfg{}
	tx := server.Db().GormDao().Begin()
	if tx.Error != nil {
		logs.Error("gfg单一钱包 GetBalance 查询用户余额时，事务开启失败 userId=", UserId, " err=", tx.Error)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	// 使用 FOR UPDATE 加锁查询，防止其他事务修改这行数据
	if err := tx.Table("x_user").Where("UserId = ?", UserId).Set("gorm:query_option", "FOR UPDATE").First(&userBalance).Error; err != nil {
		tx.Rollback()
		logs.Error("gfg单一钱包 Bet 查询用户余额错误 userId=", UserId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfg单一钱包 Bet 用户余额为负数 userId=", UserId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
			"data":      gin.H{"Data": float64(int(userBalance.Amount*100)) / 100},
		})
		return
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logs.Error("gfg单一钱包 Bet 查询用户余额错误,提交事务失败 userId=", UserId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100
	// logs.Info("gfg单一钱包 GetBalance:", UserId, fmt.Sprintf("%v", balance2))
	ctx.Gin().JSON(200, gin.H{
		"code":      int32(0),
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Data": balance2},
		"msg":       "",
	})
}

func (l *GFGConfig) check(authorization, bodyBytes string) bool {
	if authorization == "" {
		return false
	}

	// 尝试用默认配置验证
	if base.MD5(bodyBytes+l.defaultConfig.Key) == authorization {
		return true
	}

	// 尝试用新手保护配置验证
	if l.xinshouConfig.Key != "" && base.MD5(bodyBytes+l.xinshouConfig.Key) == authorization {
		return true
	}

	return false
}

// CancelBet 取消下注 api/Balance/CancelBet
func (l *GFGConfig) CancelBet(ctx *abugo.AbuHttpContent) {
	// 读取原始请求体
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg单一钱包 CancelBet bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfg单一钱包 CancelBet 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 验证签名
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg单一钱包 CancelBet 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}

	// 解析请求体
	type cancelBetReq struct {
		AgentId       string   `json:"agentId"`       // 代理ID
		Account       string   `json:"account"`       // 玩家账号
		MainRoundId   string   `json:"mainRoundId"`   // 欲取消交易单所属局号
		RoundIds      []string `json:"roundIds"`      // 欲取消的交易单号列表
		BalanceChange float64  `json:"balanceChange"` // 取消该交易单对于玩家余额变动值
		Timestamp     int64    `json:"timestamp"`     // 时间戳（毫秒级）
		Token         string   `json:"token"`         // 登录时平台方提供的token，可选
	}

	reqdata := cancelBetReq{}
	err = json.Unmarshal(bodyBytes, &reqdata)
	if err != nil {
		logs.Error("gfg单一钱包 CancelBet JSON解析失败", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      703,
			"msg":       "JSON解析失败",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 验证参数
	if len(reqdata.RoundIds) == 0 {
		logs.Error("gfg单一钱包 CancelBet 订单ID列表为空")
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "订单ID列表为空",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 验证代理ID
	agentId := strings.Split(reqdata.Account, "_")[0]
	// 检查代理ID是否匹配（支持多配置）
	if agentId != l.defaultConfig.Agent && agentId != l.xinshouConfig.Agent {
		logs.Error("gfg单一钱包 CancelBet 代理ID不匹配 agentId=", agentId, " defaultAgent=", l.defaultConfig.Agent, " xinshouAgent=", l.xinshouConfig.Agent)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 获取用户ID
	userId := l.getUserIdByAccountId(reqdata.Account)
	if userId <= 0 {
		logs.Error("gfg单一钱包 CancelBet 无效的用户Id", reqdata.Account)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 检查是否重复请求
	cacheKey := fmt.Sprintf("gfg:cancelbet:%s:%d", strings.Join(reqdata.RoundIds, "_"), userId)
	if server.Redis().Exists(cacheKey) {
		logs.Error("gfg单一钱包 CancelBet 重复请求 cacheKey=", cacheKey)
		ctx.Gin().JSON(200, gin.H{
			"code":      1001,
			"msg":       "重复请求",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 设置缓存，防止重复请求，有效期5分钟
	server.Redis().SetStringEx(cacheKey, 300, "1")

	tablePre := "x_third_live_pre_order"
	// 开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询并锁定用户余额
		userBalance := thirdGameModel.UserBalance{}
		if err := tx.Table("x_user").Where("UserId = ?", userId).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&userBalance).Error; err != nil {
			logs.Error("gfg单一钱包 CancelBet 查询用户余额错误 userId=", userId, " err=", err)
			return err
		}

		// 处理每个订单
		var totalRefundAmount float64 = 0
		thirdTime := time.Now().Format("2006-01-02 15:04:05")
		// 记录处理成功的单号
		successOrderIds := make([]string, 0)

		for _, orderId := range reqdata.RoundIds {
			// 查询并锁定订单
			order := ThirdOrderGfg{}
			if err := tx.Table(tablePre).Where("ThirdId=? and Brand=?", orderId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error; err != nil {
				if err == daogorm.ErrRecordNotFound {
					// 订单不存在，跳过
					logs.Warn("gfg单一钱包 CancelBet 订单不存在 orderId=", orderId, " userId=", userId)
					continue
				}
				logs.Error("gfg单一钱包 CancelBet 查询订单错误 orderId=", orderId, " userId=", userId, " err=", err)
				return err
			}

			// 检查订单状态
			if order.DataState != -1 {
				// 订单已结算或已取消，跳过
				logs.Error("gfg单一钱包 CancelBet 订单状态不允许取消 orderId=", orderId, " userId=", userId, " state=", order.DataState)
				continue
			}

			// 更新订单状态为已取消
			if err := tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
				"DataState": -2,
				"ThirdTime": thirdTime,
				"ValidBet":  0,
				"WinAmount": 0,
				"RawData":   string(bodyBytes),
			}).Error; err != nil {
				logs.Error("gfg单一钱包 CancelBet 更新订单状态错误 orderId=", orderId, " userId=", userId, " err=", err)
				return err
			}

			// 累计退款金额
			totalRefundAmount += order.BetAmount
			// 添加成功处理的订单ID
			successOrderIds = append(successOrderIds, orderId)
		}

		// 使用请求中的余额变化或计算的总退款金额
		refundAmount := totalRefundAmount
		if reqdata.BalanceChange > 0 {
			refundAmount = reqdata.BalanceChange
		}

		// 如果没有有效订单需要取消，直接返回
		if refundAmount <= 0 || len(successOrderIds) == 0 {
			// 不需要做任何更改，直接返回
			return nil
		}

		// 更新用户余额
		beforeAmount := userBalance.Amount
		afterAmount := beforeAmount + refundAmount

		if refundAmount != 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", refundAmount),
			})
			e := resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && refundAmount != 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("GFG 取消下注 更新用户余额失败 userId=", userId, " refundAmount=", refundAmount, " err=", e.Error())
				return e
			}
		}

		// 生成处理成功的订单ID字符串
		successOrdersStr := strings.Join(successOrderIds, ",")

		// 创建金额变更日志，记录处理成功的单号
		amountChangeLog := thirdGameModel.AmountChangeLog{
			UserId:       userId,
			BeforeAmount: beforeAmount,
			Amount:       refundAmount,
			AfterAmount:  afterAmount,
			Reason:       utils.BalanceCReasonLiveCancel,
			CreateTime:   time.Now().Format("2006-01-02 15:04:05"),
			Memo:         fmt.Sprintf("GFG取消下注退款，thirId：%s", successOrdersStr),
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
		}

		if err := tx.Table("x_amount_change_log").Create(&amountChangeLog).Error; err != nil {
			logs.Error("gfg单一钱包 CancelBet 创建金额变更日志错误 userId=", userId, " err=", err)
			return err
		}

		return nil
	})

	if err != nil {
		logs.Error("gfg单一钱包 CancelBet 事务处理失败 userId=", userId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "系统错误",
		})
		return
	}

	// 查询最新余额
	userBalance := thirdGameModel.UserBalance{}
	if err := server.Db().GormDao().Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error; err != nil {
		logs.Error("gfg单一钱包 CancelBet 查询最新用户余额错误 userId=", userId, " err=", err)
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data":      gin.H{"Data": 0},
			"msg":       "",
		})
		return
	}

	// 异步刷新用户余额缓存
	go func() {
		// 删除缓存，防止重复请求
		server.Redis().Del(cacheKey)

		// 刷新用户余额
		if l.RefreshUserAmountFunc != nil {
			if err := l.RefreshUserAmountFunc(userId); err != nil {
				logs.Error("gfg单一钱包 CancelBet 刷新用户余额错误 userId=", userId, " err=", err)
			}
		}
	}()

	// 返回成功响应
	ctx.Gin().JSON(200, gin.H{
		"code":      0,
		"timestamp": time.Now().Unix(),
		"data":      gin.H{"Balance": float64(int(userBalance.Amount*100)) / 100},
		"msg":       "",
	})
}

// 1-200 才需要处理lockMoney unlockMoney
// 返回金额是 实际扣掉的玩家的钱也就是lockMoney,派奖时根据lockMoney 能知晓扣掉的金额
// win 此局 净输赢 比如下注金额100，赢钱200，win=100
// api/Balance/LockBalance
func (l *GFGConfig) Bet(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg单一钱包 Bet bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfg单一钱包 Bet 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	// logs.Info("gfg单一钱包 Bet authorization=", authorization)
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg单一钱包 Bet 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}
	type betReq struct {
		AccountId    string  `json:"accountId"`
		Money        float64 `json:"money"`
		GameId       int     `json:"gameId"`
		OrderId      string  `json:"orderId"`
		MinLockMoney float64 `json:"minLockMoney"`
		Timestamp    int64   `json:"timestamp"`
	}
	requests := betReq{}
	err = json.Unmarshal(bodyBytes, &requests)
	if err != nil {
		logs.Error("gfg单一钱包 Bet json解析错误 bodyBytes=", string(bodyBytes), " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "非法参数",
		})
		return
	}

	// 获取用户Id，下划线后面的部分
	userId := l.getUserIdByAccountId(requests.AccountId)
	if userId <= 0 {
		logs.Error("gfg单一钱包 无效的用户Id", requests.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	userBalance := thirdGameModel.UserBalance{}
	err = server.Db().GormDao().Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error
	if err != nil {
		logs.Error("gfg单一钱包 Bet 查询用户余额错误 userId=", userId, " thirdId=", requests.OrderId, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询用户余额错误",
		})
		return
	}
	if userBalance.Amount < 0 {
		logs.Error("gfg单一钱包 Bet 用户余额为负数 userId=", userId, " thirdId=", requests.OrderId, " userBalance=", userBalance)
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足,负余额",
		})
		return
	}
	balance2 := float64(int(userBalance.Amount*100)) / 100

	//三方来源的数据整理
	var (
		betAmount1   = requests.Money
		betAmountMin = requests.MinLockMoney
		thirdId      = requests.OrderId
		gamecode     = fmt.Sprintf("%d", requests.GameId)
		thirdTime    = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05") // time.UnixMilli(requests.Timestamp)
	)

	if betAmountMin > balance2 {
		ctx.Gin().JSON(200, gin.H{
			"code":      9004,
			"timestamp": time.Now().Unix(),
			"msg":       "用户余额不足",
		})
		return
	}
	var betAmount float64 = 0
	if betAmount1 > balance2 {
		//实际冻结额度，如果可用余额大于"申请冻结额"，则返回"申请冻结额"，如果可用余额小于"申请冻结额"大于"最低冻结金额"，则返回所有余额，否则失败
		betAmount = balance2
	} else {
		betAmount = betAmount1
	}
	gameList := GameListGfg{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		logs.Error("gfg单一钱包 Bet 查询游戏错误 userId=", userId, " gamecode=", gamecode, " requests=", requests, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询游戏错误",
		})
		return
	}
	gameName := gameList.Name
	gameType := gameList.GameType

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, gamecode); err != nil {
		logs.Error("gfg_single Bet 权限检查错误 userId=", userId, " gameId=", gamecode, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "玩家余额被冻结",
			"timestamp": time.Now().Unix(),
		})
		return
	} else if !allowed {
		logs.Error("gfg_single Bet 权限被拒绝 userId=", userId, " gameId=", gamecode, " hint=", hint)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "玩家余额被冻结",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	var tablepre string
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
	} else if gameType == 5 {
		//如果下注金额为0
		if requests.Money <= 0 && requests.MinLockMoney <= 0 {
			// 返回成功响应
			ctx.Gin().JSON(200, gin.H{
				"code":      0,
				"timestamp": time.Now().Unix(),
				"data": gin.H{
					"money":     betAmount,
					"accountId": requests.AccountId,
					"balance":   balance2,
				},
				"msg": "",
			})
			return
		}
		// 真人游戏调用专门的处理方法
		// 将响应数据转换为结构体
		requestsbet := betGfgReq{
			AccountId: requests.AccountId,
			Money:     requests.Money,
			GameId:    requests.GameId,
			OrderId:   requests.OrderId,
			LockMoney: requests.MinLockMoney,
			Timestamp: requests.Timestamp,
		}
		l.LiveResult(ctx, requestsbet, userId, gameList)
		return
	} else {
		logs.Error("gfg单一钱包 Bet gameType错误  GameId=", gamecode, " gameType=", gameType, " thirdId=", thirdId)
		ctx.Gin().JSON(200, gin.H{
			"code":      702,
			"timestamp": time.Now().Unix(),
			"msg":       "游戏ID对应的类型错误",
		})
		return
	}

	// 获取下注渠道
	//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
	//获取投注渠道
	ChannelId := base.GetUserChannelId(ctx, &userBalance)
	// 开启事务
	server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderGfg{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e == nil {
			//已经存在订单
			logs.Error("gfg单一钱包 Bet 订单已存在 thirdId=", thirdId, " betTran=", betTran)
			ctx.Gin().JSON(200, gin.H{
				"code":      2,
				"timestamp": time.Now().Unix(),
				"msg":       "注单已存在",
			})
			return nil
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("gfg单一钱包 Bet 查询错误 thirdId=", thirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询注单错误",
			})
			return e
		}

		// 获取用户对应的配置
		userConfig := l.getConfigByAccountId(requests.AccountId)

		order := ThirdOrderGfg{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       gamecode,
			GameName:     gameName,
			BetAmount:    betAmount,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     userConfig.Currency,
			RawData:      string(bodyBytes),
			State:        1,
			DataState:    -1,
			CreateTime:   thirdTime,
			BetCtxType:   1,
		}
		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("gfg单一钱包 Bet 创建注单错误 thirdId=", thirdId, " order=", order, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      9013,
				"timestamp": time.Now().Unix(),
				"msg":       "创建注单失败",
			})
			return e
		}

		//Money=0 或者Money>0两种情况 money=0就不走预扣 money>0 就要预扣款
		if requests.Money > 0 {
			//更新用户余额
			resultTmp := tx.Table("x_user").Where("UserId=? and Amount>= ?", userId, betAmount).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount-?", betAmount),
			})
			if resultTmp.Error != nil {
				logs.Error("gfg单一钱包 Bet  更新用户余额失败 userId=", userId, " thirdId=", thirdId, " e=", resultTmp.Error)
				ctx.Gin().JSON(200, gin.H{
					"code":      9013,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return resultTmp.Error
			}
			if resultTmp.RowsAffected <= 0 {
				d := errors.New("更新条数0")
				logs.Error("gfg单一钱包 Bet 更新用户余额失败，更新条数0 userId=", userId, " thirdId=", thirdId)
				ctx.Gin().JSON(200, gin.H{
					"code":      9013,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return d
			}

			//操作成功
			afterBalance := balance2 - betAmount
			//创建账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userId,
				BeforeAmount: balance2,
				Amount:       0 - betAmount,
				AfterAmount:  afterBalance,
				Reason:       utils.BalanceCReasonGFGDeduction,
				Memo:         l.brandName + " 预扣款 bet,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfg单一钱包 Bet  创建账变失败 userId=", userId, " thirdId=", thirdId, " e=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      9013,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}
		}

		logs.Info("gfg单一钱包 Bet 下注成功 thirdId=", thirdId, " order=", order)
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"money":     betAmount,
				"accountId": requests.AccountId,
				"balance":   balance2,
			},
			"msg": "",
		})
		return nil
	})
	// 推送下注事件通知
	if l.thirdGamePush != nil {
		// 获取用户对应的配置
		userConfig := l.getRouteConfig(userId)
		logs.Info("gfg单一钱包 Bet 下注成功 推送下注事件", gameName, " betAmount=", betAmount)
		l.thirdGamePush.PushBetEvent(userId, gameName, l.brandName, betAmount, userConfig.Currency, l.brandName, thirdId, gameType)
	}
	// gfg下一局开始的时候更新余额
	// 发送余额变动通知
	go func(notifyUserId int) {
		rKey := cacheKeyGFG + "ntyAmount:" + strconv.Itoa(userId)
		tmpErr := server.Redis().Del(rKey)
		if tmpErr != nil {
			logs.Error("gfg单一钱包 Bet redis 删除余额通知锁 userId=", userId, " tmpErr=", tmpErr.Error())
		}
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][GFGConfig] Bet 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		} else {
			logs.Error("[ERROR][GFGConfig] Bet 发送余额变动通知失败,RefreshUserAmountFunc is nil")
		}
	}(userId)
}

type betGfgReq struct {
	OrderId     string  `json:"orderId"`
	LockMoney   float64 `json:"lockMoney"`
	Money       float64 `json:"money"` // 跟win参数重复 可以不使用
	AccountId   string  `json:"accountId"`
	GameId      int     `json:"gameId"`
	RoundId     string  `json:"roundId"`
	MainRoundId string  `json:"mainRoundId"`
	Timestamp   int64   `json:"timestamp"`
	ValidBet    float64 `json:"validBet"`
	Win         float64 `json:"win"`
	Bet         float64 `json:"bet"`
	Fee         float64 `json:"fee"`
}

func (l *GFGConfig) Result(ctx *abugo.AbuHttpContent) {
	bodyBytes, err := io.ReadAll(ctx.Gin().Request.Body)
	logs.Info("gfg单一钱包 Result bodyBytes=", string(bodyBytes))
	if err != nil {
		logs.Error("gfg单一钱包 Result 读取消息体错误 err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"msg":       "非法参数",
			"timestamp": time.Now().Unix(),
		})
		return
	}
	authorization := ctx.Gin().Request.Header.Get("Authorization")
	//   logs.Info("gfg单一钱包 Result authorization=", authorization)
	if !l.check(authorization, string(bodyBytes)) {
		logs.Error("gfg单一钱包 Result 签名校验错误 authorization=", authorization, " bodyBytes=", string(bodyBytes))
		ctx.Gin().JSON(200, gin.H{
			"code":      701,
			"timestamp": time.Now().Unix(),
			"msg":       "无效签名",
		})
		return
	}

	requests := betGfgReq{}
	err = json.Unmarshal(bodyBytes, &requests)
	if err != nil {
		logs.Error("gfg单一钱包 Result json解析错误 bodyBytes=", string(bodyBytes), " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "非法参数",
		})
		return
	}

	// 获取用户Id，下划线后面的部分
	userId := l.getUserIdByAccountId(requests.AccountId)
	if userId <= 0 {
		logs.Error("gfg单一钱包 无效的用户Id", requests.AccountId)
		ctx.Gin().JSON(200, gin.H{
			"code":      1018,
			"msg":       "无效的用户Id",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// 参数校验 有效下注
	if requests.ValidBet < 0 || requests.Bet < 0 || requests.Fee < 0 {
		// 参数不正确 返回8019结算异常
		logs.Error("gfg单一钱包 Result 参数不正确 userId=", userId, " thirdId=", requests.OrderId, " requests=", requests)
		ctx.Gin().JSON(200, gin.H{
			"code":      8019,
			"timestamp": time.Now().Unix(),
			"msg":       "参数错误,下注金额或者有效下注或者费率为负数",
		})
		return
	}

	//三方来源的数据整理
	var (
		winAmount       = requests.Bet + requests.Win // + requests.Fee 费率用户出 Christine Hsieh 确定的
		thirdId         = requests.OrderId
		thirdIdRealLook = requests.RoundId
		gamecode        = fmt.Sprintf("%d", requests.GameId)
		thirdTime       = time.Now().In(tzUTC8).Format("2006-01-02 15:04:05") // time.UnixMilli(requests.Timestamp)
		validBet        = requests.ValidBet
	)
	if requests.ValidBet > requests.Bet {
		validBet = requests.Bet
	}

	gameList := GameListGfg{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, gamecode).First(&gameList).Error
	if err != nil {
		logs.Error("gfg单一钱包 Bet 查询游戏错误 userId=", userId, " gamecode=", gamecode, " requests=", requests, " err=", err.Error())
		ctx.Gin().JSON(200, gin.H{
			"code":      503,
			"timestamp": time.Now().Unix(),
			"msg":       "查询游戏错误",
		})
		return
	}
	gameType := gameList.GameType
	var tablepre string
	var table string
	var betType int
	var settleType int
	if gameType == 1 {
		tablepre = "x_third_dianzhi_pre_order"
		table = "x_third_dianzhi"
		betType = utils.BalanceCReasonGFGBetDianZi
		settleType = utils.BalanceCReasonGFGSettleDianZi
		// if requests.ValidBet != 0 && requests.Win == 0 { // 只有电子不输不赢的时候流水取下注金额
		// 	validBet = requests.Bet
		// }
		// 所有电子的有效流水取不大于下注金额的输赢绝对值
		validBet = math.Abs(requests.Win + requests.Fee)
		if validBet > math.Abs(requests.Bet) {
			validBet = math.Abs(requests.Bet)
		}
	} else if gameType == 2 {
		tablepre = "x_third_qipai_pre_order"
		table = "x_third_qipai"
		betType = utils.BalanceCReasonGFGBet
		settleType = utils.BalanceCReasonGFGSettle
		// if requests.Win == 0 { // 只有棋牌不输不赢的时候流水取0
		// 	validBet = 0
		// }
		// 所有棋牌的有效流水取不大于下注金额的输赢绝对值
		validBet = math.Abs(requests.Win + requests.Fee)
		if validBet > math.Abs(requests.Bet) {
			validBet = math.Abs(requests.Bet)
		}
	} else if gameType == 5 {
		// 真人游戏调用专门的处理方法
		l.LiveResult(ctx, requests, userId, gameList)
		return
	} else {
		logs.Error("gfg单一钱包 Result gameType错误 userId=", userId, " GameId=", gamecode, " gameType=", gameType, "thirdId=", thirdId)
		ctx.Gin().JSON(200, gin.H{
			"code":      702,
			"timestamp": time.Now().Unix(),
			"msg":       "游戏ID对应的类型错误",
		})
		return
	}

	// 开启事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		betTran := ThirdOrderGfg{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			logs.Error("gfg单一钱包 Result 查询注单错误 thirdId=", thirdId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				ctx.Gin().JSON(200, gin.H{
					"code":      3,
					"timestamp": time.Now().Unix(),
					"msg":       "注单不存在",
				})
			} else {
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "查询注单错误",
				})
			}
			return e
		}
		if betTran.DataState != -1 {
			logs.Error("gfg单一钱包 Result 注单状态错误 thirdId=", thirdId, " betTran=", betTran)
			ctx.Gin().JSON(200, gin.H{
				"code":      3,
				"timestamp": time.Now().Unix(),
				"msg":       "注单不是未开奖状态",
			})
			return errors.New("注单不是未开奖状态")
		}

		//获取用户余额
		userBalance := UserBalanceGfg{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("gfg单一钱包 Result 查询用户余额错误 userId=", userId, " thirdId=", thirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额错误",
			})
		}

		// 处理返还金额-----start 开始返还预扣款-------------------------------------
		//		//LockMoney=0 或者LockMoney>0两种情况 LockMoney=0就不需要返还预扣款 LockMoney>0 就要返还预扣款
		returnAmount := requests.LockMoney
		if returnAmount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount +  ?", returnAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				logs.Error("gfg单一钱包 Result 返还用户余额 更新用户余额错误 userId=", userId, " thirdId=", thirdId, " userBalance=", userBalance.Amount, "requests=", requests, " betTran=", betTran)
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("gfg单一钱包 Result 更新用户余额错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}

			// 创建返还账变
			afterAmount := userBalance.Amount + returnAmount
			amountLog := AmountChangeLogGfg{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       returnAmount,
				AfterAmount:  afterAmount,
				Reason:       utils.BalanceCReasonGFGReturn,
				CreateTime:   thirdTime, //注单的账变创建时间使用实时时间
				Memo:         l.brandName + " 返还预扣款,thirdId:" + thirdIdRealLook,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfg单一钱包 Result 创建账变记录错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " amountLog=", amountLog, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "创建账变记录错误",
				})
				return e
			}
			userBalance.Amount = afterAmount
		}
		// -------------end--------结束返还预扣款-------------------------------
		balance2 := float64(int(userBalance.Amount*100)) / 100

		if requests.RoundId == "" {
			if requests.Bet == 0 && requests.Win == 0 && requests.Fee == 0 && requests.ValidBet == 0 {
				// RoundId空表示无效订单 下一局的unlockBalance消息
				e = tx.Table(tablepre).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Delete(&betTran).Error
				if e != nil {
					logs.Error("gfg单一钱包 Result 删除无效订单错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
					ctx.Gin().JSON(200, gin.H{
						"code":      503,
						"timestamp": time.Now().Unix(),
						"msg":       "删除无效订单失败",
					})
					return e
				} else {
					ctx.Gin().JSON(200, gin.H{
						"code":      0,
						"timestamp": time.Now().Unix(),
						"data": gin.H{
							"Data": balance2,
						},
						"msg": "",
					})
					return nil
				}
			} else {
				thirdIdRealLook = requests.OrderId
			}
		}

		updataData := map[string]interface{}{
			"BetAmount": requests.Bet,
			"WinAmount": winAmount + requests.Fee,
			"ValidBet":  validBet,
			"RawData":   string(bodyBytes),
			"GameId":    gamecode,
			"ThirdId":   thirdIdRealLook,
			"Fee":       requests.Fee,
			"DataState": 1,
		}
		if winAmount+requests.Fee != 0 {
			updataData["ThirdTime"] = thirdTime
		}
		resultTmp := tx.Table(tablepre).Where("Id=?", betTran.Id).Updates(updataData)
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("gfg单一钱包 Result 更新结算信息错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "更新结算信息失败",
			})
			return e
		}

		e = tx.Table(tablepre).Where("ThirdId=? and Brand=?", thirdIdRealLook, l.brandName).First(&betTran).Error
		if e != nil {
			logs.Error("gfg单一钱包 Result 查询注单错误 thirdId=", thirdIdRealLook, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询注单错误",
			})
			return e
		}

		// 处理下注
		if requests.Bet > 0 {

			// 下注扣钱
			resultTmp = tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", requests.Bet),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				logs.Error("gfg单一钱包 Result 更新用户余额错误 userId=", userId, " thirdId=", thirdId, " userBalance=", userBalance.Amount, "requests=", requests, " betTran=", betTran)
				e = errors.New("更新条数0")
			}

			if e != nil {
				logs.Error("gfg单一钱包 Result 更新用户余额错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}

			// 创建下注账变
			amountLog := AmountChangeLogGfg{
				UserId:       userId,
				BeforeAmount: userBalance.Amount,
				Amount:       0 - requests.Bet,
				AfterAmount:  userBalance.Amount - requests.Bet,
				Reason:       betType,
				CreateTime:   thirdTime, //注单的账变创建时间使用实时时间
				Memo:         l.brandName + " bet,thirdId:" + thirdIdRealLook,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfg单一钱包 Result 创建账变记录错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " amountLog=", amountLog, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "创建账变记录错误",
				})
				return e
			}
		}

		// 处理结算
		if winAmount != 0 {
			resultTmp = tx.Table("x_user").Where("UserId=?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("gfg单一钱包 Result 更新用户余额错误 userId=", userId, " thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " winAmount=", winAmount, " betTran=", betTran, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "更新用户余额失败",
				})
				return e
			}
		}
		if requests.Bet != 0 || winAmount != 0 {
			amountLog := AmountChangeLogGfg{
				UserId:       userId,
				BeforeAmount: userBalance.Amount - requests.Bet,
				Amount:       winAmount,
				AfterAmount:  userBalance.Amount - requests.Bet + winAmount,
				Reason:       settleType,
				CreateTime:   thirdTime,
				Memo:         l.brandName + " settle,thirdId:" + thirdIdRealLook,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("gfg单一钱包 Result 创建账变记录错误 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran, " amountLog=", amountLog, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "创建账变记录错误",
				})
				return e
			}
		}

		betTran.Id = 0
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			logs.Error("gfg单一钱包 Result 移动至统计表错误 thirdId=", thirdIdRealLook, " betTran=", betTran, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "移动至统计表错误",
			})
			return e
		}

		logs.Info("gfg单一钱包 Result 结算成功 thirdId=", thirdId, " thirdIdRealLook=", thirdIdRealLook, " betTran=", betTran)
		retBalance, _ := decimal.NewFromFloat(balance2).Sub(decimal.NewFromFloat(requests.Bet)).Add(decimal.NewFromFloat(winAmount)).Float64()
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data": gin.H{
				"Data": retBalance, //balance2 - requests.Bet + winAmount,
			},
			"msg": "",
		})
		return nil
	})
	if winAmount != 0 {
		// 推送派奖事件通知
		if l.thirdGamePush != nil {
			//l.thirdGamePush.PushRewardEvent(userId, betTran.GameName, l.brandName, requests.ValidBet, winAmount, l.currency)
			//1-dianzi电子 2-qipai棋牌 3-quwei小游戏 4-lottery彩票 5-live真人 6-sport体育 7-texas德州
			l.thirdGamePush.PushRewardEvent(gameType, l.brandName, thirdIdRealLook)
		}
	}
	if err == nil {
		// 发送余额变动通知
		go func(notifyUserId int) {
			//if requests.Win != 0 {
			//	rKey := cacheKeyGFG + "ntyAmount:" + strconv.Itoa(notifyUserId)
			//	tmpErr := server.Redis().SetNxString(rKey, time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"), 59)
			//	if tmpErr != nil {
			//		return
			//	}
			//	time.Sleep(1 * time.Minute) // gfg延时一分钟更新余额
			//	tmpErr = server.Redis().SetNxString(rKey, time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"), 59)
			//	if tmpErr != nil {
			//		return
			//	}
			//}

			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][GFGConfig] Result 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][GFGConfig] Result 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(userId)
	}
}

// LiveResult 真人游戏专用的Result处理方法
// 真人游戏下注和派奖是两个独立的注单
func (l *GFGConfig) LiveResult(ctx *abugo.AbuHttpContent, requests betGfgReq, userId int, gameList GameListGfg) {
	// 根据roundId判断是下注还是派奖
	if strings.HasPrefix(requests.RoundId, "wcDeduct:") {
		// 下注处理
		l.LiveBetResult(ctx, requests, userId, gameList)
	} else if strings.HasPrefix(requests.RoundId, "wcDeposit:") {
		// 派奖处理
		l.LiveSettleResult(ctx, requests, userId, gameList)
	} else if strings.HasPrefix(requests.RoundId, "wcTip:") {
		// 打赏处理
		l.LiveGift(ctx, requests, userId, gameList)
	} else {
		logs.Error("gfg单一钱包 LiveResult roundId格式错误 userId=", userId, " roundId=", requests.RoundId, " requests=", requests)
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "roundId格式错误",
		})
	}
}

// LiveGift 真人游戏打赏处理
func (l *GFGConfig) LiveGift(ctx *abugo.AbuHttpContent, requests betGfgReq, userId int, gameList GameListGfg) {
	logs.Info("gfg单一钱包 LiveGift 真人打赏 userId=", userId, " requests=", requests)

	// 真人游戏打赏的参数验证
	if requests.Bet <= 0 {
		logs.Error("gfg单一钱包 LiveGift 打赏金额必须大于0 userId=", userId, " bet=", requests.Bet)
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "打赏金额必须大于0",
		})
		return
	}

	// 设置真人游戏相关参数
	tablepre := "x_third_live_pre_order"
	table := "x_third_live"
	gamecode := fmt.Sprintf("%d", requests.GameId)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betThirdId := requests.RoundId // 打赏订单号：wcDeduct:1055392
	// 注单详情
	rawData, _ := json.Marshal(requests)
	// 开启事务处理打赏
	err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 检查订单是否已存在
		betTran := ThirdOrderGfg{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", betThirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e == nil {
			// 订单已存在，返回成功
			logs.Info("gfg单一钱包 LiveGift 订单已存在 betThirdId=", betThirdId)
			userBalance := UserBalanceGfg{}
			if err := tx.Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error; err == nil {
				balance := float64(int(userBalance.Amount*100)) / 100
				ctx.Gin().JSON(200, gin.H{
					"code":      0,
					"timestamp": time.Now().Unix(),
					"data":      gin.H{"Data": balance},
					"msg":       "",
				})
			}
			return nil
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("gfg单一钱包 LiveGift 查询订单错误 betThirdId=", betThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询订单错误",
			})
			return e
		}

		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveGift 查询用户余额错误 userId=", userId, " betThirdId=", betThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额错误",
			})
			return e
		}

		// 检查余额是否足够
		if userBalance.Amount < requests.Bet {
			logs.Error("gfg单一钱包 LiveGift 用户余额不足 userId=", userId, " balance=", userBalance.Amount, " bet=", requests.Bet)
			ctx.Gin().JSON(200, gin.H{
				"code":      9004,
				"timestamp": time.Now().Unix(),
				"msg":       "用户余额不足",
			})
			return errors.New("用户余额不足")
		}

		// 获取下注渠道
		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)

		// 创建打赏订单
		order := ThirdOrderGfg{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      betThirdId,
			GameId:       gamecode,
			GameName:     "打赏",
			BetAmount:    requests.Bet,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.getRouteConfig(userId).Currency,
			BetCtx:       string(rawData),
			RawData:      string(rawData),
			State:        1,
			DataState:    1,
			CreateTime:   thirdTime,
			BetCtxType:   1,
		}

		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveGift 创建订单错误 betThirdId=", betThirdId, " order=", order, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "创建订单错误",
			})
			return e
		}

		// 扣除用户余额
		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, requests.Bet).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", requests.Bet),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("gfg单一钱包 LiveGift 扣除余额失败 userId=", userId, " betThirdId=", betThirdId, " bet=", requests.Bet, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "扣除余额失败",
			})
			return e
		}

		// 创建打赏账变记录
		amountLog := AmountChangeLogGfg{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       0 - requests.Bet,
			AfterAmount:  userBalance.Amount - requests.Bet,
			Reason:       utils.BalanceCReasonLiveTip,
			CreateTime:   thirdTime,
			Memo:         l.brandName + " 真人打赏,betThirdId:" + betThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveGift 创建账变记录错误 betThirdId=", betThirdId, " amountLog=", amountLog, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "创建账变记录错误",
			})
			return e
		}

		// 复制到正式表
		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveGift 移动至统计表错误 betThirdId=", betThirdId, " order=", order, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "移动至统计表错误",
			})
			return e
		}

		// 返回成功响应
		balance := float64(int((userBalance.Amount-requests.Bet)*100)) / 100
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data":      gin.H{"Data": balance},
			"msg":       "",
		})

		logs.Info("gfg单一钱包  LiveGift 打赏成功 betThirdId=", betThirdId, " userId=", userId, " bet=", requests.Bet, " balance=", balance)
		return nil
	})

	if err != nil {
		logs.Error("gfg单一钱包 LiveGift 事务处理失败 err=", err)
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][GFGConfig] LiveGift 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		}
	}(userId)
}

// LiveBetResult 真人游戏下注处 理
func (l *GFGConfig) LiveBetResult(ctx *abugo.AbuHttpContent, requests betGfgReq, userId int, gameList GameListGfg) {
	logs.Info("gfg单一钱包 LiveBetResult 真人下注 userId=", userId, " requests=", requests)

	// 真人游戏下注的参数验证
	if requests.Bet <= 0 {
		logs.Error("gfg单一钱包 LiveBetResult 下注金额必须大于0 userId=", userId, " bet=", requests.Bet)
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "下注金额必须大于0",
		})
		return
	}

	// 设置真人游戏相关参数
	tablepre := "x_third_live_pre_order"
	gamecode := fmt.Sprintf("%d", requests.GameId)
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	betThirdId := requests.RoundId // 下注订单号：wcDeduct:1055392
	// 注单详情
	rawData, _ := json.Marshal(requests)
	// 开启事务处理下注
	err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 检查订单是否已存在
		betTran := ThirdOrderGfg{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", betThirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e == nil {
			// 订单已存在，返回成功
			logs.Info("gfg单一钱包 LiveBetResult 订单已存在 betThirdId=", betThirdId)
			userBalance := UserBalanceGfg{}
			if err := tx.Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error; err == nil {
				balance := float64(int(userBalance.Amount*100)) / 100
				ctx.Gin().JSON(200, gin.H{
					"code":      0,
					"timestamp": time.Now().Unix(),
					"data":      gin.H{"Data": balance},
					"msg":       "",
				})
			}
			return nil
		}
		if e != daogorm.ErrRecordNotFound {
			logs.Error("gfg单一钱包 LiveBetResult 查询订单错误 betThirdId=", betThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询订单错误",
			})
			return e
		}

		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveBetResult 查询用户余额错误 userId=", userId, " betThirdId=", betThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额错误",
			})
			return e
		}

		// 检查余额是否足够
		if userBalance.Amount < requests.Bet {
			logs.Error("gfg单一钱包 LiveBetResult 用户余额不足 userId=", userId, " balance=", userBalance.Amount, " bet=", requests.Bet)
			ctx.Gin().JSON(200, gin.H{
				"code":      9004,
				"timestamp": time.Now().Unix(),
				"msg":       "用户余额不足",
			})
			return errors.New("用户余额不足")
		}

		// 获取下注渠道
		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(user.Token).Host)
		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		// 创建下注订单
		order := ThirdOrderGfg{
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userId,
			Brand:        l.brandName,
			ThirdId:      betThirdId,
			GameId:       gamecode,
			GameName:     gameList.Name,
			BetAmount:    requests.Bet,
			WinAmount:    0,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.getRouteConfig(userId).Currency,
			BetCtx:       string(rawData),
			RawData:      string(rawData),
			State:        1,
			DataState:    -1,
			CreateTime:   thirdTime,
			BetCtxType:   1,
		}

		e = tx.Table(tablepre).Create(&order).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveBetResult 创建订单错误 betThirdId=", betThirdId, " order=", order, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "创建订单错误",
			})
			return e
		}

		// 扣除用户余额
		resultTmp := tx.Table("x_user").Where("UserId = ? AND Amount >= ?", userId, requests.Bet).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", requests.Bet),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("gfg单一钱包 LiveBetResult 扣除余额失败 userId=", userId, " betThirdId=", betThirdId, " bet=", requests.Bet, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "扣除余额失败",
			})
			return e
		}

		// 创建下注账变记录
		amountLog := AmountChangeLogGfg{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       0 - requests.Bet,
			AfterAmount:  userBalance.Amount - requests.Bet,
			Reason:       utils.BalanceCReasonLiveBet,
			CreateTime:   thirdTime,
			Memo:         l.brandName + " 真人下注,betThirdId:" + betThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveBetResult 创建账变记录错误 betThirdId=", betThirdId, " amountLog=", amountLog, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "创建账变记录错误",
			})
			return e
		}
		// 返回成功响应
		balance := float64(int((userBalance.Amount-requests.Bet)*100)) / 100
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data":      gin.H{"Data": balance},
			"msg":       "",
		})

		logs.Info("gfg单一钱包 LiveBetResult 下注成功 betThirdId=", betThirdId, " userId=", userId, " bet=", requests.Bet, " balance=", balance)
		return nil
	})

	if err != nil {
		logs.Error("gfg单一钱包 LiveBetResult 事务处理失败 err=", err)
		return
	}

	// 发送余额变动通知
	go func(notifyUserId int) {
		if l.RefreshUserAmountFunc != nil {
			tmpErr := l.RefreshUserAmountFunc(notifyUserId)
			if tmpErr != nil {
				logs.Error("[ERROR][GFGConfig] LiveBetResult 发送余额变动通知错误", notifyUserId, tmpErr.Error())
			}
		}
	}(userId)
}

// LiveSettleResult 真人游戏派奖处理
func (l *GFGConfig) LiveSettleResult(ctx *abugo.AbuHttpContent, requests betGfgReq, userId int, gameList GameListGfg) {
	logs.Info("gfg单一钱包 LiveSettleResult 真人派奖 userId=", userId, " requests=", requests)

	// 真人游戏派奖的参数验证
	if requests.Win < 0 {
		logs.Error("gfg单一钱包 LiveSettleResult 派奖金额不能为负数 userId=", userId, " win=", requests.Win)
		ctx.Gin().JSON(200, gin.H{
			"code":      10,
			"timestamp": time.Now().Unix(),
			"msg":       "派奖金额不能为负数",
		})
		return
	}

	// 设置真人游戏相关参数
	tablepre := "x_third_live_pre_order"
	table := "x_third_live"
	thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
	settleThirdId := requests.RoundId // 派彩订单号：wcDeposit:1055392
	winAmount := requests.Win

	// 从派彩订单号中提取游戏局号，构建下注订单号
	// 派彩订单号格式：wcDeposit:1055392，下注订单号格式：wcDeduct:1055392
	gameRoundId := strings.TrimPrefix(settleThirdId, "wcDeposit:")
	betThirdId := "wcDeduct:" + gameRoundId

	logs.Info("gfg单一钱包 LiveSettleResult 派彩订单处理 settleThirdId=", settleThirdId, " betThirdId=", betThirdId, " gameRoundId=", gameRoundId)

	// 开启事务处理派奖
	err := server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		// 查询对应的下注订单
		betTran := ThirdOrderGfg{}
		e := tx.Table(tablepre).Where("ThirdId=? and Brand=?", betThirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&betTran).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveSettleResult 查询下注订单错误 betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				ctx.Gin().JSON(200, gin.H{
					"code":      3,
					"timestamp": time.Now().Unix(),
					"msg":       "对应的下注订单不存在",
				})
			} else {
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "查询下注订单错误",
				})
			}
			return e
		}

		// 检查订单是否已结算
		if betTran.DataState != -1 {
			logs.Error("gfg单一钱包 LiveSettleResult 订单已结算 betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " betTran=", betTran)
			userBalance := UserBalanceGfg{}
			if err := tx.Table("x_user").Where("UserId = ?", userId).First(&userBalance).Error; err == nil {
				balance := float64(int(userBalance.Amount*100)) / 100
				ctx.Gin().JSON(200, gin.H{
					"code":      0,
					"timestamp": time.Now().Unix(),
					"data":      gin.H{"Data": balance},
					"msg":       "",
				})
			}
			return nil
		}

		// 获取用户余额
		userBalance := UserBalanceGfg{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveSettleResult 查询用户余额错误 userId=", userId, " betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "查询用户余额错误",
			})
			return e
		}

		// 计算有效流水（真人游戏的有效流水取不大于下注金额的输赢绝对值）
		validBet := math.Abs(winAmount)
		if winAmount == 0 {
			validBet = betTran.BetAmount
		}
		if validBet > math.Abs(betTran.BetAmount) {
			validBet = math.Abs(betTran.BetAmount)
		}
		// 注单详情
		rawData, _ := json.Marshal(requests)
		// 更新注单状态（派奖）
		e = tx.Table(tablepre).Where("Id = ?", betTran.Id).Updates(map[string]interface{}{
			"DataState":  1, // 已结算状态
			"ThirdTime":  thirdTime,
			"ValidBet":   validBet,
			"WinAmount":  winAmount,
			"BetCtx":     string(rawData),
			"GameRst":    string(rawData),
			"BetCtxType": 3, // 派奖类型
			"RawData":    string(rawData),
		}).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveSettleResult 更新订单状态失败 userId=", userId, " betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "更新订单状态失败",
			})
			return e
		}

		// 更新本地订单对象
		betTran.DataState = 1
		betTran.ThirdTime = thirdTime
		betTran.ValidBet = validBet
		betTran.WinAmount = winAmount
		betTran.GameRst = string(rawData)
		betTran.BetCtxType = 3
		betTran.RawData = string(rawData)

		// 如果有派奖金额，增加用户余额
		if winAmount > 0 {
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", winAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 {
				e = errors.New("更新条数0")
			}
			if e != nil {
				logs.Error("gfg单一钱包 LiveSettleResult 增加余额失败 userId=", userId, " betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " winAmount=", winAmount, " err=", e.Error())
				ctx.Gin().JSON(200, gin.H{
					"code":      503,
					"timestamp": time.Now().Unix(),
					"msg":       "增加余额失败",
				})
				return e
			}
		}

		// 创建派奖账变记录
		amountLog := AmountChangeLogGfg{
			UserId:       userId,
			BeforeAmount: userBalance.Amount,
			Amount:       winAmount,
			AfterAmount:  userBalance.Amount + winAmount,
			Reason:       utils.BalanceCReasonLiveSettle,
			CreateTime:   thirdTime,
			Memo:         l.brandName + " 真人派奖,betThirdId:" + betThirdId + ",settleThirdId:" + settleThirdId,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveSettleResult 创建账变记录错误 betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " amountLog=", amountLog, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "创建账变记录错误",
			})
			return e
		}

		// 复制到正式表
		betTran.Id = 0
		e = tx.Table(table).Create(&betTran).Error
		if e != nil {
			logs.Error("gfg单一钱包 LiveSettleResult 移动至统计表错误 betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " betTran=", betTran, " err=", e.Error())
			ctx.Gin().JSON(200, gin.H{
				"code":      503,
				"timestamp": time.Now().Unix(),
				"msg":       "移动至统计表错误",
			})
			return e
		}

		// 返回成功响应
		balance := float64(int((userBalance.Amount+winAmount)*100)) / 100
		ctx.Gin().JSON(200, gin.H{
			"code":      0,
			"timestamp": time.Now().Unix(),
			"data":      gin.H{"Data": balance},
			"msg":       "",
		})

		logs.Info("gfg单一钱包 LiveSettleResult 派奖成功 betThirdId=", betThirdId, " settleThirdId=", settleThirdId, " userId=", userId, " winAmount=", winAmount, " balance=", balance)
		return nil
	})

	if err != nil {
		logs.Error("gfg单一钱包 LiveSettleResult 事务处理失败 err=", err)
		return
	}

	if err == nil {
		// 推送派奖事件通知
		if winAmount > 0 && l.thirdGamePush != nil {
			l.thirdGamePush.PushRewardEvent(5, l.brandName, betThirdId) // gameType=5 真人游戏
		}

		// 记录成功响应日志
		logs.Info("gfg单一钱包 LiveSettleResult 响应成功 gameid=", settleThirdId, " betThirdId=", betThirdId, " userId=", userId, " winAmount=", winAmount)

		// 发送余额变动通知
		go func(notifyUserId int) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(notifyUserId)
				if tmpErr != nil {
					logs.Error("[ERROR][GFGConfig] LiveSettleResult 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			}
		}(userId)
	}
}
