package sms

import (
	"fmt"
	"math/rand"
	"strconv"
	"xserver/abugo"
	"xserver/server"

	"github.com/beego/beego/logs"
	"github.com/gin-gonic/gin"
)

type SmsController struct {
}

func (c *SmsController) Init() {
	// 强制重新初始化SMS服务
	InitSmsService()

	// 注册SMS相关接口
	server.Http().PostNoAuth("/api/sms/send_single", c.send_single_sms)
	// 添加验证码验证接口
	server.Http().PostNoAuth("/api/sms/verify_code", c.verify_code_api)

}

// send_single_sms 发送单条短信
func (c *SmsController) send_single_sms(ctx *abugo.AbuHttpContent) {
	errcode := 0

	reqdata := struct {
		Mobile string `json:"mobile" validate:"required"`
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		logs.Error("请求参数解析失败: %v", err)
		return
	}

	// 生成4位验证码
	verifyCode := c.GenerateVerifyCode()

	// 生成短信内容
	content := fmt.Sprintf("[gogoro]您的验证码是【%s】，10分钟内有效。", verifyCode)

	smsService := GetSmsService()

	if !smsService.IsEnabled() {
		logs.Error("SMS服务未启用，请检查配置文件中的 new_sms.enabled 设置")
		ctx.RespErrString(true, &errcode, "SMS服务未启用")
		return
	}

	// 发送短信前检查余额
	balanceResult, err := smsService.GetBalance()
	if err != nil {
		logs.Error("获取SMS余额失败: %v", err)
		ctx.RespErrString(true, &errcode, "获取余额失败，无法发送短信")
		return
	}

	if balanceResult.Code != 0 {
		logs.Error("获取SMS余额返回错误: code=%d, msg=%s", balanceResult.Code, balanceResult.Msg)
		ctx.RespErrString(true, &errcode, "获取余额失败，无法发送短信")
		return
	}

	// 检查余额是否足够（余额小于10时不允许发送）
	balance, err := strconv.ParseFloat(balanceResult.Data.Balance, 64)
	if err != nil {
		logs.Error("解析SMS余额失败: balance=%s, error=%v", balanceResult.Data.Balance, err)
		ctx.RespErrString(true, &errcode, "余额格式错误，无法发送短信")
		return
	}

	if balance < 10 {
		logs.Error("SMS余额不足: balance=%.2f, 最低需要10", balance)
		ctx.RespErrString(true, &errcode, "SMS余额不足，无法发送短信")
		return
	}

	// 发送短信
	result, err := smsService.SendSingleSms(reqdata.Mobile, content)
	if err != nil {
		logs.Error("发送单条短信失败:", err)
		ctx.RespErrString(true, &errcode, "发送失败")
		return
	}

	if result.Code != 0 {
		logs.Error("短信发送返回错误:", result.Code, result.Msg)
		ctx.RespErrString(true, &errcode, result.Msg)
		return
	}

	// 短信发送成功后，直接保存生成的验证码到Redis
	logs.Info("准备保存验证码到Redis: mobile=%s, code=%s", reqdata.Mobile, verifyCode)
	c.saveVerifyCodeToRedis(reqdata.Mobile, verifyCode)

	// 验证保存是否成功
	redisKey := fmt.Sprintf("sms_verify_code:%s", reqdata.Mobile)
	savedCodeBytes, err := server.XRedis().Get(redisKey)
	if err != nil {
		logs.Error("验证保存失败：无法从Redis获取刚保存的验证码: mobile=%s, error=%v", reqdata.Mobile, err)
	} else if savedCodeBytes == nil {
		logs.Error("验证保存失败：Redis中未找到刚保存的验证码: mobile=%s", reqdata.Mobile)
	} else {
		savedCode := string(savedCodeBytes)
		logs.Info("验证保存成功：Redis中的验证码: mobile=%s, saved=%s, original=%s", reqdata.Mobile, savedCode, verifyCode)
	}

	// 构造响应数据
	responseData := gin.H{
		"msg_id": result.GetMsgIdString(),
		"msg":    result.Msg,
	}

	// 打印最终响应
	logs.Info("最终响应数据: %+v", responseData)
	logs.Info("=== SMS发送请求结束 ===")

	ctx.RespOK(responseData)
}

// saveVerifyCodeToRedis 保存验证码到Redis
func (c *SmsController) saveVerifyCodeToRedis(mobile, verifyCode string) {
	// 生成Redis key
	redisKey := fmt.Sprintf("sms_verify_code:%s", mobile)

	// 保存到Redis，过期时间10分钟（600秒）
	err := server.XRedis().Set(redisKey, verifyCode, 600)
	if err != nil {
		logs.Error("保存验证码到Redis失败: mobile=%s, code=%s, error=%v", mobile, verifyCode, err)
		return
	}

	logs.Info("验证码已保存到Redis: mobile=%s, code=%s, key=%s", mobile, verifyCode, redisKey)
}

// VerifyCode 验证验证码
func (c *SmsController) VerifyCode(mobile, inputCode string) bool {
	// 生成Redis key
	redisKey := fmt.Sprintf("sms_verify_code:%s", mobile)

	// 从Redis获取验证码
	storedCodeBytes, err := server.XRedis().Get(redisKey)
	if err != nil {
		logs.Error("从Redis获取验证码失败: mobile=%s, error=%v", mobile, err)
		return false
	}

	if storedCodeBytes == nil {
		logs.Info("验证码不存在或已过期: mobile=%s", mobile)
		return false
	}

	storedCode := string(storedCodeBytes)

	// 验证码比较
	if storedCode == inputCode {
		// 验证成功，删除Redis中的验证码
		err := server.XRedis().Del(redisKey)
		if err != nil {
			logs.Error("删除Redis验证码失败: mobile=%s, error=%v", mobile, err)
		} else {
			logs.Info("验证码验证成功并已删除: mobile=%s, code=%s", mobile, inputCode)
		}
		return true
	}

	logs.Info("验证码验证失败: mobile=%s, input=%s, stored=%s", mobile, inputCode, storedCode)
	return false
}

// verify_code_api 验证验证码API接口
func (c *SmsController) verify_code_api(ctx *abugo.AbuHttpContent) {
	errcode := 0
	reqdata := struct {
		Mobile string `json:"mobile" validate:"required"`
		Code   string `json:"verify_code" validate:"required"`
	}{}

	err := ctx.RequestData(&reqdata)
	if ctx.RespErr(err, &errcode) {
		logs.Error("验证码验证请求参数解析失败: %v", err)
		return
	}

	// 验证验证码
	isValid := c.VerifyCode(reqdata.Mobile, reqdata.Code)

	if isValid {
		ctx.RespOK(gin.H{
			"valid": true,
			"msg":   "验证码验证成功",
		})
	} else {
		ctx.RespErrString(true, &errcode, "验证码错误或已过期")
	}
}

// GenerateVerifyCode 生成4位验证码
func (c *SmsController) GenerateVerifyCode() string {
	// 生成1000-9999之间的4位随机数字验证码
	code := rand.Intn(9000) + 1000
	return strconv.Itoa(code)
}
