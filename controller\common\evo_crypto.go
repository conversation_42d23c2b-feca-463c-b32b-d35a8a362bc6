package common

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"io"

	"github.com/beego/beego/logs"
)

// EVO_CRYPTO_KEY EVO加密密钥，恰好32字节用于AES-256
const EVO_CRYPTO_KEY = "EVO_TOKEN_ENCRYPT_KEY_32_BYTES!1"

// EvoCrypto EVO加密工具结构体
type EvoCrypto struct {
	key []byte
}

// NewEvoCrypto 创建新的EVO加密工具实例
func NewEvoCrypto() *EvoCrypto {
	return &EvoCrypto{
		key: []byte(EVO_CRYPTO_KEY),
	}
}

// EncryptToken 加密EVO token
// 使用AES-256-GCM加密，返回base64编码的加密字符串
func (ec *EvoCrypto) EncryptToken(plainToken string) (string, error) {
	if plainToken == "" {
		return "", errors.New("token不能为空")
	}

	// 创建AES加密器
	block, err := aes.NewCipher(ec.key)
	if err != nil {
		logs.Error("EVO加密 创建AES加密器失败:", err)
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		logs.Error("EVO加密 创建GCM模式失败:", err)
		return "", err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		logs.Error("EVO加密 生成nonce失败:", err)
		return "", err
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(plainToken), nil)

	// 返回base64编码的结果
	encoded := base64.StdEncoding.EncodeToString(ciphertext)
	logs.Info("EVO加密成功，原文长度:", len(plainToken), "加密后长度:", len(encoded))

	return encoded, nil
}

// DecryptToken 解密EVO token
// 解析base64编码的加密字符串，返回原始token
func (ec *EvoCrypto) DecryptToken(encryptedToken string) (string, error) {
	if encryptedToken == "" {
		return "", errors.New("加密token不能为空")
	}

	// 解码base64
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedToken)
	if err != nil {
		logs.Error("EVO解密 base64解码失败:", err)
		return "", err
	}

	// 创建AES解密器
	block, err := aes.NewCipher(ec.key)
	if err != nil {
		logs.Error("EVO解密 创建AES解密器失败:", err)
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		logs.Error("EVO解密 创建GCM模式失败:", err)
		return "", err
	}

	// 检查长度
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", errors.New("加密token格式错误")
	}

	// 分离nonce和密文
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// 解密
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		logs.Error("EVO解密 解密失败:", err)
		return "", err
	}

	result := string(plaintext)
	logs.Info("EVO解密成功，解密后长度:", len(result))

	return result, nil
}

// EncryptTokenCmd 命令行工具函数 - 用于手动加密token
// 这个函数可以在申请EVO商户号时手动调用
func EncryptTokenCmd(plainToken string) {
	if plainToken == "" {
		fmt.Println("错误: token不能为空")
		fmt.Println("用法: EncryptTokenCmd(\"your_plain_token\")")
		return
	}

	crypto := NewEvoCrypto()
	encrypted, err := crypto.EncryptToken(plainToken)
	if err != nil {
		fmt.Printf("加密失败: %v\n", err)
		return
	}

	fmt.Println("===== EVO Token 加密工具 =====")
	fmt.Printf("原始Token: %s\n", plainToken)
	fmt.Printf("加密Token: %s\n", encrypted)
	fmt.Println("=============================")
	fmt.Println("请将加密后的Token保存到配置中")

	// 验证加密解密是否正确
	decrypted, err := crypto.DecryptToken(encrypted)
	if err != nil {
		fmt.Printf("验证失败: %v\n", err)
		return
	}

	if decrypted == plainToken {
		fmt.Println("✅ 加密解密验证成功!")
	} else {
		fmt.Println("❌ 加密解密验证失败!")
	}
}

// DecryptTokenCmd 命令行工具函数 - 用于测试解密
func DecryptTokenCmd(encryptedToken string) {
	if encryptedToken == "" {
		fmt.Println("错误: 加密token不能为空")
		fmt.Println("用法: DecryptTokenCmd(\"your_encrypted_token\")")
		return
	}

	crypto := NewEvoCrypto()
	decrypted, err := crypto.DecryptToken(encryptedToken)
	if err != nil {
		fmt.Printf("解密失败: %v\n", err)
		return
	}

	fmt.Println("===== EVO Token 解密工具 =====")
	fmt.Printf("加密Token: %s\n", encryptedToken)
	fmt.Printf("解密Token: %s\n", decrypted)
	fmt.Println("=============================")
}

// 全局实例，方便调用
var GlobalEvoCrypto = NewEvoCrypto()

// 便捷函数
func EncryptEvoToken(plainToken string) (string, error) {
	return GlobalEvoCrypto.EncryptToken(plainToken)
}

func DecryptEvoToken(encryptedToken string) (string, error) {
	return GlobalEvoCrypto.DecryptToken(encryptedToken)
}
