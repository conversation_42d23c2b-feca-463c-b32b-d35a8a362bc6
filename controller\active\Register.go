package active

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/msg"
	"xserver/gormgen/xHashGame/model"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/golang-module/carbon/v2"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// 注册送礼基础配置
type RegisterGiftBaseConfig struct {
	RequireDuringRegistration int32    `json:"RequireDuringRegistration"` // 是否要求在注册时参与，0=否，1=是
	MaxIPAttempts             int32    `json:"MaxIPAttempts"`             // 同一IP最大领取次数，0表示不限制，大于0表示限制
	BlockedIPList             string   `json:"BlockedIPList"`             // 黑名单IP列表，多个IP用逗号分隔
	ApplicableGames           int32    `json:"ApplicableGames"`           // 适用游戏范围，0表示全站通用，1表示仅限指定游戏
	RewardAmount              float32  `json:"RewardAmount"`              // 奖励金额
	RewardWalletType          int32    `json:"RewardWalletType"`          // 奖励账户类型，0表示真金账户，1表示彩金账户
	UnlockType                int32    `json:"UnlockType"`                // 解锁类型，0=无要求，1=充值
	UnlockAmount              int32    `json:"UnlockAmount"`              // 解锁金额当UnlockType是1时需要
	CanPlayAfterBonus         int32    `json:"CanPlayAfterBonus"`         // 领取奖励后是否立即可玩游戏，0=否，1=是
	WagerRequirement          int32    `json:"WagerRequirement"`          // 提取奖励需打流水要求，0=倍数，1=金额
	WagerMultiple             []int32  `json:"WagerMultiple"`             // 提取奖励需打流水倍数[3,8] 3倍充值流水和8倍奖励流水
	WagerAmount               int32    `json:"WagerAmount"`               // 提取奖励需打固定金额
	RewardAmountType          int32    `json:"RewardAmountType"`          // 奖励金额类型，当WagerRequirement=0时使用：0=奖励金额，1=充值金额，2=充值与奖励金额
	RechargeTurnover          int32    `json:"RechargeTurnover"`          // 提现所需的充值流水
	RewardTurnover            int32    `json:"RewardTurnover"`            // 提现所需的奖励流水
	RechargeRewardTurnover    [2]int32 `json:"RechargeRewardTurnover"`    // 提现所需的充值与奖励流水
}

// 处理注册赠送活动
func ProcessRegisterGift(userId int32, ip string) error {
	userTb := server.DaoxHashGame().XUser
	userDb := server.DaoxHashGame().XUser.WithContext(context.Background())

	user, err := userDb.Where(userTb.UserID.Eq(userId)).First()
	if err != nil {
		logs.Error("ProcessRegisterGift 获取用户信息错误: %v", err)
		return err
	}

	if user.IsTest == 1 {
		return fmt.Errorf("测试账号不能参与活动")
	}

	// 查询注册送礼活动配置
	activeDefine, err := GetRegisterGiftActive(int(user.SellerID), int(user.ChannelID))
	if err != nil {
		logs.Error("获取注册赠送活动配置失败: %v", err)
		return err
	}

	// 如果活动不存在或状态为关闭，直接返回
	if activeDefine == nil || activeDefine.State != utils.ActiveStateOpen {
		logs.Info("注册赠送活动未开启或不存在")
		return fmt.Errorf("注册赠送活动未开启或不存在")
	}

	// 判断活动是否在有效期内
	now := time.Now().UnixMilli()
	if (activeDefine.EffectStartTime > 0 && now < activeDefine.EffectStartTime) ||
		(activeDefine.EffectEndTime > 0 && now > activeDefine.EffectEndTime) {
		return fmt.Errorf("注册赠送活动不在有效期内")
	}

	// 解析活动基础配置
	var config RegisterGiftBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &config)
	if err != nil {
		return fmt.Errorf("解析活动配置失败: %v", err)
	}

	// 判断是否需要在注册时参与
	if config.RequireDuringRegistration != 1 {
		return fmt.Errorf("该活动不需要在注册时参与")
	}

	// 检查IP黑名单
	if config.BlockedIPList != "" {
		blockedIPs := strings.Split(config.BlockedIPList, ",")
		for _, blockedIP := range blockedIPs {
			if blockedIP == ip {
				return fmt.Errorf("当前IP在活动黑名单中")
			}
		}
	}

	// 检查IP领取次数限制
	if config.MaxIPAttempts > 0 {
		count, err := GetIPRegisterGiftCount(ip)
		if err != nil {
			return fmt.Errorf("检查IP领取次数失败: %v", err)
		}

		if count >= int(config.MaxIPAttempts) {
			return fmt.Errorf("当前IP已达到最大领取次数")
		}
	}

	// 赠送奖励
	rewardAmount := float64(config.RewardAmount)
	// 配置中的解锁金额（用于解锁条件判断）
	configUnlockAmount := float64(config.UnlockAmount)
	withdrawLiuSuiAdd := 0.0

	// 获取用户实际充值金额（用于流水计算）
	actualRechargeAmount := 0.0
	if config.WagerRequirement == 0 && (config.RewardAmountType == 1 || config.RewardAmountType == 2) {
		// 只有在需要基于充值金额计算流水时才查询
		rechargeTb := server.DaoxHashGame().XRecharge
		rechargeDb := rechargeTb.WithContext(context.Background())

		recharges, err := rechargeDb.
			Where(rechargeTb.UserID.Eq(userId)).
			Where(rechargeTb.State.Eq(5)). // 充值成功的状态
			Find()

		if err != nil {
			logs.Error("ProcessRegisterGift 查询用户充值记录失败: %v", err)
			// 如果查询失败，使用配置中的解锁金额作为备用
			actualRechargeAmount = configUnlockAmount
		} else {
			// 计算总充值金额
			for _, recharge := range recharges {
				actualRechargeAmount += recharge.RealAmount
			}
			logs.Info("ProcessRegisterGift 用户 %d 实际充值金额: %.2f", userId, actualRechargeAmount)
		}
	}

	// 计算流水要求
	if config.WagerRequirement == 0 { // 倍数
		if config.RewardAmountType == 0 { // 仅奖励金额
			withdrawLiuSuiAdd = rewardAmount * float64(config.WagerMultiple[0])
		} else if config.RewardAmountType == 1 { // 仅充值金额
			withdrawLiuSuiAdd = actualRechargeAmount * float64(config.WagerMultiple[0])
		} else if config.RewardAmountType == 2 { // 充值与奖励金额
			rechargeLiuSui := actualRechargeAmount * float64(config.WagerMultiple[0]) // 计算提现所需充值流水
			rewardLiuSui := rewardAmount * float64(config.WagerMultiple[1])           // 计算提现所需奖励流水
			withdrawLiuSuiAdd = rechargeLiuSui + rewardLiuSui                         // 提现所需流水为充值流水和奖励流水之和
			logs.Info("ProcessRegisterGift 用户 %d 流水计算: 充值流水=%.2f(%.2f*%d), 奖励流水=%.2f(%.2f*%d), 总流水=%.2f",
				userId, rechargeLiuSui, actualRechargeAmount, config.WagerMultiple[0],
				rewardLiuSui, rewardAmount, config.WagerMultiple[1], withdrawLiuSuiAdd)
		}
	} else if config.WagerRequirement == 1 { // 固定金额
		withdrawLiuSuiAdd = float64(config.WagerAmount)
	}

	// 使用SaveActiveData保存活动数据并发放奖励
	// 构建活动数据保存信息
	configStr, _ := json.Marshal(map[string]interface{}{})
	baseConfigStr, _ := json.Marshal(config)
	minLiuShuiDecimal := decimal.NewFromFloat(withdrawLiuSuiAdd)

	saveActiveDataInfo := SaveActiveDataInfo{
		AuditType:         int(activeDefine.AuditType), // 审核类型
		SellerID:          user.SellerID,               // 运营商ID
		ChannelID:         user.ChannelID,              // 渠道ID
		ActiveId:          int(utils.RegisterGift),     // 活动ID
		Level:             1,                           // 活动等级
		RealAmount:        rewardAmount,                // 实际奖励金额
		TotalLiushui:      0.0,                         // 总流水（注册活动不需要历史流水）
		WithdrawLiuSuiAdd: withdrawLiuSuiAdd,           // 提现流水增加值
		ActiveName:        activeDefine.Title,          // 活动名称
		ActiveMemo:        "注册赠送",                      // 活动备注
		BalanceCReason:    utils.RegisterGift,          // 余额变更原因
		ConfigStr:         configStr,                   // 配置字符串
		BastConfigStr:     baseConfigStr,               // 基础配置字符串
		FirstRecharge:     0.0,                         // 首充金额（注册活动不涉及充值）
		TotalRecharge:     0.0,                         // 总充值金额
		MinLiuShui:        minLiuShuiDecimal,           // 最小流水要求
		UserIP:            ip,                          // 用户IP
		DeviceID:          "",                          // 设备ID（注册时可能没有）
	}

	// 保存活动数据并发放奖励
	err = SaveActiveData(nil, user, saveActiveDataInfo)
	if err != nil {
		return fmt.Errorf("保存注册赠送活动数据失败: %v", err)
	}

	// 更新用户的UsdtGiftStatus为2（已领取状态）
	if _, err := userDb.WithContext(context.Background()).Where(userTb.UserID.Eq(user.UserID)).Update(userTb.UsdtGiftStatus, 2); err != nil {
		return fmt.Errorf("更新用户领取状态失败: %v", err)
	}

	// 创建消息发送服务
	messageService := msg.NewSendMessageAPIService()
	// 触发发送消息
	err = messageService.SendActivityMessage(int(user.UserID), rewardAmount, nil, int(activeDefine.ID))
	if err != nil {
		logs.Error("发送消息失败: ", " userId=", user.UserID, " err=", err)
	}
	return nil
}

// 获取注册赠送活动配置
func GetRegisterGiftActive(sellerId, channelId int) (*model.XActiveDefine, error) {
	gdb := server.Db().Gorm()

	// 查询活动配置
	var activeDefine model.XActiveDefine
	err := gdb.Table("x_active_define").
		Where("ActiveId = ?", utils.RegisterGift).
		Where("SellerId = ?", sellerId).
		Where("ChannelId = ?", channelId).
		Where("State = ?", utils.ActiveStateOpen).
		First(&activeDefine).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &activeDefine, nil
}

// 获取同一IP注册赠送次数
func GetIPRegisterGiftCount(ip string) (int, error) {
	// 查询活动审核表，统计同IP领取次数
	var count int
	query := `
		SELECT COUNT(*) FROM x_active_reward_audit
		WHERE ActiveId = ? AND UserIP = ?
	`

	err := server.Db().Gorm().Raw(query, utils.RegisterGift, ip).Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}

// 检查用户是否满足注册赠送活动的解锁条件
func CheckRegisterGiftUnlock(user *model.XUser) (bool, error) {
	// 获取当前活动配置
	activeDefine, err := GetRegisterGiftActive(int(user.SellerID), int(user.ChannelID))
	if err != nil {
		logs.Error("获取注册赠送活动配置失败: %v", err)
		return false, err
	}

	// 如果活动不存在或未开启
	if activeDefine == nil {
		return false, nil
	}

	var config RegisterGiftBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &config)
	if err != nil {
		logs.Error("解析活动配置失败: %v", err)
		return false, fmt.Errorf("解析活动配置失败: %v", err)
	}

	// 查询用户是否参与了注册赠送活动
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	_, err = activeRewardAuditTb.WithContext(context.Background()).
		Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
		Where(activeRewardAuditTb.ActiveID.Eq(int32(utils.RegisterGift))).
		Where(activeRewardAuditTb.AuditState.In(utils.ActiveAwardAuditStateAutoPass, utils.ActiveAwardAuditStatePass, utils.ActiveAwardAuditStateWait)).
		First()

	// 用户是否参与了活动
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("查询活动记录失败: %v", err)
		}
	}

	// 如果活动不需要解锁，直接返回true
	if config.UnlockType == 0 {
		return true, nil
	}

	// 查询用户充值记录并计算总充值金额
	rechargeTb := server.DaoxHashGame().XRecharge
	rechargeDb := rechargeTb.WithContext(context.Background())

	var totalRechargeAmount float64
	recharges, err := rechargeDb.
		Where(rechargeTb.UserID.Eq(user.UserID)).
		Where(rechargeTb.State.Eq(5)). // 充值成功的状态
		Find()

	if err != nil {
		logs.Error("查询用户 %d 充值记录失败: %v", user.UserID, err)
		return false, fmt.Errorf("查询充值记录失败: %v", err)
	}

	// 计算总充值金额
	for _, recharge := range recharges {
		totalRechargeAmount += recharge.RealAmount
	}

	// 检查是否满足解锁条件
	if config.UnlockType == 0 {
		return true, nil
	} else if config.UnlockType == 1 {
		return totalRechargeAmount >= float64(config.UnlockAmount), nil
	} else {
		return false, fmt.Errorf("不支持的解锁类型: %d", config.UnlockType)
	}
}

// 检查注册赠送活动状态 isRegisterBonusReceived 是否已领取奖励(true:已领取，false:未领取)，RegisterGiftUnlocked 是否已解锁(true:解锁，false:未解锁)
func CheckRegisterGiftStatus(user *model.XUser, result map[string]interface{}) (isRegisterBonusReceived bool, RegisterGiftUnlocked bool) {
	activeDefine, errCheck := GetRegisterGiftActive(int(user.SellerID), int(user.ChannelID))
	// 活动不存在或已关闭，返回false（未领取奖励）和true（可以进入游戏，无弹窗）
	if errCheck != nil || activeDefine == nil {
		isRegisterBonusReceived = false
		RegisterGiftUnlocked = true
		logs.Info("用户 %d 注册赠送活动不存在或已关闭，可以进入游戏", user.UserID)
		return isRegisterBonusReceived, RegisterGiftUnlocked
	}

	// 首先查询用户是否参与过注册赠送活动
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	_, err := activeRewardAuditTb.WithContext(context.Background()).
		Where(activeRewardAuditTb.UserID.Eq(user.UserID)).
		Where(activeRewardAuditTb.ActiveID.Eq(int32(utils.RegisterGift))).
		First()

	// 如果用户没有参与过活动记录，说明是老用户，不受活动限制
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 老用户直接设为false（未领取奖励）和true（可以进入游戏，无弹窗）
		isRegisterBonusReceived = false
		RegisterGiftUnlocked = true
		// 老用户不添加活动配置到结果中，避免前端显示弹窗
		return isRegisterBonusReceived, RegisterGiftUnlocked
	}

	// 解析活动配置
	var config RegisterGiftBaseConfig
	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &config)
	if err != nil {
		logs.Error("解析注册赠送活动配置失败: %v", err)
		isRegisterBonusReceived = false
		RegisterGiftUnlocked = true
		return isRegisterBonusReceived, RegisterGiftUnlocked
	}

	// 添加活动配置到返回结果
	result["RegisterGiftConfig"] = config
	// 添加活动类型到返回结果
	result["GameType"] = activeDefine.GameType
	// 检查用户是否已领取注册奖金UsdtGiftStatus=2已领取
	isRegisterBonusReceived = user.UsdtGiftStatus == 2

	// 如果不需要解锁（UnlockType为0）
	if config.UnlockType == 0 {
		RegisterGiftUnlocked = true // 已领取奖励，可以进入游戏，不需要弹窗
		return isRegisterBonusReceived, RegisterGiftUnlocked
	}
	// 允许领取后立即游戏（CanPlayAfterBonus为1）
	if config.CanPlayAfterBonus == 1 {
		RegisterGiftUnlocked = true // 已领取奖励，可以进入游戏，不需要弹窗
		return isRegisterBonusReceived, RegisterGiftUnlocked
	}

	// 检查用户是否已经解锁注册赠送
	isUnlocked, unlockErr := CheckRegisterGiftUnlock(user)

	if unlockErr != nil {
		logs.Error("检查用户注册赠送解锁状态失败: %v", unlockErr)
		RegisterGiftUnlocked = false // 出错时不能进游戏，需要弹窗
		logs.Error("用户 %d 因错误设置为未解锁状态", user.UserID)
	}

	RegisterGiftUnlocked = isUnlocked
	return isRegisterBonusReceived, RegisterGiftUnlocked
}

// ActivityLiuSuiCheck 活动流水检查结果
type ActivityLiuSuiCheck struct {
	Pass    bool   // 是否通过检查
	Message string // 提示消息
}

// checkActivityLiuSui 检查用户是否参与活动以及活动流水是否达到要求
func CheckActivityLiuSui(ctx *abugo.AbuHttpContent, userId int) (ActivityLiuSuiCheck, error) {
	result := ActivityLiuSuiCheck{
		Pass:    true,
		Message: "",
	}
	host := ctx.Host()
	channelId, sellerId := server.GetChannel(ctx, host)
	// 首先检查注册活动是否开启
	activeDefineInfo, err := GetActiveDefine(int32(sellerId), int32(channelId), utils.RegisterGift)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return result, nil
		}
		return result, nil // 如果查询失败，也让用户通过，避免阻止提现
	}
	// 活动未开启，直接返回通过
	if activeDefineInfo.State != utils.ActiveStateOpen {
		return result, nil
	}

	// 只查询用户是否参与了用户注册活动(activeId = 10000)，并且不是被拒绝的记录
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	userActivity, err := activeRewardAuditTb.WithContext(ctx.Gin()).
		Where(activeRewardAuditTb.UserID.Eq(int32(userId))).
		Where(activeRewardAuditTb.ActiveID.Eq(utils.RegisterGift)).
		Where(activeRewardAuditTb.AuditState.Neq(utils.ActiveAwardAuditStateRefuse)). // 排除被拒绝的记录
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户未参与活动或所有记录都被拒绝，直接返回通过
			return result, nil
		}
		return result, nil // 如果查询失败，也让用户通过，避免阻止提现
	}

	// 记录日志，方便调试
	logs.Info("CheckActivityLiuSui 用户参与了活动: 用户ID=%v, 活动ID=10000, 活动记录ID=%v, 审核状态=%v",
		userId, userActivity.ID, userActivity.AuditState)

	// 检查用户流水是否达到要求
	requiredLiuSui := userActivity.LiuShui

	// 如果没有要求流水，直接通过
	if requiredLiuSui <= 0 {
		return result, nil
	}

	// 从活动配置中获取有效时间
	startTimeStr := ""
	endTimeStr := ""

	// 使用活动的开始和结束时间
	if activeDefineInfo.EffectStartTime > 0 {
		startTime := time.UnixMilli(activeDefineInfo.EffectStartTime)
		startTimeStr = startTime.Format("2006-01-02")
	} else {
		startTimeStr = "2020-01-01" // 默认开始时间
	}

	if activeDefineInfo.EffectEndTime > 0 {
		endTime := time.UnixMilli(activeDefineInfo.EffectEndTime)
		endTimeStr = endTime.Format("2006-01-02")
	} else {
		endTimeStr = carbon.Now().Format("Y-m-d") // 如果未设置结束时间，使用当前日期
	}

	// 定义Comprehensive结构体，用于接收存储过程返回的数据
	type Comprehensive struct {
		Type           string          `json:"type" gorm:"column:type"`
		TypeId         int             `json:"type_id" gorm:"column:type_id"`
		Amount         decimal.Decimal `json:"Amount" gorm:"column:Amount"`
		Fee            decimal.Decimal `json:"Fee" gorm:"column:Fee"`
		LiuSui         decimal.Decimal `json:"LiuSui" gorm:"column:LiuSui"`
		RewardAmount   decimal.Decimal `json:"RewardAmount" gorm:"column:RewardAmount"`
		TotalBetCount  int32           `json:"TotalBetCount" gorm:"column:TotalBetCount"`
		TotalBetUser   int32           `json:"TotalBetUser" gorm:"column:TotalBetUser"`
		WinCount       int32           `json:"WinCount" gorm:"column:WinCount"`
		ValidBetAmount decimal.Decimal `json:"ValidBetAmount" gorm:"column:ValidBetAmount"`
	}

	var resultData []Comprehensive

	// 调用存储过程
	err = server.Db().Gorm().Raw("CALL ReportManage_x_custom_dailly_GetList(?,?,?,?,?,?,?,?)",
		startTimeStr, // 开始日期
		endTimeStr,   // 结束日期
		"",           // SellerId (空字符串)
		"",           // ChannelId (空字符串)
		"",           // Symbol (空字符串)
		userId,       // 用户ID
		-1,           // SpecialAgent (-1表示所有)
		-1).          // TopAgentId (-1表示所有)
		Scan(&resultData).Error

	// 处理存储过程返回的结果
	if err != nil {
		// 如果查询失败，也让用户通过
		return result, nil
	}

	// 初始化流水总额
	currentLiuSui := 0.0

	// 如果有返回的数据，处理每一条记录
	if len(resultData) > 0 {
		for _, row := range resultData {
			// 只处理Amount字段
			if !row.Amount.IsZero() {
				amountFloat, _ := row.Amount.Float64()
				if amountFloat > 0 {
					currentLiuSui += amountFloat
				}
			}
		}
	}

	// 检查流水是否达标
	if currentLiuSui < float64(requiredLiuSui) {
		// 流水未达到要求
		result.Pass = false
		result.Message = fmt.Sprintf("您参与的注册活动流水未达标，所需流水: %.2f，当前流水: %.2f，无法提现", float64(requiredLiuSui), currentLiuSui)
	}

	return result, nil
}

// updateRegisterGiftWager 更新注册赠送活动的流水要求
// @param userId 用户ID
// @param rechargeAmount 新增充值金额（本次充值的金额）
// @return error 错误信息
func UpdateRegisterGiftWager(userId int, rechargeAmount float64) error {
	logs.Info("updateRegisterGiftWager 开始更新注册赠送活动流水要求，用户ID:", userId, "新增充值金额:", rechargeAmount)

	// 1. 检查用户是否有注册赠送活动记录，包含状态是 3 或者 4
	activeRewardAuditTb := server.DaoxHashGame().XActiveRewardAudit
	activeRewardAuditDb := activeRewardAuditTb.WithContext(context.Background())

	activeRewardData, err := activeRewardAuditDb.
		Where(activeRewardAuditTb.UserID.Eq(int32(userId))).
		Where(activeRewardAuditTb.ActiveID.Eq(int32(utils.RegisterGift))).
		Where(activeRewardAuditTb.AuditState.In(utils.ActiveAwardAuditStatePass, utils.ActiveAwardAuditStateAutoPass)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.Info("updateRegisterGiftWager 用户没有注册赠送活动记录，跳过流水更新，用户ID:", userId)
			return nil // 没有注册赠送记录，不需要更新
		}
		logs.Error("updateRegisterGiftWager 查询活动记录失败:", err, "用户ID:", userId)
		return fmt.Errorf("查询活动记录失败: %v", err)
	}

	logs.Info("updateRegisterGiftWager 找到注册赠送活动记录，用户ID:", userId)

	// 2. 获取注册赠送活动配置
	activeDefine, err := GetActiveDefine(activeRewardData.SellerID, activeRewardData.ChannelID, utils.RegisterGift)
	if err != nil {
		logs.Error("updateRegisterGiftWager 获取注册赠送活动配置失败:", err, "SellerId:", activeRewardData.SellerID, "ChannelId:", activeRewardData.ChannelID)
		return fmt.Errorf("获取注册赠送活动配置失败: %v", err)
	}

	// 3. 解析活动配置
	var config RegisterGiftBaseConfig

	err = json.Unmarshal([]byte(activeDefine.BaseConfig), &config)
	if err != nil {
		logs.Error("updateRegisterGiftWager 解析活动配置失败:", err, "Config:", activeDefine.Config)
		return fmt.Errorf("解析活动配置失败: %v", err)
	}

	// 5. 计算新的流水要求
	var newWithdrawLiuSui float64
	rewardAmount := float64(config.RewardAmount)

	if config.WagerRequirement == 0 { // 倍数计算
		if len(config.WagerMultiple) < 2 {
			logs.Error("updateRegisterGiftWager WagerMultiple配置不完整:", config.WagerMultiple)
			return fmt.Errorf("WagerMultiple配置不完整")
		}

		switch config.RewardAmountType {
		case 0: // 仅奖励金额
			newWithdrawLiuSui = rewardAmount * float64(config.WagerMultiple[0])
		case 1: // 仅充值金额
			newWithdrawLiuSui = rechargeAmount * float64(config.WagerMultiple[0])
		case 2: // 充值与奖励金额
			rechargeLiuSui := rechargeAmount * float64(config.WagerMultiple[0])
			rewardLiuSui := rewardAmount * float64(config.WagerMultiple[1])
			newWithdrawLiuSui = rechargeLiuSui + rewardLiuSui
		}
	} else if config.WagerRequirement == 1 { // 固定金额
		newWithdrawLiuSui = float64(config.WagerAmount)
	}

	// 6. 获取用户当前的WithdrawLiuSui
	userTb := server.DaoxHashGame().XUser
	userDb := userTb.WithContext(context.Background())

	userData, err := userDb.Where(userTb.UserID.Eq(int32(userId))).First()
	if err != nil {
		logs.Error("updateRegisterGiftWager 获取用户信息失败:", err)
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 7. 计算新的总流水要求
	currentWithdrawLiuSui := userData.WithdrawLiuSui
	newTotalWithdrawLiuSui := newWithdrawLiuSui

	logs.Info("updateRegisterGiftWager 流水更新计算:",
		"用户ID:", userId,
		"当前流水:", currentWithdrawLiuSui,
		"新计算流水:", newWithdrawLiuSui,
		"更新后流水:", newTotalWithdrawLiuSui)

	// 8. 更新用户的WithdrawLiuSui
	_, err = userDb.Where(userTb.UserID.Eq(int32(userId))).
		Update(userTb.WithdrawLiuSui, newTotalWithdrawLiuSui)

	if err != nil {
		logs.Error("updateRegisterGiftWager 更新用户流水失败:", err)
		return fmt.Errorf("更新用户流水失败: %v", err)
	}

	// 9. 更新活动记录中的MinLiuShui
	_, err = activeRewardAuditDb.
		Where(activeRewardAuditTb.UserID.Eq(int32(userId))).
		Where(activeRewardAuditTb.ActiveID.Eq(int32(utils.RegisterGift))).
		Update(activeRewardAuditTb.MinLiuShui, newTotalWithdrawLiuSui)

	if err != nil {
		logs.Error("updateRegisterGiftWager 更新活动记录流水失败:", err)
		return fmt.Errorf("更新活动记录流水失败: %v", err)
	}

	logs.Info("updateRegisterGiftWager 流水更新成功:",
		"用户ID:", userId,
		"新流水要求:", newTotalWithdrawLiuSui)

	return nil
}
