// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"xserver/gormgen/xHashGame/model"
)

func newXOrder(db *gorm.DB, opts ...gen.DOOption) xOrder {
	_xOrder := xOrder{}

	_xOrder.xOrderDo.UseDB(db, opts...)
	_xOrder.xOrderDo.UseModel(&model.XOrder{})

	tableName := _xOrder.xOrderDo.TableName()
	_xOrder.ALL = field.NewAsterisk(tableName)
	_xOrder.ID = field.NewInt32(tableName, "Id")
	_xOrder.SellerID = field.NewInt32(tableName, "SellerId")
	_xOrder.UserID = field.NewInt32(tableName, "UserId")
	_xOrder.GameID = field.NewInt32(tableName, "GameId")
	_xOrder.RoomLevel = field.NewInt32(tableName, "RoomLevel")
	_xOrder.ToAddress = field.NewString(tableName, "ToAddress")
	_xOrder.FromAddress = field.NewString(tableName, "FromAddress")
	_xOrder.TxID = field.NewString(tableName, "TxId")
	_xOrder.ChainType = field.NewInt32(tableName, "ChainType")
	_xOrder.BlockNum = field.NewInt32(tableName, "BlockNum")
	_xOrder.BlockHash = field.NewString(tableName, "BlockHash")
	_xOrder.Symbol = field.NewString(tableName, "Symbol")
	_xOrder.Amount = field.NewFloat64(tableName, "Amount")
	_xOrder.BonusAmount = field.NewFloat64(tableName, "BonusAmount")
	_xOrder.State = field.NewInt32(tableName, "State")
	_xOrder.Memo = field.NewString(tableName, "Memo")
	_xOrder.CreateTime = field.NewTime(tableName, "CreateTime")
	_xOrder.RewardOrder = field.NewString(tableName, "RewardOrder")
	_xOrder.RewardAmount = field.NewFloat64(tableName, "RewardAmount")
	_xOrder.BonusRewardAmount = field.NewFloat64(tableName, "BonusRewardAmount")
	_xOrder.RewardTxID = field.NewString(tableName, "RewardTxId")
	_xOrder.WinAmount = field.NewFloat64(tableName, "WinAmount")
	_xOrder.BetArea = field.NewString(tableName, "BetArea")
	_xOrder.OpenArea = field.NewString(tableName, "OpenArea")
	_xOrder.RewardType = field.NewInt32(tableName, "RewardType")
	_xOrder.RewardRate = field.NewFloat64(tableName, "RewardRate")
	_xOrder.DataState = field.NewInt32(tableName, "DataState")
	_xOrder.AuditAccount = field.NewString(tableName, "AuditAccount")
	_xOrder.AuditTime = field.NewTime(tableName, "AuditTime")
	_xOrder.GasFee = field.NewFloat64(tableName, "GasFee")
	_xOrder.IsWin = field.NewInt32(tableName, "IsWin")
	_xOrder.Fee = field.NewFloat64(tableName, "Fee")
	_xOrder.IsHe = field.NewInt32(tableName, "IsHe")
	_xOrder.Fined = field.NewInt32(tableName, "Fined")
	_xOrder.LiuSui = field.NewFloat64(tableName, "LiuSui")
	_xOrder.FirstLiuSui = field.NewFloat64(tableName, "FirstLiuSui")
	_xOrder.ValidBetAmount = field.NewFloat64(tableName, "ValidBetAmount")
	_xOrder.TopAgentID = field.NewInt32(tableName, "TopAgentId")
	_xOrder.FineAccount = field.NewString(tableName, "FineAccount")
	_xOrder.FineTime = field.NewTime(tableName, "FineTime")
	_xOrder.FineMemo = field.NewString(tableName, "FineMemo")
	_xOrder.HbcState = field.NewInt32(tableName, "HbcState")
	_xOrder.ChannelID = field.NewInt32(tableName, "ChannelId")
	_xOrder.BetChannelID = field.NewInt32(tableName, "BetChannelId")
	_xOrder.BlockTime = field.NewInt64(tableName, "BlockTime")
	_xOrder.Period = field.NewString(tableName, "Period")
	_xOrder.ReportState = field.NewInt32(tableName, "ReportState")
	_xOrder.BlockMaker = field.NewString(tableName, "BlockMaker")
	_xOrder.NextBlockHash = field.NewString(tableName, "NextBlockHash")
	_xOrder.RewardTime = field.NewTime(tableName, "RewardTime")
	_xOrder.ExceedLimit = field.NewInt32(tableName, "ExceedLimit")
	_xOrder.UserAmount = field.NewFloat64(tableName, "UserAmount")
	_xOrder.UserBonusAmount = field.NewFloat64(tableName, "UserBonusAmount")
	_xOrder.IsTest = field.NewInt32(tableName, "IsTest")
	_xOrder.IsPanda = field.NewInt32(tableName, "IsPanda")
	_xOrder.HeFeeRate = field.NewFloat64(tableName, "HeFeeRate")
	_xOrder.IsFanBei = field.NewInt32(tableName, "IsFanBei")
	_xOrder.CSGroup = field.NewString(tableName, "CSGroup")
	_xOrder.CSID = field.NewString(tableName, "CSId")
	_xOrder.SpecialAgent = field.NewInt32(tableName, "SpecialAgent")
	_xOrder.IsCommission = field.NewInt32(tableName, "IsCommission")
	_xOrder.UtType = field.NewInt32(tableName, "UtType")
	_xOrder.IP = field.NewString(tableName, "Ip")
	_xOrder.Lang = field.NewString(tableName, "Lang")
	_xOrder.BlackUserType = field.NewInt32(tableName, "BlackUserType")
	_xOrder.BlackUserID = field.NewInt32(tableName, "BlackUserId")
	_xOrder.RiskEarlyWarningFirst = field.NewInt32(tableName, "RiskEarlyWarningFirst")
	_xOrder.RandomHash = field.NewString(tableName, "RandomHash")
	_xOrder.RandomBlock = field.NewString(tableName, "RandomBlock")
	_xOrder.Random = field.NewInt32(tableName, "Random")
	_xOrder.Md5Hash = field.NewString(tableName, "Md5Hash")

	_xOrder.fillFieldMap()

	return _xOrder
}

type xOrder struct {
	xOrderDo xOrderDo

	ALL         field.Asterisk
	ID          field.Int32
	SellerID    field.Int32   // 运营商
	UserID      field.Int32   // 玩家id
	GameID      field.Int32   // 游戏id
	RoomLevel   field.Int32   // 房间等级
	ToAddress   field.String  // 游戏地址
	FromAddress field.String  // 玩家地址
	TxID        field.String  // 下注哈希
	ChainType   field.Int32   // 网链分类 1trc 2erc 3bsc
	BlockNum    field.Int32   // 下注块编号
	BlockHash   field.String  // 下注块哈希
	Symbol      field.String  // 币种
	Amount      field.Float64 // 下注金额
	BonusAmount field.Float64 // Bonus下注金额
	/*
		订单状态
		1.未找到房间信息
		2.未找到运营商
	*/
	State                 field.Int32
	Memo                  field.String  // 备忘录
	CreateTime            field.Time    // 订单创建时间
	RewardOrder           field.String  // 返奖订单号
	RewardAmount          field.Float64 // 返奖励金额
	BonusRewardAmount     field.Float64 // Bonus返奖励金额
	RewardTxID            field.String  // 返奖哈希
	WinAmount             field.Float64 // 中奖金额
	BetArea               field.String  // 下注区域
	OpenArea              field.String  // 开奖结果
	RewardType            field.Int32   // 赔率类型
	RewardRate            field.Float64 // 赔率
	DataState             field.Int32   // 数据状态
	AuditAccount          field.String  // 审核人
	AuditTime             field.Time    // 审核时间
	GasFee                field.Float64 // gas费
	IsWin                 field.Int32   // 是否中奖 1中奖 2不中奖
	Fee                   field.Float64 // 手续费
	IsHe                  field.Int32   // 是否和局
	Fined                 field.Int32   // 是否已扣除业绩 1已扣除 2 未扣除
	LiuSui                field.Float64 // 流水
	FirstLiuSui           field.Float64 // 扣减前有效投注
	ValidBetAmount        field.Float64 // 有效投注
	TopAgentID            field.Int32   // 顶级id
	FineAccount           field.String  // 扣除账号
	FineTime              field.Time    // 扣除时间
	FineMemo              field.String  // 扣除备注
	HbcState              field.Int32   // hbc状态
	ChannelID             field.Int32   // 渠道id
	BetChannelID          field.Int32   // 下注渠道Id
	BlockTime             field.Int64   // 区块时间
	Period                field.String  // 彩票期号
	ReportState           field.Int32   // tg机器人播报状态
	BlockMaker            field.String  // 出块者
	NextBlockHash         field.String  // 下一个区块区块哈希值
	RewardTime            field.Time    // 返奖时间
	ExceedLimit           field.Int32   // 是否超限制  1超过,2未超
	UserAmount            field.Float64 // 玩家该笔下注后余额
	UserBonusAmount       field.Float64 // 玩家该笔下注后Bonus余额
	IsTest                field.Int32   // 是否是测试账号的注单
	IsPanda               field.Int32   // 是否是量化用户
	HeFeeRate             field.Float64
	IsFanBei              field.Int32 // 是否翻倍 ”翻倍”在牛牛玩法中是指”牛牛”、”牛九”二种中奖情形；在庄闲玩法是指”和”的中奖情形
	CSGroup               field.String
	CSID                  field.String
	SpecialAgent          field.Int32
	IsCommission          field.Int32  // 是否计算佣金 1计算 2不计算
	UtType                field.Int32  // 是否ut下注 1-是
	IP                    field.String // 登录ip
	Lang                  field.String // 登录语言
	BlackUserType         field.Int32  // 用户类型 1用户 2代理 3渠道 4运营商
	BlackUserID           field.Int32  // 用户Id
	RiskEarlyWarningFirst field.Int32  // 风控预警首次
	RandomHash            field.String // 随机区块哈希
	RandomBlock           field.String // 随机区块
	Random                field.Int32  // 随机数
	Md5Hash               field.String // md5hash

	fieldMap map[string]field.Expr
}

func (x xOrder) Table(newTableName string) *xOrder {
	x.xOrderDo.UseTable(newTableName)
	return x.updateTableName(newTableName)
}

func (x xOrder) As(alias string) *xOrder {
	x.xOrderDo.DO = *(x.xOrderDo.As(alias).(*gen.DO))
	return x.updateTableName(alias)
}

func (x *xOrder) updateTableName(table string) *xOrder {
	x.ALL = field.NewAsterisk(table)
	x.ID = field.NewInt32(table, "Id")
	x.SellerID = field.NewInt32(table, "SellerId")
	x.UserID = field.NewInt32(table, "UserId")
	x.GameID = field.NewInt32(table, "GameId")
	x.RoomLevel = field.NewInt32(table, "RoomLevel")
	x.ToAddress = field.NewString(table, "ToAddress")
	x.FromAddress = field.NewString(table, "FromAddress")
	x.TxID = field.NewString(table, "TxId")
	x.ChainType = field.NewInt32(table, "ChainType")
	x.BlockNum = field.NewInt32(table, "BlockNum")
	x.BlockHash = field.NewString(table, "BlockHash")
	x.Symbol = field.NewString(table, "Symbol")
	x.Amount = field.NewFloat64(table, "Amount")
	x.BonusAmount = field.NewFloat64(table, "BonusAmount")
	x.State = field.NewInt32(table, "State")
	x.Memo = field.NewString(table, "Memo")
	x.CreateTime = field.NewTime(table, "CreateTime")
	x.RewardOrder = field.NewString(table, "RewardOrder")
	x.RewardAmount = field.NewFloat64(table, "RewardAmount")
	x.BonusRewardAmount = field.NewFloat64(table, "BonusRewardAmount")
	x.RewardTxID = field.NewString(table, "RewardTxId")
	x.WinAmount = field.NewFloat64(table, "WinAmount")
	x.BetArea = field.NewString(table, "BetArea")
	x.OpenArea = field.NewString(table, "OpenArea")
	x.RewardType = field.NewInt32(table, "RewardType")
	x.RewardRate = field.NewFloat64(table, "RewardRate")
	x.DataState = field.NewInt32(table, "DataState")
	x.AuditAccount = field.NewString(table, "AuditAccount")
	x.AuditTime = field.NewTime(table, "AuditTime")
	x.GasFee = field.NewFloat64(table, "GasFee")
	x.IsWin = field.NewInt32(table, "IsWin")
	x.Fee = field.NewFloat64(table, "Fee")
	x.IsHe = field.NewInt32(table, "IsHe")
	x.Fined = field.NewInt32(table, "Fined")
	x.LiuSui = field.NewFloat64(table, "LiuSui")
	x.FirstLiuSui = field.NewFloat64(table, "FirstLiuSui")
	x.ValidBetAmount = field.NewFloat64(table, "ValidBetAmount")
	x.TopAgentID = field.NewInt32(table, "TopAgentId")
	x.FineAccount = field.NewString(table, "FineAccount")
	x.FineTime = field.NewTime(table, "FineTime")
	x.FineMemo = field.NewString(table, "FineMemo")
	x.HbcState = field.NewInt32(table, "HbcState")
	x.ChannelID = field.NewInt32(table, "ChannelId")
	x.BetChannelID = field.NewInt32(table, "BetChannelId")
	x.BlockTime = field.NewInt64(table, "BlockTime")
	x.Period = field.NewString(table, "Period")
	x.ReportState = field.NewInt32(table, "ReportState")
	x.BlockMaker = field.NewString(table, "BlockMaker")
	x.NextBlockHash = field.NewString(table, "NextBlockHash")
	x.RewardTime = field.NewTime(table, "RewardTime")
	x.ExceedLimit = field.NewInt32(table, "ExceedLimit")
	x.UserAmount = field.NewFloat64(table, "UserAmount")
	x.UserBonusAmount = field.NewFloat64(table, "UserBonusAmount")
	x.IsTest = field.NewInt32(table, "IsTest")
	x.IsPanda = field.NewInt32(table, "IsPanda")
	x.HeFeeRate = field.NewFloat64(table, "HeFeeRate")
	x.IsFanBei = field.NewInt32(table, "IsFanBei")
	x.CSGroup = field.NewString(table, "CSGroup")
	x.CSID = field.NewString(table, "CSId")
	x.SpecialAgent = field.NewInt32(table, "SpecialAgent")
	x.IsCommission = field.NewInt32(table, "IsCommission")
	x.UtType = field.NewInt32(table, "UtType")
	x.IP = field.NewString(table, "Ip")
	x.Lang = field.NewString(table, "Lang")
	x.BlackUserType = field.NewInt32(table, "BlackUserType")
	x.BlackUserID = field.NewInt32(table, "BlackUserId")
	x.RiskEarlyWarningFirst = field.NewInt32(table, "RiskEarlyWarningFirst")
	x.RandomHash = field.NewString(table, "RandomHash")
	x.RandomBlock = field.NewString(table, "RandomBlock")
	x.Random = field.NewInt32(table, "Random")
	x.Md5Hash = field.NewString(table, "Md5Hash")

	x.fillFieldMap()

	return x
}

func (x *xOrder) WithContext(ctx context.Context) *xOrderDo { return x.xOrderDo.WithContext(ctx) }

func (x xOrder) TableName() string { return x.xOrderDo.TableName() }

func (x xOrder) Alias() string { return x.xOrderDo.Alias() }

func (x xOrder) Columns(cols ...field.Expr) gen.Columns { return x.xOrderDo.Columns(cols...) }

func (x *xOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := x.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (x *xOrder) fillFieldMap() {
	x.fieldMap = make(map[string]field.Expr, 71)
	x.fieldMap["Id"] = x.ID
	x.fieldMap["SellerId"] = x.SellerID
	x.fieldMap["UserId"] = x.UserID
	x.fieldMap["GameId"] = x.GameID
	x.fieldMap["RoomLevel"] = x.RoomLevel
	x.fieldMap["ToAddress"] = x.ToAddress
	x.fieldMap["FromAddress"] = x.FromAddress
	x.fieldMap["TxId"] = x.TxID
	x.fieldMap["ChainType"] = x.ChainType
	x.fieldMap["BlockNum"] = x.BlockNum
	x.fieldMap["BlockHash"] = x.BlockHash
	x.fieldMap["Symbol"] = x.Symbol
	x.fieldMap["Amount"] = x.Amount
	x.fieldMap["BonusAmount"] = x.BonusAmount
	x.fieldMap["State"] = x.State
	x.fieldMap["Memo"] = x.Memo
	x.fieldMap["CreateTime"] = x.CreateTime
	x.fieldMap["RewardOrder"] = x.RewardOrder
	x.fieldMap["RewardAmount"] = x.RewardAmount
	x.fieldMap["BonusRewardAmount"] = x.BonusRewardAmount
	x.fieldMap["RewardTxId"] = x.RewardTxID
	x.fieldMap["WinAmount"] = x.WinAmount
	x.fieldMap["BetArea"] = x.BetArea
	x.fieldMap["OpenArea"] = x.OpenArea
	x.fieldMap["RewardType"] = x.RewardType
	x.fieldMap["RewardRate"] = x.RewardRate
	x.fieldMap["DataState"] = x.DataState
	x.fieldMap["AuditAccount"] = x.AuditAccount
	x.fieldMap["AuditTime"] = x.AuditTime
	x.fieldMap["GasFee"] = x.GasFee
	x.fieldMap["IsWin"] = x.IsWin
	x.fieldMap["Fee"] = x.Fee
	x.fieldMap["IsHe"] = x.IsHe
	x.fieldMap["Fined"] = x.Fined
	x.fieldMap["LiuSui"] = x.LiuSui
	x.fieldMap["FirstLiuSui"] = x.FirstLiuSui
	x.fieldMap["ValidBetAmount"] = x.ValidBetAmount
	x.fieldMap["TopAgentId"] = x.TopAgentID
	x.fieldMap["FineAccount"] = x.FineAccount
	x.fieldMap["FineTime"] = x.FineTime
	x.fieldMap["FineMemo"] = x.FineMemo
	x.fieldMap["HbcState"] = x.HbcState
	x.fieldMap["ChannelId"] = x.ChannelID
	x.fieldMap["BetChannelId"] = x.BetChannelID
	x.fieldMap["BlockTime"] = x.BlockTime
	x.fieldMap["Period"] = x.Period
	x.fieldMap["ReportState"] = x.ReportState
	x.fieldMap["BlockMaker"] = x.BlockMaker
	x.fieldMap["NextBlockHash"] = x.NextBlockHash
	x.fieldMap["RewardTime"] = x.RewardTime
	x.fieldMap["ExceedLimit"] = x.ExceedLimit
	x.fieldMap["UserAmount"] = x.UserAmount
	x.fieldMap["UserBonusAmount"] = x.UserBonusAmount
	x.fieldMap["IsTest"] = x.IsTest
	x.fieldMap["IsPanda"] = x.IsPanda
	x.fieldMap["HeFeeRate"] = x.HeFeeRate
	x.fieldMap["IsFanBei"] = x.IsFanBei
	x.fieldMap["CSGroup"] = x.CSGroup
	x.fieldMap["CSId"] = x.CSID
	x.fieldMap["SpecialAgent"] = x.SpecialAgent
	x.fieldMap["IsCommission"] = x.IsCommission
	x.fieldMap["UtType"] = x.UtType
	x.fieldMap["Ip"] = x.IP
	x.fieldMap["Lang"] = x.Lang
	x.fieldMap["BlackUserType"] = x.BlackUserType
	x.fieldMap["BlackUserId"] = x.BlackUserID
	x.fieldMap["RiskEarlyWarningFirst"] = x.RiskEarlyWarningFirst
	x.fieldMap["RandomHash"] = x.RandomHash
	x.fieldMap["RandomBlock"] = x.RandomBlock
	x.fieldMap["Random"] = x.Random
	x.fieldMap["Md5Hash"] = x.Md5Hash
}

func (x xOrder) clone(db *gorm.DB) xOrder {
	x.xOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return x
}

func (x xOrder) replaceDB(db *gorm.DB) xOrder {
	x.xOrderDo.ReplaceDB(db)
	return x
}

type xOrderDo struct{ gen.DO }

func (x xOrderDo) Debug() *xOrderDo {
	return x.withDO(x.DO.Debug())
}

func (x xOrderDo) WithContext(ctx context.Context) *xOrderDo {
	return x.withDO(x.DO.WithContext(ctx))
}

func (x xOrderDo) ReadDB() *xOrderDo {
	return x.Clauses(dbresolver.Read)
}

func (x xOrderDo) WriteDB() *xOrderDo {
	return x.Clauses(dbresolver.Write)
}

func (x xOrderDo) Session(config *gorm.Session) *xOrderDo {
	return x.withDO(x.DO.Session(config))
}

func (x xOrderDo) Clauses(conds ...clause.Expression) *xOrderDo {
	return x.withDO(x.DO.Clauses(conds...))
}

func (x xOrderDo) Returning(value interface{}, columns ...string) *xOrderDo {
	return x.withDO(x.DO.Returning(value, columns...))
}

func (x xOrderDo) Not(conds ...gen.Condition) *xOrderDo {
	return x.withDO(x.DO.Not(conds...))
}

func (x xOrderDo) Or(conds ...gen.Condition) *xOrderDo {
	return x.withDO(x.DO.Or(conds...))
}

func (x xOrderDo) Select(conds ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Select(conds...))
}

func (x xOrderDo) Where(conds ...gen.Condition) *xOrderDo {
	return x.withDO(x.DO.Where(conds...))
}

func (x xOrderDo) Order(conds ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Order(conds...))
}

func (x xOrderDo) Distinct(cols ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Distinct(cols...))
}

func (x xOrderDo) Omit(cols ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Omit(cols...))
}

func (x xOrderDo) Join(table schema.Tabler, on ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Join(table, on...))
}

func (x xOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.LeftJoin(table, on...))
}

func (x xOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.RightJoin(table, on...))
}

func (x xOrderDo) Group(cols ...field.Expr) *xOrderDo {
	return x.withDO(x.DO.Group(cols...))
}

func (x xOrderDo) Having(conds ...gen.Condition) *xOrderDo {
	return x.withDO(x.DO.Having(conds...))
}

func (x xOrderDo) Limit(limit int) *xOrderDo {
	return x.withDO(x.DO.Limit(limit))
}

func (x xOrderDo) Offset(offset int) *xOrderDo {
	return x.withDO(x.DO.Offset(offset))
}

func (x xOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *xOrderDo {
	return x.withDO(x.DO.Scopes(funcs...))
}

func (x xOrderDo) Unscoped() *xOrderDo {
	return x.withDO(x.DO.Unscoped())
}

func (x xOrderDo) Create(values ...*model.XOrder) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Create(values)
}

func (x xOrderDo) CreateInBatches(values []*model.XOrder, batchSize int) error {
	return x.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (x xOrderDo) Save(values ...*model.XOrder) error {
	if len(values) == 0 {
		return nil
	}
	return x.DO.Save(values)
}

func (x xOrderDo) First() (*model.XOrder, error) {
	if result, err := x.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOrder), nil
	}
}

func (x xOrderDo) Take() (*model.XOrder, error) {
	if result, err := x.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOrder), nil
	}
}

func (x xOrderDo) Last() (*model.XOrder, error) {
	if result, err := x.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOrder), nil
	}
}

func (x xOrderDo) Find() ([]*model.XOrder, error) {
	result, err := x.DO.Find()
	return result.([]*model.XOrder), err
}

func (x xOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.XOrder, err error) {
	buf := make([]*model.XOrder, 0, batchSize)
	err = x.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (x xOrderDo) FindInBatches(result *[]*model.XOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return x.DO.FindInBatches(result, batchSize, fc)
}

func (x xOrderDo) Attrs(attrs ...field.AssignExpr) *xOrderDo {
	return x.withDO(x.DO.Attrs(attrs...))
}

func (x xOrderDo) Assign(attrs ...field.AssignExpr) *xOrderDo {
	return x.withDO(x.DO.Assign(attrs...))
}

func (x xOrderDo) Joins(fields ...field.RelationField) *xOrderDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Joins(_f))
	}
	return &x
}

func (x xOrderDo) Preload(fields ...field.RelationField) *xOrderDo {
	for _, _f := range fields {
		x = *x.withDO(x.DO.Preload(_f))
	}
	return &x
}

func (x xOrderDo) FirstOrInit() (*model.XOrder, error) {
	if result, err := x.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOrder), nil
	}
}

func (x xOrderDo) FirstOrCreate() (*model.XOrder, error) {
	if result, err := x.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.XOrder), nil
	}
}

func (x xOrderDo) FindByPage(offset int, limit int) (result []*model.XOrder, count int64, err error) {
	result, err = x.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = x.Offset(-1).Limit(-1).Count()
	return
}

func (x xOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = x.Count()
	if err != nil {
		return
	}

	err = x.Offset(offset).Limit(limit).Scan(result)
	return
}

func (x xOrderDo) Scan(result interface{}) (err error) {
	return x.DO.Scan(result)
}

func (x xOrderDo) Delete(models ...*model.XOrder) (result gen.ResultInfo, err error) {
	return x.DO.Delete(models)
}

func (x *xOrderDo) withDO(do gen.Dao) *xOrderDo {
	x.DO = *do.(*gen.DO)
	return x
}
