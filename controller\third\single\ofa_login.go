package single

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xserver/abugo"
	"xserver/controller/third/single/base"
	thirdGameModel "xserver/model/third_game"
	"xserver/server"
	"xserver/utils"

	"github.com/beego/beego/logs"
	"github.com/google/uuid"
	daogorm "gorm.io/gorm"
	daogormclause "gorm.io/gorm/clause"
)

func (l *OFALiveSingleService) getLoginNameFromUserId(userId int64) string {
	return fmt.Sprintf("%s_%d", l.systemCode, userId)
}

func (l *OFALiveSingleService) getUserIdFromLoginName(loginName string) (userId int64, err error) {
	userIdTmp := strings.Split(loginName, "_")
	if len(userIdTmp) != 2 {
		err = errors.New("会员账号格式错误")
		return
	}
	userId, err = strconv.ParseInt(userIdTmp[1], 10, 64)
	if err != nil {
		return
	}
	if !strings.EqualFold(loginName, l.getLoginNameFromUserId(userId)) {
		err = errors.New("会员账号格式错误")
		return
	}
	return
}

// 通过用户ID转换登录密码
func (l *OFALiveSingleService) getUserPwd(userId int) string {
	return base.MD5(fmt.Sprintf("%d", userId) + "_Hx_+-#&6")[:16]
}

// OFALiveSupportLang 支持的语言 简体中文cn,繁体中文tw,英文en,越南语vi,泰国语th,韩语kr
var OFALiveSupportLang = []string{"cn", "tw", "en", "vi", "th", "kr"}

// 支持的语言 1=zh-cn(简体中文) 2=zh-tw(䌓体中文） 3=en-us(英语) 4=euc-jp(日语) 5=ko(韩语) 6=th(泰文) 7=vi(越南文) 8=id(印尼语) 9=沙特阿拉伯语 10=德语 11=西班牙语 12=法语 13=俄语
// var OFALiveSupportLang2 = []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"}

// Login /api/v2/seamless/user-login
func (l *OFALiveSingleService) Login(ctx *abugo.AbuHttpContent) {

	type RequestData struct {
		GameId   string `json:"GameId" validate:"required"` // 游戏code
		LangCode string `json:"LangCode"`                   // 语言
		HomeUrl  string `json:"HomeUrl"`                    // 返回商户地址
	}
	errcode := 0
	reqdata := RequestData{}
	err := ctx.RequestData(&reqdata)
	if err != nil {
		logs.Error("OFALive_single 登录游戏 请求参数错误 err=", err.Error())
		ctx.RespErrString(true, &errcode, "参数错误,请稍后再试")
		return
	}
	loginLang := reqdata.LangCode
	token := server.GetToken(ctx)
	if token == nil {
		logs.Error("OFALive_single 登录游戏 获取token错误")
		ctx.RespErrString(true, &errcode, "登录过期,请重新登录0")
		return
	}
	userId := token.UserId
	if err, errcode = base.IsLoginByUserId(cacheKeyOFALive, userId); err != nil {
		logs.Error("OFALive_single 登录游戏 获取用户登录状态错误 userId=", userId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	}

	// 检查游戏权限
	if allowed, hint, err := base.BeforeEnterGameId(userId, l.brandName, reqdata.GameId); err != nil {
		logs.Error("OFALive 登录游戏 权限检查错误 userId=", userId, " gameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, err.Error())
		return
	} else if !allowed {
		logs.Error("OFALive 登录游戏 权限被拒绝 userId=", userId, " gameId=", reqdata.GameId, " hint=", hint)
		ctx.RespErrString(true, &errcode, hint)
		return
	}

	gameList := thirdGameModel.GameList{}
	err = server.Db().GormDao().Table("x_game_list").Where("Brand = ? and GameId = ?", l.brandName, reqdata.GameId).First(&gameList).Error
	if err != nil {
		if errors.Is(err, daogorm.ErrRecordNotFound) {
			ctx.RespErrString(true, &errcode, "游戏code不存在!")
			return
		}
		logs.Error("OFALive_single 登录游戏 查询游戏错误 userId=", userId, " GameId=", reqdata.GameId, " err=", err.Error())
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}
	if gameList.State != 1 || gameList.OpenState != 1 {
		logs.Error("OFALive_single 登录游戏 游戏不可用 userId=", userId, " GameId=", reqdata.GameId, " gameList=", gameList)
		ctx.RespErrString(true, &errcode, "游戏未开放")
		return
	}

	loginToken := l.getTokenByUser(userId, token.Account)
	if loginToken == "" {
		logs.Error("OFALive_single 登录游戏 生成token失败", userId)
		ctx.RespErrString(true, &errcode, "系统错误,请稍后再试")
		return
	}

	loginName := strconv.Itoa(userId)
	gameURL, err := l.GetOrCreateUserGameURL(loginName, loginName, l.currency, loginLang, "")
	if err != nil {
		logs.Error("获取或创建用户并获取游戏URL错误: %v", err)
	} else {
		logs.Info("成功获取游戏URL: %s", gameURL.URL)
	}
	ctx.RespOK(gameURL.URL)
	return
}

// 生成OFALive token
func (l *OFALiveSingleService) getTokenByUser(userId int, account string) (token string) {
	type OFALiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}

	userIdStr := fmt.Sprintf("%d", userId)
	rKeyUser := cacheKeyOFALive + "uid:" + userIdStr
	rKeyToken := ""
	if v := server.Redis().Get(rKeyUser); v != nil {
		token = string(v.([]byte))
		rKeyToken = cacheKeyOFALive + "token:" + token
		err := server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("OFALive_single getTokenByUser set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			token = ""
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("OFALive_single getTokenByUser set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			token = ""
			return
		}
		return token
	}

	token = "OFALive_" + uuid.NewString()
	tokendata := OFALiveTokenData{
		UserId:    userId,
		Account:   account,
		CreatedAt: time.Now().In(tzUTC8).Format("2006-01-02 15:04:05"),
	}
	tokendataByte, _ := json.Marshal(tokendata)
	tokendataStr := string(tokendataByte)
	rKeyToken = cacheKeyOFALive + "token:" + token
	if err := server.Redis().SetNxString(rKeyUser, token, 86400); err != nil {
		logs.Error("OFALive_single getTokenByUser set redis rKeyUser=", rKeyUser, " err=", err.Error())
		token = ""
		return
	}

	if err := server.Redis().SetNxString(rKeyToken, tokendataStr, 86401); err != nil {
		logs.Error("OFALive_single getTokenByUser set redis rKeyToken=", rKeyToken, " err=", err.Error())
		token = ""
		return
	}
	return
}

// 验证token
func (l *OFALiveSingleService) getUserByToken(token string) (userId int, account string) {
	type OFALiveTokenData struct {
		UserId    int    `json:"user_id"`
		Account   string `json:"account"`
		CreatedAt string `json:"created_at"`
	}
	if token == "" {
		return
	}
	rKeyToken := cacheKeyOFALive + "token:" + token
	if v := server.Redis().Get(rKeyToken); v != nil {
		tokendata := OFALiveTokenData{}
		err := json.Unmarshal(v.([]byte), &tokendata)
		if err != nil {
			logs.Error("OFALive_single getUserIdByToken json.Unmarshal rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		rKeyUser := cacheKeyOFALive + "uid:" + fmt.Sprintf("%d", tokendata.UserId)
		err = server.Redis().Expire(rKeyToken, 86401)
		if err != nil {
			logs.Error("OFALive_single getUserIdByToken set redis Expire rKeyToken=", rKeyToken, " err=", err.Error())
			return
		}

		err = server.Redis().Expire(rKeyUser, 86400)
		if err != nil {
			logs.Error("OFALive_single getUserIdByToken set redis Expire rKeyUser=", rKeyUser, " err=", err.Error())
			return
		}

		userId = tokendata.UserId
		account = tokendata.Account
	} else {
		logs.Error("OFALive_single getUserIdBy token=", token, " 不存在")
	}
	return
}

// Gift 送禮 API URL 	Gift
func (l *OFALiveSingleService) Gift(ctx *abugo.AbuHttpContent) {
	type RequestData struct {
		SystemCode string  `json:"system_code"` // 系统代码，必填，最大长度10
		WebId      string  `json:"web_id"`      // 站台代码，必填，最大长度15
		UserId     string  `json:"user_id"`     // 玩家的唯一识别码，必填，最大长度20
		TipSn      string  `json:"tip_sn"`      // 打赏序号，必填
		GameCode   string  `json:"game_code"`   // 游戏代码，必填
		GameName   string  `json:"game_name"`   // 游戏名称，必填
		TableCode  string  `json:"table_code"`  // 桌代码，必填
		Money      float64 `json:"money"`       // 打赏金额，必填
		DealerId   string  `json:"dealer_id"`   // 荷代码，必填
		DealerName string  `json:"dealer_name"` // 荷官名称，必填
		GiftId     string  `json:"gift_id"`     // 礼物代码，必填
		GiftName   string  `json:"gift_name"`   // 礼物名称，必填
		TranTime   string  `json:"tran_time"`   // 打赏时间，必填
	}

	type ResponseData struct {
		Code      string `json:"code"`      // 状态代码，"00000"为成功，其他为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳
		Data      struct {
			Balance float64 `json:"balance"` // 余额
		} `json:"data"`
	}

	respdata := ResponseData{
		Code:      OFALive_Code_Success,
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, bodyBytes, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 送禮")
	if err != nil {
		logs.Error("OFALive_single 送禮 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_System_Busy
		respdata.Message = "系统忙碌"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	}
	logs.Info("OFALive_single 送禮 Request.Body=", string(bodyBytes))

	reqdata := RequestData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 送禮 解析请求消息体错误 err=", err.Error())
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 送禮 商户编码不正确 TipSn=", reqdata.TipSn, " reqdata.SystemCode=", reqdata.SystemCode, " l.merchantCode=", l.systemCode)
		respdata.Code = OFALive_Code_Incorrect_Param_Rules
		respdata.Message = "参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 送禮 用户名转换错误 TipSn=", reqdata.TipSn, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "资料不存在"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	}

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"

	// 开始送礼事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount,Token").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("OFALive_single 送禮 获取用户余额失败 TipSn=", reqdata.TipSn, " userId=", userId, " err=", e.Error())
			if e == daogorm.ErrRecordNotFound {
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "资料不存在"
			} else {
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "系统忙碌"
			}
			return e
		}

		//ChannelId, _ := server.GetChannel(ctx, server.GetTokenFromRedis(userBalance.Token).Host)
		//获取投注渠道
		ChannelId := base.GetUserChannelId(ctx, &userBalance)
		if userBalance.Amount+reqdata.Money < 0 {
			logs.Error("OFALive_single 送禮 余额不足 TipSn=", reqdata.TipSn, " userId=", userId, " userBalance.Amount=", userBalance.Amount, " reqdata.Money=", reqdata.Money)
			respdata.Code = OFALive_Code_Param_Out_Of_Range
			respdata.Message = "超出参数范围"
			return e
		}
		respdata.Data.Balance = userBalance.Amount

		thirdId := fmt.Sprintf("%v", reqdata.TipSn)
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")

		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil && e != daogorm.ErrRecordNotFound {
			logs.Error("OFALive_single 送禮 查询订单失败 TipSn=", reqdata.TipSn, " thirdId=", thirdId, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		} else if e == nil {
			// 如果找到了相同的订单，返回重复序号错误
			logs.Error("OFALive_single 送禮 重复序号 TipSn=", reqdata.TipSn, " thirdId=", thirdId)
			respdata.Code = OFALive_Code_Data_Already_Exist
			respdata.Message = "资料已存在，无法重复建立"
			return errors.New("重复序号")
		}

		// 创建注单
		order = thirdGameModel.ThirdOrder{
			// Id: 0,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			BetChannelId: ChannelId,
			UserId:       userBalance.UserId,
			Brand:        l.brandName,
			ThirdId:      thirdId,
			GameId:       reqdata.GiftId,
			GameName:     "送礼物-" + reqdata.GiftName,
			BetAmount:    0,
			WinAmount:    -reqdata.Money,
			ValidBet:     0,
			ThirdTime:    thirdTime,
			Currency:     l.currency,
			RawData:      string(decryptedBytes),
			State:        1,
			Fee:          0,
			DataState:    1,
			CreateTime:   thirdTime,
		}
		e = tx.Table(tablePre).Create(&order).Error
		if e != nil {
			logs.Error("OFALive_single 送禮 创建预设表订单失败 TipSn=", reqdata.TipSn, " thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		}

		order.Id = 0
		e = tx.Table(table).Create(&order).Error
		if e != nil {
			logs.Error("OFALive_single 送禮 创建正式表订单失败 TipSn=", reqdata.TipSn, " thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		}
		resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
			"Amount": daogorm.Expr("Amount - ?", reqdata.Money),
		})
		e = resultTmp.Error
		if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && reqdata.Money != 0 {
			e = errors.New("更新条数0")
		}
		if e != nil {
			logs.Error("OFALive_single 送禮 加扣款失败 TipSn=", reqdata.TipSn, " userId=", userId, " thirdId=", thirdId, " Money=", reqdata.Money, " err=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		}
		// 创建账变记录
		amountLog := thirdGameModel.AmountChangeLog{
			UserId:       userBalance.UserId,
			BeforeAmount: userBalance.Amount,
			Amount:       -reqdata.Money,
			AfterAmount:  userBalance.Amount - reqdata.Money,
			Reason:       utils.BalanceCReasonOFALiveTip,
			Memo:         l.brandName + " gift,thirdId:" + thirdId + ",tid:" + reqdata.TipSn + ",dealer:" + reqdata.DealerName,
			SellerId:     userBalance.SellerId,
			ChannelId:    userBalance.ChannelId,
			CreateTime:   thirdTime,
		}
		e = tx.Table("x_amount_change_log").Create(&amountLog).Error
		if e != nil {
			logs.Error("OFALive_single 送禮 创建账变记录失败 TipSn=", reqdata.TipSn, " thirdId=", thirdId, " amountLog=", amountLog, " error=", e)
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "系统忙碌"
			return e
		}
		respdata.Data.Balance = userBalance.Amount + reqdata.Money
		return nil
	})

	if err != nil {
		logs.Error("OFALive_single 送禮 事务处理失败 TipSn=", reqdata.TipSn, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][OFALive_single] 送禮 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][OFALive_single] 送禮 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
	if err != nil {
		logs.Error("OFALive_single 送禮 加密响应数据失败 err=", err.Error())
		respdata.Code = OFALive_Code_System_Busy
		respdata.Message = "系统忙碌"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 送禮")
		return
	}

	return
}

// 重新派彩方法
func (l *OFALiveSingleService) ReBetResult(ctx *abugo.AbuHttpContent) {
	type ResponseData struct {
		Code      string `json:"code"`      // 00000即为成功，其它代码皆为失败
		Message   string `json:"message"`   // 错误信息
		Timestamp string `json:"timestamp"` // 时间戳
		Data      struct {
			BetSn   string  `json:"bet_sn"`  // 注单序号
			Balance float64 `json:"balance"` // 会员该笔结算后余额
		} `json:"data"`
	}
	respdata := ResponseData{
		Code:      OFALive_Code_Success,
		Message:   "成功",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 处理加密请求
	decryptedBytes, bodyBytes, _, _, err := l.ProcessEncryptedRequest(ctx, "OFALive_single 重新派彩")
	if err != nil {
		logs.Error("OFALive_single 重新派彩 处理请求失败: ", err.Error())
		respdata.Code = OFALive_Code_Fail
		respdata.Message = "读取请求消息体错误"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	}
	logs.Info("OFALive_single 重新派彩 Request.Body=", string(bodyBytes))

	// 解析JSON数据
	reqdata := OFARequestSettleData{}
	err = json.Unmarshal(decryptedBytes, &reqdata)
	if err != nil {
		logs.Error("OFALive_single 重新派彩 解析请求消息体错误 err=", err.Error())
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "需求参数异常"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	}

	if !strings.EqualFold(reqdata.SystemCode, l.systemCode) {
		logs.Error("OFALive_single 重新派彩 商户编码不正确 Uuid=", reqdata.UserId, " reqdata.SystemCode=", reqdata.SystemCode, " l.systemCode=", l.systemCode)
		respdata.Code = OFALive_Code_Param_Error
		respdata.Message = "商户编码不正确"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	}

	userId, err := strconv.Atoi(reqdata.UserId)
	if err != nil {
		logs.Error("OFALive_single 重新派彩 会员账号错误 Uuid=", reqdata.UserId, " reqdata.UserId=", reqdata.UserId, " err=", err.Error())
		respdata.Code = OFALive_Code_Data_Not_Exist
		respdata.Message = "会员账号错误"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	}

	//判断是否是重复请求数据
	cacheSignKey := base.CacheSignKey{}
	cacheSignKey.UserId = userId         //用户ID
	cacheSignKey.Brand = l.brandName     //三方厂商名称
	cacheSignKey.ThirdId = reqdata.BetSn //三方订单ID
	cacheSignKey.ReqUrl = ctx.Gin().Request.URL.String()
	//如果是重复请求，返回上次响应结果
	duplicateResult, err := base.CheckDuplicateRedis(cacheSignKey, reqdata, nil)
	if duplicateResult != nil && err == nil {
		l.ProcessEncryptedResponse(ctx, duplicateResult, "OFALive_single 重新派彩")
		logs.Error(l.brandName, " 检测到重复请求 thirdId=", reqdata.BetSn, " duplicateResult=", duplicateResult)
		return
	}

	//保存本次请求数据
	defer func() {
		if e := base.SetRequestCacheRedis(cacheSignKey, reqdata, respdata, nil); e != nil {
			logs.Error(l.brandName, " 设置缓存出错 thirdId=", reqdata.BetSn, " err=", e.Error())
		}
	}()

	tablePre := "x_third_live_pre_order"
	table := "x_third_live"
	// 开始派彩事务
	err = server.Db().GormDao().Transaction(func(tx *daogorm.DB) error {
		var e error
		// 获取用户余额
		userBalance := thirdGameModel.UserBalance{}
		e = tx.Table("x_user").Clauses(daogormclause.Locking{Strength: "UPDATE"}).Select("UserId,SellerId,ChannelId,Amount").Where("UserId = ?", userId).First(&userBalance).Error
		if e != nil {
			logs.Error("OFALive_single 重新派彩 获取用户余额失败 Uuid=", reqdata.UserId, " userId=", userId, " err=", e.Error())
			if errors.Is(e, daogorm.ErrRecordNotFound) {
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "会员不存在"
			} else {
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "查询用户信息失败"
			}
			return e
		}

		thirdId := reqdata.BetSn
		thirdTime := time.Now().In(tzUTC8).Format("2006-01-02 15:04:05")
		// 查询注单
		order := thirdGameModel.ThirdOrder{}
		e = tx.Table(tablePre).Where("ThirdId=? and Brand=?", thirdId, l.brandName).Clauses(daogormclause.Locking{Strength: "UPDATE"}).First(&order).Error
		if e != nil {
			if errors.Is(e, daogorm.ErrRecordNotFound) { // 如果注单不存在则跳过
				logs.Error("OFALive_single 重新派彩 订单不存在  thirdId=", thirdId, " order=", order, " error=", e.Error())
				respdata.Code = OFALive_Code_Data_Not_Exist
				respdata.Message = "单号不存在"
				return e
			}
			logs.Error("OFALive_single 重新派彩 查询订单失败  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "查询订单失败"
			return e
		}

		betCtx := l.BetRecord2String(reqdata)

		if order.DataState != 1 {
			e = errors.New("订单未结算不能重新结算")
			logs.Error("OFALive_single 重新派彩 订单未结算不能重新结算  thirdId=", thirdId, " order=", order, " error=", e.Error())
			respdata.Code = OFALive_Code_Data_Not_Exist
			respdata.Message = "订单未结算不能重新结算"
			return e
		}

		//1 扣回該局所有有贏的會員金額
		//2 依新的結果發放中獎金額給會員
		// 获取原始赢钱金额和新的赢钱金额
		originalWinAmount := order.WinAmount
		newWinAmount := reqdata.WinMoney

		// 记录当前余额，用于后续计算
		currentBalance := userBalance.Amount

		// 如果原始赢钱金额大于0，需要先扣回玩家赢的钱
		if originalWinAmount > 0 {
			// 扣回原来赢的钱
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount - ?", originalWinAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && originalWinAmount != 0 {
				e = errors.New("扣回原赢金额更新条数0")
			}
			if e != nil {
				logs.Error("OFALive_single 重新派彩 扣回原赢金额失败  userId=", userId, " thirdId=", thirdId, " originalWinAmount=", originalWinAmount, " err=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "扣回原赢金额失败"
				return e
			}

			// 创建扣回原赢金额的账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: currentBalance,
				Amount:       -originalWinAmount,
				AfterAmount:  currentBalance - originalWinAmount,
				Reason:       utils.BalanceCReasonOFALiveSettleCancel,
				Memo:         l.brandName + " resettle deduct,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("OFALive_single 重新派彩 创建扣回账变记录失败  thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "创建扣回账变记录失败"
				return e
			}

			// 更新当前余额
			currentBalance -= originalWinAmount
		}

		// 如果新的赢钱金额大于0，需要给玩家派奖
		if newWinAmount > 0 {
			// 给玩家派奖
			resultTmp := tx.Table("x_user").Where("UserId = ?", userId).Updates(map[string]interface{}{
				"Amount": daogorm.Expr("Amount + ?", newWinAmount),
			})
			e = resultTmp.Error
			if resultTmp.Error == nil && resultTmp.RowsAffected <= 0 && newWinAmount != 0 {
				e = errors.New("派奖更新条数0")
			}
			if e != nil {
				logs.Error("OFALive_single 重新派彩 派奖失败  userId=", userId, " thirdId=", thirdId, " newWinAmount=", newWinAmount, " err=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "派奖失败"
				return e
			}

			// 创建派奖账变记录
			amountLog := thirdGameModel.AmountChangeLog{
				UserId:       userBalance.UserId,
				BeforeAmount: currentBalance,
				Amount:       newWinAmount,
				AfterAmount:  currentBalance + newWinAmount,
				Reason:       utils.BalanceCReasonOFALiveRESettle,
				Memo:         l.brandName + " resettle add,thirdId:" + thirdId,
				SellerId:     userBalance.SellerId,
				ChannelId:    userBalance.ChannelId,
				CreateTime:   thirdTime,
			}
			e = tx.Table("x_amount_change_log").Create(&amountLog).Error
			if e != nil {
				logs.Error("OFALive_single 重新派彩 创建派奖账变记录失败  thirdId=", thirdId, " amountLog=", amountLog, " error=", e.Error())
				respdata.Code = OFALive_Code_System_Busy
				respdata.Message = "创建派奖账变记录失败"
				return e
			}

			// 更新当前余额
			currentBalance += newWinAmount
		}

		// 更新注单状态 - 预注单表
		e = tx.Table(tablePre).Where("Id = ?", order.Id).Updates(map[string]interface{}{
			"ThirdTime":  thirdTime,
			"WinAmount":  newWinAmount,
			"BetCtx":     betCtx,
			"GameRst":    betCtx,
			"BetCtxType": 3,
			"RawData":    string(decryptedBytes),
		}).Error
		if e != nil {
			logs.Error("OFALive_single 重新派彩 更新预设表订单状态失败  thirdId=", thirdId, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "更新订单状态失败"
			return e
		}

		// 更新注单状态 - 正式注单表
		e = tx.Table(table).Where("ThirdId=? and Brand=? and UserId=?", thirdId, l.brandName, userId).Updates(map[string]interface{}{
			"ThirdTime":  thirdTime,
			"WinAmount":  newWinAmount,
			"BetCtx":     betCtx,
			"GameRst":    betCtx,
			"BetCtxType": 3,
			"RawData":    string(decryptedBytes),
		}).Error
		if e != nil {
			logs.Error("OFALive_single 重新派彩 更新订单状态失败  thirdId=", thirdId, " error=", e.Error())
			respdata.Code = OFALive_Code_System_Busy
			respdata.Message = "更新订单状态失败"
			return e
		}

		// 设置响应数据
		respdata.Data.BetSn = thirdId
		respdata.Data.Balance = currentBalance

		return nil
	})

	if err != nil {
		logs.Error("OFALive_single 重新派彩 事务处理失败 Uuid=", reqdata.UserId, " err=", err.Error())
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	} else {
		// 发送余额变动通知
		go func(notifyUserId int64) {
			if l.RefreshUserAmountFunc != nil {
				tmpErr := l.RefreshUserAmountFunc(int(notifyUserId))
				if tmpErr != nil {
					logs.Error("[ERROR][OFALive_single] 重新派彩 发送余额变动通知错误", notifyUserId, tmpErr.Error())
				}
			} else {
				logs.Error("[ERROR][OFALive_single] 重新派彩 发送余额变动通知失败,RefreshUserAmountFunc is nil")
			}
		}(int64(userId))
	}

	// 处理加密响应
	err = l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
	if err != nil {
		logs.Error("OFALive_single 重新派彩 加密响应数据失败 err=", err.Error())
		respdata.Code = OFALive_Code_Fail
		respdata.Message = "系统忙碌"
		l.ProcessEncryptedResponse(ctx, respdata, "OFALive_single 重新派彩")
		return
	}
	return
}
