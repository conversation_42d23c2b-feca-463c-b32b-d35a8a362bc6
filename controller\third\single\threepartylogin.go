package single

import (
	"sync"
	"xserver/abugo"
	"xserver/controller/third/single/base"
)

type ThreeParyLonginService struct {
	Url                   string `json:"url"`
	Key                   string `json:"key"`
	Currency              string `json:"currency"`
	Lobby                 string `json:"lobby"`
	Platform              string `json:"platform"`
	Token                 string `json:"token"`
	brandName             string
	RefreshUserAmountFunc func(int) error
	mu                    sync.Mutex
	thirdGamePush         *base.ThirdGamePush
}

func (t *ThreeParyLonginService) Refund(ctx *abugo.AbuHttpContent) {
	ctx.RespOK()
}
